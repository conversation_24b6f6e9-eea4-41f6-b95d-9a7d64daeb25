package com.cleevio.cinemax.api.common.integration.cards

import com.cleevio.cinemax.api.common.integration.AbstractConnector
import com.cleevio.cinemax.api.common.integration.ErrorHandlerLoggingLevel
import com.cleevio.cinemax.api.common.integration.cards.config.CinemaxCardsConfigProperties
import com.cleevio.cinemax.api.common.integration.cards.dto.AvailableCardUsagesResponse
import com.cleevio.cinemax.api.common.integration.cards.dto.CardsLoginRequest
import com.cleevio.cinemax.api.common.integration.cards.dto.CardsLoginResponse
import com.cleevio.cinemax.api.common.integration.cards.dto.GetCardResponse
import com.cleevio.cinemax.api.common.integration.cards.dto.GetCardUsageResponse
import com.cleevio.cinemax.api.common.integration.cards.dto.ListCardsUsagesRequest
import com.cleevio.cinemax.api.common.integration.cards.dto.ListPurchasableSkusRequest
import com.cleevio.cinemax.api.common.integration.cards.dto.ListPurchasableSkusResponse
import com.cleevio.cinemax.api.common.integration.cards.dto.UseCardsRequest
import com.cleevio.cinemax.api.common.integration.cards.dto.UseCardsResponse
import com.cleevio.cinemax.api.common.util.MAPPER
import org.springframework.http.HttpHeaders.AUTHORIZATION
import org.springframework.http.MediaType
import org.springframework.http.converter.json.MappingJackson2HttpMessageConverter
import org.springframework.stereotype.Component
import org.springframework.web.client.RestClient

@Component
class CinemaxCardsConnector(
    val cardsConfigProperties: CinemaxCardsConfigProperties,
) : AbstractConnector(
    baseUrl = cardsConfigProperties.baseUrl,
    restClientBuilder = {
        it.setCinemaxCardsHeaders()
    },
    enableRequestLogging = true,
    errorHandlerLoggingLevel = ErrorHandlerLoggingLevel.WARN
) {
    private var accessToken: String? = null
    private val loginRestClient: RestClient = RestClient
        .builder()
        .baseUrl(cardsConfigProperties.authBaseUrl)
        .also {
            it.setCinemaxCardsHeaders()
        }
        .build()

    fun getCard(cardCode: String): GetCardResponse = executeWithLogin {
        restClient.get()
            .uri("/card/$cardCode")
            .header(AUTHORIZATION, "Bearer $accessToken")
            .retrieveResponseWithErrorHandler<GetCardResponse>()
    }

    fun useCards(request: UseCardsRequest): UseCardsResponse = executeWithLogin {
        restClient.post()
            .uri("/card/useCards")
            .header(AUTHORIZATION, "Bearer $accessToken")
            .body(request)
            .retrieveResponseWithErrorHandler<UseCardsResponse>()
    }

    fun listCardsUsages(request: ListCardsUsagesRequest): AvailableCardUsagesResponse = executeWithLogin {
        restClient.post()
            .uri("/card/listAvailableCardsUsages")
            .header(AUTHORIZATION, "Bearer $accessToken")
            .body(request)
            .retrieveResponseWithErrorHandler<AvailableCardUsagesResponse>()
    }

    fun getCardUsage(
        cardCode: String,
        cardUsageId: Long,
    ): GetCardUsageResponse = executeWithLogin {
        restClient.get()
            .uri("/card/$cardCode/usage/$cardUsageId")
            .header(AUTHORIZATION, "Bearer $accessToken")
            .retrieveResponseWithErrorHandler<GetCardUsageResponse>()
    }

    fun deleteCardUsage(
        cardCode: String,
        cardUsageId: Long,
    ) = executeWithLogin {
        restClient.delete()
            .uri("/card/$cardCode/usage/$cardUsageId")
            .header(AUTHORIZATION, "Bearer $accessToken")
            .retrieveWithErrorHandler()
    }

    fun listPurchasableSkus(cardCode: String): ListPurchasableSkusResponse = executeWithLogin {
        val type = "CINEMA"

        val request = ListPurchasableSkusRequest(
            type = type
        )

        restClient.post()
            .uri("/card/$cardCode/listPurchasableSkus")
            .header(AUTHORIZATION, "Bearer $accessToken")
            .body(request)
            .retrieveResponseWithErrorHandler<ListPurchasableSkusResponse>()
    }

    private fun login() {
        accessToken = loginRestClient.post()
            .uri("/oauth/token")
            .body(
                CardsLoginRequest(
                    clientId = cardsConfigProperties.clientId,
                    clientSecret = cardsConfigProperties.clientSecret
                )
            )
            .retrieveResponseWithErrorHandler<CardsLoginResponse>().accessToken
    }

    private fun <T> executeWithLogin(logic: () -> T): T = executeWithRetry(
        retryOnStatusCode = 401,
        retryLogic = { login() },
        logic = logic
    )
}

private fun RestClient.Builder.setCinemaxCardsHeaders() {
    this.defaultHeaders { headers ->
        headers.contentType = MediaType.APPLICATION_JSON
    }.messageConverters {
        it.add(MappingJackson2HttpMessageConverter(MAPPER))
    }
}
