package com.cleevio.cinemax.api.module.card.service

import com.cleevio.cinemax.api.common.integration.IntegrationException
import com.cleevio.cinemax.api.common.integration.cards.CinemaxCardsConnector
import com.cleevio.cinemax.api.common.integration.cards.dto.ListPurchasableSkusResponse
import com.cleevio.cinemax.api.common.integration.cards.dto.PurchasableSkuResponse
import com.cleevio.cinemax.api.module.card.exception.CardProductListingFailedException
import com.cleevio.cinemax.api.module.card.service.command.ListPurchasableCardProductsCommand
import com.cleevio.cinemax.api.module.product.entity.Product
import jakarta.validation.Valid
import org.springframework.stereotype.Service
import org.springframework.validation.annotation.Validated

/**
 * Service responsible for card purchase operations.
 * Integrates with Cards API and manages card product creation.
 */
@Service
@Validated
class CardPurchaseService(
    private val cardJpaFinderService: CardJpaFinderService,
    private val cinemaxCardsConnector: CinemaxCardsConnector,
    private val cardProductCreationService: CardProductCreationService,
) {

    /**
     * Lists all purchasable card products for a given card ID.
     * Calls Cards API, creates products if they don't exist, and returns them.
     */
    fun listPurchasableCardProducts(
        @Valid command: ListPurchasableCardProductsCommand
    ): List<Product> {
        val card = cardJpaFinderService.getById(command.cardId)

        val purchasableCardProducts: ListPurchasableSkusResponse = runCatching {
            cinemaxCardsConnector.listPurchasableSkus(cardCode = card.code)
        }.getOrElse {
            throw CardProductListingFailedException()
        }

        // for each check if exist in db - if not then create Product entity else get
        purchasableCardProducts.forEach {

        }

        cardProductCreationService.createOrGetCardProducts(skus)

        return try {
            // Call Cards API to get available SKUs
            val skus = cinemaxCardsConnector.listPurchasableSkus(
                cardId = command.cardId,
                type = "CINEMA"
            )

            // Create or retrieve existing products for these SKUs

        } catch (e: IntegrationException) {
            // Handle Cards API errors as specified in requirements
            throw CardProductListingFailedException()
        }
    }
}
