package com.cleevio.cinemax.api.module.card.controller

import com.cleevio.cinemax.api.common.constant.ApiVersion
import com.cleevio.cinemax.api.common.constant.Role
import com.cleevio.cinemax.api.common.dto.IdResult
import com.cleevio.cinemax.api.common.dto.toSimplePage
import com.cleevio.cinemax.api.module.basketitem.controller.dto.AdminSearchTicketBasketItemsFilter
import com.cleevio.cinemax.api.module.basketitem.controller.dto.AdminSearchTicketBasketItemsWithSummaryResponse
import com.cleevio.cinemax.api.module.discountcard.controller.dto.DiscountCardResponse
import com.cleevio.cinemax.api.module.discountcard.controller.mapper.DiscountCardResponseMapper
import com.cleevio.cinemax.api.module.discountcard.service.DiscountCardImplementationFinderService
import com.cleevio.cinemax.api.module.discountcard.service.DiscountCardService
import com.cleevio.cinemax.api.module.discountcard.service.command.ActivateDiscountCardCommand
import com.cleevio.cinemax.api.module.discountcard.service.command.DeactivateDiscountCardCommand
import com.cleevio.cinemax.api.module.discountcard.service.command.ValidateDiscountCardCommand
import com.cleevio.cinemax.api.module.discountcard.service.implementationForCode
import com.cleevio.cinemax.api.module.discountcard.service.implementationForId
import com.cleevio.cinemax.psql.tables.BasketItemColumnNames
import io.swagger.v3.oas.annotations.tags.Tag
import org.springdoc.core.annotations.ParameterObject
import org.springframework.data.domain.Pageable
import org.springframework.data.domain.Sort
import org.springframework.data.web.PageableDefault
import org.springframework.http.HttpStatus
import org.springframework.security.access.prepost.PreAuthorize
import org.springframework.web.bind.annotation.PathVariable
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RequestParam
import org.springframework.web.bind.annotation.ResponseStatus
import org.springframework.web.bind.annotation.RestController
import java.util.UUID

@Tag(name = "POS Discount Cards")
@RestController
@RequestMapping("/pos-app/cards")
class CardController(
//    private val discountCardServices: List<DiscountCardService>,
//    private val discountCardImplementationFinderService: DiscountCardImplementationFinderService,
//    private val discountCardResponseMapper: DiscountCardResponseMapper,
) {


}
