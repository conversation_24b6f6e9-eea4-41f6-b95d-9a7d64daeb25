package com.cleevio.cinemax.api.module.card.controller.mapper

import com.cleevio.cinemax.api.module.card.controller.dto.CardProductResponse
import com.cleevio.cinemax.api.module.product.entity.Product
import org.springframework.stereotype.Component

@Component
class CardProductResponseMapper {

    fun mapSingle(input: Product) = CardProductResponse(
        id = input.id,
        title = input.title,
        price = input.price,
    )

    fun mapList(input: List<Product>) = input.map { mapSingle(it) }
}
