package com.cleevio.cinemax.api.module.card.exception

import com.cleevio.cinemax.api.common.exception.ApiException
import org.springframework.http.HttpStatus
import org.springframework.web.bind.annotation.ResponseStatus

@ResponseStatus(value = HttpStatus.UNPROCESSABLE_ENTITY)
class CardProductListingFailedException : ApiException(
    module = Module.CARD,
    errorType = CardErrorType.CARD_PRODUCT_LISTING_FAILED,
)
