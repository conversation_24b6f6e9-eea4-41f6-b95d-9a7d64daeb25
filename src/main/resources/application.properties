spring.cloud.config.enabled=false
spring.profiles.active=${SPRING_ACTIVE_PROFILES:local}
spring.application.name=${POD_NAMESPACE:cinemax-api}
spring.task.scheduling.pool.size=5
spring.threads.virtual.enabled=true

management.endpoints.web.exposure.include=*
management.health.mail.enabled=false
# management.server.port=8282

cleevio.distributed-locks.redis.application-name="${spring.application.name}-${spring.profiles.active}"
cleevio.distributed-locks.redis.lock-watchdog-timeout-seconds=30

features.receipt.long-polling-timeout=300000

logbook.write.max-body-size=10000

cinemax.configuration.dummy-receipt-print=false
cinemax.configuration.security.web-app-api-key=${CINEMAX_WEB_APP_SECURITY_APIKEY}
cinemax.configuration.security.vip-app-api-key=${CINEMAX_VIP_APP_SECURITY_APIKEY}
