<configuration>
    <include resource="org/springframework/boot/logging/logback/defaults.xml"/>
    <logger name="org.springframework.web.filter.CommonsRequestLoggingFilter">
        <level value="DEBUG" />
    </logger>

    <appender name="CONSOLE" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>${CONSOLE_LOG_PATTERN}</pattern>
            <charset>utf8</charset>
        </encoder>
    </appender>
    <appender name="SENTRY" class="io.sentry.logback.SentryAppender">
        <options>
            <dsn>${sentry.dsn}</dsn>
        </options>
    </appender>
    <appender name="LOKI" class="com.github.loki4j.logback.Loki4jAppender">
        <batchTimeoutMs>5000</batchTimeoutMs>
        <http>
            <url>${LOKI_URL}</url>
            <auth>
                <username>${LOKI_USERNAME}</username>
                <password>${LOKI_PASSWORD}</password>
            </auth>
            <requestTimeoutMs>15000</requestTimeoutMs>
        </http>
        <format>
            <label>
                <pattern>app=${OTEL_SERVICE_NAME};host=${HOSTNAME};level=%level;profile=${SPRING_PROFILES_ACTIVE}</pattern>
                <pairSeparator>;</pairSeparator>
            </label>
            <message>
                <pattern>[%level] [%thread] %msg %ex</pattern>
            </message>
            <sortByTime>true</sortByTime>
        </format>
    </appender>

    <springProfile name="!local &amp; !test">
        <root level="INFO">
            <appender-ref ref="CONSOLE"/>
            <appender-ref ref="SENTRY"/>
            <appender-ref ref="LOKI"/>
        </root>
    </springProfile>

    <springProfile name="local">
        <root level="INFO">
            <appender-ref ref="CONSOLE"/>
        </root>
    </springProfile>
</configuration>
