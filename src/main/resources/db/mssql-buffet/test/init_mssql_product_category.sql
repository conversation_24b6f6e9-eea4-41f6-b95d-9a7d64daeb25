-----------------------
--- init test table ---
-----------------------
SET ANSI_NULLS ON
    ;
SET QUOTED_IDENTIFIER ON
    ;
CREATE TABLE [dbo].[rcdme](
    [rcdmeID] [int] IDENTITY(1,1) NOT NULL,
    [druh] [char](2) NOT NULL,
    [nazev] [varchar](30) NOT NULL,
    [dan] [smallint] NOT NULL,
    [barva] [int] NOT NULL,
    [zuziv] [char](10) NOT NULL,
    [zcas] [smalldatetime] NULL,
    [lsleva] [bit] NOT NULL,
    [pc] [int] NOT NULL,
    [obrazek] [varchar](50) NOT NULL,
    [ltablet] [bit] NOT NULL,
    [hexColor] [varchar](6) NULL,
    [lnezob] [bit] NOT NULL,
    CONSTRAINT [PK__rcdme__19DFD96B] PRIMARY KEY CLUSTERED
(
[rcdmeID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
    ) ON [PRIMARY]
    ;

ALTER TABLE [dbo].[rcdme] ADD  CONSTRAINT [DF__rcdme__nazev__1AD3FDA4]  DEFAULT ('') FOR [nazev]
    ;
ALTER TABLE [dbo].[rcdme] ADD  CONSTRAINT [DF__rcdme__dan__1DB06A4F]  DEFAULT ((0)) FOR [dan]
    ;
ALTER TABLE [dbo].[rcdme] ADD  CONSTRAINT [DF_rcdme_brava]  DEFAULT ((14215660)) FOR [barva]
    ;
ALTER TABLE [dbo].[rcdme] ADD  CONSTRAINT [DF__rcdme__zuziv__1BC821DD]  DEFAULT ('') FOR [zuziv]
    ;
ALTER TABLE [dbo].[rcdme] ADD  CONSTRAINT [DF__rcdme__zcas__1CBC4616]  DEFAULT (getdate()) FOR [zcas]
    ;
ALTER TABLE [dbo].[rcdme] ADD  DEFAULT ((0)) FOR [lsleva]
    ;
ALTER TABLE [dbo].[rcdme] ADD  DEFAULT ((0)) FOR [pc]
    ;
ALTER TABLE [dbo].[rcdme] ADD  DEFAULT ('') FOR [obrazek]
    ;
ALTER TABLE [dbo].[rcdme] ADD  DEFAULT ((0)) FOR [ltablet]
    ;
ALTER TABLE [dbo].[rcdme] ADD  DEFAULT ((0)) FOR [lnezob]
    ;
----------------------
--- init test data ---
----------------------
INSERT INTO rp_BufetE.dbo.rcdme (druh, nazev, dan, barva, zuziv, zcas, lsleva, pc, obrazek, ltablet, hexColor, lnezob) VALUES (N'06', N'Nachos                        ', 23, 4227327, N'ildi      ', N'2016-08-10 17:38:00', N'False', 2, N'NACHOS.PNG', N'False', N'', N'False');
INSERT INTO rp_BufetE.dbo.rcdme (druh, nazev, dan, barva, zuziv, zcas, lsleva, pc, obrazek, ltablet, hexColor, lnezob) VALUES (N'13', N'Zlava                         ', 23, 32896, N'ildi      ', N'2017-08-25 16:54:00', N'True', 11, N'', N'False', N'', N'False');
INSERT INTO rp_BufetE.dbo.rcdme (druh, nazev, dan, barva, zuziv, zcas, lsleva, pc, obrazek, ltablet, hexColor, lnezob) VALUES (N'24', N'Fanta sprite postmix          ', 23, 255, N'diana     ', N'2015-01-13 12:44:00', N'False', 0, N'MENU_NAPOJE.PNG', N'False', N'', N'False');
