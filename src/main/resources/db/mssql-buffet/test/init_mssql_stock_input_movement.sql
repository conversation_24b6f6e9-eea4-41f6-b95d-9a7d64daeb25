------------------------
--- init test tables ---
------------------------
/****** Object:  Table [dbo].[rprij]    Script Date: 16.06.2023 8:57:41 ******/
SET ANSI_NULLS ON
;
SET QUOTED_IDENTIFIER ON
;
CREATE TABLE [dbo].[rprij](
    [rprijID] [int] IDENTITY(1,1) NOT NULL,
    [rzboziID] [int] NOT NULL,
    [rdodID] [int] NOT NULL,
    [sklad] [char](4) NOT NULL,
    [zbozi] [char](5) NOT NULL,
    [cenadod] [float] NOT NULL,
    [cenacelk] [numeric](10, 3) NOT NULL,
    [lcelk] [bit] NOT NULL,
    [DPHsk] [smallint] NOT NULL,
    [datum] [smalldatetime] NULL,
    [zaloz] [datetime] NULL,
    [mnoz] [float] NOT NULL,
    [doklad] [char](15) NULL,
    [pozn] [varchar](40) NOT NULL,
    [zuziv] [char](10) NOT NULL,
    [zcas] [smalldatetime] NULL,
    CONSTRAINT [PK__rprij__7E37BEF6] PRIMARY KEY CLUSTERED
(
[rprijID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
    ) ON [PRIMARY]
;

----------------------
--- init test data ---
----------------------
INSERT INTO rp_BufetE.dbo.rprij (rzboziID,rdodID,sklad,zbozi,cenadod,cenacelk,lcelk,DPHsk,datum,zaloz,mnoz,doklad,pozn,zuziv,zcas) VALUES (169,2,N'01  ',N'07005',0.106,63.600,0,20,'2024-02-09 00:00:00.000','2019-05-03 17:37:00.000',500.0,N'V246101068     ',N'Stock Input 1',N'manager   ','2019-05-03 16:37:00.000');
INSERT INTO rp_BufetE.dbo.rprij (rzboziID,rdodID,sklad,zbozi,cenadod,cenacelk,lcelk,DPHsk,datum,zaloz,mnoz,doklad,pozn,zuziv,zcas) VALUES (182,2,N'01  ',N'08031',1.11,59.940,0,20,'2024-02-09 00:00:00.000','2019-05-04 17:37:00.000',45.0,N'V246101068     ',N'Stock Input 2',N'manager   ','2019-05-04 16:37:00.000');
INSERT INTO rp_BufetE.dbo.rprij (rzboziID,rdodID,sklad,zbozi,cenadod,cenacelk,lcelk,DPHsk,datum,zaloz,mnoz,doklad,pozn,zuziv,zcas) VALUES (34,2,N'01  ',N'07001',5.4,808.315,0,20,'2024-02-09 00:00:00.000','2019-05-05 17:37:00.000',124.74,N'V246101068     ',N'Stock Input 3',N'manager   ','2019-05-05 16:37:00.000');