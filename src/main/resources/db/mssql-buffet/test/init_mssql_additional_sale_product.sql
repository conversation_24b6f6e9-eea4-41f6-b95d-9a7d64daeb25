-----------------------
--- init test table ---
-----------------------
/****** Object:  Table [dbo].[rzbozir]    Script Date: 16.06.2023 8:57:41 ******/
SET ANSI_NULLS ON
    ;
SET QUOTED_IDENTIFIER ON
    ;
CREATE TABLE [dbo].[rzbozir](
    [rzbozirID] [int] IDENTITY(1,1) NOT NULL,
    [cislo] [char](5) NOT NULL,
    [nazev] [varchar](40) NOT NULL,
    [druh] [char](2) NOT NULL,
    [cena] [numeric](9, 2) NOT NULL,
    [aktivni] [bit] NOT NULL,
    [cenacelk] [money] NOT NULL,
    [lcelk] [bit] NOT NULL,
    [zuziv] [char](10) NOT NULL,
    [zcas] [smalldatetime] NULL,
    [lproc] [bit] NOT NULL,
    [lBufet] [bit] NOT NULL,
    [lBar] [bit] NOT NULL,
    [lTablet] [bit] NOT NULL,
    PRIMARY KEY CLUSTERED
(
[rzbozirID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
    ) ON [PRIMARY]
    ;

ALTER TABLE [dbo].[rzbozir] ADD  DEFAULT ((0)) FOR [lproc]
    ;
ALTER TABLE [dbo].[rzbozir] ADD  DEFAULT ((0)) FOR [lBufet]
    ;
ALTER TABLE [dbo].[rzbozir] ADD  DEFAULT ((0)) FOR [lBar]
    ;
ALTER TABLE [dbo].[rzbozir] ADD  DEFAULT ((0)) FOR [lTablet]
    ;
SET IDENTITY_INSERT [dbo].[rzbozir] ON
    ;
----------------------
--- init test data ---
----------------------
INSERT INTO rp_BufetE.dbo.rzbozir (rzbozirID, cislo, nazev, druh, cena, aktivni, cenacelk, lcelk, zuziv, zcas, lproc, lBufet, lBar, lTablet) VALUES (52, N'00026', N'Zlava voucher', N'13', 83.33, 1, 100.0000, 1, N'DISdata   ', N'2017-07-04 12:34:00', 1, 1, 0, 0);
INSERT INTO rp_BufetE.dbo.rzbozir (rzbozirID, cislo, nazev, druh, cena, aktivni, cenacelk, lcelk, zuziv, zcas, lproc, lBufet, lBar, lTablet) VALUES (20, N'00001', N'Mliecko do kávy 2cl                     ', N'11', 0.25, 1, 0.3000, 1, N'DISdata   ', N'2023-01-20 15:26:00', 0, 0, 1, 0);
INSERT INTO rp_BufetE.dbo.rzbozir (rzbozirID, cislo, nazev, druh, cena, aktivni, cenacelk, lcelk, zuziv, zcas, lproc, lBufet, lBar, lTablet) VALUES (25, N'00006', N'Cerstvé štavy 0,1l                      ', N'11', 1.63, 0, 1.9500, 1, N'DISdata   ', N'2023-01-20 15:35:00', 0, 0, 1, 0);
INSERT INTO rp_BufetE.dbo.rzbozir (rzbozirID, cislo, nazev, druh, cena, aktivni, cenacelk, lcelk, zuziv, zcas, lproc, lBufet, lBar, lTablet) VALUES (28, N'00001', N'                     ', N'11', 0.25, 1, 0.3000, 1, N'DISdata   ', N'2023-01-20 15:27:00', 0, 0, 1, 0);
INSERT INTO rp_BufetE.dbo.rzbozir (rzbozirID, cislo, nazev, druh, cena, aktivni, cenacelk, lcelk, zuziv, zcas, lproc, lBufet, lBar, lTablet) VALUES (31, N'00001', N'Voucher IMAX 3D                     ', N'  ', 0.25, 1, 0.3000, 1, N'DISdata   ', N'2023-01-20 15:27:00', 0, 0, 1, 0);
INSERT INTO rp_BufetE.dbo.rzbozir (rzbozirID, cislo, nazev, druh, cena, aktivni, cenacelk, lcelk, zuziv, zcas, lproc, lBufet, lBar, lTablet) VALUES (68,N'00035',N'Záloha za obal                          ',N'17',0.15,1,0.1500,1,N'DISdata   ','2023-01-20 15:26:00',0,1,1,0);
