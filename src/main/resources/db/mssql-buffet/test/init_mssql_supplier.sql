-----------------------
--- init test table ---
-----------------------

/****** Object:  Table [dbo].[rdod]    Script Date: 16.06.2023 8:57:41 ******/
SET ANSI_NULLS ON
;
SET QUOTED_IDENTIFIER ON
;
CREATE TABLE [dbo].[rdod](
    [rdodID] [int] IDENTITY(1,1) NOT NULL,
    [cisdod] [char](6) NOT NULL,
    [nazev] [varchar](30) NOT NULL,
    [ulice] [varchar](40) NOT NULL,
    [misto] [varchar](40) NOT NULL,
    [psc] [char](6) NOT NULL,
    [jmeno1] [char](20) NOT NULL,
    [jmeno2] [char](20) NOT NULL,
    [jmeno3] [char](20) NOT NULL,
    [telefon1] [char](20) NOT NULL,
    [telefon2] [char](20) NOT NULL,
    [telefon3] [char](20) NOT NULL,
    [fax] [char](20) NOT NULL,
    [email] [varchar](70) NOT NULL,
    [banka] [varchar](40) NOT NULL,
    [ucet] [char](15) NOT NULL,
    [ico] [char](15) NOT NULL,
    [dic] [char](15) NOT NULL,
    [zuziv] [char](10) NOT NULL,
    [zcas] [smalldatetime] NULL,
    PRIMARY KEY CLUSTERED
(
[rdodID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
    ) ON [PRIMARY]
;

----------------------
--- init test data ---
----------------------
INSERT INTO rp_BufetE.dbo.rdod (cisdod, nazev, ulice, misto, psc, jmeno1, jmeno2, jmeno3, telefon1, telefon2, telefon3, fax, email, banka, ucet, ico, dic, zuziv, zcas) VALUES
-- Supplier 1
(N'SUP1  ', N'Supplier One', N'Street 1', N'City 1', N'10001 ',
 N'Contact Name 1', N'                    ', N'                    ',
 N'*********       ', N'                    ', N'                    ',
 N'                    ', N'<EMAIL>', N'Bank One', N'123456',
 N'1111           ', N'111-22-3333   ', N'DISdata   ', '2019-05-03 16:37:00.000'),

-- Supplier 2
(N'SUP2  ', N'Supplier Two', N'Street 2', N'City 2', N'10002 ',
 N'Contact Name 2', N'                    ', N'                    ',
 N'*********       ', N'                    ', N'                    ',
 N'                    ', N'<EMAIL>', N'Bank Two', N'654321',
 N'2222           ', N'222-33-4444   ', N'DISdata   ', '2019-05-04 16:37:00.000'),

-- Supplier 3
(N'SUP3  ', N'Supplier Three', N'                    ', N'                    ', N'       ',
 N'                    ', N'                    ', N'                    ',
 N'                    ', N'                    ', N'                    ',
 N'                    ', N'', N'                    ', N'                    ',
 N'                    ', N'                    ', N'DISdata   ', '2019-05-05 16:37:00.000');