-----------------------
--- init test table ---
-----------------------

/****** Object:  Table [dbo].[ruziv]    Script Date: 16.06.2023 8:57:41 ******/
SET ANSI_NULLS ON
    ;
SET QUOTED_IDENTIFIER ON
    ;
CREATE TABLE [dbo].[ruziv](
    [ruzivID] [smallint] IDENTITY(1,1) NOT NULL,
    [uziv] [char](10) NOT NULL,
    [Jmeno] [char](40) NOT NULL,
    [<PERSON><PERSON><PERSON>] [char](10) NOT NULL,
    [zapis] [bit] NOT NULL,
    [pokladna] [char](10) NOT NULL,
    [sklad] [char](4) NOT NULL,
    [datOd] [smalldatetime] NULL,
    [datDo] [smalldatetime] NULL,
    [datPrist] [smalldatetime] NULL,
    [pokD] [int] NOT NULL,
    [pokZ] [int] NOT NULL,
    [uzpokl] [smalldatetime] NULL,
    [prace] [char](12) NOT NULL,
    [vstup] [bit] NOT NULL,
    [old] [int] NOT NULL,
    [zuziv] [char](10) NOT NULL,
    [zcas] [smalldatetime] NULL,
    CONSTRAINT [PK__ruziv__36B12243] PRIMARY KEY CLUSTERED
(
[ruzivID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
    ) ON [PRIMARY]
;

ALTER TABLE [dbo].[ruziv] ADD  CONSTRAINT [DF__ruziv__Jmeno__37A5467C]  DEFAULT ('') FOR [Jmeno]
;
ALTER TABLE [dbo].[ruziv] ADD  CONSTRAINT [DF__ruziv__zapis__38996AB5]  DEFAULT ((0)) FOR [zapis]
;
ALTER TABLE [dbo].[ruziv] ADD  CONSTRAINT [DF__ruziv__pokladna__398D8EEE]  DEFAULT ('') FOR [pokladna]
;
ALTER TABLE [dbo].[ruziv] ADD  CONSTRAINT [DF_ruziv_sklad]  DEFAULT ('') FOR [sklad]
;
ALTER TABLE [dbo].[ruziv] ADD  CONSTRAINT [DF__ruziv__datOd__3A81B327]  DEFAULT (getdate()) FOR [datOd]
;
ALTER TABLE [dbo].[ruziv] ADD  CONSTRAINT [DF__ruziv__pokD__3B75D760]  DEFAULT ((0)) FOR [pokD]
;
ALTER TABLE [dbo].[ruziv] ADD  CONSTRAINT [DF__ruziv__pokZ__3C69FB99]  DEFAULT ((0)) FOR [pokZ]
;
ALTER TABLE [dbo].[ruziv] ADD  CONSTRAINT [DF__ruziv__prace__3D5E1FD2]  DEFAULT ('') FOR [prace]
;
ALTER TABLE [dbo].[ruziv] ADD  CONSTRAINT [DF__ruziv__vstup__3E52440B]  DEFAULT ((0)) FOR [vstup]
;
ALTER TABLE [dbo].[ruziv] ADD  CONSTRAINT [DF__ruziv__old__3F466844]  DEFAULT ((0)) FOR [old]
;
ALTER TABLE [dbo].[ruziv] ADD  CONSTRAINT [DF__ruziv__zuziv__403A8C7D]  DEFAULT ('') FOR [zuziv]
;
