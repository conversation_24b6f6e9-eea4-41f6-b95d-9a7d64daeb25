-----------------------
--- init test table ---
-----------------------
/****** Object:  Table [dbo].[stoly]    Script Date: 16.06.2023 8:57:41 ******/
SET ANSI_NULLS ON
    ;
SET QUOTED_IDENTIFIER ON
    ;
CREATE TABLE [dbo].[stoly](
    [stoly<PERSON>] [int] IDENTITY(1,1) NOT NULL,
    [nazev] [varchar](40) NOT NULL,
    [zuziv] [char](10) NOT NULL,
    [zcas] [smalldatetime] NULL,
    [karta] [char](10) NOT NULL,
    [MAC] [nvarchar](50) NULL,
    [lhoto] [bit] NOT NULL,
    [oktran] [bit] NOT NULL,
    [lvydej] [bit] NOT NULL,
    [taletProdej] [bit] NOT NULL,
    PRIMARY KEY CLUSTERED
(
[stoly<PERSON>] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
    ) ON [PRIMARY]
;

ALTER TABLE [dbo].[stoly] ADD  DEFAULT ('') FOR [zuziv]
;
ALTER TABLE [dbo].[stoly] ADD  DEFAULT ('') FOR [karta]
;
ALTER TABLE [dbo].[stoly] ADD  DEFAULT ((0)) FOR [lhoto]
;
ALTER TABLE [dbo].[stoly] ADD  DEFAULT ((0)) FOR [oktran]
;
ALTER TABLE [dbo].[stoly] ADD  DEFAULT ((0)) FOR [lvydej]
;
ALTER TABLE [dbo].[stoly] ADD  DEFAULT ((0)) FOR [taletProdej]
;

----------------------
--- init test data ---
----------------------
INSERT INTO rp_BufetE.dbo.stoly (nazev, zuziv, zcas, karta, MAC, lhoto, oktran, lvydej, taletProdej) VALUES (N'Stol 1                                  ', N'DISdata   ', N'2021-03-21 11:57:00', N'          ', N'', 1, 1, 0, 0);
INSERT INTO rp_BufetE.dbo.stoly (nazev, zuziv, zcas, karta, MAC, lhoto, oktran, lvydej, taletProdej) VALUES (N'E14, 1211                               ', N'dzuro     ', N'2022-07-30 11:57:00', N'          ', N'10.1.255.236                                      ', 0, 0, 0, 1);
INSERT INTO rp_BufetE.dbo.stoly (nazev, zuziv, zcas, karta, MAC, lhoto, oktran, lvydej, taletProdej) VALUES (N'F13, 1211                               ', N'monika    ', N'2023-03-26 15:53:00', N'123456789 ', N'10.1.255.137                                      ', 1, 0, 0, 1);
INSERT INTO rp_BufetE.dbo.stoly (nazev, zuziv, zcas, karta, MAC, lhoto, oktran, lvydej, taletProdej) VALUES (N'Stol 10                                 ', N'myska     ', N'2022-05-08 00:09:00', N'          ', N'', 0, 1, 0, 0);
INSERT INTO rp_BufetE.dbo.stoly (nazev, zuziv, zcas, karta, MAC, lhoto, oktran, lvydej, taletProdej) VALUES (N'                                        ', N'roman     ', N'2021-08-04 22:58:00', N'          ', N'', 0, 1, 0, 0);
