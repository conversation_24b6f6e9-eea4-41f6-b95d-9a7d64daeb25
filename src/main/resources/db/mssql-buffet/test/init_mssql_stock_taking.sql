------------------------
--- init test tables ---
------------------------
SET ANSI_NULLS ON
    ;
SET QUOTED_IDENTIFIER ON
    ;
CREATE TABLE [dbo].[stavy](
    [stavyID] [int] IDENTITY(1,1) NOT NULL,
    [sklad] [char](4) NOT NULL,
    [zbozi] [char](5) NOT NULL,
    [stavmj] [float] NOT NULL,
    [stavmj2] [float] NOT NULL,
    [cenacelk] [float] NOT NULL,
    [stavold] [float] NOT NULL,
    [cenaold] [float] NOT NULL,
    [zaloz] [datetime] NULL,
    [uloz] [datetime] NULL,
    [zuziv] [char](10) NOT NULL,
    [zcas] [smalldatetime] NULL,
    CONSTRAINT [PK__stavy__0CDAE408] PRIMARY KEY CLUSTERED
(
[stavyID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
    ) ON [PRIMARY]
;

ALTER TABLE [dbo].[stavy] ADD  CONSTRAINT [DF__stavy__satvmj__0EC32C7A]  DEFAULT ((0)) FOR [stavmj]
;
ALTER TABLE [dbo].[stavy] ADD  CONSTRAINT [DF_stavy_stavmj2]  DEFAULT ((0)) FOR [stavmj2]
;
ALTER TABLE [dbo].[stavy] ADD  CONSTRAINT [DF__stavy__cenacelk__0FB750B3]  DEFAULT ((0)) FOR [cenacelk]
;
ALTER TABLE [dbo].[stavy] ADD  CONSTRAINT [DF__stavy__stavold__10AB74EC]  DEFAULT ((0)) FOR [stavold]
;
ALTER TABLE [dbo].[stavy] ADD  CONSTRAINT [DF__stavy__cenaold__119F9925]  DEFAULT ((0)) FOR [cenaold]
;
ALTER TABLE [dbo].[stavy] ADD  CONSTRAINT [DF__stavy__zuziv__1293BD5E]  DEFAULT ('') FOR [zuziv]
;

----------------------
--- init test data ---
----------------------
INSERT INTO rp_BufetE.dbo.stavy (sklad, zbozi, stavmj, stavmj2, cenacelk, stavold, cenaold, zaloz, uloz, zuziv, zcas) VALUES(N'01  ', N'02001', 350.5, 0.0, 1049.5311999999526, 0.0, 0.0, '2015-06-24 08:21:21.000', NULL, N'Manager   ', '2015-06-24 08:21:00.000');
INSERT INTO rp_BufetE.dbo.stavy (sklad, zbozi, stavmj, stavmj2, cenacelk, stavold, cenaold, zaloz, uloz, zuziv, zcas) VALUES(N'01  ', N'02002', 516.2, 0.0, 98.04000000000026, 0.0, 0.0, '2015-06-24 08:21:53.000', NULL, N'Manager   ', '2015-06-24 08:22:00.000');
INSERT INTO rp_BufetE.dbo.stavy (sklad, zbozi, stavmj, stavmj2, cenacelk, stavold, cenaold, zaloz, uloz, zuziv, zcas) VALUES(N'01  ', N'02003', 2.92, 0.0, 46.25279999999999, 0.0, 0.0, '2015-06-24 08:22:06.000', NULL, N'Manager   ', '2015-06-28 08:22:00.000');
INSERT INTO rp_BufetE.dbo.stavy (sklad, zbozi, stavmj, stavmj2, cenacelk, stavold, cenaold, zaloz, uloz, zuziv, zcas) VALUES(N'01  ', N'02004', 653.5, 0.0, 267.7299999999994, 0.0, 0.0, '2015-06-24 08:22:18.000', NULL, N'Manager   ', '2015-06-29 08:22:00.000');
-- -- invalid product component id
INSERT INTO rp_BufetE.dbo.stavy (sklad, zbozi, stavmj, stavmj2, cenacelk, stavold, cenaold, zaloz, uloz, zuziv, zcas) VALUES(N'01  ', N'00000', 582.0, 0.0, 214.87439999999958, 0.0, 0.0, '2015-06-24 08:22:30.000', NULL, N'Manager   ', '2015-06-24 08:22:00.000');
