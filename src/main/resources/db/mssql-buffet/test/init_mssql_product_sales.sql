------------------------
--- init test tables ---
------------------------
/****** Object:  Table [dbo].[rprodej]    Script Date: 16.06.2023 8:57:41 ******/
SET ANSI_NULLS ON
    ;
SET QUOTED_IDENTIFIER ON
    ;
CREATE TABLE [dbo].[rprodej](
    [rprodejID] [int] IDENTITY(1,1) NOT NULL,
    [rmenuID] [int] NOT NULL,
    [rzboziID] [int] NOT NULL,
    [rpoklID] [int] NOT NULL,
    [doklad] [char](10) NOT NULL,
    [sklad] [char](4) NOT NULL,
    [cena] [numeric](12, 2) NOT NULL,
    [mnoz] [int] NOT NULL,
    [dph] [tinyint] NOT NULL,
    [sluzby] [numeric](6, 2) NOT NULL,
    [lvrat] [bit] NOT NULL,
    [lhoto] [bit] NOT NULL,
    [datprod] [smalldatetime] NULL,
    [datpokl] [smalldatetime] NULL,
    [uzivprod] [int] NOT NULL,
    [uzivpokl] [int] NOT NULL,
    [pokladna] [char](10) NOT NULL,
    [zuziv] [char](10) NOT NULL,
    [zcas] [smalldatetime] NULL,
    [karta] [char](10) NOT NULL,
    [rzbozirID] [int] NOT NULL,
    [pcID] [int] NOT NULL,
    [voucher] [char](10) NOT NULL,
    [ltablet] [bit] NOT NULL,
    [provoz] [smallint],
    CONSTRAINT [PK__rprodej__6501FCD8] PRIMARY KEY CLUSTERED
(
[rprodejID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
    ) ON [PRIMARY]
;

ALTER TABLE [dbo].[rprodej] ADD  CONSTRAINT [DF_rprodej_rzboziID]  DEFAULT ((0)) FOR [rzboziID]
;
ALTER TABLE [dbo].[rprodej] ADD  CONSTRAINT [DF__rprodej__rpoklID__65F62111]  DEFAULT ((0)) FOR [rpoklID]
;
ALTER TABLE [dbo].[rprodej] ADD  CONSTRAINT [DF__rprodej__doklad__66EA454A]  DEFAULT ('') FOR [doklad]
;
ALTER TABLE [dbo].[rprodej] ADD  CONSTRAINT [DF_rprodej_sklad]  DEFAULT ('01') FOR [sklad]
;
ALTER TABLE [dbo].[rprodej] ADD  CONSTRAINT [DF_rprodej_dph]  DEFAULT ((0)) FOR [dph]
;
ALTER TABLE [dbo].[rprodej] ADD  CONSTRAINT [DF__rprodej__sluzby__67DE6983]  DEFAULT ((0)) FOR [sluzby]
;
ALTER TABLE [dbo].[rprodej] ADD  CONSTRAINT [DF__rprodej__lvrat__68D28DBC]  DEFAULT ((0)) FOR [lvrat]
;
ALTER TABLE [dbo].[rprodej] ADD  CONSTRAINT [DF__rprodej__lhoto__69C6B1F5]  DEFAULT ((0)) FOR [lhoto]
;
ALTER TABLE [dbo].[rprodej] ADD  CONSTRAINT [DF__rprodej__uzivpok__6ABAD62E]  DEFAULT ((0)) FOR [uzivpokl]
;
ALTER TABLE [dbo].[rprodej] ADD  CONSTRAINT [DF__rprodej__pokladn__6BAEFA67]  DEFAULT ('') FOR [pokladna]
;
ALTER TABLE [dbo].[rprodej] ADD  CONSTRAINT [DF__rprodej__zuziv__6CA31EA0]  DEFAULT ('') FOR [zuziv]
;
ALTER TABLE [dbo].[rprodej] ADD  CONSTRAINT [DF__rprodej__zcas__6D9742D9]  DEFAULT (getdate()) FOR [zcas]
;
ALTER TABLE [dbo].[rprodej] ADD  DEFAULT ('') FOR [karta]
;
ALTER TABLE [dbo].[rprodej] ADD  DEFAULT ((0)) FOR [rzbozirID]
;
ALTER TABLE [dbo].[rprodej] ADD  DEFAULT ((0)) FOR [pcID]
;
ALTER TABLE [dbo].[rprodej] ADD  DEFAULT ('') FOR [voucher]
;
ALTER TABLE [dbo].[rprodej] ADD  DEFAULT ((0)) FOR [ltablet]
;
SET ANSI_NULLS ON
;
SET QUOTED_IDENTIFIER ON
;

SET ANSI_NULLS ON
    ;
SET QUOTED_IDENTIFIER ON
    ;
CREATE TABLE [dbo].[stoly](
    [stolyID] [int] IDENTITY(1,1) NOT NULL,
    [nazev] [varchar](40) NOT NULL,
    [zuziv] [char](10) NOT NULL,
    [zcas] [smalldatetime] NULL,
    [karta] [char](10) NOT NULL,
    [MAC] [nvarchar](50) NULL,
    [lhoto] [bit] NOT NULL,
    [oktran] [bit] NOT NULL,
    [lvydej] [bit] NOT NULL,
    [taletProdej] [bit] NOT NULL,
    PRIMARY KEY CLUSTERED
(
[stolyID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
    ) ON [PRIMARY]
;

ALTER TABLE [dbo].[stoly] ADD  DEFAULT ('') FOR [zuziv]
;
ALTER TABLE [dbo].[stoly] ADD  DEFAULT ('') FOR [karta]
;
ALTER TABLE [dbo].[stoly] ADD  DEFAULT ((0)) FOR [lhoto]
;
ALTER TABLE [dbo].[stoly] ADD  DEFAULT ((0)) FOR [oktran]
;
ALTER TABLE [dbo].[stoly] ADD  DEFAULT ((0)) FOR [lvydej]
;
ALTER TABLE [dbo].[stoly] ADD  DEFAULT ((0)) FOR [taletProdej]
;

----------------------
--- init test data ---
----------------------
INSERT INTO rp_BufetE.dbo.stoly (nazev, zuziv, zcas, karta, MAC, lhoto, oktran, lvydej, taletProdej) VALUES (N'Stul 1                                  ', N'DISdata   ', N'2024-03-27 11:59:00', N'          ', N'', 1, 0, 0, 0);
