------------------------
--- init test tables ---
------------------------SET ANSI_NULLS ON
;
SET QUOTED_IDENTIFIER ON
;
CREATE TABLE [dbo].[rcdsk](
    [rcdskID] [int] IDENTITY(1,1) NOT NULL,
    [druh] [char](2) NOT NULL,
    [nazev] [varchar](30) NOT NULL,
    [dan] [smallint] NOT NULL,
    [zuziv] [char](10) NOT NULL,
    [zcas] [smalldatetime] NULL,
    CONSTRAINT [PK_rcdsk] PRIMARY KEY CLUSTERED
(
[rcdskID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
    ) ON [PRIMARY]
;

----------------------
--- init test data ---
----------------------
INSERT INTO rp_BufetE.dbo.rcdsk (druh,nazev,dan,zuziv,zcas) VALUES (N'01',N'Nealko                        ',23,N'DISdata   ','2019-05-03 16:37:00.000');
INSERT INTO rp_BufetE.dbo.rcdsk (druh,nazev,dan,zuziv,zcas) VALUES (N'02',N'Kukurica                      ',23,N'DISdata   ','2019-05-04 16:37:00.000');
INSERT INTO rp_BufetE.dbo.rcdsk (druh,nazev,dan,zuziv,zcas) VALUES (N'03',N'Káva                          ',19,N'DISdata   ','2019-05-05 16:37:00.000');
