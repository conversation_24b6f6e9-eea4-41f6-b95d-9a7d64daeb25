-----------------------
--- init test table ---
-----------------------
/****** Object:  Table [dbo].[rmenu]    Script Date: 16.06.2023 8:57:41 ******/
SET ANSI_NULLS ON
    ;
SET QUOTED_IDENTIFIER ON
    ;
CREATE TABLE [dbo].[rmenu](
    [rmenuID] [int] IDENTITY(1,1) NOT NULL,
    [cislo] [char](5) NOT NULL,
    [nazev] [varchar](40) NOT NULL,
    [druh] [char](2) NOT NULL,
    [minim] [smallint] NOT NULL,
    [cena] [numeric](9, 2) NOT NULL,
    [carkod] [char](25) NOT NULL,
    [rezervace] [smallint] NOT NULL,
    [aktivni] [bit] NOT NULL,
    [zuziv] [char](10) NOT NULL,
    [zcas] [datetime] NULL,
    [cenacelk] [money] NOT NULL,
    [lcelk] [bit] NOT NULL,
    [sklad] [char](4) NOT NULL,
    [lzbozi] [bit] NOT NULL,
    [poradiTablet] [int] NOT NULL,
    [poradiProdej] [int] NOT NULL,
    [lBufet] [bit] NOT NULL,
    [lBar] [bit] NOT NULL,
    [lTablet] [bit] NOT NULL,
    [poznamka] [text] NULL,
    [obrazek] [varchar](50) NOT NULL,
    [tabletne] [bit] NOT NULL,
    [lastChangeByUser] [varchar](255) NULL,
    [nazev2] [varchar](40) NOT NULL,
    CONSTRAINT [PK_rmenu] PRIMARY KEY CLUSTERED
(
[rmenuID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
    ) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
    ;

ALTER TABLE [dbo].[rmenu] ADD  CONSTRAINT [DF__rmenu__nazev__5535A963]  DEFAULT ('') FOR [nazev]
    ;
ALTER TABLE [dbo].[rmenu] ADD  CONSTRAINT [DF_rmenu_druh]  DEFAULT ('') FOR [druh]
    ;
ALTER TABLE [dbo].[rmenu] ADD  CONSTRAINT [DF__rmenu__minim__5812160E]  DEFAULT ((0)) FOR [minim]
    ;
ALTER TABLE [dbo].[rmenu] ADD  CONSTRAINT [DF__rmenu__cena__59063A47]  DEFAULT ((0)) FOR [cena]
    ;
ALTER TABLE [dbo].[rmenu] ADD  CONSTRAINT [DF_rmenu_carkod]  DEFAULT ('') FOR [carkod]
    ;
ALTER TABLE [dbo].[rmenu] ADD  CONSTRAINT [DF_rmenu_rezervace]  DEFAULT ((0)) FOR [rezervace]
    ;
ALTER TABLE [dbo].[rmenu] ADD  CONSTRAINT [DF_rmenu_aktivni]  DEFAULT ((1)) FOR [aktivni]
    ;
ALTER TABLE [dbo].[rmenu] ADD  CONSTRAINT [DF_rmenu_zuziv]  DEFAULT ('') FOR [zuziv]
    ;
ALTER TABLE [dbo].[rmenu] ADD  CONSTRAINT [DF__rmenu__zcas__59FA5E80]  DEFAULT (getdate()) FOR [zcas]
    ;
ALTER TABLE [dbo].[rmenu] ADD  DEFAULT ((0)) FOR [cenacelk]
    ;
ALTER TABLE [dbo].[rmenu] ADD  DEFAULT ((0)) FOR [lcelk]
    ;
ALTER TABLE [dbo].[rmenu] ADD  DEFAULT ('') FOR [sklad]
    ;
ALTER TABLE [dbo].[rmenu] ADD  DEFAULT ((0)) FOR [lzbozi]
    ;
ALTER TABLE [dbo].[rmenu] ADD  DEFAULT ((0)) FOR [poradiTablet]
    ;
ALTER TABLE [dbo].[rmenu] ADD  DEFAULT ((0)) FOR [poradiProdej]
    ;
ALTER TABLE [dbo].[rmenu] ADD  DEFAULT ((0)) FOR [lBufet]
    ;
ALTER TABLE [dbo].[rmenu] ADD  DEFAULT ((0)) FOR [lBar]
    ;
ALTER TABLE [dbo].[rmenu] ADD  DEFAULT ((0)) FOR [lTablet]
    ;
ALTER TABLE [dbo].[rmenu] ADD  CONSTRAINT [DF__rmenu__obrazek__0F624AF8]  DEFAULT ('NENI.PNG') FOR [obrazek]
    ;
ALTER TABLE [dbo].[rmenu] ADD  DEFAULT ((0)) FOR [tabletne]
    ;
ALTER TABLE [dbo].[rmenu] ADD  DEFAULT ('') FOR [nazev2]
    ;

/****** Object:  Table [dbo].[rpolm]    Script Date: 16.06.2023 8:57:41 ******/
SET ANSI_NULLS ON
    ;
SET QUOTED_IDENTIFIER ON
    ;
CREATE TABLE [dbo].[rpolm](
    [rpolmID] [int] IDENTITY(1,1) NOT NULL,
    [rmenuID] [int] NOT NULL,
    [rzboziID] [int] NOT NULL,
    [menu] [char](5) NOT NULL,
    [zbozi] [char](5) NOT NULL,
    [mnozs] [float] NOT NULL,
    [zuziv] [char](10) NOT NULL,
    [zcas] [smalldatetime] NULL,
    [rmenu2ID] [int] NOT NULL DEFAULT 0,
    [rzbozirID] [int] NOT NULL DEFAULT 0,
    CONSTRAINT [PK__rpolm__2CF2ADDF] PRIMARY KEY CLUSTERED
(
[rpolmID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
    ) ON [PRIMARY]
;

ALTER TABLE [dbo].[rpolm] ADD  CONSTRAINT [DF_rpolm_menu]  DEFAULT ('') FOR [menu]
;
ALTER TABLE [dbo].[rpolm] ADD  CONSTRAINT [DF_rpolm_zbozi]  DEFAULT ('') FOR [zbozi]
;
ALTER TABLE [dbo].[rpolm] ADD  CONSTRAINT [DF__rpolm__zuziv__2DE6D218]  DEFAULT ('') FOR [zuziv]
;
ALTER TABLE [dbo].[rpolm] ADD  CONSTRAINT [DF__rpolm__zcas__2EDAF651]  DEFAULT (getdate()) FOR [zcas]
;

----------------------
--- init test data ---
----------------------
INSERT INTO rp_BufetE.dbo.rmenu (cislo, nazev, druh, minim, cena, carkod, rezervace, aktivni, zuziv, zcas, cenacelk, lcelk, sklad, lzbozi, poradiTablet, poradiProdej, lBufet, lBar, lTablet, poznamka, obrazek, tabletne, nazev2) VALUES (N'01027', N'Fanta+Cola combo                  ', N'05', 25, 2.67, N'                         ', 0, 1, N'DISdata   ', N'2023-01-19 15:08:08.000', 3.2000, 1, N'    ', 0, 6, 0, 0, 0, 1, null, N'FANTA POHAR.PNG                                   ', 0, N'');
INSERT INTO rp_BufetE.dbo.rmenu (cislo, nazev, druh, minim, cena, carkod, rezervace, aktivni, zuziv, zcas, cenacelk, lcelk, sklad, lzbozi, poradiTablet, poradiProdej, lBufet, lBar, lTablet, poznamka, obrazek, tabletne, nazev2) VALUES (N'01031', N'Záloha na obal                 ', N'05', 0, 2.08, N'                         ', 0, 1, N'Spravca   ', N'2023-03-14 14:38:20.000', 2.5000, 1, N'    ', 1, 16, 6, 0, 0, 1, null, N'CAPPY-POMARANC-PET.JPG                            ', 0, N'');
INSERT INTO rp_BufetE.dbo.rmenu (cislo, nazev, druh, minim, cena, carkod, rezervace, aktivni, zuziv, zcas, cenacelk, lcelk, sklad, lzbozi, poradiTablet, poradiProdej, lBufet, lBar, lTablet, poznamka, obrazek, tabletne, nazev2) VALUES (N'VP   ', N'VIP karta -20%                    ', N'35', 10, 0.00, N'                         ', 0, 1, N'DISdata   ', N'2017-09-06 09:41:16.000', 0.0000, 0, N'    ', 0, 0, 0, 1, 1, 1, null, N'NENI.PNG', 0, N'');
INSERT INTO rp_BufetE.dbo.rmenu (cislo, nazev, druh, minim, cena, carkod, rezervace, aktivni, zuziv, zcas, cenacelk, lcelk, sklad, lzbozi, poradiTablet, poradiProdej, lBufet, lBar, lTablet, poznamka, obrazek, tabletne, nazev2) VALUES (N'02564', N'Produktova sleva                  ', N'35', 25, 2.5, N'                         ', 0, 1, N'DISdata   ', N'2022-01-19 15:08:08.000', 5.0000, 1, N'    ', 0, 6, 0, 0, 0, 1, null, N'NENI.PNG', 0, N'');
-- invalid records
INSERT INTO rp_BufetE.dbo.rmenu (cislo, nazev, druh, minim, cena, carkod, rezervace, aktivni, zuziv, zcas, cenacelk, lcelk, sklad, lzbozi, poradiTablet, poradiProdej, lBufet, lBar, lTablet, poznamka, obrazek, tabletne, nazev2) VALUES (N'     ', N'', N'08', 0, 0.00, N'                         ', 0, 1, N'DISdata   ', N'2023-03-09 14:58:26.000', 0.0000, 0, N'    ', 0, 0, 0, 0, 0, 0, null, N'NENI.PNG', 0, N'');
INSERT INTO rp_BufetE.dbo.rmenu (cislo, nazev, druh, minim, cena, carkod, rezervace, aktivni, zuziv, zcas, cenacelk, lcelk, sklad, lzbozi, poradiTablet, poradiProdej, lBufet, lBar, lTablet, poznamka, obrazek, tabletne, nazev2) VALUES (N'01147', N'Slovakia  tycinky', N'  ', 0, 2.50, N'                         ', 0, 1, N'jano      ', N'2023-06-07 11:16:09.000', 2.5000, 1, N'    ', 1, 0, 0, 1, 0, 0, null, N'NENI.PNG', 0, N'');

-- rpolm product-in-product records for product originalId=1 (Fanta+Cola combo)
INSERT INTO rp_BufetE.dbo.rpolm (rmenuID, rzboziID, menu, zbozi, mnozs, zuziv, zcas, rmenu2ID, rzbozirID) VALUES (1, 0, N'01027', N'', 1, N'DISdata   ', N'2008-10-31 00:19:00', 11, 0);
INSERT INTO rp_BufetE.dbo.rpolm (rmenuID, rzboziID, menu, zbozi, mnozs, zuziv, zcas, rmenu2ID, rzbozirID) VALUES (1, 0, N'01027', N'', 1, N'DISdata   ', N'2008-10-31 00:19:00', 12, 0);
-- rpolm product originalId=2 (Zaloha na obal)
INSERT INTO rp_BufetE.dbo.rpolm (rmenuID, rzboziID, menu, zbozi, mnozs, zuziv, zcas, rmenu2ID, rzbozirID) VALUES (2, 78, N'01027', N'0789', 1, N'DISdata   ', N'2008-10-31 00:19:00', 0, 0);
