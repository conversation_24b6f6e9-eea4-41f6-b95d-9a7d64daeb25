------------------------
--- init test tables ---
------------------------
SET ANSI_NULLS ON
    ;
SET QUOTED_IDENTIFIER ON
    ;
CREATE TABLE [dbo].[voucher](
    [voucherID] [int] IDENTITY(1,1) NOT NULL,
    [cislo] [char](10) NOT NULL,
    [typ] [char](15) NOT NULL,
    [maxpo] [int] NOT NULL,
    [pocpo] [int] NOT NULL,
    [platod] [smalldatetime] NULL,
    [platdo] [smalldatetime] NULL,
    [slevac] [char](5) NOT NULL,
    [cmenu] [char](5) NOT NULL,
    [czbozir] [char](5) NOT NULL,
    [zuziv] [char](10) NOT NULL,
    [zcas] [smalldatetime] NULL,
    [sezmenu] [varchar](200) NOT NULL,
    PRIMARY KEY CLUSTERED
(
[voucherID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
    ) ON [PRIMARY]
;

ALTER TABLE [dbo].[voucher] ADD  DEFAULT ((1)) FOR [maxpo]
;
ALTER TABLE [dbo].[voucher] ADD  DEFAULT ((0)) FOR [pocpo]
;
ALTER TABLE [dbo].[voucher] ADD  DEFAULT ('') FOR [zuziv]
;
ALTER TABLE [dbo].[voucher] ADD  DEFAULT (getdate()) FOR [zcas]
;
ALTER TABLE [dbo].[voucher] ADD  DEFAULT ('') FOR [sezmenu]
;

----------------------
--- init test data ---
----------------------
INSERT INTO rp_BufetE.dbo.voucher (cislo, typ, maxpo, pocpo, platod, platdo, slevac, cmenu, czbozir, zuziv, zcas, sezmenu) VALUES (N'5103670506', N'Unicredit      ', 1, 4, N'2023-12-31 23:59:00', N'2034-12-31 23:59:00', N'00026', N'70002', N'     ', N'          ', N'2022-10-10 08:52:00', N'');
INSERT INTO rp_BufetE.dbo.voucher (cislo, typ, maxpo, pocpo, platod, platdo, slevac, cmenu, czbozir, zuziv, zcas, sezmenu) VALUES (N'6209016364', N'Akce1          ', 1, 0, N'2023-05-10 00:00:00', N'2034-12-31 23:59:00', N'00026', N'01052', N'     ', N'          ', N'2023-03-11 14:41:00', N'');
INSERT INTO rp_BufetE.dbo.voucher (cislo, typ, maxpo, pocpo, platod, platdo, slevac, cmenu, czbozir, zuziv, zcas, sezmenu) VALUES (N'5101300656', N'influencer     ', 1, 0, N'2022-03-13 00:00:00', N'2033-06-30 23:59:00', N'00026', N'01400', N'     ', N'          ', N'2023-05-13 23:27:00', N'');
-- expired voucher
INSERT INTO rp_BufetE.dbo.voucher (cislo, typ, maxpo, pocpo, platod, platdo, slevac, cmenu, czbozir, zuziv, zcas, sezmenu) VALUES (N'5101300657', N'influencer     ', 1, 0, N'2012-03-13 00:00:00', N'2022-06-30 23:59:00', N'00026', N'01400', N'     ', N'          ', N'2023-05-13 23:27:00', N'');
-- voucher with blank 'slevac' attribute
INSERT INTO rp_BufetE.dbo.voucher (cislo, typ, maxpo, pocpo, platod, platdo, slevac, cmenu, czbozir, zuziv, zcas, sezmenu) VALUES (N'5101300658', N'influencer     ', 1, 0, N'2022-03-13 00:00:00', N'2033-06-30 23:59:00', N'     ', N'01400', N'     ', N'          ', N'2023-05-13 23:27:00', N'');
