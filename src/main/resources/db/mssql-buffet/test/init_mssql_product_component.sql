------------------------
--- init test tables ---
------------------------
SET ANSI_NULLS ON
    ;
SET QUOTED_IDENTIFIER ON
    ;
CREATE TABLE [dbo].[rzbozi](
    [rzbo<PERSON><PERSON>] [int] IDENTITY(1,1) NOT NULL,
    [zbozi] [char](5) NOT NULL,
    [nazev] [varchar](40) NOT NULL,
    [skupina] [char](2) NOT NULL,
    [mj] [char](5) NOT NULL,
    [minim] [int] NOT NULL,
    [cena] [numeric](9, 2) NOT NULL,
    [carkod] [char](25) NOT NULL,
    [aktivni] [bit] NOT NULL,
    [zuziv] [char](10) NOT NULL,
    [zcas] [smalldatetime] NULL,
    [cenap] [numeric](10, 4) NOT NULL,
    [lastChangeByUser] [varchar](255) NULL,
    CONSTRAINT [PK__rzbozi__6D0D32F4] PRIMARY KEY CLUSTERED
(
[rzboziID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
    ) ON [PRIMARY]
;

ALTER TABLE [dbo].[rzbozi] ADD  CONSTRAINT [DF__rzbozi__nazev__6E01572D]  DEFAULT ('') FOR [nazev]
;
ALTER TABLE [dbo].[rzbozi] ADD  CONSTRAINT [DF__rzbozi__mj__70DDC3D8]  DEFAULT ('Ks') FOR [mj]
;
ALTER TABLE [dbo].[rzbozi] ADD  CONSTRAINT [DF__rzbozi__minim__71D1E811]  DEFAULT ((0)) FOR [minim]
;
ALTER TABLE [dbo].[rzbozi] ADD  CONSTRAINT [DF__rzbozi__cena__72C60C4A]  DEFAULT ((0)) FOR [cena]
;
ALTER TABLE [dbo].[rzbozi] ADD  CONSTRAINT [DF_rzbozi_carkod]  DEFAULT ('') FOR [carkod]
;
ALTER TABLE [dbo].[rzbozi] ADD  CONSTRAINT [DF_rzbozi_aktivni]  DEFAULT ((1)) FOR [aktivni]
;
ALTER TABLE [dbo].[rzbozi] ADD  CONSTRAINT [DF_rzbozi_zuziv]  DEFAULT ('') FOR [zuziv]
;
ALTER TABLE [dbo].[rzbozi] ADD  CONSTRAINT [DF__rzbozi__zcas__73BA3083]  DEFAULT (getdate()) FOR [zcas]
;
ALTER TABLE [dbo].[rzbozi] ADD  DEFAULT ((0)) FOR [cenap]
;

SET ANSI_NULLS ON
    ;
SET QUOTED_IDENTIFIER ON
    ;
CREATE TABLE [dbo].[rzboziskl](
    [rzbozisklID] [int] IDENTITY(1,1) NOT NULL,
    [sklad] [char](4) NOT NULL,
    [zbozi] [char](5) NOT NULL,
    [cenaskl] [float] NOT NULL,
    [mnozskl] [float] NOT NULL,
    [minim] [numeric](12, 3) NOT NULL,
    [datprij] [datetime] NULL,
    [datvydej] [datetime] NULL,
    [zuziv] [char](10) NOT NULL,
    [zcas] [smalldatetime] NULL,
    CONSTRAINT [PK__rzboziskl__24285DB4] PRIMARY KEY CLUSTERED
(
[rzbozisklID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
    ) ON [PRIMARY]
;

ALTER TABLE [dbo].[rzboziskl] ADD  CONSTRAINT [DF__rzboziskl__cenas__251C81ED]  DEFAULT ((0)) FOR [cenaskl]
;
ALTER TABLE [dbo].[rzboziskl] ADD  CONSTRAINT [DF__rzboziskl__mnozs__2610A626]  DEFAULT ((0)) FOR [mnozskl]
;
ALTER TABLE [dbo].[rzboziskl] ADD  CONSTRAINT [DF__rzboziskl__minim__2704CA5F]  DEFAULT ((0)) FOR [minim]
;
ALTER TABLE [dbo].[rzboziskl] ADD  CONSTRAINT [DF__rzboziskl__zuziv__27F8EE98]  DEFAULT ('') FOR [zuziv]
;
ALTER TABLE [dbo].[rzboziskl] ADD  CONSTRAINT [DF__rzboziskl__zcas__28ED12D1]  DEFAULT (getdate()) FOR [zcas]
;

----------------------
--- init test data ---
----------------------
INSERT INTO rp_BufetE.dbo.rzbozi (zbozi, nazev, skupina, mj, minim, cena, carkod, aktivni, zuziv, zcas, cenap) VALUES (N'05002', N'Pohár 0,3                               ', N'05', N'ks   ', 5, 0.00, N'                         ', 1, N'manager   ', N'2011-01-01 10:23:00', 0.1234);
INSERT INTO rp_BufetE.dbo.rzbozi (zbozi, nazev, skupina, mj, minim, cena, carkod, aktivni, zuziv, zcas, cenap) VALUES (N'01005', N'Coca cola sirup                         ', N'01', N'l    ', 5, 0.00, N'                         ', 0, N'jano      ', N'2015-08-11 09:31:00', 5.4987);
INSERT INTO rp_BufetE.dbo.rzbozi (zbozi, nazev, skupina, mj, minim, cena, carkod, aktivni, zuziv, zcas, cenap) VALUES (N'02001', N'Kukurica                                ', N'02', N'Kg   ', 0, 0.00, N'                         ', 1, N'jano      ', N'2022-03-01 12:07:00', 1.5142);

INSERT INTO rp_BufetE.dbo.rzboziskl (sklad, zbozi, cenaskl, mnozskl, minim, datprij, datvydej, zuziv, zcas) VALUES (N'01  ', N'01005', 0.7940200006701161, 0.14500000029147486, 0.000, N'2016-03-01 13:27:57.780', N'2016-04-03 20:49:09.490', N'maja      ', N'2016-04-03 20:50:00');
INSERT INTO rp_BufetE.dbo.rzboziskl (sklad, zbozi, cenaskl, mnozskl, minim, datprij, datvydej, zuziv, zcas) VALUES (N'01  ', N'02001', 1060.6178383365059, 703.3274790029997, 0.000, N'2023-08-10 16:49:01.457', N'2023-09-25 11:40:52.560', N'DISdata   ', N'2023-09-25 11:40:00');
INSERT INTO rp_BufetE.dbo.rzboziskl (sklad, zbozi, cenaskl, mnozskl, minim, datprij, datvydej, zuziv, zcas) VALUES (N'01  ', N'05002', 0, 0, 0.000, N'2019-04-02 00:42:20.297', N'2019-04-19 21:28:52.990', N'monika    ', N'2019-04-19 21:29:00');
