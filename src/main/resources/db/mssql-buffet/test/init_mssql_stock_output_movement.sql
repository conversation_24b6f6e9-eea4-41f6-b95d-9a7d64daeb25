------------------------
--- init test tables ---
------------------------
/****** Object:  Table [dbo].[rvydej]    Script Date: 16.06.2023 8:57:41 ******/
SET ANSI_NULLS ON
;
SET QUOTED_IDENTIFIER ON
;
CREATE TABLE [dbo].[rvydej](
    [rvydejID] [int] IDENTITY(1,1) NOT NULL,
    [rmenuID] [int] NOT NULL,
    [rzboziID] [int] NOT NULL,
    [rprodejID] [int] NOT NULL,
    [rprijID] [int] NOT NULL,
    [zeskladu] [char](4) NOT NULL,
    [nastr] [char](4) NOT NULL,
    [druhv] [tinyint] NOT NULL,
    [zbozi] [char](5) NOT NULL,
    [mnoz] [float] NOT NULL,
    [cena] [float] NOT NULL,
    [DPHsk] [smallint] NOT NULL,
    [datum] [smalldatetime] NULL,
    [zaloz] [datetime] NULL,
    [pozn] [varchar](40) NOT NULL,
    [zuziv] [char](10) NOT NULL,
    [zcas] [datetime] NULL,
    [lvse] [bit] NOT NULL,
    CONSTRAINT [PK__rvydej__04E4BC85] PRIMARY KEY CLUSTERED
(
[rvydejID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
    ) ON [PRIMARY]
;

----------------------
--- init test data ---
----------------------
INSERT INTO rp_BufetE.dbo.rvydej (rmenuID,rzboziID,rprodejID,rprijID,zeskladu,nastr,druhv,zbozi,mnoz,cena,DPHsk,datum,zaloz,pozn,zuziv,zcas,lvse) VALUES (533,22,1254557,0,N'01  ',N'    ',2,N'04002',1.54,0.03962911745208026,20,'2024-02-21 11:30:00.000','2019-05-03 17:37:00.000',N'Stock Input 1',N'DISdata   ','2019-05-03 16:37:00.000',0);
INSERT INTO rp_BufetE.dbo.rvydej (rmenuID,rzboziID,rprodejID,rprijID,zeskladu,nastr,druhv,zbozi,mnoz,cena,DPHsk,datum,zaloz,pozn,zuziv,zcas,lvse) VALUES (533,16,1254557,0,N'01  ',N'    ',3,N'02001',2,1.8840943829578858,20,'2024-02-21 11:30:00.000','2019-05-04 17:37:00.000',N'Stock Input 2',N'DISdata   ','2019-05-04 16:37:00.000',0);
INSERT INTO rp_BufetE.dbo.rvydej (rmenuID,rzboziID,rprodejID,rprijID,zeskladu,nastr,druhv,zbozi,mnoz,cena,DPHsk,datum,zaloz,pozn,zuziv,zcas,lvse) VALUES (533,17,1254557,0,N'01  ',N'    ',2,N'02002',3,2.45669954193084,20,'2024-02-21 11:30:00.000','2019-05-05 17:37:00.000',N'Stock Input 3',N'DISdata   ','2019-05-05 16:37:00.000',0);
INSERT INTO rp_BufetE.dbo.rvydej (rmenuID,rzboziID,rprodejID,rprijID,zeskladu,nastr,druhv,zbozi,mnoz,cena,DPHsk,datum,zaloz,pozn,zuziv,zcas,lvse) VALUES (533,22,1254557,0,N'01  ',N'    ',4,N'04002',1.54,0.03962911745208026,20,'2024-02-21 11:30:00.000','2019-05-03 17:37:00.000',N'Stock Input 1',N'DISdata   ','2019-05-03 16:37:00.000',0);

