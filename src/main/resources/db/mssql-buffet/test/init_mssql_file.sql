-----------------------
--- init test table ---
-----------------------
/****** Object:  Table [dbo].[images]    Script Date: 16.06.2023 8:57:41 ******/
SET ANSI_NULLS ON
    ;
SET QUOTED_IDENTIFIER ON
    ;
CREATE TABLE [dbo].[images](
    [imagesID] [int] IDENTITY(1,1) NOT NULL,
    [nazev] [varchar](50) NOT NULL,
    [img] [image] NULL,
    [pripona] [char](10) NULL,
    [zuziv] [char](10) NOT NULL,
    [zcas] [smalldatetime] NULL,
    PRIMARY KEY CLUSTERED
(
[imagesID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
    ) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
;

ALTER TABLE [dbo].[images] ADD  DEFAULT ('') FOR [zuziv]
;
ALTER TABLE [dbo].[images] ADD  DEFAULT (getdate()) FOR [zcas]
;
----------------------
--- init test data ---
----------------------
INSERT INTO rp_BufetE.dbo.images (nazev, img, pripona, zuziv, zcas) VALUES(N'HARIBO.PNG                                          ', 0x89504E470D0A1A0A0000000D49484452000000640000004B0806000000861822B7000000097048597300000B1300000B1301009A9C18000000C549444154789CEDD1310D00300CC0B01EE58FB9F7102C878D20527648D9DF01BC0C893124C6901843620C893124C6901843620C893124C6901843620C893124C6901843620C893124C6901843620C893124C6901843620C893124C6901843620C893124C6901843620C893124C6901843620C893124C6901843620C893124C6901843620C893124C6901843620C893124C6901843620C893124C6901843620C893124C6901843620C893124C6901843620C893124C6901843620C893124C6901843620C89393A9D012D913CA7FA0000000049454E44AE426082, N'PNG       ', N'DISdata   ', '2014-10-27 11:00:00.000');
INSERT INTO rp_BufetE.dbo.images (nazev, img, pripona, zuziv, zcas) VALUES(N'ZACAPA.JPG                                          ', 0x89504E470D0A1A0A0000000D49484452000000640000004B0806000000861822B7000000097048597300000B1300000B1301009A9C18000000C549444154789CEDD1310D00300CC0B01EE58FB9F7102C878D20527648D9DF01BC0C893124C6901843620C893124C6901843620C893124C6901843620C893124C6901843620C893124C6901843620C893124C6901843620C893124C6901843620C893124C6901843620C893124C6901843620C893124C6901843620C893124C6901843620C893124C6901843620C893124C6901843620C893124C6901843620C893124C6901843620C893124C6901843620C893124C6901843620C893124C6901843620C89393A9D012D913CA7FA0000000049454E44AE426082, N'JPG       ', N'DISdata   ', '2014-10-28 12:00:00.000');
INSERT INTO rp_BufetE.dbo.images (nazev, img, pripona, zuziv, zcas) VALUES(N'MENU NAPOJE.PNG                                          ', 0x89504E470D0A1A0A0000000D49484452000000640000004B0806000000861822B7000000097048597300000B1300000B1301009A9C18000000C549444154789CEDD1310D00300CC0B01EE58FB9F7102C878D20527648D9DF01BC0C893124C6901843620C893124C6901843620C893124C6901843620C893124C6901843620C893124C6901843620C893124C6901843620C893124C6901843620C893124C6901843620C893124C6901843620C893124C6901843620C893124C6901843620C893124C6901843620C893124C6901843620C893124C6901843620C893124C6901843620C893124C6901843620C893124C6901843620C893124C6901843620C89393A9D012D913CA7FA0000000049454E44AE426082, N'PNG       ', N'DISdata   ', '2015-10-28 13:00:00.000');
-- invalid records: blank image file name (NENI.PNG), null img attribute
INSERT INTO rp_BufetE.dbo.images (nazev, img, pripona, zuziv, zcas) VALUES(N'NENI.PNG                                          ', 0x89504E470D0A1A0A0000000D49484452000000640000004B0806000000861822B7000000097048597300000B1300000B1301009A9C18000000C549444154789CEDD1310D00300CC0B01EE58FB9F7102C878D20527648D9DF01BC0C893124C6901843620C893124C6901843620C893124C6901843620C893124C6901843620C893124C6901843620C893124C6901843620C893124C6901843620C893124C6901843620C893124C6901843620C893124C6901843620C893124C6901843620C893124C6901843620C893124C6901843620C893124C6901843620C893124C6901843620C893124C6901843620C893124C6901843620C893124C6901843620C89393A9D012D913CA7FA0000000049454E44AE426082, N'PNG       ', N'DISdata   ', '2016-10-27 11:00:00.000');
INSERT INTO rp_BufetE.dbo.images (nazev, img, pripona, zuziv, zcas) VALUES(N'OBR.BMP                                           ', NULL, N'BMP       ', N'jano      ', '2017-09-29 15:00:00.000');
