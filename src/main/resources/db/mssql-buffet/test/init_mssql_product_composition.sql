------------------------
--- init test tables ---
------------------------
SET ANSI_NULLS ON
    ;
SET QUOTED_IDENTIFIER ON
    ;
CREATE TABLE [dbo].[rpolm](
    [rpolmID] [int] IDENTITY(1,1) NOT NULL,
    [rmenuID] [int] NOT NULL,
    [rzboziID] [int] NOT NULL,
    [menu] [char](5) NOT NULL,
    [zbozi] [char](5) NOT NULL,
    [mnozs] [float] NOT NULL,
    [zuziv] [char](10) NOT NULL,
    [zcas] [smalldatetime] NULL,
    [rmenu2ID] [int] NOT NULL DEFAULT 0,
    [rzbozirID] [int] NOT NULL DEFAULT 0,
    CONSTRAINT [PK__rpolm__2CF2ADDF] PRIMARY KEY CLUSTERED
(
[rpolmID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
    ) ON [PRIMARY]
;

ALTER TABLE [dbo].[rpolm] ADD  CONSTRAINT [DF_rpolm_menu]  DEFAULT ('') FOR [menu]
;
ALTER TABLE [dbo].[rpolm] ADD  CONSTRAINT [DF_rpolm_zbozi]  DEFAULT ('') FOR [zbozi]
;
ALTER TABLE [dbo].[rpolm] ADD  CONSTRAINT [DF__rpolm__zuziv__2DE6D218]  DEFAULT ('') FOR [zuziv]
;
ALTER TABLE [dbo].[rpolm] ADD  CONSTRAINT [DF__rpolm__zcas__2EDAF651]  DEFAULT (getdate()) FOR [zcas]
;

----------------------
--- init test data ---
----------------------
INSERT INTO rp_BufetE.dbo.rpolm (rmenuID, rzboziID, menu, zbozi, mnozs, zuziv, zcas, rmenu2ID, rzbozirID) VALUES (57, 16, N'01054', N'02001', 0.2268, N'DISdata   ', N'2008-10-31 00:19:00', 0, 0);
INSERT INTO rp_BufetE.dbo.rpolm (rmenuID, rzboziID, menu, zbozi, mnozs, zuziv, zcas, rmenu2ID, rzbozirID) VALUES (57, 18, N'01054', N'02003', 0.06798, N'DISdata   ', N'2008-11-26 20:19:00', 0, 0);
INSERT INTO rp_BufetE.dbo.rpolm (rmenuID, rzboziID, menu, zbozi, mnozs, zuziv, zcas, rmenu2ID, rzbozirID) VALUES (57, 17, N'01054', N'02002', 0.00838, N'jano      ', N'2022-04-04 16:36:00', 0, 0);
-- invalid product id
INSERT INTO rp_BufetE.dbo.rpolm (rmenuID, rzboziID, menu, zbozi, mnozs, zuziv, zcas, rmenu2ID, rzbozirID) VALUES (100, 16, N'01054', N'02001', 0.2268, N'DISdata   ', N'2008-10-31 00:19:00', 0, 0);
-- invalid product component id
INSERT INTO rp_BufetE.dbo.rpolm (rmenuID, rzboziID, menu, zbozi, mnozs, zuziv, zcas, rmenu2ID, rzbozirID) VALUES (57, 100, N'01054', N'02001', 0.2268, N'DISdata   ', N'2008-10-31 00:19:00', 0, 0);
-- with product in product
INSERT INTO rp_BufetE.dbo.rpolm (rmenuID, rzboziID, menu, zbozi, mnozs, zuziv, zcas, rmenu2ID, rzbozirID) VALUES (57, 0, N'01054', N'02001', 5, N'DISdata   ', N'2008-10-31 00:19:00', 111, 0);
-- with package deposit product id in rzbozirID, but null rzboziID and null rmenu2ID
INSERT INTO rp_BufetE.dbo.rpolm (rmenuID, rzboziID, menu, zbozi, mnozs, zuziv, zcas, rmenu2ID, rzbozirID) VALUES (57, 0, N'01054', N'02001', 2, N'DISdata   ', N'2008-10-31 00:19:00', 0, 68);