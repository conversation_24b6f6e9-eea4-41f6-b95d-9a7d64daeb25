/****** Object:  Table [dbo].[bonus]    Script Date: 16.06.2023 8:57:41 ******/
SET ANSI_NULLS ON
    ;
SET QUOTED_IDENTIFIER ON
    ;
CREATE TABLE [dbo].[bonus](
    [bonusID] [int] IDENTITY(1,1) NOT NULL,
    [rprodej<PERSON>] [int] NOT NULL,
    [karta] [char](10) NOT NULL,
    [doklad] [char](10) NOT NULL,
    [body] [int] NOT NULL,
    [celkem] [numeric](12, 2) NOT NULL,
    [zuziv] [char](10) NOT NULL,
    [zcas] [smalldatetime] NULL,
    PRIMARY KEY CLUSTERED
(
[bonusID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
    ) ON [PRIMARY]
    ;
/****** Object:  Table [dbo].[Combo]    Script Date: 16.06.2023 8:57:41 ******/
SET ANSI_NULLS ON
    ;
SET QUOTED_IDENTIFIER ON
    ;
CREATE TABLE [dbo].[Combo](
    [id] [int] IDENTITY(1,1) NOT NULL,
    [code] [varchar](255) NOT NULL,
    [name] [varchar](255) NOT NULL,
    [lastChange] [datetime] NULL,
    [lastChangeByUser] [varchar](255) NULL,
    [active] [tinyint] NOT NULL,
    [image] [varchar](255) NULL,
    [price] [float] NOT NULL,
    [description] [text] NULL,
    CONSTRAINT [PK_Combo] PRIMARY KEY CLUSTERED
(
[id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
    ) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
    ;
/****** Object:  Table [dbo].[Combo$Has Acl Branch]    Script Date: 16.06.2023 8:57:41 ******/
SET ANSI_NULLS ON
    ;
SET QUOTED_IDENTIFIER ON
    ;
CREATE TABLE [dbo].[Combo$Has Acl Branch](
    [id] [int] IDENTITY(1,1) NOT NULL,
    [idCombo] [int] NOT NULL,
    [codeAclBranch] [varchar](50) NOT NULL,
    CONSTRAINT [PK_Combo$Has Acl Branch] PRIMARY KEY CLUSTERED
(
[id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
    ) ON [PRIMARY]
    ;
/****** Object:  Table [dbo].[Combo$Has Menu]    Script Date: 16.06.2023 8:57:41 ******/
SET ANSI_NULLS ON
    ;
SET QUOTED_IDENTIFIER ON
    ;
CREATE TABLE [dbo].[Combo$Has Menu](
    [id] [int] IDENTITY(1,1) NOT NULL,
    [idCombo] [int] NOT NULL,
    [idMenu] [int] NOT NULL,
    [position] [int] NOT NULL,
    CONSTRAINT [PK_Combo$Has Menu] PRIMARY KEY CLUSTERED
(
[id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
    ) ON [PRIMARY]
    ;
/****** Object:  Table [dbo].[Combo$Has Step]    Script Date: 16.06.2023 8:57:41 ******/
SET ANSI_NULLS ON
    ;
SET QUOTED_IDENTIFIER ON
    ;
CREATE TABLE [dbo].[Combo$Has Step](
    [id] [int] IDENTITY(1,1) NOT NULL,
    [idCombo] [int] NOT NULL,
    [idComboStep] [int] NOT NULL,
    [position] [int] NOT NULL,
    CONSTRAINT [PK_Combo$Has Step] PRIMARY KEY CLUSTERED
(
[id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
    ) ON [PRIMARY]
    ;
/****** Object:  Table [dbo].[Combo$Step]    Script Date: 16.06.2023 8:57:41 ******/
SET ANSI_NULLS ON
    ;
SET QUOTED_IDENTIFIER ON
    ;
CREATE TABLE [dbo].[Combo$Step](
    [id] [int] IDENTITY(1,1) NOT NULL,
    [name] [varchar](255) NOT NULL,
    [minItems] [int] NOT NULL,
    CONSTRAINT [PK_Combo$Step] PRIMARY KEY CLUSTERED
(
[id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
    ) ON [PRIMARY]
    ;
/****** Object:  Table [dbo].[Combo$Step Item]    Script Date: 16.06.2023 8:57:41 ******/
SET ANSI_NULLS ON
    ;
SET QUOTED_IDENTIFIER ON
    ;
CREATE TABLE [dbo].[Combo$Step Item](
    [id] [int] IDENTITY(1,1) NOT NULL,
    [idComboStep] [int] NOT NULL,
    [idMenu] [int] NOT NULL,
    [position] [int] NOT NULL,
    CONSTRAINT [PK_Combo$Step Item] PRIMARY KEY CLUSTERED
(
[id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
    ) ON [PRIMARY]
    ;
/****** Object:  Table [dbo].[chybac]    Script Date: 16.06.2023 8:57:41 ******/
SET ANSI_NULLS ON
    ;
SET QUOTED_IDENTIFIER ON
    ;
CREATE TABLE [dbo].[chybac](
    [chybacID] [int] IDENTITY(1,1) NOT NULL,
    [stanice] [varchar](50) NOT NULL,
    [datum] [smalldatetime] NULL,
    [chyba] [text] NULL,
    [zuziv] [char](10) NOT NULL,
    [zcas] [smalldatetime] NULL,
    PRIMARY KEY CLUSTERED
(
[chybacID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
    ) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
    ;
/****** Object:  Table [dbo].[images]    Script Date: 16.06.2023 8:57:41 ******/
SET ANSI_NULLS ON
    ;
SET QUOTED_IDENTIFIER ON
    ;
CREATE TABLE [dbo].[images](
    [imagesID] [int] IDENTITY(1,1) NOT NULL,
    [nazev] [varchar](50) NOT NULL,
    [img] [image] NULL,
    [pripona] [char](10) NULL,
    [zuziv] [char](10) NOT NULL,
    [zcas] [smalldatetime] NULL,
    PRIMARY KEY CLUSTERED
(
[imagesID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
    ) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
    ;
/****** Object:  Table [dbo].[Menu$Has Acl Branch]    Script Date: 16.06.2023 8:57:41 ******/
SET ANSI_NULLS ON
    ;
SET QUOTED_IDENTIFIER ON
    ;
CREATE TABLE [dbo].[Menu$Has Acl Branch](
    [id] [int] IDENTITY(1,1) NOT NULL,
    [idMenu] [int] NOT NULL,
    [codeAclBranch] [varchar](50) NULL,
    CONSTRAINT [PK_Menu$Has Acl Branch] PRIMARY KEY CLUSTERED
(
[id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
    ) ON [PRIMARY]
    ;
/****** Object:  Table [dbo].[NonStockProduct$Has Acl Branch]    Script Date: 16.06.2023 8:57:41 ******/
SET ANSI_NULLS ON
    ;
SET QUOTED_IDENTIFIER ON
    ;
CREATE TABLE [dbo].[NonStockProduct$Has Acl Branch](
    [id] [int] IDENTITY(1,1) NOT NULL,
    [idNonStockProduct] [int] NOT NULL,
    [codeAclBranch] [varchar](50) NOT NULL,
    CONSTRAINT [PK_NonStockProduct$Has Acl Branch] PRIMARY KEY CLUSTERED
(
[id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
    ) ON [PRIMARY]
    ;
/****** Object:  Table [dbo].[param]    Script Date: 16.06.2023 8:57:41 ******/
SET ANSI_NULLS ON
    ;
SET QUOTED_IDENTIFIER ON
    ;
CREATE TABLE [dbo].[param](
    [paramID] [int] IDENTITY(1,1) NOT NULL,
    [jmeno] [char](15) NOT NULL,
    [obsah] [varchar](150) NOT NULL,
    [typ] [nchar](1) NOT NULL,
    [popis] [varchar](50) NOT NULL,
    [modifik] [bit] NOT NULL,
    [zobrazit] [bit] NOT NULL,
    [mazat] [bit] NOT NULL,
    [nacist] [bit] NOT NULL,
    [zuziv] [char](10) NOT NULL,
    [zcas] [smalldatetime] NULL,
    PRIMARY KEY CLUSTERED
(
[paramID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
    ) ON [PRIMARY]
    ;
/****** Object:  Table [dbo].[pc]    Script Date: 16.06.2023 8:57:41 ******/
SET ANSI_NULLS ON
    ;
SET QUOTED_IDENTIFIER ON
    ;
CREATE TABLE [dbo].[pc](
    [pcID] [int] IDENTITY(1,1) NOT NULL,
    [nazev] [varchar](20) NOT NULL,
    [popis] [text] NULL,
    [lnepocitat] [bit] NOT NULL,
    [zuziv] [char](10) NOT NULL,
    [zcas] [smalldatetime] NULL,
    PRIMARY KEY CLUSTERED
(
[pcID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
    ) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
    ;
/****** Object:  Table [dbo].[prava]    Script Date: 16.06.2023 8:57:41 ******/
SET ANSI_NULLS ON
    ;
SET QUOTED_IDENTIFIER ON
    ;
CREATE TABLE [dbo].[prava](
    [pravaID] [int] IDENTITY(1,1) NOT NULL,
    [klic] [char](5) NOT NULL,
    [nazev] [varchar](50) NOT NULL,
    PRIMARY KEY CLUSTERED
(
[pravaID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
    ) ON [PRIMARY]
    ;
/****** Object:  Table [dbo].[pravuz]    Script Date: 16.06.2023 8:57:41 ******/
SET ANSI_NULLS ON
    ;
SET QUOTED_IDENTIFIER ON
    ;
CREATE TABLE [dbo].[pravuz](
    [pravuzID] [int] IDENTITY(1,1) NOT NULL,
    [pravaID] [int] NOT NULL,
    [ruzivID] [int] NOT NULL,
    PRIMARY KEY CLUSTERED
(
[pravuzID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
    ) ON [PRIMARY]
    ;
/****** Object:  Table [dbo].[prodmenu]    Script Date: 16.06.2023 8:57:41 ******/
SET ANSI_NULLS ON
    ;
SET QUOTED_IDENTIFIER ON
    ;
CREATE TABLE [dbo].[prodmenu](
    [prodmenuID] [int] IDENTITY(1,1) NOT NULL,
    [pokladna] [char](10) NOT NULL,
    [stolyID] [int] NOT NULL,
    [rmenuID] [int] NOT NULL,
    [nazev] [char](40) NOT NULL,
    [pocet] [char](20) NOT NULL,
    [celkem] [numeric](14, 2) NOT NULL,
    [stav] [char](1) NOT NULL,
    [doklad] [char](20) NOT NULL,
    [dph] [smallint] NOT NULL,
    [zuziv] [char](10) NOT NULL,
    [zcas] [smalldatetime] NULL,
    [rzbozirID] [int] NOT NULL,
    [rsedadlaID] [int] NOT NULL,
    [nazevf] [varchar](50) NOT NULL,
    [nazevf2] [varchar](45) NOT NULL,
    [sluzbyVIP] [numeric](6, 2) NOT NULL,
    [voucher] [char](10) NOT NULL,
    PRIMARY KEY CLUSTERED
(
[prodmenuID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
    ) ON [PRIMARY]
    ;
/****** Object:  Table [dbo].[Product$Has Acl Branch]    Script Date: 16.06.2023 8:57:41 ******/
SET ANSI_NULLS ON
    ;
SET QUOTED_IDENTIFIER ON
    ;
CREATE TABLE [dbo].[Product$Has Acl Branch](
    [id] [int] IDENTITY(1,1) NOT NULL,
    [idProduct] [int] NOT NULL,
    [codeAclBranch] [varchar](50) NOT NULL,
    CONSTRAINT [PK_Product$Has Acl Branch_1] PRIMARY KEY CLUSTERED
(
[id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY],
    CONSTRAINT [IX_Product$Has Acl Branch] UNIQUE NONCLUSTERED
(
    [codeAclBranch] ASC,
[idProduct] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
    ) ON [PRIMARY]
    ;
/****** Object:  Table [dbo].[rcdme]    Script Date: 16.06.2023 8:57:41 ******/
SET ANSI_NULLS ON
    ;
SET QUOTED_IDENTIFIER ON
    ;
CREATE TABLE [dbo].[rcdme](
    [rcdmeID] [int] IDENTITY(1,1) NOT NULL,
    [druh] [char](2) NOT NULL,
    [nazev] [varchar](30) NOT NULL,
    [dan] [smallint] NOT NULL,
    [barva] [int] NOT NULL,
    [zuziv] [char](10) NOT NULL,
    [zcas] [smalldatetime] NULL,
    [lsleva] [bit] NOT NULL,
    [pc] [int] NOT NULL,
    [obrazek] [varchar](50) NOT NULL,
    [ltablet] [bit] NOT NULL,
    [hexColor] [varchar](6) NULL,
    [lnezob] [bit] NOT NULL,
    CONSTRAINT [PK__rcdme__19DFD96B] PRIMARY KEY CLUSTERED
(
[rcdmeID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
    ) ON [PRIMARY]
    ;
/****** Object:  Table [dbo].[rcdsk]    Script Date: 16.06.2023 8:57:41 ******/
SET ANSI_NULLS ON
    ;
SET QUOTED_IDENTIFIER ON
    ;
CREATE TABLE [dbo].[rcdsk](
    [rcdskID] [int] IDENTITY(1,1) NOT NULL,
    [druh] [char](2) NOT NULL,
    [nazev] [varchar](30) NOT NULL,
    [dan] [smallint] NOT NULL,
    [zuziv] [char](10) NOT NULL,
    [zcas] [smalldatetime] NULL,
    CONSTRAINT [PK_rcdsk] PRIMARY KEY CLUSTERED
(
[rcdskID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
    ) ON [PRIMARY]
    ;
/****** Object:  Table [dbo].[rcpoh]    Script Date: 16.06.2023 8:57:41 ******/
SET ANSI_NULLS ON
    ;
SET QUOTED_IDENTIFIER ON
    ;
CREATE TABLE [dbo].[rcpoh](
    [rcpohID] [int] IDENTITY(1,1) NOT NULL,
    [rcradID] [int] NOT NULL,
    [pohyb] [char](2) NOT NULL,
    [nazev] [varchar](40) NOT NULL,
    [cena] [money] NOT NULL,
    [druhp] [char](1) NOT NULL,
    [typp] [char](1) NOT NULL,
    [platnost] [bit] NOT NULL,
    [zuziv] [char](10) NOT NULL,
    [zcas] [smalldatetime] NULL,
    PRIMARY KEY CLUSTERED
(
[rcpohID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
    ) ON [PRIMARY]
    ;
/****** Object:  Table [dbo].[rcrad]    Script Date: 16.06.2023 8:57:41 ******/
SET ANSI_NULLS ON
    ;
SET QUOTED_IDENTIFIER ON
    ;
CREATE TABLE [dbo].[rcrad](
    [rcradID] [int] IDENTITY(1,1) NOT NULL,
    [rada] [char](2) NOT NULL,
    [nazev] [varchar](40) NOT NULL,
    [pred] [char](5) NOT NULL,
    [za] [char](5) NOT NULL,
    [doklad] [int] NOT NULL,
    [zuziv] [char](10) NOT NULL,
    [zcas] [smalldatetime] NULL,
    PRIMARY KEY CLUSTERED
(
[rcradID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
    ) ON [PRIMARY]
    ;
/****** Object:  Table [dbo].[rcstr]    Script Date: 16.06.2023 8:57:41 ******/
SET ANSI_NULLS ON
    ;
SET QUOTED_IDENTIFIER ON
    ;
CREATE TABLE [dbo].[rcstr](
    [rcstrID] [int] IDENTITY(1,1) NOT NULL,
    [cstr] [char](4) NOT NULL,
    [nazev] [varchar](30) NOT NULL,
    [lsklad] [bit] NOT NULL,
    [lprodej] [bit] NOT NULL,
    [zuziv] [char](10) NOT NULL,
    [zcas] [smalldatetime] NULL,
    CONSTRAINT [PK__rcstr__4222D4EF] PRIMARY KEY CLUSTERED
(
[rcstrID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
    ) ON [PRIMARY]
    ;
/****** Object:  Table [dbo].[rdod]    Script Date: 16.06.2023 8:57:41 ******/
SET ANSI_NULLS ON
    ;
SET QUOTED_IDENTIFIER ON
    ;
CREATE TABLE [dbo].[rdod](
    [rdodID] [int] IDENTITY(1,1) NOT NULL,
    [cisdod] [char](6) NOT NULL,
    [nazev] [varchar](30) NOT NULL,
    [ulice] [varchar](40) NOT NULL,
    [misto] [varchar](40) NOT NULL,
    [psc] [char](6) NOT NULL,
    [jmeno1] [char](20) NOT NULL,
    [jmeno2] [char](20) NOT NULL,
    [jmeno3] [char](20) NOT NULL,
    [telefon1] [char](20) NOT NULL,
    [telefon2] [char](20) NOT NULL,
    [telefon3] [char](20) NOT NULL,
    [fax] [char](20) NOT NULL,
    [email] [varchar](70) NOT NULL,
    [banka] [varchar](40) NOT NULL,
    [ucet] [char](15) NOT NULL,
    [ico] [char](15) NOT NULL,
    [dic] [char](15) NOT NULL,
    [zuziv] [char](10) NOT NULL,
    [zcas] [smalldatetime] NULL,
    PRIMARY KEY CLUSTERED
(
[rdodID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
    ) ON [PRIMARY]
    ;
/****** Object:  Table [dbo].[rdoklad]    Script Date: 16.06.2023 8:57:41 ******/
SET ANSI_NULLS ON
    ;
SET QUOTED_IDENTIFIER ON
    ;
CREATE TABLE [dbo].[rdoklad](
    [rdokladID] [int] IDENTITY(1,1) NOT NULL,
    [rpoklID] [int] NOT NULL,
    [pokladna] [char](10) NOT NULL,
    [ruzivID] [int] NOT NULL,
    [vazba] [bit] NOT NULL,
    [dat_vys] [smalldatetime] NOT NULL,
    [dat_dod] [smalldatetime] NULL,
    [dat_dph] [smalldatetime] NULL,
    [dat_spl] [smalldatetime] NULL,
    [doklad] [char](12) NOT NULL,
    [reference] [char](15) NOT NULL,
    [varsymb] [char](10) NOT NULL,
    [pohyb] [char](2) NOT NULL,
    [rodbID] [int] NOT NULL,
    [pocet] [smallint] NOT NULL,
    [zaklad]  AS ([celkem]-[dph]),
    [dph] [numeric](9, 2) NOT NULL,
    [celkem] [numeric](11, 2) NOT NULL,
    [typ] [char](1) NOT NULL,
    [zuziv] [char](10) NOT NULL,
    [zcas] [smalldatetime] NULL,
    PRIMARY KEY CLUSTERED
(
[rdokladID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
    ) ON [PRIMARY]
    ;
/****** Object:  Table [dbo].[rklic]    Script Date: 16.06.2023 8:57:41 ******/
SET ANSI_NULLS ON
    ;
SET QUOTED_IDENTIFIER ON
    ;
CREATE TABLE [dbo].[rklic](
    [rklicID] [smallint] IDENTITY(1,1) NOT NULL,
    [cilic] [char](6) NOT NULL,
    [sn] [char](7) NOT NULL,
    [zarizeni] [char](6) NOT NULL,
    [provoz] [char](15) NOT NULL,
    [appl] [char](8) NOT NULL,
    [dan] [tinyint] NOT NULL,
    [ddan] [tinyint] NOT NULL,
    [zdan] [smallint] NOT NULL,
    [skuzby] [smallint] NOT NULL,
    [casprod] [smallint] NOT NULL,
    [pobocka] [smallint] NOT NULL,
    [verze] [char](15) NOT NULL,
    [zaokrouhl] [smallint] NOT NULL,
    CONSTRAINT [PK__rklic__47DBAE45] PRIMARY KEY CLUSTERED
(
[rklicID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
    ) ON [PRIMARY]
    ;
/****** Object:  Table [dbo].[rkurz]    Script Date: 16.06.2023 8:57:41 ******/
SET ANSI_NULLS ON
    ;
SET QUOTED_IDENTIFIER ON
    ;
CREATE TABLE [dbo].[rkurz](
    [rkurzID] [int] NOT NULL,
    [nazev] [varchar](40) NOT NULL,
    [mnozs] [smallint] NOT NULL,
    [znak] [char](5) NOT NULL,
    [znISO] [char](3) NULL,
    [kurz] [numeric](10, 3) NOT NULL,
    [zuziv] [char](10) NOT NULL,
    [zcas] [smalldatetime] NOT NULL
    ) ON [PRIMARY]
    ;
/****** Object:  Table [dbo].[rmenu]    Script Date: 16.06.2023 8:57:41 ******/
SET ANSI_NULLS ON
    ;
SET QUOTED_IDENTIFIER ON
    ;
CREATE TABLE [dbo].[rmenu](
    [rmenuID] [int] IDENTITY(1,1) NOT NULL,
    [cislo] [char](5) NOT NULL,
    [nazev] [varchar](40) NOT NULL,
    [druh] [char](2) NOT NULL,
    [minim] [smallint] NOT NULL,
    [cena] [numeric](9, 2) NOT NULL,
    [carkod] [char](25) NOT NULL,
    [rezervace] [smallint] NOT NULL,
    [aktivni] [bit] NOT NULL,
    [zuziv] [char](10) NOT NULL,
    [zcas] [datetime] NULL,
    [cenacelk] [money] NOT NULL,
    [lcelk] [bit] NOT NULL,
    [sklad] [char](4) NOT NULL,
    [lzbozi] [bit] NOT NULL,
    [poradiTablet] [int] NOT NULL,
    [poradiProdej] [int] NOT NULL,
    [lBufet] [bit] NOT NULL,
    [lBar] [bit] NOT NULL,
    [lTablet] [bit] NOT NULL,
    [poznamka] [text] NULL,
    [obrazek] [varchar](50) NOT NULL,
    [tabletne] [bit] NOT NULL,
    [lastChangeByUser] [varchar](255) NULL,
    [nazev2] [varchar](40) NOT NULL,
    CONSTRAINT [PK_rmenu] PRIMARY KEY CLUSTERED
(
[rmenuID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
    ) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
    ;
/****** Object:  Table [dbo].[rpokl]    Script Date: 16.06.2023 8:57:41 ******/
SET ANSI_NULLS ON
    ;
SET QUOTED_IDENTIFIER ON
    ;
CREATE TABLE [dbo].[rpokl](
    [rpoklID] [int] IDENTITY(1,1) NOT NULL,
    [pokladna] [char](10) NOT NULL,
    [doklad] [char](4) NOT NULL,
    [datum] [smalldatetime] NOT NULL,
    [pocstav] [numeric](12, 2) NOT NULL,
    [prodejho] [numeric](10, 2) NOT NULL,
    [prodejnh] [numeric](10, 2) NOT NULL,
    [sluzbyho] [numeric](10, 2) NOT NULL,
    [sluzbynh] [numeric](10, 2) NOT NULL,
    [vracenho] [numeric](8, 2) NOT NULL,
    [vracennh] [numeric](8, 2) NOT NULL,
    [oprijem] [numeric](10, 2) NOT NULL,
    [ovydej] [numeric](10, 2) NOT NULL,
    [odvod] [numeric](11, 2) NOT NULL,
    [zust_den]  AS ((((((([prodejho]+[prodejnh])+[sluzbyho])+[sluzbynh])-[vracenho])-[vracennh])+[oprijem])-[ovydej]),
    [zust_pok]  AS (((((([pocstav]+[prodejho])+[sluzbyho])-[vracenho])+[oprijem])-[ovydej])-[odvod]),
    [ruzivID] [int] NOT NULL,
    [zuziv] [char](10) NOT NULL,
    [zcas] [smalldatetime] NULL,
    PRIMARY KEY CLUSTERED
(
[rpoklID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
    ) ON [PRIMARY]
    ;
/****** Object:  Table [dbo].[rpolm]    Script Date: 16.06.2023 8:57:41 ******/
SET ANSI_NULLS ON
    ;
SET QUOTED_IDENTIFIER ON
    ;
CREATE TABLE [dbo].[rpolm](
    [rpolmID] [int] IDENTITY(1,1) NOT NULL,
    [rmenuID] [int] NOT NULL,
    [rzboziID] [int] NOT NULL,
    [menu] [char](5) NOT NULL,
    [zbozi] [char](5) NOT NULL,
    [mnozs] [float] NOT NULL,
    [zuziv] [char](10) NOT NULL,
    [zcas] [smalldatetime] NULL,
    [rmenu2ID] [int] NOT NULL DEFAULT 0,
    [rzbozirID] [int] NOT NULL DEFAULT 0,
    CONSTRAINT [PK__rpolm__2CF2ADDF] PRIMARY KEY CLUSTERED
(
[rpolmID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
    ) ON [PRIMARY]
    ;
/****** Object:  Table [dbo].[rprij]    Script Date: 16.06.2023 8:57:41 ******/
SET ANSI_NULLS ON
    ;
SET QUOTED_IDENTIFIER ON
    ;
CREATE TABLE [dbo].[rprij](
    [rprijID] [int] IDENTITY(1,1) NOT NULL,
    [rzboziID] [int] NOT NULL,
    [rdodID] [int] NOT NULL,
    [sklad] [char](4) NOT NULL,
    [zbozi] [char](5) NOT NULL,
    [cenadod] [float] NOT NULL,
    [cenacelk] [numeric](10, 3) NOT NULL,
    [lcelk] [bit] NOT NULL,
    [DPHsk] [smallint] NOT NULL,
    [datum] [smalldatetime] NULL,
    [zaloz] [datetime] NULL,
    [mnoz] [float] NOT NULL,
    [doklad] [char](15) NULL,
    [pozn] [varchar](40) NOT NULL,
    [zuziv] [char](10) NOT NULL,
    [zcas] [smalldatetime] NULL,
    CONSTRAINT [PK__rprij__7E37BEF6] PRIMARY KEY CLUSTERED
(
[rprijID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
    ) ON [PRIMARY]
    ;
/****** Object:  Table [dbo].[rprodej]    Script Date: 16.06.2023 8:57:41 ******/
SET ANSI_NULLS ON
    ;
SET QUOTED_IDENTIFIER ON
    ;
CREATE TABLE [dbo].[rprodej](
    [rprodejID] [int] IDENTITY(1,1) NOT NULL,
    [rmenuID] [int] NOT NULL,
    [rzboziID] [int] NOT NULL,
    [rpoklID] [int] NOT NULL,
    [doklad] [char](10) NOT NULL,
    [sklad] [char](4) NOT NULL,
    [cena] [numeric](12, 2) NOT NULL,
    [mnoz] [int] NOT NULL,
    [dph] [tinyint] NOT NULL,
    [sluzby] [numeric](6, 2) NOT NULL,
    [lvrat] [bit] NOT NULL,
    [lhoto] [bit] NOT NULL,
    [datprod] [smalldatetime] NULL,
    [datpokl] [smalldatetime] NULL,
    [uzivprod] [int] NOT NULL,
    [uzivpokl] [int] NOT NULL,
    [pokladna] [char](10) NOT NULL,
    [zuziv] [char](10) NOT NULL,
    [zcas] [smalldatetime] NULL,
    [karta] [char](10) NOT NULL,
    [rzbozirID] [int] NOT NULL,
    [pcID] [int] NOT NULL,
    [voucher] [char](10) NOT NULL,
    [ltablet] [bit] NOT NULL,
    [provoz] [smallint],
    CONSTRAINT [PK__rprodej__6501FCD8] PRIMARY KEY CLUSTERED
(
[rprodejID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
    ) ON [PRIMARY]
    ;
/****** Object:  Table [dbo].[rprodejRus]    Script Date: 16.06.2023 8:57:41 ******/
SET ANSI_NULLS ON
    ;
SET QUOTED_IDENTIFIER ON
    ;
CREATE TABLE [dbo].[rprodejRus](
    [rprodejRusID] [int] IDENTITY(1,1) NOT NULL,
    [rprodejID] [int] NOT NULL,
    [rmenuID] [int] NOT NULL,
    [rzboziID] [int] NOT NULL,
    [rpoklID] [int] NOT NULL,
    [doklad] [char](10) NOT NULL,
    [sklad] [char](4) NOT NULL,
    [cena] [numeric](12, 2) NOT NULL,
    [mnoz] [int] NOT NULL,
    [dph] [tinyint] NOT NULL,
    [sluzby] [numeric](6, 2) NOT NULL,
    [lvrat] [bit] NOT NULL,
    [lhoto] [bit] NOT NULL,
    [datprod] [smalldatetime] NULL,
    [datpokl] [smalldatetime] NULL,
    [uzivprod] [int] NOT NULL,
    [uzivpokl] [int] NOT NULL,
    [pokladna] [char](10) NOT NULL,
    [zuziv] [char](10) NOT NULL,
    [zcas] [smalldatetime] NULL,
    PRIMARY KEY CLUSTERED
(
[rprodejRusID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
    ) ON [PRIMARY]
    ;
/****** Object:  Table [dbo].[rprovoz]    Script Date: 16.06.2023 8:57:41 ******/
SET ANSI_NULLS ON
    ;
SET QUOTED_IDENTIFIER ON
    ;
CREATE TABLE [dbo].[rprovoz](
    [rprovozID] [int] NOT NULL,
    [sfirma] [varchar](40) NOT NULL,
    [spsc] [char](6) NOT NULL,
    [smisto] [varchar](40) NOT NULL,
    [sulice] [varchar](40) NOT NULL,
    [szkrf] [char](10) NOT NULL,
    [sreg] [varchar](40) NOT NULL,
    [sico] [char](15) NOT NULL,
    [stel] [char](15) NOT NULL,
    [sdic] [char](15) NOT NULL,
    [semail] [varchar](40) NOT NULL,
    [svyrizuje] [varchar](30) NOT NULL,
    [sbanka] [char](30) NOT NULL,
    [sfax] [char](15) NOT NULL,
    [sucet] [char](35) NOT NULL,
    [sreztab] [bit] NOT NULL,
    [sprecen] [bit] NOT NULL,
    PRIMARY KEY CLUSTERED
(
[rprovozID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
    ) ON [PRIMARY]
    ;
/****** Object:  Table [dbo].[rslev]    Script Date: 16.06.2023 8:57:41 ******/
SET ANSI_NULLS ON
    ;
SET QUOTED_IDENTIFIER ON
    ;
CREATE TABLE [dbo].[rslev](
    [rslevID] [int] IDENTITY(1,1) NOT NULL,
    [cislo] [char](5) NOT NULL,
    [sleva] [int] NOT NULL,
    [lproc] [bit] NOT NULL,
    [prijmeni] [char](20) NULL,
    [jmeno] [char](10) NULL,
    [adrmis] [char](20) NULL,
    [adrul] [char](20) NULL,
    [psc] [char](6) NULL,
    [telef] [char](15) NULL,
    [zuziv] [char](10) NULL,
    [zcas] [smalldatetime] NULL,
    PRIMARY KEY CLUSTERED
(
[rslevID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
    ) ON [PRIMARY]
    ;
/****** Object:  Table [dbo].[ruziv]    Script Date: 16.06.2023 8:57:41 ******/
SET ANSI_NULLS ON
    ;
SET QUOTED_IDENTIFIER ON
    ;
CREATE TABLE [dbo].[ruziv](
    [ruzivID] [smallint] IDENTITY(1,1) NOT NULL,
    [uziv] [char](10) NOT NULL,
    [Jmeno] [char](40) NOT NULL,
    [Heslo] [char](10) NOT NULL,
    [zapis] [bit] NOT NULL,
    [pokladna] [char](10) NOT NULL,
    [sklad] [char](4) NOT NULL,
    [datOd] [smalldatetime] NULL,
    [datDo] [smalldatetime] NULL,
    [datPrist] [smalldatetime] NULL,
    [pokD] [int] NOT NULL,
    [pokZ] [int] NOT NULL,
    [uzpokl] [smalldatetime] NULL,
    [prace] [char](12) NOT NULL,
    [vstup] [bit] NOT NULL,
    [old] [int] NOT NULL,
    [zuziv] [char](10) NOT NULL,
    [zcas] [smalldatetime] NULL,
    CONSTRAINT [PK__ruziv__36B12243] PRIMARY KEY CLUSTERED
(
[ruzivID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
    ) ON [PRIMARY]
    ;
/****** Object:  Table [dbo].[rvydej]    Script Date: 16.06.2023 8:57:41 ******/
SET ANSI_NULLS ON
    ;
SET QUOTED_IDENTIFIER ON
    ;
CREATE TABLE [dbo].[rvydej](
    [rvydejID] [int] IDENTITY(1,1) NOT NULL,
    [rmenuID] [int] NOT NULL,
    [rzboziID] [int] NOT NULL,
    [rprodejID] [int] NOT NULL,
    [rprijID] [int] NOT NULL,
    [zeskladu] [char](4) NOT NULL,
    [nastr] [char](4) NOT NULL,
    [druhv] [tinyint] NOT NULL,
    [zbozi] [char](5) NOT NULL,
    [mnoz] [float] NOT NULL,
    [cena] [float] NOT NULL,
    [DPHsk] [smallint] NOT NULL,
    [datum] [smalldatetime] NULL,
    [zaloz] [datetime] NULL,
    [pozn] [varchar](40) NOT NULL,
    [zuziv] [char](10) NOT NULL,
    [zcas] [datetime] NULL,
    [lvse] [bit] NOT NULL,
    CONSTRAINT [PK__rvydej__04E4BC85] PRIMARY KEY CLUSTERED
(
[rvydejID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
    ) ON [PRIMARY]
    ;
/****** Object:  Table [dbo].[rzbozi]    Script Date: 16.06.2023 8:57:41 ******/
SET ANSI_NULLS ON
    ;
SET QUOTED_IDENTIFIER ON
    ;
CREATE TABLE [dbo].[rzbozi](
    [rzboziID] [int] IDENTITY(1,1) NOT NULL,
    [zbozi] [char](5) NOT NULL,
    [nazev] [varchar](40) NOT NULL,
    [skupina] [char](2) NOT NULL,
    [mj] [char](5) NOT NULL,
    [minim] [int] NOT NULL,
    [cena] [numeric](9, 2) NOT NULL,
    [carkod] [char](25) NOT NULL,
    [aktivni] [bit] NOT NULL,
    [zuziv] [char](10) NOT NULL,
    [zcas] [smalldatetime] NULL,
    [cenap] [numeric](10, 4) NOT NULL,
    [lastChangeByUser] [varchar](255) NULL,
    CONSTRAINT [PK__rzbozi__6D0D32F4] PRIMARY KEY CLUSTERED
(
[rzboziID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
    ) ON [PRIMARY]
    ;
/****** Object:  Table [dbo].[rzbozir]    Script Date: 16.06.2023 8:57:41 ******/
SET ANSI_NULLS ON
    ;
SET QUOTED_IDENTIFIER ON
    ;
CREATE TABLE [dbo].[rzbozir](
    [rzbozirID] [int] IDENTITY(1,1) NOT NULL,
    [cislo] [char](5) NOT NULL,
    [nazev] [varchar](40) NOT NULL,
    [druh] [char](2) NOT NULL,
    [cena] [numeric](9, 2) NOT NULL,
    [aktivni] [bit] NOT NULL,
    [cenacelk] [money] NOT NULL,
    [lcelk] [bit] NOT NULL,
    [zuziv] [char](10) NOT NULL,
    [zcas] [smalldatetime] NULL,
    [lproc] [bit] NOT NULL,
    [lBufet] [bit] NOT NULL,
    [lBar] [bit] NOT NULL,
    [lTablet] [bit] NOT NULL,
    PRIMARY KEY CLUSTERED
(
[rzbozirID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
    ) ON [PRIMARY]
    ;
/****** Object:  Table [dbo].[rzboziskl]    Script Date: 16.06.2023 8:57:41 ******/
SET ANSI_NULLS ON
    ;
SET QUOTED_IDENTIFIER ON
    ;
CREATE TABLE [dbo].[rzboziskl](
    [rzbozisklID] [int] IDENTITY(1,1) NOT NULL,
    [sklad] [char](4) NOT NULL,
    [zbozi] [char](5) NOT NULL,
    [cenaskl] [float] NOT NULL,
    [mnozskl] [float] NOT NULL,
    [minim] [numeric](12, 3) NOT NULL,
    [datprij] [datetime] NULL,
    [datvydej] [datetime] NULL,
    [zuziv] [char](10) NOT NULL,
    [zcas] [smalldatetime] NULL,
    CONSTRAINT [PK__rzboziskl__24285DB4] PRIMARY KEY CLUSTERED
(
[rzbozisklID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
    ) ON [PRIMARY]
    ;
/****** Object:  Table [dbo].[rzboziskl0]    Script Date: 16.06.2023 8:57:41 ******/
SET ANSI_NULLS ON
    ;
SET QUOTED_IDENTIFIER ON
    ;
CREATE TABLE [dbo].[rzboziskl0](
    [rzbozisklID] [int] NOT NULL,
    [sklad] [char](4) NOT NULL,
    [zbozi] [char](5) NOT NULL,
    [cenaskl] [float] NOT NULL,
    [mnozskl] [float] NOT NULL,
    [minim] [numeric](12, 3) NOT NULL,
    [datprij] [datetime] NULL,
    [datvydej] [datetime] NULL,
    [zuziv] [char](10) NOT NULL,
    [zcas] [smalldatetime] NULL
    ) ON [PRIMARY]
    ;
/****** Object:  Table [dbo].[stavy]    Script Date: 16.06.2023 8:57:41 ******/
SET ANSI_NULLS ON
    ;
SET QUOTED_IDENTIFIER ON
    ;
CREATE TABLE [dbo].[stavy](
    [stavyID] [int] IDENTITY(1,1) NOT NULL,
    [sklad] [char](4) NOT NULL,
    [zbozi] [char](5) NOT NULL,
    [stavmj] [float] NOT NULL,
    [stavmj2] [float] NOT NULL,
    [cenacelk] [float] NOT NULL,
    [stavold] [float] NOT NULL,
    [cenaold] [float] NOT NULL,
    [zaloz] [datetime] NULL,
    [uloz] [datetime] NULL,
    [zuziv] [char](10) NOT NULL,
    [zcas] [smalldatetime] NULL,
    CONSTRAINT [PK__stavy__0CDAE408] PRIMARY KEY CLUSTERED
(
[stavyID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
    ) ON [PRIMARY]
    ;
/****** Object:  Table [dbo].[stoly]    Script Date: 16.06.2023 8:57:41 ******/
SET ANSI_NULLS ON
    ;
SET QUOTED_IDENTIFIER ON
    ;
CREATE TABLE [dbo].[stoly](
    [stolyID] [int] IDENTITY(1,1) NOT NULL,
    [nazev] [varchar](40) NOT NULL,
    [zuziv] [char](10) NOT NULL,
    [zcas] [smalldatetime] NULL,
    [karta] [char](10) NOT NULL,
    [MAC] [nvarchar](50) NULL,
    [lhoto] [bit] NOT NULL,
    [oktran] [bit] NOT NULL,
    [lvydej] [bit] NOT NULL,
    [taletProdej] [bit] NOT NULL,
    PRIMARY KEY CLUSTERED
(
[stolyID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
    ) ON [PRIMARY]
    ;
/****** Object:  Table [dbo].[tisks]    Script Date: 16.06.2023 8:57:41 ******/
SET ANSI_NULLS ON
    ;
SET QUOTED_IDENTIFIER ON
    ;
CREATE TABLE [dbo].[tisks](
    [tisksID] [int] IDENTITY(1,1) NOT NULL,
    [formul] [varchar](20) NOT NULL,
    [pc] [tinyint] NOT NULL,
    [nazev] [varchar](50) NOT NULL,
    [soubor] [varchar](200) NOT NULL,
    [dalsip] [varchar](100) NULL,
    [zuziv] [nchar](10) NOT NULL,
    [zcas] [smalldatetime] NULL,
    PRIMARY KEY CLUSTERED
(
[tisksID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
    ) ON [PRIMARY]
    ;
/****** Object:  Table [dbo].[typvst]    Script Date: 16.06.2023 8:57:41 ******/
SET ANSI_NULLS ON
    ;
SET QUOTED_IDENTIFIER ON
    ;
CREATE TABLE [dbo].[typvst](
    [typvstID] [int] IDENTITY(1,1) NOT NULL,
    [nazev] [varchar](30) NOT NULL,
    [definice] [text] NULL,
    [defcompil] [text] NULL,
    [uvodni] [varchar](50) NOT NULL,
    [koncove] [varchar](50) NOT NULL,
    [lhromad] [bit] NOT NULL,
    [lskolni] [bit] NOT NULL,
    [zuziv] [char](10) NOT NULL,
    [zcas] [smalldatetime] NULL,
    PRIMARY KEY CLUSTERED
(
[typvstID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
    ) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
    ;
/****** Object:  Table [dbo].[voucher]    Script Date: 16.06.2023 8:57:41 ******/
SET ANSI_NULLS ON
    ;
SET QUOTED_IDENTIFIER ON
    ;
CREATE TABLE [dbo].[voucher](
    [voucherID] [int] IDENTITY(1,1) NOT NULL,
    [cislo] [char](10) NOT NULL,
    [typ] [char](15) NOT NULL,
    [maxpo] [int] NOT NULL,
    [pocpo] [int] NOT NULL,
    [platod] [smalldatetime] NULL,
    [platdo] [smalldatetime] NULL,
    [slevac] [char](5) NOT NULL,
    [cmenu] [char](5) NOT NULL,
    [czbozir] [char](5) NOT NULL,
    [zuziv] [char](10) NOT NULL,
    [zcas] [smalldatetime] NULL,
    [sezmenu] [varchar](200) NOT NULL,
    PRIMARY KEY CLUSTERED
(
[voucherID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
    ) ON [PRIMARY]
    ;
/****** Object:  Table [dbo].[provozy]    Script Date: 16.1.2025 8:10:00 ******/
SET ANSI_NULLS ON
    ;
SET QUOTED_IDENTIFIER ON
    ;
CREATE TABLE [dbo].[provozy](
    [provozyID] [int] IDENTITY(1,1) NOT NULL,
    [cislo] [int] NOT NULL,
    [nazev] [varchar](40) NOT NULL,
    [zkratka] [char](3) DEFAULT ('') NOT NULL,
    [spoj] [varchar](150) DEFAULT ('') NOT NULL
);

ALTER TABLE [dbo].[bonus] ADD  DEFAULT ('') FOR [zuziv]
    ;
ALTER TABLE [dbo].[bonus] ADD  DEFAULT (getdate()) FOR [zcas]
    ;
ALTER TABLE [dbo].[Combo] ADD  DEFAULT ((1)) FOR [active]
    ;
ALTER TABLE [dbo].[chybac] ADD  DEFAULT (getdate()) FOR [datum]
    ;
ALTER TABLE [dbo].[chybac] ADD  DEFAULT ('') FOR [zuziv]
    ;
ALTER TABLE [dbo].[chybac] ADD  DEFAULT (getdate()) FOR [zcas]
    ;
ALTER TABLE [dbo].[images] ADD  DEFAULT ('') FOR [zuziv]
    ;
ALTER TABLE [dbo].[images] ADD  DEFAULT (getdate()) FOR [zcas]
    ;
ALTER TABLE [dbo].[param] ADD  DEFAULT ('') FOR [popis]
    ;
ALTER TABLE [dbo].[param] ADD  DEFAULT ((1)) FOR [modifik]
    ;
ALTER TABLE [dbo].[param] ADD  DEFAULT ((1)) FOR [zobrazit]
    ;
ALTER TABLE [dbo].[param] ADD  DEFAULT ((1)) FOR [mazat]
    ;
ALTER TABLE [dbo].[param] ADD  DEFAULT ((1)) FOR [nacist]
    ;
ALTER TABLE [dbo].[param] ADD  DEFAULT ('') FOR [zuziv]
    ;
ALTER TABLE [dbo].[param] ADD  DEFAULT (getdate()) FOR [zcas]
    ;
ALTER TABLE [dbo].[pc] ADD  DEFAULT ((0)) FOR [lnepocitat]
    ;
ALTER TABLE [dbo].[pc] ADD  DEFAULT ('') FOR [zuziv]
    ;
ALTER TABLE [dbo].[pc] ADD  DEFAULT (getdate()) FOR [zcas]
    ;
ALTER TABLE [dbo].[prodmenu] ADD  CONSTRAINT [DF_prodmenu_rmenuID]  DEFAULT ((0)) FOR [rmenuID]
    ;
ALTER TABLE [dbo].[prodmenu] ADD  DEFAULT ('') FOR [zuziv]
    ;
ALTER TABLE [dbo].[prodmenu] ADD  DEFAULT ((0)) FOR [rzbozirID]
    ;
ALTER TABLE [dbo].[prodmenu] ADD  DEFAULT ((0)) FOR [rsedadlaID]
    ;
ALTER TABLE [dbo].[prodmenu] ADD  DEFAULT ('') FOR [nazevf]
    ;
ALTER TABLE [dbo].[prodmenu] ADD  DEFAULT ('') FOR [nazevf2]
    ;
ALTER TABLE [dbo].[prodmenu] ADD  DEFAULT ((0)) FOR [sluzbyVIP]
    ;
ALTER TABLE [dbo].[prodmenu] ADD  DEFAULT ('') FOR [voucher]
    ;
ALTER TABLE [dbo].[rcdme] ADD  CONSTRAINT [DF__rcdme__nazev__1AD3FDA4]  DEFAULT ('') FOR [nazev]
    ;
ALTER TABLE [dbo].[rcdme] ADD  CONSTRAINT [DF__rcdme__dan__1DB06A4F]  DEFAULT ((0)) FOR [dan]
    ;
ALTER TABLE [dbo].[rcdme] ADD  CONSTRAINT [DF_rcdme_brava]  DEFAULT ((14215660)) FOR [barva]
    ;
ALTER TABLE [dbo].[rcdme] ADD  CONSTRAINT [DF__rcdme__zuziv__1BC821DD]  DEFAULT ('') FOR [zuziv]
    ;
ALTER TABLE [dbo].[rcdme] ADD  CONSTRAINT [DF__rcdme__zcas__1CBC4616]  DEFAULT (getdate()) FOR [zcas]
    ;
ALTER TABLE [dbo].[rcdme] ADD  DEFAULT ((0)) FOR [lsleva]
    ;
ALTER TABLE [dbo].[rcdme] ADD  DEFAULT ((0)) FOR [pc]
    ;
ALTER TABLE [dbo].[rcdme] ADD  DEFAULT ('') FOR [obrazek]
    ;
ALTER TABLE [dbo].[rcdme] ADD  DEFAULT ((0)) FOR [ltablet]
    ;
ALTER TABLE [dbo].[rcdme] ADD  DEFAULT ((0)) FOR [lnezob]
    ;
ALTER TABLE [dbo].[rcdsk] ADD  DEFAULT ('') FOR [nazev]
    ;
ALTER TABLE [dbo].[rcdsk] ADD  DEFAULT ((0)) FOR [dan]
    ;
ALTER TABLE [dbo].[rcdsk] ADD  DEFAULT ('') FOR [zuziv]
    ;
ALTER TABLE [dbo].[rcdsk] ADD  DEFAULT (getdate()) FOR [zcas]
    ;
ALTER TABLE [dbo].[rcpoh] ADD  DEFAULT ((0)) FOR [rcradID]
    ;
ALTER TABLE [dbo].[rcpoh] ADD  DEFAULT ('') FOR [nazev]
    ;
ALTER TABLE [dbo].[rcpoh] ADD  DEFAULT ((0)) FOR [cena]
    ;
ALTER TABLE [dbo].[rcpoh] ADD  DEFAULT ((1)) FOR [platnost]
    ;
ALTER TABLE [dbo].[rcpoh] ADD  DEFAULT ('') FOR [zuziv]
    ;
ALTER TABLE [dbo].[rcpoh] ADD  DEFAULT (getdate()) FOR [zcas]
    ;
ALTER TABLE [dbo].[rcrad] ADD  DEFAULT ('') FOR [nazev]
    ;
ALTER TABLE [dbo].[rcrad] ADD  DEFAULT ('') FOR [pred]
    ;
ALTER TABLE [dbo].[rcrad] ADD  DEFAULT ('') FOR [za]
    ;
ALTER TABLE [dbo].[rcrad] ADD  DEFAULT ((0)) FOR [doklad]
    ;
ALTER TABLE [dbo].[rcrad] ADD  DEFAULT ('') FOR [zuziv]
    ;
ALTER TABLE [dbo].[rcrad] ADD  DEFAULT (getdate()) FOR [zcas]
    ;
ALTER TABLE [dbo].[rcstr] ADD  CONSTRAINT [DF__rcstr__nazev__4316F928]  DEFAULT ('') FOR [nazev]
    ;
ALTER TABLE [dbo].[rcstr] ADD  CONSTRAINT [DF_rcstr_lsklad]  DEFAULT ((0)) FOR [lsklad]
    ;
ALTER TABLE [dbo].[rcstr] ADD  CONSTRAINT [DF_rcstr_lprodej]  DEFAULT ((0)) FOR [lprodej]
    ;
ALTER TABLE [dbo].[rcstr] ADD  CONSTRAINT [DF__rcstr__zuziv__440B1D61]  DEFAULT ('') FOR [zuziv]
    ;
ALTER TABLE [dbo].[rcstr] ADD  CONSTRAINT [DF__rcstr__zcas__44FF419A]  DEFAULT (getdate()) FOR [zcas]
    ;
ALTER TABLE [dbo].[rdod] ADD  DEFAULT ('') FOR [ulice]
    ;
ALTER TABLE [dbo].[rdod] ADD  DEFAULT ('') FOR [misto]
    ;
ALTER TABLE [dbo].[rdod] ADD  DEFAULT ('') FOR [psc]
    ;
ALTER TABLE [dbo].[rdod] ADD  DEFAULT ('') FOR [jmeno1]
    ;
ALTER TABLE [dbo].[rdod] ADD  DEFAULT ('') FOR [jmeno2]
    ;
ALTER TABLE [dbo].[rdod] ADD  DEFAULT ('') FOR [jmeno3]
    ;
ALTER TABLE [dbo].[rdod] ADD  DEFAULT ('') FOR [telefon1]
    ;
ALTER TABLE [dbo].[rdod] ADD  DEFAULT ('') FOR [telefon2]
    ;
ALTER TABLE [dbo].[rdod] ADD  DEFAULT ('') FOR [telefon3]
    ;
ALTER TABLE [dbo].[rdod] ADD  DEFAULT ('') FOR [fax]
    ;
ALTER TABLE [dbo].[rdod] ADD  DEFAULT ('') FOR [email]
    ;
ALTER TABLE [dbo].[rdod] ADD  DEFAULT ('') FOR [banka]
    ;
ALTER TABLE [dbo].[rdod] ADD  DEFAULT ('') FOR [ucet]
    ;
ALTER TABLE [dbo].[rdod] ADD  DEFAULT ('') FOR [ico]
    ;
ALTER TABLE [dbo].[rdod] ADD  DEFAULT ('') FOR [dic]
    ;
ALTER TABLE [dbo].[rdod] ADD  DEFAULT ('') FOR [zuziv]
    ;
ALTER TABLE [dbo].[rdod] ADD  DEFAULT (getdate()) FOR [zcas]
    ;
ALTER TABLE [dbo].[rdoklad] ADD  DEFAULT ((0)) FOR [rpoklID]
    ;
ALTER TABLE [dbo].[rdoklad] ADD  DEFAULT ((0)) FOR [vazba]
    ;
ALTER TABLE [dbo].[rdoklad] ADD  DEFAULT ('') FOR [reference]
    ;
ALTER TABLE [dbo].[rdoklad] ADD  DEFAULT ('') FOR [varsymb]
    ;
ALTER TABLE [dbo].[rdoklad] ADD  DEFAULT ((0)) FOR [rodbID]
    ;
ALTER TABLE [dbo].[rdoklad] ADD  DEFAULT ((0)) FOR [pocet]
    ;
ALTER TABLE [dbo].[rdoklad] ADD  DEFAULT (getdate()) FOR [zcas]
    ;
ALTER TABLE [dbo].[rklic] ADD  CONSTRAINT [DF__rklic__zarizeni__48CFD27E]  DEFAULT ('') FOR [zarizeni]
    ;
ALTER TABLE [dbo].[rklic] ADD  CONSTRAINT [DF__rklic__provoz__49C3F6B7]  DEFAULT ('') FOR [provoz]
    ;
ALTER TABLE [dbo].[rklic] ADD  CONSTRAINT [DF__rklic__appl__4AB81AF0]  DEFAULT ('') FOR [appl]
    ;
ALTER TABLE [dbo].[rklic] ADD  CONSTRAINT [DF__rklic__dan__4BAC3F29]  DEFAULT ((0)) FOR [dan]
    ;
ALTER TABLE [dbo].[rklic] ADD  CONSTRAINT [DF__rklic__ddan__4CA06362]  DEFAULT ((0)) FOR [ddan]
    ;
ALTER TABLE [dbo].[rklic] ADD  CONSTRAINT [DF__rklic__zdan__4D94879B]  DEFAULT ((0)) FOR [zdan]
    ;
ALTER TABLE [dbo].[rklic] ADD  CONSTRAINT [DF__rklic__skuzby__4E88ABD4]  DEFAULT ((0)) FOR [skuzby]
    ;
ALTER TABLE [dbo].[rklic] ADD  CONSTRAINT [DF__rklic__casprod__4F7CD00D]  DEFAULT ((30)) FOR [casprod]
    ;
ALTER TABLE [dbo].[rklic] ADD  CONSTRAINT [DF__rklic__pobocka__5070F446]  DEFAULT ((0)) FOR [pobocka]
    ;
ALTER TABLE [dbo].[rklic] ADD  CONSTRAINT [DF__rklic__verze__5165187F]  DEFAULT ('') FOR [verze]
    ;
ALTER TABLE [dbo].[rklic] ADD  CONSTRAINT [DF__rklic__zaokrouhl__52593CB8]  DEFAULT ((0)) FOR [zaokrouhl]
    ;
ALTER TABLE [dbo].[rmenu] ADD  CONSTRAINT [DF__rmenu__nazev__5535A963]  DEFAULT ('') FOR [nazev]
    ;
ALTER TABLE [dbo].[rmenu] ADD  CONSTRAINT [DF_rmenu_druh]  DEFAULT ('') FOR [druh]
    ;
ALTER TABLE [dbo].[rmenu] ADD  CONSTRAINT [DF__rmenu__minim__5812160E]  DEFAULT ((0)) FOR [minim]
    ;
ALTER TABLE [dbo].[rmenu] ADD  CONSTRAINT [DF__rmenu__cena__59063A47]  DEFAULT ((0)) FOR [cena]
    ;
ALTER TABLE [dbo].[rmenu] ADD  CONSTRAINT [DF_rmenu_carkod]  DEFAULT ('') FOR [carkod]
    ;
ALTER TABLE [dbo].[rmenu] ADD  CONSTRAINT [DF_rmenu_rezervace]  DEFAULT ((0)) FOR [rezervace]
    ;
ALTER TABLE [dbo].[rmenu] ADD  CONSTRAINT [DF_rmenu_aktivni]  DEFAULT ((1)) FOR [aktivni]
    ;
ALTER TABLE [dbo].[rmenu] ADD  CONSTRAINT [DF_rmenu_zuziv]  DEFAULT ('') FOR [zuziv]
    ;
ALTER TABLE [dbo].[rmenu] ADD  CONSTRAINT [DF__rmenu__zcas__59FA5E80]  DEFAULT (getdate()) FOR [zcas]
    ;
ALTER TABLE [dbo].[rmenu] ADD  DEFAULT ((0)) FOR [cenacelk]
    ;
ALTER TABLE [dbo].[rmenu] ADD  DEFAULT ((0)) FOR [lcelk]
    ;
ALTER TABLE [dbo].[rmenu] ADD  DEFAULT ('') FOR [sklad]
    ;
ALTER TABLE [dbo].[rmenu] ADD  DEFAULT ((0)) FOR [lzbozi]
    ;
ALTER TABLE [dbo].[rmenu] ADD  DEFAULT ((0)) FOR [poradiTablet]
    ;
ALTER TABLE [dbo].[rmenu] ADD  DEFAULT ((0)) FOR [poradiProdej]
    ;
ALTER TABLE [dbo].[rmenu] ADD  DEFAULT ((0)) FOR [lBufet]
    ;
ALTER TABLE [dbo].[rmenu] ADD  DEFAULT ((0)) FOR [lBar]
    ;
ALTER TABLE [dbo].[rmenu] ADD  DEFAULT ((0)) FOR [lTablet]
    ;
ALTER TABLE [dbo].[rmenu] ADD  CONSTRAINT [DF__rmenu__obrazek__0F624AF8]  DEFAULT ('NENI.PNG') FOR [obrazek]
    ;
ALTER TABLE [dbo].[rmenu] ADD  DEFAULT ((0)) FOR [tabletne]
    ;
ALTER TABLE [dbo].[rmenu] ADD  DEFAULT ('') FOR [nazev2]
    ;
ALTER TABLE [dbo].[rpokl] ADD  DEFAULT ((0)) FOR [pocstav]
    ;
ALTER TABLE [dbo].[rpokl] ADD  DEFAULT ((0)) FOR [prodejho]
    ;
ALTER TABLE [dbo].[rpokl] ADD  DEFAULT ((0)) FOR [prodejnh]
    ;
ALTER TABLE [dbo].[rpokl] ADD  DEFAULT ((0)) FOR [sluzbyho]
    ;
ALTER TABLE [dbo].[rpokl] ADD  DEFAULT ((0)) FOR [sluzbynh]
    ;
ALTER TABLE [dbo].[rpokl] ADD  DEFAULT ((0)) FOR [vracenho]
    ;
ALTER TABLE [dbo].[rpokl] ADD  DEFAULT ((0)) FOR [vracennh]
    ;
ALTER TABLE [dbo].[rpokl] ADD  DEFAULT ((0)) FOR [oprijem]
    ;
ALTER TABLE [dbo].[rpokl] ADD  DEFAULT ((0)) FOR [ovydej]
    ;
ALTER TABLE [dbo].[rpokl] ADD  DEFAULT ((0)) FOR [odvod]
    ;
ALTER TABLE [dbo].[rpokl] ADD  DEFAULT ('') FOR [zuziv]
    ;
ALTER TABLE [dbo].[rpokl] ADD  DEFAULT (getdate()) FOR [zcas]
    ;
ALTER TABLE [dbo].[rpolm] ADD  CONSTRAINT [DF_rpolm_menu]  DEFAULT ('') FOR [menu]
    ;
ALTER TABLE [dbo].[rpolm] ADD  CONSTRAINT [DF_rpolm_zbozi]  DEFAULT ('') FOR [zbozi]
    ;
ALTER TABLE [dbo].[rpolm] ADD  CONSTRAINT [DF__rpolm__zuziv__2DE6D218]  DEFAULT ('') FOR [zuziv]
    ;
ALTER TABLE [dbo].[rpolm] ADD  CONSTRAINT [DF__rpolm__zcas__2EDAF651]  DEFAULT (getdate()) FOR [zcas]
    ;
ALTER TABLE [dbo].[rprij] ADD  CONSTRAINT [DF__rprij__rdodID__7F2BE32F]  DEFAULT ((0)) FOR [rdodID]
    ;
ALTER TABLE [dbo].[rprij] ADD  CONSTRAINT [DF_rprij_sklad]  DEFAULT ('01') FOR [sklad]
    ;
ALTER TABLE [dbo].[rprij] ADD  CONSTRAINT [DF__rprij__cenadod__00200768]  DEFAULT ((0)) FOR [cenadod]
    ;
ALTER TABLE [dbo].[rprij] ADD  CONSTRAINT [DF_rprij_cenacelk]  DEFAULT ((0)) FOR [cenacelk]
    ;
ALTER TABLE [dbo].[rprij] ADD  CONSTRAINT [DF_rprij_lcelk]  DEFAULT ((0)) FOR [lcelk]
    ;
ALTER TABLE [dbo].[rprij] ADD  CONSTRAINT [DF__rprij__DPHsk__01142BA1]  DEFAULT ((0)) FOR [DPHsk]
    ;
ALTER TABLE [dbo].[rprij] ADD  CONSTRAINT [DF_rprij_zaloz]  DEFAULT (getdate()) FOR [zaloz]
    ;
ALTER TABLE [dbo].[rprij] ADD  CONSTRAINT [DF_rprij_doklad]  DEFAULT ('') FOR [doklad]
    ;
ALTER TABLE [dbo].[rprij] ADD  CONSTRAINT [DF_rprij_pozn]  DEFAULT ('') FOR [pozn]
    ;
ALTER TABLE [dbo].[rprij] ADD  CONSTRAINT [DF__rprij__zuziv__02084FDA]  DEFAULT ('') FOR [zuziv]
    ;
ALTER TABLE [dbo].[rprij] ADD  CONSTRAINT [DF__rprij__zcas__02FC7413]  DEFAULT (getdate()) FOR [zcas]
    ;
ALTER TABLE [dbo].[rprodej] ADD  CONSTRAINT [DF_rprodej_rzboziID]  DEFAULT ((0)) FOR [rzboziID]
    ;
ALTER TABLE [dbo].[rprodej] ADD  CONSTRAINT [DF__rprodej__rpoklID__65F62111]  DEFAULT ((0)) FOR [rpoklID]
    ;
ALTER TABLE [dbo].[rprodej] ADD  CONSTRAINT [DF__rprodej__doklad__66EA454A]  DEFAULT ('') FOR [doklad]
    ;
ALTER TABLE [dbo].[rprodej] ADD  CONSTRAINT [DF_rprodej_sklad]  DEFAULT ('01') FOR [sklad]
    ;
ALTER TABLE [dbo].[rprodej] ADD  CONSTRAINT [DF_rprodej_dph]  DEFAULT ((0)) FOR [dph]
    ;
ALTER TABLE [dbo].[rprodej] ADD  CONSTRAINT [DF__rprodej__sluzby__67DE6983]  DEFAULT ((0)) FOR [sluzby]
    ;
ALTER TABLE [dbo].[rprodej] ADD  CONSTRAINT [DF__rprodej__lvrat__68D28DBC]  DEFAULT ((0)) FOR [lvrat]
    ;
ALTER TABLE [dbo].[rprodej] ADD  CONSTRAINT [DF__rprodej__lhoto__69C6B1F5]  DEFAULT ((0)) FOR [lhoto]
    ;
ALTER TABLE [dbo].[rprodej] ADD  CONSTRAINT [DF__rprodej__uzivpok__6ABAD62E]  DEFAULT ((0)) FOR [uzivpokl]
    ;
ALTER TABLE [dbo].[rprodej] ADD  CONSTRAINT [DF__rprodej__pokladn__6BAEFA67]  DEFAULT ('') FOR [pokladna]
    ;
ALTER TABLE [dbo].[rprodej] ADD  CONSTRAINT [DF__rprodej__zuziv__6CA31EA0]  DEFAULT ('') FOR [zuziv]
    ;
ALTER TABLE [dbo].[rprodej] ADD  CONSTRAINT [DF__rprodej__zcas__6D9742D9]  DEFAULT (getdate()) FOR [zcas]
    ;
ALTER TABLE [dbo].[rprodej] ADD  DEFAULT ('') FOR [karta]
    ;
ALTER TABLE [dbo].[rprodej] ADD  DEFAULT ((0)) FOR [rzbozirID]
    ;
ALTER TABLE [dbo].[rprodej] ADD  DEFAULT ((0)) FOR [pcID]
    ;
ALTER TABLE [dbo].[rprodej] ADD  DEFAULT ('') FOR [voucher]
    ;
ALTER TABLE [dbo].[rprodej] ADD  DEFAULT ((0)) FOR [ltablet]
    ;
ALTER TABLE [dbo].[rprodejRus] ADD  DEFAULT ((0)) FOR [rprodejID]
    ;
ALTER TABLE [dbo].[rprodejRus] ADD  DEFAULT ((0)) FOR [rzboziID]
    ;
ALTER TABLE [dbo].[rprodejRus] ADD  DEFAULT ((0)) FOR [rpoklID]
    ;
ALTER TABLE [dbo].[rprodejRus] ADD  DEFAULT ('') FOR [doklad]
    ;
ALTER TABLE [dbo].[rprodejRus] ADD  DEFAULT ('01') FOR [sklad]
    ;
ALTER TABLE [dbo].[rprodejRus] ADD  DEFAULT ((0)) FOR [dph]
    ;
ALTER TABLE [dbo].[rprodejRus] ADD  DEFAULT ((0)) FOR [sluzby]
    ;
ALTER TABLE [dbo].[rprodejRus] ADD  DEFAULT ((0)) FOR [lvrat]
    ;
ALTER TABLE [dbo].[rprodejRus] ADD  DEFAULT ((0)) FOR [lhoto]
    ;
ALTER TABLE [dbo].[rprodejRus] ADD  DEFAULT ((0)) FOR [uzivpokl]
    ;
ALTER TABLE [dbo].[rprodejRus] ADD  DEFAULT ('') FOR [pokladna]
    ;
ALTER TABLE [dbo].[rprodejRus] ADD  DEFAULT ('') FOR [zuziv]
    ;
ALTER TABLE [dbo].[rprodejRus] ADD  DEFAULT (getdate()) FOR [zcas]
    ;
ALTER TABLE [dbo].[rslev] ADD  DEFAULT ((0)) FOR [sleva]
    ;
ALTER TABLE [dbo].[rslev] ADD  DEFAULT ((0)) FOR [lproc]
    ;
ALTER TABLE [dbo].[rslev] ADD  DEFAULT ('') FOR [prijmeni]
    ;
ALTER TABLE [dbo].[rslev] ADD  DEFAULT ('') FOR [jmeno]
    ;
ALTER TABLE [dbo].[rslev] ADD  DEFAULT ('') FOR [adrmis]
    ;
ALTER TABLE [dbo].[rslev] ADD  DEFAULT ('') FOR [adrul]
    ;
ALTER TABLE [dbo].[rslev] ADD  DEFAULT ('') FOR [psc]
    ;
ALTER TABLE [dbo].[rslev] ADD  DEFAULT ('') FOR [telef]
    ;
ALTER TABLE [dbo].[rslev] ADD  DEFAULT ('') FOR [zuziv]
    ;
ALTER TABLE [dbo].[rslev] ADD  DEFAULT (getdate()) FOR [zcas]
    ;
ALTER TABLE [dbo].[ruziv] ADD  CONSTRAINT [DF__ruziv__Jmeno__37A5467C]  DEFAULT ('') FOR [Jmeno]
    ;
ALTER TABLE [dbo].[ruziv] ADD  CONSTRAINT [DF__ruziv__zapis__38996AB5]  DEFAULT ((0)) FOR [zapis]
    ;
ALTER TABLE [dbo].[ruziv] ADD  CONSTRAINT [DF__ruziv__pokladna__398D8EEE]  DEFAULT ('') FOR [pokladna]
    ;
ALTER TABLE [dbo].[ruziv] ADD  CONSTRAINT [DF_ruziv_sklad]  DEFAULT ('') FOR [sklad]
    ;
ALTER TABLE [dbo].[ruziv] ADD  CONSTRAINT [DF__ruziv__datOd__3A81B327]  DEFAULT (getdate()) FOR [datOd]
    ;
ALTER TABLE [dbo].[ruziv] ADD  CONSTRAINT [DF__ruziv__pokD__3B75D760]  DEFAULT ((0)) FOR [pokD]
    ;
ALTER TABLE [dbo].[ruziv] ADD  CONSTRAINT [DF__ruziv__pokZ__3C69FB99]  DEFAULT ((0)) FOR [pokZ]
    ;
ALTER TABLE [dbo].[ruziv] ADD  CONSTRAINT [DF__ruziv__prace__3D5E1FD2]  DEFAULT ('') FOR [prace]
    ;
ALTER TABLE [dbo].[ruziv] ADD  CONSTRAINT [DF__ruziv__vstup__3E52440B]  DEFAULT ((0)) FOR [vstup]
    ;
ALTER TABLE [dbo].[ruziv] ADD  CONSTRAINT [DF__ruziv__old__3F466844]  DEFAULT ((0)) FOR [old]
    ;
ALTER TABLE [dbo].[ruziv] ADD  CONSTRAINT [DF__ruziv__zuziv__403A8C7D]  DEFAULT ('') FOR [zuziv]
    ;
ALTER TABLE [dbo].[rvydej] ADD  CONSTRAINT [DF_rvydej_rmenuID]  DEFAULT ((0)) FOR [rmenuID]
    ;
ALTER TABLE [dbo].[rvydej] ADD  CONSTRAINT [DF_rvydej_rprodejID]  DEFAULT ((0)) FOR [rprodejID]
    ;
ALTER TABLE [dbo].[rvydej] ADD  CONSTRAINT [DF_rvydej_rprijID]  DEFAULT ((0)) FOR [rprijID]
    ;
ALTER TABLE [dbo].[rvydej] ADD  CONSTRAINT [DF_rvydej_zeskladu]  DEFAULT ('01') FOR [zeskladu]
    ;
ALTER TABLE [dbo].[rvydej] ADD  CONSTRAINT [DF_rvydej_doklad]  DEFAULT ('') FOR [nastr]
    ;
ALTER TABLE [dbo].[rvydej] ADD  CONSTRAINT [DF__rvydej__cena__05D8E0BE]  DEFAULT ((0)) FOR [cena]
    ;
ALTER TABLE [dbo].[rvydej] ADD  CONSTRAINT [DF__rvydej__DPHsk__06CD04F7]  DEFAULT ((0)) FOR [DPHsk]
    ;
ALTER TABLE [dbo].[rvydej] ADD  CONSTRAINT [DF_rvydej_zalozeno_1]  DEFAULT (getdate()) FOR [zaloz]
    ;
ALTER TABLE [dbo].[rvydej] ADD  CONSTRAINT [DF_rvydej_pozn]  DEFAULT ('') FOR [pozn]
    ;
ALTER TABLE [dbo].[rvydej] ADD  CONSTRAINT [DF__rvydej__zuziv__07C12930]  DEFAULT ('') FOR [zuziv]
    ;
ALTER TABLE [dbo].[rvydej] ADD  CONSTRAINT [DF__rvydej__zcas__08B54D69]  DEFAULT (getdate()) FOR [zcas]
    ;
ALTER TABLE [dbo].[rvydej] ADD  DEFAULT ((0)) FOR [lvse]
    ;
ALTER TABLE [dbo].[rzbozi] ADD  CONSTRAINT [DF__rzbozi__nazev__6E01572D]  DEFAULT ('') FOR [nazev]
    ;
ALTER TABLE [dbo].[rzbozi] ADD  CONSTRAINT [DF__rzbozi__mj__70DDC3D8]  DEFAULT ('Ks') FOR [mj]
    ;
ALTER TABLE [dbo].[rzbozi] ADD  CONSTRAINT [DF__rzbozi__minim__71D1E811]  DEFAULT ((0)) FOR [minim]
    ;
ALTER TABLE [dbo].[rzbozi] ADD  CONSTRAINT [DF__rzbozi__cena__72C60C4A]  DEFAULT ((0)) FOR [cena]
    ;
ALTER TABLE [dbo].[rzbozi] ADD  CONSTRAINT [DF_rzbozi_carkod]  DEFAULT ('') FOR [carkod]
    ;
ALTER TABLE [dbo].[rzbozi] ADD  CONSTRAINT [DF_rzbozi_aktivni]  DEFAULT ((1)) FOR [aktivni]
    ;
ALTER TABLE [dbo].[rzbozi] ADD  CONSTRAINT [DF_rzbozi_zuziv]  DEFAULT ('') FOR [zuziv]
    ;
ALTER TABLE [dbo].[rzbozi] ADD  CONSTRAINT [DF__rzbozi__zcas__73BA3083]  DEFAULT (getdate()) FOR [zcas]
    ;
ALTER TABLE [dbo].[rzbozi] ADD  DEFAULT ((0)) FOR [cenap]
    ;
ALTER TABLE [dbo].[rzbozir] ADD  DEFAULT ((0)) FOR [lproc]
    ;
ALTER TABLE [dbo].[rzbozir] ADD  DEFAULT ((0)) FOR [lBufet]
    ;
ALTER TABLE [dbo].[rzbozir] ADD  DEFAULT ((0)) FOR [lBar]
    ;
ALTER TABLE [dbo].[rzbozir] ADD  DEFAULT ((0)) FOR [lTablet]
    ;
ALTER TABLE [dbo].[rzboziskl] ADD  CONSTRAINT [DF__rzboziskl__cenas__251C81ED]  DEFAULT ((0)) FOR [cenaskl]
    ;
ALTER TABLE [dbo].[rzboziskl] ADD  CONSTRAINT [DF__rzboziskl__mnozs__2610A626]  DEFAULT ((0)) FOR [mnozskl]
    ;
ALTER TABLE [dbo].[rzboziskl] ADD  CONSTRAINT [DF__rzboziskl__minim__2704CA5F]  DEFAULT ((0)) FOR [minim]
    ;
ALTER TABLE [dbo].[rzboziskl] ADD  CONSTRAINT [DF__rzboziskl__zuziv__27F8EE98]  DEFAULT ('') FOR [zuziv]
    ;
ALTER TABLE [dbo].[rzboziskl] ADD  CONSTRAINT [DF__rzboziskl__zcas__28ED12D1]  DEFAULT (getdate()) FOR [zcas]
    ;
ALTER TABLE [dbo].[stavy] ADD  CONSTRAINT [DF__stavy__satvmj__0EC32C7A]  DEFAULT ((0)) FOR [stavmj]
    ;
ALTER TABLE [dbo].[stavy] ADD  CONSTRAINT [DF_stavy_stavmj2]  DEFAULT ((0)) FOR [stavmj2]
    ;
ALTER TABLE [dbo].[stavy] ADD  CONSTRAINT [DF__stavy__cenacelk__0FB750B3]  DEFAULT ((0)) FOR [cenacelk]
    ;
ALTER TABLE [dbo].[stavy] ADD  CONSTRAINT [DF__stavy__stavold__10AB74EC]  DEFAULT ((0)) FOR [stavold]
    ;
ALTER TABLE [dbo].[stavy] ADD  CONSTRAINT [DF__stavy__cenaold__119F9925]  DEFAULT ((0)) FOR [cenaold]
    ;
ALTER TABLE [dbo].[stavy] ADD  CONSTRAINT [DF__stavy__zuziv__1293BD5E]  DEFAULT ('') FOR [zuziv]
    ;
ALTER TABLE [dbo].[stoly] ADD  DEFAULT ('') FOR [zuziv]
    ;
ALTER TABLE [dbo].[stoly] ADD  DEFAULT ('') FOR [karta]
    ;
ALTER TABLE [dbo].[stoly] ADD  DEFAULT ((0)) FOR [lhoto]
    ;
ALTER TABLE [dbo].[stoly] ADD  DEFAULT ((0)) FOR [oktran]
    ;
ALTER TABLE [dbo].[stoly] ADD  DEFAULT ((0)) FOR [lvydej]
    ;
ALTER TABLE [dbo].[stoly] ADD  DEFAULT ((0)) FOR [taletProdej]
    ;
ALTER TABLE [dbo].[tisks] ADD  DEFAULT ((0)) FOR [pc]
    ;
ALTER TABLE [dbo].[tisks] ADD  DEFAULT ('') FOR [soubor]
    ;
ALTER TABLE [dbo].[tisks] ADD  DEFAULT ('') FOR [dalsip]
    ;
ALTER TABLE [dbo].[tisks] ADD  DEFAULT ('') FOR [zuziv]
    ;
ALTER TABLE [dbo].[typvst] ADD  DEFAULT ('') FOR [uvodni]
    ;
ALTER TABLE [dbo].[typvst] ADD  DEFAULT ('') FOR [koncove]
    ;
ALTER TABLE [dbo].[typvst] ADD  DEFAULT ((0)) FOR [lhromad]
    ;
ALTER TABLE [dbo].[typvst] ADD  DEFAULT ((0)) FOR [lskolni]
    ;
ALTER TABLE [dbo].[typvst] ADD  DEFAULT ('') FOR [zuziv]
    ;
ALTER TABLE [dbo].[typvst] ADD  DEFAULT (getdate()) FOR [zcas]
    ;
ALTER TABLE [dbo].[voucher] ADD  DEFAULT ((1)) FOR [maxpo]
    ;
ALTER TABLE [dbo].[voucher] ADD  DEFAULT ((0)) FOR [pocpo]
    ;
ALTER TABLE [dbo].[voucher] ADD  DEFAULT ('') FOR [zuziv]
    ;
ALTER TABLE [dbo].[voucher] ADD  DEFAULT (getdate()) FOR [zcas]
    ;
ALTER TABLE [dbo].[voucher] ADD  DEFAULT ('') FOR [sezmenu]
    ;
ALTER TABLE [dbo].[Combo$Has Acl Branch]  WITH CHECK ADD  CONSTRAINT [FK_Combo$Has Acl Branch_Combo] FOREIGN KEY([idCombo])
    REFERENCES [dbo].[Combo] ([id])
    ON DELETE CASCADE
           ;
ALTER TABLE [dbo].[Combo$Has Acl Branch] CHECK CONSTRAINT [FK_Combo$Has Acl Branch_Combo]
    ;
ALTER TABLE [dbo].[Combo$Has Menu]  WITH CHECK ADD  CONSTRAINT [FK_Combo$Has Menu_Combo] FOREIGN KEY([idCombo])
    REFERENCES [dbo].[Combo] ([id])
    ON DELETE CASCADE
           ;
ALTER TABLE [dbo].[Combo$Has Menu] CHECK CONSTRAINT [FK_Combo$Has Menu_Combo]
    ;
ALTER TABLE [dbo].[Combo$Has Menu]  WITH CHECK ADD  CONSTRAINT [FK_Combo$Has Menu_rmenu] FOREIGN KEY([idMenu])
    REFERENCES [dbo].[rmenu] ([rmenuID])
    ON DELETE CASCADE
           ;
ALTER TABLE [dbo].[Combo$Has Menu] CHECK CONSTRAINT [FK_Combo$Has Menu_rmenu]
    ;
ALTER TABLE [dbo].[Combo$Has Step]  WITH CHECK ADD  CONSTRAINT [FK_Combo$Has Step_Combo] FOREIGN KEY([idCombo])
    REFERENCES [dbo].[Combo] ([id])
    ON DELETE CASCADE
           ;
ALTER TABLE [dbo].[Combo$Has Step] CHECK CONSTRAINT [FK_Combo$Has Step_Combo]
    ;
ALTER TABLE [dbo].[Combo$Has Step]  WITH CHECK ADD  CONSTRAINT [FK_Combo$Has Step_Combo$Step] FOREIGN KEY([idComboStep])
    REFERENCES [dbo].[Combo$Step] ([id])
    ON DELETE CASCADE
           ;
ALTER TABLE [dbo].[Combo$Has Step] CHECK CONSTRAINT [FK_Combo$Has Step_Combo$Step]
    ;
ALTER TABLE [dbo].[Combo$Step Item]  WITH CHECK ADD  CONSTRAINT [FK_Combo$Step Item_Combo$Step] FOREIGN KEY([idComboStep])
    REFERENCES [dbo].[Combo$Step] ([id])
    ON DELETE CASCADE
           ;
ALTER TABLE [dbo].[Combo$Step Item] CHECK CONSTRAINT [FK_Combo$Step Item_Combo$Step]
    ;
ALTER TABLE [dbo].[Combo$Step Item]  WITH CHECK ADD  CONSTRAINT [FK_Combo$Step Item_rmenu] FOREIGN KEY([idMenu])
    REFERENCES [dbo].[rmenu] ([rmenuID])
    ON DELETE CASCADE
           ;
ALTER TABLE [dbo].[Combo$Step Item] CHECK CONSTRAINT [FK_Combo$Step Item_rmenu]
    ;
ALTER TABLE [dbo].[Menu$Has Acl Branch]  WITH CHECK ADD  CONSTRAINT [FK_Menu$Has Acl Branch_rmenu] FOREIGN KEY([idMenu])
    REFERENCES [dbo].[rmenu] ([rmenuID])
    ON DELETE CASCADE
           ;
ALTER TABLE [dbo].[Menu$Has Acl Branch] CHECK CONSTRAINT [FK_Menu$Has Acl Branch_rmenu]
    ;
ALTER TABLE [dbo].[NonStockProduct$Has Acl Branch]  WITH CHECK ADD  CONSTRAINT [FK_NonStockProduct$Has Acl Branch_rzbozir] FOREIGN KEY([idNonStockProduct])
    REFERENCES [dbo].[rzbozir] ([rzbozirID])
    ON DELETE CASCADE
           ;
ALTER TABLE [dbo].[NonStockProduct$Has Acl Branch] CHECK CONSTRAINT [FK_NonStockProduct$Has Acl Branch_rzbozir]
    ;
ALTER TABLE [dbo].[Product$Has Acl Branch]  WITH CHECK ADD  CONSTRAINT [FK_Product$Has Acl Branch_rzbozi] FOREIGN KEY([idProduct])
    REFERENCES [dbo].[rzbozi] ([rzboziID])
    ;
ALTER TABLE [dbo].[Product$Has Acl Branch] CHECK CONSTRAINT [FK_Product$Has Acl Branch_rzbozi]
    ;

IF NOT EXISTS (
    SELECT * FROM sys.objects WHERE NAME='cisDokladu' and objectproperty(object_id,'IsProcedure')=1
) EXEC (
    'CREATE  PROC [cisDokladu]
      @pohyb char(2), @delka Int=6
    AS
    BEGIN
      declare @doklad char(12)
    Update rcrad
    set doklad=rcrad.doklad+1,
        @doklad=RTRIM(rcrad.pred)+REPLACE(STR(rcrad.doklad+1,@delka),'' '',''0'')+RTRIM(rcrad.za)
        FROM rcrad join rcpoh ON rcrad.rcradID=rcpoh.rcradID
    WHERE rcpoh.pohyb=@pohyb
    SELECT @doklad as doklad
    END'
)
GO
