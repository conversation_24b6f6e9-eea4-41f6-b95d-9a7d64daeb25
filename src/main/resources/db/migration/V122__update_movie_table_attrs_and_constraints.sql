ALTER TABLE movie ALTER COLUMN original_id DROP NOT NULL;
ALTER TABLE movie ALTER COLUMN parsed_format DROP NOT NULL;

DELETE FROM ticket WHERE ticket.id IN (
    SELECT ticket.id FROM ticket
        JOIN screening ON ticket.screening_id = screening.id
        JOIN movie ON screening.movie_id = movie.id AND movie_id IN (
            SELECT id FROM movie WHERE code IN (
                SELECT code FROM movie GROUP BY code HAVING count(code) > 1 AND code IS NOT NULL
            )
        )
    );

DELETE FROM reservation WHERE reservation.id IN (
    SELECT reservation.id FROM reservation
        JOIN screening ON reservation.screening_id = screening.id
        JOIN movie ON screening.movie_id = movie.id AND movie_id IN (
            SELECT id FROM movie WHERE code IN (
                SELECT code FROM movie GROUP BY code HAVING count(code) > 1 AND code IS NOT NULL
            )
        )
    );

DELETE FROM screening WHERE screening.id IN (
    SELECT screening.id FROM screening
        JOIN movie ON screening.movie_id = movie.id AND movie_id IN (
            SELECT id FROM movie WHERE code IN (
                SELECT code FROM movie GROUP BY code HAVING count(code) > 1 AND code IS NOT NULL
            )
        )
    );

DELETE FROM movie WHERE code IN (
    SELECT code FROM movie GROUP BY code HAVING count(code) > 1 AND code IS NOT NULL
);

DELETE FROM movie WHERE disfilm_code IN (
    SELECT disfilm_code FROM movie GROUP BY disfilm_code HAVING count(disfilm_code) > 1 AND disfilm_code IS NOT NULL
);

CREATE UNIQUE INDEX "6aeb71092e884abd9590_ui" ON movie (code) WHERE deleted_at IS NULL;
CREATE UNIQUE INDEX "ce2f56b92134458cbd23_ui" ON movie (disfilm_code) WHERE deleted_at IS NULL AND disfilm_code IS NOT NULL;

UPDATE synchronization_from_mssql SET last_synchronization = 'epoch' WHERE type = 'MOVIE';

CREATE SEQUENCE movie_code_seq AS BIGINT INCREMENT BY 1 START WITH 1;
