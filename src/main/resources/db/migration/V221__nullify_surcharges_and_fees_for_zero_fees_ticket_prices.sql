UPDATE ticket_price SET
    seat_surcharge = CASE
        WHEN zero_fees = TRUE AND seat_surcharge_type IS NOT NULL THEN 0.0
        ELSE seat_surcharge END,
    auditorium_surcharge = CASE
        WHEN zero_fees = TRUE AND auditorium_surcharge_type IS NOT NULL THEN 0.0
        ELSE auditorium_surcharge END,
    seat_service_fee = CASE
        WHEN zero_fees = TRUE AND seat_service_fee_type IS NOT NULL THEN 0.0
        ELSE seat_service_fee END,
    auditorium_service_fee = CASE
        WHEN zero_fees = TRUE AND auditorium_service_fee_type IS NOT NULL THEN 0.0
        ELSE auditorium_service_fee END,
    service_fee_general = CASE
        WHEN zero_fees = TRUE THEN 0.0
        ELSE service_fee_general END;
