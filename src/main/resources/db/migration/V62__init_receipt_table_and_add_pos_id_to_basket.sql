CREATE TABLE receipt
(
   id         UUID PRIMARY KEY,
   basket_id  UUID NOT NULL,
   directory  TEXT NOT NULL,
   content    TEXT NOT NULL,
   created_at TIMESTAMP NOT NULL
);

CREATE INDEX "94a003b91a314b5eaf61_ix" ON receipt (basket_id);

ALTER TABLE basket ADD COLUMN payment_pos_configuration_id UUID;
ALTER TABLE basket ADD CONSTRAINT "fba8277d21a54164a4f2_fk"
    FOREIGN KEY (payment_pos_configuration_id) REFERENCES pos_configuration(id);
