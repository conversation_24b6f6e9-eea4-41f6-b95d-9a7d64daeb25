UPDATE screening_fee SET surcharge_vip = 0.00 WHERE surcharge_vip IS NULL;
UPDATE screening_fee SET surcharge_premium = 0.00 WHERE surcharge_premium IS NULL;
UPDATE screening_fee SET surcharge_imax = 0.00 WHERE surcharge_imax IS NULL;
UPDATE screening_fee SET surcharge_ultra_x = 0.00 WHERE surcharge_ultra_x IS NULL;
UPDATE screening_fee SET service_fee_vip = 0.00 WHERE service_fee_vip IS NULL;
UPDATE screening_fee SET service_fee_premium = 0.00 WHERE service_fee_premium IS NULL;
UPDATE screening_fee SET service_fee_imax = 0.00 WHERE service_fee_imax IS NULL;
UPDATE screening_fee SET service_fee_ultra_x = 0.00 WHERE service_fee_ultra_x IS NULL;
UPDATE screening_fee SET surcharge_d_box = 0.00 WHERE surcharge_d_box IS NULL;
UPDATE screening_fee SET service_fee_general = 0.00 WHERE service_fee_general IS NULL;

ALTER TABLE screening_fee ALTER COLUMN surcharge_vip SET NOT NULL;
ALTER TABLE screening_fee ALTER COLUMN surcharge_premium SET NOT NULL;
ALTER TABLE screening_fee ALTER COLUMN surcharge_imax SET NOT NULL;
ALTER TABLE screening_fee ALTER COLUMN surcharge_ultra_x SET NOT NULL;
ALTER TABLE screening_fee ALTER COLUMN service_fee_vip SET NOT NULL;
ALTER TABLE screening_fee ALTER COLUMN service_fee_premium SET NOT NULL;
ALTER TABLE screening_fee ALTER COLUMN service_fee_imax SET NOT NULL;
ALTER TABLE screening_fee ALTER COLUMN service_fee_ultra_x SET NOT NULL;
ALTER TABLE screening_fee ALTER COLUMN surcharge_d_box SET NOT NULL;
ALTER TABLE screening_fee ALTER COLUMN service_fee_general SET NOT NULL;

UPDATE auditorium_default SET surcharge_vip = 0.00 WHERE surcharge_vip IS NULL;
UPDATE auditorium_default SET surcharge_premium = 0.00 WHERE surcharge_premium IS NULL;
UPDATE auditorium_default SET surcharge_imax = 0.00 WHERE surcharge_imax IS NULL;
UPDATE auditorium_default SET surcharge_ultra_x = 0.00 WHERE surcharge_ultra_x IS NULL;
UPDATE auditorium_default SET service_fee_vip = 0.00 WHERE service_fee_vip IS NULL;
UPDATE auditorium_default SET service_fee_premium = 0.00 WHERE service_fee_premium IS NULL;
UPDATE auditorium_default SET service_fee_imax = 0.00 WHERE service_fee_imax IS NULL;
UPDATE auditorium_default SET service_fee_ultra_x = 0.00 WHERE service_fee_ultra_x IS NULL;
UPDATE auditorium_default SET surcharge_d_box = 0.00 WHERE surcharge_d_box IS NULL;
UPDATE auditorium_default SET pro_commission = 0.00 WHERE pro_commission IS NULL;
UPDATE auditorium_default SET film_fond_commission = 0.00 WHERE film_fond_commission IS NULL;
UPDATE auditorium_default SET distributor_commission = 0.00 WHERE distributor_commission IS NULL;

ALTER TABLE auditorium_default ALTER COLUMN surcharge_vip SET NOT NULL;
ALTER TABLE auditorium_default ALTER COLUMN surcharge_premium SET NOT NULL;
ALTER TABLE auditorium_default ALTER COLUMN surcharge_imax SET NOT NULL;
ALTER TABLE auditorium_default ALTER COLUMN surcharge_ultra_x SET NOT NULL;
ALTER TABLE auditorium_default ALTER COLUMN service_fee_vip SET NOT NULL;
ALTER TABLE auditorium_default ALTER COLUMN service_fee_premium SET NOT NULL;
ALTER TABLE auditorium_default ALTER COLUMN service_fee_imax SET NOT NULL;
ALTER TABLE auditorium_default ALTER COLUMN service_fee_ultra_x SET NOT NULL;
ALTER TABLE auditorium_default ALTER COLUMN surcharge_d_box SET NOT NULL;
ALTER TABLE auditorium_default ALTER COLUMN pro_commission SET NOT NULL;
ALTER TABLE auditorium_default ALTER COLUMN film_fond_commission SET NOT NULL;
ALTER TABLE auditorium_default ALTER COLUMN distributor_commission SET NOT NULL;

UPDATE synchronization_from_mssql SET last_synchronization = 'epoch' WHERE type = 'SCREENING_FEE';
UPDATE synchronization_from_mssql SET last_synchronization = 'epoch' WHERE type = 'AUDITORIUM_DEFAULT';
