ALTER TABLE product ADD COLUMN deleted_at TIMESTAMP;

DROP INDEX "7ac4d56800d648ff9a97_ui"; -- unique index nullable original_id
DROP INDEX "8b85e970a3d44a22b6f4_ui"; -- unique index original_code
DROP INDEX "c7fd61555f7e49e4a97b_ix"; -- index original_code

CREATE UNIQUE INDEX "e952cf50d5244e078a48_ui" ON product(original_id, deleted_at) ;
CREATE UNIQUE INDEX "54c3420a84ea4fc3a5c1_ui" ON product(original_code, deleted_at);
CREATE INDEX "f8d3c52dbc0143d2bfd3_ix" ON product(original_code, deleted_at);
CREATE INDEX "16fe369cc8324ba88a8d_ix" ON product (id, deleted_at);