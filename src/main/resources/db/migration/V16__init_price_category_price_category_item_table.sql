CREATE TABLE price_category
(
   id             UUID PRIMARY KEY,
   original_id    INT NOT NULL,
   distributor_id UUID,
   title          TEXT,
   active         BOOLEAN NOT NULL,
   created_at     TIMESTAMP NOT NULL,
   updated_at     TIMESTAMP NOT NULL
);

CREATE UNIQUE INDEX "d39ed12de3af4db1801d_ui" ON price_category (original_id);
CREATE INDEX "e620351ded894c6fa9ae_ix" ON price_category (id, active);

CREATE TABLE price_category_item
(
   id                UUID PRIMARY KEY,
   price_category_id UUID NOT NULL,
   number            TEXT NOT NULL,
   title             TEXT,
   price             NUMERIC NOT NULL,
   created_at        TIMESTAMP NOT NULL,
   updated_at        TIMESTAMP NOT NULL
);
ALTER TABLE price_category_item ADD CONSTRAINT "78fff8a6cb564fbc84a3_fk" FOREIGN KEY (price_category_id) REFERENCES price_category(id);
CREATE UNIQUE INDEX "7c2a12765b8d4b05843a_ui" ON price_category_item (price_category_id, number);
