ALTER TABLE product_composition
    ADD COLUMN product_in_product_id UUID,
    ADD CONSTRAINT "3fec0016ca32487a95b2_fk" FOREIGN KEY (product_in_product_id) REFERENCES product(id),
    ALTER COLUMN original_id DROP NOT NULL,
    ALTER COLUMN product_component_id DROP NOT NULL;

CREATE INDEX "136f37ceafd7485d9b78_ix" ON product_composition (product_in_product_id)
    WHERE product_composition.product_in_product_id IS NOT NULL;

DROP INDEX "a5aba40f561345f083e7_ui"; --product_composition (original_id)
CREATE UNIQUE INDEX "3d9428a714d144e28fb1_ui" ON product_composition (original_id) WHERE original_id IS NOT NULL;

UPDATE synchronization_from_mssql SET last_synchronization = 'epoch' WHERE type = 'PRODUCT_COMPOSITION';
