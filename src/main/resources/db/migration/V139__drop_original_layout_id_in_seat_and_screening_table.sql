ALTER TABLE seat
    ADD COLUMN auditorium_layout_id UUID,
    ADD CONSTRAINT "344461d9d4aa4ccb9502_fk" FOREIGN KEY (auditorium_layout_id) REFERENCES "auditorium_layout" (id);

UPDATE seat
SET auditorium_layout_id = al.id
FROM auditorium_layout al
WHERE original_layout_id = al.original_id AND auditorium_layout_id IS NULL;

ALTER TABLE seat
    ALTER COLUMN auditorium_layout_id SET NOT NULL;

CREATE INDEX "1aea58c7417547d3a28c_ix" ON "seat" (auditorium_layout_id);
DROP INDEX "9401e8c7fdd6438ea384_ix";
CREATE INDEX "03905d9ea6d547199d95_ix" ON "screening" (auditorium_layout_id);

ALTER TABLE seat DROP COLUMN original_layout_id;
ALTER TABLE screening DROP COLUMN original_layout_id;
