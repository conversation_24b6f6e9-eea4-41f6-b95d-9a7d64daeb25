ALTER TABLE price_category
    ADD COLUMN original_code TEXT NOT NULL DEFAULT 'TEMP',
    DROP COLUMN distributor_id;

ALTER TABLE price_category
    ALTER COLUMN original_code DROP DEFAULT;

ALTER TABLE price_category_item
    ADD COLUMN discounted BOOLEAN NOT NULL DEFAULT FALSE;

ALTER TABLE price_category_item
    ALTER COLUMN discounted DROP DEFAULT;

-- forcing price categories sync from mssql
DELETE FROM synchronization_from_mssql WHERE type = 'PRICE_CATEGORY';
