ALTER TABLE discount_card DROP COLUMN usage_count;
ALTER TABLE discount_card DROP COLUMN usage_limit;
ALTER TABLE discount_card ADD COLUMN product_id UUID;
ALTER TABLE discount_card ADD COLUMN products_count INT;

ALTER TABLE discount_card
    ADD CONSTRAINT "4fab4a792ebc41c8b6ab_fk" FOREIGN KEY (product_id)
    REFERENCES product(id);

DELETE FROM synchronization_from_mssql WHERE type = 'DISCOUNT_VOUCHER';
DELETE FROM synchronization_from_mssql WHERE type = 'DISCOUNT_CARD';
