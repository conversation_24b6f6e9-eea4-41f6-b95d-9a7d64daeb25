ALTER TABLE basket_item ADD COLUMN vat_amount NUMERIC;

-- tickets before 2025-01-01
UPDATE basket_item bi
    SET vat_amount = round(price - (price / 1.20), 2)
    FROM basket b
    WHERE b.id = bi.basket_id
        AND bi.type = 'TICKET'
        AND b.paid_at::date < '2025-01-01';

-- tickets after 2025-01-01
UPDATE basket_item bi
    SET vat_amount = round(price - (price / 1.23), 2)
    FROM basket b
    WHERE b.id = bi.basket_id
        AND bi.type = 'TICKET'
        AND b.paid_at::date >= '2025-01-01';

-- products and product discounts before 2025-01-01
UPDATE basket_item bi
    SET vat_amount = round(price - (price / 1.20), 2)
    FROM basket b
    WHERE b.id = bi.basket_id
        AND bi.type IN ('PRODUCT', 'PRODUCT_DISCOUNT')
        AND b.paid_at::date < '2025-01-01';

-- products and product discounts after 2025-01-01
UPDATE basket_item bi
    SET vat_amount = round(bi.price - (bi.price / ((pc.tax_rate::NUMERIC / 100) + 1)), 2)
    FROM basket b, product p, product_category pc
    WHERE b.id = bi.basket_id
        AND p.id = bi.product_id
        AND pc.id = p.product_category_id
        AND bi.type IN ('PRODUCT', 'PRODUCT_DISCOUNT')
        AND b.paid_at::date >= '2025-01-01';

-- there are still some items in baskets which are not paid
-- tickets before 2025-01-01
UPDATE basket_item bi
    SET vat_amount = round(price - (price / 1.20), 2)
    FROM basket b
    WHERE b.id = bi.basket_id
        AND bi.type = 'TICKET'
        AND bi.created_at::date < '2025-01-01'
        AND b.paid_at IS NULL;

-- tickets after 2025-01-01
UPDATE basket_item bi
    SET vat_amount = round(price - (price / 1.23), 2)
    FROM basket b
    WHERE b.id = bi.basket_id
        AND bi.type = 'TICKET'
        AND bi.created_at::date >= '2025-01-01'
        AND b.paid_at IS NULL;

-- products and product discounts before 2025-01-01
UPDATE basket_item bi
    SET vat_amount = round(price - (price / 1.20), 2)
    FROM basket b
    WHERE b.id = bi.basket_id
        AND bi.type IN ('PRODUCT', 'PRODUCT_DISCOUNT')
        AND bi.created_at::date < '2025-01-01'
        AND b.paid_at IS NULL;

-- products and product discounts after 2025-01-01
UPDATE basket_item bi
    SET vat_amount = round(bi.price - (bi.price / ((pc.tax_rate::NUMERIC / 100) + 1)), 2)
    FROM basket b, product p, product_category pc
    WHERE b.id = bi.basket_id
        AND p.id = bi.product_id
        AND pc.id = p.product_category_id
        AND bi.type IN ('PRODUCT', 'PRODUCT_DISCOUNT')
        AND bi.created_at::date >= '2025-01-01'
        AND b.paid_at IS NULL;

-- product in product(s)
WITH product_in_product_vat_amount AS (
    SELECT
        pco.product_id AS product_id,
        round(
            sum(
                pco.product_in_product_price -
                (pco.product_in_product_price * pco.amount / ((pc.tax_rate::NUMERIC / 100) + 1))
            ),
            2
        ) AS vat_amount,
        round(
            sum(
                pco.product_in_product_flagship_price -
                (pco.product_in_product_flagship_price * pco.amount / ((pc.tax_rate::NUMERIC / 100) + 1))
            ),
            2
        ) AS flagship_vat_amount
    FROM product_composition pco
        JOIN product p ON p.id = pco.product_id
        JOIN product pip ON pip.id = pco.product_in_product_id
        JOIN product_category pc ON pc.id = pip.product_category_id
    WHERE
        p.type = 'PRODUCT_IN_PRODUCT'
    GROUP BY
        pco.product_id
)
UPDATE basket_item bi
    SET vat_amount = CASE
        WHEN b.type = 'FLAGSHIP' THEN pipva.flagship_vat_amount
        ELSE pipva.vat_amount
    END
    FROM product_in_product_vat_amount AS pipva,
        branch b
    WHERE pipva.product_id = bi.product_id
        AND branch_id = b.id;

ALTER TABLE basket_item ALTER COLUMN vat_amount SET NOT NULL;
