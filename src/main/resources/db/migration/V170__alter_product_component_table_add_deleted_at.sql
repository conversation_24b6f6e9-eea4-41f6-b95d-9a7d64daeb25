ALTER TABLE product_component ADD COLUMN deleted_at TIMESTAMP;

DROP INDEX "a5885182d653435c962b_ix"; -- dropping index on product_component (product_component_category_id)
DROP INDEX "39eecc33ea1d44b28019_ui"; -- dropping index on product_component (original_id)
DROP INDEX "f5d4b56dbb31424fa2f8_ui"; -- dropping index on product_component (original_code);

CREATE INDEX "3bd2f091961b4403b686_ix" ON product_component (id) WHERE deleted_at IS NULL;
CREATE INDEX "6fb77a7e797f4ef58648_ui" ON product_component (original_code) WHERE deleted_at IS NULL;
CREATE INDEX "933fcd54306a4139a14d_ix" ON product_component (product_component_category_id) WHERE deleted_at IS NULL;
