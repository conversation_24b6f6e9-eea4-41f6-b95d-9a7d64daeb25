-- update conditional to branch table having only 1 record (thus excluding HQ databases)
UPDATE basket_item SET branch_id = (SELECT id FROM branch) WHERE (SELECT COUNT(id) FROM branch) = 1;
UPDATE auditorium SET branch_id = (SELECT id FROM branch) WHERE (SELECT COUNT(id) FROM branch) = 1;

ALTER TABLE branch ADD COLUMN type TEXT DEFAULT 'REGULAR';
ALTER TABLE branch ALTER COLUMN type SET NOT NULL;
-- Bratislava Bory Mall
UPDATE branch SET type = 'FLAGSHIP' WHERE code = '510640';

ALTER TABLE product ADD COLUMN flagship_price NUMERIC;
