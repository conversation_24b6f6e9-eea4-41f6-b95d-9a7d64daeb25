ALTER TABLE screening
    ADD COLUMN auditorium_layout_id UUID;

WITH auditorium_layouts AS (SELECT id,
                                   original_id
                            FROM auditorium_layout)
UPDATE screening
SET auditorium_layout_id = al.id
FROM auditorium_layouts al
WHERE screening.original_layout_id = al.original_id;

ALTER TABLE screening
    ADD CONSTRAINT "a56cf3c9f35f44da828d_fk" FOREIGN KEY (auditorium_layout_id) REFERENCES "auditorium_layout" (id);

CREATE INDEX "9401e8c7fdd6438ea384_ix" ON "screening" (auditorium_layout_id) WHERE screening.auditorium_layout_id IS NOT NULL;
