ALTER TABLE daily_closing_movement ADD COLUMN deleted_at TIMESTAMP;

DROP INDEX "ce7c3535e1ef471a94f8_ix";
DROP INDEX "97eb54256ee847b384c4_ui";
DROP INDEX "b0900887cbed4a18bb41_ix";

CREATE INDEX "b4a69d4088804d5ba85c_ix" ON "daily_closing_movement" (daily_closing_id) WHERE deleted_at IS NULL;
CREATE UNIQUE INDEX "5176fcaa78c047369894_ui" ON "daily_closing_movement" (daily_closing_id, type, item_subtype, payment_type)
    WHERE item_subtype != 'OTHER' AND deleted_at IS NULL;
CREATE INDEX "da34f84319494e0c898e_ix" ON "daily_closing_movement" (daily_closing_id, item_subtype)
    WHERE item_subtype = 'OTHER' AND deleted_at IS NULL;

