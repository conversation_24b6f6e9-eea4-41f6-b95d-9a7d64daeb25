CREATE TABLE product_component_category
(
    id            UUID PRIMARY KEY,
    original_id   INT,
    original_code TEXT      NOT NULL,
    title         TEXT      NOT NULL,
    tax_rate      INT       NOT NULL,
    created_at    TIMESTAMP NOT NULL,
    updated_at    TIMESTAMP NOT NULL,
    deleted_at    TIMESTAMP,
    created_by    TEXT,
    updated_by    TEXT
);

CREATE UNIQUE INDEX "5da19f7d7a1347e7aba0_ui" ON product_component_category (original_id);
CREATE UNIQUE INDEX "22ef20c6066348bf9f4a_ui" ON product_component_category (original_code) WHERE deleted_at IS NULL;

ALTER TABLE product_component ADD COLUMN product_component_category_id UUID;
ALTER TABLE product_component ADD CONSTRAINT "8036bb682597487a87c9_fk" FOREIGN KEY (product_component_category_id) REFERENCES product_component_category(id);

UPDATE synchronization_from_mssql SET last_synchronization = 'epoch' WHERE type = 'PRODUCT_COMPONENT';