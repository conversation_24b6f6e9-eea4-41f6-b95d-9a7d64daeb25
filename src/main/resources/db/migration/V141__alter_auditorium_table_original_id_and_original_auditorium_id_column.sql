ALTER TABLE auditorium
    ALTER COLUMN original_id DROP NOT NULL;

ALTER TABLE auditorium
    RENAME COLUMN original_auditorium_id TO original_auditorium_code;

ALTER TABLE auditorium ADD COLUMN created_by TEXT NOT NULL DEFAULT 'anonymous';
ALTER TABLE auditorium ALTER COLUMN created_by DROP DEFAULT;
ALTER TABLE auditorium ADD COLUMN updated_by TEXT NOT NULL DEFAULT 'anonymous';
ALTER TABLE auditorium ALTER COLUMN updated_by DROP DEFAULT;

DROP INDEX "cba5439b6da542989522_ui";
CREATE UNIQUE INDEX "fc3b4ffbfa8b4e969378_ui" ON auditorium (original_id) WHERE original_id IS NOT NULL;
CREATE UNIQUE INDEX "f21eda67630b4efca7d6_ui" ON auditorium (code);
CREATE UNIQUE INDEX "039ec7303a6040b68c57_ui" ON auditorium (original_auditorium_code);
