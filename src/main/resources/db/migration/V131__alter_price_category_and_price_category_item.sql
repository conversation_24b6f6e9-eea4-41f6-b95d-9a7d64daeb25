ALTER TABLE price_category ADD COLUMN deleted_at TIMESTAMP;
ALTER TABLE price_category ADD COLUMN created_by TEXT NOT NULL DEFAULT 'anonymous';
ALTER TABLE price_category ALTER COLUMN created_by DROP DEFAULT;
ALTER TABLE price_category ADD COLUMN updated_by TEXT NOT NULL DEFAULT 'anonymous';
ALTER TABLE price_category ALTER COLUMN updated_by DROP DEFAULT;
ALTER TABLE price_category ALTER COLUMN original_id DROP NOT NULL;
ALTER TABLE price_category ALTER COLUMN original_code DROP NOT NULL;

ALTER TABLE price_category_item ADD COLUMN created_by TEXT NOT NULL DEFAULT 'anonymous';
ALTER TABLE price_category_item ALTER COLUMN created_by DROP DEFAULT;
ALTER TABLE price_category_item ADD COLUMN updated_by TEXT NOT NULL DEFAULT 'anonymous';
ALTER TABLE price_category_item ALTER COLUMN updated_by DROP DEFAULT;

