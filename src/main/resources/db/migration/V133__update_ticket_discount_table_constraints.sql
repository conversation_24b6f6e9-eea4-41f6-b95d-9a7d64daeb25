ALTER TABLE ticket_discount ALTER COLUMN original_id DROP NOT NULL;

ALTER TABLE ticket_discount ADD COLUMN created_by TEXT NOT NULL DEFAULT 'anonymous';
ALTER TABLE ticket_discount ALTER COLUMN created_by DROP DEFAULT;
ALTER TABLE ticket_discount ADD COLUMN updated_by TEXT NOT NULL DEFAULT 'anonymous';
ALTER TABLE ticket_discount ALTER COLUMN updated_by DROP DEFAULT;
ALTER TABLE ticket_discount ADD COLUMN deleted_at TIMESTAMP;

DROP INDEX "c90d9871f1864512a767_ui"; -- ticket_discount (code)
CREATE UNIQUE INDEX "f7a293da72b046b98b80_ui" ON ticket_discount (code) WHERE deleted_at IS NULL;
