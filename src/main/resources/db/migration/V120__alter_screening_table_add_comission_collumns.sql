ALTER TABLE screening
    ADD COLUMN pro_commission         INT,
    ADD COLUMN film_fond_commission   INT,
    ADD COLUMN distributor_commission INT,
    ADD COLUMN publish_online         BOOLEAN,
    ADD COLUMN state                  TEXT NOT NULL DEFAULT 'PUBLISHED',
    ADD COLUMN ad_time_slot           INT DEFAULT 15;

ALTER TABLE screening
    ALTER COLUMN state DROP DEFAULT,
    ALTER COLUMN ad_time_slot DROP DEFAULT;

CREATE INDEX "4e71fd23231d46fdb469_ix" ON "screening" (state);

-- changing data type of sale_time_limit and converting TIME to INT minutes
ALTER TABLE screening
    ADD COLUMN sale_time_limit_temp INT;
UPDATE screening
SET sale_time_limit_temp = EXTRACT(EPOCH FROM sale_time_limit) / 60;
ALTER TABLE screening
    DROP COLUMN sale_time_limit;
ALTER TABLE screening
    RENAME COLUMN sale_time_limit_temp TO sale_time_limit;
