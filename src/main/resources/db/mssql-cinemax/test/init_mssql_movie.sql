------------------------
--- init test tables ---
------------------------
CREATE TABLE [dbo].[rfilm](
    [rfilmID] [int] IDENTITY(1,1) NOT NULL,
    [distr] [char](4) NOT NULL,
    [poradatel] [char](6) NOT NULL,
    [cisfilm] [char](6) NOT NULL,
    [nazevf] [varchar](50) NOT NULL,
    [ornazevf] [varchar](45) NOT NULL,
    [prod] [char](2) NOT NULL,
    [datprem] [datetime] NULL,
    [monopol] [datetime] NULL,
    [zanrf] [char](2) NOT NULL,
    [pristup] [char](5) NOT NULL,
    [minf] [char](3) NOT NULL,
    [uprava] [char](15) NOT NULL,
    [producent] [char](40) NOT NULL,
    [rezie] [char](30) NOT NULL,
    [scenar] [char](30) NOT NULL,
    [kamera] [char](30) NOT NULL,
    [strih] [char](30) NOT NULL,
    [hudba] [char](30) NOT NULL,
    [hraji] [text] NULL,
    [slogan] [text] NULL,
    [popis] [text] NULL,
    [cisfilm_u] [int] NOT NULL,
    [zuziv] [char](10) NOT NULL,
    [zcas] [smalldatetime] NULL,
    [cenkat] [nchar](2) NOT NULL,
    [nlist] [smallint] NOT NULL,
    [kodupravy] [char](1) NOT NULL,
    [form_2d] [bit] NOT NULL,
    [form_3d] [bit] NOT NULL,
    [cisfilmUFD] [char](10) NOT NULL,
    [Jazyk] [char](4) NOT NULL,
    [cform] [char](2) NOT NULL,
    [cfotrm] [char](2) NOT NULL,
    PRIMARY KEY CLUSTERED
(
[rfilmID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
    ) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
;

ALTER TABLE [dbo].[rfilm] ADD  CONSTRAINT [DF__rfilm__poradatel__4F47C5E3]  DEFAULT ('') FOR [poradatel]
;
ALTER TABLE [dbo].[rfilm] ADD  CONSTRAINT [DF__rfilm__ornazevf__503BEA1C]  DEFAULT ('') FOR [ornazevf]
;
ALTER TABLE [dbo].[rfilm] ADD  CONSTRAINT [DF__rfilm__prod__51300E55]  DEFAULT ('') FOR [prod]
;
ALTER TABLE [dbo].[rfilm] ADD  CONSTRAINT [DF__rfilm__zanrf__5224328E]  DEFAULT ('') FOR [zanrf]
;
ALTER TABLE [dbo].[rfilm] ADD  CONSTRAINT [DF__rfilm__pristup__531856C7]  DEFAULT ('') FOR [pristup]
;
ALTER TABLE [dbo].[rfilm] ADD  CONSTRAINT [DF__rfilm__minf__540C7B00]  DEFAULT ('') FOR [minf]
;
ALTER TABLE [dbo].[rfilm] ADD  CONSTRAINT [DF__rfilm__uprava__55009F39]  DEFAULT ('') FOR [uprava]
;
ALTER TABLE [dbo].[rfilm] ADD  CONSTRAINT [DF__rfilm__producent__55F4C372]  DEFAULT ('') FOR [producent]
;
ALTER TABLE [dbo].[rfilm] ADD  CONSTRAINT [DF__rfilm__rezie__56E8E7AB]  DEFAULT ('') FOR [rezie]
;
ALTER TABLE [dbo].[rfilm] ADD  CONSTRAINT [DF__rfilm__scenar__57DD0BE4]  DEFAULT ('') FOR [scenar]
;
ALTER TABLE [dbo].[rfilm] ADD  CONSTRAINT [DF__rfilm__kamera__58D1301D]  DEFAULT ('') FOR [kamera]
;
ALTER TABLE [dbo].[rfilm] ADD  CONSTRAINT [DF__rfilm__strih__59C55456]  DEFAULT ('') FOR [strih]
;
ALTER TABLE [dbo].[rfilm] ADD  CONSTRAINT [DF__rfilm__hudba__5AB9788F]  DEFAULT ('') FOR [hudba]
;
ALTER TABLE [dbo].[rfilm] ADD  CONSTRAINT [DF__rfilm__cisfilm_u__5BAD9CC8]  DEFAULT ((0)) FOR [cisfilm_u]
;
ALTER TABLE [dbo].[rfilm] ADD  CONSTRAINT [DF__rfilm__zuziv__5CA1C101]  DEFAULT ('') FOR [zuziv]
;
ALTER TABLE [dbo].[rfilm] ADD  CONSTRAINT [DF__rfilm__zcas__5D95E53A]  DEFAULT (getdate()) FOR [zcas]
;
ALTER TABLE [dbo].[rfilm] ADD  CONSTRAINT [DF__rfilm__cenkat__5E8A0973]  DEFAULT ('') FOR [cenkat]
;
ALTER TABLE [dbo].[rfilm] ADD  CONSTRAINT [DF_rfilm_nlist_1]  DEFAULT ((0)) FOR [nlist]
;
ALTER TABLE [dbo].[rfilm] ADD  DEFAULT ('') FOR [kodupravy]
;
ALTER TABLE [dbo].[rfilm] ADD  DEFAULT ((0)) FOR [form_2d]
;
ALTER TABLE [dbo].[rfilm] ADD  DEFAULT ((0)) FOR [form_3d]
;
ALTER TABLE [dbo].[rfilm] ADD  DEFAULT ('') FOR [cisfilmUFD]
;
ALTER TABLE [dbo].[rfilm] ADD  DEFAULT ('') FOR [Jazyk]
;
ALTER TABLE [dbo].[rfilm] ADD  DEFAULT ('') FOR [cform]
;
ALTER TABLE [dbo].[rfilm] ADD  DEFAULT ('') FOR [cfotrm]
;

SET ANSI_NULLS ON
;
SET QUOTED_IDENTIFIER ON
;
CREATE TABLE [dbo].[rprog](
    [rprogID] [int] IDENTITY(1,1) NOT NULL,
    [rupravaID] [int] NOT NULL,
    [rfilmID] [int] NOT NULL,
    [rvstID] [int] NOT NULL,
    [csalu] [char](6) NOT NULL,
    [porad] [smalldatetime] NULL,
    [distr] [char](4) NOT NULL,
    [ciskop] [char](4) NOT NULL,
    [limit] [smallint] NOT NULL,
    [pujcovne] [numeric](4, 2) NOT NULL,
    [cenfix] [smallmoney] NOT NULL,
    [cenmin] [smallmoney] NOT NULL,
    [cenkat] [char](3) NOT NULL,
    [osa] [numeric](5, 2) NOT NULL,
    [osaproc] [bit] NOT NULL,
    [fk] [numeric](5, 2) NOT NULL,
    [fkproc] [bit] NOT NULL,
    [sp] [numeric](5, 2) NOT NULL,
    [spproc] [bit] NOT NULL,
    [ps] [bit] NOT NULL,
    [priplsluz] [numeric](6, 2) NOT NULL,
    [negraficky] [bit] NOT NULL,
    [naweb] [bit] NOT NULL,
    [stop] [bit] NOT NULL,
    [odpadlo] [bit] NOT NULL,
    [neucast] [bit] NOT NULL,
    [divaku] [smallint] NOT NULL,
    [trzba] [numeric](9, 2) NOT NULL,
    [rezervace] [smallint] NOT NULL,
    [fondcel] [numeric](9, 2) NOT NULL,
    [priplcel] [numeric](9, 2) NOT NULL,
    [odrezerv] [bit] NOT NULL,
    [kdyodrezerv] [smalldatetime] NULL,
    [cbanID] [smallint] NOT NULL,
    [pohhot] [char](2) NOT NULL,
    [pohfakt] [char](2) NOT NULL,
    [pohzal] [char](2) NOT NULL,
    [delegace] [int] NOT NULL,
    [casprod] [smallint] NOT NULL,
    [pozn] [text] NULL,
    [zuziv] [char](10) NOT NULL,
    [zcas] [smalldatetime] NULL,
    [neikar] [bit] NOT NULL,
    [rpoukazyID] [int] NOT NULL,
    [cistyp] [char](3) NOT NULL,
    [rcsluzby] [char](2) NOT NULL,
    [sluzbyDbox] [numeric](6, 2) NOT NULL,
    [sluzbyVIP] [numeric](6, 2) NOT NULL,
    [priplatekVIP] [numeric](6, 2) NOT NULL,
    [sluzbyPremium] [numeric](6, 2) NOT NULL,
    [priplatekPremium] [numeric](6, 2) NOT NULL,
    [sluzbyImax] [numeric](6, 2) NOT NULL,
    [priplatekImax] [numeric](6, 2) NOT NULL,
    [cistyp2] [char](3) NOT NULL,
    [cistyp3] [char](3) NOT NULL,
    [cistyp4] [char](3) NOT NULL,
    [vs] [char](10) NOT NULL,
    [jazyk] [char](4) NOT NULL,
    [cfotrm] [char](2) NOT NULL,
    [sluzbyUltraX] [numeric](6, 2) NOT NULL,
    [priplatekUltraX] [numeric](6, 2) NOT NULL,
    CONSTRAINT [PK__rprog__7CD98669] PRIMARY KEY CLUSTERED
(
[rprogID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
    ) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
;
ALTER TABLE [dbo].[rprog] ADD  CONSTRAINT [DF__rprog__ciskop__10E07F16]  DEFAULT ('') FOR [ciskop]
;
ALTER TABLE [dbo].[rprog] ADD  CONSTRAINT [DF__rprog__limit__11D4A34F]  DEFAULT ((0)) FOR [limit]
;
ALTER TABLE [dbo].[rprog] ADD  CONSTRAINT [DF__rprog__pujcovne__12C8C788]  DEFAULT ((0)) FOR [pujcovne]
;
ALTER TABLE [dbo].[rprog] ADD  CONSTRAINT [DF__rprog__cenfix__13BCEBC1]  DEFAULT ((0)) FOR [cenfix]
;
ALTER TABLE [dbo].[rprog] ADD  CONSTRAINT [DF__rprog__cenmin__14B10FFA]  DEFAULT ((0)) FOR [cenmin]
;
ALTER TABLE [dbo].[rprog] ADD  CONSTRAINT [DF__rprog__cenkat__15A53433]  DEFAULT ('') FOR [cenkat]
;
ALTER TABLE [dbo].[rprog] ADD  CONSTRAINT [DF__rprog__osa__1699586C]  DEFAULT ((0)) FOR [osa]
;
ALTER TABLE [dbo].[rprog] ADD  CONSTRAINT [DF__rprog__osaproc__178D7CA5]  DEFAULT ((0)) FOR [osaproc]
;
ALTER TABLE [dbo].[rprog] ADD  CONSTRAINT [DF__rprog__fk__1881A0DE]  DEFAULT ((0)) FOR [fk]
;
ALTER TABLE [dbo].[rprog] ADD  CONSTRAINT [DF__rprog__fkproc__1975C517]  DEFAULT ((0)) FOR [fkproc]
;
ALTER TABLE [dbo].[rprog] ADD  CONSTRAINT [DF_rprog_sp_1]  DEFAULT ((0)) FOR [sp]
;
ALTER TABLE [dbo].[rprog] ADD  CONSTRAINT [DF_rprog_spproc_1]  DEFAULT ((1)) FOR [spproc]
;
ALTER TABLE [dbo].[rprog] ADD  CONSTRAINT [DF__rprog__ps__1A69E950]  DEFAULT ((0)) FOR [ps]
;
ALTER TABLE [dbo].[rprog] ADD  CONSTRAINT [DF_rprog_priplsluz]  DEFAULT ((0)) FOR [priplsluz]
;
ALTER TABLE [dbo].[rprog] ADD  CONSTRAINT [DF__rprog__negrafick__1C5231C2]  DEFAULT ((0)) FOR [negraficky]
;
ALTER TABLE [dbo].[rprog] ADD  CONSTRAINT [DF_rprog_naweb]  DEFAULT ((1)) FOR [naweb]
;
ALTER TABLE [dbo].[rprog] ADD  CONSTRAINT [DF__rprog__stop__1D4655FB]  DEFAULT ((0)) FOR [stop]
;
ALTER TABLE [dbo].[rprog] ADD  CONSTRAINT [DF__rprog__odpadlo__1E3A7A34]  DEFAULT ((0)) FOR [odpadlo]
;
ALTER TABLE [dbo].[rprog] ADD  CONSTRAINT [DF__rprog__neucast__1F2E9E6D]  DEFAULT ((0)) FOR [neucast]
;
ALTER TABLE [dbo].[rprog] ADD  CONSTRAINT [DF__rprog__divaku__2022C2A6]  DEFAULT ((0)) FOR [divaku]
;
ALTER TABLE [dbo].[rprog] ADD  CONSTRAINT [DF__rprog__trzba__2116E6DF]  DEFAULT ((0)) FOR [trzba]
;
ALTER TABLE [dbo].[rprog] ADD  CONSTRAINT [DF__rprog__rezervace__220B0B18]  DEFAULT ((0)) FOR [rezervace]
;
ALTER TABLE [dbo].[rprog] ADD  CONSTRAINT [DF__rprog__fondcel__22FF2F51]  DEFAULT ((0)) FOR [fondcel]
;
ALTER TABLE [dbo].[rprog] ADD  CONSTRAINT [DF__rprog__priplcel__23F3538A]  DEFAULT ((0)) FOR [priplcel]
;
ALTER TABLE [dbo].[rprog] ADD  CONSTRAINT [DF__rprog__odrezerv__24E777C3]  DEFAULT ((0)) FOR [odrezerv]
;
ALTER TABLE [dbo].[rprog] ADD  CONSTRAINT [DF__rprog__cbanID__25DB9BFC]  DEFAULT ((0)) FOR [cbanID]
;
ALTER TABLE [dbo].[rprog] ADD  CONSTRAINT [DF__rprog__pohhot__26CFC035]  DEFAULT ('') FOR [pohhot]
;
ALTER TABLE [dbo].[rprog] ADD  CONSTRAINT [DF__rprog__pohfakt__27C3E46E]  DEFAULT ('') FOR [pohfakt]
;
ALTER TABLE [dbo].[rprog] ADD  CONSTRAINT [DF__rprog__pohzal__28B808A7]  DEFAULT ('') FOR [pohzal]
;
ALTER TABLE [dbo].[rprog] ADD  CONSTRAINT [DF__rprog__delegace__29AC2CE0]  DEFAULT ((0)) FOR [delegace]
;
ALTER TABLE [dbo].[rprog] ADD  CONSTRAINT [DF_rprog_casprod_1]  DEFAULT ((0)) FOR [casprod]
;
ALTER TABLE [dbo].[rprog] ADD  CONSTRAINT [DF__rprog__zuziv__2AA05119]  DEFAULT ('') FOR [zuziv]
;
ALTER TABLE [dbo].[rprog] ADD  CONSTRAINT [DF__rprog__zcas__2B947552]  DEFAULT (getdate()) FOR [zcas]
;
ALTER TABLE [dbo].[rprog] ADD  DEFAULT ((0)) FOR [neikar]
;
ALTER TABLE [dbo].[rprog] ADD  DEFAULT ((0)) FOR [rpoukazyID]
;
ALTER TABLE [dbo].[rprog] ADD  DEFAULT ('00') FOR [cistyp]
;
ALTER TABLE [dbo].[rprog] ADD  DEFAULT ('') FOR [rcsluzby]
;
ALTER TABLE [dbo].[rprog] ADD  DEFAULT ((0)) FOR [sluzbyDbox]
;
ALTER TABLE [dbo].[rprog] ADD  DEFAULT ((0)) FOR [sluzbyVIP]
;
ALTER TABLE [dbo].[rprog] ADD  DEFAULT ((0)) FOR [priplatekVIP]
;
ALTER TABLE [dbo].[rprog] ADD  DEFAULT ((0)) FOR [sluzbyPremium]
;
ALTER TABLE [dbo].[rprog] ADD  DEFAULT ((0)) FOR [priplatekPremium]
;
ALTER TABLE [dbo].[rprog] ADD  DEFAULT ((0)) FOR [sluzbyImax]
;
ALTER TABLE [dbo].[rprog] ADD  DEFAULT ((0)) FOR [priplatekImax]
;
ALTER TABLE [dbo].[rprog] ADD  DEFAULT ('') FOR [cistyp2]
;
ALTER TABLE [dbo].[rprog] ADD  DEFAULT ('') FOR [cistyp3]
;
ALTER TABLE [dbo].[rprog] ADD  DEFAULT ('') FOR [cistyp4]
;
ALTER TABLE [dbo].[rprog] ADD  DEFAULT ('') FOR [vs]
;
ALTER TABLE [dbo].[rprog] ADD  DEFAULT ('') FOR [jazyk]
;
ALTER TABLE [dbo].[rprog] ADD  DEFAULT ('') FOR [cfotrm]
;
ALTER TABLE [dbo].[rprog] ADD  DEFAULT ((0)) FOR [sluzbyUltraX]
;
ALTER TABLE [dbo].[rprog] ADD  DEFAULT ((0)) FOR [priplatekUltraX]
;

----------------------
--- init test data ---
----------------------
INSERT INTO rps_CinemaxE.dbo.rfilm (distr, poradatel, cisfilm, nazevf, ornazevf, prod, datprem, monopol, zanrf, pristup, minf, uprava, producent, rezie, scenar, kamera, strih, hudba, hraji, slogan, popis, cisfilm_u, zuziv, zcas, cenkat, nlist, kodupravy, form_2d, form_3d, cisfilmUFD, Jazyk, cform, cfotrm) VALUES (0, N'      ', N'001080', N'Bohemian Rhapsody 2D (ST)                         ', N'Bohemian Rhapsody 2D (ST)                    ', N'56', N'2018-11-01 0:00:00', N'', N'i ', N'12   ', 134, N'               ', N'                                        ', N'                              ', N'                              ', N'                              ', N'                              ', N'                              ', N'', N'', N'', 0, N'erika     ', N'2019-10-08 18:59:00', N'  ', 774, N'2', N'True', N'False', N'CIN16163A2', N'    ', N'  ', N'  ');
INSERT INTO rps_CinemaxE.dbo.rfilm (distr, poradatel, cisfilm, nazevf, ornazevf, prod, datprem, monopol, zanrf, pristup, minf, uprava, producent, rezie, scenar, kamera, strih, hudba, hraji, slogan, popis, cisfilm_u, zuziv, zcas, cenkat, nlist, kodupravy, form_2d, form_3d, cisfilmUFD, Jazyk, cform, cfotrm) VALUES (4, N'      ', N'200402', N'Pán prstenov: Návrat krála                        ', N'LOTR:The Return of the King                  ', N'56', N'2004-01-15 0:00:00', N'2010-12-31 0:00:00', N'da', N'     ', 200, N'Titulky        ', N'                                        ', N'Peter Jackson                 ', N'                              ', N'                              ', N'                              ', N'                              ', N'', N'', N'', 0, N'erika     ', N'2020-07-27 19:38:00', N'  ', 0, N'1', N'False', N'False', N'          ', N'    ', N'  ', N'  ');
INSERT INTO rps_CinemaxE.dbo.rfilm (distr, poradatel, cisfilm, nazevf, ornazevf, prod, datprem, monopol, zanrf, pristup, minf, uprava, producent, rezie, scenar, kamera, strih, hudba, hraji, slogan, popis, cisfilm_u, zuziv, zcas, cenkat, nlist, kodupravy, form_2d, form_3d, cisfilmUFD, Jazyk, cform, cfotrm) VALUES (0, N'      ', N'CN0518', N'Strážcovia galaxie 3 IMAX 3D (ST)                 ', N'                                             ', N'  ', N'2023-05-04 0:00:00', N'', N'  ', N'ANO  ', 149, N'               ', N'                                        ', N'                              ', N'                              ', N'                              ', N'                              ', N'                              ', N'', N'', N'', 0, N'ildiko    ', N'2023-04-01 13:53:00', N'  ', 0, N' ', N'False', N'False', N'          ', N'    ', N'  ', N'  ');
-- record with blank title
INSERT INTO rps_CinemaxE.dbo.rfilm (distr, poradatel, cisfilm, nazevf, ornazevf, prod, datprem, monopol, zanrf, pristup, minf, uprava, producent, rezie, scenar, kamera, strih, hudba, hraji, slogan, popis, cisfilm_u, zuziv, zcas, cenkat, nlist, kodupravy, form_2d, form_3d, cisfilmUFD, Jazyk, cform, cfotrm) VALUES (0, N'      ', N'CN0518', N'                                             ', N'                                             ', N'56', N'2023-05-04 0:00:00', N'', N'2 ', N'12   ', 149, N'               ', N'                                        ', N'                              ', N'                              ', N'                              ', N'                              ', N'                              ', N'', N'', N'', 0, N'ildiko    ', N'2023-04-01 13:53:00', N'  ', 0, N'2', N'False', N'False', N'          ', N'    ', N'  ', N'  ');

INSERT INTO rps_CinemaxE.dbo.rprog (rupravaID, rfilmID, rvstID, csalu, porad, distr, ciskop, limit, pujcovne, cenfix, cenmin, cenkat, osa, osaproc, fk, fkproc, sp, spproc, ps, priplsluz, negraficky, naweb, stop, odpadlo, neucast, divaku, trzba, rezervace, fondcel, priplcel, odrezerv, kdyodrezerv, cbanID, pohhot, pohfakt, pohzal, delegace, casprod, pozn, zuziv, zcas, neikar, rpoukazyID, cistyp, rcsluzby, sluzbyDbox, sluzbyVIP, priplatekVIP, sluzbyPremium, priplatekPremium, sluzbyImax, priplatekImax, cistyp2, cistyp3, cistyp4, vs, jazyk, cfotrm, sluzbyUltraX, priplatekUltraX) VALUES (1, 1, 749, 522630, N'2023-07-21 16:00:00', 0, N'    ', 222, N'50.00', N'0.0000', N'0.0000', N'002', N'1.00', N'True', N'4.00', N'True', N'0.00', N'True', N'False', N'0.00', N'False', N'True', N'False', N'False', N'False', 0, N'0.00', 0, N'0.00', N'0.00', N'False', N'', 0, 21, 8, N'  ', 0, 30, N'', N'DISdata   ', N'2023-07-18 15:14:00', N'False', 0, '00', N'  ', N'1.00', N'0.00', N'0.00', N'0.00', N'0.00', N'2.00', N'3.00', N'   ', N'   ', N'   ', N'          ', N'    ', N'  ', N'0.00', N'0.00');
INSERT INTO rps_CinemaxE.dbo.rprog (rupravaID, rfilmID, rvstID, csalu, porad, distr, ciskop, limit, pujcovne, cenfix, cenmin, cenkat, osa, osaproc, fk, fkproc, sp, spproc, ps, priplsluz, negraficky, naweb, stop, odpadlo, neucast, divaku, trzba, rezervace, fondcel, priplcel, odrezerv, kdyodrezerv, cbanID, pohhot, pohfakt, pohzal, delegace, casprod, pozn, zuziv, zcas, neikar, rpoukazyID, cistyp, rcsluzby, sluzbyDbox, sluzbyVIP, priplatekVIP, sluzbyPremium, priplatekPremium, sluzbyImax, priplatekImax, cistyp2, cistyp3, cistyp4, vs, jazyk, cfotrm, sluzbyUltraX, priplatekUltraX) VALUES (1, 2, 749, 522630, N'2023-07-21 16:00:00', 0, N'    ', 222, N'50.00', N'0.0000', N'0.0000', N'002', N'1.00', N'True', N'4.00', N'True', N'0.00', N'True', N'False', N'0.00', N'False', N'True', N'False', N'False', N'False', 0, N'0.00', 0, N'0.00', N'0.00', N'False', N'', 0, 21, 8, N'  ', 0, 30, N'', N'DISdata   ', N'2023-07-18 15:14:00', N'False', 0, '00', N'  ', N'1.00', N'0.00', N'0.00', N'0.00', N'0.00', N'2.00', N'3.00', N'   ', N'   ', N'   ', N'          ', N'    ', N'  ', N'0.00', N'0.00');
INSERT INTO rps_CinemaxE.dbo.rprog (rupravaID, rfilmID, rvstID, csalu, porad, distr, ciskop, limit, pujcovne, cenfix, cenmin, cenkat, osa, osaproc, fk, fkproc, sp, spproc, ps, priplsluz, negraficky, naweb, stop, odpadlo, neucast, divaku, trzba, rezervace, fondcel, priplcel, odrezerv, kdyodrezerv, cbanID, pohhot, pohfakt, pohzal, delegace, casprod, pozn, zuziv, zcas, neikar, rpoukazyID, cistyp, rcsluzby, sluzbyDbox, sluzbyVIP, priplatekVIP, sluzbyPremium, priplatekPremium, sluzbyImax, priplatekImax, cistyp2, cistyp3, cistyp4, vs, jazyk, cfotrm, sluzbyUltraX, priplatekUltraX) VALUES (1, 3, 749, 522630, N'2023-07-21 16:00:00', 0, N'    ', 222, N'50.00', N'0.0000', N'0.0000', N'002', N'1.00', N'True', N'4.00', N'True', N'0.00', N'True', N'False', N'0.00', N'False', N'True', N'False', N'False', N'False', 0, N'0.00', 0, N'0.00', N'0.00', N'False', N'', 0, 21, 8, N'  ', 0, 30, N'', N'DISdata   ', N'2023-07-18 15:14:00', N'False', 0, '00', N'  ', N'1.00', N'0.00', N'0.00', N'0.00', N'0.00', N'2.00', N'3.00', N'   ', N'   ', N'   ', N'          ', N'    ', N'  ', N'0.00', N'0.00');
INSERT INTO rps_CinemaxE.dbo.rprog (rupravaID, rfilmID, rvstID, csalu, porad, distr, ciskop, limit, pujcovne, cenfix, cenmin, cenkat, osa, osaproc, fk, fkproc, sp, spproc, ps, priplsluz, negraficky, naweb, stop, odpadlo, neucast, divaku, trzba, rezervace, fondcel, priplcel, odrezerv, kdyodrezerv, cbanID, pohhot, pohfakt, pohzal, delegace, casprod, pozn, zuziv, zcas, neikar, rpoukazyID, cistyp, rcsluzby, sluzbyDbox, sluzbyVIP, priplatekVIP, sluzbyPremium, priplatekPremium, sluzbyImax, priplatekImax, cistyp2, cistyp3, cistyp4, vs, jazyk, cfotrm, sluzbyUltraX, priplatekUltraX) VALUES (1, 4, 749, 522630, N'2023-07-21 16:00:00', 0, N'    ', 222, N'50.00', N'0.0000', N'0.0000', N'002', N'1.00', N'True', N'4.00', N'True', N'0.00', N'True', N'False', N'0.00', N'False', N'True', N'False', N'False', N'False', 0, N'0.00', 0, N'0.00', N'0.00', N'False', N'', 0, 21, 8, N'  ', 0, 30, N'', N'DISdata   ', N'2023-07-18 15:14:00', N'False', 0, '00', N'  ', N'1.00', N'0.00', N'0.00', N'0.00', N'0.00', N'2.00', N'3.00', N'   ', N'   ', N'   ', N'          ', N'    ', N'  ', N'0.00', N'0.00');
INSERT INTO rps_CinemaxE.dbo.rprog (rupravaID, rfilmID, rvstID, csalu, porad, distr, ciskop, limit, pujcovne, cenfix, cenmin, cenkat, osa, osaproc, fk, fkproc, sp, spproc, ps, priplsluz, negraficky, naweb, stop, odpadlo, neucast, divaku, trzba, rezervace, fondcel, priplcel, odrezerv, kdyodrezerv, cbanID, pohhot, pohfakt, pohzal, delegace, casprod, pozn, zuziv, zcas, neikar, rpoukazyID, cistyp, rcsluzby, sluzbyDbox, sluzbyVIP, priplatekVIP, sluzbyPremium, priplatekPremium, sluzbyImax, priplatekImax, cistyp2, cistyp3, cistyp4, vs, jazyk, cfotrm, sluzbyUltraX, priplatekUltraX) VALUES (1, 5, 749, 522630, N'2023-07-21 16:00:00', 0, N'    ', 222, N'50.00', N'0.0000', N'0.0000', N'002', N'1.00', N'True', N'4.00', N'True', N'0.00', N'True', N'False', N'0.00', N'False', N'True', N'False', N'False', N'False', 0, N'0.00', 0, N'0.00', N'0.00', N'False', N'', 0, 21, 8, N'  ', 0, 30, N'', N'DISdata   ', N'2023-07-18 15:14:00', N'False', 0, '00', N'  ', N'1.00', N'0.00', N'0.00', N'0.00', N'0.00', N'2.00', N'3.00', N'   ', N'   ', N'   ', N'          ', N'    ', N'  ', N'0.00', N'0.00');
