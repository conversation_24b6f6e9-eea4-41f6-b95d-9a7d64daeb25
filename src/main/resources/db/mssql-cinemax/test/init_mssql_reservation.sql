-----------------------
--- init test table ---
-----------------------
SET ANSI_NULLS ON
;
SET QUOTED_IDENTIFIER ON
;
CREATE TABLE [dbo].[rsedadla](
    [rsedadlaID] [int] NOT NULL,
    [rprogID] [int] NOT NULL,
    [rmustrID] [int] NOT NULL,
    [rezervaceID] [int] NOT NULL,
    [csalu] [char](6) NOT NULL,
    [porad] [smalldatetime] NULL,
    [ceny] [varchar](51) NOT NULL,
    [cenkat] [tinyint] NOT NULL,
    [delegace] [tinyint] NOT NULL,
    [delegold] [tinyint] NOT NULL,
    [datprod] [smalldatetime] NULL,
    [cena] [numeric](6, 2) NOT NULL,
    [uzivprod] [int] NOT NULL,
    [ruzivID] [int] NOT NULL,
    [zuziv] [char](10) NOT NULL,
    [zcas] [datetime] NULL,
    [sleva] [char](8) NOT NULL,
    PRIMARY KEY CLUSTERED
(
[rsedadlaID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
    ) ON [PRIMARY]
;

ALTER TABLE [dbo].[rsedadla] ADD  CONSTRAINT [DF__rsedadla__rezerv__719CDDE7]  DEFAULT ((0)) FOR [rezervaceID]
;
ALTER TABLE [dbo].[rsedadla] ADD  CONSTRAINT [DF__rsedadla__jmeno__72910220]  DEFAULT ('') FOR [ceny]
;
ALTER TABLE [dbo].[rsedadla] ADD  CONSTRAINT [DF__rsedadla__delego__73852659]  DEFAULT ((0)) FOR [delegold]
;
ALTER TABLE [dbo].[rsedadla] ADD  CONSTRAINT [DF__rsedadla__cena__74794A92]  DEFAULT ((0)) FOR [cena]
;
ALTER TABLE [dbo].[rsedadla] ADD  CONSTRAINT [DF__rsedadla__uzivpr__756D6ECB]  DEFAULT ((0)) FOR [uzivprod]
;
ALTER TABLE [dbo].[rsedadla] ADD  CONSTRAINT [DF__rsedadla__ruzivI__76619304]  DEFAULT ((0)) FOR [ruzivID]
;
ALTER TABLE [dbo].[rsedadla] ADD  CONSTRAINT [DF__rsedadla__zuziv__7755B73D]  DEFAULT ('') FOR [zuziv]
;
ALTER TABLE [dbo].[rsedadla] ADD  CONSTRAINT [DF__rsedadla__zcas__7849DB76]  DEFAULT (getdate()) FOR [zcas]
;
ALTER TABLE [dbo].[rsedadla] ADD  DEFAULT ('') FOR [sleva]
;

----------------------
--- init test data ---
----------------------
INSERT INTO rps_CinemaxE.dbo.rsedadla (rsedadlaID, rprogID, rmustrID, rezervaceID, csalu, porad, ceny, cenkat, delegace, delegold, datprod, cena, uzivprod, ruzivID, zuziv, zcas, sleva) VALUES (1, 129361, 1676, 0, N'510070', N'2023-08-19 16:00:00', N'1,2,', 1, 11, 0, null, 0.00, 0, 0, N'monika    ', N'2023-06-05 14:54:12.570', N'        ');
INSERT INTO rps_CinemaxE.dbo.rsedadla (rsedadlaID, rprogID, rmustrID, rezervaceID, csalu, porad, ceny, cenkat, delegace, delegold, datprod, cena, uzivprod, ruzivID, zuziv, zcas, sleva) VALUES (2, 129361, 1680, 10, N'510070', N'2023-08-19 16:00:00', N'1,2,', 1, 12, 0, null, 0.00, 0, 0, N'monika    ', N'2023-06-05 14:55:14.923', N'        ');
INSERT INTO rps_CinemaxE.dbo.rsedadla (rsedadlaID, rprogID, rmustrID, rezervaceID, csalu, porad, ceny, cenkat, delegace, delegold, datprod, cena, uzivprod, ruzivID, zuziv, zcas, sleva) VALUES (3, 132579, 1639, 100, N'510060', N'2023-08-10 21:50:00', N'1,2,', 4, 12, 0, N'2023-08-10 21:50:00', 0.00, 0, 0, N'     ', N'2023-08-07 21:48:50.553', N'        ');
