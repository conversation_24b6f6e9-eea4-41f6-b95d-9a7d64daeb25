------------------------
--- init test table ---
------------------------
/****** Object:  Table [dbo].[ruprava]    Script Date: 28.06.2023 10:01:15 ******/
SET ANSI_NULLS ON
;
SET QUOTED_IDENTIFIER ON
;
CREATE TABLE [dbo].[ruprava](
    [rupravaID] [int] IDENTITY(1,1) NOT NULL,
    [ruprava2ID] [int] NOT NULL,
    [csalu] [char](6) NOT NULL,
    [cislo] [char](2) NOT NULL,
    [nazev] [varchar](40) NOT NULL,
    [popis] [text] NULL,
    [nabo] [smallint] NOT NULL,
    [zuziv] [char](10) NOT NULL,
    [zcas] [smalldatetime] NULL,
    CONSTRAINT [PK__ruprava__44FF419A] PRIMARY KEY CLUSTERED
(
[ruprava<PERSON>] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
    ) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
;
----------------------
--- init test data ---
----------------------
INSERT INTO rps_CinemaxE.dbo.ruprava (ruprava2ID,csalu,cislo,nazev,popis,nabo,zuziv,zcas) VALUES (0,N'510000',N'01',N'IMAX 2D                                 ',N'',0,N'jano      ','2019-05-03 16:37:00.000');
INSERT INTO rps_CinemaxE.dbo.ruprava (ruprava2ID,csalu,cislo,nazev,popis,nabo,zuziv,zcas) VALUES (0,N'510110',N'02',N'Základná',N'',0,N'          ','2019-05-04 16:37:00.000');
INSERT INTO rps_CinemaxE.dbo.ruprava (ruprava2ID,csalu,cislo,nazev,popis,nabo,zuziv,zcas) VALUES (0,N'510210',N'03',N'IMAX 3D',N'',0,N'          ','2019-05-05 16:37:00.000');


