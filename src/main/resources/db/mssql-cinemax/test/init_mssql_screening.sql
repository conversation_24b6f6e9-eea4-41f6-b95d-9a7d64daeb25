-----------------------
--- init test table ---
-----------------------
SET ANSI_NULLS ON
;
SET QUOTED_IDENTIFIER ON
;
CREATE TABLE [dbo].[rprog](
    [rprogID] [int] IDENTITY(1,1) NOT NULL,
    [rupravaID] [int] NOT NULL,
    [rfilmID] [int] NOT NULL,
    [rvstID] [int] NOT NULL,
    [csalu] [char](6) NOT NULL,
    [porad] [smalldatetime] NULL,
    [distr] [char](4) NOT NULL,
    [ciskop] [char](4) NOT NULL,
    [limit] [smallint] NOT NULL,
    [pujcovne] [numeric](4, 2) NOT NULL,
    [cenfix] [smallmoney] NOT NULL,
    [cenmin] [smallmoney] NOT NULL,
    [cenkat] [char](3) NOT NULL,
    [osa] [numeric](5, 2) NOT NULL,
    [osaproc] [bit] NOT NULL,
    [fk] [numeric](5, 2) NOT NULL,
    [fkproc] [bit] NOT NULL,
    [sp] [numeric](5, 2) NOT NULL,
    [spproc] [bit] NOT NULL,
    [ps] [bit] NOT NULL,
    [priplsluz] [numeric](6, 2) NOT NULL,
    [negraficky] [bit] NOT NULL,
    [naweb] [bit] NOT NULL,
    [stop] [bit] NOT NULL,
    [odpadlo] [bit] NOT NULL,
    [neucast] [bit] NOT NULL,
    [divaku] [smallint] NOT NULL,
    [trzba] [numeric](9, 2) NOT NULL,
    [rezervace] [smallint] NOT NULL,
    [fondcel] [numeric](9, 2) NOT NULL,
    [priplcel] [numeric](9, 2) NOT NULL,
    [odrezerv] [bit] NOT NULL,
    [kdyodrezerv] [smalldatetime] NULL,
    [cbanID] [smallint] NOT NULL,
    [pohhot] [char](2) NOT NULL,
    [pohfakt] [char](2) NOT NULL,
    [pohzal] [char](2) NOT NULL,
    [delegace] [int] NOT NULL,
    [casprod] [smallint] NOT NULL,
    [pozn] [text] NULL,
    [zuziv] [char](10) NOT NULL,
    [zcas] [smalldatetime] NULL,
    [neikar] [bit] NOT NULL,
    [rpoukazyID] [int] NOT NULL,
    [cistyp] [char](3) NOT NULL,
    [rcsluzby] [char](2) NOT NULL,
    [sluzbyDbox] [numeric](6, 2) NOT NULL,
    [sluzbyVIP] [numeric](6, 2) NOT NULL,
    [priplatekVIP] [numeric](6, 2) NOT NULL,
    [sluzbyPremium] [numeric](6, 2) NOT NULL,
    [priplatekPremium] [numeric](6, 2) NOT NULL,
    [sluzbyImax] [numeric](6, 2) NOT NULL,
    [priplatekImax] [numeric](6, 2) NOT NULL,
    [cistyp2] [char](3) NOT NULL,
    [cistyp3] [char](3) NOT NULL,
    [cistyp4] [char](3) NOT NULL,
    [vs] [char](10) NOT NULL,
    [jazyk] [char](4) NOT NULL,
    [cfotrm] [char](2) NOT NULL,
    [sluzbyUltraX] [numeric](6, 2) NOT NULL,
    [priplatekUltraX] [numeric](6, 2) NOT NULL,
    CONSTRAINT [PK__rprog__7CD98669] PRIMARY KEY CLUSTERED
(
[rprogID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
    ) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
;
ALTER TABLE [dbo].[rprog] ADD  CONSTRAINT [DF__rprog__ciskop__10E07F16]  DEFAULT ('') FOR [ciskop]
;
ALTER TABLE [dbo].[rprog] ADD  CONSTRAINT [DF__rprog__limit__11D4A34F]  DEFAULT ((0)) FOR [limit]
;
ALTER TABLE [dbo].[rprog] ADD  CONSTRAINT [DF__rprog__pujcovne__12C8C788]  DEFAULT ((0)) FOR [pujcovne]
;
ALTER TABLE [dbo].[rprog] ADD  CONSTRAINT [DF__rprog__cenfix__13BCEBC1]  DEFAULT ((0)) FOR [cenfix]
;
ALTER TABLE [dbo].[rprog] ADD  CONSTRAINT [DF__rprog__cenmin__14B10FFA]  DEFAULT ((0)) FOR [cenmin]
;
ALTER TABLE [dbo].[rprog] ADD  CONSTRAINT [DF__rprog__cenkat__15A53433]  DEFAULT ('') FOR [cenkat]
;
ALTER TABLE [dbo].[rprog] ADD  CONSTRAINT [DF__rprog__osa__1699586C]  DEFAULT ((0)) FOR [osa]
;
ALTER TABLE [dbo].[rprog] ADD  CONSTRAINT [DF__rprog__osaproc__178D7CA5]  DEFAULT ((0)) FOR [osaproc]
;
ALTER TABLE [dbo].[rprog] ADD  CONSTRAINT [DF__rprog__fk__1881A0DE]  DEFAULT ((0)) FOR [fk]
;
ALTER TABLE [dbo].[rprog] ADD  CONSTRAINT [DF__rprog__fkproc__1975C517]  DEFAULT ((0)) FOR [fkproc]
;
ALTER TABLE [dbo].[rprog] ADD  CONSTRAINT [DF_rprog_sp_1]  DEFAULT ((0)) FOR [sp]
;
ALTER TABLE [dbo].[rprog] ADD  CONSTRAINT [DF_rprog_spproc_1]  DEFAULT ((1)) FOR [spproc]
;
ALTER TABLE [dbo].[rprog] ADD  CONSTRAINT [DF__rprog__ps__1A69E950]  DEFAULT ((0)) FOR [ps]
;
ALTER TABLE [dbo].[rprog] ADD  CONSTRAINT [DF_rprog_priplsluz]  DEFAULT ((0)) FOR [priplsluz]
;
ALTER TABLE [dbo].[rprog] ADD  CONSTRAINT [DF__rprog__negrafick__1C5231C2]  DEFAULT ((0)) FOR [negraficky]
;
ALTER TABLE [dbo].[rprog] ADD  CONSTRAINT [DF_rprog_naweb]  DEFAULT ((1)) FOR [naweb]
;
ALTER TABLE [dbo].[rprog] ADD  CONSTRAINT [DF__rprog__stop__1D4655FB]  DEFAULT ((0)) FOR [stop]
;
ALTER TABLE [dbo].[rprog] ADD  CONSTRAINT [DF__rprog__odpadlo__1E3A7A34]  DEFAULT ((0)) FOR [odpadlo]
;
ALTER TABLE [dbo].[rprog] ADD  CONSTRAINT [DF__rprog__neucast__1F2E9E6D]  DEFAULT ((0)) FOR [neucast]
;
ALTER TABLE [dbo].[rprog] ADD  CONSTRAINT [DF__rprog__divaku__2022C2A6]  DEFAULT ((0)) FOR [divaku]
;
ALTER TABLE [dbo].[rprog] ADD  CONSTRAINT [DF__rprog__trzba__2116E6DF]  DEFAULT ((0)) FOR [trzba]
;
ALTER TABLE [dbo].[rprog] ADD  CONSTRAINT [DF__rprog__rezervace__220B0B18]  DEFAULT ((0)) FOR [rezervace]
;
ALTER TABLE [dbo].[rprog] ADD  CONSTRAINT [DF__rprog__fondcel__22FF2F51]  DEFAULT ((0)) FOR [fondcel]
;
ALTER TABLE [dbo].[rprog] ADD  CONSTRAINT [DF__rprog__priplcel__23F3538A]  DEFAULT ((0)) FOR [priplcel]
;
ALTER TABLE [dbo].[rprog] ADD  CONSTRAINT [DF__rprog__odrezerv__24E777C3]  DEFAULT ((0)) FOR [odrezerv]
;
ALTER TABLE [dbo].[rprog] ADD  CONSTRAINT [DF__rprog__cbanID__25DB9BFC]  DEFAULT ((0)) FOR [cbanID]
;
ALTER TABLE [dbo].[rprog] ADD  CONSTRAINT [DF__rprog__pohhot__26CFC035]  DEFAULT ('') FOR [pohhot]
;
ALTER TABLE [dbo].[rprog] ADD  CONSTRAINT [DF__rprog__pohfakt__27C3E46E]  DEFAULT ('') FOR [pohfakt]
;
ALTER TABLE [dbo].[rprog] ADD  CONSTRAINT [DF__rprog__pohzal__28B808A7]  DEFAULT ('') FOR [pohzal]
;
ALTER TABLE [dbo].[rprog] ADD  CONSTRAINT [DF__rprog__delegace__29AC2CE0]  DEFAULT ((0)) FOR [delegace]
;
ALTER TABLE [dbo].[rprog] ADD  CONSTRAINT [DF_rprog_casprod_1]  DEFAULT ((0)) FOR [casprod]
;
ALTER TABLE [dbo].[rprog] ADD  CONSTRAINT [DF__rprog__zuziv__2AA05119]  DEFAULT ('') FOR [zuziv]
;
ALTER TABLE [dbo].[rprog] ADD  CONSTRAINT [DF__rprog__zcas__2B947552]  DEFAULT (getdate()) FOR [zcas]
;
ALTER TABLE [dbo].[rprog] ADD  DEFAULT ((0)) FOR [neikar]
;
ALTER TABLE [dbo].[rprog] ADD  DEFAULT ((0)) FOR [rpoukazyID]
;
ALTER TABLE [dbo].[rprog] ADD  DEFAULT ('00') FOR [cistyp]
;
ALTER TABLE [dbo].[rprog] ADD  DEFAULT ('') FOR [rcsluzby]
;
ALTER TABLE [dbo].[rprog] ADD  DEFAULT ((0)) FOR [sluzbyDbox]
;
ALTER TABLE [dbo].[rprog] ADD  DEFAULT ((0)) FOR [sluzbyVIP]
;
ALTER TABLE [dbo].[rprog] ADD  DEFAULT ((0)) FOR [priplatekVIP]
;
ALTER TABLE [dbo].[rprog] ADD  DEFAULT ((0)) FOR [sluzbyPremium]
;
ALTER TABLE [dbo].[rprog] ADD  DEFAULT ((0)) FOR [priplatekPremium]
;
ALTER TABLE [dbo].[rprog] ADD  DEFAULT ((0)) FOR [sluzbyImax]
;
ALTER TABLE [dbo].[rprog] ADD  DEFAULT ((0)) FOR [priplatekImax]
;
ALTER TABLE [dbo].[rprog] ADD  DEFAULT ('') FOR [cistyp2]
;
ALTER TABLE [dbo].[rprog] ADD  DEFAULT ('') FOR [cistyp3]
;
ALTER TABLE [dbo].[rprog] ADD  DEFAULT ('') FOR [cistyp4]
;
ALTER TABLE [dbo].[rprog] ADD  DEFAULT ('') FOR [vs]
;
ALTER TABLE [dbo].[rprog] ADD  DEFAULT ('') FOR [jazyk]
;
ALTER TABLE [dbo].[rprog] ADD  DEFAULT ('') FOR [cfotrm]
;
ALTER TABLE [dbo].[rprog] ADD  DEFAULT ((0)) FOR [sluzbyUltraX]
;
ALTER TABLE [dbo].[rprog] ADD  DEFAULT ((0)) FOR [priplatekUltraX]
;

----------------------
--- init test data ---
----------------------
INSERT INTO rps_CinemaxE.dbo.rprog (rupravaID, rfilmID, rvstID, csalu, porad, distr, ciskop, limit, pujcovne, cenfix, cenmin, cenkat, osa, osaproc, fk, fkproc, sp, spproc, ps, priplsluz, negraficky, naweb, stop, odpadlo, neucast, divaku, trzba, rezervace, fondcel, priplcel, odrezerv, kdyodrezerv, cbanID, pohhot, pohfakt, pohzal, delegace, casprod, pozn, zuziv, zcas, neikar, rpoukazyID, cistyp, rcsluzby, sluzbyDbox, sluzbyVIP, priplatekVIP, sluzbyPremium, priplatekPremium, sluzbyImax, priplatekImax, cistyp2, cistyp3, cistyp4, vs, jazyk, cfotrm, sluzbyUltraX, priplatekUltraX) VALUES (1, 4387, 749, 522630, N'2023-07-21 16:00:00', 0, N'    ', 222, N'50.00', N'0.0000', N'0.0000', N'002', N'1.00', N'True', N'4.00', N'True', N'0.00', N'True', N'False', N'0.00', N'False', N'True', N'False', N'False', N'False', 0, N'0.00', 0, N'0.00', N'0.00', N'False', N'', 0, 21, 8, N'  ', 0, 30, N'', N'DISdata   ', N'2023-07-18 15:14:00', N'False', 0, '00', N'  ', N'1.00', N'0.00', N'0.00', N'0.00', N'0.00', N'2.00', N'3.00', N'   ', N'   ', N'   ', N'          ', N'    ', N'  ', N'0.00', N'0.00');
INSERT INTO rps_CinemaxE.dbo.rprog (rupravaID, rfilmID, rvstID, csalu, porad, distr, ciskop, limit, pujcovne, cenfix, cenmin, cenkat, osa, osaproc, fk, fkproc, sp, spproc, ps, priplsluz, negraficky, naweb, stop, odpadlo, neucast, divaku, trzba, rezervace, fondcel, priplcel, odrezerv, kdyodrezerv, cbanID, pohhot, pohfakt, pohzal, delegace, casprod, pozn, zuziv, zcas, neikar, rpoukazyID, cistyp, rcsluzby, sluzbyDbox, sluzbyVIP, priplatekVIP, sluzbyPremium, priplatekPremium, sluzbyImax, priplatekImax, cistyp2, cistyp3, cistyp4, vs, jazyk, cfotrm, sluzbyUltraX, priplatekUltraX) VALUES (3, 4628, 761, 522632, N'2023-07-19 16:00:00', 5, N'    ', 88, N'60.00', N'0.0000', N'0.0000', N'761', N'2.00', N'True', N'5.00', N'True', N'0.00', N'True', N'True', N'4.00', N'False', N'False', N'False', N'False', N'False', 0, N'0.00', 0, N'0.00', N'0.00', N'False', N'', 0, 21, 8, N'  ', 0, 30, N'', N'DISdata   ', N'2023-07-19 16:16:00', N'False', 0, '05', N'  ', N'0.00', N'0.00', N'4.00', N'0.00', N'0.00', N'0.00', N'2.00', N'06   ', N'16   ', N'23   ', N'          ', N'ENG ', N'  ', N'0.00', N'0.00');
INSERT INTO rps_CinemaxE.dbo.rprog (rupravaID, rfilmID, rvstID, csalu, porad, distr, ciskop, limit, pujcovne, cenfix, cenmin, cenkat, osa, osaproc, fk, fkproc, sp, spproc, ps, priplsluz, negraficky, naweb, stop, odpadlo, neucast, divaku, trzba, rezervace, fondcel, priplcel, odrezerv, kdyodrezerv, cbanID, pohhot, pohfakt, pohzal, delegace, casprod, pozn, zuziv, zcas, neikar, rpoukazyID, cistyp, rcsluzby, sluzbyDbox, sluzbyVIP, priplatekVIP, sluzbyPremium, priplatekPremium, sluzbyImax, priplatekImax, cistyp2, cistyp3, cistyp4, vs, jazyk, cfotrm, sluzbyUltraX, priplatekUltraX) VALUES (5, 4628, 761, 522632, N'2023-07-20 16:00:00', 5, N'    ', 88, N'70.00', N'0.0000', N'0.0000', N'761', N'3.00', N'True', N'6.00', N'True', N'0.00', N'True', N'True', N'4.00', N'False', N'True', N'False', N'False', N'False', 0, N'0.00', 0, N'0.00', N'0.00', N'False', N'', 0, 21, 8, N'  ', 0, 1000, N'', N'DISdata   ', N'2023-07-19 16:17:00', N'False', 0, 0, N'  ', N'0.00', N'0.00', N'4.00', N'0.00', N'0.00', N'0.00', N'2.00', N'   ', N'   ', N'   ', N'          ', N'CZ ', N'01', N'0.00', N'0.00');
