-----------------------
--- init test table ---
-----------------------
SET ANSI_NULLS ON
;
SET QUOTED_IDENTIFIER ON
;
CREATE TABLE [dbo].[rvst](
	[rvstID] [smallint] IDENTITY(1,1) NOT NULL,
	[csalu] [char](6) NOT NULL,
	[distr] [nchar](4) NOT NULL,
	[kat] [char](3) NOT NULL,
	[nazev] [nchar](15) NOT NULL,
	[rkurzID1] [int] NOT NULL,
	[rkurzID2] [int] NOT NULL,
	[rkurzID3] [int] NOT NULL,
	[rkurzID4] [int] NOT NULL,
	[rkurzID5] [int] NOT NULL,
	[rkurzID6] [int] NOT NULL,
	[vst1] [smallmoney] NOT NULL,
	[nazev1] [varchar](15) NOT NULL,
	[vst2] [smallmoney] NOT NULL,
	[nazev2] [varchar](15) NOT NULL,
	[vst3] [smallmoney] NOT NULL,
	[nazev3] [varchar](15) NOT NULL,
	[vst4] [smallmoney] NOT NULL,
	[nazev4] [varchar](15) NOT NULL,
	[vst5] [smallmoney] NOT NULL,
	[nazev5] [varchar](15) NOT NULL,
	[vst6] [smallmoney] NOT NULL,
	[nazev6] [varchar](15) NOT NULL,
	[vst7] [smallmoney] NOT NULL,
	[nazev7] [varchar](15) NOT NULL,
	[vst8] [smallmoney] NOT NULL,
	[nazev8] [varchar](15) NOT NULL,
	[vst9] [smallmoney] NOT NULL,
	[nazev9] [varchar](15) NOT NULL,
	[vst10] [smallmoney] NOT NULL,
	[nazev10] [varchar](15) NOT NULL,
	[vst11] [smallmoney] NOT NULL,
	[nazev11] [varchar](15) NOT NULL,
	[vst12] [smallmoney] NOT NULL,
	[nazev12] [varchar](15) NOT NULL,
	[vst13] [smallmoney] NOT NULL,
	[nazev13] [varchar](15) NOT NULL,
	[vst14] [smallmoney] NOT NULL,
	[nazev14] [varchar](15) NOT NULL,
	[vst15] [smallmoney] NOT NULL,
	[nazev15] [varchar](15) NOT NULL,
	[vst16] [smallmoney] NOT NULL,
	[nazev16] [varchar](15) NOT NULL,
	[vst17] [smallmoney] NOT NULL,
	[nazev17] [varchar](15) NOT NULL,
	[vst18] [smallmoney] NOT NULL,
	[nazev18] [varchar](15) NOT NULL,
	[vst19] [smallmoney] NOT NULL,
	[nazev19] [varchar](15) NOT NULL,
	[vst20] [smallmoney] NOT NULL,
	[nazev20] [varchar](15) NOT NULL,
	[zuziv] [char](10) NOT NULL,
	[zcas] [smalldatetime] NULL,
	[kategorie] [nchar](3) NOT NULL,
	[aktivni] [bit] NOT NULL,
	[sleva2] [char](2) NOT NULL,
	[sleva3] [char](2) NOT NULL,
	[sleva4] [char](2) NOT NULL,
	[sleva19] [char](2) NOT NULL,
	[sleva18] [char](2) NOT NULL,
	[sleva17] [char](2) NOT NULL,
 CONSTRAINT [PK__rvst__37703C52] PRIMARY KEY CLUSTERED
(
	[rvstID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
;
ALTER TABLE [dbo].[rvst] ADD  CONSTRAINT [DF__rvst__csalu__1E6F845E]  DEFAULT ('') FOR [csalu]
;
ALTER TABLE [dbo].[rvst] ADD  CONSTRAINT [DF__rvst__nazev__1F63A897]  DEFAULT ('') FOR [nazev]
;
ALTER TABLE [dbo].[rvst] ADD  CONSTRAINT [DF_rvst_rkurzID1]  DEFAULT ((1)) FOR [rkurzID1]
;
ALTER TABLE [dbo].[rvst] ADD  CONSTRAINT [DF_rvst_rkurzID2_1]  DEFAULT ((1)) FOR [rkurzID2]
;
ALTER TABLE [dbo].[rvst] ADD  CONSTRAINT [DF_rvst_rkurzID3_1]  DEFAULT ((1)) FOR [rkurzID3]
;
ALTER TABLE [dbo].[rvst] ADD  CONSTRAINT [DF_rvst_rkurzID4_1]  DEFAULT ((1)) FOR [rkurzID4]
;
ALTER TABLE [dbo].[rvst] ADD  CONSTRAINT [DF_rvst_rkurzID5_1]  DEFAULT ((1)) FOR [rkurzID5]
;
ALTER TABLE [dbo].[rvst] ADD  CONSTRAINT [DF_rvst_rkurzID6_1]  DEFAULT ((1)) FOR [rkurzID6]
;
ALTER TABLE [dbo].[rvst] ADD  CONSTRAINT [DF__rvst__vst1__2057CCD0]  DEFAULT ((0)) FOR [vst1]
;
ALTER TABLE [dbo].[rvst] ADD  CONSTRAINT [DF__rvst__nazev1__214BF109]  DEFAULT ('') FOR [nazev1]
;
ALTER TABLE [dbo].[rvst] ADD  CONSTRAINT [DF__rvst__vst2__22401542]  DEFAULT ((0)) FOR [vst2]
;
ALTER TABLE [dbo].[rvst] ADD  CONSTRAINT [DF__rvst__nazev2__2334397B]  DEFAULT ('') FOR [nazev2]
;
ALTER TABLE [dbo].[rvst] ADD  CONSTRAINT [DF__rvst__vst3__24285DB4]  DEFAULT ((0)) FOR [vst3]
;
ALTER TABLE [dbo].[rvst] ADD  CONSTRAINT [DF__rvst__nazev3__251C81ED]  DEFAULT ('') FOR [nazev3]
;
ALTER TABLE [dbo].[rvst] ADD  CONSTRAINT [DF__rvst__vst4__2610A626]  DEFAULT ((0)) FOR [vst4]
;
ALTER TABLE [dbo].[rvst] ADD  CONSTRAINT [DF__rvst__nazev4__2704CA5F]  DEFAULT ('') FOR [nazev4]
;
ALTER TABLE [dbo].[rvst] ADD  CONSTRAINT [DF__rvst__vst5__27F8EE98]  DEFAULT ((0)) FOR [vst5]
;
ALTER TABLE [dbo].[rvst] ADD  CONSTRAINT [DF__rvst__nazev5__28ED12D1]  DEFAULT ('') FOR [nazev5]
;
ALTER TABLE [dbo].[rvst] ADD  CONSTRAINT [DF__rvst__vst6__29E1370A]  DEFAULT ((0)) FOR [vst6]
;
ALTER TABLE [dbo].[rvst] ADD  CONSTRAINT [DF__rvst__nazev6__2AD55B43]  DEFAULT ('') FOR [nazev6]
;
ALTER TABLE [dbo].[rvst] ADD  CONSTRAINT [DF__rvst__vst7__2BC97F7C]  DEFAULT ((0)) FOR [vst7]
;
ALTER TABLE [dbo].[rvst] ADD  CONSTRAINT [DF__rvst__nazev7__2CBDA3B5]  DEFAULT ('') FOR [nazev7]
;
ALTER TABLE [dbo].[rvst] ADD  CONSTRAINT [DF__rvst__vst8__2DB1C7EE]  DEFAULT ((0)) FOR [vst8]
;
ALTER TABLE [dbo].[rvst] ADD  CONSTRAINT [DF__rvst__nazev8__2EA5EC27]  DEFAULT ('') FOR [nazev8]
;
ALTER TABLE [dbo].[rvst] ADD  CONSTRAINT [DF__rvst__vst9__2F9A1060]  DEFAULT ((0)) FOR [vst9]
;
ALTER TABLE [dbo].[rvst] ADD  CONSTRAINT [DF__rvst__nazev9__308E3499]  DEFAULT ('') FOR [nazev9]
;
ALTER TABLE [dbo].[rvst] ADD  CONSTRAINT [DF__rvst__vst10__318258D2]  DEFAULT ((0)) FOR [vst10]
;
ALTER TABLE [dbo].[rvst] ADD  CONSTRAINT [DF__rvst__nazev10__32767D0B]  DEFAULT ('') FOR [nazev10]
;
ALTER TABLE [dbo].[rvst] ADD  CONSTRAINT [DF__rvst__vst11__336AA144]  DEFAULT ((0)) FOR [vst11]
;
ALTER TABLE [dbo].[rvst] ADD  CONSTRAINT [DF__rvst__nazev11__345EC57D]  DEFAULT ('') FOR [nazev11]
;
ALTER TABLE [dbo].[rvst] ADD  CONSTRAINT [DF__rvst__vst12__3552E9B6]  DEFAULT ((0)) FOR [vst12]
;
ALTER TABLE [dbo].[rvst] ADD  CONSTRAINT [DF__rvst__nazev12__36470DEF]  DEFAULT ('') FOR [nazev12]
;
ALTER TABLE [dbo].[rvst] ADD  CONSTRAINT [DF__rvst__vst13__373B3228]  DEFAULT ((0)) FOR [vst13]
;
ALTER TABLE [dbo].[rvst] ADD  CONSTRAINT [DF__rvst__nazev13__382F5661]  DEFAULT ('') FOR [nazev13]
;
ALTER TABLE [dbo].[rvst] ADD  CONSTRAINT [DF__rvst__vst14__39237A9A]  DEFAULT ((0)) FOR [vst14]
;
ALTER TABLE [dbo].[rvst] ADD  CONSTRAINT [DF__rvst__nazev14__3A179ED3]  DEFAULT ('') FOR [nazev14]
;
ALTER TABLE [dbo].[rvst] ADD  CONSTRAINT [DF__rvst__vst15__3B0BC30C]  DEFAULT ((0)) FOR [vst15]
;
ALTER TABLE [dbo].[rvst] ADD  CONSTRAINT [DF__rvst__nazev15__3BFFE745]  DEFAULT ('') FOR [nazev15]
;
ALTER TABLE [dbo].[rvst] ADD  CONSTRAINT [DF__rvst__vst16__3CF40B7E]  DEFAULT ((0)) FOR [vst16]
;
ALTER TABLE [dbo].[rvst] ADD  CONSTRAINT [DF__rvst__nazev16__3DE82FB7]  DEFAULT ('') FOR [nazev16]
;
ALTER TABLE [dbo].[rvst] ADD  CONSTRAINT [DF__rvst__vst17__3EDC53F0]  DEFAULT ((0)) FOR [vst17]
;
ALTER TABLE [dbo].[rvst] ADD  CONSTRAINT [DF__rvst__nazev17__3FD07829]  DEFAULT ('') FOR [nazev17]
;
ALTER TABLE [dbo].[rvst] ADD  CONSTRAINT [DF__rvst__vst18__40C49C62]  DEFAULT ((0)) FOR [vst18]
;
ALTER TABLE [dbo].[rvst] ADD  CONSTRAINT [DF__rvst__nazev18__41B8C09B]  DEFAULT ('') FOR [nazev18]
;
ALTER TABLE [dbo].[rvst] ADD  CONSTRAINT [DF__rvst__vst19__42ACE4D4]  DEFAULT ((0)) FOR [vst19]
;
ALTER TABLE [dbo].[rvst] ADD  CONSTRAINT [DF__rvst__nazev19__43A1090D]  DEFAULT ('') FOR [nazev19]
;
ALTER TABLE [dbo].[rvst] ADD  CONSTRAINT [DF__rvst__vst20__44952D46]  DEFAULT ((0)) FOR [vst20]
;
ALTER TABLE [dbo].[rvst] ADD  CONSTRAINT [DF__rvst__nazev20__4589517F]  DEFAULT ('') FOR [nazev20]
;
ALTER TABLE [dbo].[rvst] ADD  CONSTRAINT [DF__rvst__zuziv__467D75B8]  DEFAULT ('') FOR [zuziv]
;
ALTER TABLE [dbo].[rvst] ADD  CONSTRAINT [DF__rvst__zcas__477199F1]  DEFAULT (getdate()) FOR [zcas]
;
ALTER TABLE [dbo].[rvst] ADD  CONSTRAINT [DF__rvst__kategorie__4865BE2A]  DEFAULT ('') FOR [kategorie]
;
ALTER TABLE [dbo].[rvst] ADD  DEFAULT ((1)) FOR [aktivni]
;
ALTER TABLE [dbo].[rvst] ADD  DEFAULT ('') FOR [sleva2]
;
ALTER TABLE [dbo].[rvst] ADD  DEFAULT ('') FOR [sleva3]
;
ALTER TABLE [dbo].[rvst] ADD  DEFAULT ('') FOR [sleva4]
;
ALTER TABLE [dbo].[rvst] ADD  DEFAULT ('') FOR [sleva19]
;
ALTER TABLE [dbo].[rvst] ADD  DEFAULT ('') FOR [sleva18]
;
ALTER TABLE [dbo].[rvst] ADD  DEFAULT ('') FOR [sleva17]
;

----------------------
--- init test data ---
----------------------

INSERT INTO rps_CinemaxE.dbo.rvst (csalu, distr, kat, nazev, rkurzID1, rkurzID2, rkurzID3, rkurzID4, rkurzID5, rkurzID6, vst1, nazev1, vst2, nazev2, vst3, nazev3, vst4, nazev4, vst5, nazev5, vst6, nazev6, vst7, nazev7, vst8, nazev8, vst9, nazev9, vst10, nazev10, vst11, nazev11, vst12, nazev12, vst13, nazev13, vst14, nazev14, vst15, nazev15, vst16, nazev16, vst17, nazev17, vst18, nazev18, vst19, nazev19, vst20, nazev20, zuziv, zcas, kategorie, aktivni, sleva2, sleva3, sleva4, sleva19, sleva18, sleva17) VALUES (N'      ', N'04  ', N'I  ', N'skola 3€       ', 1, 1, 1, 1, 1, 1, 3.0000, N'3              ', 3.0000, N'', 3.0000, N'', 3.0000, N'', 3.0000, N'', 3.0000, N'', 0.0000, N'', 0.0000, N'', 0.0000, N'', 0.0000, N'', 0.0000, N'', 0.0000, N'', 0.0000, N'', 0.0000, N'', 0.0000, N'', 0.0000, N'', 0.0000, N'', 0.0000, N'', 0.0000, N'', 0.0000, N'', N'tibor     ', N'2016-04-22 15:48:00', N'   ', 1, N'  ', N'  ', N'  ', N'  ', N'  ', N'  ');
INSERT INTO rps_CinemaxE.dbo.rvst (csalu, distr, kat, nazev, rkurzID1, rkurzID2, rkurzID3, rkurzID4, rkurzID5, rkurzID6, vst1, nazev1, vst2, nazev2, vst3, nazev3, vst4, nazev4, vst5, nazev5, vst6, nazev6, vst7, nazev7, vst8, nazev8, vst9, nazev9, vst10, nazev10, vst11, nazev11, vst12, nazev12, vst13, nazev13, vst14, nazev14, vst15, nazev15, vst16, nazev16, vst17, nazev17, vst18, nazev18, vst19, nazev19, vst20, nazev20, zuziv, zcas, kategorie, aktivni, sleva2, sleva3, sleva4, sleva19, sleva18, sleva17) VALUES (N'      ', N'59  ', N'T  ', N'               ', 1, 1, 1, 1, 1, 1, 9.9500, N'9,95           ', 9.9500, N'9,95           ', 9.9500, N'9,95           ', 9.9500, N'9,95           ', 9.9500, N'9,95           ', 9.9500, N'9,95           ', 0.0000, N'', 0.0000, N'', 0.0000, N'', 0.0000, N'', 0.0000, N'', 0.0000, N'', 0.0000, N'', 0.0000, N'', 0.0000, N'', 0.0000, N'', 9.9500, N'9,95           ', 9.9500, N'9,95           ', 9.9500, N'9,95           ', 0.0000, N'', N'maja      ', N'2016-05-23 17:58:00', N'   ', 1, N'W7  ', N'W7  ', N'W7 ', N'W7  ', N'W7  ', N'W7  ');
INSERT INTO rps_CinemaxE.dbo.rvst (csalu, distr, kat, nazev, rkurzID1, rkurzID2, rkurzID3, rkurzID4, rkurzID5, rkurzID6, vst1, nazev1, vst2, nazev2, vst3, nazev3, vst4, nazev4, vst5, nazev5, vst6, nazev6, vst7, nazev7, vst8, nazev8, vst9, nazev9, vst10, nazev10, vst11, nazev11, vst12, nazev12, vst13, nazev13, vst14, nazev14, vst15, nazev15, vst16, nazev16, vst17, nazev17, vst18, nazev18, vst19, nazev19, vst20, nazev20, zuziv, zcas, kategorie, aktivni, sleva2, sleva3, sleva4, sleva19, sleva18, sleva17) VALUES (N'      ', N'72  ', N'G  ', N'10,95€         ', 1, 1, 1, 1, 1, 1, 10.9500, N'', 10.9500, N'', 10.9500, N'', 10.9500, N'', 10.9500, N'', 10.9500, N'', 0.0000, N'', 0.0000, N'', 0.0000, N'', 0.0000, N'', 0.0000, N'', 0.0000, N'', 0.0000, N'', 0.0000, N'', 0.0000, N'', 0.0000, N'', 0.0000, N'', 0.0000, N'', 0.0000, N'', 0.0000, N'', N'lucia     ', N'2017-06-20 00:09:00', N'   ', 0, N'  ', N'W7  ', N'  ', N'  ', N'W7  ', N'  ');
INSERT INTO rps_CinemaxE.dbo.rvst (csalu, distr, kat, nazev, rkurzID1, rkurzID2, rkurzID3, rkurzID4, rkurzID5, rkurzID6, vst1, nazev1, vst2, nazev2, vst3, nazev3, vst4, nazev4, vst5, nazev5, vst6, nazev6, vst7, nazev7, vst8, nazev8, vst9, nazev9, vst10, nazev10, vst11, nazev11, vst12, nazev12, vst13, nazev13, vst14, nazev14, vst15, nazev15, vst16, nazev16, vst17, nazev17, vst18, nazev18, vst19, nazev19, vst20, nazev20, zuziv, zcas, kategorie, aktivni, sleva2, sleva3, sleva4, sleva19, sleva18, sleva17) VALUES (N'      ', N'72  ', N'G  ', N'0€         ', 1, 1, 1, 1, 1, 1, 0.0000, N'', 0.0000, N'', 0.0000, N'', 0.0000, N'', 0.0000, N'', 0.0000, N'', 0.0000, N'', 0.0000, N'', 0.0000, N'', 0.0000, N'', 0.0000, N'', 0.0000, N'', 0.0000, N'', 0.0000, N'', 0.0000, N'', 0.0000, N'', 0.0000, N'', 0.0000, N'', 0.0000, N'', 0.0000, N'', N'lucia     ', N'2017-06-20 00:09:00', N'   ', 1, N'  ', N'  ', N'  ', N'  ', N'  ', N'  ');
