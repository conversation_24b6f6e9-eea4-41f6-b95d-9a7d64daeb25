-----------------------
--- init test table ---
-----------------------
CREATE TABLE [dbo].[rkin](
    [rkinID] [int] IDENTITY(1,1) NOT NULL,
    [csalu] [char](6) NOT NULL,
    [nazev] [varchar](40) NOT NULL,
    [kapacita] [smallint] NOT NULL,
    [misto] [varchar](30) NOT NULL,
    [ulice] [varchar](40) NOT NULL,
    [posta] [varchar](40) NOT NULL,
    [csdk] [varchar](30) NOT NULL,
    [vedouci] [varchar](25) NOT NULL,
    [nazevp1] [varchar](20) NOT NULL,
    [nazevp2] [varchar](20) NOT NULL,
    [tech] [smalldatetime] NULL,
    [tel] [char](20) NOT NULL,
    [fax] [char](20) NOT NULL,
    [mob] [char](20) NOT NULL,
    [email] [varchar](50) NOT NULL,
    [banka] [varchar](30) NOT NULL,
    [cuctu] [char](20) NOT NULL,
    [ico] [char](15) NOT NULL,
    [dic] [char](15) NOT NULL,
    [letni] [bit] NOT NULL,
    [zuziv] [char](10) NOT NULL,
    [zcas] [smalldatetime] NULL,
    [ldan] [bit] NOT NULL,
    [pozn] [varchar](50) NOT NULL,
    PRIMARY KEY CLUSTERED
(
[rkinID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
    ) ON [PRIMARY]
;

ALTER TABLE [dbo].[rkin] ADD  CONSTRAINT [DF__rkin__kapacita__5070F446]  DEFAULT ((1)) FOR [kapacita]
;
ALTER TABLE [dbo].[rkin] ADD  CONSTRAINT [DF__rkin__misto__5165187F]  DEFAULT ('') FOR [misto]
;
ALTER TABLE [dbo].[rkin] ADD  CONSTRAINT [DF__rkin__ulice__52593CB8]  DEFAULT ('') FOR [ulice]
;
ALTER TABLE [dbo].[rkin] ADD  CONSTRAINT [DF__rkin__posta__534D60F1]  DEFAULT ('') FOR [posta]
;
ALTER TABLE [dbo].[rkin] ADD  CONSTRAINT [DF__rkin__csdk__5441852A]  DEFAULT ('') FOR [csdk]
;
ALTER TABLE [dbo].[rkin] ADD  CONSTRAINT [DF__rkin__vedouci__5535A963]  DEFAULT ('') FOR [vedouci]
;
ALTER TABLE [dbo].[rkin] ADD  CONSTRAINT [DF__rkin__nazevp1__5629CD9C]  DEFAULT ('') FOR [nazevp1]
;
ALTER TABLE [dbo].[rkin] ADD  CONSTRAINT [DF__rkin__nazevp2__571DF1D5]  DEFAULT ('') FOR [nazevp2]
;
ALTER TABLE [dbo].[rkin] ADD  CONSTRAINT [DF__rkin__tel__5812160E]  DEFAULT ('') FOR [tel]
;
ALTER TABLE [dbo].[rkin] ADD  CONSTRAINT [DF__rkin__fax__59063A47]  DEFAULT ('') FOR [fax]
;
ALTER TABLE [dbo].[rkin] ADD  CONSTRAINT [DF__rkin__mob__59FA5E80]  DEFAULT ('') FOR [mob]
;
ALTER TABLE [dbo].[rkin] ADD  CONSTRAINT [DF__rkin__email__5AEE82B9]  DEFAULT ('') FOR [email]
;
ALTER TABLE [dbo].[rkin] ADD  CONSTRAINT [DF__rkin__banka__5BE2A6F2]  DEFAULT ('') FOR [banka]
;
ALTER TABLE [dbo].[rkin] ADD  CONSTRAINT [DF__rkin__cuctu__5CD6CB2B]  DEFAULT ('') FOR [cuctu]
;
ALTER TABLE [dbo].[rkin] ADD  CONSTRAINT [DF__rkin__ico__5DCAEF64]  DEFAULT ('') FOR [ico]
;
ALTER TABLE [dbo].[rkin] ADD  CONSTRAINT [DF__rkin__dic__5EBF139D]  DEFAULT ('') FOR [dic]
;
ALTER TABLE [dbo].[rkin] ADD  CONSTRAINT [DF__rkin__letni__5FB337D6]  DEFAULT ((0)) FOR [letni]
;
ALTER TABLE [dbo].[rkin] ADD  CONSTRAINT [DF__rkin__zuziv__60A75C0F]  DEFAULT ('') FOR [zuziv]
;
ALTER TABLE [dbo].[rkin] ADD  CONSTRAINT [DF__rkin__ldan__619B8048]  DEFAULT ((0)) FOR [ldan]
;
ALTER TABLE [dbo].[rkin] ADD  CONSTRAINT [DF__rkin__pozn__628FA481]  DEFAULT ('') FOR [pozn]

----------------------
--- init test data ---
----------------------
INSERT INTO rps_CinemaxE.dbo.rkin (csalu, nazev, kapacita, misto, ulice, posta, csdk, vedouci, nazevp1, nazevp2, tech, tel, fax, mob, email, banka, cuctu, ico, dic, letni, zuziv, zcas, ldan, pozn) VALUES (522630, N'Sála A - Dunajská Streda                ', 222, N'Dunajská Streda               ', N'', N'', N'', N'', N'', N'', N'', N'                    ', N'                    ', N'                    ', N'', N'', N'                    ', N'               ', N'               ', N'False', N'DISdata   ', N'2007-10-10 20:59:00', N'False', N'');
INSERT INTO rps_CinemaxE.dbo.rkin (csalu, nazev, kapacita, misto, ulice, posta, csdk, vedouci, nazevp1, nazevp2, tech, tel, fax, mob, email, banka, cuctu, ico, dic, letni, zuziv, zcas, ldan, pozn) VALUES (522631, N'Sála B - Dunajská Streda                ', 150, N'Dunajská Streda               ', N'', N'', N'', N'', N'', N'', N'', N'                    ', N'                    ', N'                    ', N'', N'', N'                    ', N'               ', N'               ', N'False', N'DISdata   ', N'2007-10-10 21:00:00', N'False', N'');
INSERT INTO rps_CinemaxE.dbo.rkin (csalu, nazev, kapacita, misto, ulice, posta, csdk, vedouci, nazevp1, nazevp2, tech, tel, fax, mob, email, banka, cuctu, ico, dic, letni, zuziv, zcas, ldan, pozn) VALUES (510360, N'Sála A - CINEMAX BRATISLAVA             ', 88, N'Bratislava                    ', N'', N'', N'', N'', N'', N'', N'', N'                    ', N'                    ', N'                    ', N'', N'', N'                    ', N'               ', N'               ', N'False', N'DISdata   ', N'2007-10-10 21:01:00', N'False', N'');
