------------------------
--- init test table ---
------------------------
/****** Object:  Table [dbo].[rdistr]    Script Date: 28.06.2023 10:01:15 ******/
SET ANSI_NULLS ON
;
SET QUOTED_IDENTIFIER ON
;
CREATE TABLE [dbo].[rdistr](
    [rdistrID] [int] IDENTITY(1,1) NOT NULL,
    [kod] [char](2) NOT NULL,
    [distr] [char](4) NOT NULL,
    [nazevd] [varchar](50) NOT NULL,
    [limit] [smallint] NOT NULL,
    [provize] [numeric](4, 2) NOT NULL,
    [ulice] [varchar](50) NOT NULL,
    [misto] [varchar](50) NOT NULL,
    [psc] [char](6) NOT NULL,
    [telefon1] [char](15) NOT NULL,
    [telefon2] [char](15) NOT NULL,
    [telefon3] [char](15) NOT NULL,
    [fax] [char](15) NOT NULL,
    [ucet] [char](15) NOT NULL,
    [banka] [varchar](50) NOT NULL,
    [ico] [char](10) NOT NULL,
    [dic] [char](12) NOT NULL,
    [dph] [tinyint] NOT NULL,
    [jmeno1] [char](20) NOT NULL,
    [jmeno2] [char](20) NOT NULL,
    [jmeno3] [char](20) NOT NULL,
    [znak] [char](12) NOT NULL,
    [mail] [varchar](220) NOT NULL,
    [zuziv] [char](10) NOT NULL,
    [zcas] [smalldatetime] NULL,
    [fix] [int] NOT NULL,
    [ldan] [bit] NOT NULL,
    [pozn] [text] NULL,
    [vipcena] [numeric](6, 2) NOT NULL,
    CONSTRAINT [PK__rdistr__398D8EEE] PRIMARY KEY CLUSTERED
(
[rdistrID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
    ) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
;

----------------------
--- init test data ---
----------------------
INSERT INTO rps_CinemaxE.dbo.rdistr (kod,distr,nazevd,[limit],provize,ulice,misto,psc,telefon1,telefon2,telefon3,fax,ucet,banka,ico,dic,dph,jmeno1,jmeno2,jmeno3,znak,mail,zuziv,zcas,fix,ldan,pozn,vipcena) VALUES (N'01',N'SS  ',N'Vertigo Distribution s.r.o.                       ',0,50.00,N'Ul. gen.Klapku 68/43                              ',N'Komárno                                           ',N'945 01',N'+************  ',N'+************  ',N'+************  ',N'               ',N'**********/1111',N'UniCredit Bank Czech Republic and Slovakia, a.s.  ',N'********  ',N'**********  ',5,N'Tomáš Ficza         ',N'Peter Ficza         ',N'Jan Ficza         ',N'            ',N'<EMAIL>; <EMAIL>                                                                                                                                                                     ',N'Manager   ','2019-05-03 16:37:00.000',0,0,N'marketingová akcia -  event Spievankovo',0.00);
INSERT INTO rps_CinemaxE.dbo.rdistr (kod,distr,nazevd,[limit],provize,ulice,misto,psc,telefon1,telefon2,telefon3,fax,ucet,banka,ico,dic,dph,jmeno1,jmeno2,jmeno3,znak,mail,zuziv,zcas,fix,ldan,pozn,vipcena) VALUES (N'01',N'2G  ',N'IMAX Film Holding Co.                             ',0,0.00,N'12582 West Millennium Drive                       ',N'CA, Los Angeles                                   ',N'900 94',N'**********     ',N'               ',N'               ',N'               ',N'*********      ',N'WELLS FARGO BANK, N.A.                            ',N'          ',N'            ',0,N'David King          ',N'                    ',N'                    ',N'            ',N'<EMAIL> ; <EMAIL> ;<EMAIL> ; <EMAIL>                                                                                                                                         ',N'kramar    ','2019-05-04 16:37:00.000',0,0,N'',0.00);
INSERT INTO rps_CinemaxE.dbo.rdistr (kod,distr,nazevd,[limit],provize,ulice,misto,psc,telefon1,telefon2,telefon3,fax,ucet,banka,ico,dic,dph,jmeno1,jmeno2,jmeno3,znak,mail,zuziv,zcas,fix,ldan,pozn,vipcena) VALUES (N'01',N'13  ',N'SATURN ENTERTAINMENT, spol. s r. o.               ',0,0.00,N'                                      ',N'                                        ',N'',N'  ',N'               ',N'               ',N'               ',N' ',N'                      ',N'  ',N'',0,N' ',N'                    ',N'                    ',N'            ',N'                                                                                                                                                            ',N'    ','2019-05-05 16:37:00.000',0,1,NULL,0.00);


