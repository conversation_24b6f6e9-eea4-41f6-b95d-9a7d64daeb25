------------------------
--- init test tables ---
------------------------
/****** Object:  Table [dbo].[rslev]    Script Date: 28.06.2023 10:01:15 ******/
SET ANSI_NULLS ON
;
SET QUOTED_IDENTIFIER ON
;
CREATE TABLE [dbo].[rslev](
	[rslevID] [int] IDENTITY(1,1) NOT NULL,
	[cislo] [char](8) NOT NULL,
	[sleva] [numeric](7, 2) NOT NULL,
	[lproc] [bit] NOT NULL,
	[prijmeni] [char](20) NOT NULL,
	[jmeno] [char](10) NOT NULL,
	[adrmis] [char](20) NOT NULL,
	[adrul] [char](20) NOT NULL,
	[psc] [char](6) NOT NULL,
	[telef] [char](15) NOT NULL,
	[zuziv] [char](10) NOT NULL,
	[zcas] [smalldatetime] NULL,
	[lpcena] [bit] NOT NULL,
	[lnetisk] [bit] NOT NULL,
	[pocslev] [int] NOT NULL,
	[lnest] [bit] NOT NULL,
	[sleva2] [char](8) NOT NULL,
	[aktivni] [bit] NOT NULL,
	[liver] [bit] NOT NULL,
	[liver_3d] [bit] NOT NULL,
	[liver_po] [bit] NOT NULL,
	[pc] [int] NOT NULL,
	[pocpo] [int] NOT NULL,
	[nulsluzby] [bit] NOT NULL,
	[jenvoucher] [bit] NOT NULL,
	[pocZaNula] [int] NOT NULL,
 CONSTRAINT [PK__rslev__74AE54BC] PRIMARY KEY CLUSTERED
(
	[rslevID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
;

ALTER TABLE [dbo].[rslev] ADD  CONSTRAINT [DF__rslev__sleva__07F6335A]  DEFAULT ((0)) FOR [sleva]
;
ALTER TABLE [dbo].[rslev] ADD  CONSTRAINT [DF__rslev__lproc__08EA5793]  DEFAULT ((0)) FOR [lproc]
;
ALTER TABLE [dbo].[rslev] ADD  CONSTRAINT [DF__rslev__prijmeni__09DE7BCC]  DEFAULT ('') FOR [prijmeni]
;
ALTER TABLE [dbo].[rslev] ADD  CONSTRAINT [DF__rslev__jmeno__0AD2A005]  DEFAULT ('') FOR [jmeno]
;
ALTER TABLE [dbo].[rslev] ADD  CONSTRAINT [DF__rslev__adrmis__0BC6C43E]  DEFAULT ('') FOR [adrmis]
;
ALTER TABLE [dbo].[rslev] ADD  CONSTRAINT [DF__rslev__adrul__0CBAE877]  DEFAULT ('') FOR [adrul]
;
ALTER TABLE [dbo].[rslev] ADD  CONSTRAINT [DF__rslev__psc__0DAF0CB0]  DEFAULT ('') FOR [psc]
;
ALTER TABLE [dbo].[rslev] ADD  CONSTRAINT [DF__rslev__telef__0EA330E9]  DEFAULT ('') FOR [telef]
;
ALTER TABLE [dbo].[rslev] ADD  CONSTRAINT [DF__rslev__zuziv__0F975522]  DEFAULT ('') FOR [zuziv]
;
ALTER TABLE [dbo].[rslev] ADD  CONSTRAINT [DF__rslev__zcas__108B795B]  DEFAULT (getdate()) FOR [zcas]
;
ALTER TABLE [dbo].[rslev] ADD  DEFAULT ((0)) FOR [lpcena]
;
ALTER TABLE [dbo].[rslev] ADD  DEFAULT ((0)) FOR [lnetisk]
;
ALTER TABLE [dbo].[rslev] ADD  DEFAULT ((0)) FOR [pocslev]
;
ALTER TABLE [dbo].[rslev] ADD  DEFAULT ((0)) FOR [lnest]
;
ALTER TABLE [dbo].[rslev] ADD  DEFAULT ('') FOR [sleva2]
;
ALTER TABLE [dbo].[rslev] ADD  DEFAULT ((1)) FOR [aktivni]
;
ALTER TABLE [dbo].[rslev] ADD  DEFAULT ((0)) FOR [liver]
;
ALTER TABLE [dbo].[rslev] ADD  DEFAULT ((0)) FOR [liver_3d]
;
ALTER TABLE [dbo].[rslev] ADD  DEFAULT ((0)) FOR [liver_po]
;
ALTER TABLE [dbo].[rslev] ADD  DEFAULT ((0)) FOR [pc]
;
ALTER TABLE [dbo].[rslev] ADD  DEFAULT ((0)) FOR [pocpo]
;
ALTER TABLE [dbo].[rslev] ADD  DEFAULT ((0)) FOR [nulsluzby]
;
ALTER TABLE [dbo].[rslev] ADD  DEFAULT ((0)) FOR [jenvoucher]
;
ALTER TABLE [dbo].[rslev] ADD  DEFAULT ((0)) FOR [pocZaNula]
;

----------------------
--- init test data ---
----------------------
INSERT INTO rps_CinemaxE.dbo.rslev (cislo, sleva, lproc, prijmeni, jmeno, adrmis, adrul, psc, telef, zuziv, zcas, lpcena, lnetisk, pocslev, lnest, sleva2, aktivni, liver, liver_3d, liver_po, pc, pocpo, nulsluzby, jenvoucher, pocZaNula) VALUES (N'2F      ', 100.00, 1, N'2xFree              ', N'          ', N'                    ', N'                    ', N'      ', N'               ', N'lucia     ', N'2017-08-06 19:55:00', 0, 0, 0, 1, N'        ', 1, 0, 0, 0, 0, 2, 1, 1, 2);
INSERT INTO rps_CinemaxE.dbo.rslev (cislo, sleva, lproc, prijmeni, jmeno, adrmis, adrul, psc, telef, zuziv, zcas, lpcena, lnetisk, pocslev, lnest, sleva2, aktivni, liver, liver_3d, liver_po, pc, pocpo, nulsluzby, jenvoucher, pocZaNula) VALUES (N'DW      ', 100.00, 1, N'                    ', N'          ', N'                    ', N'                    ', N'      ', N'               ', N'jano      ', N'2018-02-01 09:43:00', 0, 0, 0, 0, N'        ', 1, 0, 0, 0, 0, 0, 0, 1, 0);
INSERT INTO rps_CinemaxE.dbo.rslev (cislo, sleva, lproc, prijmeni, jmeno, adrmis, adrul, psc, telef, zuziv, zcas, lpcena, lnetisk, pocslev, lnest, sleva2, aktivni, liver, liver_3d, liver_po, pc, pocpo, nulsluzby, jenvoucher, pocZaNula) VALUES (N'23      ', 2.80, 0, N'Voucher             ', N'          ', N'                    ', N'                    ', N'      ', N'               ', N'DISdata   ', N'2019-05-29 13:33:00', 1, 1, 0, 0, N'        ', 1, 0, 0, 0, 3, 0, 0, 0, 0);
INSERT INTO rps_CinemaxE.dbo.rslev (cislo, sleva, lproc, prijmeni, jmeno, adrmis, adrul, psc, telef, zuziv, zcas, lpcena, lnetisk, pocslev, lnest, sleva2, aktivni, liver, liver_3d, liver_po, pc, pocpo, nulsluzby, jenvoucher, pocZaNula) VALUES (N'FP      ', 1.20, 0, N'FILM karta          ', N'          ', N'                    ', N'                    ', N'      ', N'               ', N'dzuro     ', N'2022-02-10 10:55:00', 0, 0, 0, 0, N'        ', 1, 0, 0, 0, 9, 0, 0, 0, 0);
INSERT INTO rps_CinemaxE.dbo.rslev (cislo, sleva, lproc, prijmeni, jmeno, adrmis, adrul, psc, telef, zuziv, zcas, lpcena, lnetisk, pocslev, lnest, sleva2, aktivni, liver, liver_3d, liver_po, pc, pocpo, nulsluzby, jenvoucher, pocZaNula) VALUES (N'B1      ', 1.00, 0, N'Billa 1             ', N'          ', N'                    ', N'                    ', N'      ', N'               ', N'dzuro     ', N'2023-01-31 14:46:00', 0, 0, 0, 1, N'        ', 0, 0, 0, 0, 0, 1, 0, 0, 0);
