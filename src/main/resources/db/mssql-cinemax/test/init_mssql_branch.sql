------------------------
--- init test table ---
------------------------
SET ANSI_NULLS ON
    ;
SET QUOTED_IDENTIFIER ON
    ;
CREATE TABLE [dbo].[rprovozy](
    rprovoz<PERSON><PERSON>   int identity primary key,
    nazev        varchar(40)             not null,
    csalu4       char(5)                 not null,
    provozSklad  smallint                not null,
    provozSklad2 smallint                not null,
    zkratka      char(3)      default '' not null,
    spoj         varchar(150) default '' not null,
    pc           char(2)      default '' not null,
    csaluDistr   char(6)      default '' not null,
    ncsal        tinyint      default 4  not null
);

----------------------
--- init test data ---
----------------------
INSERT INTO rps_CinemaxE.dbo.rprovozy (nazev, csalu4, provozSklad, provozSklad2, z<PERSON><PERSON>ka, spoj, pc, csaluDistr, ncsal) VALUES (N'CINEMAX DUNAJSKÁ STREDA', N'5226 ', 1, 11, N'DS ', N'DRIVER=SQL Server;SERVER=10.1.3.2;UID=sa;PWD=******;APP=RPS;WSID=DISdata # server;DATABASE=rps_CinemaxE', N'05', N'522901', 4);
INSERT INTO rps_CinemaxE.dbo.rprovozy (nazev, csalu4, provozSklad, provozSklad2, zkratka, spoj, pc, csaluDistr, ncsal) VALUES (N'CINEMAX KOŠICE', N'5430 ', 2, 10, N'KE ', N'DRIVER=SQL Server;SERVER=10.1.4.2;UID=sa;PWD=******;APP=RPS;WSID=DISdata # server;DATABASE=rps_CinemaxE', N'09', N'543029', 4);
INSERT INTO rps_CinemaxE.dbo.rprovozy (nazev, csalu4, provozSklad, provozSklad2, zkratka, spoj, pc, csaluDistr, ncsal) VALUES (N'CINEMAX NITRA', N'5237 ', 3, 12, N'NR ', N'DRIVER=SQL Server;SERVER=10.1.5.2;UID=sa;PWD=******;APP=RPS;WSID=DISdata # server;DATABASE=rps_CinemaxE', N'01', N'523905', 4);
-- invalid record
INSERT INTO rps_CinemaxE.dbo.rprovozy (nazev, csalu4, provozSklad, provozSklad2, zkratka, spoj, pc, csaluDistr, ncsal) VALUES (N'             ', N'5439 ', 4, 0, N'PP ', N'DRIVER=SQL Server;SERVER=10.1.6.2;UID=sa;PWD=******;APP=RPS;WSID=DISdata # server;DATABASE=rps_CinemaxE', N'04', N'543951', 4);
