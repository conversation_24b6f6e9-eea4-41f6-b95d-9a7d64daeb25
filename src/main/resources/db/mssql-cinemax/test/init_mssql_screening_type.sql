------------------------
--- init test table ---
------------------------
/****** Object:  Table [dbo].[ctyppr]    Script Date: 28.06.2023 10:01:15 ******/
SET ANSI_NULLS ON
;
SET QUOTED_IDENTIFIER ON
;
CREATE TABLE [dbo].[ctyppr](
    [ctypprID] [int] IDENTITY(1,1) NOT NULL,
    [cistyp] [char](3) NOT NULL,
    [nazev] [varchar](40) NOT NULL,
    [zuziv] [char](10) NOT NULL,
    [zcas] [smalldatetime] NULL,
    [priplatek] [numeric](6, 2) NOT NULL,
    [sluzby] [numeric](6, 2) NOT NULL,
    PRIMARY KEY CLUSTERED
(
[ctypprID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
    ) ON [PRIMARY]
;

----------------------
--- init test data ---
----------------------
INSERT INTO rps_CinemaxE.dbo.ctyppr (cistyp,nazev,zuziv,zcas,priplatek,sluzby) VALUES (N'08 ',N'Artmax special                          ',N'Manager   ','2019-05-03 16:37:00.000',0.00,0.00);
INSERT INTO rps_CinemaxE.dbo.ctyppr (cistyp,nazev,zuziv,zcas,priplatek,sluzby) VALUES (N'23 ',N'Detské filmy                            ',N'Manager   ','2019-05-04 16:37:00.000',0.00,0.00);
INSERT INTO rps_CinemaxE.dbo.ctyppr (cistyp,nazev,zuziv,zcas,priplatek,sluzby) VALUES (N'24 ',N'Ultra-X                                 ',N'DISdata   ','2019-05-05 16:37:00.000',0.00,0.00);
INSERT INTO rps_CinemaxE.dbo.ctyppr (cistyp,nazev,zuziv,zcas,priplatek,sluzby) VALUES (N'00 ',N'                                        ',N'kramar    ','2019-05-03 16:37:00.000',0.00,0.00);
