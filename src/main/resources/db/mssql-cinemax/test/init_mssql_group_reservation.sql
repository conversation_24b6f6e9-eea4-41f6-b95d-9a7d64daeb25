------------------------
--- init test tables ---
------------------------
SET ANSI_NULLS ON
;
SET QUOTED_IDENTIFIER ON
;
CREATE TABLE [dbo].[rezervace](
    [rezervaceID] [int] IDENTITY(1,1) NOT NULL,
    [rprogID] [int] NOT NULL,
    [datrez] [smalldatetime] NULL,
    [porad] [smalldatetime] NOT NULL,
    [dokdy] [smalldatetime] NULL,
    [jmeno] [varchar](20) NOT NULL,
    [popis] [varchar](50) NOT NULL,
    [pocet] [smallint] NOT NULL,
    [zuziv] [nchar](10) NOT NULL,
    [zcas] [smalldatetime] NULL,
    [okula] [numeric](6, 2) NOT NULL,
    PRIMARY KEY CLUSTERED
(
[rezervaceID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
    ) ON [PRIMARY]
;

ALTER TABLE [dbo].[rezervace] ADD  CONSTRAINT [DF__rezervace__jmeno__6319B466]  DEFAULT ('') FOR [jmeno]
;
ALTER TABLE [dbo].[rezervace] ADD  CONSTRAINT [DF__rezervace__popis__640DD89F]  DEFAULT ('') FOR [popis]
;
ALTER TABLE [dbo].[rezervace] ADD  CONSTRAINT [DF__rezervace__pocet__6501FCD8]  DEFAULT ((0)) FOR [pocet]
;
ALTER TABLE [dbo].[rezervace] ADD  CONSTRAINT [DF__rezervace__zuziv__65F62111]  DEFAULT ('') FOR [zuziv]
;
ALTER TABLE [dbo].[rezervace] ADD  CONSTRAINT [DF__rezervace__zcas__66EA454A]  DEFAULT (getdate()) FOR [zcas]
;
ALTER TABLE [dbo].[rezervace] ADD  DEFAULT ((0)) FOR [okula]
;

SET ANSI_NULLS ON
;
SET QUOTED_IDENTIFIER ON
;
CREATE TABLE [dbo].[rsedadla](
    [rsedadlaID] [int] NOT NULL,
    [rprogID] [int] NOT NULL,
    [rmustrID] [int] NOT NULL,
    [rezervaceID] [int] NOT NULL,
    [csalu] [char](6) NOT NULL,
    [porad] [smalldatetime] NULL,
    [ceny] [varchar](51) NOT NULL,
    [cenkat] [tinyint] NOT NULL,
    [delegace] [tinyint] NOT NULL,
    [delegold] [tinyint] NOT NULL,
    [datprod] [smalldatetime] NULL,
    [cena] [numeric](6, 2) NOT NULL,
    [uzivprod] [int] NOT NULL,
    [ruzivID] [int] NOT NULL,
    [zuziv] [char](10) NOT NULL,
    [zcas] [datetime] NULL,
    [sleva] [char](8) NOT NULL,
    PRIMARY KEY CLUSTERED
(
[rsedadlaID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
    ) ON [PRIMARY]
;

ALTER TABLE [dbo].[rsedadla] ADD  CONSTRAINT [DF__rsedadla__rezerv__719CDDE7]  DEFAULT ((0)) FOR [rezervaceID]
;
ALTER TABLE [dbo].[rsedadla] ADD  CONSTRAINT [DF__rsedadla__jmeno__72910220]  DEFAULT ('') FOR [ceny]
;
ALTER TABLE [dbo].[rsedadla] ADD  CONSTRAINT [DF__rsedadla__delego__73852659]  DEFAULT ((0)) FOR [delegold]
;
ALTER TABLE [dbo].[rsedadla] ADD  CONSTRAINT [DF__rsedadla__cena__74794A92]  DEFAULT ((0)) FOR [cena]
;
ALTER TABLE [dbo].[rsedadla] ADD  CONSTRAINT [DF__rsedadla__uzivpr__756D6ECB]  DEFAULT ((0)) FOR [uzivprod]
;
ALTER TABLE [dbo].[rsedadla] ADD  CONSTRAINT [DF__rsedadla__ruzivI__76619304]  DEFAULT ((0)) FOR [ruzivID]
;
ALTER TABLE [dbo].[rsedadla] ADD  CONSTRAINT [DF__rsedadla__zuziv__7755B73D]  DEFAULT ('') FOR [zuziv]
;
ALTER TABLE [dbo].[rsedadla] ADD  CONSTRAINT [DF__rsedadla__zcas__7849DB76]  DEFAULT (getdate()) FOR [zcas]
;
ALTER TABLE [dbo].[rsedadla] ADD  DEFAULT ('') FOR [sleva]
;


----------------------
--- init test data ---
----------------------
INSERT INTO rps_CinemaxE.dbo.rezervace (rprogID, datrez, porad, dokdy, jmeno, popis, pocet, zuziv, zcas, okula) VALUES (102825, N'2021-10-22 17:13:00', N'2021-11-02 19:00:00', null, N'Billa               ', N'                                                  ', 0, N'monika    ', N'2021-11-02 16:46:00', 0.00);
INSERT INTO rps_CinemaxE.dbo.rezervace (rprogID, datrez, porad, dokdy, jmeno, popis, pocet, zuziv, zcas, okula) VALUES (102826, N'2021-10-22 17:13:00', N'2021-11-02 19:00:00', N'2021-10-22 18:48:00', N'Internet', N'Internet: <EMAIL>', 0, N'Internet  ', N'2021-11-02 16:46:00', 1.50);
INSERT INTO rps_CinemaxE.dbo.rezervace (rprogID, datrez, porad, dokdy, jmeno, popis, pocet, zuziv, zcas, okula) VALUES (102825, N'2021-10-22 17:14:00', N'2021-11-02 19:00:00', null, N'Fitshaker           ', N'                                                  ', 0, N'monika    ', N'2021-11-02 16:48:00', 0.00);
INSERT INTO rps_CinemaxE.dbo.rezervace (rprogID, datrez, porad, dokdy, jmeno, popis, pocet, zuziv, zcas, okula) VALUES (102826, N'2021-10-22 17:15:00', N'2021-11-02 19:00:00', N'2021-10-22 19:55:00', N'Internet', N'Internet: <EMAIL>', 0, N'Internet  ', N'2021-11-02 16:49:00', 0.00);
-- invalid record - blank group reservation name
INSERT INTO rps_CinemaxE.dbo.rezervace (rprogID, datrez, porad, dokdy, jmeno, popis, pocet, zuziv, zcas, okula) VALUES (102827, N'2021-10-22 17:16:00', N'2021-11-02 19:00:00', null, N'                    ', N'Test                                              ', 0, N'monika    ', N'2021-11-02 16:48:00', 0.00);

INSERT INTO rps_CinemaxE.dbo.rsedadla (rsedadlaID, rprogID, rmustrID, rezervaceID, csalu, porad, ceny, cenkat, delegace, delegold, datprod, cena, uzivprod, ruzivID, zuziv, zcas, sleva) VALUES (1, 129361, 1676, 1, N'510070', N'2023-08-19 16:00:00', N'1,2,', 1, 12, 0, null, 0.00, 0, 0, N'monika    ', N'2023-06-05 14:54:12.570', N'        ');
INSERT INTO rps_CinemaxE.dbo.rsedadla (rsedadlaID, rprogID, rmustrID, rezervaceID, csalu, porad, ceny, cenkat, delegace, delegold, datprod, cena, uzivprod, ruzivID, zuziv, zcas, sleva) VALUES (2, 129361, 1676, 2, N'510070', N'2023-08-19 16:00:00', N'1,2,', 1, 12, 0, null, 0.00, 0, 0, N'monika    ', N'2023-06-05 14:54:12.570', N'        ');
INSERT INTO rps_CinemaxE.dbo.rsedadla (rsedadlaID, rprogID, rmustrID, rezervaceID, csalu, porad, ceny, cenkat, delegace, delegold, datprod, cena, uzivprod, ruzivID, zuziv, zcas, sleva) VALUES (3, 129361, 1676, 3, N'510070', N'2023-08-19 16:00:00', N'1,2,', 1, 12, 0, null, 0.00, 0, 0, N'monika    ', N'2023-06-05 14:54:12.570', N'        ');
INSERT INTO rps_CinemaxE.dbo.rsedadla (rsedadlaID, rprogID, rmustrID, rezervaceID, csalu, porad, ceny, cenkat, delegace, delegold, datprod, cena, uzivprod, ruzivID, zuziv, zcas, sleva) VALUES (4, 129361, 1676, 4, N'510070', N'2023-08-19 16:00:00', N'1,2,', 1, 12, 0, null, 0.00, 0, 0, N'monika    ', N'2023-06-05 14:54:12.570', N'        ');
