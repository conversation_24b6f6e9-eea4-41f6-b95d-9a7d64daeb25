------------------------
--- init test table ---
------------------------
/****** Object:  Table [dbo].[rlistky]    Script Date: 28.06.2023 10:01:15 ******/
SET ANSI_NULLS ON
;
SET QUOTED_IDENTIFIER ON
;
CREATE TABLE [dbo].[rlistky](
    [rlistkyID] [int] IDENTITY(1,1) NOT NULL,
    [rsedadlaID] [int] NOT NULL,
    [rprogID] [int] NOT NULL,
    [rpoklID] [int] NOT NULL,
    [rdokladID] [int] NOT NULL,
    [rodbID] [int] NOT NULL,
    [csalu] [char](6) NOT NULL,
    [sekce] [char](3) NOT NULL,
    [doklad] [char](10) NOT NULL,
    [cena] [numeric](6, 2) NOT NULL,
    [sluzby] [numeric](6, 2) NOT NULL,
    [fond] [numeric](4, 2) NOT NULL,
    [sp] [numeric](5, 2) NOT NULL,
    [sleva] [char](8) NOT NULL,
    [lvrat] [bit] NOT NULL,
    [lhoto] [bit] NOT NULL,
    [labo] [bit] NOT NULL,
    [datprod] [smalldatetime] NULL,
    [datpokl] [smalldatetime] NULL,
    [uzivprod] [int] NOT NULL,
    [uzivpokl] [int] NOT NULL,
    [pokladna] [char](10) NOT NULL,
    [rada] [char](5) NOT NULL,
    [misto] [char](5) NOT NULL,
    [pozn] [char](10) NOT NULL,
    [zuziv] [char](10) NOT NULL,
    [zcas] [smalldatetime] NULL,
    [vstup] [smallint] NOT NULL,
    [karta] [char](12) NULL,
    [rmustrID] [int] NOT NULL,
    [sluzbyDbox] [numeric](6, 2) NOT NULL,
    [okula] [numeric](6, 2) NOT NULL,
    [sluzbyVIP] [numeric](6, 2) NOT NULL,
    [priplatekVIP] [numeric](6, 2) NOT NULL,
    [sluzbyPremium] [numeric](6, 2) NOT NULL,
    [priplatekPremium] [numeric](6, 2) NOT NULL,
    [sluzbyImax] [numeric](6, 2) NOT NULL,
    [priplatekImax] [numeric](6, 2) NOT NULL,
    [vouncher] [char](10) NOT NULL,
    [hodslevy] [numeric](6, 2) NOT NULL,
    [cenkat] [tinyint] NOT NULL,
    [sluzbyUltraX] [numeric](6, 2) NOT NULL,
    [priplatekUltraX] [numeric](6, 2) NOT NULL,
    CONSTRAINT [PK__rlistky__267ABA7A] PRIMARY KEY CLUSTERED
(
[rlistkyID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
    ) ON [PRIMARY]
;
----------------------
--- init test data ---
----------------------
-- online tickets
-- single ticket in a basket, no discounts, no 3d glasses, no surcharges/fees
INSERT INTO rps_CinemaxE.dbo.rlistky (rsedadlaID, rprogID, rpoklID, rdokladID, rodbID, csalu, sekce, doklad, cena, sluzby, fond, sp, sleva, lvrat, lhoto, labo, datprod, datpokl, uzivprod, uzivpokl, pokladna, rada, misto, pozn, zuziv, zcas, vstup, karta, rmustrID, sluzbyDbox, okula, sluzbyVIP, priplatekVIP, sluzbyPremium, priplatekPremium, sluzbyImax, priplatekImax, vouncher, hodslevy, cenkat, sluzbyUltraX, priplatekUltraX) VALUES (18881594, 111860, 16790, 0, 0, N'510030', N'01 ', N'0002104035', 8.95, 0.00, 0.00, 0.00, N'        ', 0, 0, 0, N'2022-07-09 08:37:00', N'2022-07-09 22:48:00', 3, 3, N'Internet  ', N'11   ', N'12   ', N'4800427267', N'          ', N'2022-07-09 08:37:00', 1, N'            ', 967, 0.00, 0.00, 0.00, 0.00, 0.00, 0.00, 0.00, 0.00, N'          ', 0.00, 19, 0.00, 0.00);
-- two tickets in a basket, 3d glasses, no discounts, IMAX surcharge, unused tickets
INSERT INTO rps_CinemaxE.dbo.rlistky (rsedadlaID, rprogID, rpoklID, rdokladID, rodbID, csalu, sekce, doklad, cena, sluzby, fond, sp, sleva, lvrat, lhoto, labo, datprod, datpokl, uzivprod, uzivpokl, pokladna, rada, misto, pozn, zuziv, zcas, vstup, karta, rmustrID, sluzbyDbox, okula, sluzbyVIP, priplatekVIP, sluzbyPremium, priplatekPremium, sluzbyImax, priplatekImax, vouncher, hodslevy, cenkat, sluzbyUltraX, priplatekUltraX) VALUES (18887245, 111880, 16782, 0, 0, N'510000', N'01 ', N'0002103985', 12.95, 0.00, 0.00, 0.00, N'        ', 0, 0, 0, N'2022-07-09 14:41:00', N'2022-07-09 23:09:00', 3, 3, N'Internet  ', N'13   ', N'15   ', N'4800427249', N'          ', N'2022-07-09 14:41:00', 0, N'            ', 2533, 0.00, 1.50, 0.00, 0.00, 0.00, 0.00, 0.00, 4.00, N'          ', 0.00, 20, 0.00, 0.00);
INSERT INTO rps_CinemaxE.dbo.rlistky (rsedadlaID, rprogID, rpoklID, rdokladID, rodbID, csalu, sekce, doklad, cena, sluzby, fond, sp, sleva, lvrat, lhoto, labo, datprod, datpokl, uzivprod, uzivpokl, pokladna, rada, misto, pozn, zuziv, zcas, vstup, karta, rmustrID, sluzbyDbox, okula, sluzbyVIP, priplatekVIP, sluzbyPremium, priplatekPremium, sluzbyImax, priplatekImax, vouncher, hodslevy, cenkat, sluzbyUltraX, priplatekUltraX) VALUES (18887246, 111880, 16782, 0, 0, N'510000', N'01 ', N'0002103986', 12.95, 0.00, 0.00, 0.00, N'        ', 0, 0, 0, N'2022-07-09 14:41:00', N'2022-07-09 23:09:00', 3, 3, N'Internet  ', N'13   ', N'14   ', N'4800427249', N'          ', N'2022-07-09 14:41:00', 0, N'            ', 2534, 0.00, 1.50, 0.00, 0.00, 0.00, 0.00, 0.00, 4.00, N'          ', 0.00, 20, 0.00, 0.00);
-- single ticket in a basket, discount card with discount, no 3d glasses, no surcharges/fees
INSERT INTO rps_CinemaxE.dbo.rlistky (rsedadlaID, rprogID, rpoklID, rdokladID, rodbID, csalu, sekce, doklad, cena, sluzby, fond, sp, sleva, lvrat, lhoto, labo, datprod, datpokl, uzivprod, uzivpokl, pokladna, rada, misto, pozn, zuziv, zcas, vstup, karta, rmustrID, sluzbyDbox, okula, sluzbyVIP, priplatekVIP, sluzbyPremium, priplatekPremium, sluzbyImax, priplatekImax, vouncher, hodslevy, cenkat, sluzbyUltraX, priplatekUltraX) VALUES (19052397, 112948, 16782, 0, 0, N'510030', N'01 ', N'0002103041', 6.00, 0.00, 0.00, 0.00, N'FPW7    ', 0, 0, 0, N'2022-07-09 21:15:00', N'2022-07-09 23:09:00', 3, 3, N'Internet  ', N'5    ', N'6    ', N'4800427036', N'          ', N'2022-07-09 21:15:00', 1, N'67120097    ', 1948, 0.00, 0.00, 0.00, 0.00, 0.00, 0.00, 0.00, 0.00, N'          ', -3.20, 19, 0.00, 0.00);
-- two tickets in a basket, no 3d glasses, only isDiscounted, surcharges/fees - VIP
INSERT INTO rps_CinemaxE.dbo.rlistky (rsedadlaID, rprogID, rpoklID, rdokladID, rodbID, csalu, sekce, doklad, cena, sluzby, fond, sp, sleva, lvrat, lhoto, labo, datprod, datpokl, uzivprod, uzivpokl, pokladna, rada, misto, pozn, zuziv, zcas, vstup, karta, rmustrID, sluzbyDbox, okula, sluzbyVIP, priplatekVIP, sluzbyPremium, priplatekPremium, sluzbyImax, priplatekImax, vouncher, hodslevy, cenkat, sluzbyUltraX, priplatekUltraX) VALUES (18876432, 111837, 16782, 0, 0, N'520000', N'01 ', N'0002102493', 8.20, 6.00, 0.00, 0.00, N'W7      ', 0, 0, 0, N'2022-07-09 23:48:00', N'2022-07-10 23:09:00', 3, 3, N'Internet  ', N'13   ', N'5    ', N'4800426868', N'          ', N'2022-07-09 23:48:00', 1, N'            ', 1386, 0.00, 0.00, 5.00, 1.00, 0.00, 0.00, 0.00, 0.00, N'          ', 0.00, 19, 1.00, 0.00);
INSERT INTO rps_CinemaxE.dbo.rlistky (rsedadlaID, rprogID, rpoklID, rdokladID, rodbID, csalu, sekce, doklad, cena, sluzby, fond, sp, sleva, lvrat, lhoto, labo, datprod, datpokl, uzivprod, uzivpokl, pokladna, rada, misto, pozn, zuziv, zcas, vstup, karta, rmustrID, sluzbyDbox, okula, sluzbyVIP, priplatekVIP, sluzbyPremium, priplatekPremium, sluzbyImax, priplatekImax, vouncher, hodslevy, cenkat, sluzbyUltraX, priplatekUltraX) VALUES (18876433, 111837, 16782, 0, 0, N'520000', N'01 ', N'0002102494', 9.95, 6.00, 0.00, 0.00, N'        ', 0, 0, 0, N'2022-07-09 23:48:00', N'2022-07-10 23:09:00', 3, 3, N'Internet  ', N'13   ', N'6    ', N'4800426868', N'          ', N'2022-07-09 23:48:00', 1, N'            ', 1387, 0.00, 0.00, 5.00, 1.00, 0.00, 0.00, 0.00, 0.00, N'          ', 0.00, 20, 1.00, 0.00);
-- single ticket in a basket, no 3d glasses, no discount, surcharges/fees - PP + general
INSERT INTO rps_CinemaxE.dbo.rlistky (rsedadlaID, rprogID, rpoklID, rdokladID, rodbID, csalu, sekce, doklad, cena, sluzby, fond, sp, sleva, lvrat, lhoto, labo, datprod, datpokl, uzivprod, uzivpokl, pokladna, rada, misto, pozn, zuziv, zcas, vstup, karta, rmustrID, sluzbyDbox, okula, sluzbyVIP, priplatekVIP, sluzbyPremium, priplatekPremium, sluzbyImax, priplatekImax, vouncher, hodslevy, cenkat, sluzbyUltraX, priplatekUltraX) VALUES (17771717, 105100, 15879, 0, 0, N'520060', N'01 ', N'0001980804', 6.20, 4.80, 0.00, 0.00, N'        ', 0, 0, 0, N'2023-02-01 08:37:00', N'2023-02-01 23:59:00', 3, 3, N'Internet  ', N'8    ', N'4    ', N'4800393744', N'          ', N'2023-02-01 08:37:00', 1, N'            ', 1556, 0.00, 0.00, 0.00, 0.00, 1.00, 0.00, 0.00, 0.00, N'          ', 0.00, 20, 0.00, 0.00);

-- offline tickets
INSERT INTO rps_CinemaxE.dbo.rlistky (rsedadlaID, rprogID, rpoklID, rdokladID, rodbID, csalu, sekce, doklad, cena, sluzby, fond, sp, sleva, lvrat, lhoto, labo, datprod, datpokl, uzivprod, uzivpokl, pokladna, rada, misto, pozn, zuziv, zcas, vstup, karta, rmustrID, sluzbyDbox, okula, sluzbyVIP, priplatekVIP, sluzbyPremium, priplatekPremium, sluzbyImax, priplatekImax, vouncher, hodslevy, cenkat, sluzbyUltraX, priplatekUltraX) VALUES(17427776, 102900, 15624, 0, 0, N'510000', N'01 ', N'0001948986', 12.45, 0.00, 0.00, 0.00, N'        ', 0, 0, 0, '2023-11-01 09:01:00.000', '2023-11-02 00:26:00.000', 3, 3, N'pokl3     ', N'15   ', N'30   ', N'          ', N'          ', '2023-11-01 09:01:00.000', 0, N'            ', 448, 0.00, 0.00, 0.00, 0.00, 0.00, 0.00, 0.00, 4.00, N'          ', 0.00, 1, 0.00, 0.00);
INSERT INTO rps_CinemaxE.dbo.rlistky (rsedadlaID, rprogID, rpoklID, rdokladID, rodbID, csalu, sekce, doklad, cena, sluzby, fond, sp, sleva, lvrat, lhoto, labo, datprod, datpokl, uzivprod, uzivpokl, pokladna, rada, misto, pozn, zuziv, zcas, vstup, karta, rmustrID, sluzbyDbox, okula, sluzbyVIP, priplatekVIP, sluzbyPremium, priplatekPremium, sluzbyImax, priplatekImax, vouncher, hodslevy, cenkat, sluzbyUltraX, priplatekUltraX) VALUES(17458498, 103054, 15624, 0, 0, N'520000', N'01 ', N'0001948987', 8.45, 0.00, 0.00, 0.00, N'        ', 0, 1, 0, '2023-11-01 09:08:00.000', '2023-11-02 00:26:00.000', 3, 3, N'pokl1     ', N'7    ', N'8    ', N'          ', N'          ', '2023-11-01 09:08:00.000', 1, N'            ', 1755, 0.00, 0.00, 0.00, 0.00, 0.00, 0.00, 0.00, 0.00, N'          ', 0.00, 2, 0.00, 0.00);
INSERT INTO rps_CinemaxE.dbo.rlistky (rsedadlaID, rprogID, rpoklID, rdokladID, rodbID, csalu, sekce, doklad, cena, sluzby, fond, sp, sleva, lvrat, lhoto, labo, datprod, datpokl, uzivprod, uzivpokl, pokladna, rada, misto, pozn, zuziv, zcas, vstup, karta, rmustrID, sluzbyDbox, okula, sluzbyVIP, priplatekVIP, sluzbyPremium, priplatekPremium, sluzbyImax, priplatekImax, vouncher, hodslevy, cenkat, sluzbyUltraX, priplatekUltraX) VALUES(17458499, 103054, 15624, 0, 0, N'520000', N'01 ', N'0001948988', 8.45, 0.00, 0.00, 0.00, N'        ', 0, 1, 0, '2023-11-01 09:08:00.000', '2023-11-02 00:26:00.000', 3, 3, N'pokl1     ', N'7    ', N'7    ', N'          ', N'          ', '2023-11-01 09:08:00.000', 1, N'            ', 1756, 0.00, 0.00, 0.00, 0.00, 0.00, 0.00, 0.00, 0.00, N'          ', 0.00, 2, 0.00, 0.00);
