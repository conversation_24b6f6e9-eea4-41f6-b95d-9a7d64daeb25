------------------------
--- init test tables ---
------------------------
SET ANSI_NULLS ON
;
SET QUOTED_IDENTIFIER ON
;
CREATE TABLE [dbo].[rcards](
    [rcardsID] [int] IDENTITY(1,1) NOT NULL,
    [rodbID] [int] NOT NULL,
    [rlistIeID] [int] NOT NULL,
    [cislo] [char](10) NOT NULL,
    [typ] [char](1) NOT NULL,
    [platod] [smalldatetime] NULL,
    [platdo] [smalldatetime] NULL,
    [cena] [numeric](8, 2) NULL,
    [zuziv] [char](10) NOT NULL,
    [zcas] [smalldatetime] NULL,
    [lvipcena] [bit] NOT NULL,
    [KreditOd] [smalldatetime] NULL,
    [body] [int] NOT NULL,
    PRIMARY KEY CLUSTERED
(
[rcardsID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
    ) ON [PRIMARY]
;

ALTER TABLE [dbo].[rcards] ADD  DEFAULT ((0)) FOR [rodbID]
;
ALTER TABLE [dbo].[rcards] ADD  DEFAULT ((0)) FOR [rlistIeID]
;
ALTER TABLE [dbo].[rcards] ADD  DEFAULT ('') FOR [zuziv]
;
ALTER TABLE [dbo].[rcards] ADD  DEFAULT (getdate()) FOR [zcas]
;
ALTER TABLE [dbo].[rcards] ADD  DEFAULT ((0)) FOR [lvipcena]
;
ALTER TABLE [dbo].[rcards] ADD  DEFAULT ((0)) FOR [body]
;

----------------------
--- init test data ---
----------------------
INSERT INTO rps_CinemaxE.dbo.rcards (rodbID, rlistIeID, cislo, typ, platod, platdo, cena, zuziv, zcas, lvipcena, KreditOd, body) VALUES (8375, 5166, N'17517296  ', N'W', N'2019-10-20 00:00:00', N'2035-11-28 00:00:00', 199.00, N'adamko    ', N'2021-08-10 20:47:00', 1, N'2019-10-20 20:32:00', 10);
INSERT INTO rps_CinemaxE.dbo.rcards (rodbID, rlistIeID, cislo, typ, platod, platdo, cena, zuziv, zcas, lvipcena, KreditOd, body) VALUES (12564, 0, N'58113446  ', N'F', N'2018-01-15 00:00:00', N'2051-02-23 00:00:00', 0.00, N'monika    ', N'2022-01-13 20:37:00', 0, null, 0);
INSERT INTO rps_CinemaxE.dbo.rcards (rodbID, rlistIeID, cislo, typ, platod, platdo, cena, zuziv, zcas, lvipcena, KreditOd, body) VALUES (0, 0, N'96421780  ', N'H', N'2022-12-26 00:00:00', N'2035-12-25 00:00:00', 0.00, N'adamko    ', N'2023-08-10 20:47:00', 0, null, 0);
-- invalid card type
INSERT INTO rps_CinemaxE.dbo.rcards (rodbID, rlistIeID, cislo, typ, platod, platdo, cena, zuziv, zcas, lvipcena, KreditOd, body) VALUES (0, 0, N'96421781  ', N'C', N'2022-12-26 00:00:00', N'2035-12-25 00:00:00', 0.00, N'adamko    ', N'2023-08-10 20:47:00', 0, null, 0);
-- expired card
INSERT INTO rps_CinemaxE.dbo.rcards (rodbID, rlistIeID, cislo, typ, platod, platdo, cena, zuziv, zcas, lvipcena, KreditOd, body) VALUES (0, 0, N'96421781  ', N'C', N'2012-12-26 00:00:00', N'2022-12-25 00:00:00', 0.00, N'adamko    ', N'2023-08-10 20:47:00', 0, null, 0);
