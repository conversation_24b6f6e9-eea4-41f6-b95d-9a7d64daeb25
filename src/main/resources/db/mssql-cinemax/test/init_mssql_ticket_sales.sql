------------------------
--- init test tables ---
------------------------
/****** Object:  Table [dbo].[rlistky]    Script Date: 28.06.2023 10:01:15 ******/
SET ANSI_NULLS ON
;
SET QUOTED_IDENTIFIER ON
;
CREATE TABLE [dbo].[rlistky](
    [rlistkyID] [int] IDENTITY(1,1) NOT NULL,
    [rsedadlaID] [int] NOT NULL,
    [rprogID] [int] NOT NULL,
    [rpoklID] [int] NOT NULL,
    [rdokladID] [int] NOT NULL,
    [rodbID] [int] NOT NULL,
    [csalu] [char](6) NOT NULL,
    [sekce] [char](3) NOT NULL,
    [doklad] [char](10) NOT NULL,
    [cena] [numeric](6, 2) NOT NULL,
    [sluzby] [numeric](6, 2) NOT NULL,
    [fond] [numeric](4, 2) NOT NULL,
    [sp] [numeric](5, 2) NOT NULL,
    [sleva] [char](8) NOT NULL,
    [lvrat] [bit] NOT NULL,
    [lhoto] [bit] NOT NULL,
    [labo] [bit] NOT NULL,
    [datprod] [smalldatetime] NULL,
    [datpokl] [smalldatetime] NULL,
    [uzivprod] [int] NOT NULL,
    [uzivpokl] [int] NOT NULL,
    [pokladna] [char](10) NOT NULL,
    [rada] [char](5) NOT NULL,
    [misto] [char](5) NOT NULL,
    [pozn] [char](10) NOT NULL,
    [zuziv] [char](10) NOT NULL,
    [zcas] [smalldatetime] NULL,
    [vstup] [smallint] NOT NULL,
    [karta] [char](12) NULL,
    [rmustrID] [int] NOT NULL,
    [sluzbyDbox] [numeric](6, 2) NOT NULL,
    [okula] [numeric](6, 2) NOT NULL,
    [sluzbyVIP] [numeric](6, 2) NOT NULL,
    [priplatekVIP] [numeric](6, 2) NOT NULL,
    [sluzbyPremium] [numeric](6, 2) NOT NULL,
    [priplatekPremium] [numeric](6, 2) NOT NULL,
    [sluzbyImax] [numeric](6, 2) NOT NULL,
    [priplatekImax] [numeric](6, 2) NOT NULL,
    [vouncher] [char](10) NOT NULL,
    [hodslevy] [numeric](6, 2) NOT NULL,
    [cenkat] [tinyint] NOT NULL,
    [sluzbyUltraX] [numeric](6, 2) NOT NULL,
    [priplatekUltraX] [numeric](6, 2) NOT NULL,
    CONSTRAINT [PK__rlistky__267ABA7A] PRIMARY KEY CLUSTERED
(
[rlistkyID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
    ) ON [PRIMARY]
;

ALTER TABLE [dbo].[rlistky] ADD  CONSTRAINT [DF__rlistky__rpoklID__50FB042B]  DEFAULT ((0)) FOR [rpoklID]
;
ALTER TABLE [dbo].[rlistky] ADD  CONSTRAINT [DF_rlistky_rdokladID]  DEFAULT ((0)) FOR [rdokladID]
;
ALTER TABLE [dbo].[rlistky] ADD  CONSTRAINT [DF_rlistky_rodbID]  DEFAULT ((0)) FOR [rodbID]
;
ALTER TABLE [dbo].[rlistky] ADD  CONSTRAINT [DF_rlistky_sekce_1]  DEFAULT ('') FOR [sekce]
;
ALTER TABLE [dbo].[rlistky] ADD  CONSTRAINT [DF__rlistky__doklad__51EF2864]  DEFAULT ('') FOR [doklad]
;
ALTER TABLE [dbo].[rlistky] ADD  CONSTRAINT [DF__rlistky__sluzby__52E34C9D]  DEFAULT ((0)) FOR [sluzby]
;
ALTER TABLE [dbo].[rlistky] ADD  CONSTRAINT [DF__rlistky__fond__53D770D6]  DEFAULT ((0)) FOR [fond]
;
ALTER TABLE [dbo].[rlistky] ADD  CONSTRAINT [DF_rlistky_sp_1]  DEFAULT ((0)) FOR [sp]
;
ALTER TABLE [dbo].[rlistky] ADD  CONSTRAINT [DF__rlistky__sleva__54CB950F]  DEFAULT ('') FOR [sleva]
;
ALTER TABLE [dbo].[rlistky] ADD  CONSTRAINT [DF__rlistky__lvrat__55BFB948]  DEFAULT ((0)) FOR [lvrat]
;
ALTER TABLE [dbo].[rlistky] ADD  CONSTRAINT [DF__rlistky__lhoto__56B3DD81]  DEFAULT ((1)) FOR [lhoto]
;
ALTER TABLE [dbo].[rlistky] ADD  CONSTRAINT [DF_rlistky_labo]  DEFAULT ((0)) FOR [labo]
;
ALTER TABLE [dbo].[rlistky] ADD  CONSTRAINT [DF__rlistky__uzivpro__57A801BA]  DEFAULT ((0)) FOR [uzivprod]
;
ALTER TABLE [dbo].[rlistky] ADD  CONSTRAINT [DF__rlistky__uzivpok__589C25F3]  DEFAULT ((0)) FOR [uzivpokl]
;
ALTER TABLE [dbo].[rlistky] ADD  CONSTRAINT [DF__rlistky__pokladn__59904A2C]  DEFAULT ((0)) FOR [pokladna]
;
ALTER TABLE [dbo].[rlistky] ADD  CONSTRAINT [DF__rlistky__rada__5A846E65]  DEFAULT ('') FOR [rada]
;
ALTER TABLE [dbo].[rlistky] ADD  CONSTRAINT [DF__rlistky__misto__5B78929E]  DEFAULT ('') FOR [misto]
;
ALTER TABLE [dbo].[rlistky] ADD  CONSTRAINT [DF__rlistky__pozn__5C6CB6D7]  DEFAULT ('') FOR [pozn]
;
ALTER TABLE [dbo].[rlistky] ADD  CONSTRAINT [DF__rlistky__zuziv__5D60DB10]  DEFAULT ('') FOR [zuziv]
;
ALTER TABLE [dbo].[rlistky] ADD  CONSTRAINT [DF__rlistky__zcas__5E54FF49]  DEFAULT (getdate()) FOR [zcas]
;
ALTER TABLE [dbo].[rlistky] ADD  DEFAULT ((0)) FOR [vstup]
;
ALTER TABLE [dbo].[rlistky] ADD  DEFAULT ('') FOR [karta]
;
ALTER TABLE [dbo].[rlistky] ADD  DEFAULT ((0)) FOR [rmustrID]
;
ALTER TABLE [dbo].[rlistky] ADD  DEFAULT ((0)) FOR [sluzbyDbox]
;
ALTER TABLE [dbo].[rlistky] ADD  DEFAULT ((0)) FOR [okula]
;
ALTER TABLE [dbo].[rlistky] ADD  DEFAULT ((0)) FOR [sluzbyVIP]
;
ALTER TABLE [dbo].[rlistky] ADD  DEFAULT ((0)) FOR [priplatekVIP]
;
ALTER TABLE [dbo].[rlistky] ADD  DEFAULT ((0)) FOR [sluzbyPremium]
;
ALTER TABLE [dbo].[rlistky] ADD  DEFAULT ((0)) FOR [priplatekPremium]
;
ALTER TABLE [dbo].[rlistky] ADD  DEFAULT ((0)) FOR [sluzbyImax]
;
ALTER TABLE [dbo].[rlistky] ADD  DEFAULT ((0)) FOR [priplatekImax]
;
ALTER TABLE [dbo].[rlistky] ADD  DEFAULT ('') FOR [vouncher]
;
ALTER TABLE [dbo].[rlistky] ADD  DEFAULT ((0)) FOR [hodslevy]
;
ALTER TABLE [dbo].[rlistky] ADD  DEFAULT ((0)) FOR [cenkat]
;
ALTER TABLE [dbo].[rlistky] ADD  DEFAULT ((0)) FOR [sluzbyUltraX]
;
ALTER TABLE [dbo].[rlistky] ADD  DEFAULT ((0)) FOR [priplatekUltraX]
;

/****** Object:  Table [dbo].[rprodej]    Script Date: 16.06.2023 8:57:41 ******/
SET ANSI_NULLS ON
    ;
SET QUOTED_IDENTIFIER ON
    ;
CREATE TABLE [dbo].[rprodej](
    [rprodejID] [int] IDENTITY(1,1) NOT NULL,
    [rmenuID] [int] NOT NULL,
    [rzboziID] [int] NOT NULL,
    [rpoklID] [int] NOT NULL,
    [doklad] [char](10) NOT NULL,
    [sklad] [char](4) NOT NULL,
    [cena] [numeric](12, 2) NOT NULL,
    [mnoz] [int] NOT NULL,
    [dph] [tinyint] NOT NULL,
    [sluzby] [numeric](6, 2) NOT NULL,
    [lvrat] [bit] NOT NULL,
    [lhoto] [bit] NOT NULL,
    [datprod] [smalldatetime] NULL,
    [datpokl] [smalldatetime] NULL,
    [uzivprod] [int] NOT NULL,
    [uzivpokl] [int] NOT NULL,
    [pokladna] [char](10) NOT NULL,
    [zuziv] [char](10) NOT NULL,
    [zcas] [smalldatetime] NULL,
    [karta] [char](10) NOT NULL,
    [rzbozirID] [int] NOT NULL,
    [pcID] [int] NOT NULL,
    [voucher] [char](10) NOT NULL,
    [ltablet] [bit] NOT NULL,
    CONSTRAINT [PK__rprodej__6501FCD8] PRIMARY KEY CLUSTERED
(
[rprodejID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
    ) ON [PRIMARY]
;

ALTER TABLE [dbo].[rprodej] ADD  CONSTRAINT [DF_rprodej_rzboziID]  DEFAULT ((0)) FOR [rzboziID]
;
ALTER TABLE [dbo].[rprodej] ADD  CONSTRAINT [DF__rprodej__rpoklID__65F62111]  DEFAULT ((0)) FOR [rpoklID]
;
ALTER TABLE [dbo].[rprodej] ADD  CONSTRAINT [DF__rprodej__doklad__66EA454A]  DEFAULT ('') FOR [doklad]
;
ALTER TABLE [dbo].[rprodej] ADD  CONSTRAINT [DF_rprodej_sklad]  DEFAULT ('01') FOR [sklad]
;
ALTER TABLE [dbo].[rprodej] ADD  CONSTRAINT [DF_rprodej_dph]  DEFAULT ((0)) FOR [dph]
;
ALTER TABLE [dbo].[rprodej] ADD  CONSTRAINT [DF__rprodej__sluzby__67DE6983]  DEFAULT ((0)) FOR [sluzby]
;
ALTER TABLE [dbo].[rprodej] ADD  CONSTRAINT [DF__rprodej__lvrat__68D28DBC]  DEFAULT ((0)) FOR [lvrat]
;
ALTER TABLE [dbo].[rprodej] ADD  CONSTRAINT [DF__rprodej__lhoto__69C6B1F5]  DEFAULT ((0)) FOR [lhoto]
;
ALTER TABLE [dbo].[rprodej] ADD  CONSTRAINT [DF__rprodej__uzivpok__6ABAD62E]  DEFAULT ((0)) FOR [uzivpokl]
;
ALTER TABLE [dbo].[rprodej] ADD  CONSTRAINT [DF__rprodej__pokladn__6BAEFA67]  DEFAULT ('') FOR [pokladna]
;
ALTER TABLE [dbo].[rprodej] ADD  CONSTRAINT [DF__rprodej__zuziv__6CA31EA0]  DEFAULT ('') FOR [zuziv]
;
ALTER TABLE [dbo].[rprodej] ADD  CONSTRAINT [DF__rprodej__zcas__6D9742D9]  DEFAULT (getdate()) FOR [zcas]
;
ALTER TABLE [dbo].[rprodej] ADD  DEFAULT ('') FOR [karta]
;
ALTER TABLE [dbo].[rprodej] ADD  DEFAULT ((0)) FOR [rzbozirID]
;
ALTER TABLE [dbo].[rprodej] ADD  DEFAULT ((0)) FOR [pcID]
;
ALTER TABLE [dbo].[rprodej] ADD  DEFAULT ('') FOR [voucher]
;
ALTER TABLE [dbo].[rprodej] ADD  DEFAULT ((0)) FOR [ltablet]
;
SET ANSI_NULLS ON
;
SET QUOTED_IDENTIFIER ON
;

CREATE TABLE [dbo].[rsedadla](
    [rsedadlaID] [int] NOT NULL,
    [rprogID] [int] NOT NULL,
    [rmustrID] [int] NOT NULL,
    [rezervaceID] [int] NOT NULL,
    [csalu] [char](6) NOT NULL,
    [porad] [smalldatetime] NULL,
    [ceny] [varchar](51) NOT NULL,
    [cenkat] [tinyint] NOT NULL,
    [delegace] [tinyint] NOT NULL,
    [delegold] [tinyint] NOT NULL,
    [datprod] [smalldatetime] NULL,
    [cena] [numeric](6, 2) NOT NULL,
    [uzivprod] [int] NOT NULL,
    [ruzivID] [int] NOT NULL,
    [zuziv] [char](10) NOT NULL,
    [zcas] [datetime] NULL,
    [sleva] [char](8) NOT NULL,
    PRIMARY KEY CLUSTERED
(
[rsedadlaID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
    ) ON [PRIMARY]
;

ALTER TABLE [dbo].[rsedadla] ADD  CONSTRAINT [DF__rsedadla__rezerv__719CDDE7]  DEFAULT ((0)) FOR [rezervaceID]
;
ALTER TABLE [dbo].[rsedadla] ADD  CONSTRAINT [DF__rsedadla__jmeno__72910220]  DEFAULT ('') FOR [ceny]
;
ALTER TABLE [dbo].[rsedadla] ADD  CONSTRAINT [DF__rsedadla__delego__73852659]  DEFAULT ((0)) FOR [delegold]
;
ALTER TABLE [dbo].[rsedadla] ADD  CONSTRAINT [DF__rsedadla__cena__74794A92]  DEFAULT ((0)) FOR [cena]
;
ALTER TABLE [dbo].[rsedadla] ADD  CONSTRAINT [DF__rsedadla__uzivpr__756D6ECB]  DEFAULT ((0)) FOR [uzivprod]
;
ALTER TABLE [dbo].[rsedadla] ADD  CONSTRAINT [DF__rsedadla__ruzivI__76619304]  DEFAULT ((0)) FOR [ruzivID]
;
ALTER TABLE [dbo].[rsedadla] ADD  CONSTRAINT [DF__rsedadla__zuziv__7755B73D]  DEFAULT ('') FOR [zuziv]
;
ALTER TABLE [dbo].[rsedadla] ADD  CONSTRAINT [DF__rsedadla__zcas__7849DB76]  DEFAULT (getdate()) FOR [zcas]
;
ALTER TABLE [dbo].[rsedadla] ADD  DEFAULT ('') FOR [sleva]
;

CREATE TABLE [dbo].[rfilm](
    [rfilmID] [int] IDENTITY(1,1) NOT NULL,
    [distr] [char](4) NOT NULL,
    [poradatel] [char](6) NOT NULL,
    [cisfilm] [char](6) NOT NULL,
    [nazevf] [varchar](50) NOT NULL,
    [ornazevf] [varchar](45) NOT NULL,
    [prod] [char](2) NOT NULL,
    [datprem] [datetime] NULL,
    [monopol] [datetime] NULL,
    [zanrf] [char](2) NOT NULL,
    [pristup] [char](5) NOT NULL,
    [minf] [char](3) NOT NULL,
    [uprava] [char](15) NOT NULL,
    [producent] [char](40) NOT NULL,
    [rezie] [char](30) NOT NULL,
    [scenar] [char](30) NOT NULL,
    [kamera] [char](30) NOT NULL,
    [strih] [char](30) NOT NULL,
    [hudba] [char](30) NOT NULL,
    [hraji] [text] NULL,
    [slogan] [text] NULL,
    [popis] [text] NULL,
    [cisfilm_u] [int] NOT NULL,
    [zuziv] [char](10) NOT NULL,
    [zcas] [smalldatetime] NULL,
    [cenkat] [nchar](2) NOT NULL,
    [nlist] [smallint] NOT NULL,
    [kodupravy] [char](1) NOT NULL,
    [form_2d] [bit] NOT NULL,
    [form_3d] [bit] NOT NULL,
    [cisfilmUFD] [char](10) NOT NULL,
    [Jazyk] [char](4) NOT NULL,
    [cform] [char](2) NOT NULL,
    [cfotrm] [char](2) NOT NULL,
    PRIMARY KEY CLUSTERED
(
[rfilmID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
    ) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
;

ALTER TABLE [dbo].[rfilm] ADD  CONSTRAINT [DF__rfilm__poradatel__4F47C5E3]  DEFAULT ('') FOR [poradatel]
;
ALTER TABLE [dbo].[rfilm] ADD  CONSTRAINT [DF__rfilm__ornazevf__503BEA1C]  DEFAULT ('') FOR [ornazevf]
;
ALTER TABLE [dbo].[rfilm] ADD  CONSTRAINT [DF__rfilm__prod__51300E55]  DEFAULT ('') FOR [prod]
;
ALTER TABLE [dbo].[rfilm] ADD  CONSTRAINT [DF__rfilm__zanrf__5224328E]  DEFAULT ('') FOR [zanrf]
;
ALTER TABLE [dbo].[rfilm] ADD  CONSTRAINT [DF__rfilm__pristup__531856C7]  DEFAULT ('') FOR [pristup]
;
ALTER TABLE [dbo].[rfilm] ADD  CONSTRAINT [DF__rfilm__minf__540C7B00]  DEFAULT ('') FOR [minf]
;
ALTER TABLE [dbo].[rfilm] ADD  CONSTRAINT [DF__rfilm__uprava__55009F39]  DEFAULT ('') FOR [uprava]
;
ALTER TABLE [dbo].[rfilm] ADD  CONSTRAINT [DF__rfilm__producent__55F4C372]  DEFAULT ('') FOR [producent]
;
ALTER TABLE [dbo].[rfilm] ADD  CONSTRAINT [DF__rfilm__rezie__56E8E7AB]  DEFAULT ('') FOR [rezie]
;
ALTER TABLE [dbo].[rfilm] ADD  CONSTRAINT [DF__rfilm__scenar__57DD0BE4]  DEFAULT ('') FOR [scenar]
;
ALTER TABLE [dbo].[rfilm] ADD  CONSTRAINT [DF__rfilm__kamera__58D1301D]  DEFAULT ('') FOR [kamera]
;
ALTER TABLE [dbo].[rfilm] ADD  CONSTRAINT [DF__rfilm__strih__59C55456]  DEFAULT ('') FOR [strih]
;
ALTER TABLE [dbo].[rfilm] ADD  CONSTRAINT [DF__rfilm__hudba__5AB9788F]  DEFAULT ('') FOR [hudba]
;
ALTER TABLE [dbo].[rfilm] ADD  CONSTRAINT [DF__rfilm__cisfilm_u__5BAD9CC8]  DEFAULT ((0)) FOR [cisfilm_u]
;
ALTER TABLE [dbo].[rfilm] ADD  CONSTRAINT [DF__rfilm__zuziv__5CA1C101]  DEFAULT ('') FOR [zuziv]
;
ALTER TABLE [dbo].[rfilm] ADD  CONSTRAINT [DF__rfilm__zcas__5D95E53A]  DEFAULT (getdate()) FOR [zcas]
;
ALTER TABLE [dbo].[rfilm] ADD  CONSTRAINT [DF__rfilm__cenkat__5E8A0973]  DEFAULT ('') FOR [cenkat]
;
ALTER TABLE [dbo].[rfilm] ADD  CONSTRAINT [DF_rfilm_nlist_1]  DEFAULT ((0)) FOR [nlist]
;
ALTER TABLE [dbo].[rfilm] ADD  DEFAULT ('') FOR [kodupravy]
;
ALTER TABLE [dbo].[rfilm] ADD  DEFAULT ((0)) FOR [form_2d]
;
ALTER TABLE [dbo].[rfilm] ADD  DEFAULT ((0)) FOR [form_3d]
;
ALTER TABLE [dbo].[rfilm] ADD  DEFAULT ('') FOR [cisfilmUFD]
;
ALTER TABLE [dbo].[rfilm] ADD  DEFAULT ('') FOR [Jazyk]
;
ALTER TABLE [dbo].[rfilm] ADD  DEFAULT ('') FOR [cform]
;
ALTER TABLE [dbo].[rfilm] ADD  DEFAULT ('') FOR [cfotrm]
;
SET ANSI_NULLS ON
;
SET QUOTED_IDENTIFIER ON
;

CREATE TABLE [dbo].[rprog](
    [rprogID] [int] IDENTITY(1,1) NOT NULL,
    [rupravaID] [int] NOT NULL,
    [rfilmID] [int] NOT NULL,
    [rvstID] [int] NOT NULL,
    [csalu] [char](6) NOT NULL,
    [porad] [smalldatetime] NULL,
    [distr] [char](4) NOT NULL,
    [ciskop] [char](4) NOT NULL,
    [limit] [smallint] NOT NULL,
    [pujcovne] [numeric](4, 2) NOT NULL,
    [cenfix] [smallmoney] NOT NULL,
    [cenmin] [smallmoney] NOT NULL,
    [cenkat] [char](3) NOT NULL,
    [osa] [numeric](5, 2) NOT NULL,
    [osaproc] [bit] NOT NULL,
    [fk] [numeric](5, 2) NOT NULL,
    [fkproc] [bit] NOT NULL,
    [sp] [numeric](5, 2) NOT NULL,
    [spproc] [bit] NOT NULL,
    [ps] [bit] NOT NULL,
    [priplsluz] [numeric](6, 2) NOT NULL,
    [negraficky] [bit] NOT NULL,
    [naweb] [bit] NOT NULL,
    [stop] [bit] NOT NULL,
    [odpadlo] [bit] NOT NULL,
    [neucast] [bit] NOT NULL,
    [divaku] [smallint] NOT NULL,
    [trzba] [numeric](9, 2) NOT NULL,
    [rezervace] [smallint] NOT NULL,
    [fondcel] [numeric](9, 2) NOT NULL,
    [priplcel] [numeric](9, 2) NOT NULL,
    [odrezerv] [bit] NOT NULL,
    [kdyodrezerv] [smalldatetime] NULL,
    [cbanID] [smallint] NOT NULL,
    [pohhot] [char](2) NOT NULL,
    [pohfakt] [char](2) NOT NULL,
    [pohzal] [char](2) NOT NULL,
    [delegace] [int] NOT NULL,
    [casprod] [smallint] NOT NULL,
    [pozn] [text] NULL,
    [zuziv] [char](10) NOT NULL,
    [zcas] [smalldatetime] NULL,
    [neikar] [bit] NOT NULL,
    [rpoukazyID] [int] NOT NULL,
    [cistyp] [char](3) NOT NULL,
    [rcsluzby] [char](2) NOT NULL,
    [sluzbyDbox] [numeric](6, 2) NOT NULL,
    [sluzbyVIP] [numeric](6, 2) NOT NULL,
    [priplatekVIP] [numeric](6, 2) NOT NULL,
    [sluzbyPremium] [numeric](6, 2) NOT NULL,
    [priplatekPremium] [numeric](6, 2) NOT NULL,
    [sluzbyImax] [numeric](6, 2) NOT NULL,
    [priplatekImax] [numeric](6, 2) NOT NULL,
    [cistyp2] [char](3) NOT NULL,
    [cistyp3] [char](3) NOT NULL,
    [cistyp4] [char](3) NOT NULL,
    [vs] [char](10) NOT NULL,
    [jazyk] [char](4) NOT NULL,
    [cfotrm] [char](2) NOT NULL,
    [sluzbyUltraX] [numeric](6, 2) NOT NULL,
    [priplatekUltraX] [numeric](6, 2) NOT NULL,
    CONSTRAINT [PK__rprog__7CD98669] PRIMARY KEY CLUSTERED
(
[rprogID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
    ) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
;
ALTER TABLE [dbo].[rprog] ADD  CONSTRAINT [DF__rprog__ciskop__10E07F16]  DEFAULT ('') FOR [ciskop]
;
ALTER TABLE [dbo].[rprog] ADD  CONSTRAINT [DF__rprog__limit__11D4A34F]  DEFAULT ((0)) FOR [limit]
;
ALTER TABLE [dbo].[rprog] ADD  CONSTRAINT [DF__rprog__pujcovne__12C8C788]  DEFAULT ((0)) FOR [pujcovne]
;
ALTER TABLE [dbo].[rprog] ADD  CONSTRAINT [DF__rprog__cenfix__13BCEBC1]  DEFAULT ((0)) FOR [cenfix]
;
ALTER TABLE [dbo].[rprog] ADD  CONSTRAINT [DF__rprog__cenmin__14B10FFA]  DEFAULT ((0)) FOR [cenmin]
;
ALTER TABLE [dbo].[rprog] ADD  CONSTRAINT [DF__rprog__cenkat__15A53433]  DEFAULT ('') FOR [cenkat]
;
ALTER TABLE [dbo].[rprog] ADD  CONSTRAINT [DF__rprog__osa__1699586C]  DEFAULT ((0)) FOR [osa]
;
ALTER TABLE [dbo].[rprog] ADD  CONSTRAINT [DF__rprog__osaproc__178D7CA5]  DEFAULT ((0)) FOR [osaproc]
;
ALTER TABLE [dbo].[rprog] ADD  CONSTRAINT [DF__rprog__fk__1881A0DE]  DEFAULT ((0)) FOR [fk]
;
ALTER TABLE [dbo].[rprog] ADD  CONSTRAINT [DF__rprog__fkproc__1975C517]  DEFAULT ((0)) FOR [fkproc]
;
ALTER TABLE [dbo].[rprog] ADD  CONSTRAINT [DF_rprog_sp_1]  DEFAULT ((0)) FOR [sp]
;
ALTER TABLE [dbo].[rprog] ADD  CONSTRAINT [DF_rprog_spproc_1]  DEFAULT ((1)) FOR [spproc]
;
ALTER TABLE [dbo].[rprog] ADD  CONSTRAINT [DF__rprog__ps__1A69E950]  DEFAULT ((0)) FOR [ps]
;
ALTER TABLE [dbo].[rprog] ADD  CONSTRAINT [DF_rprog_priplsluz]  DEFAULT ((0)) FOR [priplsluz]
;
ALTER TABLE [dbo].[rprog] ADD  CONSTRAINT [DF__rprog__negrafick__1C5231C2]  DEFAULT ((0)) FOR [negraficky]
;
ALTER TABLE [dbo].[rprog] ADD  CONSTRAINT [DF_rprog_naweb]  DEFAULT ((1)) FOR [naweb]
;
ALTER TABLE [dbo].[rprog] ADD  CONSTRAINT [DF__rprog__stop__1D4655FB]  DEFAULT ((0)) FOR [stop]
;
ALTER TABLE [dbo].[rprog] ADD  CONSTRAINT [DF__rprog__odpadlo__1E3A7A34]  DEFAULT ((0)) FOR [odpadlo]
;
ALTER TABLE [dbo].[rprog] ADD  CONSTRAINT [DF__rprog__neucast__1F2E9E6D]  DEFAULT ((0)) FOR [neucast]
;
ALTER TABLE [dbo].[rprog] ADD  CONSTRAINT [DF__rprog__divaku__2022C2A6]  DEFAULT ((0)) FOR [divaku]
;
ALTER TABLE [dbo].[rprog] ADD  CONSTRAINT [DF__rprog__trzba__2116E6DF]  DEFAULT ((0)) FOR [trzba]
;
ALTER TABLE [dbo].[rprog] ADD  CONSTRAINT [DF__rprog__rezervace__220B0B18]  DEFAULT ((0)) FOR [rezervace]
;
ALTER TABLE [dbo].[rprog] ADD  CONSTRAINT [DF__rprog__fondcel__22FF2F51]  DEFAULT ((0)) FOR [fondcel]
;
ALTER TABLE [dbo].[rprog] ADD  CONSTRAINT [DF__rprog__priplcel__23F3538A]  DEFAULT ((0)) FOR [priplcel]
;
ALTER TABLE [dbo].[rprog] ADD  CONSTRAINT [DF__rprog__odrezerv__24E777C3]  DEFAULT ((0)) FOR [odrezerv]
;
ALTER TABLE [dbo].[rprog] ADD  CONSTRAINT [DF__rprog__cbanID__25DB9BFC]  DEFAULT ((0)) FOR [cbanID]
;
ALTER TABLE [dbo].[rprog] ADD  CONSTRAINT [DF__rprog__pohhot__26CFC035]  DEFAULT ('') FOR [pohhot]
;
ALTER TABLE [dbo].[rprog] ADD  CONSTRAINT [DF__rprog__pohfakt__27C3E46E]  DEFAULT ('') FOR [pohfakt]
;
ALTER TABLE [dbo].[rprog] ADD  CONSTRAINT [DF__rprog__pohzal__28B808A7]  DEFAULT ('') FOR [pohzal]
;
ALTER TABLE [dbo].[rprog] ADD  CONSTRAINT [DF__rprog__delegace__29AC2CE0]  DEFAULT ((0)) FOR [delegace]
;
ALTER TABLE [dbo].[rprog] ADD  CONSTRAINT [DF_rprog_casprod_1]  DEFAULT ((0)) FOR [casprod]
;
ALTER TABLE [dbo].[rprog] ADD  CONSTRAINT [DF__rprog__zuziv__2AA05119]  DEFAULT ('') FOR [zuziv]
;
ALTER TABLE [dbo].[rprog] ADD  CONSTRAINT [DF__rprog__zcas__2B947552]  DEFAULT (getdate()) FOR [zcas]
;
ALTER TABLE [dbo].[rprog] ADD  DEFAULT ((0)) FOR [neikar]
;
ALTER TABLE [dbo].[rprog] ADD  DEFAULT ((0)) FOR [rpoukazyID]
;
ALTER TABLE [dbo].[rprog] ADD  DEFAULT ('00') FOR [cistyp]
;
ALTER TABLE [dbo].[rprog] ADD  DEFAULT ('') FOR [rcsluzby]
;
ALTER TABLE [dbo].[rprog] ADD  DEFAULT ((0)) FOR [sluzbyDbox]
;
ALTER TABLE [dbo].[rprog] ADD  DEFAULT ((0)) FOR [sluzbyVIP]
;
ALTER TABLE [dbo].[rprog] ADD  DEFAULT ((0)) FOR [priplatekVIP]
;
ALTER TABLE [dbo].[rprog] ADD  DEFAULT ((0)) FOR [sluzbyPremium]
;
ALTER TABLE [dbo].[rprog] ADD  DEFAULT ((0)) FOR [priplatekPremium]
;
ALTER TABLE [dbo].[rprog] ADD  DEFAULT ((0)) FOR [sluzbyImax]
;
ALTER TABLE [dbo].[rprog] ADD  DEFAULT ((0)) FOR [priplatekImax]
;
ALTER TABLE [dbo].[rprog] ADD  DEFAULT ('') FOR [cistyp2]
;
ALTER TABLE [dbo].[rprog] ADD  DEFAULT ('') FOR [cistyp3]
;
ALTER TABLE [dbo].[rprog] ADD  DEFAULT ('') FOR [cistyp4]
;
ALTER TABLE [dbo].[rprog] ADD  DEFAULT ('') FOR [vs]
;
ALTER TABLE [dbo].[rprog] ADD  DEFAULT ('') FOR [jazyk]
;
ALTER TABLE [dbo].[rprog] ADD  DEFAULT ('') FOR [cfotrm]
;
ALTER TABLE [dbo].[rprog] ADD  DEFAULT ((0)) FOR [sluzbyUltraX]
;
ALTER TABLE [dbo].[rprog] ADD  DEFAULT ((0)) FOR [priplatekUltraX]
;

SET ANSI_NULLS ON
;
SET QUOTED_IDENTIFIER ON
;
CREATE TABLE [dbo].[rvst](
    [rvstID] [smallint] IDENTITY(1,1) NOT NULL,
    [csalu] [char](6) NOT NULL,
    [distr] [nchar](4) NOT NULL,
    [kat] [char](3) NOT NULL,
    [nazev] [nchar](15) NOT NULL,
    [rkurzID1] [int] NOT NULL,
    [rkurzID2] [int] NOT NULL,
    [rkurzID3] [int] NOT NULL,
    [rkurzID4] [int] NOT NULL,
    [rkurzID5] [int] NOT NULL,
    [rkurzID6] [int] NOT NULL,
    [vst1] [smallmoney] NOT NULL,
    [nazev1] [varchar](15) NOT NULL,
    [vst2] [smallmoney] NOT NULL,
    [nazev2] [varchar](15) NOT NULL,
    [vst3] [smallmoney] NOT NULL,
    [nazev3] [varchar](15) NOT NULL,
    [vst4] [smallmoney] NOT NULL,
    [nazev4] [varchar](15) NOT NULL,
    [vst5] [smallmoney] NOT NULL,
    [nazev5] [varchar](15) NOT NULL,
    [vst6] [smallmoney] NOT NULL,
    [nazev6] [varchar](15) NOT NULL,
    [vst7] [smallmoney] NOT NULL,
    [nazev7] [varchar](15) NOT NULL,
    [vst8] [smallmoney] NOT NULL,
    [nazev8] [varchar](15) NOT NULL,
    [vst9] [smallmoney] NOT NULL,
    [nazev9] [varchar](15) NOT NULL,
    [vst10] [smallmoney] NOT NULL,
    [nazev10] [varchar](15) NOT NULL,
    [vst11] [smallmoney] NOT NULL,
    [nazev11] [varchar](15) NOT NULL,
    [vst12] [smallmoney] NOT NULL,
    [nazev12] [varchar](15) NOT NULL,
    [vst13] [smallmoney] NOT NULL,
    [nazev13] [varchar](15) NOT NULL,
    [vst14] [smallmoney] NOT NULL,
    [nazev14] [varchar](15) NOT NULL,
    [vst15] [smallmoney] NOT NULL,
    [nazev15] [varchar](15) NOT NULL,
    [vst16] [smallmoney] NOT NULL,
    [nazev16] [varchar](15) NOT NULL,
    [vst17] [smallmoney] NOT NULL,
    [nazev17] [varchar](15) NOT NULL,
    [vst18] [smallmoney] NOT NULL,
    [nazev18] [varchar](15) NOT NULL,
    [vst19] [smallmoney] NOT NULL,
    [nazev19] [varchar](15) NOT NULL,
    [vst20] [smallmoney] NOT NULL,
    [nazev20] [varchar](15) NOT NULL,
    [zuziv] [char](10) NOT NULL,
    [zcas] [smalldatetime] NULL,
    [kategorie] [nchar](3) NOT NULL,
    [aktivni] [bit] NOT NULL,
    [sleva2] [char](2) NOT NULL,
    [sleva3] [char](2) NOT NULL,
    [sleva4] [char](2) NOT NULL,
    [sleva19] [char](2) NOT NULL,
    [sleva18] [char](2) NOT NULL,
    [sleva17] [char](2) NOT NULL,
    CONSTRAINT [PK__rvst__37703C52] PRIMARY KEY CLUSTERED
(
[rvstID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
    ) ON [PRIMARY]
;
ALTER TABLE [dbo].[rvst] ADD  CONSTRAINT [DF__rvst__csalu__1E6F845E]  DEFAULT ('') FOR [csalu]
;
ALTER TABLE [dbo].[rvst] ADD  CONSTRAINT [DF__rvst__nazev__1F63A897]  DEFAULT ('') FOR [nazev]
;
ALTER TABLE [dbo].[rvst] ADD  CONSTRAINT [DF_rvst_rkurzID1]  DEFAULT ((1)) FOR [rkurzID1]
;
ALTER TABLE [dbo].[rvst] ADD  CONSTRAINT [DF_rvst_rkurzID2_1]  DEFAULT ((1)) FOR [rkurzID2]
;
ALTER TABLE [dbo].[rvst] ADD  CONSTRAINT [DF_rvst_rkurzID3_1]  DEFAULT ((1)) FOR [rkurzID3]
;
ALTER TABLE [dbo].[rvst] ADD  CONSTRAINT [DF_rvst_rkurzID4_1]  DEFAULT ((1)) FOR [rkurzID4]
;
ALTER TABLE [dbo].[rvst] ADD  CONSTRAINT [DF_rvst_rkurzID5_1]  DEFAULT ((1)) FOR [rkurzID5]
;
ALTER TABLE [dbo].[rvst] ADD  CONSTRAINT [DF_rvst_rkurzID6_1]  DEFAULT ((1)) FOR [rkurzID6]
;
ALTER TABLE [dbo].[rvst] ADD  CONSTRAINT [DF__rvst__vst1__2057CCD0]  DEFAULT ((0)) FOR [vst1]
;
ALTER TABLE [dbo].[rvst] ADD  CONSTRAINT [DF__rvst__nazev1__214BF109]  DEFAULT ('') FOR [nazev1]
;
ALTER TABLE [dbo].[rvst] ADD  CONSTRAINT [DF__rvst__vst2__22401542]  DEFAULT ((0)) FOR [vst2]
;
ALTER TABLE [dbo].[rvst] ADD  CONSTRAINT [DF__rvst__nazev2__2334397B]  DEFAULT ('') FOR [nazev2]
;
ALTER TABLE [dbo].[rvst] ADD  CONSTRAINT [DF__rvst__vst3__24285DB4]  DEFAULT ((0)) FOR [vst3]
;
ALTER TABLE [dbo].[rvst] ADD  CONSTRAINT [DF__rvst__nazev3__251C81ED]  DEFAULT ('') FOR [nazev3]
;
ALTER TABLE [dbo].[rvst] ADD  CONSTRAINT [DF__rvst__vst4__2610A626]  DEFAULT ((0)) FOR [vst4]
;
ALTER TABLE [dbo].[rvst] ADD  CONSTRAINT [DF__rvst__nazev4__2704CA5F]  DEFAULT ('') FOR [nazev4]
;
ALTER TABLE [dbo].[rvst] ADD  CONSTRAINT [DF__rvst__vst5__27F8EE98]  DEFAULT ((0)) FOR [vst5]
;
ALTER TABLE [dbo].[rvst] ADD  CONSTRAINT [DF__rvst__nazev5__28ED12D1]  DEFAULT ('') FOR [nazev5]
;
ALTER TABLE [dbo].[rvst] ADD  CONSTRAINT [DF__rvst__vst6__29E1370A]  DEFAULT ((0)) FOR [vst6]
;
ALTER TABLE [dbo].[rvst] ADD  CONSTRAINT [DF__rvst__nazev6__2AD55B43]  DEFAULT ('') FOR [nazev6]
;
ALTER TABLE [dbo].[rvst] ADD  CONSTRAINT [DF__rvst__vst7__2BC97F7C]  DEFAULT ((0)) FOR [vst7]
;
ALTER TABLE [dbo].[rvst] ADD  CONSTRAINT [DF__rvst__nazev7__2CBDA3B5]  DEFAULT ('') FOR [nazev7]
;
ALTER TABLE [dbo].[rvst] ADD  CONSTRAINT [DF__rvst__vst8__2DB1C7EE]  DEFAULT ((0)) FOR [vst8]
;
ALTER TABLE [dbo].[rvst] ADD  CONSTRAINT [DF__rvst__nazev8__2EA5EC27]  DEFAULT ('') FOR [nazev8]
;
ALTER TABLE [dbo].[rvst] ADD  CONSTRAINT [DF__rvst__vst9__2F9A1060]  DEFAULT ((0)) FOR [vst9]
;
ALTER TABLE [dbo].[rvst] ADD  CONSTRAINT [DF__rvst__nazev9__308E3499]  DEFAULT ('') FOR [nazev9]
;
ALTER TABLE [dbo].[rvst] ADD  CONSTRAINT [DF__rvst__vst10__318258D2]  DEFAULT ((0)) FOR [vst10]
;
ALTER TABLE [dbo].[rvst] ADD  CONSTRAINT [DF__rvst__nazev10__32767D0B]  DEFAULT ('') FOR [nazev10]
;
ALTER TABLE [dbo].[rvst] ADD  CONSTRAINT [DF__rvst__vst11__336AA144]  DEFAULT ((0)) FOR [vst11]
;
ALTER TABLE [dbo].[rvst] ADD  CONSTRAINT [DF__rvst__nazev11__345EC57D]  DEFAULT ('') FOR [nazev11]
;
ALTER TABLE [dbo].[rvst] ADD  CONSTRAINT [DF__rvst__vst12__3552E9B6]  DEFAULT ((0)) FOR [vst12]
;
ALTER TABLE [dbo].[rvst] ADD  CONSTRAINT [DF__rvst__nazev12__36470DEF]  DEFAULT ('') FOR [nazev12]
;
ALTER TABLE [dbo].[rvst] ADD  CONSTRAINT [DF__rvst__vst13__373B3228]  DEFAULT ((0)) FOR [vst13]
;
ALTER TABLE [dbo].[rvst] ADD  CONSTRAINT [DF__rvst__nazev13__382F5661]  DEFAULT ('') FOR [nazev13]
;
ALTER TABLE [dbo].[rvst] ADD  CONSTRAINT [DF__rvst__vst14__39237A9A]  DEFAULT ((0)) FOR [vst14]
;
ALTER TABLE [dbo].[rvst] ADD  CONSTRAINT [DF__rvst__nazev14__3A179ED3]  DEFAULT ('') FOR [nazev14]
;
ALTER TABLE [dbo].[rvst] ADD  CONSTRAINT [DF__rvst__vst15__3B0BC30C]  DEFAULT ((0)) FOR [vst15]
;
ALTER TABLE [dbo].[rvst] ADD  CONSTRAINT [DF__rvst__nazev15__3BFFE745]  DEFAULT ('') FOR [nazev15]
;
ALTER TABLE [dbo].[rvst] ADD  CONSTRAINT [DF__rvst__vst16__3CF40B7E]  DEFAULT ((0)) FOR [vst16]
;
ALTER TABLE [dbo].[rvst] ADD  CONSTRAINT [DF__rvst__nazev16__3DE82FB7]  DEFAULT ('') FOR [nazev16]
;
ALTER TABLE [dbo].[rvst] ADD  CONSTRAINT [DF__rvst__vst17__3EDC53F0]  DEFAULT ((0)) FOR [vst17]
;
ALTER TABLE [dbo].[rvst] ADD  CONSTRAINT [DF__rvst__nazev17__3FD07829]  DEFAULT ('') FOR [nazev17]
;
ALTER TABLE [dbo].[rvst] ADD  CONSTRAINT [DF__rvst__vst18__40C49C62]  DEFAULT ((0)) FOR [vst18]
;
ALTER TABLE [dbo].[rvst] ADD  CONSTRAINT [DF__rvst__nazev18__41B8C09B]  DEFAULT ('') FOR [nazev18]
;
ALTER TABLE [dbo].[rvst] ADD  CONSTRAINT [DF__rvst__vst19__42ACE4D4]  DEFAULT ((0)) FOR [vst19]
;
ALTER TABLE [dbo].[rvst] ADD  CONSTRAINT [DF__rvst__nazev19__43A1090D]  DEFAULT ('') FOR [nazev19]
;
ALTER TABLE [dbo].[rvst] ADD  CONSTRAINT [DF__rvst__vst20__44952D46]  DEFAULT ((0)) FOR [vst20]
;
ALTER TABLE [dbo].[rvst] ADD  CONSTRAINT [DF__rvst__nazev20__4589517F]  DEFAULT ('') FOR [nazev20]
;
ALTER TABLE [dbo].[rvst] ADD  CONSTRAINT [DF__rvst__zuziv__467D75B8]  DEFAULT ('') FOR [zuziv]
;
ALTER TABLE [dbo].[rvst] ADD  CONSTRAINT [DF__rvst__zcas__477199F1]  DEFAULT (getdate()) FOR [zcas]
;
ALTER TABLE [dbo].[rvst] ADD  CONSTRAINT [DF__rvst__kategorie__4865BE2A]  DEFAULT ('') FOR [kategorie]
;
ALTER TABLE [dbo].[rvst] ADD  DEFAULT ((1)) FOR [aktivni]
;
ALTER TABLE [dbo].[rvst] ADD  DEFAULT ('') FOR [sleva2]
;
ALTER TABLE [dbo].[rvst] ADD  DEFAULT ('') FOR [sleva3]
;
ALTER TABLE [dbo].[rvst] ADD  DEFAULT ('') FOR [sleva4]
;
ALTER TABLE [dbo].[rvst] ADD  DEFAULT ('') FOR [sleva19]
;
ALTER TABLE [dbo].[rvst] ADD  DEFAULT ('') FOR [sleva18]
;
ALTER TABLE [dbo].[rvst] ADD  DEFAULT ('') FOR [sleva17]
;
SET IDENTITY_INSERT [dbo].[rvst] ON
;

----------------------
--- init test data ---
----------------------
INSERT INTO rps_CinemaxE.dbo.rsedadla (rsedadlaID, rprogID, rmustrID, rezervaceID, csalu, porad, ceny, cenkat, delegace, delegold, datprod, cena, uzivprod, ruzivID, zuziv, zcas, sleva) VALUES (1, 1, 1, 0, N'510070', N'2023-08-19 16:00:00', N'1,2,', 1, 1, 0, null, 0.00, 0, 0, N'monika    ', N'2023-06-05 14:54:00.000', N'        ');
INSERT INTO rps_CinemaxE.dbo.rsedadla (rsedadlaID, rprogID, rmustrID, rezervaceID, csalu, porad, ceny, cenkat, delegace, delegold, datprod, cena, uzivprod, ruzivID, zuziv, zcas, sleva) VALUES (2, 1, 2, 0, N'510070', N'2023-08-19 16:00:00', N'1,2,', 1, 1, 0, null, 0.00, 0, 0, N'monika    ', N'2023-06-05 14:55:00.000', N'        ');
INSERT INTO rps_CinemaxE.dbo.rsedadla (rsedadlaID, rprogID, rmustrID, rezervaceID, csalu, porad, ceny, cenkat, delegace, delegold, datprod, cena, uzivprod, ruzivID, zuziv, zcas, sleva) VALUES (3, 1, 3, 0, N'510070', N'2023-08-19 16:00:00', N'1,2,', 1, 1, 0, null, 0.00, 0, 0, N'monika    ', N'2023-06-05 14:55:00.000', N'        ');
INSERT INTO rps_CinemaxE.dbo.rsedadla (rsedadlaID, rprogID, rmustrID, rezervaceID, csalu, porad, ceny, cenkat, delegace, delegold, datprod, cena, uzivprod, ruzivID, zuziv, zcas, sleva) VALUES (4, 1, 4, 0, N'510070', N'2023-08-19 16:00:00', N'1,2,', 1, 1, 0, null, 0.00, 0, 0, N'monika    ', N'2023-06-05 14:55:00.000', N'        ');
INSERT INTO rps_CinemaxE.dbo.rprog (rupravaID, rfilmID, rvstID, csalu, porad, distr, ciskop, limit, pujcovne, cenfix, cenmin, cenkat, osa, osaproc, fk, fkproc, sp, spproc, ps, priplsluz, negraficky, naweb, stop, odpadlo, neucast, divaku, trzba, rezervace, fondcel, priplcel, odrezerv, kdyodrezerv, cbanID, pohhot, pohfakt, pohzal, delegace, casprod, pozn, zuziv, zcas, neikar, rpoukazyID, cistyp, rcsluzby, sluzbyDbox, sluzbyVIP, priplatekVIP, sluzbyPremium, priplatekPremium, sluzbyImax, priplatekImax, cistyp2, cistyp3, cistyp4, vs, jazyk, cfotrm, sluzbyUltraX, priplatekUltraX) VALUES (1, 1, 1, 510070, N'2023-07-21 16:00:00', 0, N'    ', 222, N'50.00', N'0.0000', N'0.0000', N'002', N'1.00', N'True', N'1.00', N'True', N'0.00', N'True', N'False', N'0.00', N'False', N'True', N'False', N'False', N'False', 0, N'0.00', 0, N'0.00', N'0.00', N'False', N'', 0, 21, 8, N'  ', 0, 30, N'', N'DISdata   ', N'2023-07-18 15:14:00', N'False', 0, 0, N'  ', N'1.00', N'0.00', N'0.00', N'0.00', N'0.00', N'2.00', N'3.00', N'   ', N'   ', N'   ', N'          ', N'    ', N'  ', N'0.00', N'0.00');
INSERT INTO rps_CinemaxE.dbo.rfilm (distr, poradatel, cisfilm, nazevf, ornazevf, prod, datprem, monopol, zanrf, pristup, minf, uprava, producent, rezie, scenar, kamera, strih, hudba, hraji, slogan, popis, cisfilm_u, zuziv, zcas, cenkat, nlist, kodupravy, form_2d, form_3d, cisfilmUFD, Jazyk, cform, cfotrm) VALUES (1, N'      ', N'559568', N'Oppenheimer                                       ', N'Oppenheimer                                  ', N'56', N'2023-07-21 0:00:00', N'2023-07-21 0:00:00', N'd ', N'15   ', 200, N'Titulky        ', N'                                        ', N'                              ', N'                              ', N'                              ', N'                              ', N'                              ', N'', N'', N'', 0, N'erika     ', N'2023-07-27 19:38:00', N'  ', 0, N'1', N'False', N'False', N'          ', N'    ', N'  ', N'  ');
INSERT INTO rps_CinemaxE.dbo.rvst (rvstID, csalu, distr, kat, nazev, rkurzID1, rkurzID2, rkurzID3, rkurzID4, rkurzID5, rkurzID6, vst1, nazev1, vst2, nazev2, vst3, nazev3, vst4, nazev4, vst5, nazev5, vst6, nazev6, vst7, nazev7, vst8, nazev8, vst9, nazev9, vst10, nazev10, vst11, nazev11, vst12, nazev12, vst13, nazev13, vst14, nazev14, vst15, nazev15, vst16, nazev16, vst17, nazev17, vst18, nazev18, vst19, nazev19, vst20, nazev20, zuziv, zcas, kategorie, aktivni, sleva2, sleva3, sleva4, sleva19, sleva18, sleva17) VALUES (1, N'      ', N'04  ', N'I  ', N'Do 15h        ', 1, 1, 1, 1, 1, 1, 6.0000, N'Dospely        ', 4.0000, N'Student        ', 0.0000, N'', 0.0000, N'', 0.0000, N'', 0.0000, N'', 0.0000, N'', 0.0000, N'', 0.0000, N'', 0.0000, N'', 0.0000, N'', 0.0000, N'', 0.0000, N'', 0.0000, N'', 0.0000, N'', 0.0000, N'', 0.0000, N'', 0.0000, N'', 0.0000, N'', 0.0000, N'', N'tibor     ', N'2016-04-22 15:48:00', N'   ', 1, N'W7', N'  ', N'  ', N'  ', N'  ', N'  ');

----------------------
--- init procedure ---
----------------------
CREATE proc [dbo].[prodej]
 @ljdok bit, @rsedadlaID int, @rprogID int, @rodbID int, @csalu char(6), @sekce char(3), @cena numeric(7,2),
 @sluzby numeric(6,2), @sleva char(8), @lvrat bit, @lhoto bit, @labo bit, @datprod datetime,
 @uzivprod int, @pokladna char(10), @rada char(5), @misto char(5), @pozn char(10), @rdokladID int = 0, @sp numeric(5,2) = 0, @karta char(12) = '',@sluzbyDbox numeric(6,2)=0,
 @sluzbyVIP numeric(6,2)=0, @priplatekVIP numeric(6,2)=0, @sluzbyPremium numeric(6,2)=0, @priplatekPremium numeric(6,2)=0,@sluzbyImax numeric(6,2)=0,@priplatekImax numeric(6,2)=0,
 @rlistkyID int = 0 output,@okula numeric(6,2)=0,@vouncher char(10)='',@cenkat tinyint = 0,@sluzbyUltraX numeric(6,2)=0,@priplatekUltraX numeric(6,2)=0
AS
BEGIN
Begin Tran
 Declare @doklad char(10), @nlist as Int, @error int, @rmustrID int, @rprogID2 int, @hodslevy numeric(6,2)=0.0,@slevpo char(2) = ''
 IF isnull(@rsedadlaID,0)>0 --Místenkový prodej
SELECT @rmustrID=rmustrID,@rprogID2=rprogID FROM rsedadla where rsedadlaID=@rsedadlaID
    else --Nemístenkový prodej
SET @rmustrID=0
    if ISNULL(@rmustrID,0)>0 AND @rprogID!=@rprogID2 --Nemělo by nastat sedadla nepatří k představení prodej
BEGIN
       RAISERROR('Predávané miesto nie je miestom daného predstavenia!',16,1) WITH LOG
       ROLLBACK TRAN
       RETURN
END
 IF @labo=1  -- Jde o pernamentku
Update ruprava
Set @nlist=ruprava.nabo+1, nabo=ruprava.nabo+1
    FROM ruprava Join Rprog ON ruprava.rupravaID=rprog.rupravaID Where rprog.rprogID=@rprogID
    ELSE  -- Jde o vstupenku
--  IF @cena>0 AND Charindex('V',@pozn)=0  --Pouze FKMB
Update rfilm
Set @nlist=rfilm.nlist+1, nlist=rfilm.nlist+1
    From rfilm Join rprog On rfilm.rfilmID=rprog.rfilmID Where rprog.rprogID=@rprogID
--  Else
--    Set @nlist=0
    IF LEN(RTRIM(@sleva))>0 --Jedná se o služby
SELECT @hodslevy=@cena-v.vst1-@sluzbyDbox FROM rprog p join rvst v ON p.rvstID=v.rvstID WHERE p.rprogID=@rprogID
    IF @cenkat>0
SET @slevpo = (SELECT CASE  @cenkat WHEN 2 THEN v.sleva2 WHEN 3 THEN v.sleva3 WHEN 4 THEN v.sleva4 WHEN 17 THEN v.sleva17 WHEN 18 THEN v.sleva18 WHEN 19 THEN v.sleva19 ELSE '' END
    FROM rvst v Join rprog p ON p.rvstID=v.rvstID WHERE p.rprogID=@rprogID)
    IF Len(Rtrim(@slevpo))>0 AND CharIndex(Rtrim(@slevpo),@sleva)=0 AND LEN(RTRIM(@sleva))<7
SET @sleva=RTRIM(@sleva)+RTRIM(@slevpo)
SET @error=@@error
SET @cena=@cena+@priplatekVIP+@priplatekPremium+@priplatekImax+@priplatekUltraX
SET @sluzby=@sluzby+@sluzbyVIP+@sluzbyPremium+@sluzbyImax+@sluzbyUltraX
    IF @ljdok=1  --Doklad je odvozen z ID
Begin
Insert Into rlistky (rsedadlaID,rprogID,rdokladID,rodbID,csalu,sekce,doklad,cena,sluzby,sp,sleva,lvrat,lhoto,labo,datprod,uzivprod,pokladna,rada,misto,pozn,karta,rmustrID,
                     sluzbyDbox,okula,sluzbyVIP,priplatekVIP,sluzbyPremium,priplatekPremium,sluzbyImax,priplatekImax,sluzbyUltraX,priplatekUltraX,vouncher,hodslevy,cenkat) Values
    (@rsedadlaID,@rprogID,@rdokladID,@rodbID,@csalu,@sekce,'', @cena,@sluzby,@sp,@sleva,@lvrat,@lhoto,@labo,@datprod,@uzivprod,@pokladna,@rada,@misto,@pozn,@karta,@rmustrID,
     ISNULL(@sluzbyDbox,0),@okula,@sluzbyVIP,@priplatekVIP,@sluzbyPremium,@priplatekPremium,@sluzbyImax,@priplatekImax,@sluzbyUltraX,@priplatekUltraX,@vouncher,@hodslevy,@cenkat)
    SET @error=@@error
SET @rlistkyID=@@identity
SET @doklad = REPLICATE('0',10-len(cast(@rlistkyID as char(10))))+RTRIM(cast(@rlistkyID as char(10)))
Update rlistky set doklad=@doklad where rlistkyID=@rlistkyID
    SET @error=@@error
End
ELSE  --Doklad je odvozen
Begin
    SET @doklad = REPLICATE('0',10-len(cast(@nlist as char(10))))+RTRIM(cast(@nlist as char(10)))
    Insert Into rlistky (rsedadlaID,rprogID,rdokladID,rodbID,csalu,sekce,doklad,cena,sluzby,sp,sleva,lvrat,lhoto,labo,datprod,uzivprod,pokladna,rada,misto,pozn,karta,rmustrID,
          sluzbyDbox,okula,sluzbyVIP,priplatekVIP,sluzbyPremium,priplatekPremium,sluzbyImax,priplatekImax,sluzbyUltraX,priplatekUltraX,vouncher,hodslevy) Values
        (@rsedadlaID,@rprogID,@rdokladID,@rodbID,@csalu,@sekce,@doklad,@cena,@sluzby,@sp,@sleva,@lvrat,@lhoto,@labo,@datprod,@uzivprod,@pokladna,@rada,@misto,@pozn,@karta,@rmustrID,
          ISNULL(@sluzbyDbox,0),@okula,@sluzbyVIP,@priplatekVIP,@sluzbyPremium,@priplatekPremium,@sluzbyImax,@priplatekImax,@sluzbyUltraX,@priplatekUltraX,@vouncher,@hodslevy)
    SET @error=@@error
    SET @rlistkyID=@@identity
End
   if LEN(Rtrim(@vouncher))>0 --AND  @error<=0
update voucher set pocpo=pocpo+1 WHERE cislo=@vouncher
    IF @error>0
    rollback tran
  Else
    Commit tran
Select @rlistkyID as rlistkyID
END