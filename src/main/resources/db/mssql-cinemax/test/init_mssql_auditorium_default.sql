-----------------------
--- init test table ---
-----------------------
/****** Object:  Table [dbo].[rklic]    Script Date: 28.06.2023 10:01:15 ******/
SET ANSI_NULLS ON
;
SET QUOTED_IDENTIFIER ON
;
CREATE TABLE [dbo].[rklic](
    [rklicID] [smallint] IDENTITY(1,1) NOT NULL,
    [cilic] [char](6) NOT NULL,
    [sn] [char](7) NOT NULL,
    [zarizeni] [char](6) NOT NULL,
    [provoz] [char](15) NOT NULL,
    [appl] [char](8) NOT NULL,
    [letni] [bit] NOT NULL,
    [dan] [tinyint] NOT NULL,
    [ddan] [tinyint] NOT NULL,
    [zdan] [tinyint] NOT NULL,
    [skuzby] [smallint] NOT NULL,
    [casprod] [smallint] NOT NULL,
    [osa] [numeric](5, 2) NOT NULL,
    [osaproc] [numeric](5, 2) NOT NULL,
    [pobocka] [smallint] NOT NULL,
    [int_prog] [bit] NOT NULL,
    [int_rez] [bit] NOT NULL,
    [int_user] [char](10) NOT NULL,
    [int_pasw] [char](10) NOT NULL,
    [verze] [char](15) NOT NULL,
    [typ_vst] [char](1) NOT NULL,
    [typ_tisk] [char](1) NOT NULL,
    [fondkin] [bit] NOT NULL,
    [fondkc] [numeric](5, 2) NOT NULL,
    [fondproc] [numeric](5, 2) NOT NULL,
    [odrezerv] [int] NOT NULL,
    [zaokrouhl] [tinyint] NOT NULL,
    [ipmask] [char](15) NOT NULL,
    [pujcovne] [smallint] NOT NULL,
    [sluzbyDbox] [numeric](6, 2) NOT NULL,
    [sluzbyVIP] [numeric](6, 2) NOT NULL,
    [priplatekVIP] [numeric](6, 2) NOT NULL,
    [sluzbyPremium] [numeric](6, 2) NOT NULL,
    [priplatekPremium] [numeric](6, 2) NOT NULL,
    [sluzbyImax] [numeric](6, 2) NOT NULL,
    [priplatekImax] [numeric](6, 2) NOT NULL,
    [sluzbyUltraX] [numeric](6, 2) NOT NULL,
    [priplatekUltraX] [numeric](6, 2) NOT NULL,
    [vyslheslo] [varchar](20) NOT NULL,
    PRIMARY KEY CLUSTERED
(
[rklicID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
    ) ON [PRIMARY]
;

----------------------
--- init test data ---
----------------------
INSERT INTO rps_CinemaxE.dbo.rklic (
    cilic, sn, zarizeni, provoz, appl, letni, dan, ddan, zdan, skuzby, casprod, osa, osaproc, pobocka, int_prog, int_rez, int_user, int_pasw, verze, typ_vst, typ_tisk, fondkin, fondkc, fondproc, odrezerv, zaokrouhl, ipmask, pujcovne, sluzbyDbox, sluzbyVIP, priplatekVIP, sluzbyPremium, priplatekPremium, sluzbyImax, priplatekImax, sluzbyUltraX, priplatekUltraX, vyslheslo
) VALUES
      (N'510000', N'92I7320', N'510640', N'510640         ', N'REZ-SYST', 0, 20, 20, 2, 0, 25, 1.00, 2.00, 0, 1, 1, N'          ', N'          ', N'               ', N' ', N' ', 1, 3.00, 50, 9.00, 8.00, 5.00, 4.00, 3.00, 2.00, 1.00, 10.00, 50, 25, 2, 13.00, 16.00, N''),
      (N'510010', N'92I7321', N'510641', N'510641         ', N'REZ-SYST', 0, 20, 20, 2, 0, 26, 1.01, 3.00, 0, 1, 1, N'          ', N'          ', N'               ', N' ', N' ', 1, 4.00, 51, 9.01, 8.01, 5.01, 4.01, 3.01, 2.01, 1.01, 11.00, 51, 26, 3, 14.00, 17.00, N''),
      (N'510020', N'92I7322', N'510642', N'510642         ', N'REZ-SYST', 0, 20, 20, 2, 0, 27, 1.02, 4.00, 0, 1, 1, N'          ', N'          ', N'               ', N' ', N' ', 1, 5.00, 52, 9.02, 8.02, 5.02, 4.02, 3.02, 2.02, 1.02, 12.00, 52, 27, 4, 15.00, 18.00, N'');
