------------------------
--- init test tables ---
------------------------
SET ANSI_NULLS ON
;
SET QUOTED_IDENTIFIER ON
;
CREATE TABLE [dbo].[voucher](
    [voucherID] [int] IDENTITY(1,1) NOT NULL,
    [cislo] [char](10) NOT NULL,
    [typ] [char](15) NOT NULL,
    [maxpo] [int] NOT NULL,
    [pocpo] [int] NOT NULL,
    [platod] [smalldatetime] NULL,
    [platdo] [smalldatetime] NULL,
    [sleva] [char](2) NOT NULL,
    [sleva3d] [char](2) NOT NULL,
    [zuziv] [char](10) NOT NULL,
    [zcas] [smalldatetime] NULL,
    PRIMARY KEY CLUSTERED
(
[voucherID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
    ) ON [PRIMARY]
;

ALTER TABLE [dbo].[voucher] ADD  DEFAULT ('') FOR [typ]
;
ALTER TABLE [dbo].[voucher] ADD  DEFAULT ((1)) FOR [maxpo]
;
ALTER TABLE [dbo].[voucher] ADD  DEFAULT ((0)) FOR [pocpo]
;
ALTER TABLE [dbo].[voucher] ADD  DEFAULT ('') FOR [sleva]
;
ALTER TABLE [dbo].[voucher] ADD  DEFAULT ('') FOR [sleva3d]
;
ALTER TABLE [dbo].[voucher] ADD  DEFAULT ('') FOR [zuziv]
;
ALTER TABLE [dbo].[voucher] ADD  DEFAULT (getdate()) FOR [zcas]
;

----------------------
--- init test data ---
----------------------
INSERT INTO rps_CinemaxE.dbo.voucher (cislo, typ, maxpo, pocpo, platod, platdo, sleva, sleva3d, zuziv, zcas) VALUES (N'6092217854', N'O2             ', 2, 0, N'2023-03-10 00:00:00', N'2034-12-31 23:59:00', N'O2', N'O2', N'          ', N'2023-03-10 10:55:00');
INSERT INTO rps_CinemaxE.dbo.voucher (cislo, typ, maxpo, pocpo, platod, platdo, sleva, sleva3d, zuziv, zcas) VALUES (N'6128016208', N'IQOS           ', 2, 0, N'2023-05-02 00:00:00', N'2034-12-31 20:00:00', N'K2', N'K2', N'          ', N'2023-05-02 11:00:00');
INSERT INTO rps_CinemaxE.dbo.voucher (cislo, typ, maxpo, pocpo, platod, platdo, sleva, sleva3d, zuziv, zcas) VALUES (N'6209016364', N'Akce1          ', 1000, 0, N'2023-05-10 00:00:00', N'2033-12-31 23:59:00', N'00', N'00', N'          ', N'2023-05-10 15:28:00');
-- expired voucher
INSERT INTO rps_CinemaxE.dbo.voucher (cislo, typ, maxpo, pocpo, platod, platdo, sleva, sleva3d, zuziv, zcas) VALUES (N'6209016365', N'Akce1          ', 1000, 0, N'2003-05-10 00:00:00', N'2013-12-31 23:59:00', N'00', N'00', N'          ', N'2023-05-10 15:28:00');
-- voucher with blank 'sleva' attribute
INSERT INTO rps_CinemaxE.dbo.voucher (cislo, typ, maxpo, pocpo, platod, platdo, sleva, sleva3d, zuziv, zcas) VALUES (N'5103670506', N'Akce1          ', 1000, 0, N'2023-05-10 00:00:00', N'2033-12-31 23:59:00', N'  ', N'00', N'          ', N'2023-05-10 15:28:00');
