------------------------
--- init test tables ---
------------------------
/****** Object:  Table [dbo].[prava]    Script Date: 28.06.2023 10:01:15 ******/
SET ANSI_NULLS ON
;
SET QUOTED_IDENTIFIER ON
;
CREATE TABLE [dbo].[prava](
    [pravaID] [int] IDENTITY(1,1) NOT NULL,
    [klic] [char](5) NOT NULL,
    [nazev] [varchar](50) NOT NULL,
    PRIMARY KEY CLUSTERED
(
[pravaID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
    ) ON [PRIMARY]
;

/****** Object:  Table [dbo].[ruziv]    Script Date: 28.06.2023 10:01:15 ******/
SET ANSI_NULLS ON
;
SET QUOTED_IDENTIFIER ON
;
CREATE TABLE [dbo].[ruziv](
    [ruzivID] [smallint] IDENTITY(1,1) NOT NULL,
    [uziv] [char](10) NOT NULL,
    [Jmeno] [char](40) NOT NULL,
    [Heslo] [char](10) NOT NULL,
    [zapis] [bit] NOT NULL,
    [pokladna] [char](10) NOT NULL,
    [datOd] [datetime] NULL,
    [datDo] [datetime] NULL,
    [datPrist] [smalldatetime] NULL,
    [pokD] [int] NOT NULL,
    [pokZ] [int] NOT NULL,
    [uzpokl] [smalldatetime] NULL,
    [prace] [char](12) NOT NULL,
    [vstup] [bit] NOT NULL,
    [old] [int] NOT NULL,
    [nlist] [smallint] NOT NULL,
    [nabo] [smallint] NOT NULL,
    [zuziv] [char](10) NOT NULL,
    [zcas] [smalldatetime] NULL,
    [linternet] [bit] NOT NULL,
    CONSTRAINT [PK__ruziv__45F365D3] PRIMARY KEY CLUSTERED
(
[ruzivID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
    ) ON [PRIMARY]
;

ALTER TABLE [dbo].[ruziv] ADD  CONSTRAINT [DF__ruziv__Jmeno__77BFCB91]  DEFAULT ('') FOR [Jmeno]
;
ALTER TABLE [dbo].[ruziv] ADD  CONSTRAINT [DF__ruziv__zapis__78B3EFCA]  DEFAULT ((0)) FOR [zapis]
;
ALTER TABLE [dbo].[ruziv] ADD  CONSTRAINT [DF__ruziv__pokladna__79A81403]  DEFAULT ('') FOR [pokladna]
;
ALTER TABLE [dbo].[ruziv] ADD  CONSTRAINT [DF__ruziv__datOd__7A9C383C]  DEFAULT (getdate()) FOR [datOd]
;
ALTER TABLE [dbo].[ruziv] ADD  CONSTRAINT [DF__ruziv__pokD__7B905C75]  DEFAULT ((0)) FOR [pokD]
;
ALTER TABLE [dbo].[ruziv] ADD  CONSTRAINT [DF__ruziv__pokZ__7C8480AE]  DEFAULT ((0)) FOR [pokZ]
;
ALTER TABLE [dbo].[ruziv] ADD  CONSTRAINT [DF__ruziv__prace__7D78A4E7]  DEFAULT ('') FOR [prace]
;
ALTER TABLE [dbo].[ruziv] ADD  CONSTRAINT [DF__ruziv__vstup__7E6CC920]  DEFAULT ((0)) FOR [vstup]
;
ALTER TABLE [dbo].[ruziv] ADD  CONSTRAINT [DF__ruziv__old__7F60ED59]  DEFAULT ((0)) FOR [old]
;
ALTER TABLE [dbo].[ruziv] ADD  CONSTRAINT [DF_ruziv_nlist_1]  DEFAULT ((0)) FOR [nlist]
;
ALTER TABLE [dbo].[ruziv] ADD  CONSTRAINT [DF_ruziv_nabo_1]  DEFAULT ((0)) FOR [nabo]
;
ALTER TABLE [dbo].[ruziv] ADD  CONSTRAINT [DF__ruziv__zuziv__00551192]  DEFAULT ('') FOR [zuziv]
;
ALTER TABLE [dbo].[ruziv] ADD  DEFAULT ((0)) FOR [linternet]
;

/****** Object:  Table [dbo].[pravuz]    Script Date: 28.06.2023 10:01:15 ******/
SET ANSI_NULLS ON
;
SET QUOTED_IDENTIFIER ON
;
CREATE TABLE [dbo].[pravuz](
    [pravuzID] [int] IDENTITY(1,1) NOT NULL,
    [pravaID] [int] NOT NULL,
    [ruzivID] [int] NOT NULL,
    PRIMARY KEY CLUSTERED
(
[pravuzID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
    ) ON [PRIMARY]
;

----------------------
--- init test data ---
----------------------
INSERT INTO rps_CinemaxE.dbo.prava (klic, nazev) VALUES (N'm100 ', N'Číselníky');
INSERT INTO rps_CinemaxE.dbo.prava (klic, nazev) VALUES (N'm101 ', N'Začátky');
INSERT INTO rps_CinemaxE.dbo.prava (klic, nazev) VALUES (N'm702 ', N'Uživatelé systému');
