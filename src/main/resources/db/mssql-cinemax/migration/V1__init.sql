/* manually added HQ database table */
CREATE TABLE [dbo].[rprovozy](
    rprovozyID   int identity primary key,
    nazev        varchar(40)             not null,
    csalu4       char(5)                 not null,
    provozSklad  smallint                not null,
    provozSklad2 smallint                not null,
    zkra<PERSON>ka      char(3)      default '' not null,
    spoj         varchar(150) default '' not null,
    pc           char(2)      default '' not null,
    csaluDistr   char(6)      default '' not null,
    ncsal        tinyint      default 4  not null
);

/****** Object:  Table [dbo].[Acl]    Script Date: 28.06.2023 10:01:15 ******/
SET ANSI_NULLS ON
;
SET QUOTED_IDENTIFIER ON
;
CREATE TABLE [dbo].[Acl](
	[id] [int] IDENTITY(1,1) NOT NULL,
	[idResource] [int] NOT NULL,
	[idPrivilege] [int] NOT NULL,
	[idRole] [int] NOT NULL,
	[allowed] [tinyint] NOT NULL,
 CONSTRAINT [PK_Acl] PRIMARY KEY CLUSTERED
(
	[id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
;
/****** Object:  Table [dbo].[Acl$Branch]    Script Date: 28.06.2023 10:01:15 ******/
SET ANSI_NULLS ON
;
SET QUOTED_IDENTIFIER ON
;
CREATE TABLE [dbo].[Acl$Branch](
	[id] [int] IDENTITY(1,1) NOT NULL,
	[code] [varchar](4) NULL,
	[name] [varchar](255) NULL,
	[type] [varchar](255) NULL,
 CONSTRAINT [PK_Acl$Branch] PRIMARY KEY CLUSTERED
(
	[id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
;
/****** Object:  Table [dbo].[Acl$Privilege]    Script Date: 28.06.2023 10:01:15 ******/
SET ANSI_NULLS ON
;
SET QUOTED_IDENTIFIER ON
;
CREATE TABLE [dbo].[Acl$Privilege](
	[id] [int] IDENTITY(1,1) NOT NULL,
	[name] [varchar](255) NOT NULL,
	[comment] [varchar](255) NULL,
 CONSTRAINT [PK_Acl$Privilege] PRIMARY KEY CLUSTERED
(
	[id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
;
/****** Object:  Table [dbo].[Acl$Resource]    Script Date: 28.06.2023 10:01:15 ******/
SET ANSI_NULLS ON
;
SET QUOTED_IDENTIFIER ON
;
CREATE TABLE [dbo].[Acl$Resource](
	[id] [int] IDENTITY(1,1) NOT NULL,
	[name] [varchar](255) NOT NULL,
	[comment] [varchar](255) NULL,
 CONSTRAINT [PK_Acl$Resource] PRIMARY KEY CLUSTERED
(
	[id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
;
/****** Object:  Table [dbo].[Acl$Role]    Script Date: 28.06.2023 10:01:15 ******/
SET ANSI_NULLS ON
;
SET QUOTED_IDENTIFIER ON
;
CREATE TABLE [dbo].[Acl$Role](
	[id] [int] IDENTITY(1,1) NOT NULL,
	[idParent] [int] NULL,
	[name] [varchar](255) NOT NULL,
 CONSTRAINT [PK_Acl$Role] PRIMARY KEY CLUSTERED
(
	[id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
;
/****** Object:  Table [dbo].[Acl$User]    Script Date: 28.06.2023 10:01:15 ******/
SET ANSI_NULLS ON
;
SET QUOTED_IDENTIFIER ON
;
CREATE TABLE [dbo].[Acl$User](
	[id] [int] IDENTITY(1,1) NOT NULL,
	[username] [varchar](255) NOT NULL,
	[password] [varchar](255) NULL,
	[passwordIsEncrypted] [tinyint] NOT NULL,
	[name] [varchar](255) NULL,
	[surname] [varchar](255) NULL,
	[lastLogin] [datetime] NULL,
	[aaa] [nchar](10) NULL,
	[active] [tinyint] NOT NULL,
 CONSTRAINT [PK_Acl$User] PRIMARY KEY CLUSTERED
(
	[id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
;
/****** Object:  Table [dbo].[Acl$UserHasBranch]    Script Date: 28.06.2023 10:01:15 ******/
SET ANSI_NULLS ON
;
SET QUOTED_IDENTIFIER ON
;
CREATE TABLE [dbo].[Acl$UserHasBranch](
	[id] [int] IDENTITY(1,1) NOT NULL,
	[idBranch] [int] NULL,
	[idUser] [int] NULL,
 CONSTRAINT [PK_Acl$UserHasBranch] PRIMARY KEY CLUSTERED
(
	[id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY],
 CONSTRAINT [IX_Acl$UserHasBranch] UNIQUE NONCLUSTERED
(
	[idBranch] ASC,
	[idUser] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
;
/****** Object:  Table [dbo].[Acl$UserHasRole]    Script Date: 28.06.2023 10:01:15 ******/
SET ANSI_NULLS ON
;
SET QUOTED_IDENTIFIER ON
;
CREATE TABLE [dbo].[Acl$UserHasRole](
	[id] [int] IDENTITY(1,1) NOT NULL,
	[idRole] [int] NOT NULL,
	[idUser] [int] NOT NULL,
 CONSTRAINT [PK_Acl$UserHasRole] PRIMARY KEY CLUSTERED
(
	[id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY],
 CONSTRAINT [IX_Acl$UserHasRole] UNIQUE NONCLUSTERED
(
	[idRole] ASC,
	[idUser] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
;
/****** Object:  Table [dbo].[cban]    Script Date: 28.06.2023 10:01:15 ******/
SET ANSI_NULLS ON
;
SET QUOTED_IDENTIFIER ON
;
CREATE TABLE [dbo].[cban](
	[cbanID] [int] IDENTITY(1,1) NOT NULL,
	[pc] [smallint] NOT NULL,
	[ucet] [varchar](30) NOT NULL,
	[banka] [char](4) NOT NULL,
	[iban] [varchar](50) NOT NULL,
	[zuziv] [char](10) NOT NULL,
	[zcas] [smalldatetime] NULL,
PRIMARY KEY CLUSTERED
(
	[cbanID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
;
/****** Object:  Table [dbo].[cjazyk]    Script Date: 28.06.2023 10:01:15 ******/
SET ANSI_NULLS ON
;
SET QUOTED_IDENTIFIER ON
;
CREATE TABLE [dbo].[cjazyk](
	[cjazykID] [int] IDENTITY(1,1) NOT NULL,
	[kod] [char](4) NOT NULL,
	[nazev] [varchar](30) NOT NULL,
	[zuziv] [char](10) NOT NULL,
	[zcas] [smalldatetime] NULL,
PRIMARY KEY CLUSTERED
(
	[cjazykID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
;
/****** Object:  Table [dbo].[ctyppr]    Script Date: 28.06.2023 10:01:15 ******/
SET ANSI_NULLS ON
;
SET QUOTED_IDENTIFIER ON
;
CREATE TABLE [dbo].[ctyppr](
	[ctypprID] [int] IDENTITY(1,1) NOT NULL,
	[cistyp] [char](3) NOT NULL,
	[nazev] [varchar](40) NOT NULL,
	[zuziv] [char](10) NOT NULL,
	[zcas] [smalldatetime] NULL,
	[priplatek] [numeric](6, 2) NOT NULL,
	[sluzby] [numeric](6, 2) NOT NULL,
PRIMARY KEY CLUSTERED
(
	[ctypprID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
;
/****** Object:  Table [dbo].[devices]    Script Date: 28.06.2023 10:01:15 ******/
SET ANSI_NULLS ON
;
SET QUOTED_IDENTIFIER ON
;
CREATE TABLE [dbo].[devices](
	[deviceId] [int] IDENTITY(1,1) NOT NULL,
	[token] [char](32) NOT NULL
) ON [PRIMARY]
;
/****** Object:  Table [dbo].[chybac]    Script Date: 28.06.2023 10:01:15 ******/
SET ANSI_NULLS ON
;
SET QUOTED_IDENTIFIER ON
;
CREATE TABLE [dbo].[chybac](
	[chybacID] [int] IDENTITY(1,1) NOT NULL,
	[stanice] [varchar](50) NOT NULL,
	[datum] [smalldatetime] NULL,
	[chyba] [text] NULL,
	[zuziv] [char](10) NOT NULL,
	[zcas] [smalldatetime] NULL,
PRIMARY KEY CLUSTERED
(
	[chybacID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
;
/****** Object:  Table [dbo].[kontrola]    Script Date: 28.06.2023 10:01:15 ******/
SET ANSI_NULLS ON
;
SET QUOTED_IDENTIFIER ON
;
CREATE TABLE [dbo].[kontrola](
	[kontrolaID] [int] IDENTITY(1,1) NOT NULL,
	[datumcas] [datetime] NOT NULL,
	[pcname] [varchar](70) NOT NULL,
	[zuziv] [char](10) NULL,
PRIMARY KEY CLUSTERED
(
	[kontrolaID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
;
/****** Object:  Table [dbo].[mince]    Script Date: 28.06.2023 10:01:15 ******/
SET ANSI_NULLS ON
;
SET QUOTED_IDENTIFIER ON
;
CREATE TABLE [dbo].[mince](
	[minceID] [int] IDENTITY(1,1) NOT NULL,
	[hodnota] [numeric](8, 2) NULL
) ON [PRIMARY]
;
/****** Object:  Table [dbo].[param]    Script Date: 28.06.2023 10:01:15 ******/
SET ANSI_NULLS ON
;
SET QUOTED_IDENTIFIER ON
;
CREATE TABLE [dbo].[param](
	[paramID] [int] IDENTITY(1,1) NOT NULL,
	[jmeno] [char](15) NOT NULL,
	[obsah] [varchar](150) NOT NULL,
	[typ] [nchar](1) NOT NULL,
	[popis] [varchar](50) NOT NULL,
	[modifik] [bit] NOT NULL,
	[zobrazit] [bit] NOT NULL,
	[mazat] [bit] NOT NULL,
	[nacist] [bit] NOT NULL,
	[zuziv] [char](10) NOT NULL,
	[zcas] [smalldatetime] NULL,
PRIMARY KEY CLUSTERED
(
	[paramID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
;
/****** Object:  Table [dbo].[prava]    Script Date: 28.06.2023 10:01:15 ******/
SET ANSI_NULLS ON
;
SET QUOTED_IDENTIFIER ON
;
CREATE TABLE [dbo].[prava](
	[pravaID] [int] IDENTITY(1,1) NOT NULL,
	[klic] [char](5) NOT NULL,
	[nazev] [varchar](50) NOT NULL,
PRIMARY KEY CLUSTERED
(
	[pravaID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
;
/****** Object:  Table [dbo].[pravuz]    Script Date: 28.06.2023 10:01:15 ******/
SET ANSI_NULLS ON
;
SET QUOTED_IDENTIFIER ON
;
CREATE TABLE [dbo].[pravuz](
	[pravuzID] [int] IDENTITY(1,1) NOT NULL,
	[pravaID] [int] NOT NULL,
	[ruzivID] [int] NOT NULL,
PRIMARY KEY CLUSTERED
(
	[pravuzID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
;
/****** Object:  Table [dbo].[Product$Has Acl Branch]    Script Date: 28.06.2023 10:01:15 ******/
SET ANSI_NULLS ON
;
SET QUOTED_IDENTIFIER ON
;
CREATE TABLE [dbo].[Product$Has Acl Branch](
	[id] [int] IDENTITY(1,1) NOT NULL,
	[idProduct] [int] NOT NULL,
	[codeAclBranch] [varchar](50) NOT NULL,
 CONSTRAINT [PK_Product$Has Acl Branch_1] PRIMARY KEY CLUSTERED
(
	[id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY],
 CONSTRAINT [IX_Product$Has Acl Branch] UNIQUE NONCLUSTERED
(
	[codeAclBranch] ASC,
	[idProduct] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
;
/****** Object:  Table [dbo].[rbarvy]    Script Date: 28.06.2023 10:01:15 ******/
SET ANSI_NULLS ON
;
SET QUOTED_IDENTIFIER ON
;
CREATE TABLE [dbo].[rbarvy](
	[rbarvyID] [int] IDENTITY(1,1) NOT NULL,
	[jmeno] [nchar](20) NOT NULL,
	[barvav] [int] NOT NULL,
	[barvas] [int] NOT NULL,
	[nfont] [smallint] NOT NULL,
	[fontb] [bit] NOT NULL,
	[fonti] [bit] NOT NULL,
	[cenkat] [tinyint] NOT NULL,
	[delegace] [tinyint] NOT NULL,
PRIMARY KEY CLUSTERED
(
	[rbarvyID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
;
/****** Object:  Table [dbo].[rcag]    Script Date: 28.06.2023 10:01:15 ******/
SET ANSI_NULLS ON
;
SET QUOTED_IDENTIFIER ON
;
CREATE TABLE [dbo].[rcag](
	[rcagID] [int] IDENTITY(1,1) NOT NULL,
	[kod] [char](2) NOT NULL,
	[nazev] [varchar](40) NOT NULL,
	[zuziv] [char](10) NOT NULL,
	[zcas] [smalldatetime] NULL,
PRIMARY KEY CLUSTERED
(
	[rcagID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
;
/****** Object:  Table [dbo].[rcards]    Script Date: 28.06.2023 10:01:15 ******/
SET ANSI_NULLS ON
;
SET QUOTED_IDENTIFIER ON
;
CREATE TABLE [dbo].[rcards](
	[rcardsID] [int] IDENTITY(1,1) NOT NULL,
	[rodbID] [int] NOT NULL,
	[rlistIeID] [int] NOT NULL,
	[cislo] [char](10) NOT NULL,
	[typ] [char](1) NOT NULL,
	[platod] [smalldatetime] NULL,
	[platdo] [smalldatetime] NULL,
	[cena] [numeric](8, 2) NULL,
	[zuziv] [char](10) NOT NULL,
	[zcas] [smalldatetime] NULL,
	[lvipcena] [bit] NOT NULL,
	[KreditOd] [smalldatetime] NULL,
	[body] [int] NOT NULL,
PRIMARY KEY CLUSTERED
(
	[rcardsID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
;
/****** Object:  Table [dbo].[rcas]    Script Date: 28.06.2023 10:01:15 ******/
SET ANSI_NULLS ON
;
SET QUOTED_IDENTIFIER ON
;
CREATE TABLE [dbo].[rcas](
	[rcasID] [int] IDENTITY(1,1) NOT NULL,
	[ckin] [char](6) NOT NULL,
	[kod] [char](1) NOT NULL,
	[cas] [char](5) NOT NULL,
	[zadej] [bit] NOT NULL,
	[zuziv] [char](10) NOT NULL,
	[zcas] [smalldatetime] NULL,
PRIMARY KEY CLUSTERED
(
	[rcasID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
;
/****** Object:  Table [dbo].[rcform]    Script Date: 28.06.2023 10:01:15 ******/
SET ANSI_NULLS ON
;
SET QUOTED_IDENTIFIER ON
;
CREATE TABLE [dbo].[rcform](
	[rcformID] [int] IDENTITY(1,1) NOT NULL,
	[kod] [char](2) NOT NULL,
	[nazev] [varchar](20) NOT NULL,
	[zuziv] [char](10) NOT NULL,
	[zcas] [smalldatetime] NULL,
PRIMARY KEY CLUSTERED
(
	[rcformID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
;
/****** Object:  Table [dbo].[rckar]    Script Date: 28.06.2023 10:01:15 ******/
SET ANSI_NULLS ON
;
SET QUOTED_IDENTIFIER ON
;
CREATE TABLE [dbo].[rckar](
	[rckarID] [smallint] IDENTITY(1,1) NOT NULL,
	[kar] [char](2) NOT NULL,
	[nazkar] [char](20) NOT NULL,
	[zuziv] [char](10) NOT NULL,
	[zcas] [smalldatetime] NULL,
PRIMARY KEY CLUSTERED
(
	[rckarID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
;
/****** Object:  Table [dbo].[rcpe]    Script Date: 28.06.2023 10:01:15 ******/
SET ANSI_NULLS ON
;
SET QUOTED_IDENTIFIER ON
;
CREATE TABLE [dbo].[rcpe](
	[rcpeID] [smallint] IDENTITY(1,1) NOT NULL,
	[csalu] [char](6) NOT NULL,
	[cena] [int] NOT NULL,
	[nazev] [varchar](20) NOT NULL,
	[zuziv] [char](10) NOT NULL,
	[zcas] [smalldatetime] NULL,
 CONSTRAINT [PK__rcpe__2A4B4B5E] PRIMARY KEY CLUSTERED
(
	[rcpeID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
;
/****** Object:  Table [dbo].[rcpoh]    Script Date: 28.06.2023 10:01:15 ******/
SET ANSI_NULLS ON
;
SET QUOTED_IDENTIFIER ON
;
CREATE TABLE [dbo].[rcpoh](
	[rcpohID] [int] IDENTITY(1,1) NOT NULL,
	[rcradID] [int] NOT NULL,
	[pohyb] [char](2) NOT NULL,
	[nazev] [varchar](40) NOT NULL,
	[cena] [money] NOT NULL,
	[druhp] [char](1) NOT NULL,
	[typp] [char](1) NOT NULL,
	[platnost] [bit] NOT NULL,
	[zuziv] [char](10) NOT NULL,
	[zcas] [smalldatetime] NULL,
PRIMARY KEY CLUSTERED
(
	[rcpohID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
;
/****** Object:  Table [dbo].[rcpristup]    Script Date: 28.06.2023 10:01:15 ******/
SET ANSI_NULLS ON
;
SET QUOTED_IDENTIFIER ON
;
CREATE TABLE [dbo].[rcpristup](
	[rcpristupID] [int] IDENTITY(1,1) NOT NULL,
	[nazev] [char](5) NOT NULL,
	[zuziv] [char](10) NOT NULL,
	[zcas] [smalldatetime] NULL,
PRIMARY KEY CLUSTERED
(
	[rcpristupID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
;
/****** Object:  Table [dbo].[rcrad]    Script Date: 28.06.2023 10:01:15 ******/
SET ANSI_NULLS ON
;
SET QUOTED_IDENTIFIER ON
;
CREATE TABLE [dbo].[rcrad](
	[rcradID] [int] IDENTITY(1,1) NOT NULL,
	[rada] [char](2) NOT NULL,
	[nazev] [varchar](40) NOT NULL,
	[pred] [char](5) NOT NULL,
	[za] [char](5) NOT NULL,
	[doklad] [int] NOT NULL,
	[zuziv] [char](10) NOT NULL,
	[zcas] [smalldatetime] NULL,
PRIMARY KEY CLUSTERED
(
	[rcradID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
;
/****** Object:  Table [dbo].[rcrek]    Script Date: 28.06.2023 10:01:15 ******/
SET ANSI_NULLS ON
;
SET QUOTED_IDENTIFIER ON
;
CREATE TABLE [dbo].[rcrek](
	[rcrekID] [int] IDENTITY(1,1) NOT NULL,
	[kod] [char](2) NOT NULL,
	[nazev] [char](20) NOT NULL,
	[zuziv] [char](10) NOT NULL,
	[zcas] [smalldatetime] NULL,
PRIMARY KEY CLUSTERED
(
	[rcrekID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
;
/****** Object:  Table [dbo].[rcrez]    Script Date: 28.06.2023 10:01:15 ******/
SET ANSI_NULLS ON
;
SET QUOTED_IDENTIFIER ON
;
CREATE TABLE [dbo].[rcrez](
	[rcrezID] [smallint] IDENTITY(1,1) NOT NULL,
	[kod] [char](2) NOT NULL,
	[nazrez] [char](20) NOT NULL,
	[maxvst] [numeric](3, 0) NOT NULL,
	[zuziv] [char](10) NOT NULL,
	[zcas] [smalldatetime] NULL,
PRIMARY KEY CLUSTERED
(
	[rcrezID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
;
/****** Object:  Table [dbo].[rcsluzby]    Script Date: 28.06.2023 10:01:15 ******/
SET ANSI_NULLS ON
;
SET QUOTED_IDENTIFIER ON
;
CREATE TABLE [dbo].[rcsluzby](
	[rcsluzbyID] [int] IDENTITY(1,1) NOT NULL,
	[cislo] [char](2) NOT NULL,
	[nazev] [varchar](40) NOT NULL,
	[cena] [numeric](6, 2) NOT NULL,
	[liver] [bit] NOT NULL,
	[zuziv] [char](10) NOT NULL,
	[zcas] [smalldatetime] NULL,
PRIMARY KEY CLUSTERED
(
	[rcsluzbyID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
;
/****** Object:  Table [dbo].[rcupr]    Script Date: 28.06.2023 10:01:15 ******/
SET ANSI_NULLS ON
;
SET QUOTED_IDENTIFIER ON
;
CREATE TABLE [dbo].[rcupr](
	[rcuprID] [int] IDENTITY(1,1) NOT NULL,
	[nazev] [varchar](40) NOT NULL,
	[zuziv] [char](10) NOT NULL,
	[zcas] [smalldatetime] NULL,
	[kod] [char](1) NOT NULL,
PRIMARY KEY CLUSTERED
(
	[rcuprID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
;
/****** Object:  Table [dbo].[rczan]    Script Date: 28.06.2023 10:01:15 ******/
SET ANSI_NULLS ON
;
SET QUOTED_IDENTIFIER ON
;
CREATE TABLE [dbo].[rczan](
	[rczanID] [int] IDENTITY(1,1) NOT NULL,
	[tisksID] [int] NOT NULL,
	[tisksIDabo] [int] NOT NULL,
	[kod] [char](1) NOT NULL,
	[nazev] [varchar](40) NOT NULL,
	[zuziv] [char](10) NOT NULL,
	[zcas] [smalldatetime] NULL,
 CONSTRAINT [PK__rczan__30F848ED] PRIMARY KEY CLUSTERED
(
	[rczanID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
;
/****** Object:  Table [dbo].[rdispozice]    Script Date: 28.06.2023 10:01:15 ******/
SET ANSI_NULLS ON
;
SET QUOTED_IDENTIFIER ON
;
CREATE TABLE [dbo].[rdispozice](
	[rdispoziceID] [int] IDENTITY(1,1) NOT NULL,
	[csalu] [char](6) NOT NULL,
	[rfilmID] [int] NOT NULL,
	[ciskop] [char](4) NOT NULL,
	[od] [smalldatetime] NULL,
	[do] [smalldatetime] NULL,
	[odkino] [char](6) NOT NULL,
	[oddat] [smalldatetime] NULL,
	[dokino] [char](6) NOT NULL,
	[dodat] [smalldatetime] NULL,
	[doklad] [char](15) NOT NULL,
	[typdo] [char](1) NOT NULL,
	[typod] [char](1) NOT NULL,
	[naklady] [numeric](9, 2) NOT NULL,
	[zuziv] [char](10) NOT NULL,
	[zcas] [smalldatetime] NULL,
 CONSTRAINT [PK_rdispozice] PRIMARY KEY CLUSTERED
(
	[rdispoziceID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
;
/****** Object:  Table [dbo].[rdistr]    Script Date: 28.06.2023 10:01:15 ******/
SET ANSI_NULLS ON
;
SET QUOTED_IDENTIFIER ON
;
CREATE TABLE [dbo].[rdistr](
	[rdistrID] [int] IDENTITY(1,1) NOT NULL,
	[kod] [char](2) NOT NULL,
	[distr] [char](4) NOT NULL,
	[nazevd] [varchar](50) NOT NULL,
	[limit] [smallint] NOT NULL,
	[provize] [numeric](4, 2) NOT NULL,
	[ulice] [varchar](50) NOT NULL,
	[misto] [varchar](50) NOT NULL,
	[psc] [char](6) NOT NULL,
	[telefon1] [char](15) NOT NULL,
	[telefon2] [char](15) NOT NULL,
	[telefon3] [char](15) NOT NULL,
	[fax] [char](15) NOT NULL,
	[ucet] [char](15) NOT NULL,
	[banka] [varchar](50) NOT NULL,
	[ico] [char](10) NOT NULL,
	[dic] [char](12) NOT NULL,
	[dph] [tinyint] NOT NULL,
	[jmeno1] [char](20) NOT NULL,
	[jmeno2] [char](20) NOT NULL,
	[jmeno3] [char](20) NOT NULL,
	[znak] [char](12) NOT NULL,
	[mail] [varchar](220) NOT NULL,
	[zuziv] [char](10) NOT NULL,
	[zcas] [smalldatetime] NULL,
	[fix] [int] NOT NULL,
	[ldan] [bit] NOT NULL,
	[pozn] [text] NULL,
	[vipcena] [numeric](6, 2) NOT NULL,
 CONSTRAINT [PK__rdistr__398D8EEE] PRIMARY KEY CLUSTERED
(
	[rdistrID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
;
/****** Object:  Table [dbo].[rdoklad]    Script Date: 28.06.2023 10:01:15 ******/
SET ANSI_NULLS ON
;
SET QUOTED_IDENTIFIER ON
;
CREATE TABLE [dbo].[rdoklad](
	[rdokladID] [int] IDENTITY(1,1) NOT NULL,
	[rpoklID] [int] NOT NULL,
	[rfilmID] [int] NOT NULL,
	[pokladna] [char](10) NOT NULL,
	[ruzivID] [int] NOT NULL,
	[vazba] [bit] NOT NULL,
	[lhoto] [bit] NOT NULL,
	[ltisk] [bit] NOT NULL,
	[dat_vys] [smalldatetime] NOT NULL,
	[dat_dod] [smalldatetime] NULL,
	[dat_dph] [smalldatetime] NULL,
	[dat_spl] [smalldatetime] NULL,
	[doklad] [char](12) NOT NULL,
	[reference] [char](15) NOT NULL,
	[varsymb] [char](10) NOT NULL,
	[pohyb] [char](2) NOT NULL,
	[rodbID] [int] NOT NULL,
	[pocet] [smallint] NOT NULL,
	[zaklad]  AS ([celkem]-[dph]),
	[dph] [numeric](9, 2) NOT NULL,
	[celkem] [numeric](11, 2) NOT NULL,
	[typ] [char](1) NOT NULL,
	[pozn] [varchar](50) NOT NULL,
	[zuziv] [char](10) NOT NULL,
	[zcas] [smalldatetime] NULL,
 CONSTRAINT [PK__rdoklad__1B0907CE] PRIMARY KEY CLUSTERED
(
	[rdokladID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
;
/****** Object:  Table [dbo].[rezervace]    Script Date: 28.06.2023 10:01:15 ******/
SET ANSI_NULLS ON
;
SET QUOTED_IDENTIFIER ON
;
CREATE TABLE [dbo].[rezervace](
	[rezervaceID] [int] IDENTITY(1,1) NOT NULL,
	[rprogID] [int] NOT NULL,
	[datrez] [smalldatetime] NULL,
	[porad] [smalldatetime] NOT NULL,
	[dokdy] [smalldatetime] NULL,
	[jmeno] [varchar](20) NOT NULL,
	[popis] [varchar](50) NOT NULL,
	[pocet] [smallint] NOT NULL,
	[zuziv] [nchar](10) NOT NULL,
	[zcas] [smalldatetime] NULL,
	[okula] [numeric](6, 2) NOT NULL,
PRIMARY KEY CLUSTERED
(
	[rezervaceID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
;
/****** Object:  Table [dbo].[rezslev]    Script Date: 28.06.2023 10:01:15 ******/
SET ANSI_NULLS ON
;
SET QUOTED_IDENTIFIER ON
;
CREATE TABLE [dbo].[rezslev](
	[rezslevID] [int] IDENTITY(1,1) NOT NULL,
	[rezervaceID] [int] NOT NULL,
	[rsedadlaID] [int] NOT NULL,
	[sleva] [char](8) NOT NULL,
	[karta] [char](12) NOT NULL,
	[zcas] [smalldatetime] NULL,
	[okula] [numeric](6, 2) NULL,
	[voucher] [char](10) NOT NULL,
	[cenkat] [tinyint] NOT NULL,
PRIMARY KEY CLUSTERED
(
	[rezslevID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
;
/****** Object:  Table [dbo].[rfilm]    Script Date: 28.06.2023 10:01:15 ******/
SET ANSI_NULLS ON
;
SET QUOTED_IDENTIFIER ON
;
CREATE TABLE [dbo].[rfilm](
	[rfilmID] [int] IDENTITY(1,1) NOT NULL,
	[distr] [char](4) NOT NULL,
	[poradatel] [char](6) NOT NULL,
	[cisfilm] [char](6) NOT NULL,
	[nazevf] [varchar](50) NOT NULL,
	[ornazevf] [varchar](45) NOT NULL,
	[prod] [char](2) NOT NULL,
	[datprem] [datetime] NULL,
	[monopol] [datetime] NULL,
	[zanrf] [char](2) NOT NULL,
	[pristup] [char](5) NOT NULL,
	[minf] [char](3) NOT NULL,
	[uprava] [char](15) NOT NULL,
	[producent] [char](40) NOT NULL,
	[rezie] [char](30) NOT NULL,
	[scenar] [char](30) NOT NULL,
	[kamera] [char](30) NOT NULL,
	[strih] [char](30) NOT NULL,
	[hudba] [char](30) NOT NULL,
	[hraji] [text] NULL,
	[slogan] [text] NULL,
	[popis] [text] NULL,
	[cisfilm_u] [int] NOT NULL,
	[zuziv] [char](10) NOT NULL,
	[zcas] [smalldatetime] NULL,
	[cenkat] [nchar](2) NOT NULL,
	[nlist] [smallint] NOT NULL,
	[kodupravy] [char](1) NOT NULL,
	[form_2d] [bit] NOT NULL,
	[form_3d] [bit] NOT NULL,
	[cisfilmUFD] [char](10) NOT NULL,
	[Jazyk] [char](4) NOT NULL,
	[cform] [char](2) NOT NULL,
	[cfotrm] [char](2) NOT NULL,
PRIMARY KEY CLUSTERED
(
	[rfilmID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
;
/****** Object:  Table [dbo].[rkin]    Script Date: 28.06.2023 10:01:15 ******/
SET ANSI_NULLS ON
;
SET QUOTED_IDENTIFIER ON
;
CREATE TABLE [dbo].[rkin](
	[rkinID] [int] IDENTITY(1,1) NOT NULL,
	[csalu] [char](6) NOT NULL,
	[nazev] [varchar](40) NOT NULL,
	[kapacita] [smallint] NOT NULL,
	[misto] [varchar](30) NOT NULL,
	[ulice] [varchar](40) NOT NULL,
	[posta] [varchar](40) NOT NULL,
	[csdk] [varchar](30) NOT NULL,
	[vedouci] [varchar](25) NOT NULL,
	[nazevp1] [varchar](20) NOT NULL,
	[nazevp2] [varchar](20) NOT NULL,
	[tech] [smalldatetime] NULL,
	[tel] [char](20) NOT NULL,
	[fax] [char](20) NOT NULL,
	[mob] [char](20) NOT NULL,
	[email] [varchar](50) NOT NULL,
	[banka] [varchar](30) NOT NULL,
	[cuctu] [char](20) NOT NULL,
	[ico] [char](15) NOT NULL,
	[dic] [char](15) NOT NULL,
	[letni] [bit] NOT NULL,
	[zuziv] [char](10) NOT NULL,
	[zcas] [smalldatetime] NULL,
	[ldan] [bit] NOT NULL,
	[pozn] [varchar](50) NOT NULL,
PRIMARY KEY CLUSTERED
(
	[rkinID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
;
/****** Object:  Table [dbo].[rklic]    Script Date: 28.06.2023 10:01:15 ******/
SET ANSI_NULLS ON
;
SET QUOTED_IDENTIFIER ON
;
CREATE TABLE [dbo].[rklic](
	[rklicID] [smallint] IDENTITY(1,1) NOT NULL,
	[cilic] [char](6) NOT NULL,
	[sn] [char](7) NOT NULL,
	[zarizeni] [char](6) NOT NULL,
	[provoz] [char](15) NOT NULL,
	[appl] [char](8) NOT NULL,
	[letni] [bit] NOT NULL,
	[dan] [tinyint] NOT NULL,
	[ddan] [tinyint] NOT NULL,
	[zdan] [tinyint] NOT NULL,
	[skuzby] [smallint] NOT NULL,
	[casprod] [smallint] NOT NULL,
	[osa] [numeric](5, 2) NOT NULL,
	[osaproc] [numeric](5, 2) NOT NULL,
	[pobocka] [smallint] NOT NULL,
	[int_prog] [bit] NOT NULL,
	[int_rez] [bit] NOT NULL,
	[int_user] [char](10) NOT NULL,
	[int_pasw] [char](10) NOT NULL,
	[verze] [char](15) NOT NULL,
	[typ_vst] [char](1) NOT NULL,
	[typ_tisk] [char](1) NOT NULL,
	[fondkin] [bit] NOT NULL,
	[fondkc] [numeric](5, 2) NOT NULL,
	[fondproc] [numeric](5, 2) NOT NULL,
	[odrezerv] [int] NOT NULL,
	[zaokrouhl] [tinyint] NOT NULL,
	[ipmask] [char](15) NOT NULL,
	[pujcovne] [smallint] NOT NULL,
	[sluzbyDbox] [numeric](6, 2) NOT NULL,
	[sluzbyVIP] [numeric](6, 2) NOT NULL,
	[priplatekVIP] [numeric](6, 2) NOT NULL,
	[sluzbyPremium] [numeric](6, 2) NOT NULL,
	[priplatekPremium] [numeric](6, 2) NOT NULL,
	[sluzbyImax] [numeric](6, 2) NOT NULL,
	[priplatekImax] [numeric](6, 2) NOT NULL,
	[sluzbyUltraX] [numeric](6, 2) NOT NULL,
	[priplatekUltraX] [numeric](6, 2) NOT NULL,
	[vyslheslo] [varchar](20) NOT NULL,
PRIMARY KEY CLUSTERED
(
	[rklicID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
;
/****** Object:  Table [dbo].[rkurz]    Script Date: 28.06.2023 10:01:15 ******/
SET ANSI_NULLS ON
;
SET QUOTED_IDENTIFIER ON
;
CREATE TABLE [dbo].[rkurz](
	[rkurzID] [int] IDENTITY(1,1) NOT NULL,
	[nazev] [varchar](40) NOT NULL,
	[znak] [char](5) NOT NULL,
	[kurz] [numeric](10, 3) NOT NULL,
	[zuziv] [char](10) NOT NULL,
	[zcas] [smalldatetime] NOT NULL,
	[mnozs] [smallint] NOT NULL,
	[znISO] [char](3) NOT NULL,
PRIMARY KEY CLUSTERED
(
	[rkurzID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
;
/****** Object:  Table [dbo].[rliga]    Script Date: 28.06.2023 10:01:15 ******/
SET ANSI_NULLS ON
;
SET QUOTED_IDENTIFIER ON
;
CREATE TABLE [dbo].[rliga](
	[rligaID] [smallint] IDENTITY(1,1) NOT NULL,
	[cag] [char](2) NOT NULL,
	[nazag] [char](30) NOT NULL,
	[znak] [char](2) NOT NULL,
	[zuziv] [char](10) NOT NULL,
	[zcas] [smalldatetime] NULL,
PRIMARY KEY CLUSTERED
(
	[rligaID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
;
/****** Object:  Table [dbo].[rlistIe]    Script Date: 28.06.2023 10:01:15 ******/
SET ANSI_NULLS ON
;
SET QUOTED_IDENTIFIER ON
;
CREATE TABLE [dbo].[rlistIe](
	[rlistIeID] [int] IDENTITY(1,1) NOT NULL,
	[rlistkyID] [int] NOT NULL,
	[datum] [datetime] NULL,
	[vs] [char](10) NOT NULL,
	[res] [char](5) NOT NULL,
	[ac] [char](10) NOT NULL,
	[lsig] [char](16) NOT NULL,
	[zuziv] [char](10) NOT NULL,
	[zcas] [smalldatetime] NULL,
	[ku] [int] NOT NULL,
PRIMARY KEY CLUSTERED
(
	[rlistIeID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
;
/****** Object:  Table [dbo].[rlistky]    Script Date: 28.06.2023 10:01:15 ******/
SET ANSI_NULLS ON
;
SET QUOTED_IDENTIFIER ON
;
CREATE TABLE [dbo].[rlistky](
	[rlistkyID] [int] IDENTITY(1,1) NOT NULL,
	[rsedadlaID] [int] NOT NULL,
	[rprogID] [int] NOT NULL,
	[rpoklID] [int] NOT NULL,
	[rdokladID] [int] NOT NULL,
	[rodbID] [int] NOT NULL,
	[csalu] [char](6) NOT NULL,
	[sekce] [char](3) NOT NULL,
	[doklad] [char](10) NOT NULL,
	[cena] [numeric](6, 2) NOT NULL,
	[sluzby] [numeric](6, 2) NOT NULL,
	[fond] [numeric](4, 2) NOT NULL,
	[sp] [numeric](5, 2) NOT NULL,
	[sleva] [char](8) NOT NULL,
	[lvrat] [bit] NOT NULL,
	[lhoto] [bit] NOT NULL,
	[labo] [bit] NOT NULL,
	[datprod] [smalldatetime] NULL,
	[datpokl] [smalldatetime] NULL,
	[uzivprod] [int] NOT NULL,
	[uzivpokl] [int] NOT NULL,
	[pokladna] [char](10) NOT NULL,
	[rada] [char](5) NOT NULL,
	[misto] [char](5) NOT NULL,
	[pozn] [char](10) NOT NULL,
	[zuziv] [char](10) NOT NULL,
	[zcas] [smalldatetime] NULL,
	[vstup] [smallint] NOT NULL,
	[karta] [char](12) NULL,
	[rmustrID] [int] NOT NULL,
	[sluzbyDbox] [numeric](6, 2) NOT NULL,
	[okula] [numeric](6, 2) NOT NULL,
	[sluzbyVIP] [numeric](6, 2) NOT NULL,
	[priplatekVIP] [numeric](6, 2) NOT NULL,
	[sluzbyPremium] [numeric](6, 2) NOT NULL,
	[priplatekPremium] [numeric](6, 2) NOT NULL,
	[sluzbyImax] [numeric](6, 2) NOT NULL,
	[priplatekImax] [numeric](6, 2) NOT NULL,
	[vouncher] [char](10) NOT NULL,
	[hodslevy] [numeric](6, 2) NOT NULL,
	[cenkat] [tinyint] NOT NULL,
	[sluzbyUltraX] [numeric](6, 2) NOT NULL,
	[priplatekUltraX] [numeric](6, 2) NOT NULL,
 CONSTRAINT [PK__rlistky__267ABA7A] PRIMARY KEY CLUSTERED
(
	[rlistkyID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
;
/****** Object:  Table [dbo].[rlistkyRus]    Script Date: 28.06.2023 10:01:15 ******/
SET ANSI_NULLS ON
;
SET QUOTED_IDENTIFIER ON
;
CREATE TABLE [dbo].[rlistkyRus](
	[rlistkyRusID] [int] IDENTITY(1,1) NOT NULL,
	[rlistkyID] [int] NOT NULL,
	[rsedadlaID] [int] NOT NULL,
	[rprogID] [int] NOT NULL,
	[rpoklID] [int] NOT NULL,
	[rdokladID] [int] NOT NULL,
	[rodbID] [int] NOT NULL,
	[csalu] [char](6) NOT NULL,
	[sekce] [char](3) NOT NULL,
	[doklad] [char](10) NOT NULL,
	[cena] [numeric](6, 2) NOT NULL,
	[sluzby] [numeric](6, 2) NOT NULL,
	[fond] [numeric](4, 2) NOT NULL,
	[sp] [numeric](5, 2) NOT NULL,
	[sleva] [char](8) NOT NULL,
	[lvrat] [bit] NOT NULL,
	[lhoto] [bit] NOT NULL,
	[labo] [bit] NOT NULL,
	[datprod] [smalldatetime] NULL,
	[datpokl] [smalldatetime] NULL,
	[uzivprod] [int] NOT NULL,
	[uzivpokl] [int] NOT NULL,
	[pokladna] [char](10) NOT NULL,
	[rada] [char](5) NOT NULL,
	[misto] [char](5) NOT NULL,
	[pozn] [char](10) NOT NULL,
	[zuziv] [char](10) NULL,
	[zcas] [smalldatetime] NULL,
	[karta] [char](8) NOT NULL,
	[sluzbyDbox] [numeric](6, 2) NOT NULL,
 CONSTRAINT [PK__rlistkyRus__286302EC] PRIMARY KEY CLUSTERED
(
	[rlistkyRusID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
;
/****** Object:  Table [dbo].[rmustr]    Script Date: 28.06.2023 10:01:15 ******/
SET ANSI_NULLS ON
;
SET QUOTED_IDENTIFIER ON
;
CREATE TABLE [dbo].[rmustr](
	[rmustrID] [int] IDENTITY(1,1) NOT NULL,
	[rupravaID] [int] NOT NULL,
	[rodbID] [int] NOT NULL,
	[csalu] [char](6) NOT NULL,
	[sekce] [char](3) NOT NULL,
	[typ] [char](1) NOT NULL,
	[jmeno] [char](10) NOT NULL,
	[popis] [varchar](50) NOT NULL,
	[vlevo] [smallint] NOT NULL,
	[vrch] [smallint] NOT NULL,
	[sirka] [smallint] NOT NULL,
	[vyska] [smallint] NOT NULL,
	[rada] [smallint] NOT NULL,
	[sloup] [smallint] NOT NULL,
	[oznr] [char](4) NOT NULL,
	[podsekce] [char](15) NOT NULL,
	[umisteni] [char](15) NOT NULL,
	[oznk] [char](5) NOT NULL,
	[oznd] [char](5) NOT NULL,
	[oznl] [char](2) NOT NULL,
	[fontV] [tinyint] NOT NULL,
	[tucne] [bit] NOT NULL,
	[cursiva] [bit] NOT NULL,
	[barvav] [int] NOT NULL,
	[barvas] [int] NOT NULL,
	[cena] [tinyint] NOT NULL,
	[delegace] [tinyint] NOT NULL,
	[zobrazit] [bit] NOT NULL,
	[tol] [smallint] NOT NULL,
	[ceny] [varchar](51) NOT NULL,
	[zuziv] [char](10) NOT NULL,
	[zcas] [smalldatetime] NULL,
	[levadvoj] [bit] NOT NULL,
	[pravadvoj] [bit] NOT NULL,
	[coordsLeft] [int] NULL,
	[coordsTop] [int] NULL,
	[breakPosition] [varchar](255) NULL,
PRIMARY KEY NONCLUSTERED
(
	[rmustrID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
;
SET ANSI_PADDING ON
;
/****** Object:  Index [prodej]    Script Date: 28.06.2023 10:01:15 ******/
CREATE CLUSTERED INDEX [prodej] ON [dbo].[rmustr]
(
	[rupravaID] ASC,
	[sekce] ASC,
	[delegace] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
;
/****** Object:  Table [dbo].[rodb]    Script Date: 28.06.2023 10:01:15 ******/
SET ANSI_NULLS ON
;
SET QUOTED_IDENTIFIER ON
;
CREATE TABLE [dbo].[rodb](
	[rodbID] [int] IDENTITY(1,1) NOT NULL,
	[cisodb] [char](6) NOT NULL,
	[odpar] [char](2) NOT NULL,
	[nazevo] [char](26) NOT NULL,
	[ulice] [varchar](40) NOT NULL,
	[misto] [varchar](40) NOT NULL,
	[psc] [char](6) NOT NULL,
	[jmeno1] [varchar](40) NOT NULL,
	[jmeno2] [char](20) NOT NULL,
	[jmeno3] [char](20) NOT NULL,
	[telefon1] [char](20) NOT NULL,
	[telefon2] [char](20) NOT NULL,
	[telefon3] [char](20) NOT NULL,
	[fax] [char](20) NOT NULL,
	[email] [varchar](70) NOT NULL,
	[banka] [varchar](40) NOT NULL,
	[ucet] [char](15) NOT NULL,
	[ico] [char](15) NOT NULL,
	[dic] [char](15) NOT NULL,
	[funkce1] [varchar](30) NOT NULL,
	[vjezd] [bit] NOT NULL,
	[zuziv] [char](10) NOT NULL,
	[zcas] [smalldatetime] NULL,
 CONSTRAINT [PK__rodb__3C69FB99] PRIMARY KEY CLUSTERED
(
	[rodbID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
;
/****** Object:  Table [dbo].[rpokl]    Script Date: 28.06.2023 10:01:15 ******/
SET ANSI_NULLS ON
;
SET QUOTED_IDENTIFIER ON
;
CREATE TABLE [dbo].[rpokl](
	[rpoklID] [int] IDENTITY(1,1) NOT NULL,
	[pokladna] [char](10) NOT NULL,
	[doklad] [char](4) NOT NULL,
	[datum] [smalldatetime] NOT NULL,
	[pocstav] [numeric](12, 2) NOT NULL,
	[prodejho] [numeric](10, 2) NOT NULL,
	[prodejnh] [numeric](10, 2) NOT NULL,
	[sluzbyho] [numeric](10, 2) NOT NULL,
	[sluzbynh] [numeric](10, 2) NOT NULL,
	[spho] [numeric](10, 2) NOT NULL,
	[spneho] [numeric](10, 2) NOT NULL,
	[vracenho] [numeric](8, 2) NOT NULL,
	[vracennh] [numeric](8, 2) NOT NULL,
	[oprijem] [numeric](10, 2) NOT NULL,
	[ovydej] [numeric](10, 2) NOT NULL,
	[odvod] [numeric](11, 2) NOT NULL,
	[oprijemNh] [numeric](10, 2) NOT NULL,
	[ovydejNh] [numeric](10, 2) NOT NULL,
	[odvodNh] [numeric](11, 2) NOT NULL,
	[zust_den]  AS ((((((([prodejho]+[prodejnh])+[sluzbyho])+[sluzbynh])-[vracenho])-[vracennh])+[oprijem])-[ovydej]),
	[zust_pok]  AS (((((([pocstav]+[prodejho])+[sluzbyho])-[vracenho])+[oprijem])-[ovydej])-[odvod]),
	[ruzivID] [int] NOT NULL,
	[zuziv] [char](10) NOT NULL,
	[zcas] [smalldatetime] NULL,
 CONSTRAINT [PK__rpokl__2E1BDC42] PRIMARY KEY CLUSTERED
(
	[rpoklID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
;
/****** Object:  Table [dbo].[rpoukazy]    Script Date: 28.06.2023 10:01:15 ******/
SET ANSI_NULLS ON
;
SET QUOTED_IDENTIFIER ON
;
CREATE TABLE [dbo].[rpoukazy](
	[rpoukazyID] [int] IDENTITY(1,1) NOT NULL,
	[rad1] [varchar](40) NOT NULL,
	[rad2] [varchar](40) NULL,
	[rad3] [varchar](40) NOT NULL,
	[rad4] [varchar](40) NOT NULL,
	[zuziv] [char](10) NOT NULL,
	[zcas] [smalldatetime] NULL,
PRIMARY KEY CLUSTERED
(
	[rpoukazyID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
;
/****** Object:  Table [dbo].[rprod]    Script Date: 28.06.2023 10:01:15 ******/
SET ANSI_NULLS ON
;
SET QUOTED_IDENTIFIER ON
;
CREATE TABLE [dbo].[rprod](
	[rprodID] [int] IDENTITY(1,1) NOT NULL,
	[ciszeme] [char](2) NOT NULL,
	[kodzeme] [char](4) NOT NULL,
	[nazev] [char](26) NOT NULL,
	[eu] [bit] NOT NULL,
	[zuziv] [char](10) NOT NULL,
	[zcas] [smalldatetime] NULL,
PRIMARY KEY CLUSTERED
(
	[rprodID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
;
/****** Object:  Table [dbo].[rprog]    Script Date: 28.06.2023 10:01:15 ******/
SET ANSI_NULLS ON
;
SET QUOTED_IDENTIFIER ON
;
CREATE TABLE [dbo].[rprog](
	[rprogID] [int] IDENTITY(1,1) NOT NULL,
	[rupravaID] [int] NOT NULL,
	[rfilmID] [int] NOT NULL,
	[rvstID] [int] NOT NULL,
	[csalu] [char](6) NOT NULL,
	[porad] [smalldatetime] NULL,
	[distr] [char](4) NOT NULL,
	[ciskop] [char](4) NOT NULL,
	[limit] [smallint] NOT NULL,
	[pujcovne] [numeric](4, 2) NOT NULL,
	[cenfix] [smallmoney] NOT NULL,
	[cenmin] [smallmoney] NOT NULL,
	[cenkat] [char](3) NOT NULL,
	[osa] [numeric](5, 2) NOT NULL,
	[osaproc] [bit] NOT NULL,
	[fk] [numeric](5, 2) NOT NULL,
	[fkproc] [bit] NOT NULL,
	[sp] [numeric](5, 2) NOT NULL,
	[spproc] [bit] NOT NULL,
	[ps] [bit] NOT NULL,
	[priplsluz] [numeric](6, 2) NOT NULL,
	[negraficky] [bit] NOT NULL,
	[naweb] [bit] NOT NULL,
	[stop] [bit] NOT NULL,
	[odpadlo] [bit] NOT NULL,
	[neucast] [bit] NOT NULL,
	[divaku] [smallint] NOT NULL,
	[trzba] [numeric](9, 2) NOT NULL,
	[rezervace] [smallint] NOT NULL,
	[fondcel] [numeric](9, 2) NOT NULL,
	[priplcel] [numeric](9, 2) NOT NULL,
	[odrezerv] [bit] NOT NULL,
	[kdyodrezerv] [smalldatetime] NULL,
	[cbanID] [smallint] NOT NULL,
	[pohhot] [char](2) NOT NULL,
	[pohfakt] [char](2) NOT NULL,
	[pohzal] [char](2) NOT NULL,
	[delegace] [int] NOT NULL,
	[casprod] [smallint] NOT NULL,
	[pozn] [text] NULL,
	[zuziv] [char](10) NOT NULL,
	[zcas] [smalldatetime] NULL,
	[neikar] [bit] NOT NULL,
	[rpoukazyID] [int] NOT NULL,
	[cistyp] [char](3) NOT NULL,
	[rcsluzby] [char](2) NOT NULL,
	[sluzbyDbox] [numeric](6, 2) NOT NULL,
	[sluzbyVIP] [numeric](6, 2) NOT NULL,
	[priplatekVIP] [numeric](6, 2) NOT NULL,
	[sluzbyPremium] [numeric](6, 2) NOT NULL,
	[priplatekPremium] [numeric](6, 2) NOT NULL,
	[sluzbyImax] [numeric](6, 2) NOT NULL,
	[priplatekImax] [numeric](6, 2) NOT NULL,
	[cistyp2] [char](3) NOT NULL,
	[cistyp3] [char](3) NOT NULL,
	[cistyp4] [char](3) NOT NULL,
	[vs] [char](10) NOT NULL,
	[jazyk] [char](4) NOT NULL,
	[cfotrm] [char](2) NOT NULL,
	[sluzbyUltraX] [numeric](6, 2) NOT NULL,
	[priplatekUltraX] [numeric](6, 2) NOT NULL,
 CONSTRAINT [PK__rprog__7CD98669] PRIMARY KEY CLUSTERED
(
	[rprogID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
;
/****** Object:  Table [dbo].[rprovoz]    Script Date: 28.06.2023 10:01:15 ******/
SET ANSI_NULLS ON
;
SET QUOTED_IDENTIFIER ON
;
CREATE TABLE [dbo].[rprovoz](
	[rprovozID] [int] IDENTITY(1,1) NOT NULL,
	[sfirma] [varchar](40) NOT NULL,
	[spsc] [char](6) NOT NULL,
	[smisto] [varchar](40) NOT NULL,
	[sulice] [varchar](40) NOT NULL,
	[szkrf] [char](10) NOT NULL,
	[sreg] [varchar](40) NOT NULL,
	[sico] [char](15) NOT NULL,
	[stel] [char](15) NOT NULL,
	[sdic] [char](15) NOT NULL,
	[semail] [varchar](40) NOT NULL,
	[svyrizuje] [varchar](30) NOT NULL,
	[sbanka] [char](30) NOT NULL,
	[sfax] [char](15) NOT NULL,
	[sucet] [char](35) NOT NULL,
	[sreztab] [bit] NOT NULL,
	[sprecen] [bit] NOT NULL,
	[sDPH] [tinyint] NOT NULL,
 CONSTRAINT [PK__rprovoz__0880433F] PRIMARY KEY CLUSTERED
(
	[rprovozID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
;
/****** Object:  Table [dbo].[rsedadla]    Script Date: 28.06.2023 10:01:15 ******/
SET ANSI_NULLS ON
;
SET QUOTED_IDENTIFIER ON
;
CREATE TABLE [dbo].[rsedadla](
	[rsedadlaID] [int] IDENTITY(1,1) NOT NULL,
	[rprogID] [int] NOT NULL,
	[rmustrID] [int] NOT NULL,
	[rezervaceID] [int] NOT NULL,
	[csalu] [char](6) NOT NULL,
	[porad] [smalldatetime] NULL,
	[ceny] [varchar](51) NOT NULL,
	[cenkat] [tinyint] NOT NULL,
	[delegace] [tinyint] NOT NULL,
	[delegold] [tinyint] NOT NULL,
	[datprod] [smalldatetime] NULL,
	[cena] [numeric](6, 2) NOT NULL,
	[uzivprod] [int] NOT NULL,
	[ruzivID] [int] NOT NULL,
	[zuziv] [char](10) NOT NULL,
	[zcas] [datetime] NULL,
	[sleva] [char](8) NOT NULL,
 CONSTRAINT [PK__rsedadla__412EB0B6] PRIMARY KEY CLUSTERED
(
	[rsedadlaID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
;
/****** Object:  Table [dbo].[rsedadlaRus]    Script Date: 28.06.2023 10:01:15 ******/
SET ANSI_NULLS ON
;
SET QUOTED_IDENTIFIER ON
;
CREATE TABLE [dbo].[rsedadlaRus](
	[rsedadlaID] [int] NOT NULL,
	[rprogID] [int] NOT NULL,
	[rmustrID] [int] NOT NULL,
	[rezervaceID] [int] NOT NULL,
	[csalu] [char](6) NOT NULL,
	[porad] [smalldatetime] NULL,
	[ceny] [varchar](51) NOT NULL,
	[cenkat] [tinyint] NOT NULL,
	[delegace] [tinyint] NOT NULL,
	[delegold] [tinyint] NOT NULL,
	[datprod] [smalldatetime] NULL,
	[cena] [numeric](6, 2) NOT NULL,
	[uzivprod] [int] NOT NULL,
	[ruzivID] [int] NOT NULL,
	[zuziv] [char](10) NOT NULL,
	[zcas] [datetime] NULL,
	[sleva] [char](8) NOT NULL,
PRIMARY KEY CLUSTERED
(
	[rsedadlaID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
;
/****** Object:  Table [dbo].[rsekce]    Script Date: 28.06.2023 10:01:15 ******/
SET ANSI_NULLS ON
;
SET QUOTED_IDENTIFIER ON
;
CREATE TABLE [dbo].[rsekce](
	[rsekceID] [int] IDENTITY(1,1) NOT NULL,
	[csalu] [char](6) NOT NULL,
	[sekce] [char](3) NOT NULL,
	[nazev] [char](30) NOT NULL,
	[pc] [int] NOT NULL,
	[druh] [char](1) NOT NULL,
	[negraficky] [bit] NOT NULL,
	[limit] [smallint] NOT NULL,
	[vlevo] [smallint] NOT NULL,
	[vrch] [smallint] NOT NULL,
	[sirka] [smallint] NOT NULL,
	[vyska] [smallint] NOT NULL,
	[fontV] [tinyint] NOT NULL,
	[tucne] [bit] NOT NULL,
	[cursiva] [bit] NOT NULL,
	[col1] [int] NOT NULL,
	[col2] [int] NOT NULL,
	[col1d] [int] NOT NULL,
	[col2d] [int] NOT NULL,
	[dalsi] [text] NULL,
	[zuziv] [char](10) NOT NULL,
	[zcas] [smalldatetime] NULL,
 CONSTRAINT [PK__rsekce__37A5467C] PRIMARY KEY CLUSTERED
(
	[rsekceID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
;
/****** Object:  Table [dbo].[rslev]    Script Date: 28.06.2023 10:01:15 ******/
SET ANSI_NULLS ON
;
SET QUOTED_IDENTIFIER ON
;
CREATE TABLE [dbo].[rslev](
	[rslevID] [int] IDENTITY(1,1) NOT NULL,
	[cislo] [char](8) NOT NULL,
	[sleva] [numeric](7, 2) NOT NULL,
	[lproc] [bit] NOT NULL,
	[prijmeni] [char](20) NOT NULL,
	[jmeno] [char](10) NOT NULL,
	[adrmis] [char](20) NOT NULL,
	[adrul] [char](20) NOT NULL,
	[psc] [char](6) NOT NULL,
	[telef] [char](15) NOT NULL,
	[zuziv] [char](10) NOT NULL,
	[zcas] [smalldatetime] NULL,
	[lpcena] [bit] NOT NULL,
	[lnetisk] [bit] NOT NULL,
	[pocslev] [int] NOT NULL,
	[lnest] [bit] NOT NULL,
	[sleva2] [char](8) NOT NULL,
	[aktivni] [bit] NOT NULL,
	[liver] [bit] NOT NULL,
	[liver_3d] [bit] NOT NULL,
	[liver_po] [bit] NOT NULL,
	[pc] [int] NOT NULL,
	[pocpo] [int] NOT NULL,
	[nulsluzby] [bit] NOT NULL,
	[jenvoucher] [bit] NOT NULL,
	[pocZaNula] [int] NOT NULL,
 CONSTRAINT [PK__rslev__74AE54BC] PRIMARY KEY CLUSTERED
(
	[rslevID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
;
/****** Object:  Table [dbo].[rslevp]    Script Date: 28.06.2023 10:01:15 ******/
SET ANSI_NULLS ON
;
SET QUOTED_IDENTIFIER ON
;
CREATE TABLE [dbo].[rslevp](
	[rslevpID] [int] IDENTITY(1,1) NOT NULL,
	[cislo] [char](5) NOT NULL,
	[sleva] [numeric](7, 2) NOT NULL,
	[lproc] [bit] NOT NULL,
	[prijmeni] [char](20) NOT NULL,
	[jmeno] [char](10) NOT NULL,
	[adrmis] [char](20) NOT NULL,
	[adrul] [char](20) NOT NULL,
	[psc] [char](6) NOT NULL,
	[telef] [char](15) NOT NULL,
	[zuziv] [char](10) NOT NULL,
	[zcas] [smalldatetime] NULL,
	[aktivni] [bit] NOT NULL,
 CONSTRAINT [PK__rslevp__46E78A0C] PRIMARY KEY CLUSTERED
(
	[rslevpID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
;
/****** Object:  Table [dbo].[ruprava]    Script Date: 28.06.2023 10:01:15 ******/
SET ANSI_NULLS ON
;
SET QUOTED_IDENTIFIER ON
;
CREATE TABLE [dbo].[ruprava](
	[rupravaID] [int] IDENTITY(1,1) NOT NULL,
	[ruprava2ID] [int] NOT NULL,
	[csalu] [char](6) NOT NULL,
	[cislo] [char](2) NOT NULL,
	[nazev] [varchar](40) NOT NULL,
	[popis] [text] NULL,
	[nabo] [smallint] NOT NULL,
	[zuziv] [char](10) NOT NULL,
	[zcas] [smalldatetime] NULL,
 CONSTRAINT [PK__ruprava__44FF419A] PRIMARY KEY CLUSTERED
(
	[rupravaID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
;
/****** Object:  Table [dbo].[ruziv]    Script Date: 28.06.2023 10:01:15 ******/
SET ANSI_NULLS ON
;
SET QUOTED_IDENTIFIER ON
;
CREATE TABLE [dbo].[ruziv](
	[ruzivID] [smallint] IDENTITY(1,1) NOT NULL,
	[uziv] [char](10) NOT NULL,
	[Jmeno] [char](40) NOT NULL,
	[Heslo] [char](10) NOT NULL,
	[zapis] [bit] NOT NULL,
	[pokladna] [char](10) NOT NULL,
	[datOd] [datetime] NULL,
	[datDo] [datetime] NULL,
	[datPrist] [smalldatetime] NULL,
	[pokD] [int] NOT NULL,
	[pokZ] [int] NOT NULL,
	[uzpokl] [smalldatetime] NULL,
	[prace] [char](12) NOT NULL,
	[vstup] [bit] NOT NULL,
	[old] [int] NOT NULL,
	[nlist] [smallint] NOT NULL,
	[nabo] [smallint] NOT NULL,
	[zuziv] [char](10) NOT NULL,
	[zcas] [smalldatetime] NULL,
	[linternet] [bit] NOT NULL,
 CONSTRAINT [PK__ruziv__45F365D3] PRIMARY KEY CLUSTERED
(
	[ruzivID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
;
/****** Object:  Table [dbo].[rvst]    Script Date: 28.06.2023 10:01:15 ******/
SET ANSI_NULLS ON
;
SET QUOTED_IDENTIFIER ON
;
CREATE TABLE [dbo].[rvst](
	[rvstID] [smallint] IDENTITY(1,1) NOT NULL,
	[csalu] [char](6) NOT NULL,
	[distr] [nchar](4) NOT NULL,
	[kat] [char](3) NOT NULL,
	[nazev] [nchar](15) NOT NULL,
	[rkurzID1] [int] NOT NULL,
	[rkurzID2] [int] NOT NULL,
	[rkurzID3] [int] NOT NULL,
	[rkurzID4] [int] NOT NULL,
	[rkurzID5] [int] NOT NULL,
	[rkurzID6] [int] NOT NULL,
	[vst1] [smallmoney] NOT NULL,
	[nazev1] [varchar](15) NOT NULL,
	[vst2] [smallmoney] NOT NULL,
	[nazev2] [varchar](15) NOT NULL,
	[vst3] [smallmoney] NOT NULL,
	[nazev3] [varchar](15) NOT NULL,
	[vst4] [smallmoney] NOT NULL,
	[nazev4] [varchar](15) NOT NULL,
	[vst5] [smallmoney] NOT NULL,
	[nazev5] [varchar](15) NOT NULL,
	[vst6] [smallmoney] NOT NULL,
	[nazev6] [varchar](15) NOT NULL,
	[vst7] [smallmoney] NOT NULL,
	[nazev7] [varchar](15) NOT NULL,
	[vst8] [smallmoney] NOT NULL,
	[nazev8] [varchar](15) NOT NULL,
	[vst9] [smallmoney] NOT NULL,
	[nazev9] [varchar](15) NOT NULL,
	[vst10] [smallmoney] NOT NULL,
	[nazev10] [varchar](15) NOT NULL,
	[vst11] [smallmoney] NOT NULL,
	[nazev11] [varchar](15) NOT NULL,
	[vst12] [smallmoney] NOT NULL,
	[nazev12] [varchar](15) NOT NULL,
	[vst13] [smallmoney] NOT NULL,
	[nazev13] [varchar](15) NOT NULL,
	[vst14] [smallmoney] NOT NULL,
	[nazev14] [varchar](15) NOT NULL,
	[vst15] [smallmoney] NOT NULL,
	[nazev15] [varchar](15) NOT NULL,
	[vst16] [smallmoney] NOT NULL,
	[nazev16] [varchar](15) NOT NULL,
	[vst17] [smallmoney] NOT NULL,
	[nazev17] [varchar](15) NOT NULL,
	[vst18] [smallmoney] NOT NULL,
	[nazev18] [varchar](15) NOT NULL,
	[vst19] [smallmoney] NOT NULL,
	[nazev19] [varchar](15) NOT NULL,
	[vst20] [smallmoney] NOT NULL,
	[nazev20] [varchar](15) NOT NULL,
	[zuziv] [char](10) NOT NULL,
	[zcas] [smalldatetime] NULL,
	[kategorie] [nchar](3) NOT NULL,
	[aktivni] [bit] NOT NULL,
	[sleva2] [char](2) NOT NULL,
	[sleva3] [char](2) NOT NULL,
	[sleva4] [char](2) NOT NULL,
	[sleva19] [char](2) NOT NULL,
	[sleva18] [char](2) NOT NULL,
	[sleva17] [char](2) NOT NULL,
 CONSTRAINT [PK__rvst__37703C52] PRIMARY KEY CLUSTERED
(
	[rvstID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
;
/****** Object:  Table [dbo].[rvtn]    Script Date: 28.06.2023 10:01:15 ******/
SET ANSI_NULLS ON
;
SET QUOTED_IDENTIFIER ON
;
CREATE TABLE [dbo].[rvtn](
	[rvtnID] [int] IDENTITY(1,1) NOT NULL,
	[typ] [char](1) NOT NULL,
	[vtn] [varchar](30) NOT NULL,
	[hodnota] [varchar](50) NOT NULL,
	[typh] [char](1) NOT NULL,
	[pristup] [bit] NOT NULL,
	[kontrola] [varchar](50) NOT NULL,
	[Popis] [text] NULL,
PRIMARY KEY CLUSTERED
(
	[rvtnID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
;
/****** Object:  Table [dbo].[SedWeb]    Script Date: 28.06.2023 10:01:15 ******/
SET ANSI_NULLS ON
;
SET QUOTED_IDENTIFIER ON
;
CREATE TABLE [dbo].[SedWeb](
	[planekKod] [char](6) NOT NULL,
	[planekId] [int] NOT NULL,
	[tol] [smallint] NOT NULL,
	[lux] [smallint] NOT NULL,
	[luy] [smallint] NOT NULL,
	[text] [nchar](3) NOT NULL,
	[row] [char](5) NULL,
	[oznm] [smallint] NULL,
	[ozns] [smallint] NULL,
	[tol2] [int] NULL,
	[levadvoj] [bit] NULL,
	[pravadvoj] [bit] NULL
) ON [PRIMARY]
;
/****** Object:  Table [dbo].[tisks]    Script Date: 28.06.2023 10:01:15 ******/
SET ANSI_NULLS ON
;
SET QUOTED_IDENTIFIER ON
;
CREATE TABLE [dbo].[tisks](
	[tisksID] [int] IDENTITY(1,1) NOT NULL,
	[formul] [varchar](20) NOT NULL,
	[pc] [tinyint] NOT NULL,
	[nazev] [varchar](50) NOT NULL,
	[soubor] [varchar](200) NOT NULL,
	[dalsip] [varchar](100) NULL,
	[zuziv] [nchar](10) NOT NULL,
	[zcas] [smalldatetime] NULL,
PRIMARY KEY CLUSTERED
(
	[tisksID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
;
/****** Object:  Table [dbo].[typvst]    Script Date: 28.06.2023 10:01:15 ******/
SET ANSI_NULLS ON
;
SET QUOTED_IDENTIFIER ON
;
CREATE TABLE [dbo].[typvst](
	[typvstID] [int] IDENTITY(1,1) NOT NULL,
	[nazev] [varchar](30) NOT NULL,
	[definice] [text] NULL,
	[uvodni] [varchar](50) NOT NULL,
	[koncove] [varchar](50) NOT NULL,
	[lhromad] [bit] NOT NULL,
	[lskolni] [bit] NOT NULL,
	[zuziv] [char](10) NOT NULL,
	[zcas] [smalldatetime] NULL,
PRIMARY KEY CLUSTERED
(
	[typvstID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
;
/****** Object:  Table [dbo].[voucher]    Script Date: 28.06.2023 10:01:15 ******/
SET ANSI_NULLS ON
;
SET QUOTED_IDENTIFIER ON
;
CREATE TABLE [dbo].[voucher](
	[voucherID] [int] IDENTITY(1,1) NOT NULL,
	[cislo] [char](10) NOT NULL,
	[typ] [char](15) NOT NULL,
	[maxpo] [int] NOT NULL,
	[pocpo] [int] NOT NULL,
	[platod] [smalldatetime] NULL,
	[platdo] [smalldatetime] NULL,
	[sleva] [char](2) NOT NULL,
	[sleva3d] [char](2) NOT NULL,
	[zuziv] [char](10) NOT NULL,
	[zcas] [smalldatetime] NULL,
PRIMARY KEY CLUSTERED
(
	[voucherID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
;
/****** Object:  Table [dbo].[zaloha]    Script Date: 28.06.2023 10:01:15 ******/
SET ANSI_NULLS ON
;
SET QUOTED_IDENTIFIER ON
;
CREATE TABLE [dbo].[zaloha](
	[zalohaID] [int] IDENTITY(1,1) NOT NULL,
	[databaze] [varchar](50) NOT NULL,
	[ciladr] [varchar](200) NOT NULL,
	[soubor] [varchar](50) NOT NULL,
	[komprim] [bit] NOT NULL,
	[start] [datetime] NULL,
	[konec] [datetime] NULL,
	[stav] [bit] NOT NULL,
	[chyba] [text] NULL,
 CONSTRAINT [PK__zaloha__3C89F72A] PRIMARY KEY CLUSTERED
(
	[zalohaID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
;
/****** Object:  Index [barvy_cenkat]    Script Date: 28.06.2023 10:01:15 ******/
CREATE NONCLUSTERED INDEX [barvy_cenkat] ON [dbo].[rbarvy]
(
	[cenkat] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
;
/****** Object:  Index [barvy_delegace]    Script Date: 28.06.2023 10:01:15 ******/
CREATE NONCLUSTERED INDEX [barvy_delegace] ON [dbo].[rbarvy]
(
	[delegace] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
;
SET ANSI_PADDING ON
;
/****** Object:  Index [rcards_Cislo]    Script Date: 28.06.2023 10:01:15 ******/
CREATE UNIQUE NONCLUSTERED INDEX [rcards_Cislo] ON [dbo].[rcards]
(
	[cislo] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, IGNORE_DUP_KEY = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
;
SET ANSI_PADDING ON
;
/****** Object:  Index [rdi_distr]    Script Date: 28.06.2023 10:01:15 ******/
CREATE UNIQUE NONCLUSTERED INDEX [rdi_distr] ON [dbo].[rdistr]
(
	[distr] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, IGNORE_DUP_KEY = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
;
SET ANSI_PADDING ON
;
/****** Object:  Index [rdi_nazev]    Script Date: 28.06.2023 10:01:15 ******/
CREATE NONCLUSTERED INDEX [rdi_nazev] ON [dbo].[rdistr]
(
	[nazevd] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
;
SET ANSI_PADDING ON
;
/****** Object:  Index [pokladna]    Script Date: 28.06.2023 10:01:15 ******/
CREATE NONCLUSTERED INDEX [pokladna] ON [dbo].[rdoklad]
(
	[pokladna] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
;
/****** Object:  Index [dokdy]    Script Date: 28.06.2023 10:01:15 ******/
CREATE NONCLUSTERED INDEX [dokdy] ON [dbo].[rezervace]
(
	[dokdy] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
;
/****** Object:  Index [rprogID]    Script Date: 28.06.2023 10:01:15 ******/
CREATE NONCLUSTERED INDEX [rprogID] ON [dbo].[rezervace]
(
	[rprogID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
;
/****** Object:  Index [rezervaceID]    Script Date: 28.06.2023 10:01:15 ******/
CREATE NONCLUSTERED INDEX [rezervaceID] ON [dbo].[rezslev]
(
	[rezervaceID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
;
SET ANSI_PADDING ON
;
/****** Object:  Index [rfi_cisfilm]    Script Date: 28.06.2023 10:01:15 ******/
CREATE NONCLUSTERED INDEX [rfi_cisfilm] ON [dbo].[rfilm]
(
	[cisfilm] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
;
SET ANSI_PADDING ON
;
/****** Object:  Index [rfi_distr]    Script Date: 28.06.2023 10:01:15 ******/
CREATE NONCLUSTERED INDEX [rfi_distr] ON [dbo].[rfilm]
(
	[distr] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
;
SET ANSI_PADDING ON
;
/****** Object:  Index [rfi_nazev]    Script Date: 28.06.2023 10:01:15 ******/
CREATE NONCLUSTERED INDEX [rfi_nazev] ON [dbo].[rfilm]
(
	[nazevf] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
;
SET ANSI_PADDING ON
;
/****** Object:  Index [rkin_Csalu]    Script Date: 28.06.2023 10:01:15 ******/
CREATE NONCLUSTERED INDEX [rkin_Csalu] ON [dbo].[rkin]
(
	[csalu] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
;
SET ANSI_PADDING ON
;
/****** Object:  Index [rkin_nazev]    Script Date: 28.06.2023 10:01:15 ******/
CREATE NONCLUSTERED INDEX [rkin_nazev] ON [dbo].[rkin]
(
	[nazev] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
;
SET ANSI_PADDING ON
;
/****** Object:  Index [listIe_VS]    Script Date: 28.06.2023 10:01:15 ******/
CREATE NONCLUSTERED INDEX [listIe_VS] ON [dbo].[rlistIe]
(
	[vs] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
;
/****** Object:  Index [rlistky_datpokl]    Script Date: 28.06.2023 10:01:15 ******/
CREATE NONCLUSTERED INDEX [rlistky_datpokl] ON [dbo].[rlistky]
(
	[datpokl] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
;
/****** Object:  Index [rlistky_datprod]    Script Date: 28.06.2023 10:01:15 ******/
CREATE NONCLUSTERED INDEX [rlistky_datprod] ON [dbo].[rlistky]
(
	[datprod] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
;
SET ANSI_PADDING ON
;
/****** Object:  Index [rlistky_pokladna]    Script Date: 28.06.2023 10:01:15 ******/
CREATE NONCLUSTERED INDEX [rlistky_pokladna] ON [dbo].[rlistky]
(
	[pokladna] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
;
/****** Object:  Index [rlistky_rpoklID]    Script Date: 28.06.2023 10:01:15 ******/
CREATE NONCLUSTERED INDEX [rlistky_rpoklID] ON [dbo].[rlistky]
(
	[rpoklID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
;
/****** Object:  Index [rlistky_rprogID]    Script Date: 28.06.2023 10:01:15 ******/
CREATE NONCLUSTERED INDEX [rlistky_rprogID] ON [dbo].[rlistky]
(
	[rprogID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
;
SET ANSI_PADDING ON
;
/****** Object:  Index [mustr_csalu]    Script Date: 28.06.2023 10:01:15 ******/
CREATE NONCLUSTERED INDEX [mustr_csalu] ON [dbo].[rmustr]
(
	[csalu] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
;
/****** Object:  Index [mustr_delegace]    Script Date: 28.06.2023 10:01:15 ******/
CREATE NONCLUSTERED INDEX [mustr_delegace] ON [dbo].[rmustr]
(
	[delegace] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
;
/****** Object:  Index [mustr_rupravaID]    Script Date: 28.06.2023 10:01:15 ******/
CREATE NONCLUSTERED INDEX [mustr_rupravaID] ON [dbo].[rmustr]
(
	[rupravaID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
;
SET ANSI_PADDING ON
;
/****** Object:  Index [mustr_sekce]    Script Date: 28.06.2023 10:01:15 ******/
CREATE NONCLUSTERED INDEX [mustr_sekce] ON [dbo].[rmustr]
(
	[sekce] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
;
SET ANSI_PADDING ON
;
/****** Object:  Index [pokladna]    Script Date: 28.06.2023 10:01:15 ******/
CREATE NONCLUSTERED INDEX [pokladna] ON [dbo].[rpokl]
(
	[pokladna] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
;
SET ANSI_PADDING ON
;
/****** Object:  Index [prog_csalu]    Script Date: 28.06.2023 10:01:15 ******/
CREATE NONCLUSTERED INDEX [prog_csalu] ON [dbo].[rprog]
(
	[csalu] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
;
/****** Object:  Index [prog_rfilmID]    Script Date: 28.06.2023 10:01:15 ******/
CREATE NONCLUSTERED INDEX [prog_rfilmID] ON [dbo].[rprog]
(
	[rfilmID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
;
/****** Object:  Index [rdi_porad]    Script Date: 28.06.2023 10:01:15 ******/
CREATE NONCLUSTERED INDEX [rdi_porad] ON [dbo].[rprog]
(
	[porad] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
;
/****** Object:  Index [prodej]    Script Date: 28.06.2023 10:01:15 ******/
CREATE NONCLUSTERED INDEX [prodej] ON [dbo].[rsedadla]
(
	[rprogID] ASC,
	[rmustrID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
;
SET ANSI_PADDING ON
;
/****** Object:  Index [rsed_csalu]    Script Date: 28.06.2023 10:01:15 ******/
CREATE NONCLUSTERED INDEX [rsed_csalu] ON [dbo].[rsedadla]
(
	[csalu] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
;
/****** Object:  Index [rsed_rmustrID]    Script Date: 28.06.2023 10:01:15 ******/
CREATE NONCLUSTERED INDEX [rsed_rmustrID] ON [dbo].[rsedadla]
(
	[rmustrID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
;
/****** Object:  Index [rsed_rprogID]    Script Date: 28.06.2023 10:01:15 ******/
CREATE NONCLUSTERED INDEX [rsed_rprogID] ON [dbo].[rsedadla]
(
	[rprogID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
;
/****** Object:  Index [rsed_zcas]    Script Date: 28.06.2023 10:01:15 ******/
CREATE NONCLUSTERED INDEX [rsed_zcas] ON [dbo].[rsedadla]
(
	[zcas] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
;
ALTER TABLE [dbo].[Acl] ADD  CONSTRAINT [DF_Acl_allowed]  DEFAULT ((1)) FOR [allowed]
;
ALTER TABLE [dbo].[Acl$User] ADD  CONSTRAINT [DF_Acl$User_active]  DEFAULT ((1)) FOR [active]
;
ALTER TABLE [dbo].[cban] ADD  CONSTRAINT [DF__cban__pc__2645B050]  DEFAULT ((0)) FOR [pc]
;
ALTER TABLE [dbo].[cban] ADD  CONSTRAINT [DF__cban__iban__2739D489]  DEFAULT ('') FOR [iban]
;
ALTER TABLE [dbo].[cban] ADD  CONSTRAINT [DF__cban__zuziv__282DF8C2]  DEFAULT ('') FOR [zuziv]
;
ALTER TABLE [dbo].[cban] ADD  CONSTRAINT [DF__cban__zcas__29221CFB]  DEFAULT (getdate()) FOR [zcas]
;
ALTER TABLE [dbo].[cjazyk] ADD  DEFAULT (getdate()) FOR [zcas]
;
ALTER TABLE [dbo].[ctyppr] ADD  DEFAULT ('') FOR [zuziv]
;
ALTER TABLE [dbo].[ctyppr] ADD  DEFAULT (getdate()) FOR [zcas]
;
ALTER TABLE [dbo].[ctyppr] ADD  DEFAULT ((0)) FOR [priplatek]
;
ALTER TABLE [dbo].[ctyppr] ADD  DEFAULT ((0)) FOR [sluzby]
;
ALTER TABLE [dbo].[param] ADD  CONSTRAINT [DF__param__popis__46B27FE2]  DEFAULT ('') FOR [popis]
;
ALTER TABLE [dbo].[param] ADD  CONSTRAINT [DF__param__modifik__47A6A41B]  DEFAULT ((1)) FOR [modifik]
;
ALTER TABLE [dbo].[param] ADD  CONSTRAINT [DF__param__zobrazit__489AC854]  DEFAULT ((1)) FOR [zobrazit]
;
ALTER TABLE [dbo].[param] ADD  CONSTRAINT [DF__param__mazat__498EEC8D]  DEFAULT ((1)) FOR [mazat]
;
ALTER TABLE [dbo].[param] ADD  CONSTRAINT [DF__param__nacist__4A8310C6]  DEFAULT ((1)) FOR [nacist]
;
ALTER TABLE [dbo].[param] ADD  CONSTRAINT [DF__param__zuziv__4B7734FF]  DEFAULT ('') FOR [zuziv]
;
ALTER TABLE [dbo].[param] ADD  CONSTRAINT [DF__param__zcas__4C6B5938]  DEFAULT (getdate()) FOR [zcas]
;
ALTER TABLE [dbo].[rbarvy] ADD  CONSTRAINT [DF__rbarvy__nfont__7B264821]  DEFAULT ((9)) FOR [nfont]
;
ALTER TABLE [dbo].[rbarvy] ADD  CONSTRAINT [DF__rbarvy__fontb__7C1A6C5A]  DEFAULT ((1)) FOR [fontb]
;
ALTER TABLE [dbo].[rbarvy] ADD  CONSTRAINT [DF__rbarvy__fonti__7D0E9093]  DEFAULT ((0)) FOR [fonti]
;
ALTER TABLE [dbo].[rbarvy] ADD  CONSTRAINT [DF__rbarvy__cenkat__7E02B4CC]  DEFAULT ((0)) FOR [cenkat]
;
ALTER TABLE [dbo].[rbarvy] ADD  CONSTRAINT [DF__rbarvy__delegace__7EF6D905]  DEFAULT ((0)) FOR [delegace]
;
ALTER TABLE [dbo].[rcag] ADD  CONSTRAINT [DF__rcag__zuziv__3F115E1A]  DEFAULT ('') FOR [zuziv]
;
ALTER TABLE [dbo].[rcag] ADD  CONSTRAINT [DF__rcag__zcas__40058253]  DEFAULT (getdate()) FOR [zcas]
;
ALTER TABLE [dbo].[rcards] ADD  DEFAULT ((0)) FOR [rodbID]
;
ALTER TABLE [dbo].[rcards] ADD  DEFAULT ((0)) FOR [rlistIeID]
;
ALTER TABLE [dbo].[rcards] ADD  DEFAULT ('') FOR [zuziv]
;
ALTER TABLE [dbo].[rcards] ADD  DEFAULT (getdate()) FOR [zcas]
;
ALTER TABLE [dbo].[rcards] ADD  DEFAULT ((0)) FOR [lvipcena]
;
ALTER TABLE [dbo].[rcards] ADD  DEFAULT ((0)) FOR [body]
;
ALTER TABLE [dbo].[rcas] ADD  CONSTRAINT [DF__rcas__zadej__03317E3D]  DEFAULT ((0)) FOR [zadej]
;
ALTER TABLE [dbo].[rcas] ADD  CONSTRAINT [DF__rcas__zuziv__0425A276]  DEFAULT ('') FOR [zuziv]
;
ALTER TABLE [dbo].[rcas] ADD  CONSTRAINT [DF__rcas__zcas__0519C6AF]  DEFAULT (getdate()) FOR [zcas]
;
ALTER TABLE [dbo].[rcform] ADD  CONSTRAINT [DF__rcform__zuziv__01D345B0]  DEFAULT ('') FOR [zuziv]
;
ALTER TABLE [dbo].[rcform] ADD  CONSTRAINT [DF__rcform__zcas__02C769E9]  DEFAULT (getdate()) FOR [zcas]
;
ALTER TABLE [dbo].[rckar] ADD  CONSTRAINT [DF__rckar__zuziv__656C112C]  DEFAULT ('') FOR [zuziv]
;
ALTER TABLE [dbo].[rckar] ADD  CONSTRAINT [DF__rckar__zcas__66603565]  DEFAULT (getdate()) FOR [zcas]
;
ALTER TABLE [dbo].[rcpe] ADD  CONSTRAINT [DF_rcpe_nazev]  DEFAULT ('') FOR [nazev]
;
ALTER TABLE [dbo].[rcpe] ADD  CONSTRAINT [DF__rcpe__zuziv__00200768]  DEFAULT ('') FOR [zuziv]
;
ALTER TABLE [dbo].[rcpe] ADD  CONSTRAINT [DF__rcpe__zcas__01142BA1]  DEFAULT (getdate()) FOR [zcas]
;
ALTER TABLE [dbo].[rcpoh] ADD  CONSTRAINT [DF__rcpoh__rcradID__662B2B3B]  DEFAULT ((0)) FOR [rcradID]
;
ALTER TABLE [dbo].[rcpoh] ADD  CONSTRAINT [DF__rcpoh__nazev__671F4F74]  DEFAULT ('') FOR [nazev]
;
ALTER TABLE [dbo].[rcpoh] ADD  CONSTRAINT [DF__rcpoh__cena__681373AD]  DEFAULT ((0)) FOR [cena]
;
ALTER TABLE [dbo].[rcpoh] ADD  CONSTRAINT [DF__rcpoh__platnost__690797E6]  DEFAULT ((1)) FOR [platnost]
;
ALTER TABLE [dbo].[rcpoh] ADD  CONSTRAINT [DF__rcpoh__zuziv__69FBBC1F]  DEFAULT ('') FOR [zuziv]
;
ALTER TABLE [dbo].[rcpoh] ADD  CONSTRAINT [DF__rcpoh__zcas__6AEFE058]  DEFAULT (getdate()) FOR [zcas]
;
ALTER TABLE [dbo].[rcpristup] ADD  CONSTRAINT [DF__rcpristup__zuziv__42E1EEFE]  DEFAULT ('') FOR [zuziv]
;
ALTER TABLE [dbo].[rcpristup] ADD  CONSTRAINT [DF__rcpristup__zcas__43D61337]  DEFAULT (getdate()) FOR [zcas]
;
ALTER TABLE [dbo].[rcrad] ADD  CONSTRAINT [DF__rcrad__nazev__2BFE89A6]  DEFAULT ('') FOR [nazev]
;
ALTER TABLE [dbo].[rcrad] ADD  CONSTRAINT [DF__rcrad__pred__2CF2ADDF]  DEFAULT ('') FOR [pred]
;
ALTER TABLE [dbo].[rcrad] ADD  CONSTRAINT [DF__rcrad__za__2DE6D218]  DEFAULT ('') FOR [za]
;
ALTER TABLE [dbo].[rcrad] ADD  CONSTRAINT [DF__rcrad__doklad__2EDAF651]  DEFAULT ((0)) FOR [doklad]
;
ALTER TABLE [dbo].[rcrad] ADD  CONSTRAINT [DF__rcrad__zuziv__2FCF1A8A]  DEFAULT ('') FOR [zuziv]
;
ALTER TABLE [dbo].[rcrad] ADD  CONSTRAINT [DF__rcrad__zcas__30C33EC3]  DEFAULT (getdate()) FOR [zcas]
;
ALTER TABLE [dbo].[rcrek] ADD  CONSTRAINT [DF__rcrek__zuziv__693CA210]  DEFAULT ('') FOR [zuziv]
;
ALTER TABLE [dbo].[rcrek] ADD  CONSTRAINT [DF__rcrek__zcas__6A30C649]  DEFAULT (getdate()) FOR [zcas]
;
ALTER TABLE [dbo].[rcrez] ADD  CONSTRAINT [DF__rcrez__maxvst__7EC1CEDB]  DEFAULT ((0)) FOR [maxvst]
;
ALTER TABLE [dbo].[rcrez] ADD  CONSTRAINT [DF__rcrez__zuziv__7FB5F314]  DEFAULT ('') FOR [zuziv]
;
ALTER TABLE [dbo].[rcsluzby] ADD  DEFAULT ((0)) FOR [cena]
;
ALTER TABLE [dbo].[rcsluzby] ADD  DEFAULT ((0)) FOR [liver]
;
ALTER TABLE [dbo].[rcsluzby] ADD  DEFAULT ('') FOR [zuziv]
;
ALTER TABLE [dbo].[rcsluzby] ADD  DEFAULT (getdate()) FOR [zcas]
;
ALTER TABLE [dbo].[rcupr] ADD  CONSTRAINT [DF__rcupr__zuziv__22751F6C]  DEFAULT ('') FOR [zuziv]
;
ALTER TABLE [dbo].[rcupr] ADD  CONSTRAINT [DF__rcupr__zcas__236943A5]  DEFAULT (getdate()) FOR [zcas]
;
ALTER TABLE [dbo].[rcupr] ADD  DEFAULT ('') FOR [kod]
;
ALTER TABLE [dbo].[rczan] ADD  CONSTRAINT [DF_rczan_tisksID]  DEFAULT ((0)) FOR [tisksID]
;
ALTER TABLE [dbo].[rczan] ADD  CONSTRAINT [DF_rczan_tisksIDabo_1]  DEFAULT ((0)) FOR [tisksIDabo]
;
ALTER TABLE [dbo].[rczan] ADD  CONSTRAINT [DF__rczan__nazev__1DB06A4F]  DEFAULT ('') FOR [nazev]
;
ALTER TABLE [dbo].[rczan] ADD  CONSTRAINT [DF__rczan__zuziv__1EA48E88]  DEFAULT ('') FOR [zuziv]
;
ALTER TABLE [dbo].[rczan] ADD  CONSTRAINT [DF__rczan__zcas__1F98B2C1]  DEFAULT (getdate()) FOR [zcas]
;
ALTER TABLE [dbo].[rdispozice] ADD  CONSTRAINT [DF_rdispozice_ciskop]  DEFAULT ('') FOR [ciskop]
;
ALTER TABLE [dbo].[rdispozice] ADD  CONSTRAINT [DF_rdispozice_odkino]  DEFAULT ('') FOR [odkino]
;
ALTER TABLE [dbo].[rdispozice] ADD  CONSTRAINT [DF_rdispozice_doklad]  DEFAULT ('') FOR [doklad]
;
ALTER TABLE [dbo].[rdispozice] ADD  CONSTRAINT [DF_rdispozice_typ]  DEFAULT ('P') FOR [typdo]
;
ALTER TABLE [dbo].[rdispozice] ADD  CONSTRAINT [DF_rdispozice_typod]  DEFAULT ('P') FOR [typod]
;
ALTER TABLE [dbo].[rdispozice] ADD  CONSTRAINT [DF_rdispozice_naklady]  DEFAULT ((0)) FOR [naklady]
;
ALTER TABLE [dbo].[rdispozice] ADD  CONSTRAINT [DF_rdispozice_zuziv]  DEFAULT ('') FOR [zuziv]
;
ALTER TABLE [dbo].[rdispozice] ADD  CONSTRAINT [DF_rdispozice_zcas]  DEFAULT (getdate()) FOR [zcas]
;
ALTER TABLE [dbo].[rdistr] ADD  CONSTRAINT [DF__rdistr__kod__145C0A3F]  DEFAULT ('') FOR [kod]
;
ALTER TABLE [dbo].[rdistr] ADD  CONSTRAINT [DF__rdistr__limit__164452B1]  DEFAULT ((0)) FOR [limit]
;
ALTER TABLE [dbo].[rdistr] ADD  CONSTRAINT [DF__rdistr__provize__173876EA]  DEFAULT ((50)) FOR [provize]
;
ALTER TABLE [dbo].[rdistr] ADD  CONSTRAINT [DF__rdistr__ulice__182C9B23]  DEFAULT ('') FOR [ulice]
;
ALTER TABLE [dbo].[rdistr] ADD  CONSTRAINT [DF__rdistr__misto__1920BF5C]  DEFAULT ('') FOR [misto]
;
ALTER TABLE [dbo].[rdistr] ADD  CONSTRAINT [DF__rdistr__psc__1A14E395]  DEFAULT ('') FOR [psc]
;
ALTER TABLE [dbo].[rdistr] ADD  CONSTRAINT [DF__rdistr__telefon1__1B0907CE]  DEFAULT ('') FOR [telefon1]
;
ALTER TABLE [dbo].[rdistr] ADD  CONSTRAINT [DF__rdistr__telefon2__1BFD2C07]  DEFAULT ('') FOR [telefon2]
;
ALTER TABLE [dbo].[rdistr] ADD  CONSTRAINT [DF__rdistr__telefon3__1CF15040]  DEFAULT ('') FOR [telefon3]
;
ALTER TABLE [dbo].[rdistr] ADD  CONSTRAINT [DF__rdistr__fax__1DE57479]  DEFAULT ('') FOR [fax]
;
ALTER TABLE [dbo].[rdistr] ADD  CONSTRAINT [DF__rdistr__ucet__1ED998B2]  DEFAULT ('') FOR [ucet]
;
ALTER TABLE [dbo].[rdistr] ADD  CONSTRAINT [DF__rdistr__banka__1FCDBCEB]  DEFAULT ('') FOR [banka]
;
ALTER TABLE [dbo].[rdistr] ADD  CONSTRAINT [DF__rdistr__ico__20C1E124]  DEFAULT ('') FOR [ico]
;
ALTER TABLE [dbo].[rdistr] ADD  CONSTRAINT [DF__rdistr__dic__21B6055D]  DEFAULT ('') FOR [dic]
;
ALTER TABLE [dbo].[rdistr] ADD  CONSTRAINT [DF_rdistr_dph_1]  DEFAULT ((5)) FOR [dph]
;
ALTER TABLE [dbo].[rdistr] ADD  CONSTRAINT [DF__rdistr__jmeno1__22AA2996]  DEFAULT ('') FOR [jmeno1]
;
ALTER TABLE [dbo].[rdistr] ADD  CONSTRAINT [DF__rdistr__jmeno2__239E4DCF]  DEFAULT ('') FOR [jmeno2]
;
ALTER TABLE [dbo].[rdistr] ADD  CONSTRAINT [DF__rdistr__jmeno3__24927208]  DEFAULT ('') FOR [jmeno3]
;
ALTER TABLE [dbo].[rdistr] ADD  CONSTRAINT [DF__rdistr__znak__25869641]  DEFAULT ('') FOR [znak]
;
ALTER TABLE [dbo].[rdistr] ADD  CONSTRAINT [DF__rdistr__mail__267ABA7A]  DEFAULT ('') FOR [mail]
;
ALTER TABLE [dbo].[rdistr] ADD  CONSTRAINT [DF__rdistr__zuziv__276EDEB3]  DEFAULT ('') FOR [zuziv]
;
ALTER TABLE [dbo].[rdistr] ADD  CONSTRAINT [DF__rdistr__zcas__286302EC]  DEFAULT (getdate()) FOR [zcas]
;
ALTER TABLE [dbo].[rdistr] ADD  CONSTRAINT [DF__rdistr__fix__29572725]  DEFAULT ((0)) FOR [fix]
;
ALTER TABLE [dbo].[rdistr] ADD  CONSTRAINT [DF__rdistr__ldan__2A4B4B5E]  DEFAULT ((0)) FOR [ldan]
;
ALTER TABLE [dbo].[rdistr] ADD  DEFAULT ((0)) FOR [vipcena]
;
ALTER TABLE [dbo].[rdoklad] ADD  CONSTRAINT [DF__rdoklad__rpoklID__7720AD13]  DEFAULT ((0)) FOR [rpoklID]
;
ALTER TABLE [dbo].[rdoklad] ADD  CONSTRAINT [DF_rdoklad_rfilmID]  DEFAULT ((0)) FOR [rfilmID]
;
ALTER TABLE [dbo].[rdoklad] ADD  CONSTRAINT [DF__rdoklad__vazba__7814D14C]  DEFAULT ((0)) FOR [vazba]
;
ALTER TABLE [dbo].[rdoklad] ADD  CONSTRAINT [DF_rdoklad_lhoto]  DEFAULT ((1)) FOR [lhoto]
;
ALTER TABLE [dbo].[rdoklad] ADD  CONSTRAINT [DF_rdoklad_ltisk]  DEFAULT ((0)) FOR [ltisk]
;
ALTER TABLE [dbo].[rdoklad] ADD  CONSTRAINT [DF__rdoklad__referen__7908F585]  DEFAULT ('') FOR [reference]
;
ALTER TABLE [dbo].[rdoklad] ADD  CONSTRAINT [DF__rdoklad__varsymb__79FD19BE]  DEFAULT ('') FOR [varsymb]
;
ALTER TABLE [dbo].[rdoklad] ADD  CONSTRAINT [DF__rdoklad__rodbID__7AF13DF7]  DEFAULT ((0)) FOR [rodbID]
;
ALTER TABLE [dbo].[rdoklad] ADD  CONSTRAINT [DF__rdoklad__pocet__7BE56230]  DEFAULT ((0)) FOR [pocet]
;
ALTER TABLE [dbo].[rdoklad] ADD  CONSTRAINT [DF_rdoklad_pozn]  DEFAULT ('') FOR [pozn]
;
ALTER TABLE [dbo].[rezervace] ADD  CONSTRAINT [DF__rezervace__jmeno__6319B466]  DEFAULT ('') FOR [jmeno]
;
ALTER TABLE [dbo].[rezervace] ADD  CONSTRAINT [DF__rezervace__popis__640DD89F]  DEFAULT ('') FOR [popis]
;
ALTER TABLE [dbo].[rezervace] ADD  CONSTRAINT [DF__rezervace__pocet__6501FCD8]  DEFAULT ((0)) FOR [pocet]
;
ALTER TABLE [dbo].[rezervace] ADD  CONSTRAINT [DF__rezervace__zuziv__65F62111]  DEFAULT ('') FOR [zuziv]
;
ALTER TABLE [dbo].[rezervace] ADD  CONSTRAINT [DF__rezervace__zcas__66EA454A]  DEFAULT (getdate()) FOR [zcas]
;
ALTER TABLE [dbo].[rezervace] ADD  DEFAULT ((0)) FOR [okula]
;
ALTER TABLE [dbo].[rezslev] ADD  CONSTRAINT [DF_rezslev_karta]  DEFAULT ('') FOR [karta]
;
ALTER TABLE [dbo].[rezslev] ADD  DEFAULT (getdate()) FOR [zcas]
;
ALTER TABLE [dbo].[rezslev] ADD  DEFAULT ((0)) FOR [okula]
;
ALTER TABLE [dbo].[rezslev] ADD  DEFAULT ('') FOR [voucher]
;
ALTER TABLE [dbo].[rezslev] ADD  DEFAULT ((0)) FOR [cenkat]
;
ALTER TABLE [dbo].[rfilm] ADD  CONSTRAINT [DF__rfilm__poradatel__4F47C5E3]  DEFAULT ('') FOR [poradatel]
;
ALTER TABLE [dbo].[rfilm] ADD  CONSTRAINT [DF__rfilm__ornazevf__503BEA1C]  DEFAULT ('') FOR [ornazevf]
;
ALTER TABLE [dbo].[rfilm] ADD  CONSTRAINT [DF__rfilm__prod__51300E55]  DEFAULT ('') FOR [prod]
;
ALTER TABLE [dbo].[rfilm] ADD  CONSTRAINT [DF__rfilm__zanrf__5224328E]  DEFAULT ('') FOR [zanrf]
;
ALTER TABLE [dbo].[rfilm] ADD  CONSTRAINT [DF__rfilm__pristup__531856C7]  DEFAULT ('') FOR [pristup]
;
ALTER TABLE [dbo].[rfilm] ADD  CONSTRAINT [DF__rfilm__minf__540C7B00]  DEFAULT ('') FOR [minf]
;
ALTER TABLE [dbo].[rfilm] ADD  CONSTRAINT [DF__rfilm__uprava__55009F39]  DEFAULT ('') FOR [uprava]
;
ALTER TABLE [dbo].[rfilm] ADD  CONSTRAINT [DF__rfilm__producent__55F4C372]  DEFAULT ('') FOR [producent]
;
ALTER TABLE [dbo].[rfilm] ADD  CONSTRAINT [DF__rfilm__rezie__56E8E7AB]  DEFAULT ('') FOR [rezie]
;
ALTER TABLE [dbo].[rfilm] ADD  CONSTRAINT [DF__rfilm__scenar__57DD0BE4]  DEFAULT ('') FOR [scenar]
;
ALTER TABLE [dbo].[rfilm] ADD  CONSTRAINT [DF__rfilm__kamera__58D1301D]  DEFAULT ('') FOR [kamera]
;
ALTER TABLE [dbo].[rfilm] ADD  CONSTRAINT [DF__rfilm__strih__59C55456]  DEFAULT ('') FOR [strih]
;
ALTER TABLE [dbo].[rfilm] ADD  CONSTRAINT [DF__rfilm__hudba__5AB9788F]  DEFAULT ('') FOR [hudba]
;
ALTER TABLE [dbo].[rfilm] ADD  CONSTRAINT [DF__rfilm__cisfilm_u__5BAD9CC8]  DEFAULT ((0)) FOR [cisfilm_u]
;
ALTER TABLE [dbo].[rfilm] ADD  CONSTRAINT [DF__rfilm__zuziv__5CA1C101]  DEFAULT ('') FOR [zuziv]
;
ALTER TABLE [dbo].[rfilm] ADD  CONSTRAINT [DF__rfilm__zcas__5D95E53A]  DEFAULT (getdate()) FOR [zcas]
;
ALTER TABLE [dbo].[rfilm] ADD  CONSTRAINT [DF__rfilm__cenkat__5E8A0973]  DEFAULT ('') FOR [cenkat]
;
ALTER TABLE [dbo].[rfilm] ADD  CONSTRAINT [DF_rfilm_nlist_1]  DEFAULT ((0)) FOR [nlist]
;
ALTER TABLE [dbo].[rfilm] ADD  DEFAULT ('') FOR [kodupravy]
;
ALTER TABLE [dbo].[rfilm] ADD  DEFAULT ((0)) FOR [form_2d]
;
ALTER TABLE [dbo].[rfilm] ADD  DEFAULT ((0)) FOR [form_3d]
;
ALTER TABLE [dbo].[rfilm] ADD  DEFAULT ('') FOR [cisfilmUFD]
;
ALTER TABLE [dbo].[rfilm] ADD  DEFAULT ('') FOR [Jazyk]
;
ALTER TABLE [dbo].[rfilm] ADD  DEFAULT ('') FOR [cform]
;
ALTER TABLE [dbo].[rfilm] ADD  DEFAULT ('') FOR [cfotrm]
;
ALTER TABLE [dbo].[rkin] ADD  CONSTRAINT [DF__rkin__kapacita__5070F446]  DEFAULT ((1)) FOR [kapacita]
;
ALTER TABLE [dbo].[rkin] ADD  CONSTRAINT [DF__rkin__misto__5165187F]  DEFAULT ('') FOR [misto]
;
ALTER TABLE [dbo].[rkin] ADD  CONSTRAINT [DF__rkin__ulice__52593CB8]  DEFAULT ('') FOR [ulice]
;
ALTER TABLE [dbo].[rkin] ADD  CONSTRAINT [DF__rkin__posta__534D60F1]  DEFAULT ('') FOR [posta]
;
ALTER TABLE [dbo].[rkin] ADD  CONSTRAINT [DF__rkin__csdk__5441852A]  DEFAULT ('') FOR [csdk]
;
ALTER TABLE [dbo].[rkin] ADD  CONSTRAINT [DF__rkin__vedouci__5535A963]  DEFAULT ('') FOR [vedouci]
;
ALTER TABLE [dbo].[rkin] ADD  CONSTRAINT [DF__rkin__nazevp1__5629CD9C]  DEFAULT ('') FOR [nazevp1]
;
ALTER TABLE [dbo].[rkin] ADD  CONSTRAINT [DF__rkin__nazevp2__571DF1D5]  DEFAULT ('') FOR [nazevp2]
;
ALTER TABLE [dbo].[rkin] ADD  CONSTRAINT [DF__rkin__tel__5812160E]  DEFAULT ('') FOR [tel]
;
ALTER TABLE [dbo].[rkin] ADD  CONSTRAINT [DF__rkin__fax__59063A47]  DEFAULT ('') FOR [fax]
;
ALTER TABLE [dbo].[rkin] ADD  CONSTRAINT [DF__rkin__mob__59FA5E80]  DEFAULT ('') FOR [mob]
;
ALTER TABLE [dbo].[rkin] ADD  CONSTRAINT [DF__rkin__email__5AEE82B9]  DEFAULT ('') FOR [email]
;
ALTER TABLE [dbo].[rkin] ADD  CONSTRAINT [DF__rkin__banka__5BE2A6F2]  DEFAULT ('') FOR [banka]
;
ALTER TABLE [dbo].[rkin] ADD  CONSTRAINT [DF__rkin__cuctu__5CD6CB2B]  DEFAULT ('') FOR [cuctu]
;
ALTER TABLE [dbo].[rkin] ADD  CONSTRAINT [DF__rkin__ico__5DCAEF64]  DEFAULT ('') FOR [ico]
;
ALTER TABLE [dbo].[rkin] ADD  CONSTRAINT [DF__rkin__dic__5EBF139D]  DEFAULT ('') FOR [dic]
;
ALTER TABLE [dbo].[rkin] ADD  CONSTRAINT [DF__rkin__letni__5FB337D6]  DEFAULT ((0)) FOR [letni]
;
ALTER TABLE [dbo].[rkin] ADD  CONSTRAINT [DF__rkin__zuziv__60A75C0F]  DEFAULT ('') FOR [zuziv]
;
ALTER TABLE [dbo].[rkin] ADD  CONSTRAINT [DF__rkin__ldan__619B8048]  DEFAULT ((0)) FOR [ldan]
;
ALTER TABLE [dbo].[rkin] ADD  CONSTRAINT [DF__rkin__pozn__628FA481]  DEFAULT ('') FOR [pozn]
;
ALTER TABLE [dbo].[rklic] ADD  CONSTRAINT [DF__rklic__zarizeni__03F0984C]  DEFAULT ('') FOR [zarizeni]
;
ALTER TABLE [dbo].[rklic] ADD  CONSTRAINT [DF__rklic__provoz__04E4BC85]  DEFAULT ('') FOR [provoz]
;
ALTER TABLE [dbo].[rklic] ADD  CONSTRAINT [DF__rklic__appl__05D8E0BE]  DEFAULT ('') FOR [appl]
;
ALTER TABLE [dbo].[rklic] ADD  CONSTRAINT [DF__rklic__letni__06CD04F7]  DEFAULT ((0)) FOR [letni]
;
ALTER TABLE [dbo].[rklic] ADD  CONSTRAINT [DF__rklic__dan__07C12930]  DEFAULT ((0)) FOR [dan]
;
ALTER TABLE [dbo].[rklic] ADD  CONSTRAINT [DF__rklic__ddan__08B54D69]  DEFAULT ((0)) FOR [ddan]
;
ALTER TABLE [dbo].[rklic] ADD  CONSTRAINT [DF__rklic__zdan__09A971A2]  DEFAULT ((0)) FOR [zdan]
;
ALTER TABLE [dbo].[rklic] ADD  CONSTRAINT [DF__rklic__skuzby__0A9D95DB]  DEFAULT ((0)) FOR [skuzby]
;
ALTER TABLE [dbo].[rklic] ADD  CONSTRAINT [DF__rklic__casprod__0B91BA14]  DEFAULT ((30)) FOR [casprod]
;
ALTER TABLE [dbo].[rklic] ADD  CONSTRAINT [DF__rklic__osa__0C85DE4D]  DEFAULT ((0)) FOR [osa]
;
ALTER TABLE [dbo].[rklic] ADD  CONSTRAINT [DF__rklic__osaproc__0D7A0286]  DEFAULT ((0)) FOR [osaproc]
;
ALTER TABLE [dbo].[rklic] ADD  CONSTRAINT [DF__rklic__pobocka__0E6E26BF]  DEFAULT ((0)) FOR [pobocka]
;
ALTER TABLE [dbo].[rklic] ADD  CONSTRAINT [DF__rklic__int_prog__0F624AF8]  DEFAULT ((1)) FOR [int_prog]
;
ALTER TABLE [dbo].[rklic] ADD  CONSTRAINT [DF__rklic__int_rez__10566F31]  DEFAULT ((1)) FOR [int_rez]
;
ALTER TABLE [dbo].[rklic] ADD  CONSTRAINT [DF__rklic__int_user__114A936A]  DEFAULT ('') FOR [int_user]
;
ALTER TABLE [dbo].[rklic] ADD  CONSTRAINT [DF__rklic__int_pasw__123EB7A3]  DEFAULT ('') FOR [int_pasw]
;
ALTER TABLE [dbo].[rklic] ADD  CONSTRAINT [DF__rklic__verze__1332DBDC]  DEFAULT ('') FOR [verze]
;
ALTER TABLE [dbo].[rklic] ADD  CONSTRAINT [DF__rklic__typ_vst__14270015]  DEFAULT ('') FOR [typ_vst]
;
ALTER TABLE [dbo].[rklic] ADD  CONSTRAINT [DF__rklic__typ_tisk__151B244E]  DEFAULT ('') FOR [typ_tisk]
;
ALTER TABLE [dbo].[rklic] ADD  CONSTRAINT [DF__rklic__fondkin__160F4887]  DEFAULT ((0)) FOR [fondkin]
;
ALTER TABLE [dbo].[rklic] ADD  CONSTRAINT [DF__rklic__fondkc__17036CC0]  DEFAULT ((0)) FOR [fondkc]
;
ALTER TABLE [dbo].[rklic] ADD  CONSTRAINT [DF__rklic__fondproc__17F790F9]  DEFAULT ((0)) FOR [fondproc]
;
ALTER TABLE [dbo].[rklic] ADD  CONSTRAINT [DF__rklic__odrezerv__18EBB532]  DEFAULT ((0)) FOR [odrezerv]
;
ALTER TABLE [dbo].[rklic] ADD  CONSTRAINT [DF__rklic__zaokrouhl__19DFD96B]  DEFAULT ((0)) FOR [zaokrouhl]
;
ALTER TABLE [dbo].[rklic] ADD  CONSTRAINT [DF__rklic__ipmask__1AD3FDA4]  DEFAULT ('') FOR [ipmask]
;
ALTER TABLE [dbo].[rklic] ADD  CONSTRAINT [DF_rklic_pujcovne]  DEFAULT ((0)) FOR [pujcovne]
;
ALTER TABLE [dbo].[rklic] ADD  DEFAULT ((0)) FOR [sluzbyDbox]
;
ALTER TABLE [dbo].[rklic] ADD  DEFAULT ((0)) FOR [sluzbyVIP]
;
ALTER TABLE [dbo].[rklic] ADD  DEFAULT ((0)) FOR [priplatekVIP]
;
ALTER TABLE [dbo].[rklic] ADD  DEFAULT ((0)) FOR [sluzbyPremium]
;
ALTER TABLE [dbo].[rklic] ADD  DEFAULT ((0)) FOR [priplatekPremium]
;
ALTER TABLE [dbo].[rklic] ADD  DEFAULT ((0)) FOR [sluzbyImax]
;
ALTER TABLE [dbo].[rklic] ADD  DEFAULT ((0)) FOR [priplatekImax]
;
ALTER TABLE [dbo].[rklic] ADD  DEFAULT ((0)) FOR [sluzbyUltraX]
;
ALTER TABLE [dbo].[rklic] ADD  DEFAULT ((0)) FOR [priplatekUltraX]
;
ALTER TABLE [dbo].[rklic] ADD  DEFAULT ('') FOR [vyslheslo]
;
ALTER TABLE [dbo].[rkurz] ADD  DEFAULT ('') FOR [nazev]
;
ALTER TABLE [dbo].[rkurz] ADD  DEFAULT ('') FOR [zuziv]
;
ALTER TABLE [dbo].[rkurz] ADD  DEFAULT (getdate()) FOR [zcas]
;
ALTER TABLE [dbo].[rkurz] ADD  DEFAULT ((1)) FOR [mnozs]
;
ALTER TABLE [dbo].[rkurz] ADD  DEFAULT ('') FOR [znISO]
;
ALTER TABLE [dbo].[rliga] ADD  CONSTRAINT [DF__rliga__zuziv__625A9A57]  DEFAULT ('') FOR [zuziv]
;
ALTER TABLE [dbo].[rlistIe] ADD  DEFAULT ('') FOR [ac]
;
ALTER TABLE [dbo].[rlistIe] ADD  DEFAULT ('') FOR [zuziv]
;
ALTER TABLE [dbo].[rlistIe] ADD  DEFAULT (getdate()) FOR [zcas]
;
ALTER TABLE [dbo].[rlistIe] ADD  DEFAULT ((0)) FOR [ku]
;
ALTER TABLE [dbo].[rlistky] ADD  CONSTRAINT [DF__rlistky__rpoklID__50FB042B]  DEFAULT ((0)) FOR [rpoklID]
;
ALTER TABLE [dbo].[rlistky] ADD  CONSTRAINT [DF_rlistky_rdokladID]  DEFAULT ((0)) FOR [rdokladID]
;
ALTER TABLE [dbo].[rlistky] ADD  CONSTRAINT [DF_rlistky_rodbID]  DEFAULT ((0)) FOR [rodbID]
;
ALTER TABLE [dbo].[rlistky] ADD  CONSTRAINT [DF_rlistky_sekce_1]  DEFAULT ('') FOR [sekce]
;
ALTER TABLE [dbo].[rlistky] ADD  CONSTRAINT [DF__rlistky__doklad__51EF2864]  DEFAULT ('') FOR [doklad]
;
ALTER TABLE [dbo].[rlistky] ADD  CONSTRAINT [DF__rlistky__sluzby__52E34C9D]  DEFAULT ((0)) FOR [sluzby]
;
ALTER TABLE [dbo].[rlistky] ADD  CONSTRAINT [DF__rlistky__fond__53D770D6]  DEFAULT ((0)) FOR [fond]
;
ALTER TABLE [dbo].[rlistky] ADD  CONSTRAINT [DF_rlistky_sp_1]  DEFAULT ((0)) FOR [sp]
;
ALTER TABLE [dbo].[rlistky] ADD  CONSTRAINT [DF__rlistky__sleva__54CB950F]  DEFAULT ('') FOR [sleva]
;
ALTER TABLE [dbo].[rlistky] ADD  CONSTRAINT [DF__rlistky__lvrat__55BFB948]  DEFAULT ((0)) FOR [lvrat]
;
ALTER TABLE [dbo].[rlistky] ADD  CONSTRAINT [DF__rlistky__lhoto__56B3DD81]  DEFAULT ((1)) FOR [lhoto]
;
ALTER TABLE [dbo].[rlistky] ADD  CONSTRAINT [DF_rlistky_labo]  DEFAULT ((0)) FOR [labo]
;
ALTER TABLE [dbo].[rlistky] ADD  CONSTRAINT [DF__rlistky__uzivpro__57A801BA]  DEFAULT ((0)) FOR [uzivprod]
;
ALTER TABLE [dbo].[rlistky] ADD  CONSTRAINT [DF__rlistky__uzivpok__589C25F3]  DEFAULT ((0)) FOR [uzivpokl]
;
ALTER TABLE [dbo].[rlistky] ADD  CONSTRAINT [DF__rlistky__pokladn__59904A2C]  DEFAULT ((0)) FOR [pokladna]
;
ALTER TABLE [dbo].[rlistky] ADD  CONSTRAINT [DF__rlistky__rada__5A846E65]  DEFAULT ('') FOR [rada]
;
ALTER TABLE [dbo].[rlistky] ADD  CONSTRAINT [DF__rlistky__misto__5B78929E]  DEFAULT ('') FOR [misto]
;
ALTER TABLE [dbo].[rlistky] ADD  CONSTRAINT [DF__rlistky__pozn__5C6CB6D7]  DEFAULT ('') FOR [pozn]
;
ALTER TABLE [dbo].[rlistky] ADD  CONSTRAINT [DF__rlistky__zuziv__5D60DB10]  DEFAULT ('') FOR [zuziv]
;
ALTER TABLE [dbo].[rlistky] ADD  CONSTRAINT [DF__rlistky__zcas__5E54FF49]  DEFAULT (getdate()) FOR [zcas]
;
ALTER TABLE [dbo].[rlistky] ADD  DEFAULT ((0)) FOR [vstup]
;
ALTER TABLE [dbo].[rlistky] ADD  DEFAULT ('') FOR [karta]
;
ALTER TABLE [dbo].[rlistky] ADD  DEFAULT ((0)) FOR [rmustrID]
;
ALTER TABLE [dbo].[rlistky] ADD  DEFAULT ((0)) FOR [sluzbyDbox]
;
ALTER TABLE [dbo].[rlistky] ADD  DEFAULT ((0)) FOR [okula]
;
ALTER TABLE [dbo].[rlistky] ADD  DEFAULT ((0)) FOR [sluzbyVIP]
;
ALTER TABLE [dbo].[rlistky] ADD  DEFAULT ((0)) FOR [priplatekVIP]
;
ALTER TABLE [dbo].[rlistky] ADD  DEFAULT ((0)) FOR [sluzbyPremium]
;
ALTER TABLE [dbo].[rlistky] ADD  DEFAULT ((0)) FOR [priplatekPremium]
;
ALTER TABLE [dbo].[rlistky] ADD  DEFAULT ((0)) FOR [sluzbyImax]
;
ALTER TABLE [dbo].[rlistky] ADD  DEFAULT ((0)) FOR [priplatekImax]
;
ALTER TABLE [dbo].[rlistky] ADD  DEFAULT ('') FOR [vouncher]
;
ALTER TABLE [dbo].[rlistky] ADD  DEFAULT ((0)) FOR [hodslevy]
;
ALTER TABLE [dbo].[rlistky] ADD  DEFAULT ((0)) FOR [cenkat]
;
ALTER TABLE [dbo].[rlistky] ADD  DEFAULT ((0)) FOR [sluzbyUltraX]
;
ALTER TABLE [dbo].[rlistky] ADD  DEFAULT ((0)) FOR [priplatekUltraX]
;
ALTER TABLE [dbo].[rlistkyRus] ADD  CONSTRAINT [DF__rlistkyRu__rpokl__412EB0B6]  DEFAULT ((0)) FOR [rpoklID]
;
ALTER TABLE [dbo].[rlistkyRus] ADD  CONSTRAINT [DF_rlistkyRus_rdokladID]  DEFAULT ((0)) FOR [rdokladID]
;
ALTER TABLE [dbo].[rlistkyRus] ADD  CONSTRAINT [DF_rlistkyRus_rodbID]  DEFAULT ((0)) FOR [rodbID]
;
ALTER TABLE [dbo].[rlistkyRus] ADD  CONSTRAINT [DF_rlistkyRus_sekce]  DEFAULT ('') FOR [sekce]
;
ALTER TABLE [dbo].[rlistkyRus] ADD  CONSTRAINT [DF__rlistkyRu__dokla__4222D4EF]  DEFAULT ('') FOR [doklad]
;
ALTER TABLE [dbo].[rlistkyRus] ADD  CONSTRAINT [DF__rlistkyRu__sluzb__4316F928]  DEFAULT ((0)) FOR [sluzby]
;
ALTER TABLE [dbo].[rlistkyRus] ADD  CONSTRAINT [DF__rlistkyRus__fond__440B1D61]  DEFAULT ((0)) FOR [fond]
;
ALTER TABLE [dbo].[rlistkyRus] ADD  CONSTRAINT [DF_rlistkyRus_sp]  DEFAULT ((0)) FOR [sp]
;
ALTER TABLE [dbo].[rlistkyRus] ADD  CONSTRAINT [DF__rlistkyRu__sleva__44FF419A]  DEFAULT ('') FOR [sleva]
;
ALTER TABLE [dbo].[rlistkyRus] ADD  CONSTRAINT [DF__rlistkyRu__lvrat__45F365D3]  DEFAULT ((0)) FOR [lvrat]
;
ALTER TABLE [dbo].[rlistkyRus] ADD  CONSTRAINT [DF__rlistkyRu__lhoto__46E78A0C]  DEFAULT ((1)) FOR [lhoto]
;
ALTER TABLE [dbo].[rlistkyRus] ADD  CONSTRAINT [DF_rlistkyRus_labo]  DEFAULT ((0)) FOR [labo]
;
ALTER TABLE [dbo].[rlistkyRus] ADD  CONSTRAINT [DF__rlistkyRu__uzivp__47DBAE45]  DEFAULT ((0)) FOR [uzivprod]
;
ALTER TABLE [dbo].[rlistkyRus] ADD  CONSTRAINT [DF__rlistkyRu__uzivp__48CFD27E]  DEFAULT ((0)) FOR [uzivpokl]
;
ALTER TABLE [dbo].[rlistkyRus] ADD  CONSTRAINT [DF__rlistkyRu__pokla__49C3F6B7]  DEFAULT ((0)) FOR [pokladna]
;
ALTER TABLE [dbo].[rlistkyRus] ADD  CONSTRAINT [DF__rlistkyRus__rada__4AB81AF0]  DEFAULT ('') FOR [rada]
;
ALTER TABLE [dbo].[rlistkyRus] ADD  CONSTRAINT [DF__rlistkyRu__misto__4BAC3F29]  DEFAULT ('') FOR [misto]
;
ALTER TABLE [dbo].[rlistkyRus] ADD  CONSTRAINT [DF_rlistkyRus_pozn]  DEFAULT ('') FOR [pozn]
;
ALTER TABLE [dbo].[rlistkyRus] ADD  CONSTRAINT [DF__rlistkyRu__zuziv__4CA06362]  DEFAULT ('') FOR [zuziv]
;
ALTER TABLE [dbo].[rlistkyRus] ADD  CONSTRAINT [DF__rlistkyRus__zcas__4D94879B]  DEFAULT (getdate()) FOR [zcas]
;
ALTER TABLE [dbo].[rlistkyRus] ADD  DEFAULT ('') FOR [karta]
;
ALTER TABLE [dbo].[rlistkyRus] ADD  DEFAULT ((0)) FOR [sluzbyDbox]
;
ALTER TABLE [dbo].[rmustr] ADD  DEFAULT ((0)) FOR [rupravaID]
;
ALTER TABLE [dbo].[rmustr] ADD  DEFAULT ((0)) FOR [rodbID]
;
ALTER TABLE [dbo].[rmustr] ADD  DEFAULT ('') FOR [sekce]
;
ALTER TABLE [dbo].[rmustr] ADD  DEFAULT ('S') FOR [typ]
;
ALTER TABLE [dbo].[rmustr] ADD  DEFAULT ('') FOR [jmeno]
;
ALTER TABLE [dbo].[rmustr] ADD  DEFAULT ((9)) FOR [fontV]
;
ALTER TABLE [dbo].[rmustr] ADD  DEFAULT ((1)) FOR [tucne]
;
ALTER TABLE [dbo].[rmustr] ADD  DEFAULT ((0)) FOR [cursiva]
;
ALTER TABLE [dbo].[rmustr] ADD  DEFAULT ((0)) FOR [barvav]
;
ALTER TABLE [dbo].[rmustr] ADD  DEFAULT ((12632256)) FOR [barvas]
;
ALTER TABLE [dbo].[rmustr] ADD  DEFAULT ((1)) FOR [cena]
;
ALTER TABLE [dbo].[rmustr] ADD  DEFAULT ((1)) FOR [delegace]
;
ALTER TABLE [dbo].[rmustr] ADD  DEFAULT ((1)) FOR [zobrazit]
;
ALTER TABLE [dbo].[rmustr] ADD  DEFAULT ((0)) FOR [tol]
;
ALTER TABLE [dbo].[rmustr] ADD  DEFAULT ('') FOR [ceny]
;
ALTER TABLE [dbo].[rmustr] ADD  DEFAULT ('') FOR [zuziv]
;
ALTER TABLE [dbo].[rmustr] ADD  DEFAULT (getdate()) FOR [zcas]
;
ALTER TABLE [dbo].[rmustr] ADD  DEFAULT ((0)) FOR [levadvoj]
;
ALTER TABLE [dbo].[rmustr] ADD  DEFAULT ((0)) FOR [pravadvoj]
;
ALTER TABLE [dbo].[rodb] ADD  CONSTRAINT [DF__rodb__odpar__05A3D694]  DEFAULT ('') FOR [odpar]
;
ALTER TABLE [dbo].[rodb] ADD  CONSTRAINT [DF__rodb__ulice__0697FACD]  DEFAULT ('') FOR [ulice]
;
ALTER TABLE [dbo].[rodb] ADD  CONSTRAINT [DF__rodb__misto__078C1F06]  DEFAULT ('') FOR [misto]
;
ALTER TABLE [dbo].[rodb] ADD  CONSTRAINT [DF__rodb__psc__0880433F]  DEFAULT ('') FOR [psc]
;
ALTER TABLE [dbo].[rodb] ADD  CONSTRAINT [DF__rodb__jmeno1__09746778]  DEFAULT ('') FOR [jmeno1]
;
ALTER TABLE [dbo].[rodb] ADD  CONSTRAINT [DF__rodb__jmeno2__0A688BB1]  DEFAULT ('') FOR [jmeno2]
;
ALTER TABLE [dbo].[rodb] ADD  CONSTRAINT [DF__rodb__jmeno3__0B5CAFEA]  DEFAULT ('') FOR [jmeno3]
;
ALTER TABLE [dbo].[rodb] ADD  CONSTRAINT [DF__rodb__telefon1__0C50D423]  DEFAULT ('') FOR [telefon1]
;
ALTER TABLE [dbo].[rodb] ADD  CONSTRAINT [DF__rodb__telefon2__0D44F85C]  DEFAULT ('') FOR [telefon2]
;
ALTER TABLE [dbo].[rodb] ADD  CONSTRAINT [DF__rodb__telefon3__0E391C95]  DEFAULT ('') FOR [telefon3]
;
ALTER TABLE [dbo].[rodb] ADD  CONSTRAINT [DF__rodb__fax__0F2D40CE]  DEFAULT ('') FOR [fax]
;
ALTER TABLE [dbo].[rodb] ADD  CONSTRAINT [DF__rodb__email__10216507]  DEFAULT ('') FOR [email]
;
ALTER TABLE [dbo].[rodb] ADD  CONSTRAINT [DF__rodb__banka__11158940]  DEFAULT ('') FOR [banka]
;
ALTER TABLE [dbo].[rodb] ADD  CONSTRAINT [DF__rodb__ucet__1209AD79]  DEFAULT ('') FOR [ucet]
;
ALTER TABLE [dbo].[rodb] ADD  CONSTRAINT [DF__rodb__ico__12FDD1B2]  DEFAULT ('') FOR [ico]
;
ALTER TABLE [dbo].[rodb] ADD  CONSTRAINT [DF__rodb__dic__13F1F5EB]  DEFAULT ('') FOR [dic]
;
ALTER TABLE [dbo].[rodb] ADD  CONSTRAINT [DF_rodb_funkce1_1]  DEFAULT ('') FOR [funkce1]
;
ALTER TABLE [dbo].[rodb] ADD  CONSTRAINT [DF_rodb_vjezd_1]  DEFAULT ((0)) FOR [vjezd]
;
ALTER TABLE [dbo].[rodb] ADD  CONSTRAINT [DF__rodb__zuziv__14E61A24]  DEFAULT ('') FOR [zuziv]
;
ALTER TABLE [dbo].[rodb] ADD  CONSTRAINT [DF__rodb__zcas__15DA3E5D]  DEFAULT (getdate()) FOR [zcas]
;
ALTER TABLE [dbo].[rpokl] ADD  CONSTRAINT [DF__rpokl__pocstav__69C6B1F5]  DEFAULT ((0)) FOR [pocstav]
;
ALTER TABLE [dbo].[rpokl] ADD  CONSTRAINT [DF__rpokl__prodejho__6ABAD62E]  DEFAULT ((0)) FOR [prodejho]
;
ALTER TABLE [dbo].[rpokl] ADD  CONSTRAINT [DF__rpokl__prodejnh__6BAEFA67]  DEFAULT ((0)) FOR [prodejnh]
;
ALTER TABLE [dbo].[rpokl] ADD  CONSTRAINT [DF__rpokl__sluzbyho__6CA31EA0]  DEFAULT ((0)) FOR [sluzbyho]
;
ALTER TABLE [dbo].[rpokl] ADD  CONSTRAINT [DF__rpokl__sluzbynh__6D9742D9]  DEFAULT ((0)) FOR [sluzbynh]
;
ALTER TABLE [dbo].[rpokl] ADD  CONSTRAINT [DF_rpokl_spho_1]  DEFAULT ((0)) FOR [spho]
;
ALTER TABLE [dbo].[rpokl] ADD  CONSTRAINT [DF_rpokl_spneho]  DEFAULT ((0)) FOR [spneho]
;
ALTER TABLE [dbo].[rpokl] ADD  CONSTRAINT [DF__rpokl__vracenho__6E8B6712]  DEFAULT ((0)) FOR [vracenho]
;
ALTER TABLE [dbo].[rpokl] ADD  CONSTRAINT [DF__rpokl__vracennh__6F7F8B4B]  DEFAULT ((0)) FOR [vracennh]
;
ALTER TABLE [dbo].[rpokl] ADD  CONSTRAINT [DF__rpokl__oprijem__7073AF84]  DEFAULT ((0)) FOR [oprijem]
;
ALTER TABLE [dbo].[rpokl] ADD  CONSTRAINT [DF__rpokl__ovydej__7167D3BD]  DEFAULT ((0)) FOR [ovydej]
;
ALTER TABLE [dbo].[rpokl] ADD  CONSTRAINT [DF__rpokl__odvod__725BF7F6]  DEFAULT ((0)) FOR [odvod]
;
ALTER TABLE [dbo].[rpokl] ADD  CONSTRAINT [DF_rpokl_oprijemNh]  DEFAULT ((0)) FOR [oprijemNh]
;
ALTER TABLE [dbo].[rpokl] ADD  CONSTRAINT [DF_rpokl_ovydejNh]  DEFAULT ((0)) FOR [ovydejNh]
;
ALTER TABLE [dbo].[rpokl] ADD  CONSTRAINT [DF_rpokl_odvodNh]  DEFAULT ((0)) FOR [odvodNh]
;
ALTER TABLE [dbo].[rpokl] ADD  CONSTRAINT [DF__rpokl__zuziv__73501C2F]  DEFAULT ('') FOR [zuziv]
;
ALTER TABLE [dbo].[rpokl] ADD  CONSTRAINT [DF__rpokl__zcas__74444068]  DEFAULT (getdate()) FOR [zcas]
;
ALTER TABLE [dbo].[rpoukazy] ADD  DEFAULT ('') FOR [zuziv]
;
ALTER TABLE [dbo].[rpoukazy] ADD  DEFAULT (getdate()) FOR [zcas]
;
ALTER TABLE [dbo].[rprod] ADD  CONSTRAINT [DF__rprod__eu__6DCC4D03]  DEFAULT ((0)) FOR [eu]
;
ALTER TABLE [dbo].[rprod] ADD  CONSTRAINT [DF__rprod__zuziv__6EC0713C]  DEFAULT ('') FOR [zuziv]
;
ALTER TABLE [dbo].[rprog] ADD  CONSTRAINT [DF__rprog__ciskop__10E07F16]  DEFAULT ('') FOR [ciskop]
;
ALTER TABLE [dbo].[rprog] ADD  CONSTRAINT [DF__rprog__limit__11D4A34F]  DEFAULT ((0)) FOR [limit]
;
ALTER TABLE [dbo].[rprog] ADD  CONSTRAINT [DF__rprog__pujcovne__12C8C788]  DEFAULT ((0)) FOR [pujcovne]
;
ALTER TABLE [dbo].[rprog] ADD  CONSTRAINT [DF__rprog__cenfix__13BCEBC1]  DEFAULT ((0)) FOR [cenfix]
;
ALTER TABLE [dbo].[rprog] ADD  CONSTRAINT [DF__rprog__cenmin__14B10FFA]  DEFAULT ((0)) FOR [cenmin]
;
ALTER TABLE [dbo].[rprog] ADD  CONSTRAINT [DF__rprog__cenkat__15A53433]  DEFAULT ('') FOR [cenkat]
;
ALTER TABLE [dbo].[rprog] ADD  CONSTRAINT [DF__rprog__osa__1699586C]  DEFAULT ((0)) FOR [osa]
;
ALTER TABLE [dbo].[rprog] ADD  CONSTRAINT [DF__rprog__osaproc__178D7CA5]  DEFAULT ((0)) FOR [osaproc]
;
ALTER TABLE [dbo].[rprog] ADD  CONSTRAINT [DF__rprog__fk__1881A0DE]  DEFAULT ((0)) FOR [fk]
;
ALTER TABLE [dbo].[rprog] ADD  CONSTRAINT [DF__rprog__fkproc__1975C517]  DEFAULT ((0)) FOR [fkproc]
;
ALTER TABLE [dbo].[rprog] ADD  CONSTRAINT [DF_rprog_sp_1]  DEFAULT ((0)) FOR [sp]
;
ALTER TABLE [dbo].[rprog] ADD  CONSTRAINT [DF_rprog_spproc_1]  DEFAULT ((1)) FOR [spproc]
;
ALTER TABLE [dbo].[rprog] ADD  CONSTRAINT [DF__rprog__ps__1A69E950]  DEFAULT ((0)) FOR [ps]
;
ALTER TABLE [dbo].[rprog] ADD  CONSTRAINT [DF_rprog_priplsluz]  DEFAULT ((0)) FOR [priplsluz]
;
ALTER TABLE [dbo].[rprog] ADD  CONSTRAINT [DF__rprog__negrafick__1C5231C2]  DEFAULT ((0)) FOR [negraficky]
;
ALTER TABLE [dbo].[rprog] ADD  CONSTRAINT [DF_rprog_naweb]  DEFAULT ((1)) FOR [naweb]
;
ALTER TABLE [dbo].[rprog] ADD  CONSTRAINT [DF__rprog__stop__1D4655FB]  DEFAULT ((0)) FOR [stop]
;
ALTER TABLE [dbo].[rprog] ADD  CONSTRAINT [DF__rprog__odpadlo__1E3A7A34]  DEFAULT ((0)) FOR [odpadlo]
;
ALTER TABLE [dbo].[rprog] ADD  CONSTRAINT [DF__rprog__neucast__1F2E9E6D]  DEFAULT ((0)) FOR [neucast]
;
ALTER TABLE [dbo].[rprog] ADD  CONSTRAINT [DF__rprog__divaku__2022C2A6]  DEFAULT ((0)) FOR [divaku]
;
ALTER TABLE [dbo].[rprog] ADD  CONSTRAINT [DF__rprog__trzba__2116E6DF]  DEFAULT ((0)) FOR [trzba]
;
ALTER TABLE [dbo].[rprog] ADD  CONSTRAINT [DF__rprog__rezervace__220B0B18]  DEFAULT ((0)) FOR [rezervace]
;
ALTER TABLE [dbo].[rprog] ADD  CONSTRAINT [DF__rprog__fondcel__22FF2F51]  DEFAULT ((0)) FOR [fondcel]
;
ALTER TABLE [dbo].[rprog] ADD  CONSTRAINT [DF__rprog__priplcel__23F3538A]  DEFAULT ((0)) FOR [priplcel]
;
ALTER TABLE [dbo].[rprog] ADD  CONSTRAINT [DF__rprog__odrezerv__24E777C3]  DEFAULT ((0)) FOR [odrezerv]
;
ALTER TABLE [dbo].[rprog] ADD  CONSTRAINT [DF__rprog__cbanID__25DB9BFC]  DEFAULT ((0)) FOR [cbanID]
;
ALTER TABLE [dbo].[rprog] ADD  CONSTRAINT [DF__rprog__pohhot__26CFC035]  DEFAULT ('') FOR [pohhot]
;
ALTER TABLE [dbo].[rprog] ADD  CONSTRAINT [DF__rprog__pohfakt__27C3E46E]  DEFAULT ('') FOR [pohfakt]
;
ALTER TABLE [dbo].[rprog] ADD  CONSTRAINT [DF__rprog__pohzal__28B808A7]  DEFAULT ('') FOR [pohzal]
;
ALTER TABLE [dbo].[rprog] ADD  CONSTRAINT [DF__rprog__delegace__29AC2CE0]  DEFAULT ((0)) FOR [delegace]
;
ALTER TABLE [dbo].[rprog] ADD  CONSTRAINT [DF_rprog_casprod_1]  DEFAULT ((0)) FOR [casprod]
;
ALTER TABLE [dbo].[rprog] ADD  CONSTRAINT [DF__rprog__zuziv__2AA05119]  DEFAULT ('') FOR [zuziv]
;
ALTER TABLE [dbo].[rprog] ADD  CONSTRAINT [DF__rprog__zcas__2B947552]  DEFAULT (getdate()) FOR [zcas]
;
ALTER TABLE [dbo].[rprog] ADD  DEFAULT ((0)) FOR [neikar]
;
ALTER TABLE [dbo].[rprog] ADD  DEFAULT ((0)) FOR [rpoukazyID]
;
ALTER TABLE [dbo].[rprog] ADD  DEFAULT ('00') FOR [cistyp]
;
ALTER TABLE [dbo].[rprog] ADD  DEFAULT ('') FOR [rcsluzby]
;
ALTER TABLE [dbo].[rprog] ADD  DEFAULT ((0)) FOR [sluzbyDbox]
;
ALTER TABLE [dbo].[rprog] ADD  DEFAULT ((0)) FOR [sluzbyVIP]
;
ALTER TABLE [dbo].[rprog] ADD  DEFAULT ((0)) FOR [priplatekVIP]
;
ALTER TABLE [dbo].[rprog] ADD  DEFAULT ((0)) FOR [sluzbyPremium]
;
ALTER TABLE [dbo].[rprog] ADD  DEFAULT ((0)) FOR [priplatekPremium]
;
ALTER TABLE [dbo].[rprog] ADD  DEFAULT ((0)) FOR [sluzbyImax]
;
ALTER TABLE [dbo].[rprog] ADD  DEFAULT ((0)) FOR [priplatekImax]
;
ALTER TABLE [dbo].[rprog] ADD  DEFAULT ('') FOR [cistyp2]
;
ALTER TABLE [dbo].[rprog] ADD  DEFAULT ('') FOR [cistyp3]
;
ALTER TABLE [dbo].[rprog] ADD  DEFAULT ('') FOR [cistyp4]
;
ALTER TABLE [dbo].[rprog] ADD  DEFAULT ('') FOR [vs]
;
ALTER TABLE [dbo].[rprog] ADD  DEFAULT ('') FOR [jazyk]
;
ALTER TABLE [dbo].[rprog] ADD  DEFAULT ('') FOR [cfotrm]
;
ALTER TABLE [dbo].[rprog] ADD  DEFAULT ((0)) FOR [sluzbyUltraX]
;
ALTER TABLE [dbo].[rprog] ADD  DEFAULT ((0)) FOR [priplatekUltraX]
;
ALTER TABLE [dbo].[rprovoz] ADD  CONSTRAINT [DF_rprovoz_sDPH_1]  DEFAULT ((0)) FOR [sDPH]
;
ALTER TABLE [dbo].[rsedadla] ADD  CONSTRAINT [DF__rsedadla__rezerv__719CDDE7]  DEFAULT ((0)) FOR [rezervaceID]
;
ALTER TABLE [dbo].[rsedadla] ADD  CONSTRAINT [DF__rsedadla__jmeno__72910220]  DEFAULT ('') FOR [ceny]
;
ALTER TABLE [dbo].[rsedadla] ADD  CONSTRAINT [DF__rsedadla__delego__73852659]  DEFAULT ((0)) FOR [delegold]
;
ALTER TABLE [dbo].[rsedadla] ADD  CONSTRAINT [DF__rsedadla__cena__74794A92]  DEFAULT ((0)) FOR [cena]
;
ALTER TABLE [dbo].[rsedadla] ADD  CONSTRAINT [DF__rsedadla__uzivpr__756D6ECB]  DEFAULT ((0)) FOR [uzivprod]
;
ALTER TABLE [dbo].[rsedadla] ADD  CONSTRAINT [DF__rsedadla__ruzivI__76619304]  DEFAULT ((0)) FOR [ruzivID]
;
ALTER TABLE [dbo].[rsedadla] ADD  CONSTRAINT [DF__rsedadla__zuziv__7755B73D]  DEFAULT ('') FOR [zuziv]
;
ALTER TABLE [dbo].[rsedadla] ADD  CONSTRAINT [DF__rsedadla__zcas__7849DB76]  DEFAULT (getdate()) FOR [zcas]
;
ALTER TABLE [dbo].[rsedadla] ADD  DEFAULT ('') FOR [sleva]
;
ALTER TABLE [dbo].[rsekce] ADD  CONSTRAINT [DF__rsekce__csalu__02925FBF]  DEFAULT ('') FOR [csalu]
;
ALTER TABLE [dbo].[rsekce] ADD  CONSTRAINT [DF__rsekce__nazev__038683F8]  DEFAULT ('') FOR [nazev]
;
ALTER TABLE [dbo].[rsekce] ADD  CONSTRAINT [DF__rsekce__pc__047AA831]  DEFAULT ((0)) FOR [pc]
;
ALTER TABLE [dbo].[rsekce] ADD  CONSTRAINT [DF__rsekce__druh__056ECC6A]  DEFAULT ('S') FOR [druh]
;
ALTER TABLE [dbo].[rsekce] ADD  CONSTRAINT [DF_rsekce_negraficky_1]  DEFAULT ((0)) FOR [negraficky]
;
ALTER TABLE [dbo].[rsekce] ADD  CONSTRAINT [DF_rsekce_limit_1]  DEFAULT ((0)) FOR [limit]
;
ALTER TABLE [dbo].[rsekce] ADD  CONSTRAINT [DF__rsekce__fontV__0662F0A3]  DEFAULT ((9)) FOR [fontV]
;
ALTER TABLE [dbo].[rsekce] ADD  CONSTRAINT [DF__rsekce__tucne__075714DC]  DEFAULT ((1)) FOR [tucne]
;
ALTER TABLE [dbo].[rsekce] ADD  CONSTRAINT [DF__rsekce__cursiva__084B3915]  DEFAULT ((0)) FOR [cursiva]
;
ALTER TABLE [dbo].[rsekce] ADD  CONSTRAINT [DF__rsekce__col1__093F5D4E]  DEFAULT ((0)) FOR [col1]
;
ALTER TABLE [dbo].[rsekce] ADD  CONSTRAINT [DF__rsekce__col2__0A338187]  DEFAULT ((12632256)) FOR [col2]
;
ALTER TABLE [dbo].[rsekce] ADD  CONSTRAINT [DF__rsekce__col1d__0B27A5C0]  DEFAULT ((12632256)) FOR [col1d]
;
ALTER TABLE [dbo].[rsekce] ADD  CONSTRAINT [DF__rsekce__col2d__0C1BC9F9]  DEFAULT ((16777215)) FOR [col2d]
;
ALTER TABLE [dbo].[rsekce] ADD  CONSTRAINT [DF__rsekce__zuziv__0D0FEE32]  DEFAULT ('') FOR [zuziv]
;
ALTER TABLE [dbo].[rsekce] ADD  CONSTRAINT [DF__rsekce__zcas__0E04126B]  DEFAULT (getdate()) FOR [zcas]
;
ALTER TABLE [dbo].[rslev] ADD  CONSTRAINT [DF__rslev__sleva__07F6335A]  DEFAULT ((0)) FOR [sleva]
;
ALTER TABLE [dbo].[rslev] ADD  CONSTRAINT [DF__rslev__lproc__08EA5793]  DEFAULT ((0)) FOR [lproc]
;
ALTER TABLE [dbo].[rslev] ADD  CONSTRAINT [DF__rslev__prijmeni__09DE7BCC]  DEFAULT ('') FOR [prijmeni]
;
ALTER TABLE [dbo].[rslev] ADD  CONSTRAINT [DF__rslev__jmeno__0AD2A005]  DEFAULT ('') FOR [jmeno]
;
ALTER TABLE [dbo].[rslev] ADD  CONSTRAINT [DF__rslev__adrmis__0BC6C43E]  DEFAULT ('') FOR [adrmis]
;
ALTER TABLE [dbo].[rslev] ADD  CONSTRAINT [DF__rslev__adrul__0CBAE877]  DEFAULT ('') FOR [adrul]
;
ALTER TABLE [dbo].[rslev] ADD  CONSTRAINT [DF__rslev__psc__0DAF0CB0]  DEFAULT ('') FOR [psc]
;
ALTER TABLE [dbo].[rslev] ADD  CONSTRAINT [DF__rslev__telef__0EA330E9]  DEFAULT ('') FOR [telef]
;
ALTER TABLE [dbo].[rslev] ADD  CONSTRAINT [DF__rslev__zuziv__0F975522]  DEFAULT ('') FOR [zuziv]
;
ALTER TABLE [dbo].[rslev] ADD  CONSTRAINT [DF__rslev__zcas__108B795B]  DEFAULT (getdate()) FOR [zcas]
;
ALTER TABLE [dbo].[rslev] ADD  DEFAULT ((0)) FOR [lpcena]
;
ALTER TABLE [dbo].[rslev] ADD  DEFAULT ((0)) FOR [lnetisk]
;
ALTER TABLE [dbo].[rslev] ADD  DEFAULT ((0)) FOR [pocslev]
;
ALTER TABLE [dbo].[rslev] ADD  DEFAULT ((0)) FOR [lnest]
;
ALTER TABLE [dbo].[rslev] ADD  DEFAULT ('') FOR [sleva2]
;
ALTER TABLE [dbo].[rslev] ADD  DEFAULT ((1)) FOR [aktivni]
;
ALTER TABLE [dbo].[rslev] ADD  DEFAULT ((0)) FOR [liver]
;
ALTER TABLE [dbo].[rslev] ADD  DEFAULT ((0)) FOR [liver_3d]
;
ALTER TABLE [dbo].[rslev] ADD  DEFAULT ((0)) FOR [liver_po]
;
ALTER TABLE [dbo].[rslev] ADD  DEFAULT ((0)) FOR [pc]
;
ALTER TABLE [dbo].[rslev] ADD  DEFAULT ((0)) FOR [pocpo]
;
ALTER TABLE [dbo].[rslev] ADD  DEFAULT ((0)) FOR [nulsluzby]
;
ALTER TABLE [dbo].[rslev] ADD  DEFAULT ((0)) FOR [jenvoucher]
;
ALTER TABLE [dbo].[rslev] ADD  DEFAULT ((0)) FOR [pocZaNula]
;
ALTER TABLE [dbo].[rslevp] ADD  CONSTRAINT [DF__rslevp__sleva__339FAB6E]  DEFAULT ((0)) FOR [sleva]
;
ALTER TABLE [dbo].[rslevp] ADD  CONSTRAINT [DF__rslevp__lproc__3493CFA7]  DEFAULT ((0)) FOR [lproc]
;
ALTER TABLE [dbo].[rslevp] ADD  CONSTRAINT [DF__rslevp__prijmeni__3587F3E0]  DEFAULT ('') FOR [prijmeni]
;
ALTER TABLE [dbo].[rslevp] ADD  CONSTRAINT [DF__rslevp__jmeno__367C1819]  DEFAULT ('') FOR [jmeno]
;
ALTER TABLE [dbo].[rslevp] ADD  CONSTRAINT [DF__rslevp__adrmis__37703C52]  DEFAULT ('') FOR [adrmis]
;
ALTER TABLE [dbo].[rslevp] ADD  CONSTRAINT [DF__rslevp__adrul__3864608B]  DEFAULT ('') FOR [adrul]
;
ALTER TABLE [dbo].[rslevp] ADD  CONSTRAINT [DF__rslevp__psc__395884C4]  DEFAULT ('') FOR [psc]
;
ALTER TABLE [dbo].[rslevp] ADD  CONSTRAINT [DF__rslevp__telef__3A4CA8FD]  DEFAULT ('') FOR [telef]
;
ALTER TABLE [dbo].[rslevp] ADD  CONSTRAINT [DF__rslevp__zuziv__3B40CD36]  DEFAULT ('') FOR [zuziv]
;
ALTER TABLE [dbo].[rslevp] ADD  CONSTRAINT [DF__rslevp__zcas__3C34F16F]  DEFAULT (getdate()) FOR [zcas]
;
ALTER TABLE [dbo].[rslevp] ADD  DEFAULT ((1)) FOR [aktivni]
;
ALTER TABLE [dbo].[ruprava] ADD  CONSTRAINT [DF_ruprava_ruprava2ID_1]  DEFAULT ((0)) FOR [ruprava2ID]
;
ALTER TABLE [dbo].[ruprava] ADD  CONSTRAINT [DF__ruprava__nazev__4B422AD5]  DEFAULT ('Úprava') FOR [nazev]
;
ALTER TABLE [dbo].[ruprava] ADD  CONSTRAINT [DF_ruprava_nabo_1]  DEFAULT ((0)) FOR [nabo]
;
ALTER TABLE [dbo].[ruprava] ADD  CONSTRAINT [DF__ruprava__zuziv__4C364F0E]  DEFAULT ('') FOR [zuziv]
;
ALTER TABLE [dbo].[ruprava] ADD  CONSTRAINT [DF__ruprava__zcas__4D2A7347]  DEFAULT (getdate()) FOR [zcas]
;
ALTER TABLE [dbo].[ruziv] ADD  CONSTRAINT [DF__ruziv__Jmeno__77BFCB91]  DEFAULT ('') FOR [Jmeno]
;
ALTER TABLE [dbo].[ruziv] ADD  CONSTRAINT [DF__ruziv__zapis__78B3EFCA]  DEFAULT ((0)) FOR [zapis]
;
ALTER TABLE [dbo].[ruziv] ADD  CONSTRAINT [DF__ruziv__pokladna__79A81403]  DEFAULT ('') FOR [pokladna]
;
ALTER TABLE [dbo].[ruziv] ADD  CONSTRAINT [DF__ruziv__datOd__7A9C383C]  DEFAULT (getdate()) FOR [datOd]
;
ALTER TABLE [dbo].[ruziv] ADD  CONSTRAINT [DF__ruziv__pokD__7B905C75]  DEFAULT ((0)) FOR [pokD]
;
ALTER TABLE [dbo].[ruziv] ADD  CONSTRAINT [DF__ruziv__pokZ__7C8480AE]  DEFAULT ((0)) FOR [pokZ]
;
ALTER TABLE [dbo].[ruziv] ADD  CONSTRAINT [DF__ruziv__prace__7D78A4E7]  DEFAULT ('') FOR [prace]
;
ALTER TABLE [dbo].[ruziv] ADD  CONSTRAINT [DF__ruziv__vstup__7E6CC920]  DEFAULT ((0)) FOR [vstup]
;
ALTER TABLE [dbo].[ruziv] ADD  CONSTRAINT [DF__ruziv__old__7F60ED59]  DEFAULT ((0)) FOR [old]
;
ALTER TABLE [dbo].[ruziv] ADD  CONSTRAINT [DF_ruziv_nlist_1]  DEFAULT ((0)) FOR [nlist]
;
ALTER TABLE [dbo].[ruziv] ADD  CONSTRAINT [DF_ruziv_nabo_1]  DEFAULT ((0)) FOR [nabo]
;
ALTER TABLE [dbo].[ruziv] ADD  CONSTRAINT [DF__ruziv__zuziv__00551192]  DEFAULT ('') FOR [zuziv]
;
ALTER TABLE [dbo].[ruziv] ADD  DEFAULT ((0)) FOR [linternet]
;
ALTER TABLE [dbo].[rvst] ADD  CONSTRAINT [DF__rvst__csalu__1E6F845E]  DEFAULT ('') FOR [csalu]
;
ALTER TABLE [dbo].[rvst] ADD  CONSTRAINT [DF__rvst__nazev__1F63A897]  DEFAULT ('') FOR [nazev]
;
ALTER TABLE [dbo].[rvst] ADD  CONSTRAINT [DF_rvst_rkurzID1]  DEFAULT ((1)) FOR [rkurzID1]
;
ALTER TABLE [dbo].[rvst] ADD  CONSTRAINT [DF_rvst_rkurzID2_1]  DEFAULT ((1)) FOR [rkurzID2]
;
ALTER TABLE [dbo].[rvst] ADD  CONSTRAINT [DF_rvst_rkurzID3_1]  DEFAULT ((1)) FOR [rkurzID3]
;
ALTER TABLE [dbo].[rvst] ADD  CONSTRAINT [DF_rvst_rkurzID4_1]  DEFAULT ((1)) FOR [rkurzID4]
;
ALTER TABLE [dbo].[rvst] ADD  CONSTRAINT [DF_rvst_rkurzID5_1]  DEFAULT ((1)) FOR [rkurzID5]
;
ALTER TABLE [dbo].[rvst] ADD  CONSTRAINT [DF_rvst_rkurzID6_1]  DEFAULT ((1)) FOR [rkurzID6]
;
ALTER TABLE [dbo].[rvst] ADD  CONSTRAINT [DF__rvst__vst1__2057CCD0]  DEFAULT ((0)) FOR [vst1]
;
ALTER TABLE [dbo].[rvst] ADD  CONSTRAINT [DF__rvst__nazev1__214BF109]  DEFAULT ('') FOR [nazev1]
;
ALTER TABLE [dbo].[rvst] ADD  CONSTRAINT [DF__rvst__vst2__22401542]  DEFAULT ((0)) FOR [vst2]
;
ALTER TABLE [dbo].[rvst] ADD  CONSTRAINT [DF__rvst__nazev2__2334397B]  DEFAULT ('') FOR [nazev2]
;
ALTER TABLE [dbo].[rvst] ADD  CONSTRAINT [DF__rvst__vst3__24285DB4]  DEFAULT ((0)) FOR [vst3]
;
ALTER TABLE [dbo].[rvst] ADD  CONSTRAINT [DF__rvst__nazev3__251C81ED]  DEFAULT ('') FOR [nazev3]
;
ALTER TABLE [dbo].[rvst] ADD  CONSTRAINT [DF__rvst__vst4__2610A626]  DEFAULT ((0)) FOR [vst4]
;
ALTER TABLE [dbo].[rvst] ADD  CONSTRAINT [DF__rvst__nazev4__2704CA5F]  DEFAULT ('') FOR [nazev4]
;
ALTER TABLE [dbo].[rvst] ADD  CONSTRAINT [DF__rvst__vst5__27F8EE98]  DEFAULT ((0)) FOR [vst5]
;
ALTER TABLE [dbo].[rvst] ADD  CONSTRAINT [DF__rvst__nazev5__28ED12D1]  DEFAULT ('') FOR [nazev5]
;
ALTER TABLE [dbo].[rvst] ADD  CONSTRAINT [DF__rvst__vst6__29E1370A]  DEFAULT ((0)) FOR [vst6]
;
ALTER TABLE [dbo].[rvst] ADD  CONSTRAINT [DF__rvst__nazev6__2AD55B43]  DEFAULT ('') FOR [nazev6]
;
ALTER TABLE [dbo].[rvst] ADD  CONSTRAINT [DF__rvst__vst7__2BC97F7C]  DEFAULT ((0)) FOR [vst7]
;
ALTER TABLE [dbo].[rvst] ADD  CONSTRAINT [DF__rvst__nazev7__2CBDA3B5]  DEFAULT ('') FOR [nazev7]
;
ALTER TABLE [dbo].[rvst] ADD  CONSTRAINT [DF__rvst__vst8__2DB1C7EE]  DEFAULT ((0)) FOR [vst8]
;
ALTER TABLE [dbo].[rvst] ADD  CONSTRAINT [DF__rvst__nazev8__2EA5EC27]  DEFAULT ('') FOR [nazev8]
;
ALTER TABLE [dbo].[rvst] ADD  CONSTRAINT [DF__rvst__vst9__2F9A1060]  DEFAULT ((0)) FOR [vst9]
;
ALTER TABLE [dbo].[rvst] ADD  CONSTRAINT [DF__rvst__nazev9__308E3499]  DEFAULT ('') FOR [nazev9]
;
ALTER TABLE [dbo].[rvst] ADD  CONSTRAINT [DF__rvst__vst10__318258D2]  DEFAULT ((0)) FOR [vst10]
;
ALTER TABLE [dbo].[rvst] ADD  CONSTRAINT [DF__rvst__nazev10__32767D0B]  DEFAULT ('') FOR [nazev10]
;
ALTER TABLE [dbo].[rvst] ADD  CONSTRAINT [DF__rvst__vst11__336AA144]  DEFAULT ((0)) FOR [vst11]
;
ALTER TABLE [dbo].[rvst] ADD  CONSTRAINT [DF__rvst__nazev11__345EC57D]  DEFAULT ('') FOR [nazev11]
;
ALTER TABLE [dbo].[rvst] ADD  CONSTRAINT [DF__rvst__vst12__3552E9B6]  DEFAULT ((0)) FOR [vst12]
;
ALTER TABLE [dbo].[rvst] ADD  CONSTRAINT [DF__rvst__nazev12__36470DEF]  DEFAULT ('') FOR [nazev12]
;
ALTER TABLE [dbo].[rvst] ADD  CONSTRAINT [DF__rvst__vst13__373B3228]  DEFAULT ((0)) FOR [vst13]
;
ALTER TABLE [dbo].[rvst] ADD  CONSTRAINT [DF__rvst__nazev13__382F5661]  DEFAULT ('') FOR [nazev13]
;
ALTER TABLE [dbo].[rvst] ADD  CONSTRAINT [DF__rvst__vst14__39237A9A]  DEFAULT ((0)) FOR [vst14]
;
ALTER TABLE [dbo].[rvst] ADD  CONSTRAINT [DF__rvst__nazev14__3A179ED3]  DEFAULT ('') FOR [nazev14]
;
ALTER TABLE [dbo].[rvst] ADD  CONSTRAINT [DF__rvst__vst15__3B0BC30C]  DEFAULT ((0)) FOR [vst15]
;
ALTER TABLE [dbo].[rvst] ADD  CONSTRAINT [DF__rvst__nazev15__3BFFE745]  DEFAULT ('') FOR [nazev15]
;
ALTER TABLE [dbo].[rvst] ADD  CONSTRAINT [DF__rvst__vst16__3CF40B7E]  DEFAULT ((0)) FOR [vst16]
;
ALTER TABLE [dbo].[rvst] ADD  CONSTRAINT [DF__rvst__nazev16__3DE82FB7]  DEFAULT ('') FOR [nazev16]
;
ALTER TABLE [dbo].[rvst] ADD  CONSTRAINT [DF__rvst__vst17__3EDC53F0]  DEFAULT ((0)) FOR [vst17]
;
ALTER TABLE [dbo].[rvst] ADD  CONSTRAINT [DF__rvst__nazev17__3FD07829]  DEFAULT ('') FOR [nazev17]
;
ALTER TABLE [dbo].[rvst] ADD  CONSTRAINT [DF__rvst__vst18__40C49C62]  DEFAULT ((0)) FOR [vst18]
;
ALTER TABLE [dbo].[rvst] ADD  CONSTRAINT [DF__rvst__nazev18__41B8C09B]  DEFAULT ('') FOR [nazev18]
;
ALTER TABLE [dbo].[rvst] ADD  CONSTRAINT [DF__rvst__vst19__42ACE4D4]  DEFAULT ((0)) FOR [vst19]
;
ALTER TABLE [dbo].[rvst] ADD  CONSTRAINT [DF__rvst__nazev19__43A1090D]  DEFAULT ('') FOR [nazev19]
;
ALTER TABLE [dbo].[rvst] ADD  CONSTRAINT [DF__rvst__vst20__44952D46]  DEFAULT ((0)) FOR [vst20]
;
ALTER TABLE [dbo].[rvst] ADD  CONSTRAINT [DF__rvst__nazev20__4589517F]  DEFAULT ('') FOR [nazev20]
;
ALTER TABLE [dbo].[rvst] ADD  CONSTRAINT [DF__rvst__zuziv__467D75B8]  DEFAULT ('') FOR [zuziv]
;
ALTER TABLE [dbo].[rvst] ADD  CONSTRAINT [DF__rvst__zcas__477199F1]  DEFAULT (getdate()) FOR [zcas]
;
ALTER TABLE [dbo].[rvst] ADD  CONSTRAINT [DF__rvst__kategorie__4865BE2A]  DEFAULT ('') FOR [kategorie]
;
ALTER TABLE [dbo].[rvst] ADD  DEFAULT ((1)) FOR [aktivni]
;
ALTER TABLE [dbo].[rvst] ADD  DEFAULT ('') FOR [sleva2]
;
ALTER TABLE [dbo].[rvst] ADD  DEFAULT ('') FOR [sleva3]
;
ALTER TABLE [dbo].[rvst] ADD  DEFAULT ('') FOR [sleva4]
;
ALTER TABLE [dbo].[rvst] ADD  DEFAULT ('') FOR [sleva19]
;
ALTER TABLE [dbo].[rvst] ADD  DEFAULT ('') FOR [sleva18]
;
ALTER TABLE [dbo].[rvst] ADD  DEFAULT ('') FOR [sleva17]
;
ALTER TABLE [dbo].[rvtn] ADD  CONSTRAINT [DF__rvtn__pristup__3D5E1FD2]  DEFAULT ((1)) FOR [pristup]
;
ALTER TABLE [dbo].[rvtn] ADD  CONSTRAINT [DF__rvtn__kontrola__3E52440B]  DEFAULT ('') FOR [kontrola]
;
ALTER TABLE [dbo].[tisks] ADD  CONSTRAINT [DF__tisks__pc__18B6AB08]  DEFAULT ((0)) FOR [pc]
;
ALTER TABLE [dbo].[tisks] ADD  CONSTRAINT [DF__tisks__soubor__19AACF41]  DEFAULT ('') FOR [soubor]
;
ALTER TABLE [dbo].[tisks] ADD  CONSTRAINT [DF__tisks__dalsip__1A9EF37A]  DEFAULT ('') FOR [dalsip]
;
ALTER TABLE [dbo].[tisks] ADD  CONSTRAINT [DF__tisks__zuziv__1B9317B3]  DEFAULT ('') FOR [zuziv]
;
ALTER TABLE [dbo].[voucher] ADD  DEFAULT ('') FOR [typ]
;
ALTER TABLE [dbo].[voucher] ADD  DEFAULT ((1)) FOR [maxpo]
;
ALTER TABLE [dbo].[voucher] ADD  DEFAULT ((0)) FOR [pocpo]
;
ALTER TABLE [dbo].[voucher] ADD  DEFAULT ('') FOR [sleva]
;
ALTER TABLE [dbo].[voucher] ADD  DEFAULT ('') FOR [sleva3d]
;
ALTER TABLE [dbo].[voucher] ADD  DEFAULT ('') FOR [zuziv]
;
ALTER TABLE [dbo].[voucher] ADD  DEFAULT (getdate()) FOR [zcas]
;
ALTER TABLE [dbo].[zaloha] ADD  CONSTRAINT [DF__zaloha__komprim__3D7E1B63]  DEFAULT ((0)) FOR [komprim]
;
ALTER TABLE [dbo].[zaloha] ADD  CONSTRAINT [DF__zaloha__stav__3E723F9C]  DEFAULT ((0)) FOR [stav]
;
ALTER TABLE [dbo].[Acl]  WITH CHECK ADD  CONSTRAINT [FK_Acl_Acl$Privilege] FOREIGN KEY([idPrivilege])
REFERENCES [dbo].[Acl$Privilege] ([id])
;
ALTER TABLE [dbo].[Acl] CHECK CONSTRAINT [FK_Acl_Acl$Privilege]
;
ALTER TABLE [dbo].[Acl]  WITH CHECK ADD  CONSTRAINT [FK_Acl_Acl$Resource] FOREIGN KEY([idResource])
REFERENCES [dbo].[Acl$Resource] ([id])
;
ALTER TABLE [dbo].[Acl] CHECK CONSTRAINT [FK_Acl_Acl$Resource]
;
ALTER TABLE [dbo].[Acl]  WITH CHECK ADD  CONSTRAINT [FK_Acl_Acl$Role] FOREIGN KEY([idRole])
REFERENCES [dbo].[Acl$Role] ([id])
;
ALTER TABLE [dbo].[Acl] CHECK CONSTRAINT [FK_Acl_Acl$Role]
;
ALTER TABLE [dbo].[Acl$Role]  WITH CHECK ADD  CONSTRAINT [FK_Acl$Role_Acl$Role] FOREIGN KEY([idParent])
REFERENCES [dbo].[Acl$Role] ([id])
;
ALTER TABLE [dbo].[Acl$Role] CHECK CONSTRAINT [FK_Acl$Role_Acl$Role]
;
ALTER TABLE [dbo].[Acl$UserHasBranch]  WITH CHECK ADD  CONSTRAINT [FK_Acl$UserHasBranch_Acl$Branch] FOREIGN KEY([idBranch])
REFERENCES [dbo].[Acl$Branch] ([id])
;
ALTER TABLE [dbo].[Acl$UserHasBranch] CHECK CONSTRAINT [FK_Acl$UserHasBranch_Acl$Branch]
;
ALTER TABLE [dbo].[Acl$UserHasBranch]  WITH CHECK ADD  CONSTRAINT [FK_Acl$UserHasBranch_Acl$User] FOREIGN KEY([idUser])
REFERENCES [dbo].[Acl$User] ([id])
;
ALTER TABLE [dbo].[Acl$UserHasBranch] CHECK CONSTRAINT [FK_Acl$UserHasBranch_Acl$User]
;
ALTER TABLE [dbo].[Acl$UserHasRole]  WITH CHECK ADD  CONSTRAINT [FK_Acl$User has role_Acl$Role] FOREIGN KEY([idRole])
REFERENCES [dbo].[Acl$Role] ([id])
;
ALTER TABLE [dbo].[Acl$UserHasRole] CHECK CONSTRAINT [FK_Acl$User has role_Acl$Role]
;
ALTER TABLE [dbo].[Acl$UserHasRole]  WITH CHECK ADD  CONSTRAINT [FK_Acl$User has role_Acl$User] FOREIGN KEY([idUser])
REFERENCES [dbo].[Acl$User] ([id])
;
ALTER TABLE [dbo].[Acl$UserHasRole] CHECK CONSTRAINT [FK_Acl$User has role_Acl$User]
;

IF NOT EXISTS (
    SELECT * FROM sys.objects WHERE NAME='prodej' and objectproperty(object_id,'IsProcedure')=1
) EXEC (
    'CREATE proc [dbo].[prodej]
     @ljdok bit, @rsedadlaID int, @rprogID int, @rodbID int, @csalu char(6), @sekce char(3), @cena numeric(7,2),
     @sluzby numeric(6,2), @sleva char(8), @lvrat bit, @lhoto bit, @labo bit, @datprod datetime,
     @uzivprod int, @pokladna char(10), @rada char(5), @misto char(5), @pozn char(10), @rdokladID int = 0, @sp numeric(5,2) = 0, @karta char(12) = '''',@sluzbyDbox numeric(6,2)=0,
     @sluzbyVIP numeric(6,2)=0, @priplatekVIP numeric(6,2)=0, @sluzbyPremium numeric(6,2)=0, @priplatekPremium numeric(6,2)=0,@sluzbyImax numeric(6,2)=0,@priplatekImax numeric(6,2)=0,
     @rlistkyID int = 0 output,@okula numeric(6,2)=0,@vouncher char(10)='''',@cenkat tinyint = 0,@sluzbyUltraX numeric(6,2)=0,@priplatekUltraX numeric(6,2)=0
    AS
    BEGIN
    Begin Tran
     Declare @doklad char(10), @nlist as Int, @error int, @rmustrID int, @rprogID2 int, @hodslevy numeric(6,2)=0.0,@slevpo char(2) = ''''
     IF isnull(@rsedadlaID,0)>0 --Místenkový prodej
    SELECT @rmustrID=rmustrID,@rprogID2=rprogID FROM rsedadla where rsedadlaID=@rsedadlaID
        else --Nemístenkový prodej
    SET @rmustrID=0
        if ISNULL(@rmustrID,0)>0 AND @rprogID!=@rprogID2 --Nemělo by nastat sedadla nepatří k představení prodej
    BEGIN
           RAISERROR(''Predávané miesto nie je miestom daného predstavenia!'',16,1) WITH LOG
           ROLLBACK TRAN
           RETURN
    END
     IF @labo=1  -- Jde o pernamentku
    Update ruprava
    Set @nlist=ruprava.nabo+1, nabo=ruprava.nabo+1
        FROM ruprava Join Rprog ON ruprava.rupravaID=rprog.rupravaID Where rprog.rprogID=@rprogID
        ELSE  -- Jde o vstupenku
    --  IF @cena>0 AND Charindex(''V'',@pozn)=0  --Pouze FKMB
    Update rfilm
    Set @nlist=rfilm.nlist+1, nlist=rfilm.nlist+1
        From rfilm Join rprog On rfilm.rfilmID=rprog.rfilmID Where rprog.rprogID=@rprogID
    --  Else
    --    Set @nlist=0
        IF LEN(RTRIM(@sleva))>0 --Jedná se o služby
    SELECT @hodslevy=@cena-v.vst1-@sluzbyDbox FROM rprog p join rvst v ON p.rvstID=v.rvstID WHERE p.rprogID=@rprogID
        IF @cenkat>0
    SET @slevpo = (SELECT CASE  @cenkat WHEN 2 THEN v.sleva2 WHEN 3 THEN v.sleva3 WHEN 4 THEN v.sleva4 WHEN 17 THEN v.sleva17 WHEN 18 THEN v.sleva18 WHEN 19 THEN v.sleva19 ELSE '''' END
        FROM rvst v Join rprog p ON p.rvstID=v.rvstID WHERE p.rprogID=@rprogID)
        IF Len(Rtrim(@slevpo))>0 AND CharIndex(Rtrim(@slevpo),@sleva)=0 AND LEN(RTRIM(@sleva))<7
    SET @sleva=RTRIM(@sleva)+RTRIM(@slevpo)
    SET @error=@@error
    SET @cena=@cena+@priplatekVIP+@priplatekPremium+@priplatekImax+@priplatekUltraX
    SET @sluzby=@sluzby+@sluzbyVIP+@sluzbyPremium+@sluzbyImax+@sluzbyUltraX
        IF @ljdok=1  --Doklad je odvozen z ID
    Begin
    Insert Into rlistky (rsedadlaID,rprogID,rdokladID,rodbID,csalu,sekce,doklad,cena,sluzby,sp,sleva,lvrat,lhoto,labo,datprod,uzivprod,pokladna,rada,misto,pozn,karta,rmustrID,
                         sluzbyDbox,okula,sluzbyVIP,priplatekVIP,sluzbyPremium,priplatekPremium,sluzbyImax,priplatekImax,sluzbyUltraX,priplatekUltraX,vouncher,hodslevy,cenkat) Values
        (@rsedadlaID,@rprogID,@rdokladID,@rodbID,@csalu,@sekce,'''', @cena,@sluzby,@sp,@sleva,@lvrat,@lhoto,@labo,@datprod,@uzivprod,@pokladna,@rada,@misto,@pozn,@karta,@rmustrID,
         ISNULL(@sluzbyDbox,0),@okula,@sluzbyVIP,@priplatekVIP,@sluzbyPremium,@priplatekPremium,@sluzbyImax,@priplatekImax,@sluzbyUltraX,@priplatekUltraX,@vouncher,@hodslevy,@cenkat)
        SET @error=@@error
    SET @rlistkyID=@@identity
    SET @doklad = REPLICATE(''0'',10-len(cast(@rlistkyID as char(10))))+RTRIM(cast(@rlistkyID as char(10)))
    Update rlistky set doklad=@doklad where rlistkyID=@rlistkyID
        SET @error=@@error
    End
    ELSE  --Doklad je odvozen
    Begin
        SET @doklad = REPLICATE(''0'',10-len(cast(@nlist as char(10))))+RTRIM(cast(@nlist as char(10)))
        Insert Into rlistky (rsedadlaID,rprogID,rdokladID,rodbID,csalu,sekce,doklad,cena,sluzby,sp,sleva,lvrat,lhoto,labo,datprod,uzivprod,pokladna,rada,misto,pozn,karta,rmustrID,
              sluzbyDbox,okula,sluzbyVIP,priplatekVIP,sluzbyPremium,priplatekPremium,sluzbyImax,priplatekImax,sluzbyUltraX,priplatekUltraX,vouncher,hodslevy) Values
            (@rsedadlaID,@rprogID,@rdokladID,@rodbID,@csalu,@sekce,@doklad,@cena,@sluzby,@sp,@sleva,@lvrat,@lhoto,@labo,@datprod,@uzivprod,@pokladna,@rada,@misto,@pozn,@karta,@rmustrID,
              ISNULL(@sluzbyDbox,0),@okula,@sluzbyVIP,@priplatekVIP,@sluzbyPremium,@priplatekPremium,@sluzbyImax,@priplatekImax,@sluzbyUltraX,@priplatekUltraX,@vouncher,@hodslevy)
        SET @error=@@error
        SET @rlistkyID=@@identity
    End
       if LEN(Rtrim(@vouncher))>0 --AND  @error<=0
    update voucher set pocpo=pocpo+1 WHERE cislo=@vouncher
        IF @error>0
        rollback tran
      Else
        Commit tran
    Select @rlistkyID as rlistkyID
    END'
)
GO
