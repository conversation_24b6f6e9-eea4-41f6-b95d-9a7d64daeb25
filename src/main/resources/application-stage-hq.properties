# DATABASE
spring.datasource.psql.url=${DB_DSN}
spring.datasource.psql.username=${DB_USERNAME}
spring.datasource.psql.password=${DB_PASSWORD}

spring.datasource.mssql-cinemax.url=${DB_MSSQL_DSN};encrypt=false;databaseName=${DB_MSSQL_CINEMAX_DATABASE}
spring.datasource.mssql-cinemax.username=${DB_MSSQL_USERNAME}
spring.datasource.mssql-cinemax.password=${DB_MSSQL_PASSWORD}

spring.datasource.mssql-buffet.url=${DB_MSSQL_DSN};encrypt=false;databaseName=${DB_MSSQL_BUFFET_DATABASE}
spring.datasource.mssql-buffet.username=${DB_MSSQL_USERNAME}
spring.datasource.mssql-buffet.password=${DB_MSSQL_PASSWORD}

# LOCKS
cleevio.distributed-locks.redis.database=1

# FEATURES
features.outbox.synchronize-failed-events.cron=0 0/1 * * * *
features.common.synchronize-from-mssql.frequent.cron=0 0/5 * * * *
features.common.synchronize-from-mssql.infrequent.cron=0 0 0/1 * * *
features.common.synchronize-from-mssql.initial.cron=-
features.common.synchronize-from-mssql.buffet.cron=-
features.synchronization-from-mssql.reservation-heartbeat=-
features.reservation.synchronize-from-mssql.cron=-
features.screening.synchronize-deleted.cron=-
features.screening.synchronize-backup.cron=-
features.basket.vip.synchronize-from-mssql.cron=-
features.branch.synchronize-from-mssql.cron=0 0 0/1 * * *
features.product-component.synchronize-deleted.cron=-
features.discount-card-usage.delete-orphaned.cron=-
features.distributor.send-report-mail.cron=-
features.distributor.send-disfilm-report-mail.cron=-
features.table.synchronize-from-mssql.ignore-ids=
features.terminal-payment.internal-host-ip-address=
features.outbox-event.instant-sync.listener.enabled=false

# STORAGE
storage.local-url=http://***********:8082

# SENTRY
sentry.environment=stage-hq

# SPRING
spring.cloud.gcp.project-id=cinemax-api-dev
spring.cloud.gcp.credentials.location=${GCP_CREDENTIALS_FILE}

# CINEMAX CONFIG
cinemax.configuration.deployment-type=HEADQUARTERS
cinemax.configuration.branch-name=SK Centrala (Test)
