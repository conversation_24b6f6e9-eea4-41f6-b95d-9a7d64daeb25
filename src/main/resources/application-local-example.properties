# OPEN API
springdoc.api-docs.path=/api-docs
springdoc.swagger-ui.path=/api-docs/swagger-ui.html
springdoc.swagger-server=

# LOGGING
logging.level.root=INFO
#logging.level.org.springframework.web.filter.CommonsRequestLoggingFilter=DEBUG
logging.level.org.springframework=WARN
logging.level.org.zalando.logbook=TRACE

# DATABASE
spring.datasource.psql.driver-class-name=org.postgresql.Driver
spring.datasource.psql.url=****************************************
spring.datasource.psql.username=postgres
spring.datasource.psql.password=test

spring.datasource.mssql-cinemax.driver-class-name=com.microsoft.sqlserver.jdbc.SQLServerDriver
spring.datasource.mssql-cinemax.url=*************************************************************
spring.datasource.mssql-cinemax.username=sa
spring.datasource.mssql-cinemax.password=@BGf6iGc

spring.datasource.mssql-buffet.driver-class-name=com.microsoft.sqlserver.jdbc.SQLServerDriver
spring.datasource.mssql-buffet.url=************************************************************
spring.datasource.mssql-buffet.username=sa
spring.datasource.mssql-buffet.password=@BGf6iGc

# LOCKS
cleevio.distributed-locks.storage-type=LOCAL

# SECURITY
security.jwt.signingKey=cnFXWkNYYkdrek5pNUFwSHV5SzNXWFVpa2RVZUVwc0dERER3TVRaY1NRVTZabkh6eFpNb0xDVGt5Q2Y5UkpjeQ==
security.jwt.accessExpiration=900000
security.jwt.refreshExpiration=21600000

# FEATURES
features.outbox.synchronize-failed-events.cron=-
features.common.synchronize-from-mssql.frequent.cron=0 0/15 * * * *
features.common.synchronize-from-mssql.infrequent.cron=0 0 0/1 * * *
features.common.synchronize-from-mssql.initial.cron=-
features.synchronization-from-mssql.reservation-heartbeat=0 0/1 * * * *
features.synchronization-from-mssql.reservation-heartbeat.duration=PT1M
features.reservation.synchronize-from-mssql.cron=0 0/5 * * * *
features.screening.synchronize-deleted.cron=0 0/5 * * * *
features.screening.synchronize-backup.cron=0 0 0 * * *
features.basket.vip.synchronize-from-mssql.cron=0/5 * * * * *
features.branch.synchronize-from-mssql.cron=-
features.product-component.synchronize-deleted.cron=0 0/15 * * * *
features.discount-card-usage.delete-orphaned.cron=0 0 2 * * *
features.table.synchronize-from-mssql.ignore-ids=1,61
features.terminal-payment.internal-host-ip-address=
features.branch-sales.refresh-overview.cron=-
features.daily-closing.deduct-online-pos.cron=-
features.distributor.send-report-mail.cron=-
features.distributor.send-disfilm-report-mail.cron=-

# STORAGE
storage.type=LOCAL_STORAGE
storage.local-path=./src/main/resources/files/
storage.local-url=http://localhost:8080
storage.persistent-volume-mount-path=/pos

# INTEGRATION
integration.disfilm.base-url=https://api.disfilm.sk
integration.cinemax-web.base-url=https://test.cine-max.sk
integration.cinemax-web.secondary-base-url=
integration.cinemax-web.username=
integration.cinemax-web.password=
integration.pubsub.topics.headquarters-lists=
integration.pubsub.topics.branch-sales=
integration.pubsub.subscriptions.headquarters-lists=
integration.pubsub.subscriptions.branch-sales=

# SENTRY
sentry.dsn=

# SPRING
spring.cloud.gcp.project-id=cinemax-api-dev
spring.cloud.gcp.credentials.location=
spring.cloud.gcp.pubsub.enabled=false

# CINEMAX CONFIG
cinemax.configuration.deployment-type=HEADQUARTERS
cinemax.configuration.branch-name=Bratislava Bory
cinemax.configuration.branch-code=510640
cinemax.configuration.business-country=SK
cinemax.configuration.tax-rates.standard=23
cinemax.configuration.tax-rates.reduced=19
cinemax.configuration.tax-rates.super-reduced=5
cinemax.configuration.tax-rates.no-tax=0
cinemax.configuration.from-email-address=<EMAIL>
cinemax.configuration.disfilm-email-address=<EMAIL>
