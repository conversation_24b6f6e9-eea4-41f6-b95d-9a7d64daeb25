# OPEN API
springdoc.api-docs.path=/api-docs
springdoc.swagger-ui.path=/api-docs/swagger-ui.html
springdoc.swagger-server=

# LOGGING
logging.level.root=INFO
#logging.level.org.springframework.web.filter.CommonsRequestLoggingFilter=DEBUG
logging.level.org.springframework=WARN
logging.level.org.zalando.logbook=TRACE
#logging.level.com.zaxxer.hikari.HikariConfig=DEBUG
#logging.level.com.zaxxer.hikari=TRACE

# DATABASE
spring.datasource.psql.driver-class-name=org.postgresql.Driver
spring.datasource.psql.url=${DB_DSN}
spring.datasource.psql.username=${DB_USERNAME}
spring.datasource.psql.password=${DB_PASSWORD}

spring.datasource.mssql-cinemax.driver-class-name=com.microsoft.sqlserver.jdbc.SQLServerDriver
spring.datasource.mssql-cinemax.url=${DB_MSSQL_DSN};encrypt=false;databaseName=${DB_MSSQL_CINEMAX_DATABASE}
spring.datasource.mssql-cinemax.username=${DB_MSSQL_USERNAME}
spring.datasource.mssql-cinemax.password=${DB_MSSQL_PASSWORD}
spring.datasource.mssql-cinemax.validation-timeout=30000

spring.datasource.mssql-buffet.driver-class-name=com.microsoft.sqlserver.jdbc.SQLServerDriver
spring.datasource.mssql-buffet.url=${DB_MSSQL_DSN};encrypt=false;databaseName=${DB_MSSQL_BUFFET_DATABASE}
spring.datasource.mssql-buffet.username=${DB_MSSQL_USERNAME}
spring.datasource.mssql-buffet.password=${DB_MSSQL_PASSWORD}

# LOCKS
cleevio.distributed-locks.storage-type=REDIS
cleevio.distributed-locks.redis.address=${REDIS_URL}
cleevio.distributed-locks.redis.database=30

# SECURITY
security.jwt.signingKey=eEdDSGVNY0tVbWZZR2g1Z0xRR1ZCVU1GdllrWWRmZ05lV0VidTd2eWhGVUU2dlBtNUo0cFZLVVNhRDdZN2JBcQ==
security.jwt.accessExpiration=900000
security.jwt.refreshExpiration=14400000

# FEATURES
features.outbox.synchronize-failed-events.cron=0 0/1 * * * *
features.common.synchronize-from-mssql.frequent.cron=0 0/5 * * * *
features.common.synchronize-from-mssql.infrequent.cron=0 0/10 * * * *
features.common.synchronize-from-mssql.initial.cron=-
features.synchronization-from-mssql.reservation-heartbeat=0 0/1 * * * *
features.synchronization-from-mssql.reservation-heartbeat.duration=PT1M
features.reservation.synchronize-from-mssql.cron=0/5 * * * * *
features.screening.synchronize-deleted.cron=0 0/5 * * * *
features.screening.synchronize-backup.cron=0 0 0 * * *
features.basket.vip.synchronize-from-mssql.cron=0/5 * * * * *
features.branch.synchronize-from-mssql.cron=-
features.product-component.synchronize-deleted.cron=0 0/15 * * * *
features.discount-card-usage.delete-orphaned.cron=0 0 2 * * *
features.table.synchronize-from-mssql.ignore-ids=1,61
features.terminal-payment.internal-host-ip-address=
features.branch-sales.refresh-overview.cron=0 0 3 * * *
features.daily-closing.deduct-online-pos.cron=59 59 12 * * *
features.group-reservation.online.deleted-expired.cron=0 0/1 * * * *
features.ticket.sales-and-moved-backup-sync.cron=1 0 0 * * *
features.distributor.send-report-mail.cron=-
features.distributor.send-disfilm-report-mail.cron=-

# STORAGE
storage.type=LOCAL_STORAGE
storage.local-path=/pos/files
storage.local-url=https://api.cinemax.devel.cleevio.dev
storage.persistent-volume-mount-path=/pos

# INTEGRATION
integration.disfilm.base-url=https://api.disfilm.sk
integration.cinemax-web.base-url=https://test.cine-max.sk
integration.cinemax-web.secondary-base-url=
integration.cinemax-web.username=${CINEMAX_WEB_USERNAME}
integration.cinemax-web.password=${CINEMAX_WEB_PASSWORD}
integration.pubsub.topics.headquarters-lists=headquarters-lists-dev
integration.pubsub.topics.branch-sales=branch-sales-dev
integration.pubsub.subscriptions.headquarters-lists=headquarters-lists-dev-sub
integration.pubsub.subscriptions.branch-sales=branch-sales-dev-sub
integration.cinemax-cards.auth-base-url=https://cinemax.eu.auth0.com
integration.cinemax-cards.base-url=http://***********:8083
integration.cinemax-cards.client-id=${CINEMAX_CARDS_CLIENT_ID}
integration.cinemax-cards.client-secret=${CINEMAX_CARDS_CLIENT_SECRET}

# SENTRY
sentry.dsn=https://<EMAIL>/102
sentry.environment=dev
sentry.traces-sample-rate=1.0

# SPRING
spring.cloud.gcp.project-id=cinemax-api-dev
spring.cloud.gcp.credentials.location=${GCP_CREDENTIALS_FILE}
spring.cloud.gcp.pubsub.enabled=true

# CINEMAX CONFIG
cinemax.configuration.deployment-type=BRANCH
cinemax.configuration.branch-name=Bratislava Bory (DEV)
cinemax.configuration.branch-code=510640
cinemax.configuration.business-country=SK
cinemax.configuration.tax-rates.standard=23
cinemax.configuration.tax-rates.reduced=19
cinemax.configuration.tax-rates.super-reduced=5
cinemax.configuration.tax-rates.no-tax=0
cinemax.configuration.from-email-address=<EMAIL>
cinemax.configuration.disfilm-email-address=

# EMAIL
spring.mail.host=smtp.postmarkapp.com
spring.mail.port=587
spring.mail.username=${SMTP_USERNAME}
spring.mail.password=${SMTP_PASSWORD}
