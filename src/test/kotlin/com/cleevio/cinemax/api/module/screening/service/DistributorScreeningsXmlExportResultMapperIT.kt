package com.cleevio.cinemax.api.module.screening.service

import com.cleevio.cinemax.api.IntegrationTest
import com.cleevio.cinemax.api.common.util.flattenXml
import com.cleevio.cinemax.api.module.screening.service.model.DistributorScreeningsXmlExportRecordModel
import com.cleevio.cinemax.api.module.screening.service.model.DistributorScreeningsXmlExportScreeningRecordModel
import com.cleevio.cinemax.api.module.screening.service.model.DistributorScreeningsXmlExportTicketRecordModel
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import java.time.LocalDateTime
import kotlin.test.assertEquals

class DistributorScreeningsXmlExportResultMapperIT @Autowired constructor(
    private val underTest: DistributorScreeningsXmlExportResultMapper,
) : IntegrationTest() {

    @Test
    fun `test mapToExportResultModel - should correctly map data to XML ExportResultModel`() {
        val screeningDateTime1 = LocalDateTime.parse("2024-08-01T18:00:00")
        val screeningDateTime2 = LocalDateTime.parse("2024-08-02T20:00:00")
        val testData = listOf(
            DistributorScreeningsXmlExportRecordModel(
                auditoriumOriginalCode = 101,
                auditoriumTitle = "Auditorium A",
                screenings = listOf(
                    DistributorScreeningsXmlExportScreeningRecordModel(
                        screeningOriginalId = 123,
                        screeningDateTime = screeningDateTime1,
                        movieCode = "MOV345",
                        movieDisfilmCode = "MOV123",
                        movieRawTitle = "Test Movie 1",
                        movieTechnologyTitle = "DCI2D",
                        movieLanguageCode = "SK",
                        groTicketSales = 1000.toBigDecimal(),
                        ticketsCount = 150,
                        ticketBasePrice = 10.toBigDecimal(),
                        tickets = listOf(
                            DistributorScreeningsXmlExportTicketRecordModel(
                                ticketPrice = 10.toBigDecimal(),
                                ticketsCount = 100
                            ),
                            DistributorScreeningsXmlExportTicketRecordModel(
                                ticketPrice = 15.toBigDecimal(),
                                ticketsCount = 50
                            )
                        )
                    )
                )
            ),
            DistributorScreeningsXmlExportRecordModel(
                auditoriumOriginalCode = 102,
                auditoriumTitle = "Auditorium B",
                screenings = listOf(
                    DistributorScreeningsXmlExportScreeningRecordModel(
                        screeningOriginalId = 124,
                        screeningDateTime = screeningDateTime2,
                        movieCode = "MOV345",
                        movieDisfilmCode = null,
                        movieRawTitle = "Test Movie 2",
                        movieTechnologyTitle = null,
                        movieLanguageCode = "CZ",
                        groTicketSales = 2000.toBigDecimal(),
                        ticketsCount = 200,
                        ticketBasePrice = 12.toBigDecimal(),
                        tickets = listOf(
                            DistributorScreeningsXmlExportTicketRecordModel(
                                ticketPrice = 12.toBigDecimal(),
                                ticketsCount = 150
                            ),
                            DistributorScreeningsXmlExportTicketRecordModel(
                                ticketPrice = 20.toBigDecimal(),
                                ticketsCount = 50
                            )
                        )
                    )
                )
            )
        )

        val result = underTest.mapToExportResultModel(testData)
        val xmlContent = result.inputStream.bufferedReader().use { it.readText() }.flattenXml()
        val exportDate = Regex("<exportDate>([^<]*)</exportDate>").find(xmlContent)?.groupValues?.get(1)
        val expectedXmlContent =
            """
                <?xml version="1.0" encoding="UTF-8" standalone="no"?>
                <data xmlns="http://www.disfilm.cz/xsd/cresults.xsd?x=4">
                    <export>
                        <code>510640</code>
                        <cinema>CINEMAX Bratislava Bory</cinema>
                        <securityCode></securityCode>
                        <exportDate>$exportDate</exportDate>
                    </export>
                    <results>
                        <result>
                            <ID>123</ID>
                            <date>2024-08-01T16:00:00Z</date>
                            <screenNr/>
                            <filmCode>MOV123</filmCode>
                            <title>Test Movie 1</title>
                            <copyCode/>
                            <format>DCI2D</format>
                            <language>SK</language>
                            <boxOffice>1000.00</boxOffice>
                            <boxOfficeNet/>
                            <admissions>150</admissions>
                            <performanceType/>
                            <price>10.00</price>
                            <cancel/>
                            <priceAdmissions>
                                <price>
                                    <admissions>100</admissions>
                                    <price>10.00</price>
                                    <ticketName/>
                                </price>
                                <price>
                                    <admissions>50</admissions>
                                    <price>15.00</price>
                                    <ticketName/>
                                </price>
                            </priceAdmissions>
                            <discounts/>
                        </result>
                        <result>
                            <ID>124</ID>
                            <date>2024-08-02T18:00:00Z</date>
                            <screenNr/>
                            <filmCode>MOV345</filmCode>
                            <title>Test Movie 2</title>
                            <copyCode/>
                            <format/>
                            <language>CZ</language>
                            <boxOffice>2000.00</boxOffice>
                            <boxOfficeNet/>
                            <admissions>200</admissions>
                            <performanceType/>
                            <price>12.00</price>
                            <cancel/>
                            <priceAdmissions>
                                <price>
                                    <admissions>150</admissions>
                                    <price>12.00</price>
                                    <ticketName/>
                                </price>
                                <price>
                                    <admissions>50</admissions>
                                    <price>20.00</price>
                                    <ticketName/>
                                </price>
                            </priceAdmissions>
                            <discounts/>
                        </result>
                    </results>
                </data>
            """.flattenXml()

        assertEquals(expectedXmlContent, xmlContent)
    }
}
