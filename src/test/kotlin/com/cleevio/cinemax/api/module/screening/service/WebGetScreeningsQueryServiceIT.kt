package com.cleevio.cinemax.api.module.screening.service

import com.cleevio.cinemax.api.DatabaseCleanup
import com.cleevio.cinemax.api.INTEGRATION_TEST_DATE_TIME
import com.cleevio.cinemax.api.IntegrationTest
import com.cleevio.cinemax.api.module.auditorium.service.AuditoriumService
import com.cleevio.cinemax.api.module.auditoriumlayout.service.AuditoriumLayoutRepository
import com.cleevio.cinemax.api.module.distributor.service.DistributorService
import com.cleevio.cinemax.api.module.language.entity.Language
import com.cleevio.cinemax.api.module.language.service.LanguageRepository
import com.cleevio.cinemax.api.module.movie.service.MovieService
import com.cleevio.cinemax.api.module.pricecategory.service.PriceCategoryService
import com.cleevio.cinemax.api.module.rating.entity.Rating
import com.cleevio.cinemax.api.module.rating.service.RatingRepository
import com.cleevio.cinemax.api.module.screening.constant.ScreeningState
import com.cleevio.cinemax.api.module.screening.service.command.DeleteScreeningCommand
import com.cleevio.cinemax.api.module.screening.service.query.WebGetScreeningsQuery
import com.cleevio.cinemax.api.module.technology.entity.Technology
import com.cleevio.cinemax.api.module.technology.service.TechnologyRepository
import com.cleevio.cinemax.api.module.tmslanguage.entity.TmsLanguage
import com.cleevio.cinemax.api.module.tmslanguage.service.TmsLanguageRepository
import com.cleevio.cinemax.api.util.createAuditorium
import com.cleevio.cinemax.api.util.createAuditoriumLayout
import com.cleevio.cinemax.api.util.createDistributor
import com.cleevio.cinemax.api.util.createMovie
import com.cleevio.cinemax.api.util.createPriceCategory
import com.cleevio.cinemax.api.util.createScreening
import com.cleevio.cinemax.api.util.mapToCreateOrUpdateAuditoriumCommand
import com.cleevio.cinemax.api.util.mapToCreateOrUpdateDistributorCommand
import com.cleevio.cinemax.api.util.mapToCreateOrUpdateMovieCommand
import com.cleevio.cinemax.api.util.mapToSyncCreateOrUpdatePriceCategoryCommand
import com.cleevio.cinemax.api.util.toUUID
import io.kotest.matchers.collections.shouldContainAll
import io.kotest.matchers.shouldBe
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import java.time.LocalTime

class WebGetScreeningsQueryServiceIT @Autowired constructor(
    private val underTest: WebGetScreeningsQueryService,
    private val screeningService: ScreeningService,
    private val auditoriumService: AuditoriumService,
    private val movieService: MovieService,
    private val priceCategoryService: PriceCategoryService,
    private val distributorService: DistributorService,
    private val auditoriumLayoutRepository: AuditoriumLayoutRepository,
    private val ratingRepository: RatingRepository,
    private val technologyRepository: TechnologyRepository,
    private val languageRepository: LanguageRepository,
    private val tmsLanguageRepository: TmsLanguageRepository,
    private val screeningRepository: ScreeningRepository,
) : IntegrationTest(DatabaseCleanup.AFTER_EACH) {

    @Test
    fun `test getScreenings - should filter by date range`() {
        val query = WebGetScreeningsQuery(
            dateFrom = INTEGRATION_TEST_DATE_TIME.toLocalDate(),
            dateTo = INTEGRATION_TEST_DATE_TIME.toLocalDate().plusDays(6)
        )

        val result = underTest(query).map { it.originalId }

        result.size shouldBe 2
        result shouldContainAll listOf(1, 8)
    }

    @Test
    fun `test getScreenings - should map response correctly`() {
        val query = WebGetScreeningsQuery(
            dateFrom = INTEGRATION_TEST_DATE_TIME.toLocalDate().plusDays(6),
            dateTo = INTEGRATION_TEST_DATE_TIME.toLocalDate().plusDays(6)
        )

        val result = underTest(query)

        // should find only screening 8
        result.size shouldBe 1
        result[0].run {
            // Verify screening fields
            originalId shouldBe SCREENING_8.originalId!!.toLong()
            date shouldBe SCREENING_8.date
            time shouldBe SCREENING_8.time

            // Verify movie fields
            movie.originalId shouldBe MOVIE_2.originalId!!.toLong()
            movie.rawTitle shouldBe MOVIE_2.rawTitle

            // Verify auditorium fields
            auditorium.code shouldBe AUDITORIUM_1.code
            auditorium.originalCode shouldBe AUDITORIUM_1.originalCode
            auditorium.title shouldBe AUDITORIUM_1.title
        }
    }

    @BeforeEach
    fun setUp() {
        setOf(AUDITORIUM_1, AUDITORIUM_2).forEach {
            auditoriumService.syncCreateOrUpdateAuditorium(
                mapToCreateOrUpdateAuditoriumCommand(it)
            )
        }
        setOf(AUDITORIUM_LAYOUT_1, AUDITORIUM_LAYOUT_2).forEach {
            auditoriumLayoutRepository.save(it)
        }
        setOf(DISTRIBUTOR_1, DISTRIBUTOR_2).forEach {
            distributorService.syncCreateOrUpdateDistributor(mapToCreateOrUpdateDistributorCommand(it))
        }
        ratingRepository.saveAll(setOf(RATING_1, RATING_2))
        technologyRepository.saveAll(setOf(TECHNOLOGY_1, TECHNOLOGY_2))
        languageRepository.saveAll(setOf(LANGUAGE_1, LANGUAGE_2))
        tmsLanguageRepository.saveAll(setOf(TMS_LANGUAGE_1, TMS_LANGUAGE_2))
        setOf(MOVIE_1, MOVIE_2).forEach {
            movieService.syncCreateOrUpdateMovie(mapToCreateOrUpdateMovieCommand(it))
        }
        priceCategoryService.syncCreateOrUpdatePriceCategory(
            mapToSyncCreateOrUpdatePriceCategoryCommand(PRICE_CATEGORY_1)
        )
        screeningRepository.saveAll(
            setOf(
                SCREENING_1,
                SCREENING_2,
                SCREENING_3,
                SCREENING_4,
                SCREENING_5,
                SCREENING_6,
                SCREENING_7,
                SCREENING_8
            )
        )
        screeningService.syncDeleteScreening(DeleteScreeningCommand(SCREENING_2.id))
    }

    private val LOCAL_TIME = LocalTime.of(20, 0, 0)
    private val AUDITORIUM_1 = createAuditorium(
        originalId = 1,
        code = "A",
        title = "SÁLA A - CINEMAX BRATISLAVA",
        originalCode = 22
    )
    private val AUDITORIUM_2 = createAuditorium(
        originalId = 2,
        code = "IMAX",
        title = "IMAX - CINEMAX BRATISLAVA",
        capacity = 150,
        originalCode = 11
    )
    private val DISTRIBUTOR_1 = createDistributor(originalId = 1)
    private val DISTRIBUTOR_2 = createDistributor(originalId = 2, title = "Hello movies, s.r.o.")
    private val AUDITORIUM_LAYOUT_1 = createAuditoriumLayout(originalId = 1, auditoriumId = AUDITORIUM_1.id)
    private val AUDITORIUM_LAYOUT_2 = createAuditoriumLayout(
        originalId = 2,
        auditoriumId = AUDITORIUM_2.id,
        title = "Special"
    )
    private val RATING_1 = Rating(
        originalId = 1,
        code = "13",
        title = "13"
    )
    private val RATING_2 = Rating(
        originalId = 2,
        code = "16",
        title = "16"
    )
    private val TECHNOLOGY_1 = Technology(
        originalId = 1,
        title = "2D",
        code = "2DA"
    )
    private val LANGUAGE_1 = Language(
        originalId = 10,
        title = "slovak",
        code = "SVK"
    )
    private val TMS_LANGUAGE_1 = TmsLanguage(
        originalId = 1,
        title = "Slovenske zneni",
        code = "TMS2"
    )
    private val TECHNOLOGY_2 = Technology(
        originalId = 2,
        title = "3D IMAX",
        code = "3I"
    )
    private val LANGUAGE_2 = Language(
        originalId = 11,
        title = "czech",
        code = "CZE"
    )
    private val TMS_LANGUAGE_2 = TmsLanguage(
        originalId = 2,
        title = "Ceske zneni",
        code = "TMS1"
    )
    private val MOVIE_1 = createMovie(
        originalId = 1,
        title = "Star Wars: Episode I – The Phantom Menace",
        releaseYear = 2001,
        distributorId = DISTRIBUTOR_1.id,
        ratingId = RATING_1.id,
        technologyId = TECHNOLOGY_1.id,
        languageId = LANGUAGE_1.id,
        tmsLanguageId = TMS_LANGUAGE_1.id
    )
    private val PRICE_CATEGORY_1 = createPriceCategory(
        originalId = 1,
        title = "ARTMAX PO 17",
        active = true
    )
    private val MOVIE_2 = createMovie(
        originalId = 2,
        code = "873946",
        title = "Matrix",
        rawTitle = "Matrix RAW",
        releaseYear = 2000,
        distributorId = DISTRIBUTOR_2.id,
        duration = 150,
        ratingId = RATING_2.id,
        technologyId = TECHNOLOGY_2.id,
        languageId = LANGUAGE_2.id,
        tmsLanguageId = TMS_LANGUAGE_2.id
    )

    // should be found
    private val SCREENING_1 = createScreening(
        id = 1.toUUID(),
        originalId = 1,
        auditoriumId = AUDITORIUM_1.id,
        auditoriumLayoutId = AUDITORIUM_LAYOUT_1.id,
        movieId = MOVIE_1.id,
        priceCategoryId = PRICE_CATEGORY_1.id,
        stopped = false,
        cancelled = false,
        publishOnline = true,
        state = ScreeningState.PUBLISHED,
        date = INTEGRATION_TEST_DATE_TIME.toLocalDate(),
        time = LOCAL_TIME.plusHours(2).plusMinutes(1)
    )

    // deleted
    private val SCREENING_2 = createScreening(
        id = 2.toUUID(),
        originalId = 2,
        auditoriumId = AUDITORIUM_2.id,
        auditoriumLayoutId = AUDITORIUM_LAYOUT_2.id,
        movieId = MOVIE_1.id,
        priceCategoryId = PRICE_CATEGORY_1.id,
        stopped = false,
        cancelled = false,
        publishOnline = true,
        state = ScreeningState.PUBLISHED,
        date = INTEGRATION_TEST_DATE_TIME.toLocalDate().plusDays(2),
        time = LOCAL_TIME.plusMinutes(20)
    )

    // out of selected interval
    private val SCREENING_3 = createScreening(
        id = 3.toUUID(),
        originalId = 3,
        auditoriumId = AUDITORIUM_1.id,
        auditoriumLayoutId = AUDITORIUM_LAYOUT_1.id,
        movieId = MOVIE_1.id,
        priceCategoryId = PRICE_CATEGORY_1.id,
        stopped = true,
        date = INTEGRATION_TEST_DATE_TIME.toLocalDate().plusDays(10),
        time = LOCAL_TIME.plusHours(2)
    )

    // cancelled
    private val SCREENING_4 = createScreening(
        id = 4.toUUID(),
        originalId = 4,
        auditoriumId = AUDITORIUM_1.id,
        auditoriumLayoutId = AUDITORIUM_LAYOUT_1.id,
        movieId = MOVIE_1.id,
        priceCategoryId = PRICE_CATEGORY_1.id,
        cancelled = true,
        stopped = false,
        publishOnline = true,
        state = ScreeningState.PUBLISHED,
        date = INTEGRATION_TEST_DATE_TIME.toLocalDate().plusDays(3),
        time = LOCAL_TIME.plusHours(2).minusMinutes(1)
    )

    // stopped
    private val SCREENING_5 = createScreening(
        id = 5.toUUID(),
        originalId = 5,
        auditoriumId = AUDITORIUM_1.id,
        auditoriumLayoutId = AUDITORIUM_LAYOUT_1.id,
        movieId = MOVIE_1.id,
        priceCategoryId = PRICE_CATEGORY_1.id,
        stopped = true,
        cancelled = false,
        publishOnline = true,
        state = ScreeningState.PUBLISHED,
        date = INTEGRATION_TEST_DATE_TIME.toLocalDate().plusDays(3)
    )

    // not in PUBLISHED state
    private val SCREENING_6 = createScreening(
        id = 6.toUUID(),
        originalId = 6,
        auditoriumId = AUDITORIUM_1.id,
        auditoriumLayoutId = AUDITORIUM_LAYOUT_1.id,
        movieId = MOVIE_1.id,
        priceCategoryId = PRICE_CATEGORY_1.id,
        state = ScreeningState.DRAFT,
        stopped = false,
        cancelled = false,
        publishOnline = true,
        date = INTEGRATION_TEST_DATE_TIME.toLocalDate().plusDays(5),
        time = LOCAL_TIME.plusHours(3).plusMinutes(10)
    )

    // not published online
    private val SCREENING_7 = createScreening(
        id = 7.toUUID(),
        originalId = 7,
        auditoriumId = AUDITORIUM_2.id,
        auditoriumLayoutId = AUDITORIUM_LAYOUT_2.id,
        movieId = MOVIE_2.id,
        priceCategoryId = PRICE_CATEGORY_1.id,
        stopped = false,
        cancelled = false,
        publishOnline = false,
        state = ScreeningState.PUBLISHED,
        date = INTEGRATION_TEST_DATE_TIME.toLocalDate().plusDays(8),
        time = LOCAL_TIME.plusMinutes(15)
    )

    // should be found
    private val SCREENING_8 = createScreening(
        id = 8.toUUID(),
        originalId = 8,
        auditoriumId = AUDITORIUM_1.id,
        auditoriumLayoutId = AUDITORIUM_LAYOUT_1.id,
        movieId = MOVIE_2.id,
        priceCategoryId = PRICE_CATEGORY_1.id,
        stopped = false,
        cancelled = false,
        publishOnline = true,
        state = ScreeningState.PUBLISHED,
        date = INTEGRATION_TEST_DATE_TIME.toLocalDate().plusDays(6),
        time = LOCAL_TIME.plusHours(3)
    )
}
