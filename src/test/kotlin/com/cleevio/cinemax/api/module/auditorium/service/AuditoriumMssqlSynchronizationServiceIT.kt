package com.cleevio.cinemax.api.module.auditorium.service

import com.cleevio.cinemax.api.MssqlIntegrationTest
import com.cleevio.cinemax.api.module.auditorium.service.command.CreateOrUpdateAuditoriumCommand
import com.cleevio.cinemax.api.module.synchronizationfrommssql.constant.SynchronizationFromMssqlType
import com.cleevio.cinemax.api.module.synchronizationfrommssql.entity.SynchronizationFromMssql
import com.cleevio.cinemax.api.module.synchronizationfrommssql.service.command.CreateOrUpdateSynchronizationFromMssqlCommand
import com.cleevio.cinemax.api.util.createBranch
import com.cleevio.cinemax.api.util.mapToCreateOrUpdateAuditoriumCommand
import io.mockk.every
import io.mockk.verify
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.test.context.jdbc.Sql
import org.springframework.test.context.jdbc.SqlConfig
import org.springframework.test.context.jdbc.SqlGroup
import java.time.LocalDateTime
import java.util.UUID
import kotlin.test.assertEquals
import kotlin.test.assertTrue

@SqlGroup(
    Sql(
        scripts = [
            "/db/mssql-cinemax/test/init_mssql_auditorium.sql"
        ],
        config = SqlConfig(
            dataSource = "mssqlCinemaxDataSource",
            transactionManager = "mssqlCinemaxTransactionManager"
        )
    ),
    Sql(
        scripts = [
            "/db/mssql-cinemax/test/drop_mssql_auditorium.sql"
        ],
        config = SqlConfig(
            dataSource = "mssqlCinemaxDataSource",
            transactionManager = "mssqlCinemaxTransactionManager"
        ),
        executionPhase = Sql.ExecutionPhase.AFTER_TEST_METHOD
    )
)
class AuditoriumMssqlSynchronizationServiceIT @Autowired constructor(
    private val underTest: AuditoriumMssqlSynchronizationService,
    private val auditoriumMssqlFinderRepository: AuditoriumMssqlFinderRepository,
) : MssqlIntegrationTest() {

    @Test
    fun `test synchronize all - no PSQL auditoriums, 3 MSSQL auditoriums - should create 3 auditoriums`() {
        every { synchronizationFromMssqlFinderServiceMock.findByType(any()) } returns null
        every { auditoriumServiceMock.syncCreateOrUpdateAuditorium(any()) } returns UUID.randomUUID()
        every { synchronizationFromMssqlServiceMock.createOrUpdate(any()) } returns SYNCHRONIZATION_FROM_MSSQL
        every { branchJpaFinderServiceMock.findByAuditoriumOriginalCodePrefixLike(any()) } returnsMany listOf(
            BRANCH_1,
            BRANCH_1,
            BRANCH_2
        )

        underTest.synchronizeAll()

        val commandCaptor = mutableListOf<CreateOrUpdateAuditoriumCommand>()
        val auditoriumPrefixCaptor = mutableListOf<String>()

        verify { synchronizationFromMssqlFinderServiceMock.findByType(SynchronizationFromMssqlType.AUDITORIUM) }
        verify { branchJpaFinderServiceMock.findByAuditoriumOriginalCodePrefixLike(capture(auditoriumPrefixCaptor)) }
        verify { auditoriumServiceMock.syncCreateOrUpdateAuditorium(capture(commandCaptor)) }
        verify {
            synchronizationFromMssqlServiceMock.createOrUpdate(
                CreateOrUpdateSynchronizationFromMssqlCommand(
                    type = SynchronizationFromMssqlType.AUDITORIUM,
                    lastSynchronization = AUDITORIUM_3_UPDATED_AT
                )
            )
        }

        assertTrue(auditoriumMssqlFinderRepository.findAllByUpdatedAtGt(null).size == 3)
        assertTrue(commandCaptor.size == 3)
        assertEquals(EXPECTED_COMMAND_1, commandCaptor[0])
        assertEquals(EXPECTED_COMMAND_2, commandCaptor[1])
        assertEquals(EXPECTED_COMMAND_3, commandCaptor[2])

        assertTrue(auditoriumPrefixCaptor.size == 3)
        assertTrue(auditoriumPrefixCaptor[0].startsWith(BRANCH_1.auditoriumOriginalCodePrefix))
        assertTrue(auditoriumPrefixCaptor[1].startsWith(BRANCH_1.auditoriumOriginalCodePrefix))
        assertTrue(auditoriumPrefixCaptor[2].startsWith(BRANCH_2.auditoriumOriginalCodePrefix))
    }

    @Test
    fun `test synchronize all - 2 PSQL auditoriums, 3 MSSQL auditoriums - should create 1 auditorium`() {
        every { synchronizationFromMssqlFinderServiceMock.findByType(any()) } returns AUDITORIUM_2_UPDATED_AT
        every { auditoriumServiceMock.syncCreateOrUpdateAuditorium(any()) } returns UUID.randomUUID()
        every { synchronizationFromMssqlServiceMock.createOrUpdate(any()) } returns SYNCHRONIZATION_FROM_MSSQL
        every { branchJpaFinderServiceMock.findByAuditoriumOriginalCodePrefixLike(any()) } returns BRANCH_2

        underTest.synchronizeAll()

        val commandCaptor = mutableListOf<CreateOrUpdateAuditoriumCommand>()
        val auditoriumPrefixCaptor = mutableListOf<String>()

        verify { synchronizationFromMssqlFinderServiceMock.findByType(SynchronizationFromMssqlType.AUDITORIUM) }
        verify { branchJpaFinderServiceMock.findByAuditoriumOriginalCodePrefixLike(capture(auditoriumPrefixCaptor)) }
        verify { auditoriumServiceMock.syncCreateOrUpdateAuditorium(capture(commandCaptor)) }
        verify {
            synchronizationFromMssqlServiceMock.createOrUpdate(
                CreateOrUpdateSynchronizationFromMssqlCommand(
                    type = SynchronizationFromMssqlType.AUDITORIUM,
                    lastSynchronization = AUDITORIUM_3_UPDATED_AT
                )
            )
        }

        assertTrue(auditoriumMssqlFinderRepository.findAllByUpdatedAtGt(AUDITORIUM_3_UPDATED_AT).isEmpty())
        assertTrue(commandCaptor.size == 1)
        assertEquals(EXPECTED_COMMAND_3, commandCaptor[0])
        assertTrue(commandCaptor.size == 1)
        assertTrue(auditoriumPrefixCaptor[0].startsWith(BRANCH_2.auditoriumOriginalCodePrefix))
    }
}

private val AUDITORIUM_2_UPDATED_AT = LocalDateTime.of(2007, 10, 10, 21, 0, 0)
private val AUDITORIUM_3_UPDATED_AT = LocalDateTime.of(2007, 10, 10, 21, 1, 0)

private val BRANCH_1 = createBranch(originalId = 1, auditoriumOriginalCodePrefix = "5226")
private val BRANCH_2 = createBranch(originalId = 2, auditoriumOriginalCodePrefix = "510")
private val EXPECTED_COMMAND_1 = mapToCreateOrUpdateAuditoriumCommand(
    originalId = 1,
    originalCode = 522630,
    branchId = BRANCH_1.id,
    title = "Sála A - Dunajská Streda",
    capacity = 222,
    city = "Dunajská Streda"
)
private val EXPECTED_COMMAND_2 = mapToCreateOrUpdateAuditoriumCommand(
    originalId = 2,
    originalCode = 522631,
    branchId = BRANCH_1.id,
    title = "Sála B - Dunajská Streda",
    capacity = 150,
    city = "Dunajská Streda"
)
private val EXPECTED_COMMAND_3 = mapToCreateOrUpdateAuditoriumCommand(
    originalId = 3,
    originalCode = 510360,
    branchId = BRANCH_2.id,
    title = "Sála A - CINEMAX BRATISLAVA",
    capacity = 88,
    city = "Bratislava"
)
private val SYNCHRONIZATION_FROM_MSSQL = SynchronizationFromMssql(
    type = SynchronizationFromMssqlType.AUDITORIUM,
    lastSynchronization = LocalDateTime.MIN,
    lastHeartbeat = null
)
