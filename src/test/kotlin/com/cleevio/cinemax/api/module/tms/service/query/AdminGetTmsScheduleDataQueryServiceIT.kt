package com.cleevio.cinemax.api.module.tms.service.query

import com.cleevio.cinemax.api.DatabaseCleanup
import com.cleevio.cinemax.api.INTEGRATION_TEST_DATE_TIME
import com.cleevio.cinemax.api.IntegrationTest
import com.cleevio.cinemax.api.module.auditorium.service.AuditoriumRepository
import com.cleevio.cinemax.api.module.auditoriumdefault.service.AuditoriumDefaultRepository
import com.cleevio.cinemax.api.module.auditoriumlayout.service.AuditoriumLayoutRepository
import com.cleevio.cinemax.api.module.basket.constant.BasketState
import com.cleevio.cinemax.api.module.basket.service.BasketRepository
import com.cleevio.cinemax.api.module.basketitem.constant.BasketItemType
import com.cleevio.cinemax.api.module.basketitem.service.BasketItemRepository
import com.cleevio.cinemax.api.module.distributor.service.DistributorRepository
import com.cleevio.cinemax.api.module.language.entity.Language
import com.cleevio.cinemax.api.module.language.service.LanguageJpaFinderService
import com.cleevio.cinemax.api.module.movie.entity.Movie
import com.cleevio.cinemax.api.module.movie.service.MovieRepository
import com.cleevio.cinemax.api.module.pricecategory.service.PriceCategoryRepository
import com.cleevio.cinemax.api.module.rating.entity.Rating
import com.cleevio.cinemax.api.module.rating.service.RatingJpaFinderService
import com.cleevio.cinemax.api.module.rating.service.RatingRepository
import com.cleevio.cinemax.api.module.reservation.constant.ReservationState
import com.cleevio.cinemax.api.module.reservation.service.ReservationRepository
import com.cleevio.cinemax.api.module.screening.constant.ScreeningState
import com.cleevio.cinemax.api.module.screening.entity.Screening
import com.cleevio.cinemax.api.module.screening.service.ScreeningRepository
import com.cleevio.cinemax.api.module.screeningtype.entity.ScreeningType
import com.cleevio.cinemax.api.module.screeningtype.service.D_BOX_SCREENING_TYPE_CODE
import com.cleevio.cinemax.api.module.screeningtype.service.IMAX_SCREENING_TYPE_CODE
import com.cleevio.cinemax.api.module.screeningtype.service.ScreeningTypeRepository
import com.cleevio.cinemax.api.module.screeningtype.service.ULTRA_X_SCREENING_TYPE_CODE
import com.cleevio.cinemax.api.module.screeningtype.service.VIP_SCREENING_TYPE_CODE
import com.cleevio.cinemax.api.module.screeningtypes.service.ScreeningTypesRepository
import com.cleevio.cinemax.api.module.seat.constant.SeatType
import com.cleevio.cinemax.api.module.seat.entity.Seat
import com.cleevio.cinemax.api.module.seat.service.SeatRepository
import com.cleevio.cinemax.api.module.technology.entity.Technology
import com.cleevio.cinemax.api.module.technology.service.TechnologyJpaFinderService
import com.cleevio.cinemax.api.module.ticket.service.TicketRepository
import com.cleevio.cinemax.api.module.ticketprice.service.TicketPriceRepository
import com.cleevio.cinemax.api.module.tmslanguage.entity.TmsLanguage
import com.cleevio.cinemax.api.module.tmslanguage.service.TmsLanguageJpaFinderService
import com.cleevio.cinemax.api.util.createAuditorium
import com.cleevio.cinemax.api.util.createAuditoriumDefault
import com.cleevio.cinemax.api.util.createAuditoriumLayout
import com.cleevio.cinemax.api.util.createBasket
import com.cleevio.cinemax.api.util.createBasketItem
import com.cleevio.cinemax.api.util.createDistributor
import com.cleevio.cinemax.api.util.createMovie
import com.cleevio.cinemax.api.util.createPriceCategory
import com.cleevio.cinemax.api.util.createReservation
import com.cleevio.cinemax.api.util.createScreening
import com.cleevio.cinemax.api.util.createScreeningType
import com.cleevio.cinemax.api.util.createScreeningTypes
import com.cleevio.cinemax.api.util.createSeat
import com.cleevio.cinemax.api.util.createTicket
import com.cleevio.cinemax.api.util.createTicketPrice
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.test.context.jdbc.Sql
import org.springframework.test.context.jdbc.SqlGroup
import java.time.LocalDate
import java.time.LocalTime
import java.time.format.DateTimeFormatter
import java.util.UUID
import kotlin.test.assertEquals
import kotlin.test.assertFalse
import kotlin.test.assertTrue

@SqlGroup(
    Sql(
        scripts = [
            "/db/migration/V106__init_rating_table_with_data.sql",
            "/db/migration/V107__init_technology_table_with_data.sql",
            "/db/migration/V108__init_language_table_with_data.sql",
            "/db/migration/V109__init_tms_language_table_with_data.sql"
        ]
    )
)
class AdminGetTmsScheduleDataQueryServiceIT @Autowired constructor(
    private val underTest: AdminGetTmsScheduleDataQueryService,
    private val auditoriumRepository: AuditoriumRepository,
    private val auditoriumLayoutRepository: AuditoriumLayoutRepository,
    private val distributorRepository: DistributorRepository,
    private val movieRepository: MovieRepository,
    private val priceCategoryRepository: PriceCategoryRepository,
    private val screeningRepository: ScreeningRepository,
    private val seatRepository: SeatRepository,
    private val basketRepository: BasketRepository,
    private val basketItemRepository: BasketItemRepository,
    private val ticketRepository: TicketRepository,
    private val reservationRepository: ReservationRepository,
    private val ticketPriceRepository: TicketPriceRepository,
    private val ratingJpaFinderService: RatingJpaFinderService,
    private val technologyJpaFinderService: TechnologyJpaFinderService,
    private val languageJpaFinderService: LanguageJpaFinderService,
    private val tmsLanguageJpaFinderService: TmsLanguageJpaFinderService,
    private val ratingRepository: RatingRepository,
    private val screeningTypeRepository: ScreeningTypeRepository,
    private val screeningTypesRepository: ScreeningTypesRepository,
    private val auditoriumDefaultRepository: AuditoriumDefaultRepository,
) : IntegrationTest(DatabaseCleanup.AFTER_EACH) {

    private lateinit var movie: Movie
    private lateinit var movieWithSubtitlesAndKidsAnd3d: Movie
    private lateinit var movieWithLongDuration: Movie
    private lateinit var rating: Rating
    private lateinit var kidsRating: Rating
    private lateinit var technology: Technology
    private lateinit var language: Language
    private lateinit var subtitledLanguage: Language
    private lateinit var tmsLanguage: TmsLanguage
    private lateinit var dboxType: ScreeningType
    private lateinit var vipType: ScreeningType
    private lateinit var imaxType: ScreeningType
    private lateinit var ultraXType: ScreeningType

    @BeforeEach
    fun setUp() {
        auditoriumRepository.saveAll(setOf(AUDITORIUM_1, AUDITORIUM_2))
        auditoriumLayoutRepository.saveAll(setOf(AUDITORIUM_LAYOUT_1, AUDITORIUM_LAYOUT_2))
        distributorRepository.save(DISTRIBUTOR_1)

        auditoriumDefaultRepository.saveAll(
            setOf(
                createAuditoriumDefault(
                    originalId = 1,
                    auditoriumId = AUDITORIUM_1.id,
                    surchargeUltraX = 0.toBigDecimal(),
                    serviceFeeUltraX = 0.toBigDecimal()
                ),
                createAuditoriumDefault(
                    originalId = 2,
                    auditoriumId = AUDITORIUM_2.id,
                    surchargeUltraX = 10.toBigDecimal(),
                    serviceFeeUltraX = 5.toBigDecimal()
                )
            )
        )

        rating = ratingJpaFinderService.findByCode("12")!!
        kidsRating = ratingJpaFinderService.findByCode("U")!!
        technology = technologyJpaFinderService.findByCode("03")!!
        language = languageJpaFinderService.findByCode("SK")!!
        subtitledLanguage = languageJpaFinderService.findByCode("SKST")!!
        tmsLanguage = tmsLanguageJpaFinderService.findByCode("S")!!

        dboxType = createScreeningType(originalId = 1, code = D_BOX_SCREENING_TYPE_CODE, title = "DBOX")
        vipType = createScreeningType(originalId = 2, code = VIP_SCREENING_TYPE_CODE, title = "VIP")
        imaxType = createScreeningType(originalId = 3, code = IMAX_SCREENING_TYPE_CODE, title = "IMAX")
        ultraXType = createScreeningType(originalId = 4, code = ULTRA_X_SCREENING_TYPE_CODE, title = "ULTRAX")
        screeningTypeRepository.saveAll(setOf(dboxType, vipType, imaxType, ultraXType))

        // Regular movie
        movie = createMovie(
            originalId = 1,
            title = "Star Wars: Episode I – The Phantom Menace",
            rawTitle = "Star Wars: Episode I – The Phantom Menace",
            releaseYear = 2001,
            distributorId = DISTRIBUTOR_1.id,
            ratingId = rating.id,
            technologyId = technology.id,
            languageId = language.id,
            tmsLanguageId = tmsLanguage.id,
            duration = 136
        )

        // Movie with subtitles, kids rating and 3D
        movieWithSubtitlesAndKidsAnd3d = createMovie(
            originalId = 2,
            title = "Toy Story 4",
            rawTitle = "Toy Story 4 3D",
            releaseYear = 2019,
            distributorId = DISTRIBUTOR_1.id,
            ratingId = kidsRating.id,
            technologyId = technology.id,
            languageId = subtitledLanguage.id,
            tmsLanguageId = tmsLanguage.id,
            duration = 100
        )

        // Movie with long duration
        movieWithLongDuration = createMovie(
            originalId = 3,
            title = "Mission: Impossible - Posledné zúčtovanie",
            rawTitle = "Mission: Impossible - Posledné zúčtovanie 2D ATMOS",
            releaseYear = 2025,
            distributorId = DISTRIBUTOR_1.id,
            ratingId = rating.id,
            technologyId = technology.id,
            languageId = language.id,
            tmsLanguageId = tmsLanguage.id,
            duration = 200
        )

        movieRepository.saveAll(
            setOf(
                movie,
                movieWithSubtitlesAndKidsAnd3d,
                movieWithLongDuration
            )
        )
        priceCategoryRepository.save(PRICE_CATEGORY_1)
        seatRepository.saveAll(
            setOf(
                SEAT_1,
                SEAT_2,
                SEAT_3_CANCELLED,
                SEAT_4_CANCELLED,
                SEAT_5,
                SEAT_6,
                SEAT_7,
                SEAT_8
            )
        )
    }

    @Test
    fun `test AdminGetTmsScheduleDataQuery - no screenings - should return empty sessions`() {
        val result = underTest()
        assertTrue(result.sessions.sessionList.isEmpty())
    }

    @Test
    fun `test AdminGetTmsScheduleDataQuery - screenings exist - should filter screenings correctly`() {
        // Valid screening with regular movie
        val validScreening1 = createTestScreening(
            originalId = 1,
            time = LocalTime.of(20, 30),
            date = INTEGRATION_TEST_DATE_TIME.plusDays(2).toLocalDate(),
            auditoriumId = AUDITORIUM_1.id,
            auditoriumLayoutId = AUDITORIUM_LAYOUT_1.id,
            movieId = movie.id,
            soldSeats = setOf(SEAT_1, SEAT_2, SEAT_3_CANCELLED),
            screeningTypeIds = listOf(
                dboxType.id to false,
                imaxType.id to false,
                ultraXType.id to false,
                vipType.id to true
            )
        )

        // Valid screening with subtitled kids movie and ULTRA_X type
        val validScreening2 = createTestScreening(
            originalId = 7,
            time = LocalTime.of(16, 30),
            date = INTEGRATION_TEST_DATE_TIME.plusDays(1).toLocalDate(),
            auditoriumId = AUDITORIUM_2.id,
            auditoriumLayoutId = AUDITORIUM_LAYOUT_2.id,
            movieId = movieWithSubtitlesAndKidsAnd3d.id,
            soldSeats = setOf(SEAT_5, SEAT_6, SEAT_7, SEAT_4_CANCELLED),
            screeningTypeIds = listOf()
        )

        // Valid screening with no ticket - should appear
        val validScreening3 = createTestScreening(
            originalId = 2,
            time = LocalTime.of(16, 30),
            date = INTEGRATION_TEST_DATE_TIME.plusDays(3).toLocalDate(),
            auditoriumId = AUDITORIUM_1.id,
            auditoriumLayoutId = AUDITORIUM_LAYOUT_1.id,
            movieId = movie.id,
            soldSeats = setOf()
        )

        // Past screening with sold tickets - should not appear
        createTestScreening(
            originalId = 3,
            date = INTEGRATION_TEST_DATE_TIME.toLocalDate().minusDays(1),
            soldSeats = setOf(SEAT_1)
        )

        // Cancelled screening with sold tickets - should not appear
        createTestScreening(
            originalId = 4,
            cancelled = true,
            soldSeats = setOf(SEAT_1)
        )

        // Stopped screening with sold tickets - should not appear
        createTestScreening(
            originalId = 5,
            stopped = true,
            soldSeats = setOf(SEAT_1)
        )

        // Deleted screening with sold tickets - should not appear
        createTestScreening(
            originalId = 6,
            deleted = true,
            soldSeats = setOf(SEAT_1)
        )

        // Screening not in PUBLISHED state - should not appear
        createTestScreening(
            originalId = 14,
            soldSeats = setOf(SEAT_1),
            state = ScreeningState.DRAFT
        )

        // Invalid screening - missing language
        val movieWithoutLanguage = createMovie(
            originalId = 101,
            title = "Invalid Movie 1",
            rawTitle = "Invalid Movie 1",
            releaseYear = 2001,
            distributorId = DISTRIBUTOR_1.id,
            ratingId = rating.id,
            technologyId = technology.id,
            languageId = null,
            tmsLanguageId = tmsLanguage.id,
            duration = 136
        )
        movieRepository.save(movieWithoutLanguage)
        createTestScreening(
            originalId = 8,
            movieId = movieWithoutLanguage.id,
            soldSeats = setOf(SEAT_1)
        )

        // Invalid screening - missing technology
        val movieWithoutTechnology = createMovie(
            originalId = 102,
            title = "Invalid Movie 2",
            rawTitle = "Invalid Movie 2",
            releaseYear = 2001,
            distributorId = DISTRIBUTOR_1.id,
            ratingId = rating.id,
            technologyId = null,
            languageId = language.id,
            tmsLanguageId = tmsLanguage.id,
            duration = 136
        )
        movieRepository.save(movieWithoutTechnology)
        createTestScreening(
            originalId = 9,
            movieId = movieWithoutTechnology.id,
            soldSeats = setOf(SEAT_1)
        )

        // Invalid screening - missing TMS language
        val movieWithoutTmsLanguage = createMovie(
            originalId = 103,
            title = "Invalid Movie 3",
            rawTitle = "Invalid Movie 3",
            releaseYear = 2001,
            distributorId = DISTRIBUTOR_1.id,
            ratingId = rating.id,
            technologyId = technology.id,
            languageId = language.id,
            tmsLanguageId = null,
            duration = 136
        )
        movieRepository.save(movieWithoutTmsLanguage)
        createTestScreening(
            originalId = 10,
            movieId = movieWithoutTmsLanguage.id,
            soldSeats = setOf(SEAT_1)
        )

        // Invalid screening - missing rating code
        val movieWithMissingRating = createMovie(
            originalId = 104,
            title = "Invalid Movie 4",
            rawTitle = "Invalid Movie 4",
            releaseYear = 2001,
            distributorId = DISTRIBUTOR_1.id,
            ratingId = null,
            technologyId = technology.id,
            languageId = language.id,
            tmsLanguageId = tmsLanguage.id,
            duration = 136
        )
        movieRepository.save(movieWithMissingRating)
        createTestScreening(
            originalId = 11,
            movieId = movieWithMissingRating.id,
            soldSeats = setOf(SEAT_1)
        )

        // Invalid screening - invalid rating code (99)
        val invalidRating = Rating(
            code = "99",
            title = "Invalid Rating",
            originalId = 999
        )
        ratingRepository.save(invalidRating)

        val movieWithInvalidRating = createMovie(
            originalId = 105,
            title = "Invalid Movie 5",
            rawTitle = "Invalid Movie 5",
            releaseYear = 2001,
            distributorId = DISTRIBUTOR_1.id,
            ratingId = invalidRating.id,
            technologyId = technology.id,
            languageId = language.id,
            tmsLanguageId = tmsLanguage.id,
            duration = 136
        )
        movieRepository.save(movieWithInvalidRating)
        createTestScreening(
            originalId = 12,
            movieId = movieWithInvalidRating.id,
            soldSeats = setOf(SEAT_1)
        )

        val result = underTest()

        // Should contain only valid screenings
        assertEquals(3, result.sessions.sessionList.size)

        // Verify first screening (regular movie, no special attributes)
        result.sessions.sessionList.first { it.id == validScreening1.originalId.toString() }.let { session ->
            val movieDurationWithAdTimeSlot = movie.duration!!.plus(validScreening1.adTimeSlot)

            assertEquals(validScreening1.originalId.toString(), session.id)
            assertEquals(AUDITORIUM_1.code, session.screenIdentifier)
            assertEquals(
                validScreening1.getScreeningTime().format(DateTimeFormatter.ofPattern("yyyyMMddHHmm")),
                session.start
            )
            assertEquals(
                validScreening1.getScreeningTime().plusMinutes(movieDurationWithAdTimeSlot.toLong())
                    .format(DateTimeFormatter.ofPattern("yyyyMMddHHmm")),
                session.end
            )
            assertEquals(movieDurationWithAdTimeSlot, session.featureDuration)
            assertEquals(movie.rawTitle, session.title)
            assertEquals(rating.code, session.rating)
            assertEquals(2, session.seatsAvailable)
            assertEquals(2, session.seatsSold)

            with(session.sessionAttributes.attributes) {
                assertTrue(contains(DISTRIBUTOR_1.title))
                assertTrue(contains(rating.code))
                assertTrue(contains(language.code))
                assertTrue(contains(technology.code))
                assertTrue(contains(tmsLanguage.title))
                assertFalse(contains("SUB"))
                assertFalse(contains("KIDS"))
                assertTrue(contains("DBOX"))
                assertTrue(contains("IMAX"))
                assertTrue(contains("ULTRAX"))
                assertFalse(contains("VIP"))
                assertFalse(contains("ATMOS"))
            }
        }

        // Verify second screening (subtitled kids movie with ULTRA_X and 3D)
        result.sessions.sessionList.first { it.id == validScreening2.originalId.toString() }.let { session ->
            val movieDurationWithAdTimeSlot = movieWithSubtitlesAndKidsAnd3d.duration!!.plus(validScreening2.adTimeSlot)

            assertEquals(validScreening2.originalId.toString(), session.id)
            assertEquals(AUDITORIUM_2.code, session.screenIdentifier)
            assertEquals(
                validScreening2.getScreeningTime().format(DateTimeFormatter.ofPattern("yyyyMMddHHmm")),
                session.start
            )
            assertEquals(
                validScreening2.getScreeningTime().plusMinutes(movieDurationWithAdTimeSlot.toLong())
                    .format(DateTimeFormatter.ofPattern("yyyyMMddHHmm")),
                session.end
            )
            assertEquals(movieDurationWithAdTimeSlot, session.featureDuration)
            assertEquals(movieWithSubtitlesAndKidsAnd3d.rawTitle, session.title)
            assertEquals(kidsRating.code, session.rating)
            assertEquals(1, session.seatsAvailable)
            assertEquals(3, session.seatsSold)

            with(session.sessionAttributes.attributes) {
                assertTrue(contains(DISTRIBUTOR_1.title))
                assertTrue(contains(kidsRating.code))
                assertTrue(contains(subtitledLanguage.code))
                assertTrue(contains(technology.code))
                assertTrue(contains(tmsLanguage.title))
                assertTrue(contains("3D"))
                assertTrue(contains("SUB"))
                assertTrue(contains("KIDS"))
                assertFalse(contains("ULTRAX"))
                assertTrue(contains("ATMOS"))
            }
        }

        // Verify third screening (no sold tickets)
        result.sessions.sessionList.first { it.id == validScreening3.originalId.toString() }.let { session ->
            val movieDurationWithAdTimeSlot = movie.duration!!.plus(validScreening3.adTimeSlot)

            assertEquals(validScreening3.originalId.toString(), session.id)
            assertEquals(AUDITORIUM_1.code, session.screenIdentifier)
            assertEquals(
                validScreening3.getScreeningTime().format(DateTimeFormatter.ofPattern("yyyyMMddHHmm")),
                session.start
            )
            assertEquals(
                validScreening3.getScreeningTime().plusMinutes(movieDurationWithAdTimeSlot.toLong())
                    .format(DateTimeFormatter.ofPattern("yyyyMMddHHmm")),
                session.end
            )
            assertEquals(movieDurationWithAdTimeSlot, session.featureDuration)
            assertEquals(movie.rawTitle, session.title)
            assertEquals(rating.code, session.rating)
            assertEquals(4, session.seatsAvailable)
            assertEquals(0, session.seatsSold)
        }
    }

    @Test
    fun `test AdminGetTmsScheduleDataQuery - screening's duration overflows midnight - should filter screenings correctly`() {
        val validScreening1 = createTestScreening(
            originalId = 1,
            time = LocalTime.of(22, 30),
            date = INTEGRATION_TEST_DATE_TIME.toLocalDate(),
            auditoriumId = AUDITORIUM_1.id,
            auditoriumLayoutId = AUDITORIUM_LAYOUT_1.id,
            movieId = movieWithLongDuration.id,
            soldSeats = setOf(SEAT_1, SEAT_2, SEAT_3_CANCELLED),
            screeningTypeIds = listOf(
                dboxType.id to false,
                imaxType.id to false,
                ultraXType.id to false,
                vipType.id to true
            )
        )

        val result = underTest()

        assertEquals(1, result.sessions.sessionList.size)

        result.sessions.sessionList.first { it.id == validScreening1.originalId.toString() }.let { session ->
            val movieDurationWithAdTimeSlot = movieWithLongDuration.duration!!.plus(validScreening1.adTimeSlot)

            assertEquals(validScreening1.originalId.toString(), session.id)
            assertEquals(AUDITORIUM_1.code, session.screenIdentifier)
            assertEquals(
                validScreening1.getScreeningTime().format(DateTimeFormatter.ofPattern("yyyyMMddHHmm")),
                session.start
            )
            assertEquals(
                validScreening1.getScreeningTime().plusMinutes(movieDurationWithAdTimeSlot.toLong())
                    .format(DateTimeFormatter.ofPattern("yyyyMMddHHmm")),
                session.end
            )
            assertEquals(movieDurationWithAdTimeSlot, session.featureDuration)
            assertEquals(movieWithLongDuration.rawTitle, session.title)
            assertEquals(rating.code, session.rating)
            assertEquals(2, session.seatsAvailable)
            assertEquals(2, session.seatsSold)

            with(session.sessionAttributes.attributes) {
                assertTrue(contains(DISTRIBUTOR_1.title))
                assertTrue(contains(rating.code))
                assertTrue(contains(language.code))
                assertTrue(contains(technology.code))
                assertTrue(contains(tmsLanguage.title))
                assertFalse(contains("SUB"))
                assertFalse(contains("KIDS"))
                assertTrue(contains("DBOX"))
                assertTrue(contains("IMAX"))
                assertTrue(contains("ULTRAX"))
                assertFalse(contains("VIP"))
                assertFalse(contains("ATMOS"))
            }
        }
    }

    private fun createTestScreening(
        date: LocalDate = INTEGRATION_TEST_DATE_TIME.toLocalDate(),
        time: LocalTime = LocalTime.of(19, 0),
        stopped: Boolean = false,
        cancelled: Boolean = false,
        deleted: Boolean = false,
        originalId: Int = 1,
        auditoriumId: UUID = AUDITORIUM_1.id,
        auditoriumLayoutId: UUID = AUDITORIUM_LAYOUT_1.id,
        movieId: UUID = movie.id,
        soldSeats: Set<Seat> = emptySet(),
        state: ScreeningState = ScreeningState.PUBLISHED,
        screeningTypeIds: List<Pair<UUID, Boolean>>? = emptyList(),
    ): Screening {
        val screening = createScreening(
            originalId = originalId,
            auditoriumId = auditoriumId,
            auditoriumLayoutId = auditoriumLayoutId,
            movieId = movieId,
            priceCategoryId = PRICE_CATEGORY_1.id,
            date = date,
            time = time,
            stopped = stopped,
            cancelled = cancelled,
            state = state
        ).also {
            if (deleted) it.markDeleted()
        }
        screeningRepository.save(screening)

        screeningTypeIds?.map { (id, isBlacklisted) ->
            createScreeningTypes(
                screeningId = screening.id,
                screeningTypeId = id,
                blacklisted = isBlacklisted
            )
        }?.let {
            screeningTypesRepository.saveAll(it)
        }

        val basket = createBasket(state = BasketState.PAID)
        basketRepository.save(basket)

        soldSeats.forEach { seat ->
            val ticketPrice = createTicketPrice(
                screeningId = screening.id,
                seatId = seat.id,
                totalPrice = 10.toBigDecimal()
            )
            ticketPriceRepository.save(ticketPrice)

            val reservation = createReservation(
                screeningId = screening.id,
                seatId = seat.id,
                state = ReservationState.UNAVAILABLE
            )
            reservationRepository.save(reservation)

            val ticket = createTicket(
                screeningId = screening.id,
                reservationId = reservation.id,
                ticketPriceId = ticketPrice.id
            )
            ticketRepository.save(ticket)

            val isCancelledSeat = seat.id in setOf(SEAT_3_CANCELLED.id, SEAT_4_CANCELLED.id)
            val cancelledBasketItem = if (isCancelledSeat) {
                createBasketItem(
                    basketId = basket.id,
                    type = BasketItemType.TICKET,
                    price = (-10).toBigDecimal()
                ).also { basketItemRepository.save(it) }
            } else {
                null
            }

            val basketItem = createBasketItem(
                basketId = basket.id,
                type = BasketItemType.TICKET,
                price = 10.toBigDecimal(),
                ticketId = ticket.id,
                cancelledBasketItemId = cancelledBasketItem?.id
            )

            basketItemRepository.save(basketItem)
        }

        return screening
    }
}

private val AUDITORIUM_1 = createAuditorium(
    originalId = 1,
    code = "A",
    title = "SÁLA A - CINEMAX BRATISLAVA",
    originalCode = 22
)
private val AUDITORIUM_2 = createAuditorium(
    originalId = 2,
    code = "B",
    title = "SÁLA B - CINEMAX BRATISLAVA",
    originalCode = 33
)
private val DISTRIBUTOR_1 = createDistributor(originalId = 1)
private val AUDITORIUM_LAYOUT_1 = createAuditoriumLayout(originalId = 1, auditoriumId = AUDITORIUM_1.id)
private val AUDITORIUM_LAYOUT_2 = createAuditoriumLayout(originalId = 2, auditoriumId = AUDITORIUM_2.id)
private val PRICE_CATEGORY_1 = createPriceCategory(
    originalId = 1,
    title = "ARTMAX PO 17",
    active = true
)
private val SEAT_1 = createSeat(
    originalId = 1,
    auditoriumId = AUDITORIUM_1.id,
    auditoriumLayoutId = AUDITORIUM_LAYOUT_1.id,
    type = SeatType.REGULAR
)
private val SEAT_2 = createSeat(
    originalId = 2,
    auditoriumId = AUDITORIUM_1.id,
    auditoriumLayoutId = AUDITORIUM_LAYOUT_1.id,
    type = SeatType.REGULAR
)
private val SEAT_3_CANCELLED = createSeat(
    originalId = 3,
    auditoriumId = AUDITORIUM_1.id,
    auditoriumLayoutId = AUDITORIUM_LAYOUT_1.id,
    type = SeatType.PREMIUM_PLUS
)
private val SEAT_4_CANCELLED = createSeat(
    originalId = 4,
    auditoriumId = AUDITORIUM_1.id,
    auditoriumLayoutId = AUDITORIUM_LAYOUT_1.id,
    type = SeatType.REGULAR
)
private val SEAT_5 = createSeat(
    originalId = 5,
    auditoriumId = AUDITORIUM_2.id,
    auditoriumLayoutId = AUDITORIUM_LAYOUT_2.id,
    type = SeatType.REGULAR
)
private val SEAT_6 = createSeat(
    originalId = 6,
    auditoriumId = AUDITORIUM_2.id,
    auditoriumLayoutId = AUDITORIUM_LAYOUT_2.id,
    type = SeatType.PREMIUM_PLUS
)
private val SEAT_7 = createSeat(
    originalId = 7,
    auditoriumId = AUDITORIUM_2.id,
    auditoriumLayoutId = AUDITORIUM_LAYOUT_2.id,
    type = SeatType.VIP
)
private val SEAT_8 = createSeat(
    originalId = 8,
    auditoriumId = AUDITORIUM_2.id,
    auditoriumLayoutId = AUDITORIUM_LAYOUT_2.id,
    type = SeatType.REGULAR
)
