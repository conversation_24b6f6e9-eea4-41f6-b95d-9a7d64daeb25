package com.cleevio.cinemax.api.module.supplier.service

import com.cleevio.cinemax.api.DatabaseCleanup
import com.cleevio.cinemax.api.IntegrationTest
import com.cleevio.cinemax.api.module.supplier.service.query.AdminSearchSuppliersFilter
import com.cleevio.cinemax.api.module.supplier.service.query.AdminSearchSuppliersQuery
import com.cleevio.cinemax.api.util.assertSuppliertoSearchSupplierResponseEquals
import com.cleevio.cinemax.api.util.createSupplier
import com.cleevio.cinemax.psql.tables.SupplierColumnNames
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.data.domain.PageRequest
import org.springframework.data.domain.Pageable
import org.springframework.data.domain.Sort
import kotlin.test.assertEquals

class AdminSearchSuppliersQueryServiceIT @Autowired constructor(
    private val underTest: AdminSearchSuppliersQueryService,
    private val supplierRepository: SupplierRepository,
) : IntegrationTest(DatabaseCleanup.AFTER_EACH) {

    @Test
    fun `test AdminSearchSuppliersQuery - should correctly return all records sorted`() {
        val supplier1 = createSupplier(originalId = 1, code = "OC1").also { supplierRepository.save(it) }
        val supplier2 = createSupplier(originalId = 2, code = "OC2").also { supplierRepository.save(it) }
        val supplier3 = createSupplier(originalId = 3, code = "OC3").also { supplierRepository.save(it) }

        val result = underTest(
            AdminSearchSuppliersQuery(
                filter = AdminSearchSuppliersFilter(),
                pageable = PageRequest.of(0, 10, Sort.by(SupplierColumnNames.CREATED_AT).descending())
            )
        )

        assertEquals(3, result.totalElements)
        assertEquals(1, result.totalPages)
        assertEquals(3, result.content.size)

        assertSuppliertoSearchSupplierResponseEquals(supplier3, result.content[0])
        assertSuppliertoSearchSupplierResponseEquals(supplier2, result.content[1])
        assertSuppliertoSearchSupplierResponseEquals(supplier1, result.content[2])
    }

    @Test
    fun `test AdminSearchSuppliersQuery - should correctly return filtered records`() {
        val supplier1 = createSupplier(
            originalId = 1,
            code = "OC1",
            title = "Amazing Nachos s.r.o."
        ).also { supplierRepository.save(it) }
        val supplier2 = createSupplier(
            originalId = 2,
            code = "OC2",
            title = "Popcorn Deluxe"
        ).also { supplierRepository.save(it) }
        val supplier3 = createSupplier(
            originalId = 3,
            code = "OC3",
            title = "Luxury candy a.s."
        ).also { supplierRepository.save(it) }

        val result1 = underTest(
            AdminSearchSuppliersQuery(
                filter = AdminSearchSuppliersFilter(title = "lux"),
                pageable = PageRequest.of(0, 10, Sort.by(SupplierColumnNames.CREATED_AT).descending())
            )
        )
        assertEquals(2, result1.totalElements)
        assertEquals(1, result1.totalPages)
        assertEquals(2, result1.content.size)
        assertSuppliertoSearchSupplierResponseEquals(supplier3, result1.content[0])
        assertSuppliertoSearchSupplierResponseEquals(supplier2, result1.content[1])

        val result2 = underTest(
            AdminSearchSuppliersQuery(
                filter = AdminSearchSuppliersFilter(title = "NACHO"),
                pageable = Pageable.unpaged()
            )
        )
        assertEquals(1, result2.totalElements)
        assertEquals(1, result2.totalPages)
        assertEquals(1, result2.content.size)
        assertSuppliertoSearchSupplierResponseEquals(supplier1, result2.content[0])
    }
}
