package com.cleevio.cinemax.api.module.supplier.entity

import com.cleevio.cinemax.api.module.supplier.event.toMessagingEvent
import com.cleevio.cinemax.api.util.createSupplier
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test
import java.util.Optional

class SupplierExtensionsTest {

    @Test
    fun `test toMessagingEvent - should map Supplier to AdminSupplierCreatedOrUpdatedEvent correctly`() {
        val supplier = createSupplier()
        val event = supplier.toMessagingEvent()

        assertEquals(supplier.code, event.code)
        assertEquals(supplier.title, event.title)
        assertEquals(Optional.ofNullable(supplier.addressStreet), event.addressStreet)
        assertEquals(Optional.ofNullable(supplier.addressCity), event.addressCity)
        assertEquals(Optional.ofNullable(supplier.addressPostCode), event.addressPostCode)
        assertEquals(Optional.ofNullable(supplier.contactName), event.contactName)
        assertEquals(Optional.ofNullable(supplier.contactPhone), event.contactPhone)
        assertEquals(Optional.ofNullable(supplier.contactEmails), event.contactEmails)
        assertEquals(Optional.ofNullable(supplier.bankName), event.bankName)
        assertEquals(Optional.ofNullable(supplier.bankAccount), event.bankAccount)
        assertEquals(Optional.ofNullable(supplier.idNumber), event.idNumber)
        assertEquals(Optional.ofNullable(supplier.taxIdNumber), event.taxIdNumber)
    }
}
