package com.cleevio.cinemax.api.module.basketitem.event.listener

import com.cleevio.cinemax.api.module.basket.service.BasketService
import com.cleevio.cinemax.api.module.basketitem.event.BasketItemWithPackagingDepositUpdatedEvent
import com.cleevio.cinemax.api.module.basketitem.event.ProductWithPackagingDepositFoundEvent
import com.cleevio.cinemax.api.module.basketitem.service.BasketItemService
import com.cleevio.cinemax.api.module.basketitem.service.command.CreatePackagingDepositBasketItemCommand
import com.cleevio.cinemax.api.module.basketitem.service.command.UpdatePackagingDepositBasketItemCommand
import com.cleevio.cinemax.api.util.toUUID
import io.mockk.every
import io.mockk.just
import io.mockk.mockk
import io.mockk.runs
import io.mockk.verify
import org.junit.jupiter.api.Test

class BasketItemBasketItemEventListenerTest {

    private val basketItemService = mockk<BasketItemService>()
    private val basketService = mockk<BasketService>()
    private val underTest = BasketItemBasketItemEventListener(
        basketItemService,
        basketService
    )

    @Test
    fun `test listenToProductWithPackagingDepositFoundEvent - should call everything accordingly`() {
        every { basketItemService.createPackagingDepositBasketItem(any()) } just runs

        underTest.listenToProductWithPackagingDepositFoundEvent(
            ProductWithPackagingDepositFoundEvent(
                packagingDepositProductId = 1.toUUID(),
                quantity = 3,
                basketId = 2.toUUID()
            )
        )

        verify {
            basketItemService.createPackagingDepositBasketItem(
                CreatePackagingDepositBasketItemCommand(
                    basketId = 2.toUUID(),
                    quantity = 3,
                    productId = 1.toUUID()
                )
            )
        }
    }

    @Test
    fun `test listenToBasketItemWithPackagingDepositUpdatedEvent - should call everything accordingly`() {
        every { basketItemService.updatePackagingDepositBasketItem(any()) } just runs

        underTest.listenToBasketItemWithPackagingDepositUpdatedEvent(
            BasketItemWithPackagingDepositUpdatedEvent(
                basketId = 1.toUUID(),
                packagingDepositItemId = 2.toUUID(),
                packagingDepositItemQuantity = -3
            )
        )

        verify {
            basketItemService.updatePackagingDepositBasketItem(
                UpdatePackagingDepositBasketItemCommand(
                    basketId = 1.toUUID(),
                    basketItemId = 2.toUUID(),
                    quantity = -3
                )
            )
        }
    }
}
