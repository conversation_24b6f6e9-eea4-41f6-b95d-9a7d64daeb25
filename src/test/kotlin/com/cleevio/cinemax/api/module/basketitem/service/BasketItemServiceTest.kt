package com.cleevio.cinemax.api.module.basketitem.service

import com.cleevio.cinemax.api.common.integration.cards.constant.CardsPosType
import com.cleevio.cinemax.api.module.basket.constant.BasketState
import com.cleevio.cinemax.api.module.basket.event.BasketItemModifiedEvent
import com.cleevio.cinemax.api.module.basket.event.ProductBasketItemAddedToBasketEvent
import com.cleevio.cinemax.api.module.basket.event.ProductBasketItemDeletedFromBasketEvent
import com.cleevio.cinemax.api.module.basket.event.ProductBasketItemQuantityModifiedEvent
import com.cleevio.cinemax.api.module.basket.exception.BasketNotFoundException
import com.cleevio.cinemax.api.module.basket.exception.InvalidBasketStateException
import com.cleevio.cinemax.api.module.basket.service.BasketDiscountCardService
import com.cleevio.cinemax.api.module.basket.service.BasketJpaFinderService
import com.cleevio.cinemax.api.module.basket.service.command.DeleteDiscountCardUsagesWhenDeletingBasketItemCommand
import com.cleevio.cinemax.api.module.basket.service.command.DeleteDiscountCardUsagesWhenPatchingBasketItemCommand
import com.cleevio.cinemax.api.module.basketitem.constant.BasketItemType
import com.cleevio.cinemax.api.module.basketitem.controller.dto.CreateBasketItemRequest
import com.cleevio.cinemax.api.module.basketitem.controller.dto.CreateProductRequest
import com.cleevio.cinemax.api.module.basketitem.controller.dto.CreateReservationRequest
import com.cleevio.cinemax.api.module.basketitem.controller.dto.CreateTicketPriceRequest
import com.cleevio.cinemax.api.module.basketitem.controller.dto.CreateTicketRequest
import com.cleevio.cinemax.api.module.basketitem.exception.BasketItemNotFoundException
import com.cleevio.cinemax.api.module.basketitem.exception.InvalidBasketItemStateException
import com.cleevio.cinemax.api.module.basketitem.exception.MultipleTicketsForOneSeatException
import com.cleevio.cinemax.api.module.basketitem.exception.ProductBasketItemWithInsufficientStockQuantityException
import com.cleevio.cinemax.api.module.basketitem.exception.TwoDiscountCardsAppliedOnTicketBasketItemException
import com.cleevio.cinemax.api.module.basketitem.service.command.CreateBasketItemCommand
import com.cleevio.cinemax.api.module.basketitem.service.command.PatchBasketItemCommand
import com.cleevio.cinemax.api.module.branch.service.BranchJpaFinderService
import com.cleevio.cinemax.api.module.discountcard.service.DiscountCardJooqFinderService
import com.cleevio.cinemax.api.module.discountcardusage.service.DiscountCardUsageRepository
import com.cleevio.cinemax.api.module.groupreservation.service.GroupReservationJpaFinderService
import com.cleevio.cinemax.api.module.pricecategoryitem.constant.PriceCategoryItemNumber
import com.cleevio.cinemax.api.module.product.service.ProductDiscountPriceService
import com.cleevio.cinemax.api.module.product.service.ProductJooqFinderService
import com.cleevio.cinemax.api.module.product.service.ProductJpaFinderService
import com.cleevio.cinemax.api.module.productcategory.service.ProductCategoryJooqFinderService
import com.cleevio.cinemax.api.module.reservation.service.ReservationJpaFinderService
import com.cleevio.cinemax.api.module.screening.service.ScreeningJpaFinderService
import com.cleevio.cinemax.api.module.seat.service.SeatJpaFinderService
import com.cleevio.cinemax.api.module.ticket.exception.TicketNotFoundException
import com.cleevio.cinemax.api.module.ticket.model.TicketModel
import com.cleevio.cinemax.api.module.ticket.service.TicketJooqFinderService
import com.cleevio.cinemax.api.module.ticket.service.TicketRepository
import com.cleevio.cinemax.api.module.ticket.service.TicketService
import com.cleevio.cinemax.api.module.ticket.service.command.CreateTicketCommand
import com.cleevio.cinemax.api.module.ticket.service.command.DeleteTicketCommand
import com.cleevio.cinemax.api.module.ticket.service.command.UpdateTicketCommand
import com.cleevio.cinemax.api.module.ticketdiscount.service.TicketDiscountJooqFinderService
import com.cleevio.cinemax.api.module.ticketprice.service.TicketPriceRepository
import com.cleevio.cinemax.api.util.createAuditorium
import com.cleevio.cinemax.api.util.createBasket
import com.cleevio.cinemax.api.util.createBasketItem
import com.cleevio.cinemax.api.util.createBranch
import com.cleevio.cinemax.api.util.createDiscountCard
import com.cleevio.cinemax.api.util.createMovie
import com.cleevio.cinemax.api.util.createProduct
import com.cleevio.cinemax.api.util.createReservation
import com.cleevio.cinemax.api.util.createScreeningWithCustomDateTimeLimit
import com.cleevio.cinemax.api.util.createTicket
import com.cleevio.cinemax.api.util.createTicketPrice
import com.cleevio.cinemax.api.util.mapToCreateBasketItemCommand
import com.cleevio.cinemax.api.util.mapToDeleteBasketItemCommand
import com.cleevio.library.lockinghandler.service.LockService
import io.mockk.Called
import io.mockk.Runs
import io.mockk.every
import io.mockk.just
import io.mockk.mockk
import io.mockk.verify
import io.mockk.verifySequence
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import org.springframework.context.ApplicationEventPublisher
import java.math.BigDecimal
import java.time.LocalDate
import java.time.LocalTime
import java.util.Optional
import java.util.UUID

class BasketItemServiceTest {

    private val basketItemRepository = mockk<BasketItemRepository>()
    private val basketItemJpaFinderService = mockk<BasketItemJpaFinderService>()
    private val basketJpaFinderService = mockk<BasketJpaFinderService>()
    private val applicationEventPublisher = mockk<ApplicationEventPublisher>()
    private val productFinderService = mockk<ProductJooqFinderService>()
    private val productJpaFinderService = mockk<ProductJpaFinderService>()
    private val ticketService = mockk<TicketService>()
    private val ticketJooqFinderService = mockk<TicketJooqFinderService>()
    private val productCategoryFinderService = mockk<ProductCategoryJooqFinderService>()
    private val ticketDiscountJooqFinderService = mockk<TicketDiscountJooqFinderService>()
    private val basketDiscountCardService = mockk<BasketDiscountCardService>()
    private val discountCardJooqFinderService = mockk<DiscountCardJooqFinderService>()
    private val screeningJpaFinderService = mockk<ScreeningJpaFinderService>()
    private val productDiscountPriceService = mockk<ProductDiscountPriceService>()
    private val reservationJpaFinderService = mockk<ReservationJpaFinderService>()
    private val groupReservationJpaFinderService = mockk<GroupReservationJpaFinderService>()
    private val seatJpaFinderService = mockk<SeatJpaFinderService>()
    private val branchFinderService = mockk<BranchJpaFinderService>()
    private val ticketRepository = mockk<TicketRepository>()
    private val ticketPriceRepository = mockk<TicketPriceRepository>()
    private val discountCardUsageRepository = mockk<DiscountCardUsageRepository>()
    private val basketItemVatAmountService = mockk<BasketItemVatAmountService>()
    private val lockService = mockk<LockService>()

    private val underTest = BasketItemService(
        basketItemRepository = basketItemRepository,
        basketItemJpaFinderService = basketItemJpaFinderService,
        basketJpaFinderService = basketJpaFinderService,
        applicationEventPublisher = applicationEventPublisher,
        productJooqFinderService = productFinderService,
        productJpaFinderService = productJpaFinderService,
        productCategoryJooqFinderService = productCategoryFinderService,
        ticketService = ticketService,
        ticketJooqFinderService = ticketJooqFinderService,
        ticketDiscountJooqFinderService = ticketDiscountJooqFinderService,
        basketDiscountCardService = basketDiscountCardService,
        discountCardJooqFinderService = discountCardJooqFinderService,
        screeningJpaFinderService = screeningJpaFinderService,
        productDiscountPriceService = productDiscountPriceService,
        reservationJpaFinderService = reservationJpaFinderService,
        groupReservationJpaFinderService = groupReservationJpaFinderService,
        seatJpaFinderService = seatJpaFinderService,
        branchFinderService = branchFinderService,
        ticketRepository = ticketRepository,
        ticketPriceRepository = ticketPriceRepository,
        discountCardUsageRepository = discountCardUsageRepository,
        basketItemVatAmountService = basketItemVatAmountService,
        lockService = lockService
    )

    @Test
    fun `test createBasketItem - ticket item - should call everything accordingly`() {
        every { branchFinderService.getCurrentBranch() } returns BRANCH_1
        every { basketJpaFinderService.getNonDeletedById(any()) } returns BASKET_1
        every { ticketService.createTicket(any()) } returns TICKET_MODEL_1
        every { ticketDiscountJooqFinderService.validateAndGetDiscounts(any(), any()) } returns Pair(null, null)
        every { ticketJooqFinderService.findAllNonDeletedByBasketId(any()) } returns listOf(TICKET_1)
        every { applicationEventPublisher.publishEvent(any<BasketItemModifiedEvent>()) } just Runs
        every { basketItemVatAmountService.determineVatAmount(any(), any(), any(), any()) } returns
            TICKET_MODEL_1.ticketPrice.totalPrice / 1.23.toBigDecimal()
        every { basketItemRepository.save(any()) } returns BASKET_ITEM_1

        val command = mapToCreateBasketItemCommand(BASKET_ITEM_REQUEST_1, BASKET_1.id)
        underTest.createBasketItem(command)

        verify { basketJpaFinderService.getNonDeletedById(BASKET_1.id) }
        verify {
            ticketService.createTicket(
                CreateTicketCommand(
                    screeningId = SCREENING_1.id,
                    seatId = SEAT_1_ID,
                    priceCategoryItemNumber = TICKET_PRICE_1.basePriceItemNumber,
                    freeTicket = false,
                    includes3dGlasses = false,
                    isGroupTicket = true
                )
            )
        }
        verify { ticketDiscountJooqFinderService.validateAndGetDiscounts(null, null) }
        verify { ticketJooqFinderService.findAllNonDeletedByBasketId(BASKET_1.id) }
        verify { productFinderService wasNot Called }
        verify { applicationEventPublisher.publishEvent(BasketItemModifiedEvent(BASKET_1.id, CardsPosType.VIP)) }
        // can't use verifySequence, mocking the repo fails with class cast exception: https://github.com/mockk/mockk/issues/321
        // verify { basketItemRepository.save(any()) }
    }

    @Test
    fun `test createBasketItem - product item with stock quantity - should call everything accordingly`() {
        every { branchFinderService.getCurrentBranch() } returns BRANCH_1
        every { basketJpaFinderService.getNonDeletedById(any()) } returns BASKET_1
        every { productFinderService.existsNonDeletedByIdAndStockQuantityGreaterThanOrEqualOne(any()) } returns true
        every { productFinderService.getNonDeletedById(any()) } returns PRODUCT_1
        every { productFinderService.findRelatedPackagingDepositProduct(any()) } returns null
        every { applicationEventPublisher.publishEvent(any<ProductBasketItemAddedToBasketEvent>()) } just Runs
        every { applicationEventPublisher.publishEvent(any<BasketItemModifiedEvent>()) } just Runs
        every { basketItemVatAmountService.determineVatAmount(any(), any(), any(), any()) } returns
            PRODUCT_1.price / 1.23.toBigDecimal()
        every { basketItemRepository.save(any()) } returns BASKET_ITEM_3

        val command = mapToCreateBasketItemCommand(BASKET_ITEM_REQUEST_3, BASKET_1.id)
        underTest.createBasketItem(command)

        verify { basketJpaFinderService.getNonDeletedById(BASKET_1.id) }
        verify { ticketService wasNot Called }
        verify { productFinderService.existsNonDeletedByIdAndStockQuantityGreaterThanOrEqualOne(PRODUCT_1_ID) }
        verify { productFinderService.getNonDeletedById(PRODUCT_1_ID) }
        verify { productFinderService.findRelatedPackagingDepositProduct(PRODUCT_1_ID) }
        verify {
            applicationEventPublisher.publishEvent(
                ProductBasketItemAddedToBasketEvent(
                    productId = PRODUCT_1_ID,
                    quantity = BASKET_ITEM_REQUEST_3.quantity
                )
            )
        }
        verify { applicationEventPublisher.publishEvent(BasketItemModifiedEvent(BASKET_1.id, CardsPosType.VIP)) }
        // can't use verifySequence, mocking the repo fails with class cast exception: https://github.com/mockk/mockk/issues/321
        // verify { basketItemRepository.save(any()) }
    }

    @Test
    fun `test createBasketItem - product item without stockQuantity - should call everything accordingly`() {
        every { basketJpaFinderService.getNonDeletedById(any()) } returns BASKET_1
        every { productFinderService.getNonDeletedById(any()) } returns PRODUCT_1
        every { productFinderService.existsNonDeletedByIdAndStockQuantityGreaterThanOrEqualOne(any()) } returns false
        every { productFinderService.findAllNonDeletedActiveProductStockQuantitiesByIdIn(any()) } returns mapOf(PRODUCT_1_ID to 0L.toBigDecimal())

        val command = mapToCreateBasketItemCommand(BASKET_ITEM_REQUEST_3, BASKET_1.id)

        assertThrows<ProductBasketItemWithInsufficientStockQuantityException> {
            underTest.createBasketItem(command)
        }

        verifySequence {
            basketJpaFinderService.getNonDeletedById(BASKET_1.id)
            productFinderService.getNonDeletedById(PRODUCT_1_ID)
            productFinderService.existsNonDeletedByIdAndStockQuantityGreaterThanOrEqualOne(PRODUCT_1_ID)
            productFinderService.findAllNonDeletedActiveProductStockQuantitiesByIdIn(setOf(PRODUCT_1_ID))
            ticketService wasNot Called
            basketItemJpaFinderService wasNot Called
            applicationEventPublisher wasNot Called
            basketItemRepository wasNot Called
        }
    }

    @Test
    fun `test createBasketItem - command with not existing basket - should throw exception`() {
        every { basketJpaFinderService.getNonDeletedById(any()) } throws BasketNotFoundException()

        val command = mapToCreateBasketItemCommand(BASKET_ITEM_REQUEST_1, BASKET_1.id)
        assertThrows<BasketNotFoundException> {
            underTest.createBasketItem(command)
        }

        verifySequence {
            ticketService wasNot Called
            productFinderService wasNot Called
            applicationEventPublisher wasNot Called
            basketItemRepository wasNot Called
        }
    }

    @Test
    fun `test createBasketItem - basket is in invalid state - should throw exception`() {
        every { basketJpaFinderService.getNonDeletedById(any()) } returns DELETED_BASKET

        val command = mapToCreateBasketItemCommand(BASKET_ITEM_REQUEST_1, BASKET_1.id)
        assertThrows<InvalidBasketStateException> {
            underTest.createBasketItem(command)
        }

        verifySequence {
            ticketService wasNot Called
            productFinderService wasNot Called
            applicationEventPublisher wasNot Called
            basketItemRepository wasNot Called
        }
    }

    @Test
    fun `test createBasketItem - command with quantity more than 1 - should throw exception`() {
        every { basketJpaFinderService.getNonDeletedById(any()) } returns BASKET_1

        val command = mapToCreateBasketItemCommand(BASKET_ITEM_REQUEST_2, BASKET_1.id)
        assertThrows<MultipleTicketsForOneSeatException> {
            underTest.createBasketItem(command)
        }

        verifySequence {
            ticketService wasNot Called
            productFinderService wasNot Called
            applicationEventPublisher wasNot Called
            basketItemRepository wasNot Called
        }
    }

    @Test
    fun `test createBasketItem - two discount card discounts applied at once - should throw exception`() {
        every { basketJpaFinderService.getNonDeletedById(any()) } returns BASKET_1

        assertThrows<TwoDiscountCardsAppliedOnTicketBasketItemException> {
            underTest.createBasketItem(CREATE_BASKET_ITEM_COMMAND)
        }

        verifySequence {
            ticketService wasNot Called
            productFinderService wasNot Called
            applicationEventPublisher wasNot Called
            basketItemRepository wasNot Called
        }
    }

    @Test
    fun `test deleteBasketItem - ticket item - should call everything accordingly`() {
        every { basketJpaFinderService.getNonDeletedById(any()) } returns BASKET_1
        every { basketItemJpaFinderService.getNonDeletedById(any()) } returns BASKET_ITEM_1
        every { basketDiscountCardService.deleteDiscountCardUsagesWhenDeletingBasketItem(any()) } just Runs
        every { ticketService.deleteTicket(any()) } just Runs
        every { basketItemRepository.save(any()) } returns BASKET_ITEM_1
        every { applicationEventPublisher.publishEvent(any<BasketItemModifiedEvent>()) } just Runs

        val command = mapToDeleteBasketItemCommand(BASKET_1.id, BASKET_ITEM_1.id)
        underTest.deleteBasketItem(command)

        verify { basketJpaFinderService.getNonDeletedById(BASKET_1.id) }
        verify { basketItemJpaFinderService.getNonDeletedById(BASKET_ITEM_1.id) }
        verify {
            basketDiscountCardService.deleteDiscountCardUsagesWhenDeletingBasketItem(
                DeleteDiscountCardUsagesWhenDeletingBasketItemCommand(
                    basketId = command.basketId,
                    basketItemId = command.basketItemId,
                    basketItemType = BASKET_ITEM_1.type
                )
            )
        }
        verify { ticketService.deleteTicket(DeleteTicketCommand(TICKET_1.id)) }
        verify { productFinderService wasNot Called }
        // can't use verifySequence, mocking the repo fails with class cast exception: https://github.com/mockk/mockk/issues/321
        // verify { basketItemRepository.save(any()) }
        verify { applicationEventPublisher.publishEvent(BasketItemModifiedEvent(BASKET_1.id, CardsPosType.VIP)) }
    }

    @Test
    fun `test deleteBasketItem - product item - should call everything accordingly`() {
        every { basketJpaFinderService.getNonDeletedById(any()) } returns BASKET_1
        every { basketItemJpaFinderService.getNonDeletedById(any()) } returns BASKET_ITEM_3
        every { basketItemJpaFinderService.findRelatedPackagingDepositBasketItemModel(any(), any()) } returns null
        every { basketDiscountCardService.deleteDiscountCardUsagesWhenDeletingBasketItem(any()) } just Runs
        every { applicationEventPublisher.publishEvent(any<ProductBasketItemDeletedFromBasketEvent>()) } just Runs
        every { basketItemRepository.save(any()) } returns BASKET_ITEM_3
        every { applicationEventPublisher.publishEvent(any<BasketItemModifiedEvent>()) } just Runs

        val command = mapToDeleteBasketItemCommand(BASKET_1.id, BASKET_ITEM_3.id)
        underTest.deleteBasketItem(command)

        verify { basketJpaFinderService.getNonDeletedById(BASKET_1.id) }
        verify { basketItemJpaFinderService.getNonDeletedById(BASKET_ITEM_3.id) }
        verify { basketItemJpaFinderService.findRelatedPackagingDepositBasketItemModel(BASKET_ITEM_3.productId!!, BASKET_1.id) }
        verify {
            basketDiscountCardService.deleteDiscountCardUsagesWhenDeletingBasketItem(
                DeleteDiscountCardUsagesWhenDeletingBasketItemCommand(
                    basketId = command.basketId,
                    basketItemId = command.basketItemId,
                    basketItemType = BASKET_ITEM_3.type
                )
            )
        }
        verify {
            applicationEventPublisher.publishEvent(
                ProductBasketItemDeletedFromBasketEvent(
                    productId = PRODUCT_1_ID,
                    quantity = -BASKET_ITEM_3.quantity
                )
            )
        }
        verify { ticketService wasNot Called }
        // can't use verifySequence, mocking the repo fails with class cast exception: https://github.com/mockk/mockk/issues/321
        // verify { basketItemRepository.save(any()) }
        verify { applicationEventPublisher.publishEvent(BasketItemModifiedEvent(BASKET_1.id, CardsPosType.VIP)) }
    }

    @Test
    fun `test deleteBasketItem - command with not existing basket - should throw exception`() {
        every { basketJpaFinderService.getNonDeletedById(any()) } throws BasketNotFoundException()

        val command = mapToDeleteBasketItemCommand(BASKET_1.id, BASKET_ITEM_1.id)
        assertThrows<BasketNotFoundException> {
            underTest.deleteBasketItem(command)
        }

        verifySequence {
            ticketService wasNot Called
            productFinderService wasNot Called
            applicationEventPublisher wasNot Called
            basketItemRepository wasNot Called
        }
    }

    @Test
    fun `test deleteBasketItem - command with not existing reservation - should throw exception`() {
        every { basketJpaFinderService.getNonDeletedById(any()) } returns BASKET_1
        every { basketItemJpaFinderService.getNonDeletedById(any()) } returns BASKET_ITEM_1
        every { basketDiscountCardService.deleteDiscountCardUsagesWhenDeletingBasketItem(any()) } just Runs
        every { ticketService.deleteTicket(any()) } throws TicketNotFoundException()

        val command = mapToDeleteBasketItemCommand(BASKET_1.id, BASKET_ITEM_1.id)
        assertThrows<TicketNotFoundException> {
            underTest.deleteBasketItem(command)
        }

        verifySequence {
            basketJpaFinderService.getNonDeletedById(BASKET_1.id)
            basketItemJpaFinderService.getNonDeletedById(BASKET_ITEM_1.id)
            basketDiscountCardService.deleteDiscountCardUsagesWhenDeletingBasketItem(
                DeleteDiscountCardUsagesWhenDeletingBasketItemCommand(
                    basketId = command.basketId,
                    basketItemId = command.basketItemId,
                    basketItemType = BASKET_ITEM_1.type
                )
            )
            ticketService.deleteTicket(DeleteTicketCommand(BASKET_ITEM_1.ticketId!!))
            applicationEventPublisher wasNot Called
            basketItemRepository wasNot Called
        }
    }

    @Test
    fun `test deleteBasketItem - basket item without reservationId and productId - should throw exception`() {
        every { basketJpaFinderService.getNonDeletedById(any()) } returns BASKET_1
        every { basketItemJpaFinderService.getNonDeletedById(any()) } returns INVALID_BASKET_ITEM
        every { basketDiscountCardService.deleteDiscountCardUsagesWhenDeletingBasketItem(any()) } just Runs

        val command = mapToDeleteBasketItemCommand(BASKET_1.id, INVALID_BASKET_ITEM.id)
        assertThrows<InvalidBasketItemStateException> {
            underTest.deleteBasketItem(command)
        }

        verifySequence {
            basketJpaFinderService.getNonDeletedById(BASKET_1.id)
            basketItemJpaFinderService.getNonDeletedById(INVALID_BASKET_ITEM.id)
            basketDiscountCardService.deleteDiscountCardUsagesWhenDeletingBasketItem(
                DeleteDiscountCardUsagesWhenDeletingBasketItemCommand(
                    basketId = command.basketId,
                    basketItemId = command.basketItemId,
                    basketItemType = INVALID_BASKET_ITEM.type
                )
            )
            ticketService wasNot Called
            applicationEventPublisher wasNot Called
            basketItemRepository wasNot Called
        }
    }

    @Test
    fun `test patchBasketItem - ticket item - should call everything accordingly`() {
        every { basketJpaFinderService.getNonDeletedById(any()) } returns BASKET_1
        every { basketItemJpaFinderService.getNonDeletedById(any()) } returns BASKET_ITEM_2
        every { basketDiscountCardService.deleteDiscountCardUsagesWhenPatchingBasketItem(any()) } just Runs
        every { ticketJooqFinderService.getNonDeletedById(any()) } returns TICKET_1
        every { discountCardJooqFinderService.findAppliedOnBasketItem(any(), any()) } returns null
        every { ticketJooqFinderService.getById(any()) } returns TICKET_1
        every { ticketService.updateTicket(any()) } returns TICKET_MODEL_1
        every { ticketDiscountJooqFinderService.validateAndGetDiscounts(any(), any()) } returns Pair(null, null)
        every { ticketJooqFinderService.findAllNonDeletedByBasketId(any()) } returns listOf(TICKET_1)
        every { basketItemVatAmountService.determineVatAmount(any(), any(), any(), any()) } returns 2.toBigDecimal()
        every { basketItemRepository.save(any()) } returns BASKET_ITEM_2
        every { applicationEventPublisher.publishEvent(any<BasketItemModifiedEvent>()) } just Runs

        val command = PATCH_BASKET_ITEM_COMMAND_2.copy(
            quantity = 1,
            priceCategoryItemNumber = PriceCategoryItemNumber.PRICE_2
        )
        underTest.patchBasketItem(command)

        verify { basketJpaFinderService.getNonDeletedById(BASKET_1.id) }
        verify { basketItemJpaFinderService.getNonDeletedById(BASKET_ITEM_2.id) }
        verify {
            basketDiscountCardService.deleteDiscountCardUsagesWhenPatchingBasketItem(
                DeleteDiscountCardUsagesWhenPatchingBasketItemCommand(
                    basketId = command.basketId,
                    basketItemId = command.basketItemId,
                    ticketId = TICKET_1.id,
                    primaryTicketDiscountId = null,
                    secondaryTicketDiscountId = null
                )
            )
        }
        verify { ticketJooqFinderService.getNonDeletedById(TICKET_1.id) }
        verify { discountCardJooqFinderService.findAppliedOnBasketItem(BASKET_ITEM_2.id, BasketItemType.TICKET) }
        verify { ticketJooqFinderService.getById(TICKET_1.id) }
        verify {
            ticketService.updateTicket(
                UpdateTicketCommand(
                    ticketId = BASKET_ITEM_2.ticketId!!,
                    priceCategoryItemNumber = PriceCategoryItemNumber.PRICE_2,
                    freeTicket = false
                )
            )
        }
        verify { ticketDiscountJooqFinderService.validateAndGetDiscounts(null, null) }
        verify { ticketJooqFinderService.findAllNonDeletedByBasketId(BASKET_1.id) }
        verify { productFinderService wasNot Called }
        // can't use verifySequence, mocking the repo fails with class cast exception: https://github.com/mockk/mockk/issues/321
        // verify { basketItemRepository.save(any()) }
        verify { basketItemVatAmountService.determineVatAmount(BasketItemType.TICKET, BASKET_ITEM_2.price, 1, null) }
        verify { applicationEventPublisher.publishEvent(BasketItemModifiedEvent(BASKET_1.id, CardsPosType.VIP)) }
    }

    @Test
    fun `test patchBasketItem - product item with stock quantity - should call everything accordingly`() {
        every { basketJpaFinderService.getNonDeletedById(any()) } returns BASKET_1
        every { basketItemJpaFinderService.getNonDeletedById(any()) } returns BASKET_ITEM_3
        every { productFinderService.existsNonDeletedByIdAndStockQuantityGreaterThanOrEqualOne(any()) } returns true
        every { productFinderService.getNonDeletedById(any()) } returns PRODUCT_1
        every { basketItemJpaFinderService.findRelatedPackagingDepositBasketItemModel(any(), any()) } returns null
        every { applicationEventPublisher.publishEvent(any<ProductBasketItemQuantityModifiedEvent>()) } just Runs
        every { basketItemVatAmountService.determineVatAmount(any(), any(), any(), any()) } returns 2.toBigDecimal()
        every { basketItemRepository.save(any()) } returns BASKET_ITEM_3
        every { applicationEventPublisher.publishEvent(any<BasketItemModifiedEvent>()) } just Runs

        underTest.patchBasketItem(PATCH_BASKET_ITEM_COMMAND_3)

        verify { basketJpaFinderService.getNonDeletedById(BASKET_1.id) }
        verify { basketItemJpaFinderService.getNonDeletedById(BASKET_ITEM_3.id) }
        verify { productFinderService.existsNonDeletedByIdAndStockQuantityGreaterThanOrEqualOne(BASKET_ITEM_3.productId!!) }
        verify { productFinderService.getNonDeletedById(BASKET_ITEM_3.productId!!) }
        verify { basketItemJpaFinderService.findRelatedPackagingDepositBasketItemModel(PRODUCT_1.id, BASKET_1.id) }
        verify {
            applicationEventPublisher.publishEvent(
                ProductBasketItemQuantityModifiedEvent(BASKET_ITEM_3.productId!!, -2)
            )
        }
        // can't use verifySequence, mocking the repo fails with class cast exception: https://github.com/mockk/mockk/issues/321
        // verify { basketItemRepository.save(any()) }
        verify {
            basketItemVatAmountService.determineVatAmount(
                BasketItemType.PRODUCT,
                BASKET_ITEM_3.price,
                BASKET_ITEM_3.quantity,
                BASKET_ITEM_3.productId
            )
        }
        verify { applicationEventPublisher.publishEvent(BasketItemModifiedEvent(BASKET_1.id, CardsPosType.VIP)) }
    }

    @Test
    fun `test patchBasketItem - product item without stock quantity - should call everything accordingly`() {
        every { basketJpaFinderService.getNonDeletedById(any()) } returns BASKET_1
        every { basketItemJpaFinderService.getNonDeletedById(any()) } returns BASKET_ITEM_3
        every { productFinderService.getNonDeletedById(any()) } returns PRODUCT_1
        every { productFinderService.existsNonDeletedByIdAndStockQuantityGreaterThanOrEqualOne(any()) } returns false
        every { productFinderService.findAllNonDeletedActiveProductStockQuantitiesByIdIn(any()) } returns mapOf(PRODUCT_1_ID to 0L.toBigDecimal())

        assertThrows<ProductBasketItemWithInsufficientStockQuantityException> {
            underTest.patchBasketItem(PATCH_BASKET_ITEM_COMMAND_3)
        }

        verify { basketJpaFinderService.getNonDeletedById(BASKET_1.id) }
        verify { basketItemJpaFinderService.getNonDeletedById(BASKET_ITEM_3.id) }
        verify { productFinderService.getNonDeletedById(PRODUCT_1_ID) }
        verify { productFinderService.existsNonDeletedByIdAndStockQuantityGreaterThanOrEqualOne(BASKET_ITEM_3.productId!!) }
        productFinderService.findAllNonDeletedActiveProductStockQuantitiesByIdIn(setOf(PRODUCT_1_ID))
        verify { applicationEventPublisher wasNot Called }
        verify { basketItemRepository wasNot Called }
    }

    @Test
    fun `test patchBasketItem - command with not existing basket - should throw exception`() {
        every { basketJpaFinderService.getNonDeletedById(any()) } throws BasketNotFoundException()

        assertThrows<BasketNotFoundException> {
            underTest.patchBasketItem(PATCH_BASKET_ITEM_COMMAND_1)
        }

        verifySequence {
            basketItemJpaFinderService wasNot Called
            ticketService wasNot Called
            applicationEventPublisher wasNot Called
            basketItemRepository wasNot Called
        }
    }

    @Test
    fun `test patchBasketItem - basket is in invalid state - should throw exception`() {
        every { basketJpaFinderService.getNonDeletedById(any()) } returns DELETED_BASKET

        assertThrows<InvalidBasketStateException> {
            underTest.patchBasketItem(PATCH_BASKET_ITEM_COMMAND_1)
        }

        verifySequence {
            basketItemJpaFinderService wasNot Called
            ticketService wasNot Called
            applicationEventPublisher wasNot Called
            basketItemRepository wasNot Called
        }
    }

    @Test
    fun `test patchBasketItem - command with not existing basket item - should throw exception`() {
        every { basketJpaFinderService.getNonDeletedById(any()) } returns BASKET_1
        every { basketItemJpaFinderService.getNonDeletedById(any()) } throws BasketItemNotFoundException()

        assertThrows<BasketItemNotFoundException> {
            underTest.patchBasketItem(PATCH_BASKET_ITEM_COMMAND_1)
        }

        verifySequence {
            ticketService wasNot Called
            applicationEventPublisher wasNot Called
            basketItemRepository wasNot Called
        }
    }

    @Test
    fun `test patchBasketItem - command with quantity more than 1 - should throw exception`() {
        every { basketJpaFinderService.getNonDeletedById(any()) } returns BASKET_1
        every { basketItemJpaFinderService.getNonDeletedById(any()) } returns BASKET_ITEM_1
        every { basketDiscountCardService.deleteDiscountCardUsagesWhenPatchingBasketItem(any()) } just Runs

        val command = PATCH_BASKET_ITEM_COMMAND_1.copy(quantity = 2)
        assertThrows<MultipleTicketsForOneSeatException> {
            underTest.patchBasketItem(command)
        }

        verifySequence {
            basketJpaFinderService.getNonDeletedById(BASKET_1.id)
            basketItemJpaFinderService.getNonDeletedById(BASKET_ITEM_1.id)
            basketDiscountCardService.deleteDiscountCardUsagesWhenPatchingBasketItem(
                DeleteDiscountCardUsagesWhenPatchingBasketItemCommand(
                    basketId = command.basketId,
                    basketItemId = command.basketItemId,
                    ticketId = TICKET_1.id,
                    primaryTicketDiscountId = null,
                    secondaryTicketDiscountId = null
                )
            )
            ticketService wasNot Called
            applicationEventPublisher wasNot Called
            basketItemRepository wasNot Called
        }
    }

    @Test
    fun `test patchBasketItem - command quantity equals current quantity - should skip execution`() {
        every { basketJpaFinderService.getNonDeletedById(any()) } returns BASKET_1
        every { basketItemJpaFinderService.getNonDeletedById(any()) } returns BASKET_ITEM_1

        underTest.patchBasketItem(PATCH_BASKET_ITEM_COMMAND_1.copy(quantity = BASKET_ITEM_1.quantity))

        verifySequence {
            applicationEventPublisher wasNot Called
            basketItemRepository wasNot Called
        }
    }

    @Test
    fun `test patchBasketItem - two discount card discounts applied at once - should throw exception`() {
        every { basketJpaFinderService.getNonDeletedById(any()) } returns BASKET_1
        every { basketItemJpaFinderService.getNonDeletedById(any()) } returns BASKET_ITEM_1
        every { basketDiscountCardService.deleteDiscountCardUsagesWhenPatchingBasketItem(any()) } just Runs

        assertThrows<TwoDiscountCardsAppliedOnTicketBasketItemException> {
            underTest.patchBasketItem(PATCH_BASKET_ITEM_COMMAND_1_TWO_DISCOUNT_CARDS)
        }

        verify { basketJpaFinderService.getNonDeletedById(BASKET_1.id) }
        verify { basketItemJpaFinderService.getNonDeletedById(BASKET_ITEM_1.id) }
        verify {
            basketDiscountCardService.deleteDiscountCardUsagesWhenPatchingBasketItem(
                DeleteDiscountCardUsagesWhenPatchingBasketItemCommand(
                    basketId = BASKET_1.id,
                    basketItemId = BASKET_ITEM_1.id,
                    ticketId = BASKET_ITEM_1.ticketId!!,
                    primaryTicketDiscountId = Optional.of(TICKET_DISCOUNT_1_ID),
                    secondaryTicketDiscountId = Optional.of(TICKET_DISCOUNT_2_ID)
                )
            )
        }
    }
}

private val SEAT_1_ID = UUID.randomUUID()
private val PRODUCT_1_ID = UUID.randomUUID()
private val TICKET_PRICE_1_ID = UUID.randomUUID()
private val TABLE_1_ID = UUID.randomUUID()
private val TICKET_DISCOUNT_1_ID = UUID.randomUUID()
private val TICKET_DISCOUNT_2_ID = UUID.randomUUID()
private val DISCOUNT_CARD_1_ID = UUID.randomUUID()
private val DISCOUNT_CARD_2_ID = UUID.randomUUID()
private val DISTRIBUTOR_ID = UUID.randomUUID()

private val BRANCH_1 = createBranch()
private val AUDITORIUM_1 = createAuditorium(
    originalId = 1,
    code = "A",
    title = "SÁLA A - CINEMAX BRATISLAVA"
)
private val MOVIE_1 = createMovie(
    originalId = 1,
    title = "Star Wars: Episode I – The Phantom Menace",
    releaseYear = 2001,
    distributorId = DISTRIBUTOR_ID
)
private val SCREENING_1 = createScreeningWithCustomDateTimeLimit(
    originalId = 3,
    auditoriumId = AUDITORIUM_1.id,
    date = LocalDate.now().minusDays(1),
    time = LocalTime.now(),
    movieId = MOVIE_1.id,
    saleTimeLimit = 30
)
private val RESERVATION_1 = createReservation(
    screeningId = SCREENING_1.id,
    seatId = SEAT_1_ID
)
private val TICKET_1 = createTicket(
    screeningId = SCREENING_1.id,
    reservationId = RESERVATION_1.id,
    ticketPriceId = TICKET_PRICE_1_ID
)
private val BASKET_1 = createBasket(
    tableId = TABLE_1_ID,
    totalPrice = BigDecimal(12.0),
    state = BasketState.OPEN
)
private val DELETED_BASKET = createBasket(
    tableId = TABLE_1_ID,
    totalPrice = BigDecimal(25.0),
    state = BasketState.DELETED
)
private val TICKET_1_REQUEST = CreateTicketRequest(
    ticketPrice = CreateTicketPriceRequest(
        priceCategoryItemNumber = PriceCategoryItemNumber.PRICE_1
    ),
    reservation = CreateReservationRequest(SEAT_1_ID),
    screeningId = SCREENING_1.id,
    isGroupTicket = true
)
private val BASKET_ITEM_REQUEST_1 = CreateBasketItemRequest(
    type = BasketItemType.TICKET,
    quantity = 1,
    ticket = TICKET_1_REQUEST
)
private val BASKET_ITEM_REQUEST_2 = CreateBasketItemRequest(
    type = BasketItemType.TICKET,
    quantity = 2,
    ticket = TICKET_1_REQUEST
)
private val BASKET_ITEM_REQUEST_3 = CreateBasketItemRequest(
    type = BasketItemType.PRODUCT,
    quantity = 3,
    product = CreateProductRequest(PRODUCT_1_ID)
)
private val CREATE_BASKET_ITEM_COMMAND = CreateBasketItemCommand(
    basketId = BASKET_1.id,
    type = BasketItemType.TICKET,
    quantity = 1,
    reservationSeatId = RESERVATION_1.seatId,
    priceCategoryItemNumber = PriceCategoryItemNumber.PRICE_1,
    primaryTicketDiscountId = TICKET_DISCOUNT_1_ID,
    secondaryTicketDiscountId = TICKET_DISCOUNT_2_ID,
    primaryTicketDiscountCardId = DISCOUNT_CARD_1_ID,
    secondaryTicketDiscountCardId = DISCOUNT_CARD_2_ID,
    isGroupTicket = false
)
private val BASKET_ITEM_1 = createBasketItem(
    basketId = BASKET_1.id,
    type = BasketItemType.TICKET,
    price = BigDecimal(8.4),
    quantity = 1,
    ticketId = TICKET_1.id
)
private val BASKET_ITEM_2 = createBasketItem(
    basketId = BASKET_1.id,
    type = BasketItemType.TICKET,
    price = BigDecimal(5.6),
    quantity = 2,
    ticketId = TICKET_1.id
)
private val BASKET_ITEM_3 = createBasketItem(
    basketId = BASKET_1.id,
    type = BasketItemType.PRODUCT,
    price = BigDecimal(7.0),
    quantity = 3,
    productId = PRODUCT_1_ID
)
private val INVALID_BASKET_ITEM = createBasketItem(
    basketId = BASKET_1.id,
    type = BasketItemType.TICKET,
    price = BigDecimal(8.4),
    quantity = 1
)
private val DISCOUNT_CARD_1 = createDiscountCard(
    originalId = 123435,
    ticketDiscountId = TICKET_DISCOUNT_1_ID,
    title = "VIP karta",
    code = "67900000"
)
private val DISCOUNT_CARD_2 = createDiscountCard(
    originalId = 456789,
    ticketDiscountId = TICKET_DISCOUNT_2_ID,
    title = "FILM karta",
    code = "67900089"
)
private val PATCH_BASKET_ITEM_COMMAND_1 = PatchBasketItemCommand(
    basketId = BASKET_1.id,
    basketItemId = BASKET_ITEM_1.id,
    quantity = 1
)
private val PATCH_BASKET_ITEM_COMMAND_1_TWO_DISCOUNT_CARDS = PatchBasketItemCommand(
    basketId = BASKET_1.id,
    basketItemId = BASKET_ITEM_1.id,
    primaryTicketDiscountId = Optional.of(TICKET_DISCOUNT_1_ID),
    primaryTicketDiscountCardId = DISCOUNT_CARD_1.id,
    secondaryTicketDiscountId = Optional.of(TICKET_DISCOUNT_2_ID),
    secondaryTicketDiscountCardId = DISCOUNT_CARD_2.id
)
private val PATCH_BASKET_ITEM_COMMAND_2 = PatchBasketItemCommand(
    basketId = BASKET_1.id,
    basketItemId = BASKET_ITEM_2.id,
    quantity = 2
)
private val PATCH_BASKET_ITEM_COMMAND_3 = PatchBasketItemCommand(
    basketId = BASKET_1.id,
    basketItemId = BASKET_ITEM_3.id,
    quantity = 1
)
private val TICKET_PRICE_1 = createTicketPrice(
    id = TICKET_PRICE_1_ID,
    screeningId = SCREENING_1.id,
    seatId = SEAT_1_ID,
    basePrice = BigDecimal.TEN,
    basePriceItemNumber = PriceCategoryItemNumber.PRICE_1,
    totalPrice = BigDecimal.TEN
)
private val TICKET_MODEL_1 = TicketModel(ticket = TICKET_1, ticketPrice = TICKET_PRICE_1, isGroupTicket = true)
private val PRODUCT_1 = createProduct(
    productCategoryId = UUID.randomUUID(),
    title = "dummyProduct",
    price = BigDecimal.TEN,
    id = PRODUCT_1_ID
)
