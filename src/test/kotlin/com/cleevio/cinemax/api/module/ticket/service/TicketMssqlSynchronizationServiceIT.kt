package com.cleevio.cinemax.api.module.ticket.service

import com.cleevio.cinemax.api.MssqlIntegrationTest
import com.cleevio.cinemax.api.common.constant.PaymentType
import com.cleevio.cinemax.api.module.basket.constant.BasketState
import com.cleevio.cinemax.api.module.basket.service.command.InitOrUpdateMssqlBasketCommand
import com.cleevio.cinemax.api.module.pricecategoryitem.constant.PriceCategoryItemNumber
import com.cleevio.cinemax.api.module.reservation.constant.ReservationState
import com.cleevio.cinemax.api.module.synchronizationfrommssql.constant.SynchronizationFromMssqlType
import com.cleevio.cinemax.api.module.synchronizationfrommssql.entity.SynchronizationFromMssql
import com.cleevio.cinemax.api.module.synchronizationfrommssql.service.command.CreateOrUpdateSynchronizationFromMssqlCommand
import com.cleevio.cinemax.api.module.ticketprice.constant.AuditoriumServiceFeeType
import com.cleevio.cinemax.api.module.ticketprice.constant.AuditoriumSurchargeType
import com.cleevio.cinemax.api.module.ticketprice.constant.SeatServiceFeeType
import com.cleevio.cinemax.api.module.ticketprice.constant.SeatSurchargeType
import com.cleevio.cinemax.api.util.createDiscountCard
import com.cleevio.cinemax.api.util.createPosConfiguration
import com.cleevio.cinemax.api.util.createReservation
import com.cleevio.cinemax.api.util.createScreening
import com.cleevio.cinemax.api.util.createSeat
import com.cleevio.cinemax.api.util.createTicketDiscount
import io.mockk.Runs
import io.mockk.every
import io.mockk.just
import io.mockk.verifyAll
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.test.context.jdbc.Sql
import org.springframework.test.context.jdbc.SqlConfig
import org.springframework.test.context.jdbc.SqlGroup
import java.time.LocalDateTime
import java.util.UUID
import kotlin.test.assertEquals
import kotlin.test.assertTrue

@SqlGroup(
    Sql(
        scripts = [
            "/db/mssql-cinemax/test/init_mssql_ticket.sql"
        ],
        config = SqlConfig(
            dataSource = "mssqlCinemaxDataSource",
            transactionManager = "mssqlCinemaxTransactionManager"
        )
    ),
    Sql(
        scripts = [
            "/db/mssql-cinemax/test/drop_mssql_ticket.sql"
        ],
        config = SqlConfig(
            dataSource = "mssqlCinemaxDataSource",
            transactionManager = "mssqlCinemaxTransactionManager"
        ),
        executionPhase = Sql.ExecutionPhase.AFTER_TEST_METHOD
    )
)
class TicketMssqlSynchronizationServiceIT @Autowired constructor(
    private val underTest: TicketMssqlSynchronizationService,
    private val ticketMssqlFinderRepository: TicketMssqlFinderRepository,
) : MssqlIntegrationTest() {

    @Test
    fun `test synchronizeAll - 10 MSSQL tickets - should create 3 offline tickets and 7 online tickets`() {
        every { synchronizationFromMssqlFinderServiceMock.findByType(any()) } returns null
        every { posConfigurationJpaFinderServiceMock.findByTitle(POS_CONFIGURATION_1.title) } returns POS_CONFIGURATION_1
        every { posConfigurationJpaFinderServiceMock.findByTitle(POS_CONFIGURATION_2.title) } returns POS_CONFIGURATION_2
        every { posConfigurationJpaFinderServiceMock.findOnlinePosConfiguration() } returns POS_CONFIGURATION_3
        every { screeningJpaFinderServiceMock.findByOriginalId(SCREENING_1.originalId!!) } returns SCREENING_1
        every { screeningJpaFinderServiceMock.findByOriginalId(SCREENING_2.originalId!!) } returns SCREENING_2
        every { screeningJpaFinderServiceMock.findByOriginalId(SCREENING_3.originalId!!) } returns SCREENING_3
        every { screeningJpaFinderServiceMock.findByOriginalId(SCREENING_4.originalId!!) } returns SCREENING_4
        every { screeningJpaFinderServiceMock.findByOriginalId(SCREENING_5.originalId!!) } returns SCREENING_5
        every { screeningJpaFinderServiceMock.findByOriginalId(SCREENING_6.originalId!!) } returns SCREENING_6
        every { screeningJpaFinderServiceMock.findByOriginalId(SCREENING_7.originalId!!) } returns SCREENING_7
        every { reservationJpaFinderServiceMock.findLatestByOriginalId(RESERVATION_1.originalId!!) } returns RESERVATION_1
        every { reservationJpaFinderServiceMock.findLatestByOriginalId(RESERVATION_2.originalId!!) } returns RESERVATION_2
        every { reservationJpaFinderServiceMock.findLatestByOriginalId(RESERVATION_3.originalId!!) } returns RESERVATION_3
        every { reservationJpaFinderServiceMock.findLatestByOriginalId(RESERVATION_4.originalId!!) } returns RESERVATION_4
        every { reservationJpaFinderServiceMock.findLatestByOriginalId(RESERVATION_5.originalId!!) } returns RESERVATION_5
        every { reservationJpaFinderServiceMock.findLatestByOriginalId(RESERVATION_6.originalId!!) } returns RESERVATION_6
        every { reservationJpaFinderServiceMock.findLatestByOriginalId(RESERVATION_7.originalId!!) } returns RESERVATION_7
        every { reservationJpaFinderServiceMock.findLatestByOriginalId(RESERVATION_8.originalId!!) } returns RESERVATION_8
        every { reservationJpaFinderServiceMock.findLatestByOriginalId(RESERVATION_9.originalId!!) } returns RESERVATION_9
        every { reservationJpaFinderServiceMock.findLatestByOriginalId(RESERVATION_10.originalId!!) } returns RESERVATION_10
        every { seatJpaFinderServiceMock.findByOriginalId(SEAT_1.originalId!!) } returns SEAT_1
        every { seatJpaFinderServiceMock.findByOriginalId(SEAT_2.originalId!!) } returns SEAT_2
        every { seatJpaFinderServiceMock.findByOriginalId(SEAT_3.originalId!!) } returns SEAT_3
        every { seatJpaFinderServiceMock.findByOriginalId(SEAT_4.originalId!!) } returns SEAT_4
        every { seatJpaFinderServiceMock.findByOriginalId(SEAT_5.originalId!!) } returns SEAT_5
        every { seatJpaFinderServiceMock.findByOriginalId(SEAT_6.originalId!!) } returns SEAT_6
        every { seatJpaFinderServiceMock.findByOriginalId(SEAT_7.originalId!!) } returns SEAT_7
        every { seatJpaFinderServiceMock.findByOriginalId(SEAT_8.originalId!!) } returns SEAT_8
        every { seatJpaFinderServiceMock.findByOriginalId(SEAT_9.originalId!!) } returns SEAT_9
        every { seatJpaFinderServiceMock.findByOriginalId(SEAT_10.originalId!!) } returns SEAT_10
        every { ticketDiscountJpaFinderServiceMock.findAllNonDeletedByCodeIn(any()) } returns listOf()
        every { ticketDiscountJpaFinderServiceMock.findAllNonDeletedByCodeIn(setOf(TICKET_DISCOUNT_1.code)) } returns listOf(
            TICKET_DISCOUNT_1
        )
        every { discountCardJpaFinderServiceMock.findByCode(any()) } returns null
        every { discountCardJpaFinderServiceMock.findByCode(DISCOUNT_CARD_1.code) } returns DISCOUNT_CARD_1
        every { basketServiceMock.initOrUpdateMssqlBasket(any()) } just Runs
        every { synchronizationFromMssqlServiceMock.createOrUpdate(any()) } returns SYNCHRONIZATION_FROM_MSSQL

        underTest.synchronizeAll()

        val posConfigurationTitleCaptor = mutableListOf<String>()
        val screeningOriginalIdCaptor = mutableListOf<Int>()
        val reservationOriginalIdCaptor = mutableListOf<Int>()
        val seatOriginalIdCaptor = mutableListOf<Int>()
        val ticketDiscountCodeCaptor = mutableListOf<Set<String>>()
        val discountCardCodeCaptor = mutableListOf<String>()
        val initOrUpdateMssqlBasketCommandCaptor = mutableListOf<InitOrUpdateMssqlBasketCommand>()
        val ticketOriginalIdCaptor = mutableListOf<Int>()

        verifyAll {
            synchronizationFromMssqlFinderServiceMock.findByType(SynchronizationFromMssqlType.TICKET)
            posConfigurationJpaFinderServiceMock.findByTitle(capture(posConfigurationTitleCaptor))
            posConfigurationJpaFinderServiceMock.findOnlinePosConfiguration()
            screeningJpaFinderServiceMock.findByOriginalId(capture(screeningOriginalIdCaptor))
            reservationJpaFinderServiceMock.findLatestByOriginalId(capture(reservationOriginalIdCaptor))
            seatJpaFinderServiceMock.findByOriginalId(capture(seatOriginalIdCaptor))
            ticketDiscountJpaFinderServiceMock.findAllNonDeletedByCodeIn(capture(ticketDiscountCodeCaptor))
            discountCardJpaFinderServiceMock.findByCode(capture(discountCardCodeCaptor))
            basketServiceMock.initOrUpdateMssqlBasket(capture(initOrUpdateMssqlBasketCommandCaptor))
            synchronizationFromMssqlServiceMock.createOrUpdate(
                CreateOrUpdateSynchronizationFromMssqlCommand(
                    type = SynchronizationFromMssqlType.TICKET,
                    lastSynchronization = TICKET_10_UPDATED_AT
                )
            )
        }

        assertEquals(10, ticketMssqlFinderRepository.findAll().size)

        // online tickets
        assertEquals(10, initOrUpdateMssqlBasketCommandCaptor.size)
        assertCommandsEqual(EXPECTED_INIT_OR_UPDATE_MSSQL_BASKET_COMMAND_1, initOrUpdateMssqlBasketCommandCaptor[0])
        assertCommandsEqual(EXPECTED_INIT_OR_UPDATE_MSSQL_BASKET_COMMAND_2, initOrUpdateMssqlBasketCommandCaptor[1])
        assertCommandsEqual(EXPECTED_INIT_OR_UPDATE_MSSQL_BASKET_COMMAND_3, initOrUpdateMssqlBasketCommandCaptor[2])
        assertCommandsEqual(EXPECTED_INIT_OR_UPDATE_MSSQL_BASKET_COMMAND_4, initOrUpdateMssqlBasketCommandCaptor[3])
        assertCommandsEqual(EXPECTED_INIT_OR_UPDATE_MSSQL_BASKET_COMMAND_5, initOrUpdateMssqlBasketCommandCaptor[4])
        assertCommandsEqual(EXPECTED_INIT_OR_UPDATE_MSSQL_BASKET_COMMAND_6, initOrUpdateMssqlBasketCommandCaptor[5])
        assertCommandsEqual(EXPECTED_INIT_OR_UPDATE_MSSQL_BASKET_COMMAND_7, initOrUpdateMssqlBasketCommandCaptor[6])
        assertCommandsEqual(EXPECTED_INIT_OR_UPDATE_MSSQL_BASKET_COMMAND_8, initOrUpdateMssqlBasketCommandCaptor[7])
        assertCommandsEqual(EXPECTED_INIT_OR_UPDATE_MSSQL_BASKET_COMMAND_9, initOrUpdateMssqlBasketCommandCaptor[8])
        assertCommandsEqual(EXPECTED_INIT_OR_UPDATE_MSSQL_BASKET_COMMAND_10, initOrUpdateMssqlBasketCommandCaptor[9])

        assertEquals(3, posConfigurationTitleCaptor.size)
        assertTrue(
            posConfigurationTitleCaptor.containsAll(
                listOf(
                    POS_CONFIGURATION_1.title,
                    POS_CONFIGURATION_2.title,
                    POS_CONFIGURATION_2.title
                )
            )
        )

        assertEquals(10, screeningOriginalIdCaptor.size)
        assertTrue(
            screeningOriginalIdCaptor.containsAll(
                listOf(
                    SCREENING_1.originalId,
                    SCREENING_2.originalId,
                    SCREENING_2.originalId,
                    SCREENING_3.originalId,
                    SCREENING_4.originalId,
                    SCREENING_4.originalId,
                    SCREENING_5.originalId,
                    SCREENING_6.originalId,
                    SCREENING_7.originalId,
                    SCREENING_7.originalId
                )
            )
        )

        assertEquals(10, reservationOriginalIdCaptor.size)
        assertTrue(
            reservationOriginalIdCaptor.containsAll(
                listOf(
                    RESERVATION_1.originalId,
                    RESERVATION_2.originalId,
                    RESERVATION_3.originalId,
                    RESERVATION_4.originalId,
                    RESERVATION_5.originalId,
                    RESERVATION_6.originalId,
                    RESERVATION_7.originalId,
                    RESERVATION_8.originalId,
                    RESERVATION_9.originalId,
                    RESERVATION_10.originalId
                )
            )
        )

        assertEquals(10, seatOriginalIdCaptor.size)
        assertTrue(
            seatOriginalIdCaptor.containsAll(
                listOf(
                    SEAT_1.originalId,
                    SEAT_2.originalId,
                    SEAT_3.originalId,
                    SEAT_4.originalId,
                    SEAT_5.originalId,
                    SEAT_6.originalId,
                    SEAT_7.originalId,
                    SEAT_8.originalId,
                    SEAT_9.originalId,
                    SEAT_10.originalId
                )
            )
        )

        assertEquals(1, ticketDiscountCodeCaptor.size)
        assertTrue(ticketDiscountCodeCaptor.containsAll(listOf(setOf(TICKET_DISCOUNT_1.code))))
        assertEquals(1, discountCardCodeCaptor.size)
        assertTrue(discountCardCodeCaptor.containsAll(listOf(DISCOUNT_CARD_1.code)))
    }

    fun assertCommandsEqual(expected: InitOrUpdateMssqlBasketCommand, actual: InitOrUpdateMssqlBasketCommand) {
        assertEquals(expected.variableSymbol, actual.variableSymbol)
        assertEquals(expected.paidAt, actual.paidAt)
        assertEquals(expected.originalId, actual.originalId)
        assertEquals(expected.screeningId, actual.screeningId)
        assertEquals(expected.reservationId, actual.reservationId)
        assertEquals(expected.priceCategoryItemNumber, actual.priceCategoryItemNumber)
        assertEquals(expected.receiptNumber, actual.receiptNumber)
        assertEquals(expected.ticketDiscountPrimaryId, actual.ticketDiscountPrimaryId)
        assertEquals(expected.ticketDiscountSecondaryId, actual.ticketDiscountSecondaryId)
        assertEquals(expected.discountCardId, actual.discountCardId)
        assertEquals(expected.isUsed, actual.isUsed)
        assertEquals(expected.includes3dGlasses, actual.includes3dGlasses)
    }
}

private val TICKET_10_UPDATED_AT = LocalDateTime.of(2023, 11, 1, 9, 8, 0)

private val POS_CONFIGURATION_1 = createPosConfiguration(macAddress = "AA:BB:CC:DD:EE", title = "POKL1")
private val POS_CONFIGURATION_2 = createPosConfiguration(macAddress = "AA:BB:CC:DD:FF", title = "POKL3")
private val POS_CONFIGURATION_3 = createPosConfiguration(macAddress = "XX:XX:XX:XX:XX", title = "Internet")

private val SCREENING_1 = createScreening(originalId = 111860, auditoriumId = UUID.randomUUID(), movieId = UUID.randomUUID())
private val SCREENING_2 = createScreening(originalId = 111880, auditoriumId = UUID.randomUUID(), movieId = UUID.randomUUID())
private val SCREENING_3 = createScreening(originalId = 112948, auditoriumId = UUID.randomUUID(), movieId = UUID.randomUUID())
private val SCREENING_4 = createScreening(originalId = 111837, auditoriumId = UUID.randomUUID(), movieId = UUID.randomUUID())
private val SCREENING_5 = createScreening(originalId = 105100, auditoriumId = UUID.randomUUID(), movieId = UUID.randomUUID())
private val SCREENING_6 = createScreening(originalId = 102900, auditoriumId = UUID.randomUUID(), movieId = UUID.randomUUID())
private val SCREENING_7 = createScreening(originalId = 103054, auditoriumId = UUID.randomUUID(), movieId = UUID.randomUUID())
private val SEAT_1 = createSeat(originalId = 967, auditoriumId = UUID.randomUUID(), auditoriumLayoutId = UUID.randomUUID())
private val SEAT_2 = createSeat(originalId = 2533, auditoriumId = UUID.randomUUID(), auditoriumLayoutId = UUID.randomUUID())
private val SEAT_3 = createSeat(originalId = 2534, auditoriumId = UUID.randomUUID(), auditoriumLayoutId = UUID.randomUUID())
private val SEAT_4 = createSeat(originalId = 1948, auditoriumId = UUID.randomUUID(), auditoriumLayoutId = UUID.randomUUID())
private val SEAT_5 = createSeat(originalId = 1386, auditoriumId = UUID.randomUUID(), auditoriumLayoutId = UUID.randomUUID())
private val SEAT_6 = createSeat(originalId = 1387, auditoriumId = UUID.randomUUID(), auditoriumLayoutId = UUID.randomUUID())
private val SEAT_7 = createSeat(originalId = 1556, auditoriumId = UUID.randomUUID(), auditoriumLayoutId = UUID.randomUUID())
private val SEAT_8 = createSeat(originalId = 448, auditoriumId = UUID.randomUUID(), auditoriumLayoutId = UUID.randomUUID())
private val SEAT_9 = createSeat(originalId = 1755, auditoriumId = UUID.randomUUID(), auditoriumLayoutId = UUID.randomUUID())
private val SEAT_10 = createSeat(originalId = 1756, auditoriumId = UUID.randomUUID(), auditoriumLayoutId = UUID.randomUUID())
private val RESERVATION_1 = createReservation(
    originalId = 18881594,
    screeningId = SCREENING_1.id,
    state = ReservationState.UNAVAILABLE,
    seatId = UUID.randomUUID()
)
private val RESERVATION_2 = createReservation(
    originalId = 18887245,
    screeningId = SCREENING_2.id,
    state = ReservationState.UNAVAILABLE,
    seatId = UUID.randomUUID()
)
private val RESERVATION_3 = createReservation(
    originalId = 18887246,
    screeningId = SCREENING_2.id,
    state = ReservationState.UNAVAILABLE,
    seatId = UUID.randomUUID()
)
private val RESERVATION_4 = createReservation(
    originalId = 19052397,
    screeningId = SCREENING_3.id,
    state = ReservationState.UNAVAILABLE,
    seatId = UUID.randomUUID()
)
private val RESERVATION_5 = createReservation(
    originalId = 18876432,
    screeningId = SCREENING_4.id,
    state = ReservationState.UNAVAILABLE,
    seatId = UUID.randomUUID()
)
private val RESERVATION_6 = createReservation(
    originalId = 18876433,
    screeningId = SCREENING_4.id,
    state = ReservationState.UNAVAILABLE,
    seatId = UUID.randomUUID()
)
private val RESERVATION_7 = createReservation(
    originalId = 17771717,
    screeningId = SCREENING_5.id,
    state = ReservationState.UNAVAILABLE,
    seatId = UUID.randomUUID()
)
private val RESERVATION_8 = createReservation(
    originalId = 17427776,
    screeningId = SCREENING_6.id,
    state = ReservationState.UNAVAILABLE,
    seatId = UUID.randomUUID()
)
private val RESERVATION_9 = createReservation(
    originalId = 17458498,
    screeningId = SCREENING_7.id,
    state = ReservationState.UNAVAILABLE,
    seatId = UUID.randomUUID()
)
private val RESERVATION_10 = createReservation(
    originalId = 17458499,
    screeningId = SCREENING_7.id,
    state = ReservationState.UNAVAILABLE,
    seatId = UUID.randomUUID()
)
private val TICKET_DISCOUNT_1 = createTicketDiscount(code = "FP")
private val DISCOUNT_CARD_1 = createDiscountCard(code = "67120097")

private val EXPECTED_INIT_OR_UPDATE_MSSQL_BASKET_COMMAND_1 = InitOrUpdateMssqlBasketCommand(
    variableSymbol = "4800427267",
    paidAt = LocalDateTime.of(2022, 7, 9, 8, 37, 0),
    paymentPosConfigurationId = POS_CONFIGURATION_3.id,
    paymentType = PaymentType.CASHLESS,
    state = BasketState.PAID_ONLINE,
    originalId = 1,
    screeningId = SCREENING_1.id,
    reservationId = RESERVATION_1.id,
    seatId = SEAT_1.id,
    priceCategoryItemNumber = PriceCategoryItemNumber.PRICE_19,
    receiptNumber = "0002104035",
    ticketDiscountPrimaryId = null,
    ticketDiscountSecondaryId = null,
    discountCardId = null,
    isUsed = true,
    isCancellationItem = false,
    includes3dGlasses = false,
    basePrice = 8.95.toBigDecimal(),
    basePriceBeforeDiscount = 8.95.toBigDecimal(),
    seatSurcharge = null,
    seatSurchargeType = null,
    auditoriumSurcharge = null,
    auditoriumSurchargeType = null,
    seatServiceFee = null,
    seatServiceFeeType = null,
    auditoriumServiceFee = null,
    auditoriumServiceFeeType = null,
    serviceFeeGeneral = null,
    freeTicket = false,
    totalPrice = 8.95.toBigDecimal()
)
private val EXPECTED_INIT_OR_UPDATE_MSSQL_BASKET_COMMAND_2 = InitOrUpdateMssqlBasketCommand(
    variableSymbol = "4800427249",
    paidAt = LocalDateTime.of(2022, 7, 9, 14, 41, 0),
    paymentPosConfigurationId = POS_CONFIGURATION_3.id,
    paymentType = PaymentType.CASHLESS,
    state = BasketState.PAID_ONLINE,
    originalId = 2,
    screeningId = SCREENING_2.id,
    reservationId = RESERVATION_2.id,
    seatId = SEAT_2.id,
    priceCategoryItemNumber = PriceCategoryItemNumber.PRICE_20,
    receiptNumber = "0002103985",
    ticketDiscountPrimaryId = null,
    ticketDiscountSecondaryId = null,
    discountCardId = null,
    isUsed = false,
    isCancellationItem = false,
    includes3dGlasses = true,
    basePrice = 8.95.toBigDecimal(),
    basePriceBeforeDiscount = 8.95.toBigDecimal(),
    seatSurcharge = null,
    seatSurchargeType = null,
    auditoriumSurcharge = 4.00.toBigDecimal(),
    auditoriumSurchargeType = AuditoriumSurchargeType.IMAX,
    seatServiceFee = null,
    seatServiceFeeType = null,
    auditoriumServiceFee = null,
    auditoriumServiceFeeType = null,
    serviceFeeGeneral = null,
    freeTicket = false,
    totalPrice = 12.95.toBigDecimal()
)
private val EXPECTED_INIT_OR_UPDATE_MSSQL_BASKET_COMMAND_3 = InitOrUpdateMssqlBasketCommand(
    variableSymbol = "4800427249",
    paymentPosConfigurationId = POS_CONFIGURATION_3.id,
    paymentType = PaymentType.CASHLESS,
    paidAt = LocalDateTime.of(2022, 7, 9, 14, 41, 0),
    state = BasketState.PAID_ONLINE,
    originalId = 3,
    screeningId = SCREENING_2.id,
    reservationId = RESERVATION_3.id,
    seatId = SEAT_3.id,
    priceCategoryItemNumber = PriceCategoryItemNumber.PRICE_20,
    receiptNumber = "0002103986",
    ticketDiscountPrimaryId = null,
    ticketDiscountSecondaryId = null,
    discountCardId = null,
    isUsed = false,
    isCancellationItem = false,
    includes3dGlasses = true,
    basePrice = 8.95.toBigDecimal(),
    basePriceBeforeDiscount = 8.95.toBigDecimal(),
    seatSurcharge = null,
    seatSurchargeType = null,
    auditoriumSurcharge = 4.00.toBigDecimal(),
    auditoriumSurchargeType = AuditoriumSurchargeType.IMAX,
    seatServiceFee = null,
    seatServiceFeeType = null,
    auditoriumServiceFee = null,
    auditoriumServiceFeeType = null,
    serviceFeeGeneral = null,
    freeTicket = false,
    totalPrice = 12.95.toBigDecimal()
)
private val EXPECTED_INIT_OR_UPDATE_MSSQL_BASKET_COMMAND_4 = InitOrUpdateMssqlBasketCommand(
    variableSymbol = "4800427036",
    paymentPosConfigurationId = POS_CONFIGURATION_3.id,
    paymentType = PaymentType.CASHLESS,
    paidAt = LocalDateTime.of(2022, 7, 9, 21, 15, 0),
    state = BasketState.PAID_ONLINE,
    originalId = 4,
    screeningId = SCREENING_3.id,
    reservationId = RESERVATION_4.id,
    seatId = SEAT_4.id,
    priceCategoryItemNumber = PriceCategoryItemNumber.PRICE_19,
    receiptNumber = "0002103041",
    ticketDiscountPrimaryId = TICKET_DISCOUNT_1.id,
    ticketDiscountSecondaryId = null,
    discountCardId = DISCOUNT_CARD_1.id,
    isUsed = true,
    isCancellationItem = false,
    includes3dGlasses = false,
    basePrice = 6.0.toBigDecimal(),
    basePriceBeforeDiscount = 9.2.toBigDecimal(),
    seatSurcharge = null,
    seatSurchargeType = null,
    auditoriumSurcharge = null,
    auditoriumSurchargeType = null,
    seatServiceFee = null,
    seatServiceFeeType = null,
    auditoriumServiceFee = null,
    auditoriumServiceFeeType = null,
    serviceFeeGeneral = null,
    freeTicket = false,
    totalPrice = 6.0.toBigDecimal()
)
private val EXPECTED_INIT_OR_UPDATE_MSSQL_BASKET_COMMAND_5 = InitOrUpdateMssqlBasketCommand(
    variableSymbol = "4800426868",
    paymentPosConfigurationId = POS_CONFIGURATION_3.id,
    paymentType = PaymentType.CASHLESS,
    paidAt = LocalDateTime.of(2022, 7, 9, 23, 48, 0),
    state = BasketState.PAID_ONLINE,
    originalId = 5,
    screeningId = SCREENING_4.id,
    reservationId = RESERVATION_5.id,
    seatId = SEAT_5.id,
    priceCategoryItemNumber = PriceCategoryItemNumber.PRICE_19,
    receiptNumber = "0002102493",
    ticketDiscountPrimaryId = null,
    ticketDiscountSecondaryId = null,
    discountCardId = null,
    isUsed = true,
    isCancellationItem = false,
    includes3dGlasses = false,
    basePrice = 7.20.toBigDecimal(),
    basePriceBeforeDiscount = 7.20.toBigDecimal(),
    seatSurcharge = 1.toBigDecimal(),
    seatSurchargeType = SeatSurchargeType.VIP,
    auditoriumSurcharge = null,
    auditoriumSurchargeType = null,
    seatServiceFee = 5.toBigDecimal(),
    seatServiceFeeType = SeatServiceFeeType.VIP,
    auditoriumServiceFee = 1.toBigDecimal(),
    auditoriumServiceFeeType = AuditoriumServiceFeeType.ULTRA_X,
    serviceFeeGeneral = null,
    freeTicket = false,
    totalPrice = 14.20.toBigDecimal()
)
private val EXPECTED_INIT_OR_UPDATE_MSSQL_BASKET_COMMAND_6 = InitOrUpdateMssqlBasketCommand(
    variableSymbol = "4800426868",
    paymentPosConfigurationId = POS_CONFIGURATION_3.id,
    paymentType = PaymentType.CASHLESS,
    paidAt = LocalDateTime.of(2022, 7, 9, 23, 48, 0),
    state = BasketState.PAID_ONLINE,
    originalId = 6,
    screeningId = SCREENING_4.id,
    reservationId = RESERVATION_6.id,
    seatId = SEAT_6.id,
    priceCategoryItemNumber = PriceCategoryItemNumber.PRICE_20,
    receiptNumber = "0002102494",
    ticketDiscountPrimaryId = null,
    ticketDiscountSecondaryId = null,
    discountCardId = null,
    isUsed = true,
    isCancellationItem = false,
    includes3dGlasses = false,
    basePrice = 8.95.toBigDecimal(),
    basePriceBeforeDiscount = 8.95.toBigDecimal(),
    seatSurcharge = 1.toBigDecimal(),
    seatSurchargeType = SeatSurchargeType.VIP,
    auditoriumSurcharge = null,
    auditoriumSurchargeType = null,
    seatServiceFee = 5.toBigDecimal(),
    seatServiceFeeType = SeatServiceFeeType.VIP,
    auditoriumServiceFee = 1.toBigDecimal(),
    auditoriumServiceFeeType = AuditoriumServiceFeeType.ULTRA_X,
    serviceFeeGeneral = null,
    freeTicket = false,
    totalPrice = 15.95.toBigDecimal()
)
private val EXPECTED_INIT_OR_UPDATE_MSSQL_BASKET_COMMAND_7 = InitOrUpdateMssqlBasketCommand(
    variableSymbol = "4800393744",
    paymentPosConfigurationId = POS_CONFIGURATION_3.id,
    paymentType = PaymentType.CASHLESS,
    paidAt = LocalDateTime.of(2023, 2, 1, 8, 37, 0),
    state = BasketState.PAID_ONLINE,
    originalId = 7,
    screeningId = SCREENING_5.id,
    reservationId = RESERVATION_7.id,
    seatId = SEAT_7.id,
    priceCategoryItemNumber = PriceCategoryItemNumber.PRICE_20,
    receiptNumber = "0001980804",
    ticketDiscountPrimaryId = null,
    ticketDiscountSecondaryId = null,
    discountCardId = null,
    isUsed = true,
    isCancellationItem = false,
    includes3dGlasses = false,
    basePrice = 6.2.toBigDecimal(),
    basePriceBeforeDiscount = 6.2.toBigDecimal(),
    seatSurcharge = null,
    seatSurchargeType = null,
    auditoriumSurcharge = null,
    auditoriumSurchargeType = null,
    seatServiceFee = 1.toBigDecimal(),
    seatServiceFeeType = SeatServiceFeeType.PREMIUM_PLUS,
    auditoriumServiceFee = null,
    auditoriumServiceFeeType = null,
    serviceFeeGeneral = 3.8.toBigDecimal(),
    freeTicket = false,
    totalPrice = 11.toBigDecimal()
)
private val EXPECTED_INIT_OR_UPDATE_MSSQL_BASKET_COMMAND_8 = InitOrUpdateMssqlBasketCommand(
    variableSymbol = null,
    paymentPosConfigurationId = POS_CONFIGURATION_2.id,
    paymentType = PaymentType.CASHLESS,
    paidAt = LocalDateTime.of(2023, 11, 1, 9, 1, 0),
    state = BasketState.PAID,
    originalId = 8,
    screeningId = SCREENING_6.id,
    reservationId = RESERVATION_8.id,
    seatId = SEAT_8.id,
    priceCategoryItemNumber = PriceCategoryItemNumber.PRICE_1,
    receiptNumber = "0001948986",
    ticketDiscountPrimaryId = null,
    ticketDiscountSecondaryId = null,
    discountCardId = null,
    isUsed = false,
    isCancellationItem = false,
    includes3dGlasses = false,
    basePrice = 8.45.toBigDecimal(),
    basePriceBeforeDiscount = 8.45.toBigDecimal(),
    seatSurcharge = null,
    seatSurchargeType = null,
    auditoriumSurcharge = 4.toBigDecimal(),
    auditoriumSurchargeType = AuditoriumSurchargeType.IMAX,
    seatServiceFee = null,
    seatServiceFeeType = null,
    auditoriumServiceFee = null,
    auditoriumServiceFeeType = null,
    serviceFeeGeneral = null,
    freeTicket = false,
    totalPrice = 12.45.toBigDecimal()
)
private val EXPECTED_INIT_OR_UPDATE_MSSQL_BASKET_COMMAND_9 = InitOrUpdateMssqlBasketCommand(
    variableSymbol = null,
    paymentPosConfigurationId = POS_CONFIGURATION_1.id,
    paymentType = PaymentType.CASH,
    paidAt = LocalDateTime.of(2023, 11, 1, 9, 8, 0),
    state = BasketState.PAID,
    originalId = 9,
    screeningId = SCREENING_7.id,
    reservationId = RESERVATION_9.id,
    seatId = SEAT_9.id,
    priceCategoryItemNumber = PriceCategoryItemNumber.PRICE_2,
    receiptNumber = "0001948987",
    ticketDiscountPrimaryId = null,
    ticketDiscountSecondaryId = null,
    discountCardId = null,
    isUsed = true,
    isCancellationItem = false,
    includes3dGlasses = false,
    basePrice = 8.45.toBigDecimal(),
    basePriceBeforeDiscount = 8.45.toBigDecimal(),
    seatSurcharge = null,
    seatSurchargeType = null,
    auditoriumSurcharge = null,
    auditoriumSurchargeType = null,
    seatServiceFee = null,
    seatServiceFeeType = null,
    auditoriumServiceFee = null,
    auditoriumServiceFeeType = null,
    serviceFeeGeneral = null,
    freeTicket = false,
    totalPrice = 8.45.toBigDecimal()
)
private val EXPECTED_INIT_OR_UPDATE_MSSQL_BASKET_COMMAND_10 = InitOrUpdateMssqlBasketCommand(
    variableSymbol = null,
    paymentPosConfigurationId = POS_CONFIGURATION_1.id,
    paidAt = LocalDateTime.of(2023, 11, 1, 9, 8, 0),
    paymentType = PaymentType.CASH,
    state = BasketState.PAID,
    originalId = 10,
    screeningId = SCREENING_7.id,
    reservationId = RESERVATION_10.id,
    seatId = SEAT_10.id,
    priceCategoryItemNumber = PriceCategoryItemNumber.PRICE_2,
    receiptNumber = "0001948988",
    ticketDiscountPrimaryId = null,
    ticketDiscountSecondaryId = null,
    discountCardId = null,
    isUsed = true,
    isCancellationItem = false,
    includes3dGlasses = false,
    basePrice = 8.45.toBigDecimal(),
    basePriceBeforeDiscount = 8.45.toBigDecimal(),
    seatSurcharge = null,
    seatSurchargeType = null,
    auditoriumSurcharge = null,
    auditoriumSurchargeType = null,
    seatServiceFee = null,
    seatServiceFeeType = null,
    auditoriumServiceFee = null,
    auditoriumServiceFeeType = null,
    serviceFeeGeneral = null,
    freeTicket = false,
    totalPrice = 8.45.toBigDecimal()
)

private val SYNCHRONIZATION_FROM_MSSQL = SynchronizationFromMssql(
    type = SynchronizationFromMssqlType.TICKET,
    lastSynchronization = LocalDateTime.MIN,
    lastHeartbeat = null
)
