package com.cleevio.cinemax.api.module.outboxevent.event.listener

import com.cleevio.cinemax.api.module.outboxevent.constant.OutboxEventType
import com.cleevio.cinemax.api.module.outboxevent.service.OutboxEventService
import com.cleevio.cinemax.api.module.outboxevent.service.command.CreateOutboxEventCommand
import com.cleevio.cinemax.api.module.outboxevent.service.command.CreateOutboxEventsCommand
import com.cleevio.cinemax.api.module.screening.event.PublishedScreeningCreatedOrUpdatedEvent
import com.cleevio.cinemax.api.module.screening.event.ScreeningsPublishedEvent
import com.cleevio.cinemax.api.module.screening.event.ScreeningsSwitchedToDraftOrDeletedEvent
import io.mockk.Called
import io.mockk.every
import io.mockk.just
import io.mockk.mockk
import io.mockk.runs
import io.mockk.verify
import org.junit.jupiter.api.Test
import java.util.UUID

class ScreeningOutboxEventEventListenerTest {

    private val outboxEventService = mockk<OutboxEventService>()
    private val underTest = ScreeningOutboxEventEventListener(outboxEventService)

    @Test
    fun `test listenToPublishedScreeningCreatedOrUpdatedEvent - should correctly handle event`() {
        every { outboxEventService.createOutboxEvent(any()) } just runs

        val screeningId = UUID.fromString("65aa8e3f-4b86-4eb3-b2e9-5b1482021eda")
        underTest.listenToPublishedScreeningCreatedOrUpdatedEvent(
            PublishedScreeningCreatedOrUpdatedEvent(screeningId)
        )

        verify {
            outboxEventService.createOutboxEvent(
                CreateOutboxEventCommand(
                    entityId = screeningId,
                    type = OutboxEventType.PUBLISHED_SCREENING_CREATED_OR_UPDATED
                )
            )
        }
    }

    @Test
    fun `test listenToScreeningsPublishedEvent - should correctly handle event`() {
        every { outboxEventService.createOutboxEvents(any()) } just runs

        val screeningIds = setOf(
            UUID.fromString("65aa8e3f-4b86-4eb3-b2e9-5b1482021eda"),
            UUID.fromString("f0737ce0-6648-4a59-a293-b51c09c331ad"),
            UUID.fromString("ce3053eb-1e07-4dbe-a013-248e503de834")
        )

        underTest.listenToScreeningsPublishedEvent(ScreeningsPublishedEvent(screeningIds))

        verify {
            outboxEventService.createOutboxEvents(
                CreateOutboxEventsCommand(
                    entityIds = screeningIds,
                    type = OutboxEventType.PUBLISHED_SCREENING_CREATED_OR_UPDATED
                )
            )
        }
    }

    @Test
    fun `test listenToScreeningSwitchedToDraftOrDeletedEvent - should correctly handle event`() {
        every { outboxEventService.createOutboxEvents(any()) } just runs

        val screeningId = UUID.fromString("65aa8e3f-4b86-4eb3-b2e9-5b1482021eda")
        underTest.listenToScreeningSwitchedToDraftOrDeletedEvent(
            ScreeningsSwitchedToDraftOrDeletedEvent(setOf(screeningId))
        )

        verify {
            outboxEventService.createOutboxEvents(
                CreateOutboxEventsCommand(
                    entityIds = setOf(screeningId),
                    type = OutboxEventType.SCREENING_SWITCHED_TO_DRAFT_OR_DELETED
                )
            )
        }
    }

    @Test
    fun `test listenToScreeningSwitchedToDraftOrDeletedEvent - empty set - should correctly handle event`() {
        every { outboxEventService.createOutboxEvents(any()) } just runs

        underTest.listenToScreeningSwitchedToDraftOrDeletedEvent(
            ScreeningsSwitchedToDraftOrDeletedEvent(setOf())
        )

        verify { outboxEventService wasNot Called }
    }
}
