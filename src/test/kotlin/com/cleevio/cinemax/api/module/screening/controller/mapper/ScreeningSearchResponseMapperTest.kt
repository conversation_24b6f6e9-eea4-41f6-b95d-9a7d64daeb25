package com.cleevio.cinemax.api.module.screening.controller.mapper

import com.cleevio.cinemax.api.module.auditorium.service.AuditoriumJooqFinderService
import com.cleevio.cinemax.api.module.movie.service.MovieJooqFinderService
import com.cleevio.cinemax.api.module.pricecategory.service.PriceCategoryJooqFinderService
import com.cleevio.cinemax.api.module.pricecategoryitem.constant.PriceCategoryItemNumber
import com.cleevio.cinemax.api.module.reservation.constant.ReservationState
import com.cleevio.cinemax.api.module.reservation.service.ReservationJooqFinderService
import com.cleevio.cinemax.api.module.screening.constant.MovieTechnology
import com.cleevio.cinemax.api.module.screeningfee.service.ScreeningFeeFinderService
import com.cleevio.cinemax.api.module.seat.constant.DoubleSeatType
import com.cleevio.cinemax.api.module.seat.constant.SeatType
import com.cleevio.cinemax.api.module.seat.service.SeatJooqFinderService
import com.cleevio.cinemax.api.util.assertAuditoriumEquals
import com.cleevio.cinemax.api.util.assertMovieSearchResponseEquals
import com.cleevio.cinemax.api.util.assertPriceCategoryEquals
import com.cleevio.cinemax.api.util.assertPriceCategoryItemEquals
import com.cleevio.cinemax.api.util.assertScreeningEquals
import com.cleevio.cinemax.api.util.assertSeatEquals
import com.cleevio.cinemax.api.util.createAuditorium
import com.cleevio.cinemax.api.util.createAuditoriumLayout
import com.cleevio.cinemax.api.util.createGroupReservation
import com.cleevio.cinemax.api.util.createMovie
import com.cleevio.cinemax.api.util.createPriceCategory
import com.cleevio.cinemax.api.util.createPriceCategoryItem
import com.cleevio.cinemax.api.util.createReservation
import com.cleevio.cinemax.api.util.createScreening
import com.cleevio.cinemax.api.util.createScreeningFee
import com.cleevio.cinemax.api.util.createSeat
import com.cleevio.cinemax.api.util.mapToPriceCategoryModel
import io.mockk.every
import io.mockk.mockk
import io.mockk.verifySequence
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test
import java.math.BigDecimal
import java.time.LocalDate
import java.time.LocalTime
import java.util.UUID
import kotlin.test.assertNull

class ScreeningSearchResponseMapperTest {

    private val movieJooqFinderService = mockk<MovieJooqFinderService>()
    private val auditoriumJooqFinderService = mockk<AuditoriumJooqFinderService>()
    private val seatJooqFinderService = mockk<SeatJooqFinderService>()
    private val reservationJooqFinderService = mockk<ReservationJooqFinderService>()
    private val priceCategoryJooqFinderService = mockk<PriceCategoryJooqFinderService>()
    private val screeningFeeFinderService = mockk<ScreeningFeeFinderService>()
    private val screeningSearchResponseMapper = ScreeningSearchResponseMapper(
        movieJooqFinderService,
        auditoriumJooqFinderService,
        seatJooqFinderService,
        reservationJooqFinderService,
        priceCategoryJooqFinderService,
        screeningFeeFinderService
    )

    @Test
    fun `test mapList - single original layout - should map correctly`() {
        every { movieJooqFinderService.findAllNonDeletedByIdIn(any()) } returns listOf(MOVIE_1, MOVIE_2)
        every { auditoriumJooqFinderService.findAllByIdIn(any()) } returns listOf(AUDITORIUM_1, AUDITORIUM_2)
        every { seatJooqFinderService.findAllSeatsByAuditoriumIdAndAuditoriumLayoutIdIn(any()) } returns listOf(
            SEAT_1,
            SEAT_2,
            SEAT_3,
            SEAT_4,
            SEAT_8
        )
        every { reservationJooqFinderService.findAllByScreeningIdIn(any()) } returns listOf(
            RESERVATION_1,
            RESERVATION_2,
            RESERVATION_3,
            RESERVATION_4,
            RESERVATION_5
        )
        every { priceCategoryJooqFinderService.getByIdsWithItems(any()) } returns listOf(
            PRICE_CATEGORY_MODEL_1,
            PRICE_CATEGORY_MODEL_2
        )
        every { screeningFeeFinderService.findAllByScreeningIdIn(any()) } returns listOf()

        val mappedScreenings = screeningSearchResponseMapper.mapList(listOf(SCREENING_1, SCREENING_2, SCREENING_3))

        assertEquals(3, mappedScreenings.size)

        assertScreeningEquals(SCREENING_1, mappedScreenings[0])
        assertMovieSearchResponseEquals(MOVIE_1, mappedScreenings[0].movie)
        assertPriceCategoryEquals(PRICE_CATEGORY_1, mappedScreenings[0].priceCategory)
        assertEquals(PRICE_CATEGORY_MODEL_1.items.size, mappedScreenings[0].priceCategory.items.size)
        assertPriceCategoryItemEquals(PRICE_CATEGORY_ITEM_1, mappedScreenings[0].priceCategory.items[0])
        assertAuditoriumEquals(AUDITORIUM_1, mappedScreenings[0].auditorium)
        assertEquals(2, mappedScreenings[0].auditorium.seats.size)
        assertSeatEquals(SEAT_1, mappedScreenings[0].auditorium.seats[0])
        assertEquals(ReservationState.RESERVED, mappedScreenings[0].auditorium.seats[0].reservation.state)
        assertNull(mappedScreenings[0].auditorium.seats[0].reservation.groupReservationId)
        assertSeatEquals(SEAT_2, mappedScreenings[0].auditorium.seats[1])
        assertEquals(ReservationState.FREE, mappedScreenings[0].auditorium.seats[1].reservation.state)
        assertNull(mappedScreenings[0].auditorium.seats[1].reservation.groupReservationId)

        assertScreeningEquals(SCREENING_2, mappedScreenings[1])
        assertMovieSearchResponseEquals(MOVIE_2, mappedScreenings[1].movie)
        assertPriceCategoryEquals(PRICE_CATEGORY_1, mappedScreenings[1].priceCategory)
        assertEquals(PRICE_CATEGORY_MODEL_1.items.size, mappedScreenings[1].priceCategory.items.size)
        assertPriceCategoryItemEquals(PRICE_CATEGORY_ITEM_1, mappedScreenings[1].priceCategory.items[0])
        assertAuditoriumEquals(AUDITORIUM_2, mappedScreenings[1].auditorium)
        assertEquals(3, mappedScreenings[1].auditorium.seats.size)
        assertSeatEquals(SEAT_3, mappedScreenings[1].auditorium.seats[0])
        assertEquals(ReservationState.UNAVAILABLE, mappedScreenings[1].auditorium.seats[0].reservation.state)
        assertNull(mappedScreenings[1].auditorium.seats[0].reservation.groupReservationId)
        assertSeatEquals(SEAT_4, mappedScreenings[1].auditorium.seats[1])
        assertEquals(ReservationState.GROUP_RESERVED, mappedScreenings[1].auditorium.seats[1].reservation.state)
        assertEquals(GROUP_RESERVATION_1.id, mappedScreenings[1].auditorium.seats[1].reservation.groupReservationId)
        assertSeatEquals(SEAT_8, mappedScreenings[1].auditorium.seats[2])
        assertNull(mappedScreenings[1].auditorium.seats[2].reservation.groupReservationId)

        assertScreeningEquals(SCREENING_3, mappedScreenings[2])
        assertMovieSearchResponseEquals(MOVIE_1, mappedScreenings[2].movie)
        assertPriceCategoryEquals(PRICE_CATEGORY_2, mappedScreenings[2].priceCategory)
        assertEquals(PRICE_CATEGORY_MODEL_2.items.size, mappedScreenings[2].priceCategory.items.size)
        assertPriceCategoryItemEquals(PRICE_CATEGORY_ITEM_2, mappedScreenings[2].priceCategory.items[0])
        assertAuditoriumEquals(AUDITORIUM_2, mappedScreenings[2].auditorium)
        assertEquals(3, mappedScreenings[2].auditorium.seats.size)
        assertSeatEquals(SEAT_3, mappedScreenings[2].auditorium.seats[0])
        assertEquals(ReservationState.FREE, mappedScreenings[2].auditorium.seats[0].reservation.state)
        assertNull(mappedScreenings[2].auditorium.seats[0].reservation.groupReservationId)
        assertSeatEquals(SEAT_4, mappedScreenings[2].auditorium.seats[1])
        assertEquals(ReservationState.UNAVAILABLE, mappedScreenings[2].auditorium.seats[1].reservation.state)
        assertNull(mappedScreenings[2].auditorium.seats[1].reservation.groupReservationId)
        assertSeatEquals(SEAT_8, mappedScreenings[2].auditorium.seats[2])
        assertEquals(ReservationState.FREE, mappedScreenings[2].auditorium.seats[2].reservation.state)

        verifySequence {
            movieJooqFinderService.findAllNonDeletedByIdIn(setOf(MOVIE_1.id, MOVIE_2.id))
            auditoriumJooqFinderService.findAllByIdIn(setOf(AUDITORIUM_1.id, AUDITORIUM_2.id))
            seatJooqFinderService.findAllSeatsByAuditoriumIdAndAuditoriumLayoutIdIn(
                setOf(
                    Pair(AUDITORIUM_1.id, AUDITORIUM_LAYOUT_1.id),
                    Pair(AUDITORIUM_2.id, AUDITORIUM_LAYOUT_3.id)
                )
            )
            reservationJooqFinderService.findAllByScreeningIdIn(
                setOf(
                    SCREENING_1.id,
                    SCREENING_2.id,
                    SCREENING_3.id
                )
            )
            priceCategoryJooqFinderService.getByIdsWithItems(setOf(PRICE_CATEGORY_1.id, PRICE_CATEGORY_2.id))
            screeningFeeFinderService.findAllByScreeningIdIn(setOf(SCREENING_1.id, SCREENING_2.id, SCREENING_3.id))
        }
    }

    @Test
    fun `test mapList - multiple original layouts - should map correctly`() {
        every { movieJooqFinderService.findAllNonDeletedByIdIn(any()) } returns listOf(MOVIE_1)
        every { auditoriumJooqFinderService.findAllByIdIn(any()) } returns listOf(AUDITORIUM_1)
        every { seatJooqFinderService.findAllSeatsByAuditoriumIdAndAuditoriumLayoutIdIn(any()) } returns listOf(
            SEAT_1,
            SEAT_2,
            SEAT_5,
            SEAT_6,
            SEAT_7
        )
        every { reservationJooqFinderService.findAllByScreeningIdIn(any()) } returns listOf()
        every { priceCategoryJooqFinderService.getByIdsWithItems(any()) } returns listOf(PRICE_CATEGORY_MODEL_1)
        every { screeningFeeFinderService.findAllByScreeningIdIn(any()) } returns listOf()

        val mappedScreenings = screeningSearchResponseMapper.mapList(listOf(SCREENING_1, SCREENING_4))

        assertEquals(2, mappedScreenings.size)

        assertScreeningEquals(SCREENING_1, mappedScreenings[0])
        assertMovieSearchResponseEquals(MOVIE_1, mappedScreenings[0].movie)
        assertAuditoriumEquals(AUDITORIUM_1, mappedScreenings[0].auditorium)
        assertEquals(2, mappedScreenings[0].auditorium.seats.size)
        assertSeatEquals(SEAT_1, mappedScreenings[0].auditorium.seats[0])
        assertSeatEquals(SEAT_2, mappedScreenings[0].auditorium.seats[1])

        assertScreeningEquals(SCREENING_4, mappedScreenings[1])
        assertMovieSearchResponseEquals(MOVIE_1, mappedScreenings[1].movie)
        assertAuditoriumEquals(AUDITORIUM_1, mappedScreenings[1].auditorium)
        assertEquals(2, mappedScreenings[1].auditorium.seats.size)
        assertSeatEquals(SEAT_5, mappedScreenings[1].auditorium.seats[0])
        assertSeatEquals(SEAT_6, mappedScreenings[1].auditorium.seats[1])

        verifySequence {
            movieJooqFinderService.findAllNonDeletedByIdIn(setOf(MOVIE_1.id))
            auditoriumJooqFinderService.findAllByIdIn(setOf(AUDITORIUM_1.id))
            seatJooqFinderService.findAllSeatsByAuditoriumIdAndAuditoriumLayoutIdIn(
                setOf(
                    Pair(AUDITORIUM_1.id, AUDITORIUM_LAYOUT_1.id),
                    Pair(AUDITORIUM_1.id, AUDITORIUM_LAYOUT_2.id)
                )
            )
            reservationJooqFinderService.findAllByScreeningIdIn(setOf(SCREENING_1.id, SCREENING_4.id))
            priceCategoryJooqFinderService.getByIdsWithItems(setOf(PRICE_CATEGORY_1.id))
            screeningFeeFinderService.findAllByScreeningIdIn(setOf(SCREENING_1.id, SCREENING_4.id))
        }
    }

    @Test
    fun `test mapList - screening with saleTimeLimit spanning multiple days - should map correctly`() {
        every { movieJooqFinderService.findAllNonDeletedByIdIn(any()) } returns listOf(MOVIE_1)
        every { auditoriumJooqFinderService.findAllByIdIn(any()) } returns listOf(AUDITORIUM_1)
        every { seatJooqFinderService.findAllSeatsByAuditoriumIdAndAuditoriumLayoutIdIn(any()) } returns listOf(SEAT_5, SEAT_6)
        every { reservationJooqFinderService.findAllByScreeningIdIn(any()) } returns listOf()
        every { priceCategoryJooqFinderService.getByIdsWithItems(any()) } returns listOf(PRICE_CATEGORY_MODEL_1)
        every { screeningFeeFinderService.findAllByScreeningIdIn(any()) } returns listOf()

        val mappedScreenings = screeningSearchResponseMapper.mapList(listOf(SCREENING_5))

        assertEquals(1, mappedScreenings.size)

        assertScreeningEquals(SCREENING_5, mappedScreenings[0])
        assertMovieSearchResponseEquals(MOVIE_1, mappedScreenings[0].movie)
        assertAuditoriumEquals(AUDITORIUM_1, mappedScreenings[0].auditorium)
        assertEquals(2, mappedScreenings[0].auditorium.seats.size)
        assertSeatEquals(SEAT_5, mappedScreenings[0].auditorium.seats[0])
        assertSeatEquals(SEAT_6, mappedScreenings[0].auditorium.seats[1])

        verifySequence {
            movieJooqFinderService.findAllNonDeletedByIdIn(setOf(MOVIE_1.id))
            auditoriumJooqFinderService.findAllByIdIn(setOf(AUDITORIUM_1.id))
            seatJooqFinderService.findAllSeatsByAuditoriumIdAndAuditoriumLayoutIdIn(
                setOf(Pair(AUDITORIUM_1.id, AUDITORIUM_LAYOUT_2.id))
            )
            reservationJooqFinderService.findAllByScreeningIdIn(setOf(SCREENING_5.id))
            priceCategoryJooqFinderService.getByIdsWithItems(setOf(PRICE_CATEGORY_1.id))
            screeningFeeFinderService.findAllByScreeningIdIn(setOf(SCREENING_5.id))
        }
    }

    @Test
    fun `test mapList - DBOX seat with zero surcharge - should map to PREMIUM_PLUS`() {
        every { movieJooqFinderService.findAllNonDeletedByIdIn(any()) } returns listOf(MOVIE_1)
        every { auditoriumJooqFinderService.findAllByIdIn(any()) } returns listOf(AUDITORIUM_1)
        every { seatJooqFinderService.findAllSeatsByAuditoriumIdAndAuditoriumLayoutIdIn(any()) } returns listOf(SEAT_DBOX)
        every { reservationJooqFinderService.findAllByScreeningIdIn(any()) } returns listOf()
        every { priceCategoryJooqFinderService.getByIdsWithItems(any()) } returns listOf(PRICE_CATEGORY_MODEL_1)
        every { screeningFeeFinderService.findAllByScreeningIdIn(any()) } returns listOf(SCREENING_FEE_ZERO_DBOX)

        val mappedScreenings = screeningSearchResponseMapper.mapList(listOf(SCREENING_1))

        assertEquals(1, mappedScreenings.size)
        assertEquals(1, mappedScreenings[0].auditorium.seats.size)

        val mappedSeat = mappedScreenings[0].auditorium.seats[0]
        assertEquals(SEAT_DBOX.id, mappedSeat.id)
        assertEquals(SeatType.PREMIUM_PLUS, mappedSeat.type) // DBOX should be converted to PREMIUM_PLUS
        assertEquals(SEAT_DBOX.row, mappedSeat.row)
        assertEquals(SEAT_DBOX.number, mappedSeat.number)

        verifySequence {
            movieJooqFinderService.findAllNonDeletedByIdIn(setOf(MOVIE_1.id))
            auditoriumJooqFinderService.findAllByIdIn(setOf(AUDITORIUM_1.id))
            seatJooqFinderService.findAllSeatsByAuditoriumIdAndAuditoriumLayoutIdIn(
                setOf(Pair(AUDITORIUM_1.id, AUDITORIUM_LAYOUT_1.id))
            )
            reservationJooqFinderService.findAllByScreeningIdIn(setOf(SCREENING_1.id))
            priceCategoryJooqFinderService.getByIdsWithItems(setOf(PRICE_CATEGORY_1.id))
            screeningFeeFinderService.findAllByScreeningIdIn(setOf(SCREENING_1.id))
        }
    }
}

private val DISTRIBUTOR_1_ID = UUID.randomUUID()
private val MOVIE_1 = createMovie(
    originalId = 1,
    title = "Oppenheimer",
    parsedTechnology = MovieTechnology.IMAX,
    distributorId = DISTRIBUTOR_1_ID
)
private val MOVIE_2 = createMovie(
    originalId = 2,
    title = "Barbie",
    distributorId = DISTRIBUTOR_1_ID
)
private val AUDITORIUM_1 = createAuditorium(originalId = 1)
private val AUDITORIUM_2 = createAuditorium(originalId = 2, code = "B", title = "Sála B")
private val AUDITORIUM_LAYOUT_1 = createAuditoriumLayout(originalId = 1, auditoriumId = AUDITORIUM_1.id, code = "01")
private val AUDITORIUM_LAYOUT_2 = createAuditoriumLayout(originalId = 2, auditoriumId = AUDITORIUM_1.id, code = "02")
private val AUDITORIUM_LAYOUT_3 = createAuditoriumLayout(originalId = 3, auditoriumId = AUDITORIUM_2.id, code = "03")
private val AUDITORIUM_LAYOUT_4 = createAuditoriumLayout(originalId = 4, auditoriumId = AUDITORIUM_1.id, code = "04")
private val SEAT_1 = createSeat(
    originalId = 1,
    auditoriumLayoutId = AUDITORIUM_LAYOUT_1.id,
    auditoriumId = AUDITORIUM_1.id,
    type = SeatType.VIP,
    doubleSeatType = DoubleSeatType.DOUBLE_SEAT_RIGHT,
    row = "A",
    number = "1"
)
private val SEAT_2 = createSeat(
    originalId = 2,
    auditoriumLayoutId = AUDITORIUM_LAYOUT_1.id,
    auditoriumId = AUDITORIUM_1.id,
    type = SeatType.VIP,
    doubleSeatType = DoubleSeatType.DOUBLE_SEAT_LEFT,
    row = "A",
    number = "2"
)
private val SEAT_3 = createSeat(
    originalId = 3,
    auditoriumLayoutId = AUDITORIUM_LAYOUT_3.id,
    auditoriumId = AUDITORIUM_2.id,
    type = SeatType.REGULAR,
    row = "1",
    number = "5"
)
private val SEAT_4 = createSeat(
    originalId = 4,
    auditoriumLayoutId = AUDITORIUM_LAYOUT_3.id,
    auditoriumId = AUDITORIUM_2.id,
    type = SeatType.PREMIUM_PLUS,
    row = "1",
    number = "10"
)
private val SEAT_5 = createSeat(
    originalId = 5,
    auditoriumLayoutId = AUDITORIUM_LAYOUT_2.id,
    auditoriumId = AUDITORIUM_1.id
)
private val SEAT_6 = createSeat(
    originalId = 6,
    auditoriumLayoutId = AUDITORIUM_LAYOUT_2.id,
    auditoriumId = AUDITORIUM_1.id
)
private val SEAT_7 = createSeat(
    originalId = 7,
    auditoriumLayoutId = AUDITORIUM_LAYOUT_4.id,
    auditoriumId = AUDITORIUM_1.id
)
private val SEAT_8 = createSeat(
    originalId = 8,
    auditoriumLayoutId = AUDITORIUM_LAYOUT_3.id,
    auditoriumId = AUDITORIUM_2.id,
    type = SeatType.PREMIUM_PLUS,
    row = "2",
    number = "10"
)
private val SEAT_DBOX = createSeat(
    originalId = 9,
    auditoriumLayoutId = AUDITORIUM_LAYOUT_1.id,
    auditoriumId = AUDITORIUM_1.id,
    type = SeatType.DBOX,
    row = "A",
    number = "3"
)
private val PRICE_CATEGORY_1 = createPriceCategory(
    originalId = 1,
    title = "ARTMAX PO 17",
    active = true
)
private val PRICE_CATEGORY_2 = createPriceCategory(
    originalId = 2,
    title = "Extra - Silvest",
    active = true
)
private val PRICE_CATEGORY_ITEM_1 = createPriceCategoryItem(
    priceCategoryId = PRICE_CATEGORY_1.id,
    number = PriceCategoryItemNumber.PRICE_1,
    title = "Dospely",
    price = BigDecimal.valueOf(10.5)
)
private val PRICE_CATEGORY_ITEM_2 = createPriceCategoryItem(
    priceCategoryId = PRICE_CATEGORY_2.id,
    number = PriceCategoryItemNumber.PRICE_1,
    title = "Dospely",
    price = BigDecimal.valueOf(12.5)
)
private val PRICE_CATEGORY_MODEL_1 = mapToPriceCategoryModel(
    priceCategory = PRICE_CATEGORY_1,
    items = listOf(PRICE_CATEGORY_ITEM_1)
)
private val PRICE_CATEGORY_MODEL_2 = mapToPriceCategoryModel(
    priceCategory = PRICE_CATEGORY_2,
    items = listOf(PRICE_CATEGORY_ITEM_2)
)
private val SCREENING_1 = createScreening(
    originalId = 1,
    auditoriumLayoutId = AUDITORIUM_LAYOUT_1.id,
    auditoriumId = AUDITORIUM_1.id,
    movieId = MOVIE_1.id,
    priceCategoryId = PRICE_CATEGORY_1.id
)
private val SCREENING_2 = createScreening(
    originalId = 2,
    auditoriumLayoutId = AUDITORIUM_LAYOUT_3.id,
    auditoriumId = AUDITORIUM_2.id,
    movieId = MOVIE_2.id,
    priceCategoryId = PRICE_CATEGORY_1.id
)
private val SCREENING_3 = createScreening(
    originalId = 3,
    auditoriumLayoutId = AUDITORIUM_LAYOUT_3.id,
    auditoriumId = AUDITORIUM_2.id,
    movieId = MOVIE_1.id,
    time = LocalTime.of(20, 0, 0),
    priceCategoryId = PRICE_CATEGORY_2.id
)

// identical to SCREENING_1, but with different layout
private val SCREENING_4 = createScreening(
    originalId = 4,
    auditoriumLayoutId = AUDITORIUM_LAYOUT_2.id,
    auditoriumId = AUDITORIUM_1.id,
    movieId = MOVIE_1.id,
    priceCategoryId = PRICE_CATEGORY_1.id
)

// identical to SCREENING_4, but with saleTimeLimit spanning multiple days
private val SCREENING_5 = createScreening(
    originalId = 5,
    auditoriumLayoutId = AUDITORIUM_LAYOUT_2.id,
    auditoriumId = AUDITORIUM_1.id,
    movieId = MOVIE_1.id,
    priceCategoryId = PRICE_CATEGORY_1.id,
    date = LocalDate.of(2024, 4, 1),
    time = LocalTime.of(20, 0),
    saleTimeLimit = 2000
)
private val GROUP_RESERVATION_1 = createGroupReservation()
private val RESERVATION_1 = createReservation(
    screeningId = SCREENING_1.id,
    seatId = SEAT_1.id,
    state = ReservationState.RESERVED
)
private val RESERVATION_2 = createReservation(
    screeningId = SCREENING_2.id,
    seatId = SEAT_3.id,
    state = ReservationState.UNAVAILABLE
)
private val RESERVATION_3 = createReservation(
    screeningId = SCREENING_2.id,
    seatId = SEAT_4.id,
    state = ReservationState.GROUP_RESERVED,
    groupReservationId = GROUP_RESERVATION_1.id
)
private val RESERVATION_4 = createReservation(
    screeningId = SCREENING_3.id,
    seatId = SEAT_4.id,
    state = ReservationState.UNAVAILABLE
)
private val RESERVATION_5 = createReservation(
    screeningId = SCREENING_2.id,
    seatId = SEAT_8.id,
    state = ReservationState.GROUP_RESERVED,
    groupReservationId = GROUP_RESERVATION_1.id
).apply { markDeleted() }
private val SCREENING_FEE_ZERO_DBOX = createScreeningFee(
    originalScreeningId = 1,
    screeningId = SCREENING_1.id,
    surchargeDBox = BigDecimal.ZERO
)
