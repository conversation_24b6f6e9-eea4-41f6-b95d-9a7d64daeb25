package com.cleevio.cinemax.api.module.basketitem.service

import com.cleevio.cinemax.api.DatabaseCleanup
import com.cleevio.cinemax.api.IntegrationTest
import com.cleevio.cinemax.api.common.constant.PaymentType
import com.cleevio.cinemax.api.common.constant.STANDARD_TAX_RATE
import com.cleevio.cinemax.api.common.util.isEqualTo
import com.cleevio.cinemax.api.common.util.isGreaterThan
import com.cleevio.cinemax.api.module.auditorium.service.AuditoriumService
import com.cleevio.cinemax.api.module.auditoriumlayout.service.AuditoriumLayoutRepository
import com.cleevio.cinemax.api.module.basket.constant.BasketState
import com.cleevio.cinemax.api.module.basket.entity.Basket
import com.cleevio.cinemax.api.module.basket.event.BasketItemModifiedEvent
import com.cleevio.cinemax.api.module.basket.event.MssqlBasketItemWithDiscountCardCreatedEvent
import com.cleevio.cinemax.api.module.basket.event.ProductBasketItemAddedToBasketEvent
import com.cleevio.cinemax.api.module.basket.event.ProductBasketItemDeletedFromBasketEvent
import com.cleevio.cinemax.api.module.basket.event.ProductBasketItemQuantityModifiedEvent
import com.cleevio.cinemax.api.module.basket.exception.BasketNotFoundException
import com.cleevio.cinemax.api.module.basket.exception.InvalidBasketStateException
import com.cleevio.cinemax.api.module.basket.service.BasketJpaFinderService
import com.cleevio.cinemax.api.module.basket.service.BasketRepository
import com.cleevio.cinemax.api.module.basket.service.BasketService
import com.cleevio.cinemax.api.module.basket.service.command.CreateBasketItemInput
import com.cleevio.cinemax.api.module.basket.service.command.CreateMssqlTicketBasketItemCommand
import com.cleevio.cinemax.api.module.basket.service.command.CreateProductInput
import com.cleevio.cinemax.api.module.basket.service.command.MessagingCreateBasketCommand
import com.cleevio.cinemax.api.module.basket.service.command.MessagingCreateBasketCommand.BasketTicketItemTicketPriceInput
import com.cleevio.cinemax.api.module.basket.service.command.MessagingCreateCancelledBasketItemCommand
import com.cleevio.cinemax.api.module.basketitem.constant.BasketItemType
import com.cleevio.cinemax.api.module.basketitem.controller.dto.CreateBasketItemRequest
import com.cleevio.cinemax.api.module.basketitem.controller.dto.CreateProductRequest
import com.cleevio.cinemax.api.module.basketitem.controller.dto.CreateReservationRequest
import com.cleevio.cinemax.api.module.basketitem.controller.dto.CreateTicketDiscountRequest
import com.cleevio.cinemax.api.module.basketitem.controller.dto.CreateTicketPriceRequest
import com.cleevio.cinemax.api.module.basketitem.controller.dto.CreateTicketRequest
import com.cleevio.cinemax.api.module.basketitem.entity.BasketItem
import com.cleevio.cinemax.api.module.basketitem.event.AdminCancelledBasketItemCreatedEvent
import com.cleevio.cinemax.api.module.basketitem.event.BasketItemWithDiscountCardUpdatedEvent
import com.cleevio.cinemax.api.module.basketitem.event.BasketItemWithPackagingDepositUpdatedEvent
import com.cleevio.cinemax.api.module.basketitem.event.ProductWithPackagingDepositFoundEvent
import com.cleevio.cinemax.api.module.basketitem.event.TableBasketItemDeletedEvent
import com.cleevio.cinemax.api.module.basketitem.event.TableBasketItemUpdatedEvent
import com.cleevio.cinemax.api.module.basketitem.event.TicketBasketItemDiscountRemovalInitiatedEvent
import com.cleevio.cinemax.api.module.basketitem.exception.BasketItemIsAlreadyCancelledException
import com.cleevio.cinemax.api.module.basketitem.exception.BasketItemNotFoundException
import com.cleevio.cinemax.api.module.basketitem.exception.SecondDiscountCardDiscountAppliedOnTicketBasketItemException
import com.cleevio.cinemax.api.module.basketitem.service.command.CancelBasketItemsCommand
import com.cleevio.cinemax.api.module.basketitem.service.command.Create3dGlassesBasketItemCommand
import com.cleevio.cinemax.api.module.basketitem.service.command.CreateBasketItemCommand
import com.cleevio.cinemax.api.module.basketitem.service.command.CreateBasketItemsFromGroupReservationCommand
import com.cleevio.cinemax.api.module.basketitem.service.command.CreatePackagingDepositBasketItemCommand
import com.cleevio.cinemax.api.module.basketitem.service.command.CreateProductSalesBasketItemCommand
import com.cleevio.cinemax.api.module.basketitem.service.command.CreateTicketBasketItemsCommand
import com.cleevio.cinemax.api.module.basketitem.service.command.DeleteBasketItemCommand
import com.cleevio.cinemax.api.module.basketitem.service.command.DeleteBasketItemsCommand
import com.cleevio.cinemax.api.module.basketitem.service.command.PatchBasketItemCommand
import com.cleevio.cinemax.api.module.basketitem.service.command.UpdatePackagingDepositBasketItemCommand
import com.cleevio.cinemax.api.module.basketitem.service.command.UpdateTicketBasketItemsIncludes3dGlasses
import com.cleevio.cinemax.api.module.branch.service.BranchRepository
import com.cleevio.cinemax.api.module.discountcard.constant.DiscountCardType
import com.cleevio.cinemax.api.module.discountcard.service.DiscountCardMssqlService
import com.cleevio.cinemax.api.module.discountcardusage.entity.DiscountCardUsage
import com.cleevio.cinemax.api.module.discountcardusage.service.DiscountCardUsageFinderService
import com.cleevio.cinemax.api.module.discountcardusage.service.DiscountCardUsageService
import com.cleevio.cinemax.api.module.discountcardusage.service.command.CreateDiscountCardUsageCommand
import com.cleevio.cinemax.api.module.discountcardusage.service.command.UpdateDiscountCardUsageCommand
import com.cleevio.cinemax.api.module.distributor.service.DistributorService
import com.cleevio.cinemax.api.module.groupreservation.exception.GroupReservationNotFoundException
import com.cleevio.cinemax.api.module.groupreservation.service.GroupReservationRepository
import com.cleevio.cinemax.api.module.movie.service.MovieService
import com.cleevio.cinemax.api.module.outboxevent.event.BasketItemsCancelledEvent
import com.cleevio.cinemax.api.module.posconfiguration.constant.ProductMode
import com.cleevio.cinemax.api.module.posconfiguration.constant.TablesType
import com.cleevio.cinemax.api.module.posconfiguration.service.PosConfigurationService
import com.cleevio.cinemax.api.module.posconfiguration.service.command.CreateOrUpdatePosConfigurationCommand
import com.cleevio.cinemax.api.module.pricecategory.service.PriceCategoryService
import com.cleevio.cinemax.api.module.pricecategoryitem.constant.PriceCategoryItemNumber
import com.cleevio.cinemax.api.module.pricecategoryitem.service.PriceCategoryItemService
import com.cleevio.cinemax.api.module.product.constant.ProductType
import com.cleevio.cinemax.api.module.product.service.ProductService
import com.cleevio.cinemax.api.module.productcategory.constant.ProductCategoryType
import com.cleevio.cinemax.api.module.productcategory.service.ProductCategoryService
import com.cleevio.cinemax.api.module.productcomponent.constant.ProductComponentUnit
import com.cleevio.cinemax.api.module.productcomponent.service.ProductComponentService
import com.cleevio.cinemax.api.module.productcomponentcategory.service.ProductComponentCategoryRepository
import com.cleevio.cinemax.api.module.productcomposition.service.ProductCompositionService
import com.cleevio.cinemax.api.module.reservation.constant.ReservationState
import com.cleevio.cinemax.api.module.reservation.exception.InvalidReservationStateException
import com.cleevio.cinemax.api.module.reservation.service.ReservationJooqFinderService
import com.cleevio.cinemax.api.module.reservation.service.ReservationRepository
import com.cleevio.cinemax.api.module.reservation.service.ReservationService
import com.cleevio.cinemax.api.module.reservation.service.command.CreateReservationsForSeatsCommand
import com.cleevio.cinemax.api.module.screening.service.ScreeningService
import com.cleevio.cinemax.api.module.screeningfee.service.ScreeningFeeService
import com.cleevio.cinemax.api.module.seat.service.SeatService
import com.cleevio.cinemax.api.module.ticket.entity.Ticket
import com.cleevio.cinemax.api.module.ticket.exception.ScreeningForTicketNotFoundException
import com.cleevio.cinemax.api.module.ticket.exception.SeatForTicketNotFoundException
import com.cleevio.cinemax.api.module.ticket.service.TicketJooqFinderService
import com.cleevio.cinemax.api.module.ticket.service.TicketRepository
import com.cleevio.cinemax.api.module.ticketdiscount.constant.TicketDiscountType
import com.cleevio.cinemax.api.module.ticketdiscount.constant.TicketDiscountUsageType
import com.cleevio.cinemax.api.module.ticketdiscount.service.TicketDiscountService
import com.cleevio.cinemax.api.module.ticketprice.constant.AuditoriumServiceFeeType
import com.cleevio.cinemax.api.module.ticketprice.constant.AuditoriumSurchargeType
import com.cleevio.cinemax.api.module.ticketprice.constant.SeatServiceFeeType
import com.cleevio.cinemax.api.module.ticketprice.constant.SeatSurchargeType
import com.cleevio.cinemax.api.module.ticketprice.entity.TicketPrice
import com.cleevio.cinemax.api.module.ticketprice.service.TicketPriceJooqFinderService
import com.cleevio.cinemax.api.module.ticketprice.service.TicketPriceRepository
import com.cleevio.cinemax.api.util.TEST_PRINCIPAL_USERNAME
import com.cleevio.cinemax.api.util.createAuditorium
import com.cleevio.cinemax.api.util.createAuditoriumLayout
import com.cleevio.cinemax.api.util.createBranch
import com.cleevio.cinemax.api.util.createDiscountCard
import com.cleevio.cinemax.api.util.createDistributor
import com.cleevio.cinemax.api.util.createGroupReservation
import com.cleevio.cinemax.api.util.createMovie
import com.cleevio.cinemax.api.util.createPriceCategory
import com.cleevio.cinemax.api.util.createPriceCategoryItem
import com.cleevio.cinemax.api.util.createProduct
import com.cleevio.cinemax.api.util.createProductCategory
import com.cleevio.cinemax.api.util.createProductComponent
import com.cleevio.cinemax.api.util.createProductComponentCategory
import com.cleevio.cinemax.api.util.createProductComposition
import com.cleevio.cinemax.api.util.createReservation
import com.cleevio.cinemax.api.util.createScreening
import com.cleevio.cinemax.api.util.createScreeningFee
import com.cleevio.cinemax.api.util.createSeat
import com.cleevio.cinemax.api.util.createTicketDiscount
import com.cleevio.cinemax.api.util.mapInputItemsToInitBasketCommand
import com.cleevio.cinemax.api.util.mapToCreateBasketItemCommand
import com.cleevio.cinemax.api.util.mapToCreateOrUpdateAuditoriumCommand
import com.cleevio.cinemax.api.util.mapToCreateOrUpdateDiscountCardCommand
import com.cleevio.cinemax.api.util.mapToCreateOrUpdateDistributorCommand
import com.cleevio.cinemax.api.util.mapToCreateOrUpdateMovieCommand
import com.cleevio.cinemax.api.util.mapToCreateOrUpdatePriceCategoryItemCommand
import com.cleevio.cinemax.api.util.mapToCreateOrUpdateProductCategoryCommand
import com.cleevio.cinemax.api.util.mapToCreateOrUpdateProductComponentCommand
import com.cleevio.cinemax.api.util.mapToCreateOrUpdateProductCompositionCommand
import com.cleevio.cinemax.api.util.mapToCreateOrUpdateScreeningCommand
import com.cleevio.cinemax.api.util.mapToCreateOrUpdateScreeningFeeCommand
import com.cleevio.cinemax.api.util.mapToCreateOrUpdateSeatCommand
import com.cleevio.cinemax.api.util.mapToCreateOrUpdateTicketDiscountCommand
import com.cleevio.cinemax.api.util.mapToInitBasketCommand
import com.cleevio.cinemax.api.util.mapToSyncCreateOrUpdatePriceCategoryCommand
import com.cleevio.cinemax.api.util.mapToSyncCreateOrUpdateProductCommand
import com.cleevio.cinemax.api.util.toUUID
import io.mockk.Runs
import io.mockk.every
import io.mockk.just
import io.mockk.verify
import io.mockk.verifyOrder
import io.mockk.verifySequence
import jakarta.validation.ConstraintViolationException
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Disabled
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.Arguments
import org.junit.jupiter.params.provider.MethodSource
import org.springframework.beans.factory.annotation.Autowired
import java.math.BigDecimal
import java.time.LocalDateTime
import java.util.Optional
import java.util.UUID
import java.util.stream.Stream
import kotlin.test.assertEquals
import kotlin.test.assertFalse
import kotlin.test.assertNotEquals
import kotlin.test.assertNotNull
import kotlin.test.assertNull
import kotlin.test.assertTrue

class BasketItemServiceIT @Autowired constructor(
    private val underTest: BasketItemService,
    private val basketItemJooqFinderRepository: BasketItemJooqFinderRepository,
    private val basketItemRepository: BasketItemRepository,
    private val basketJpaFinderService: BasketJpaFinderService,
    private val basketService: BasketService,
    private val auditoriumService: AuditoriumService,
    private val movieService: MovieService,
    private val screeningService: ScreeningService,
    private val seatService: SeatService,
    private val reservationJooqFinderService: ReservationJooqFinderService,
    private val priceCategoryService: PriceCategoryService,
    private val priceCategoryItemService: PriceCategoryItemService,
    private val productService: ProductService,
    private val productCategoryService: ProductCategoryService,
    private val ticketPriceJooqFinderService: TicketPriceJooqFinderService,
    private val screeningFeeService: ScreeningFeeService,
    private val productComponentService: ProductComponentService,
    private val productCompositionService: ProductCompositionService,
    private val ticketJooqFinderService: TicketJooqFinderService,
    private val ticketDiscountService: TicketDiscountService,
    private val discountCardMssqlService: DiscountCardMssqlService,
    private val discountCardUsageService: DiscountCardUsageService,
    private val discountCardUsageFinderService: DiscountCardUsageFinderService,
    private val posConfigurationService: PosConfigurationService,
    private val distributorService: DistributorService,
    private val auditoriumLayoutRepository: AuditoriumLayoutRepository,
    private val ticketRepository: TicketRepository,
    private val reservationRepository: ReservationRepository,
    private val ticketPriceRepository: TicketPriceRepository,
    private val basketRepository: BasketRepository,
    private val reservationService: ReservationService,
    private val productComponentCategoryRepository: ProductComponentCategoryRepository,
    private val groupReservationRepository: GroupReservationRepository,
    private val branchRepository: BranchRepository,
) : IntegrationTest(DatabaseCleanup.AFTER_EACH) {

    @BeforeEach
    fun setUp() {
        branchRepository.save(BRANCH_1)

        setOf(AUDITORIUM_1, AUDITORIUM_2).forEach {
            auditoriumService.syncCreateOrUpdateAuditorium(mapToCreateOrUpdateAuditoriumCommand(it))
        }
        setOf(AUDITORIUM_LAYOUT_1, AUDITORIUM_LAYOUT_2).forEach {
            auditoriumLayoutRepository.save(it)
        }
        distributorService.syncCreateOrUpdateDistributor(mapToCreateOrUpdateDistributorCommand(DISTRIBUTOR_1))
        movieService.syncCreateOrUpdateMovie(mapToCreateOrUpdateMovieCommand(MOVIE_1))
        priceCategoryService.syncCreateOrUpdatePriceCategory(
            mapToSyncCreateOrUpdatePriceCategoryCommand(PRICE_CATEGORY_1)
        )
        screeningService.syncCreateOrUpdateScreening(mapToCreateOrUpdateScreeningCommand(SCREENING_1))
        screeningService.syncCreateOrUpdateScreening(mapToCreateOrUpdateScreeningCommand(SCREENING_2))
        setOf(SEAT_1, SEAT_2).forEach {
            seatService.createOrUpdateSeat(mapToCreateOrUpdateSeatCommand(it))
        }
        setOf(PRODUCT_CATEGORY_1, PRODUCT_CATEGORY_2, PRODUCT_CATEGORY_3, GLASSES_PRODUCT_CATEGORY).forEach {
            productCategoryService.syncCreateOrUpdateProductCategory(mapToCreateOrUpdateProductCategoryCommand(it))
        }
        setOf(PRODUCT_1, PRODUCT_2, PRODUCT_3, PRODUCT_5).forEach {
            productService.syncCreateOrUpdateProduct(
                mapToSyncCreateOrUpdateProductCommand(it, PRODUCT_CATEGORY_1.id)
            )
        }
        setOf(GLASSES_3D_PRODUCT, GLASSES_IMAX_PRODUCT).forEach {
            productService.syncCreateOrUpdateProduct(
                mapToSyncCreateOrUpdateProductCommand(it, GLASSES_PRODUCT_CATEGORY.id)
            )
        }
        productService.syncCreateOrUpdateProduct(
            mapToSyncCreateOrUpdateProductCommand(PRODUCT_4, PRODUCT_CATEGORY_3.id)
        )
        setOf(PRODUCT_DISCOUNT_1, PRODUCT_DISCOUNT_2).forEach {
            productService.syncCreateOrUpdateProduct(
                mapToSyncCreateOrUpdateProductCommand(it, PRODUCT_CATEGORY_2.id)
            )
        }
        productComponentCategoryRepository.save(PRODUCT_COMPONENT_CATEGORY_1)
        setOf(
            PRODUCT_COMPONENT_1,
            PRODUCT_COMPONENT_2,
            PRODUCT_COMPONENT_3,
            PRODUCT_COMPONENT_4,
            PRODUCT_COMPONENT_5,
            PRODUCT_COMPONENT_6
        ).forEach {
            productComponentService.syncCreateOrUpdateProductComponent(
                mapToCreateOrUpdateProductComponentCommand(it)
            )
        }
        setOf(
            PRODUCT_COMPOSITION_1,
            PRODUCT_COMPOSITION_2,
            PRODUCT_COMPOSITION_3,
            PRODUCT_COMPOSITION_4,
            PRODUCT_COMPOSITION_5,
            PRODUCT_COMPOSITION_6,
            PRODUCT_COMPOSITION_7,
            PRODUCT_COMPOSITION_8,
            PRODUCT_COMPOSITION_9,
            PRODUCT_COMPOSITION_10,
            PRODUCT_COMPOSITION_11
        ).forEach {
            productCompositionService.createOrUpdateProductComposition(
                mapToCreateOrUpdateProductCompositionCommand(it)
            )
        }
        setOf(PRICE_CATEGORY_ITEM_1, PRICE_CATEGORY_ITEM_2).forEach {
            priceCategoryItemService.createOrUpdatePriceCategoryItem(
                mapToCreateOrUpdatePriceCategoryItemCommand(it, PRICE_CATEGORY_1.id)
            )
        }
        screeningFeeService.createOrUpdateScreeningFee(mapToCreateOrUpdateScreeningFeeCommand(SCREENING_FEE_1))
        posConfigurationService.createOrUpdatePosConfiguration(POS_CONFIGURATION_COMMAND)

        every {
            reservationMssqlFinderServiceMock.findByOriginalScreeningIdAndOriginalSeatId(
                any(),
                any()
            )
        } returns null
        every { applicationEventPublisherMock.publishEvent(any<ProductBasketItemAddedToBasketEvent>()) } just Runs
        every { applicationEventPublisherMock.publishEvent(any<ProductBasketItemDeletedFromBasketEvent>()) } just Runs
        every { applicationEventPublisherMock.publishEvent(any<ProductBasketItemQuantityModifiedEvent>()) } just Runs
        every { applicationEventPublisherMock.publishEvent(any<BasketItemWithDiscountCardUpdatedEvent>()) } just Runs
        every { applicationEventPublisherMock.publishEvent(any<TableBasketItemDeletedEvent>()) } just Runs
        every { applicationEventPublisherMock.publishEvent(any<TableBasketItemUpdatedEvent>()) } just Runs
        every { applicationEventPublisherMock.publishEvent(any<BasketItemsCancelledEvent>()) } just Runs
        every { applicationEventPublisherMock.publishEvent(any<ProductWithPackagingDepositFoundEvent>()) } just Runs
        every { applicationEventPublisherMock.publishEvent(any<AdminCancelledBasketItemCreatedEvent>()) } just Runs
    }

    @Test
    fun `test createBasketItem - product item - should create basket item`() {
        val basket = basketService.initBasket(mapInputItemsToInitBasketCommand(listOf()))
        val command = mapToCreateBasketItemCommand(PRODUCT_BASKET_ITEM_REQUEST_1, basket.id)
        val created = underTest.createBasketItem(command)

        val createdItem = basketItemJooqFinderRepository.findNonDeletedById(created.id)
        assertNotNull(createdItem)

        val expectedPrice = PRODUCT_1.price.times(command.quantity.toBigDecimal())
        assertBasketItemEquals(PRODUCT_BASKET_ITEM_REQUEST_1, expectedPrice, createdItem)
        assertEquals(TEST_PRINCIPAL_USERNAME, createdItem.createdBy)
        assertEquals(TEST_PRINCIPAL_USERNAME, createdItem.updatedBy)
        assertNull(createdItem.ticketId)
    }

    @Test
    fun `test createBasketItem - product item with package deposit - should create main basket items and publish event for package deposit basket item creation`() {
        val basket = basketService.initBasket(mapInputItemsToInitBasketCommand(listOf()))
        val command = mapToCreateBasketItemCommand(PRODUCT_BASKET_ITEM_REQUEST_4, basket.id)
        val created = underTest.createBasketItem(command)

        val createdItems = basketItemJooqFinderRepository.findAll()
        assertEquals(1, createdItems.size)
        assertTrue(createdItems.all { it.basketId == basket.id })

        createdItems.first { it.id == created.id }.let {
            val expectedPrice = PRODUCT_5.price.times(command.quantity.toBigDecimal())
            assertBasketItemEquals(PRODUCT_BASKET_ITEM_REQUEST_4, expectedPrice, it)
        }

        verify {
            applicationEventPublisherMock.publishEvent(
                ProductWithPackagingDepositFoundEvent(
                    packagingDepositProductId = PRODUCT_4.id,
                    quantity = 4,
                    basketId = basket.id
                )
            )
        }
    }

    @Test
    fun `test createBasketItem - ticket item - should create basket item`() {
        val basket = basketService.initBasket(mapInputItemsToInitBasketCommand(inputItems = listOf()))
        val command = mapToCreateBasketItemCommand(
            request = TICKET_BASKET_ITEM_REQUEST_1,
            basketId = basket.id
        )
        val created = underTest.createBasketItem(command)

        val createdTicket = ticketJooqFinderService.findById(created.ticketId!!)
        assertNotNull(createdTicket)
        assertEquals(TEST_PRINCIPAL_USERNAME, createdTicket.createdBy)
        assertEquals(TEST_PRINCIPAL_USERNAME, createdTicket.updatedBy)
        assertFalse(createdTicket.isGroupTicket)

        val createdTicketPrice = ticketPriceJooqFinderService.getById(createdTicket.ticketPriceId)
        assertEquals(SCREENING_1.id, createdTicketPrice.screeningId)
        assertEquals(SEAT_1.id, createdTicketPrice.seatId)
        assertEquals(PRICE_CATEGORY_ITEM_1.number, createdTicketPrice.basePriceItemNumber)

        val createdItem = basketItemJooqFinderRepository.findNonDeletedById(created.id)
        assertNotNull(createdItem)
        assertBasketItemEquals(TICKET_BASKET_ITEM_REQUEST_1, createdTicketPrice.totalPrice, createdItem)
        assertNotNull(createdItem.ticketId)
        assertEquals(TEST_PRINCIPAL_USERNAME, createdItem.createdBy)
        assertEquals(TEST_PRINCIPAL_USERNAME, createdItem.updatedBy)

        val createdReservation = reservationJooqFinderService.findById(createdTicket.reservationId)
        assertNotNull(createdReservation)
        assertEquals(createdReservation.seatId, command.reservationSeatId)
        assertEquals(createdReservation.screeningId, createdTicket.screeningId)
        assertEquals(createdReservation.state, ReservationState.RESERVED)
        assertEquals(TEST_PRINCIPAL_USERNAME, createdReservation.createdBy)
        assertEquals(TEST_PRINCIPAL_USERNAME, createdReservation.updatedBy)
    }

    @ParameterizedTest
    @MethodSource("invalidInitBasketItemCommandProvider")
    fun `test createBasketItem - ticket items with invalid quantity args - should throw exception`(
        command: CreateBasketItemCommand,
    ) {
        assertThrows<ConstraintViolationException> {
            underTest.createBasketItem(command)
        }
    }

    @Test
    fun `test createBasketItem - product item with quantity more than 1 - should create basket item`() {
        val basket = basketService.initBasket(mapInputItemsToInitBasketCommand(inputItems = listOf()))
        val command = mapToCreateBasketItemCommand(PRODUCT_BASKET_ITEM_REQUEST_1, basket.id)
        val commandWithUpdatedQuantity = command.copy(
            quantity = 2
        )
        val created = underTest.createBasketItem(commandWithUpdatedQuantity)
        val createdItem = basketItemJooqFinderRepository.findNonDeletedById(created.id)
        assertNotNull(createdItem)

        assertEquals(PRODUCT_BASKET_ITEM_REQUEST_1.type, createdItem.type)
        val expectedPrice = PRODUCT_1.price.times(commandWithUpdatedQuantity.quantity.toBigDecimal())
        assertTrue(createdItem.price isEqualTo expectedPrice)
        assertEquals(2, createdItem.quantity)
        assertNotNull(createdItem.createdAt)
        assertNotNull(createdItem.updatedAt)
        assertEquals(TEST_PRINCIPAL_USERNAME, createdItem.createdBy)
        assertEquals(TEST_PRINCIPAL_USERNAME, createdItem.updatedBy)
    }

    @Test
    fun `test createBasketItem - ticket item with fixed price - should set basket item fixed price`() {
        ticketDiscountService.syncCreateOrUpdateTicketDiscount(
            mapToCreateOrUpdateTicketDiscountCommand(
                TICKET_DISCOUNT_4
            )
        )

        val basket = basketService.initBasket(mapInputItemsToInitBasketCommand(inputItems = listOf()))
        val command = mapToCreateBasketItemCommand(
            request = TICKET_BASKET_ITEM_REQUEST_1.copy(
                ticket = TICKET_BASKET_ITEM_REQUEST_1.ticket?.copy(
                    primaryDiscount = CreateTicketDiscountRequest(
                        ticketDiscountId = TICKET_DISCOUNT_4.id
                    )
                )
            ),
            basketId = basket.id
        )

        val created = underTest.createBasketItem(command)

        val createdTicket = ticketJooqFinderService.findById(created.ticketId!!)
        assertNotNull(createdTicket)
        assertEquals(TEST_PRINCIPAL_USERNAME, createdTicket.createdBy)
        assertEquals(TEST_PRINCIPAL_USERNAME, createdTicket.updatedBy)
        assertFalse(createdTicket.isGroupTicket)

        val createdTicketPrice = ticketPriceJooqFinderService.getById(createdTicket.ticketPriceId)
        assertEquals(SCREENING_1.id, createdTicketPrice.screeningId)
        assertEquals(SEAT_1.id, createdTicketPrice.seatId)
        assertEquals(PRICE_CATEGORY_ITEM_1.number, createdTicketPrice.basePriceItemNumber)

        val createdItem = basketItemJooqFinderRepository.findNonDeletedById(created.id)
        assertNotNull(createdItem)
        assertBasketItemEquals(TICKET_BASKET_ITEM_REQUEST_1, createdTicketPrice.totalPrice, createdItem)
        assertTrue(TICKET_DISCOUNT_4.amount isEqualTo createdItem.fixedPrice)
        assertNotNull(createdItem.ticketId)
        assertEquals(TEST_PRINCIPAL_USERNAME, createdItem.createdBy)
        assertEquals(TEST_PRINCIPAL_USERNAME, createdItem.updatedBy)
    }

    @Test
    fun `test deleteBasketItem - ticket item - should delete basket item and reservation`() {
        val modifiedRequest = TICKET_BASKET_ITEM_REQUEST_1.copy(
            ticket = CreateTicketRequest(
                ticketPrice = CreateTicketPriceRequest(
                    priceCategoryItemNumber = PRICE_CATEGORY_ITEM_1.number
                ),
                reservation = CreateReservationRequest(
                    seatId = SEAT_1.id
                ),
                screeningId = SCREENING_1.id,
                isGroupTicket = false
            )
        )
        val basket = basketService.initBasket(
            mapToInitBasketCommand(
                items = listOf(modifiedRequest)
            )
        )

        val createdItem = basketItemJooqFinderRepository.findAllNonDeleted()[0]
        assertNotNull(createdItem)
        val createdTicket = ticketJooqFinderService.findById(createdItem.ticketId!!)
        assertNotNull(createdTicket)
        assertNotNull(createdTicket.reservationId)
        assertNotNull(createdTicket.ticketPriceId)
        assertFalse(createdTicket.isGroupTicket)
        val ticketPrice = ticketPriceJooqFinderService.getById(createdTicket.ticketPriceId)

        underTest.deleteBasketItem(DeleteBasketItemCommand(basket.id, createdItem.id))

        assertNull(basketItemJooqFinderRepository.findNonDeletedById(createdItem.id))
        val deletedItem = basketItemJooqFinderRepository.findById(createdItem.id)
        assertEquals(TEST_PRINCIPAL_USERNAME, deletedItem?.updatedBy)

        val deletedTicket = ticketJooqFinderService.findById(createdTicket.id)
        assertNotNull(deletedTicket)
        assertNotNull(deletedTicket.deletedAt)
        assertEquals(TEST_PRINCIPAL_USERNAME, deletedTicket.updatedBy)

        assertNotNull(ticketPriceJooqFinderService.findById(ticketPrice.id)) // should stay non deleted

        val deletedReservation = reservationJooqFinderService.findById(createdTicket.reservationId)
        assertNotNull(deletedReservation)
        assertNotNull(deletedReservation.deletedAt)
        assertEquals(ReservationState.DELETED, deletedReservation.state)
        assertEquals(TEST_PRINCIPAL_USERNAME, deletedReservation.updatedBy)

        // create given basket item again
        val command = mapToCreateBasketItemCommand(
            request = modifiedRequest,
            basketId = basket.id
        )
        val created2 = underTest.createBasketItem(command)
        val createdItem2 = basketItemJooqFinderRepository.findNonDeletedById(created2.id)
        assertNotNull(createdItem2)
        assertNotNull(createdItem2.ticketId)
        assertEquals(createdItem.basketId, createdItem2.basketId)
        assertEquals(TICKET_BASKET_ITEM_REQUEST_1.type, createdItem2.type)
        assertEquals(ticketPrice.totalPrice, createdItem2.price)
        assertEquals(TICKET_BASKET_ITEM_REQUEST_1.quantity, createdItem2.quantity)
        assertNotNull(createdItem2.createdAt)
        assertNotNull(createdItem2.updatedAt)
    }

    @Test
    fun `test deleteBasketItem - product item - should delete basket item`() {
        val basket = basketService.initBasket(
            mapToInitBasketCommand(
                items = listOf(PRODUCT_BASKET_ITEM_REQUEST_1)
            )
        )
        val createdItem = basketItemJooqFinderRepository.findAllNonDeleted()[0]
        assertNotNull(createdItem)
        assertEquals(PRODUCT_BASKET_ITEM_REQUEST_1.type, createdItem.type)
        assertEquals(PRODUCT_1.price, createdItem.price)
        assertEquals(PRODUCT_BASKET_ITEM_REQUEST_1.quantity, createdItem.quantity)
        assertNull(createdItem.ticketId)
        assertNotNull(createdItem.createdAt)
        assertNotNull(createdItem.updatedAt)

        underTest.deleteBasketItem(DeleteBasketItemCommand(basket.id, createdItem.id))
        val deletedItem = basketItemJooqFinderRepository.findById(createdItem.id)
        assertEquals(TEST_PRINCIPAL_USERNAME, deletedItem?.updatedBy)

        assertNull(basketItemJooqFinderRepository.findNonDeletedById(createdItem.id))
    }

    @Test
    fun `test deleteBasketItem - product item with sufficient quantity - should delete basket item`() {
        val basket = basketService.initBasket(
            mapToInitBasketCommand(
                items = listOf(PRODUCT_BASKET_ITEM_REQUEST_2)
            )
        )
        val createdItem = basketItemJooqFinderRepository.findAllNonDeleted()[0]

        underTest.deleteBasketItem(DeleteBasketItemCommand(basket.id, createdItem.id))
        val deletedItem = basketItemJooqFinderRepository.findById(createdItem.id)
        assertEquals(TEST_PRINCIPAL_USERNAME, deletedItem?.updatedBy)

        assertNull(basketItemJooqFinderRepository.findNonDeletedById(createdItem.id))
    }

    @Test
    fun `test deleteBasketItems - should delete basket items`() {
        val createdBasket = basketService.initBasket(
            mapToInitBasketCommand(
                items = listOf(TICKET_BASKET_ITEM_REQUEST_1, PRODUCT_BASKET_ITEM_REQUEST_1)
            )
        )
        val createdItems = basketItemJooqFinderRepository.findAllNonDeletedByBasketId(createdBasket.id)
        assertEquals(2, createdItems.size)

        assertEquals(1, reservationJooqFinderService.findAllNonDeleted().size)
        assertEquals(1, ticketJooqFinderService.findAllNonDeletedByBasketId(createdBasket.id).size)

        underTest.deleteBasketItems(
            DeleteBasketItemsCommand(
                basketId = createdBasket.id,
                basketItemIds = createdItems.map { it.id }.toSet()
            )
        )

        val basketItems = basketItemJooqFinderRepository.findAll()
        assertEquals(2, basketItems.size)
        assertEquals(0, basketItemJooqFinderRepository.findAllNonDeletedByBasketId(createdBasket.id).size)

        assertEquals(1, reservationJooqFinderService.findAll().size)
        assertEquals(0, reservationJooqFinderService.findAllNonDeleted().size)

        assertEquals(1, ticketJooqFinderService.findAll().size)
        assertEquals(0, ticketJooqFinderService.findAllNonDeletedByBasketId(createdBasket.id).size)

        verifyOrder {
            applicationEventPublisherMock.publishEvent(
                ProductBasketItemDeletedFromBasketEvent(
                    productId = PRODUCT_1.id,
                    quantity = -1
                )
            )
            applicationEventPublisherMock.publishEvent(
                BasketItemModifiedEvent(createdBasket.id)
            )
        }
    }

    @Disabled
    @Test
    fun `test patchBasketItem - update quantity - should update quantity`() {
        val basket = basketService.initBasket(
            mapToInitBasketCommand(
                items = listOf(PRODUCT_BASKET_ITEM_REQUEST_2)
            )
        )
        val createdItem = basketItemJooqFinderRepository.findAllNonDeleted()[0]
        val updatedAt = createdItem.updatedAt
        assertEquals(TEST_PRINCIPAL_USERNAME, createdItem.updatedBy)
        assertTrue(createdItem.price isEqualTo 2.0.toBigDecimal())
        assertTrue(createdItem.vatAmount isEqualTo 0.37.toBigDecimal())

        val newQuantity = 3
        underTest.patchBasketItem(
            PatchBasketItemCommand(
                basketId = basket.id,
                basketItemId = createdItem.id,
                quantity = newQuantity
            )
        )

        val updatedItem = basketItemJooqFinderRepository.findNonDeletedById(createdItem.id)
        assertNotNull(updatedItem)
        assertTrue(updatedItem.updatedAt.isAfter(updatedAt))
        assertEquals(newQuantity, updatedItem.quantity)
        assertEquals(TEST_PRINCIPAL_USERNAME, updatedItem.updatedBy)
        assertTrue(updatedItem.price isEqualTo 3.0.toBigDecimal())
        assertTrue(updatedItem.vatAmount isEqualTo 0.56.toBigDecimal())
    }

    @Test
    fun `test patchBasketItem - apply discount to ticket - should decrease ticket price`() {
        ticketDiscountService.syncCreateOrUpdateTicketDiscount(
            mapToCreateOrUpdateTicketDiscountCommand(
                TICKET_DISCOUNT_1
            )
        )

        val basket = basketService.initBasket(
            mapToInitBasketCommand(
                items = listOf(TICKET_BASKET_ITEM_REQUEST_1, TICKET_BASKET_ITEM_REQUEST_2)
            )
        )
        val createdItems = basketItemJooqFinderRepository.findAllNonDeleted().sortedBy { it.createdAt }
        val createdItem1 = createdItems[0]
        val createdTicket1 = ticketJooqFinderService.getNonDeletedById(createdItem1.ticketId!!)
        val createdItemPrice1 = createdItem1.price
        val updatedAt = createdItem1.updatedAt
        assertEquals(TEST_PRINCIPAL_USERNAME, createdItem1.updatedBy)
        assertNull(createdTicket1.ticketDiscountPrimaryId)
        assertNull(createdTicket1.ticketDiscountSecondaryId)

        val createdItem2 = createdItems[1]
        val createdTicket2 = ticketJooqFinderService.getNonDeletedById(createdItem2.ticketId!!)
        val createdItemPrice2 = createdItem2.price
        assertNull(createdTicket2.ticketDiscountPrimaryId)
        assertNull(createdTicket2.ticketDiscountSecondaryId)

        underTest.patchBasketItem(
            PatchBasketItemCommand(
                basketId = basket.id,
                basketItemId = createdItem1.id,
                primaryTicketDiscountId = Optional.of(TICKET_DISCOUNT_1.id)
            )
        )

        val updatedItem1 = basketItemJooqFinderRepository.findNonDeletedById(createdItem1.id)
        assertNotNull(updatedItem1)
        val updatedTicket1 = ticketJooqFinderService.getNonDeletedById(createdItem1.ticketId!!)
        val updatedItemPrice1 = updatedItem1.price
        assertTrue(updatedItem1.updatedAt.isAfter(updatedAt))
        assertEquals(TEST_PRINCIPAL_USERNAME, updatedItem1.updatedBy)
        assertEquals(updatedTicket1.ticketDiscountPrimaryId, TICKET_DISCOUNT_1.id)
        assertNull(updatedTicket1.ticketDiscountSecondaryId)

        assertTrue(createdItemPrice1.isGreaterThan(updatedItemPrice1))

        val notUpdatedItem2 = basketItemJooqFinderRepository.findNonDeletedById(createdItem2.id)
        val notUpdatedPrice2 = notUpdatedItem2!!.price
        val notUpdatedTicket2 = ticketJooqFinderService.getNonDeletedById(createdItem2.ticketId!!)
        assertNull(notUpdatedTicket2.ticketDiscountPrimaryId)
        assertNull(notUpdatedTicket2.ticketDiscountSecondaryId)
        assertTrue(createdItemPrice2 isEqualTo notUpdatedPrice2)
    }

    @Test
    fun `test patchBasketItem - apply fixed price discount to ticket - should decrease ticket price`() {
        ticketDiscountService.syncCreateOrUpdateTicketDiscount(
            mapToCreateOrUpdateTicketDiscountCommand(
                TICKET_DISCOUNT_4
            )
        )

        val basket = basketService.initBasket(
            mapToInitBasketCommand(
                items = listOf(TICKET_BASKET_ITEM_REQUEST_1)
            )
        )

        val createdItem = basketItemJooqFinderRepository.findAllNonDeleted().first()
        assertTrue(createdItem.price isEqualTo 16.2.toBigDecimal())
        assertTrue(createdItem.fixedPrice isEqualTo 0.0.toBigDecimal())
        assertTrue(createdItem.vatAmount isEqualTo 3.03.toBigDecimal())

        underTest.patchBasketItem(
            PatchBasketItemCommand(
                basketId = basket.id,
                basketItemId = createdItem.id,
                primaryTicketDiscountId = Optional.of(TICKET_DISCOUNT_4.id)
            )
        )

        val updatedItem = basketItemJooqFinderRepository.findAllNonDeleted().first()
        assertTrue(updatedItem.price isEqualTo 6.2.toBigDecimal())
        assertTrue(updatedItem.fixedPrice isEqualTo 2.8.toBigDecimal())
        assertTrue(updatedItem.vatAmount isEqualTo 1.16.toBigDecimal())
    }

    @Test
    fun `test patchBasketItem - apply two discounts from discount cards subsequently, primary discount first - should throw exception`() {
        // necessary DB inserts
        ticketDiscountService.syncCreateOrUpdateTicketDiscount(
            mapToCreateOrUpdateTicketDiscountCommand(
                TICKET_DISCOUNT_1
            )
        )
        ticketDiscountService.syncCreateOrUpdateTicketDiscount(
            mapToCreateOrUpdateTicketDiscountCommand(
                TICKET_DISCOUNT_3
            )
        )
        discountCardMssqlService.createOrUpdateDiscountCard(mapToCreateOrUpdateDiscountCardCommand(DISCOUNT_CARD_1))
        discountCardMssqlService.createOrUpdateDiscountCard(mapToCreateOrUpdateDiscountCardCommand(DISCOUNT_CARD_3))

        val basket = basketService.initBasket(
            mapToInitBasketCommand(items = listOf(TICKET_BASKET_ITEM_REQUEST_1))
        )
        val ticketBasketItem = basketItemJooqFinderRepository.findAllNonDeletedByBasketId(basket.id).first()

        // scan discount card 1
        val discountCard1Usage = discountCardUsageService.createDiscountCardUsage(
            CreateDiscountCardUsageCommand(
                discountCardId = DISCOUNT_CARD_1.id,
                basketId = basket.id,
                posConfigurationId = POS_CONFIGURATION_ID
            )
        )
        assertNull(discountCard1Usage.deletedAt)

        // apply ticket discount from discount card 1 on a ticket
        underTest.patchBasketItem(
            PatchBasketItemCommand(
                basketId = basket.id,
                basketItemId = ticketBasketItem.id,
                primaryTicketDiscountId = Optional.of(TICKET_DISCOUNT_1.id),
                primaryTicketDiscountCardId = DISCOUNT_CARD_1.id
            )
        )
        // update discount card 1 usage explicitly (simulates behaviour of BasketItemDiscountCardUsageEventListener)
        discountCardUsageService.updateDiscountCardUsage(
            UpdateDiscountCardUsageCommand(
                discountCardUsageId = discountCard1Usage.id,
                basketItemId = ticketBasketItem.id
            )
        )
        val updatedDiscountCard1Usage = discountCardUsageFinderService.findNonDeletedById(discountCard1Usage.id)
        assertEquals(ticketBasketItem.id, updatedDiscountCard1Usage!!.ticketBasketItemId)
        assertNull(updatedDiscountCard1Usage.productBasketItemId)
        assertNull(updatedDiscountCard1Usage.productDiscountBasketItemId)

        // scan discount card 3
        val discountCard3Usage = discountCardUsageService.createDiscountCardUsage(
            CreateDiscountCardUsageCommand(
                discountCardId = DISCOUNT_CARD_3.id,
                basketId = basket.id,
                posConfigurationId = POS_CONFIGURATION_ID
            )
        )
        assertNull(discountCard3Usage.deletedAt)

        // apply ticket discount from discount card 3 on a ticket
        assertThrows<SecondDiscountCardDiscountAppliedOnTicketBasketItemException> {
            underTest.patchBasketItem(
                PatchBasketItemCommand(
                    basketId = basket.id,
                    basketItemId = ticketBasketItem.id,
                    secondaryTicketDiscountId = Optional.of(TICKET_DISCOUNT_3.id),
                    secondaryTicketDiscountCardId = DISCOUNT_CARD_3.id
                )
            )
        }
    }

    @Test
    fun `test patchBasketItem - apply two discounts from discount cards subsequently, secondary discount first - should throw exception`() {
        // necessary DB inserts
        ticketDiscountService.syncCreateOrUpdateTicketDiscount(
            mapToCreateOrUpdateTicketDiscountCommand(
                TICKET_DISCOUNT_1
            )
        )
        ticketDiscountService.syncCreateOrUpdateTicketDiscount(
            mapToCreateOrUpdateTicketDiscountCommand(
                TICKET_DISCOUNT_3
            )
        )
        discountCardMssqlService.createOrUpdateDiscountCard(mapToCreateOrUpdateDiscountCardCommand(DISCOUNT_CARD_1))
        discountCardMssqlService.createOrUpdateDiscountCard(mapToCreateOrUpdateDiscountCardCommand(DISCOUNT_CARD_3))

        val basket = basketService.initBasket(
            mapToInitBasketCommand(items = listOf(TICKET_BASKET_ITEM_REQUEST_1))
        )
        val ticketBasketItem = basketItemJooqFinderRepository.findAllNonDeletedByBasketId(basket.id).first()

        // scan discount card 3
        val discountCard3Usage = discountCardUsageService.createDiscountCardUsage(
            CreateDiscountCardUsageCommand(
                discountCardId = DISCOUNT_CARD_3.id,
                basketId = basket.id,
                posConfigurationId = POS_CONFIGURATION_ID
            )
        )
        assertNull(discountCard3Usage.deletedAt)

        // apply ticket discount from discount card 3 on a ticket
        underTest.patchBasketItem(
            PatchBasketItemCommand(
                basketId = basket.id,
                basketItemId = ticketBasketItem.id,
                secondaryTicketDiscountId = Optional.of(TICKET_DISCOUNT_3.id),
                secondaryTicketDiscountCardId = DISCOUNT_CARD_3.id
            )
        )
        // update discount card 3 usage explicitly (simulates behaviour of BasketItemDiscountCardUsageEventListener)
        discountCardUsageService.updateDiscountCardUsage(
            UpdateDiscountCardUsageCommand(
                discountCardUsageId = discountCard3Usage.id,
                basketItemId = ticketBasketItem.id
            )
        )
        val updatedDiscountCard2Usage = discountCardUsageFinderService.findNonDeletedById(discountCard3Usage.id)
        assertEquals(ticketBasketItem.id, updatedDiscountCard2Usage!!.ticketBasketItemId)
        assertNull(updatedDiscountCard2Usage.productBasketItemId)
        assertNull(updatedDiscountCard2Usage.productDiscountBasketItemId)

        // scan discount card 1
        val discountCard1Usage = discountCardUsageService.createDiscountCardUsage(
            CreateDiscountCardUsageCommand(
                discountCardId = DISCOUNT_CARD_1.id,
                basketId = basket.id,
                posConfigurationId = POS_CONFIGURATION_ID
            )
        )
        assertNull(discountCard1Usage.deletedAt)

        // apply ticket discount from discount card 1 on a ticket
        assertThrows<SecondDiscountCardDiscountAppliedOnTicketBasketItemException> {
            underTest.patchBasketItem(
                PatchBasketItemCommand(
                    basketId = basket.id,
                    basketItemId = ticketBasketItem.id,
                    primaryTicketDiscountId = Optional.of(TICKET_DISCOUNT_1.id),
                    primaryTicketDiscountCardId = DISCOUNT_CARD_1.id
                )
            )
        }
    }

    @Test
    fun `test patchBasketItem - remove applied ticket discount - should delete discount from ticket`() {
        ticketDiscountService.syncCreateOrUpdateTicketDiscount(
            mapToCreateOrUpdateTicketDiscountCommand(
                TICKET_DISCOUNT_1
            )
        )

        val basket = basketService.initBasket(
            mapToInitBasketCommand(
                items = listOf(TICKET_BASKET_ITEM_REQUEST_1, TICKET_BASKET_ITEM_REQUEST_2)
            )
        )
        val createdItems = basketItemJooqFinderRepository.findAllNonDeleted().sortedBy { it.createdAt }
        val createdItem1 = createdItems[0]
        val createdTicket1 = ticketJooqFinderService.getNonDeletedById(createdItem1.ticketId!!)
        val createdItemPrice1 = createdItem1.price
        assertEquals(TEST_PRINCIPAL_USERNAME, createdItem1.updatedBy)
        assertNull(createdTicket1.ticketDiscountPrimaryId)
        assertNull(createdTicket1.ticketDiscountSecondaryId)

        val createdItem2 = createdItems[1]
        val createdTicket2 = ticketJooqFinderService.getNonDeletedById(createdItem2.ticketId!!)
        assertNull(createdTicket2.ticketDiscountPrimaryId)
        assertNull(createdTicket2.ticketDiscountSecondaryId)

        underTest.patchBasketItem(
            PatchBasketItemCommand(
                basketId = basket.id,
                basketItemId = createdItem1.id,
                primaryTicketDiscountId = Optional.of(TICKET_DISCOUNT_1.id)
            )
        )

        val updatedItem1 = basketItemJooqFinderRepository.findNonDeletedById(createdItem1.id)!!
        val updatedTicket1 = ticketJooqFinderService.getNonDeletedById(createdItem1.ticketId!!)
        val updatedItemPrice1 = updatedItem1.price
        assertEquals(updatedTicket1.ticketDiscountPrimaryId, TICKET_DISCOUNT_1.id)
        assertNull(updatedTicket1.ticketDiscountSecondaryId)

        assertTrue(createdItemPrice1.isGreaterThan(updatedItemPrice1))

        underTest.patchBasketItem(
            PatchBasketItemCommand(
                basketId = basket.id,
                basketItemId = createdItem1.id,
                primaryTicketDiscountId = Optional.empty()
            )
        )

        assertNotNull(basketItemJooqFinderRepository.findNonDeletedById(createdItem1.id))
        val restoredTicket1 = ticketJooqFinderService.getNonDeletedById(createdItem1.ticketId!!)
        assertNull(restoredTicket1.ticketDiscountPrimaryId)
        assertNull(restoredTicket1.ticketDiscountSecondaryId)
    }

    @Test
    fun `test patchBasketItem - remove ticket discount - should delete discount card usage and applied card discounts`() {
        val (
            basket,
            basketTotalPrice,
            updatedTicketItem1,
            updatedTicketItem1Ticket,
            createdTicketItem2,
            createdTicketItem2TicketPrice,
            basketItemProductDiscount,
            discountCardUsage1,
            discountCardUsage2,
        ) = initDiscountCardTestData()

        underTest.patchBasketItem(
            PatchBasketItemCommand(
                basketId = basket.id,
                basketItemId = createdTicketItem2.id,
                primaryTicketDiscountId = Optional.empty()
            )
        )

        assertNotNull(discountCardUsageFinderService.getById(discountCardUsage2.id).deletedAt)
        assertNotNull(basketItemJooqFinderRepository.findById(basketItemProductDiscount.id)!!.deletedAt)
        val restoredTicketItem2 = basketItemJooqFinderRepository.findNonDeletedById(createdTicketItem2.id)!!
        assertTrue(createdTicketItem2.price isEqualTo restoredTicketItem2.price)
        assertNull(ticketJooqFinderService.getNonDeletedById(restoredTicketItem2.ticketId!!).ticketDiscountPrimaryId)

        verifyDiscountCardUsageRemainsUnaffected(discountCard1Usage = discountCardUsage1)
    }

    @Test
    fun `test deleteBasketItem - ticket with applied DC discount - should delete DC usage, ticket item and all card discounts`() {
        val (
            basket,
            basketTotalPrice,
            updatedTicketItem1,
            updatedTicketItem1Ticket,
            createdTicketItem2,
            createdTicketItem2TicketPrice,
            basketItemProductDiscount,
            discountCardUsage1,
            discountCardUsage2,
        ) = initDiscountCardTestData()

        underTest.deleteBasketItem(DeleteBasketItemCommand(basket.id, createdTicketItem2.id))

        assertNotNull(discountCardUsageFinderService.getById(discountCardUsage2.id).deletedAt)
        assertNotNull(basketItemJooqFinderRepository.findById(basketItemProductDiscount.id)!!.deletedAt)
        assertNotNull(basketItemJooqFinderRepository.findById(createdTicketItem2.id)!!.deletedAt)

        verifyDiscountCardUsageRemainsUnaffected(discountCard1Usage = discountCardUsage1)
    }

    @Test
    fun `test deleteBasketItem - discount card product discount - should delete discount card usage and all applied discounts`() {
        val (
            basket,
            basketTotalPrice,
            updatedTicketItem1,
            updatedTicketItem1Ticket,
            createdTicketItem2,
            createdTicketItem2TicketPrice,
            basketItemProductDiscount,
            discountCardUsage1,
            discountCardUsage2,
        ) = initDiscountCardTestData()

        underTest.deleteBasketItem(DeleteBasketItemCommand(basket.id, basketItemProductDiscount.id))

        verifyOrder {
            applicationEventPublisherMock.publishEvent(
                TicketBasketItemDiscountRemovalInitiatedEvent(
                    basketId = basket.id,
                    basketItemId = createdTicketItem2.id,
                    primaryTicketDiscountId = Optional.empty()
                )
            )
            applicationEventPublisherMock.publishEvent(
                BasketItemModifiedEvent(basketId = basket.id)
            )
        }

        assertNotNull(basketItemJooqFinderRepository.findById(basketItemProductDiscount.id)!!.deletedAt)
        assertNotNull(discountCardUsageFinderService.getById(discountCardUsage2.id).deletedAt)
        assertNotNull(basketItemJooqFinderRepository.findById(basketItemProductDiscount.id)!!.deletedAt)
        verifyDiscountCardUsageRemainsUnaffected(discountCard1Usage = discountCardUsage1)
    }

    @Test
    fun `test deleteBasketItem - discount card isolated product - should delete discount card usage and all applied discounts`() {
        // necessary DB inserts
        ticketDiscountService.syncCreateOrUpdateTicketDiscount(
            mapToCreateOrUpdateTicketDiscountCommand(
                TICKET_DISCOUNT_1
            )
        )
        discountCardMssqlService.createOrUpdateDiscountCard(mapToCreateOrUpdateDiscountCardCommand(DISCOUNT_CARD_1))
        discountCardMssqlService.createOrUpdateDiscountCard(
            mapToCreateOrUpdateDiscountCardCommand(
                DISCOUNT_CARD_5_ISOLATED_GROUP
            )
        )

        // init basket containing one ticket
        val basket = basketService.initBasket(
            mapToInitBasketCommand(items = listOf(TICKET_BASKET_ITEM_REQUEST_1))
        )
        val initBasketItems =
            basketItemJooqFinderRepository.findAllNonDeletedByBasketId(basket.id).sortedBy { it.createdAt }
        val createdTicketItem1 = initBasketItems[0]

        // scan discount card 1
        val discountCard1Usage = discountCardUsageService.createDiscountCardUsage(
            CreateDiscountCardUsageCommand(
                discountCardId = DISCOUNT_CARD_1.id,
                basketId = basket.id,
                posConfigurationId = POS_CONFIGURATION_ID
            )
        )
        assertNull(discountCard1Usage.deletedAt)

        // apply ticket discount from discount card 1 on a ticket
        underTest.patchBasketItem(
            PatchBasketItemCommand(
                basketId = basket.id,
                basketItemId = createdTicketItem1.id,
                primaryTicketDiscountId = Optional.of(TICKET_DISCOUNT_1.id),
                primaryTicketDiscountCardId = DISCOUNT_CARD_1.id
            )
        )

        val discountCard5Usage = discountCardUsageService.createDiscountCardUsage(
            CreateDiscountCardUsageCommand(
                discountCardId = DISCOUNT_CARD_5_ISOLATED_GROUP.id,
                basketId = basket.id,
                posConfigurationId = POS_CONFIGURATION_ID
            )
        )
        assertNull(discountCard5Usage.deletedAt)

        val createdProductItem2Isolated = underTest.createBasketItem(
            mapToCreateBasketItemCommand(
                request = PRODUCT_BASKET_ITEM_ISOLATED_PRODUCT_2_REQUEST,
                basketId = basket.id
            )
        )
        val createdProductDiscountItem3Isolated = underTest.createBasketItem(
            mapToCreateBasketItemCommand(
                request = PRODUCT_BASKET_ITEM_ISOLATED_PRODUCT_DISCOUNT_2_REQUEST,
                basketId = basket.id
            )
        )

        verifyOrder {
            applicationEventPublisherMock.publishEvent(
                BasketItemWithDiscountCardUpdatedEvent(
                    discountCardId = DISCOUNT_CARD_1.id,
                    basketId = basket.id,
                    basketItemId = createdTicketItem1.id
                )
            )
            applicationEventPublisherMock.publishEvent(
                BasketItemModifiedEvent(basketId = basket.id)
            )
            applicationEventPublisherMock.publishEvent(
                BasketItemWithDiscountCardUpdatedEvent(
                    discountCardId = DISCOUNT_CARD_5_ISOLATED_GROUP.id,
                    basketId = basket.id,
                    basketItemId = createdProductItem2Isolated.id
                )
            )
            applicationEventPublisherMock.publishEvent(
                BasketItemModifiedEvent(basketId = basket.id)
            )
            applicationEventPublisherMock.publishEvent(
                BasketItemWithDiscountCardUpdatedEvent(
                    discountCardId = DISCOUNT_CARD_5_ISOLATED_GROUP.id,
                    basketId = basket.id,
                    basketItemId = createdProductDiscountItem3Isolated.id
                )
            )
            applicationEventPublisherMock.publishEvent(
                BasketItemModifiedEvent(basketId = basket.id)
            )
        }

        // update discount card usages explicitly (simulates behaviour of BasketItemDiscountCardUsageEventListener)
        discountCardUsageService.updateDiscountCardUsage(
            UpdateDiscountCardUsageCommand(
                discountCardUsageId = discountCard1Usage.id,
                basketItemId = createdTicketItem1.id
            )
        )
        discountCardUsageService.updateDiscountCardUsage(
            UpdateDiscountCardUsageCommand(
                discountCardUsageId = discountCard5Usage.id,
                basketItemId = createdProductItem2Isolated.id
            )
        )
        discountCardUsageService.updateDiscountCardUsage(
            UpdateDiscountCardUsageCommand(
                discountCardUsageId = discountCard5Usage.id,
                basketItemId = createdProductDiscountItem3Isolated.id
            )
        )
        val updatedDiscountCard1Usage = discountCardUsageFinderService.findNonDeletedById(discountCard1Usage.id)

        underTest.deleteBasketItem(DeleteBasketItemCommand(basket.id, createdProductItem2Isolated.id))

        assertNotNull(discountCardUsageFinderService.getById(discountCard5Usage.id).deletedAt)
        assertNotNull(basketItemJooqFinderRepository.findById(createdProductItem2Isolated.id)!!.deletedAt)
        assertNotNull(basketItemJooqFinderRepository.findById(createdProductDiscountItem3Isolated.id)!!.deletedAt)

        verifyDiscountCardUsageRemainsUnaffected(discountCard1Usage = updatedDiscountCard1Usage!!)
    }

    @Test
    fun `test deleteBasketItem - delete sole product - voucher with IG containing the same product remains applied`() {
        discountCardMssqlService.createOrUpdateDiscountCard(
            mapToCreateOrUpdateDiscountCardCommand(DISCOUNT_CARD_3_ISOLATED_GROUP)
        )
        val basket = basketService.initBasket(
            mapToInitBasketCommand(
                items = listOf(PRODUCT_BASKET_ITEM_REQUEST_1)
            )
        )
        val createdProductItem1 = basketItemJooqFinderRepository.findAllNonDeleted()[0]

        val discountCard3Usage = discountCardUsageService.createDiscountCardUsage(
            CreateDiscountCardUsageCommand(
                discountCardId = DISCOUNT_CARD_3_ISOLATED_GROUP.id,
                basketId = basket.id,
                posConfigurationId = POS_CONFIGURATION_ID
            )
        )
        assertNull(discountCard3Usage.deletedAt)

        val createdProductItem2Isolated = underTest.createBasketItem(
            mapToCreateBasketItemCommand(
                request = PRODUCT_BASKET_ITEM_ISOLATED_PRODUCT_1_REQUEST,
                basketId = basket.id
            )
        )
        val createdProductDiscountItem3Isolated = underTest.createBasketItem(
            mapToCreateBasketItemCommand(
                request = PRODUCT_BASKET_ITEM_ISOLATED_PRODUCT_DISCOUNT_1_REQUEST,
                basketId = basket.id
            )
        )

        verifyOrder {
            applicationEventPublisherMock.publishEvent(
                BasketItemWithDiscountCardUpdatedEvent(
                    discountCardId = DISCOUNT_CARD_3_ISOLATED_GROUP.id,
                    basketId = basket.id,
                    basketItemId = createdProductItem2Isolated.id
                )
            )
            applicationEventPublisherMock.publishEvent(
                BasketItemModifiedEvent(basketId = basket.id)
            )
            applicationEventPublisherMock.publishEvent(
                BasketItemWithDiscountCardUpdatedEvent(
                    discountCardId = DISCOUNT_CARD_3_ISOLATED_GROUP.id,
                    basketId = basket.id,
                    basketItemId = createdProductDiscountItem3Isolated.id
                )
            )
            applicationEventPublisherMock.publishEvent(
                BasketItemModifiedEvent(basketId = basket.id)
            )
        }

        // update discount card usage explicitly (simulates behaviour of BasketItemDiscountCardUsageEventListener)
        discountCardUsageService.updateDiscountCardUsage(
            UpdateDiscountCardUsageCommand(
                discountCardUsageId = discountCard3Usage.id,
                basketItemId = createdProductItem2Isolated.id
            )
        )
        discountCardUsageService.updateDiscountCardUsage(
            UpdateDiscountCardUsageCommand(
                discountCardUsageId = discountCard3Usage.id,
                basketItemId = createdProductDiscountItem3Isolated.id
            )
        )

        assertEquals(3, basketItemJooqFinderRepository.findAllNonDeleted().size)
        assertNull(createdProductItem1.productIsolatedWithId)
        assertEquals(PRODUCT_DISCOUNT_1.id, createdProductItem2Isolated.productIsolatedWithId)
        assertEquals(PRODUCT_1.id, createdProductDiscountItem3Isolated.productIsolatedWithId)
        assertEquals(createdProductItem1.productId, createdProductItem2Isolated.productId)

        // delete sole product
        underTest.deleteBasketItem(
            DeleteBasketItemCommand(basketId = basket.id, basketItemId = createdProductItem1.id)
        )

        assertNotNull(basketItemJooqFinderRepository.findById(createdProductItem1.id)!!.deletedAt)
        assertNull(discountCardUsageFinderService.getById(discountCard3Usage.id).deletedAt)
        assertNull(basketItemJooqFinderRepository.findById(createdProductItem2Isolated.id)!!.deletedAt)
        assertNull(basketItemJooqFinderRepository.findById(createdProductDiscountItem3Isolated.id)!!.deletedAt)
    }

    @Test
    fun `test deleteBasketItem - delete voucher product - discount card with same ticket discount remains applied`() {
        ticketDiscountService.syncCreateOrUpdateTicketDiscount(
            mapToCreateOrUpdateTicketDiscountCommand(
                TICKET_DISCOUNT_1
            )
        )
        discountCardMssqlService.createOrUpdateDiscountCard(mapToCreateOrUpdateDiscountCardCommand(DISCOUNT_CARD_1))
        discountCardMssqlService.createOrUpdateDiscountCard(
            mapToCreateOrUpdateDiscountCardCommand(DISCOUNT_CARD_4_PRODUCT_AND_TICKET_DISCOUNT)
        )

        val basket = basketService.initBasket(
            mapToInitBasketCommand(
                items = listOf(TICKET_BASKET_ITEM_REQUEST_1)
            )
        )
        val createdTicketItem1 = basketItemJooqFinderRepository.findAllNonDeleted()[0]

        // scan discount card 1
        val discountCard1Usage = discountCardUsageService.createDiscountCardUsage(
            CreateDiscountCardUsageCommand(
                discountCardId = DISCOUNT_CARD_1.id,
                basketId = basket.id,
                posConfigurationId = POS_CONFIGURATION_ID
            )
        )
        assertNull(discountCard1Usage.deletedAt)

        // apply ticket discount from discount card 1 on ticket
        underTest.patchBasketItem(
            PatchBasketItemCommand(
                basketId = basket.id,
                basketItemId = createdTicketItem1.id,
                primaryTicketDiscountId = Optional.of(TICKET_DISCOUNT_1.id),
                primaryTicketDiscountCardId = DISCOUNT_CARD_1.id
            )
        )

        // scan discount card 4 (voucher)
        val discountCard4Usage = discountCardUsageService.createDiscountCardUsage(
            CreateDiscountCardUsageCommand(
                discountCardId = DISCOUNT_CARD_4_PRODUCT_AND_TICKET_DISCOUNT.id,
                basketId = basket.id,
                posConfigurationId = POS_CONFIGURATION_ID
            )
        )
        assertNull(discountCard4Usage.deletedAt)

        val createdProductItem3 = underTest.createBasketItem(
            mapToCreateBasketItemCommand(
                request = PRODUCT_BASKET_ITEM_REQUEST_3,
                basketId = basket.id
            )
        )

        verifyOrder {
            applicationEventPublisherMock.publishEvent(
                BasketItemWithDiscountCardUpdatedEvent(
                    discountCardId = DISCOUNT_CARD_1.id,
                    basketId = basket.id,
                    basketItemId = createdTicketItem1.id
                )
            )
            applicationEventPublisherMock.publishEvent(
                BasketItemModifiedEvent(basketId = basket.id)
            )
            applicationEventPublisherMock.publishEvent(
                BasketItemWithDiscountCardUpdatedEvent(
                    discountCardId = DISCOUNT_CARD_4_PRODUCT_AND_TICKET_DISCOUNT.id,
                    basketId = basket.id,
                    basketItemId = createdProductItem3.id
                )
            )
            applicationEventPublisherMock.publishEvent(
                BasketItemModifiedEvent(basketId = basket.id)
            )
        }

        // update discount card usages explicitly (simulates behaviour of BasketItemDiscountCardUsageEventListener)
        discountCardUsageService.updateDiscountCardUsage(
            UpdateDiscountCardUsageCommand(
                discountCardUsageId = discountCard1Usage.id,
                basketItemId = createdTicketItem1.id
            )
        )
        discountCardUsageService.updateDiscountCardUsage(
            UpdateDiscountCardUsageCommand(
                discountCardUsageId = discountCard4Usage.id,
                basketItemId = createdProductItem3.id
            )
        )
        val updatedDiscountCard1Usage = discountCardUsageFinderService.findNonDeletedById(discountCard1Usage.id)

        val basketItems = basketItemJooqFinderRepository.findAllNonDeleted().sortedBy { it.createdAt }
        assertEquals(2, basketItems.size)

        underTest.deleteBasketItem(
            DeleteBasketItemCommand(basket.id, createdProductItem3.id)
        )

        assertNotNull(basketItemJooqFinderRepository.findById(createdProductItem3.id)!!.deletedAt)
        assertNotNull(discountCardUsageFinderService.getById(discountCard4Usage.id).deletedAt)
        verifyDiscountCardUsageRemainsUnaffected(discountCard1Usage = updatedDiscountCard1Usage!!)
    }

    @Test
    fun `test cancelBasketItems - cancelled tickets - should correctly create cancelled basket items`() {
        val basket = basketService.initBasket(mapInputItemsToInitBasketCommand(inputItems = listOf()))
        val (item1, item2) = listOf(TICKET_BASKET_ITEM_REQUEST_1, TICKET_BASKET_ITEM_REQUEST_2).map {
            underTest.createBasketItem(
                mapToCreateBasketItemCommand(
                    request = it,
                    basketId = basket.id
                )
            )
        }
        val createdBasketItems = basketItemRepository.findAllByBasketIdAndDeletedAtIsNull(basket.id)
        assertEquals(2, createdBasketItems.size)

        underTest.cancelBasketItems(
            CancelBasketItemsCommand(
                basketItemIds = setOf(item1.id, item2.id),
                printReceipt = false
            )
        )

        val allItems = basketItemRepository.findAll()
        assertEquals(4, allItems.size)

        allItems.first { it.cancelledBasketItemId == item1.id }.also {
            assertCancelledBasketItemEquals(it, item1)
        }
        allItems.first { it.id == item1.id }.also {
            assertTrue(it.isCancelled)
        }
        allItems.first { it.cancelledBasketItemId == item2.id }.also {
            assertCancelledBasketItemEquals(it, item2)
        }
        allItems.first { it.id == item2.id }.also {
            assertTrue(it.isCancelled)
        }

        verify {
            applicationEventPublisherMock.publishEvent(
                AdminCancelledBasketItemCreatedEvent(
                    cancelledBasketItemId = createdBasketItems[0].id,
                    basketId = basket.id
                )
            )
            applicationEventPublisherMock.publishEvent(
                AdminCancelledBasketItemCreatedEvent(
                    cancelledBasketItemId = createdBasketItems[1].id,
                    basketId = basket.id
                )
            )
        }
    }

    @Test
    fun `test cancelBasketItems - cancelled products - should correctly create cancelled basket items`() {
        every { applicationEventPublisherMock.publishEvent(any()) } just Runs

        val basket = basketService.initBasket(mapInputItemsToInitBasketCommand(inputItems = listOf()))
        val (item1, item2) = listOf(PRODUCT_BASKET_ITEM_REQUEST_1, PRODUCT_BASKET_ITEM_REQUEST_5).map {
            underTest.createBasketItem(
                mapToCreateBasketItemCommand(
                    request = it,
                    basketId = basket.id
                )
            )
        }

        val receiptNumber = "000000001"
        basketItemRepository.save(
            item1.apply { productReceiptNumber = receiptNumber }
        )
        basketItemRepository.save(
            item2.apply { productReceiptNumber = receiptNumber }
        )

        underTest.cancelBasketItems(
            CancelBasketItemsCommand(
                basketItemIds = setOf(item1.id, item2.id),
                printReceipt = true
            )
        )

        val allItems = basketItemRepository.findAll()
        assertEquals(4, allItems.size)

        allItems.first { it.cancelledBasketItemId == item1.id }.also {
            assertCancelledBasketItemEquals(it, item1)
        }
        allItems.first { it.id == item1.id }.also {
            assertTrue(it.isCancelled)
        }
        allItems.first { it.cancelledBasketItemId == item2.id }.also {
            assertCancelledBasketItemEquals(it, item2)
        }
        allItems.first { it.id == item2.id }.also {
            assertTrue(it.isCancelled)
        }

        verifySequence {
            applicationEventPublisherMock.publishEvent(ProductBasketItemAddedToBasketEvent(PRODUCT_1.id, 1))
            applicationEventPublisherMock.publishEvent(BasketItemModifiedEvent(basket.id))
            applicationEventPublisherMock.publishEvent(BasketItemModifiedEvent(basket.id))

            applicationEventPublisherMock.publishEvent(
                AdminCancelledBasketItemCreatedEvent(
                    cancelledBasketItemId = item1.id,
                    basketId = basket.id
                )
            )
            applicationEventPublisherMock.publishEvent(
                AdminCancelledBasketItemCreatedEvent(
                    cancelledBasketItemId = item2.id,
                    basketId = basket.id
                )
            )

            applicationEventPublisherMock.publishEvent(
                BasketItemsCancelledEvent(
                    basketIds = setOf(basket.id),
                    printReceipt = true
                )
            )
        }
    }

    @Test
    fun `test cancelBasketItems - at least one of basket items does not exist - should throw`() {
        assertThrows<BasketItemNotFoundException> {
            underTest.cancelBasketItems(
                CancelBasketItemsCommand(
                    basketItemIds = setOf(UUID.fromString("f2a31c4e-8707-482c-8160-e56a5a19bb13")),
                    printReceipt = false
                )
            )
        }
    }

    @Test
    fun `test cancelBasketItems - at least one of basket items is already cancelled - should throw`() {
        val basket = basketService.initBasket(mapInputItemsToInitBasketCommand(inputItems = listOf()))
        val createdItem = underTest.createBasketItem(
            mapToCreateBasketItemCommand(
                request = TICKET_BASKET_ITEM_REQUEST_1,
                basketId = basket.id
            )
        )

        ticketJooqFinderService.getNonDeletedById(createdItem.ticketId!!).let {
            ticketRepository.save(it.apply { receiptNumber = "00001" })
        }
        underTest.cancelBasketItems(
            CancelBasketItemsCommand(
                basketItemIds = setOf(createdItem.id),
                printReceipt = false
            )
        )

        val cancelledBasketItems = basketItemJooqFinderRepository.findAllNonDeleted().filter { it.isCancellationItem() }
        assertEquals(1, cancelledBasketItems.size)
        assertEquals(createdItem.id, cancelledBasketItems[0].cancelledBasketItemId)

        assertThrows<BasketItemIsAlreadyCancelledException> {
            underTest.cancelBasketItems(
                CancelBasketItemsCommand(
                    basketItemIds = setOf(createdItem.id),
                    printReceipt = true
                )
            )
        }

        verify {
            applicationEventPublisherMock.publishEvent(
                AdminCancelledBasketItemCreatedEvent(
                    cancelledBasketItemId = createdItem.id,
                    basketId = basket.id
                )
            )
        }
    }

    @Test
    fun `test createTicketBasketItems - valid command - should create all basket items correctly`() {
        ticketDiscountService.syncCreateOrUpdateTicketDiscount(
            mapToCreateOrUpdateTicketDiscountCommand(TICKET_DISCOUNT_1).copy(
                usageType = TicketDiscountUsageType.PRIMARY
            )
        )
        ticketDiscountService.syncCreateOrUpdateTicketDiscount(
            mapToCreateOrUpdateTicketDiscountCommand(TICKET_DISCOUNT_2).copy(
                usageType = TicketDiscountUsageType.SECONDARY
            )
        )

        val basket = basketService.initBasket(mapInputItemsToInitBasketCommand(inputItems = listOf()))
        assertTrue(reservationRepository.findAll().isEmpty())
        assertTrue(ticketPriceRepository.findAll().isEmpty())
        assertTrue(ticketRepository.findAll().isEmpty())
        assertTrue(basketItemRepository.findAll().isEmpty())

        underTest.createTicketBasketItems(
            CreateTicketBasketItemsCommand(
                basketId = basket.id,
                screeningId = SCREENING_1.id,
                priceCategoryItemNumber = PriceCategoryItemNumber.PRICE_1,
                seatIds = setOf(SEAT_1.id, SEAT_2.id),
                primaryTicketDiscountId = TICKET_DISCOUNT_1.id,
                secondaryTicketDiscountId = TICKET_DISCOUNT_2.id,
                isGroupTicket = true
            )
        )

        val createdReservationIds = reservationRepository.findAll().also {
            assertEquals(2, it.size)
            assertEquals(setOf(SEAT_1.id, SEAT_2.id), it.map { reservation -> reservation.seatId }.toSet())
            assertTrue(it.all { reservation -> reservation.screeningId == SCREENING_1.id })
            assertTrue(it.all { reservation -> reservation.state == ReservationState.RESERVED })
        }.map { it.id }.toSet()

        val createdTicketPriceIds = ticketPriceRepository.findAll().also {
            assertEquals(2, it.size)
            assertEquals(setOf(SEAT_1.id, SEAT_2.id), it.map { price -> price.seatId }.toSet())
            assertTrue(it.all { price -> price.screeningId == SCREENING_1.id })
        }.map { it.id }.toSet()

        val createdTicketIds = ticketRepository.findAll().also {
            assertEquals(2, it.size)
            assertEquals(createdReservationIds, it.map { ticket -> ticket.reservationId }.toSet())
            assertEquals(createdTicketPriceIds, it.map { ticket -> ticket.ticketPriceId }.toSet())
            assertTrue(it.all { ticket -> ticket.screeningId == SCREENING_1.id })
            assertTrue(it.all { ticket -> ticket.ticketDiscountPrimaryId == TICKET_DISCOUNT_1.id })
            assertTrue(it.all { ticket -> ticket.ticketDiscountSecondaryId == TICKET_DISCOUNT_2.id })
            assertTrue(it.all { ticket -> ticket.isGroupTicket })
        }.map { it.id }.toSet()

        basketItemRepository.findAll().also {
            assertEquals(2, it.size)
            assertEquals(createdTicketIds, it.map { basketItem -> basketItem.ticketId }.toSet())
            assertTrue(it.all { basketItem -> basketItem.basketId == basket.id })
            assertTrue(it.all { basketItem -> basketItem.type == BasketItemType.TICKET })
            assertTrue(it.all { basketItem -> basketItem.quantity == 1 })
            assertTrue(it.all { basketItem -> basketItem.price.isEqualTo(6.2.toBigDecimal()) })
        }

        verify {
            applicationEventPublisherMock.publishEvent(
                BasketItemModifiedEvent(basket.id)
            )
        }
    }

    @Test
    fun `test createTicketBasketItems - command with invalid screeningId - should throw`() {
        val basket = basketService.initBasket(mapInputItemsToInitBasketCommand(inputItems = listOf()))

        assertThrows<ScreeningForTicketNotFoundException> {
            underTest.createTicketBasketItems(
                CreateTicketBasketItemsCommand(
                    basketId = basket.id,
                    screeningId = 1.toUUID(),
                    priceCategoryItemNumber = PriceCategoryItemNumber.PRICE_1,
                    seatIds = setOf(SEAT_1.id, SEAT_2.id)
                )
            )
        }
    }

    @Test
    fun `test createTicketBasketItems - command with invalid basketId - should throw`() {
        assertThrows<BasketNotFoundException> {
            underTest.createTicketBasketItems(
                CreateTicketBasketItemsCommand(
                    basketId = 1.toUUID(),
                    screeningId = SCREENING_1.id,
                    priceCategoryItemNumber = PriceCategoryItemNumber.PRICE_1,
                    seatIds = setOf(SEAT_1.id, SEAT_2.id)
                )
            )
        }
    }

    @Test
    fun `test createTicketBasketItems - command with unmodifiable basket - should throw`() {
        val basket = basketService.initBasket(mapInputItemsToInitBasketCommand(inputItems = listOf()))
        basketRepository.save(basket.apply { state = BasketState.PAID })

        assertThrows<InvalidBasketStateException> {
            underTest.createTicketBasketItems(
                CreateTicketBasketItemsCommand(
                    basketId = basket.id,
                    screeningId = SCREENING_1.id,
                    priceCategoryItemNumber = PriceCategoryItemNumber.PRICE_1,
                    seatIds = setOf(SEAT_1.id, SEAT_2.id)
                )
            )
        }
    }

    @Test
    fun `test createTicketBasketItems - command with invalid seatId - should throw`() {
        val basket = basketService.initBasket(mapInputItemsToInitBasketCommand(inputItems = listOf()))

        assertThrows<SeatForTicketNotFoundException> {
            underTest.createTicketBasketItems(
                CreateTicketBasketItemsCommand(
                    basketId = basket.id,
                    screeningId = SCREENING_1.id,
                    priceCategoryItemNumber = PriceCategoryItemNumber.PRICE_1,
                    seatIds = setOf(SEAT_1.id, 2.toUUID())
                )
            )
        }
    }

    @Test
    fun `test createBasketItemsFromGroupReservation - valid command - should create all basket items correctly`() {
        val basket = basketService.initBasket(mapInputItemsToInitBasketCommand(inputItems = listOf()))
        val groupReservation = groupReservationRepository.save(createGroupReservation())

        reservationService.createReservationsForSeats(
            CreateReservationsForSeatsCommand(
                screeningId = SCREENING_1.id,
                seatIdToReservationStateMap = mapOf(
                    SEAT_1.id to ReservationState.GROUP_RESERVED,
                    SEAT_2.id to ReservationState.GROUP_RESERVED
                ),
                groupReservationId = groupReservation.id
            )
        )

        val reservationsIds = reservationRepository.findAll().also {
            assertEquals(2, it.size)
            assertEquals(setOf(SEAT_1.id, SEAT_2.id), it.map { reservation -> reservation.seatId }.toSet())
            assertTrue { it.all { reservation -> reservation.state == ReservationState.GROUP_RESERVED } }
        }.map { it.id }.toSet()

        assertTrue(ticketPriceRepository.findAll().isEmpty())
        assertTrue(ticketRepository.findAll().isEmpty())
        assertTrue(basketItemRepository.findAll().isEmpty())

        underTest.createBasketItemsFromGroupReservation(
            CreateBasketItemsFromGroupReservationCommand(
                basketId = basket.id,
                groupReservationId = groupReservation.id,
                priceCategoryItemNumber = PriceCategoryItemNumber.PRICE_1
            )
        )

        assertEquals(reservationRepository.findAll().map { it.id }.toSet(), reservationsIds)

        val createdTicketPriceIds = ticketPriceRepository.findAll().also {
            assertEquals(2, it.size)
            assertEquals(
                setOf(
                    SEAT_1.id,
                    SEAT_2.id
                ),
                it.map { price -> price.seatId }.toSet()
            )
            assertTrue(it.all { price -> price.screeningId == SCREENING_1.id })
        }.map { it.id }.toSet()

        val createdTicketIds = ticketRepository.findAll().also {
            assertEquals(2, it.size)
            assertEquals(reservationsIds, it.map { ticket -> ticket.reservationId }.toSet())
            assertEquals(createdTicketPriceIds, it.map { ticket -> ticket.ticketPriceId }.toSet())
            assertTrue(it.all { ticket -> ticket.screeningId == SCREENING_1.id })
        }.map { it.id }.toSet()

        basketItemRepository.findAll().also {
            assertEquals(2, it.size)
            assertEquals(createdTicketIds, it.map { basketItem -> basketItem.ticketId }.toSet())
            assertTrue(it.all { basketItem -> basketItem.basketId == basket.id })
            assertTrue(it.all { basketItem -> basketItem.type == BasketItemType.TICKET })
            assertTrue(it.all { basketItem -> basketItem.quantity == 1 })
            assertTrue(it.all { basketItem -> basketItem.price.isEqualTo(16.2.toBigDecimal()) })
        }

        verify {
            applicationEventPublisherMock.publishEvent(
                BasketItemModifiedEvent(basket.id)
            )
        }
    }

    @Test
    fun `test createBasketItemsFromGroupReservation - basket not found - should throw`() {
        reservationService.createReservationsForSeats(
            CreateReservationsForSeatsCommand(
                SCREENING_1.id,
                mapOf(SEAT_1.id to ReservationState.GROUP_RESERVED, SEAT_2.id to ReservationState.GROUP_RESERVED)
            )
        )

        assertThrows<BasketNotFoundException> {
            underTest.createBasketItemsFromGroupReservation(
                CreateBasketItemsFromGroupReservationCommand(
                    basketId = 1.toUUID(),
                    groupReservationId = 2.toUUID(),
                    priceCategoryItemNumber = PriceCategoryItemNumber.PRICE_1
                )
            )
        }
    }

    @Test
    fun `test createBasketItemsFromGroupReservation - unmodifiable basket state - should throw`() {
        val basket = basketService.initBasket(mapInputItemsToInitBasketCommand(inputItems = listOf()))
        basketRepository.save(basket.apply { state = BasketState.PAID })

        assertThrows<InvalidBasketStateException> {
            underTest.createBasketItemsFromGroupReservation(
                CreateBasketItemsFromGroupReservationCommand(
                    basketId = basket.id,
                    groupReservationId = 1.toUUID(),
                    priceCategoryItemNumber = PriceCategoryItemNumber.PRICE_1
                )
            )
        }
    }

    @Test
    fun `test createBasketItemsFromGroupReservation - group reservation not found - should throw`() {
        val basket = basketService.initBasket(mapInputItemsToInitBasketCommand(inputItems = listOf()))

        assertThrows<GroupReservationNotFoundException> {
            underTest.createBasketItemsFromGroupReservation(
                CreateBasketItemsFromGroupReservationCommand(
                    basketId = basket.id,
                    groupReservationId = 1.toUUID(),
                    priceCategoryItemNumber = PriceCategoryItemNumber.PRICE_1
                )
            )
        }
    }

    @Test
    fun `test createBasketItemsFromGroupReservation - some of reservations has invalid state - should throw`() {
        val basket = basketService.initBasket(mapInputItemsToInitBasketCommand(inputItems = listOf()))
        val groupReservation = groupReservationRepository.save(createGroupReservation())

        reservationService.createReservationsForSeats(
            CreateReservationsForSeatsCommand(
                screeningId = SCREENING_1.id,
                seatIdToReservationStateMap = mapOf(
                    SEAT_1.id to ReservationState.UNAVAILABLE,
                    SEAT_2.id to ReservationState.RESERVED
                ),
                groupReservationId = groupReservation.id
            )
        )

        assertThrows<InvalidReservationStateException> {
            underTest.createBasketItemsFromGroupReservation(
                CreateBasketItemsFromGroupReservationCommand(
                    basketId = basket.id,
                    groupReservationId = groupReservation.id,
                    priceCategoryItemNumber = PriceCategoryItemNumber.PRICE_1
                )
            )
        }
    }

    @Test
    fun `test createMssqlTicketBasketItem - single item - should add one item`() {
        ticketDiscountService.syncCreateOrUpdateTicketDiscount(
            mapToCreateOrUpdateTicketDiscountCommand(TICKET_DISCOUNT_1)
        )
        ticketDiscountService.syncCreateOrUpdateTicketDiscount(
            mapToCreateOrUpdateTicketDiscountCommand(TICKET_DISCOUNT_3)
        )
        val reservation = reservationRepository.save(EXISTING_ONLINE_RESERVATION_1)

        basketRepository.save(
            Basket(
                totalPrice = 0.toBigDecimal(),
                state = BasketState.PAID_ONLINE,
                paymentType = PaymentType.CASHLESS,
                paidAt = LocalDateTime.now(),
                variableSymbol = "999888777666"
            )
        )

        val basket = basketRepository.findAll()[0]
        val createdItem = underTest.createMssqlTicketBasketItem(
            CreateMssqlTicketBasketItemCommand(
                basketId = basket.id,
                screeningId = SCREENING_1.id,
                seatId = SEAT_1.id,
                reservationId = reservation.id,
                originalId = 100,
                priceCategoryItemNumber = PriceCategoryItemNumber.PRICE_20,
                receiptNumber = "123456789",
                ticketDiscountPrimaryId = TICKET_DISCOUNT_1.id,
                ticketDiscountSecondaryId = TICKET_DISCOUNT_3.id,
                discountCardId = DISCOUNT_CARD_1.id,
                isUsed = true,
                includes3dGlasses = false,
                branchId = BRANCH_1.id,
                basePrice = 8.toBigDecimal(),
                freeTicket = false,
                totalPrice = 10.toBigDecimal(),
                auditoriumSurcharge = 2.toBigDecimal(),
                auditoriumSurchargeType = AuditoriumSurchargeType.IMAX
            )
        )

        assertEquals(100, createdItem.originalId)
        assertEquals(basket.id, createdItem.basketId)
        assertNotNull(createdItem.ticketId)
        assertNull(createdItem.productId)
        assertEquals(BasketItemType.TICKET, createdItem.type)
        assertTrue(createdItem.price isEqualTo 10.0.toBigDecimal())
        assertEquals(1, createdItem.quantity)
        assertNull(createdItem.productReceiptNumber)
        assertNull(createdItem.productIsolatedWithId)
        assertNull(createdItem.cancelledBasketItemId)

        val createdTicket = ticketJooqFinderService.findById(createdItem.ticketId!!)
        assertNotNull(createdTicket)
        assertEquals(100, createdTicket.originalId)
        assertEquals(SCREENING_1.id, createdTicket.screeningId)
        assertEquals(reservation.id, createdTicket.reservationId)
        assertNotNull(createdTicket.ticketPriceId)
        assertEquals(TICKET_DISCOUNT_1.id, createdTicket.ticketDiscountPrimaryId)
        assertEquals(TICKET_DISCOUNT_3.id, createdTicket.ticketDiscountSecondaryId)
        assertEquals("123456789", createdTicket.receiptNumber)
        assertFalse(createdTicket.isGroupTicket)
        assertTrue(createdTicket.isUsed)
        assertFalse(createdTicket.includes3dGlasses)

        val createdTicketPrice = ticketPriceJooqFinderService.getById(createdTicket.ticketPriceId)
        assertEquals(SCREENING_1.id, createdTicketPrice.screeningId)
        assertEquals(SEAT_1.id, createdTicketPrice.seatId)
        assertEquals(PriceCategoryItemNumber.PRICE_20, createdTicketPrice.basePriceItemNumber)

        verify {
            applicationEventPublisherMock.publishEvent(
                MssqlBasketItemWithDiscountCardCreatedEvent(
                    discountCardId = DISCOUNT_CARD_1.id,
                    screeningId = SCREENING_1.id,
                    basketId = basket.id,
                    ticketBasketItemId = createdItem.id
                )
            )
        }
    }

    @Test
    fun `test createProductSalesBasketItem - should add one item`() {
        basketRepository.save(
            Basket(
                totalPrice = 0.toBigDecimal(),
                state = BasketState.PAID,
                paymentType = PaymentType.CASHLESS,
                paidAt = LocalDateTime.now()
            )
        )

        val basket = basketRepository.findAll()[0]
        val createdItem = underTest.createProductSalesBasketItem(
            CreateProductSalesBasketItemCommand(
                basketId = basket.id,
                productId = PRODUCT_1.id,
                type = BasketItemType.PRODUCT,
                price = 5.toBigDecimal(),
                quantity = 1,
                receiptNumber = "123456789",
                isCancelled = false,
                branchId = BRANCH_1.id
            )
        )

        assertNull(createdItem.originalId)
        assertEquals(basket.id, createdItem.basketId)
        assertNull(createdItem.ticketId)
        assertEquals(PRODUCT_1.id, createdItem.productId)
        assertEquals(BasketItemType.PRODUCT, createdItem.type)
        assertTrue(createdItem.price isEqualTo 5.toBigDecimal())
        assertEquals(1, createdItem.quantity)
        assertEquals("123456789", createdItem.productReceiptNumber)
        assertNull(createdItem.productIsolatedWithId)
        assertNull(createdItem.cancelledBasketItemId)
        assertFalse(createdItem.isCancelled)
    }

    @Test
    fun `test createPackagingDepositBasketItem - another packaging deposit exists - should increment its quantity`() {
        val basket = basketService.initBasket(mapInputItemsToInitBasketCommand(inputItems = listOf()))

        val command = CreatePackagingDepositBasketItemCommand(
            basketId = basket.id,
            quantity = 2,
            productId = PRODUCT_4.id
        )

        underTest.createPackagingDepositBasketItem(command)

        val packagingDepositItem = basketItemRepository.findAllByBasketId(basket.id).first { it.productId == PRODUCT_4.id }
        assertEquals(2, packagingDepositItem.quantity)

        underTest.createPackagingDepositBasketItem(command)

        val updatedPackagingDepositItem = basketItemRepository.findAllByBasketId(basket.id).first { it.productId == PRODUCT_4.id }
        assertEquals(4, updatedPackagingDepositItem.quantity)
    }

    @Test
    fun `test createPackagingDepositBasketItem - should create packaging deposit item`() {
        val basket = basketService.initBasket(mapInputItemsToInitBasketCommand(inputItems = listOf()))

        val command = CreatePackagingDepositBasketItemCommand(
            basketId = basket.id,
            quantity = 2,
            productId = PRODUCT_4.id
        )

        underTest.createPackagingDepositBasketItem(command)

        val createdItems = basketItemJooqFinderRepository.findAllNonDeletedByBasketId(basket.id)
        assertEquals(1, createdItems.size)
        val depositItem = createdItems.first()

        assertEquals(BasketItemType.PRODUCT, depositItem.type)
        assertEquals(PRODUCT_4.id, depositItem.productId)
        assertEquals(2, depositItem.quantity)
        assertTrue(PRODUCT_4.price.times(command.quantity.toBigDecimal()) isEqualTo depositItem.price)

        assertNotNull(depositItem.createdAt)
        assertNotNull(depositItem.updatedAt)

        verify {
            applicationEventPublisherMock.publishEvent(
                BasketItemModifiedEvent(basket.id)
            )
        }
    }

    @Test
    fun `test createPackagingDepositBasketItem - product is not packaging deposit - should throw`() {
        val basket = basketService.initBasket(mapInputItemsToInitBasketCommand(inputItems = listOf()))

        val command = CreatePackagingDepositBasketItemCommand(
            basketId = basket.id,
            productId = PRODUCT_3.id,
            quantity = 2
        )

        assertThrows<IllegalStateException> {
            underTest.createPackagingDepositBasketItem(command)
        }

        verify(exactly = 0) {
            applicationEventPublisherMock.publishEvent(
                BasketItemModifiedEvent(basket.id)
            )
        }
    }

    @Test
    fun `test updatePackagingDepositBasketItem - quantity gt 0 - should update packaging deposit item`() {
        val basket = basketService.initBasket(mapInputItemsToInitBasketCommand(inputItems = listOf()))

        // create deposit item
        val createCommand = CreatePackagingDepositBasketItemCommand(
            basketId = basket.id,
            quantity = 4,
            productId = PRODUCT_4.id
        )

        underTest.createPackagingDepositBasketItem(createCommand)

        val createdItems = basketItemJooqFinderRepository.findAllNonDeletedByBasketId(basket.id)
        val createdDepositItem = createdItems.first()
        assertEquals(4, createdDepositItem.quantity)
        assertTrue(PRODUCT_4.price.times(createCommand.quantity.toBigDecimal()) isEqualTo createdDepositItem.price)

        // update deposit item
        val updateCommand = UpdatePackagingDepositBasketItemCommand(
            basketId = basket.id,
            basketItemId = createdDepositItem.id,
            quantity = 2
        )
        underTest.updatePackagingDepositBasketItem(updateCommand)

        val updatedDepositItem = basketItemJooqFinderRepository.findNonDeletedById(createdDepositItem.id)!!
        assertEquals(BasketItemType.PRODUCT, updatedDepositItem.type)
        assertEquals(PRODUCT_4.id, updatedDepositItem.productId)
        assertEquals(2, updatedDepositItem.quantity)
        assertTrue(PRODUCT_4.price.times(updateCommand.quantity.toBigDecimal()) isEqualTo updatedDepositItem.price)

        verify {
            applicationEventPublisherMock.publishEvent(
                ProductBasketItemAddedToBasketEvent(
                    productId = PRODUCT_4.id,
                    quantity = createCommand.quantity
                )
            )
            applicationEventPublisherMock.publishEvent(
                BasketItemModifiedEvent(
                    basketId = basket.id
                )
            )
            applicationEventPublisherMock.publishEvent(
                BasketItemModifiedEvent(
                    basketId = basket.id
                )
            )
        }
    }

    @Test
    fun `test updatePackagingDepositBasketItem - quantity=0 - should delete packaging deposit item`() {
        val basket = basketService.initBasket(mapInputItemsToInitBasketCommand(inputItems = listOf()))

        // create deposit item
        val createCommand = CreatePackagingDepositBasketItemCommand(
            basketId = basket.id,
            quantity = 4,
            productId = PRODUCT_4.id
        )

        underTest.createPackagingDepositBasketItem(createCommand)

        val createdItems = basketItemJooqFinderRepository.findAllNonDeletedByBasketId(basket.id)
        val createdDepositItem = createdItems.first()
        assertEquals(4, createdDepositItem.quantity)
        assertTrue(PRODUCT_4.price.times(createCommand.quantity.toBigDecimal()) isEqualTo createdDepositItem.price)

        // update deposit item
        val updateCommand = UpdatePackagingDepositBasketItemCommand(
            basketId = basket.id,
            basketItemId = createdDepositItem.id,
            quantity = 0
        )
        underTest.updatePackagingDepositBasketItem(updateCommand)

        val deletedDepositItem = basketItemJooqFinderRepository.findById(createdDepositItem.id)!!
        assertNotNull(deletedDepositItem.deletedAt)
        assertEquals(BasketItemType.PRODUCT, deletedDepositItem.type)
        assertEquals(PRODUCT_4.id, deletedDepositItem.productId)
        assertEquals(4, deletedDepositItem.quantity)
        assertTrue(PRODUCT_4.price.times(createCommand.quantity.toBigDecimal()) isEqualTo deletedDepositItem.price)

        verifySequence {
            applicationEventPublisherMock.publishEvent(
                ProductBasketItemAddedToBasketEvent(
                    productId = PRODUCT_4.id,
                    quantity = createCommand.quantity
                )
            )
            applicationEventPublisherMock.publishEvent(
                BasketItemModifiedEvent(
                    basketId = basket.id
                )
            )
            applicationEventPublisherMock.publishEvent(
                BasketItemModifiedEvent(
                    basketId = basket.id
                )
            )
        }
    }

    @Test
    fun `test patchBasketItem - product item with package deposit - should publish event for package deposit basket item update`() {
        // prepare testing data
        val basket = basketService.initBasket(mapInputItemsToInitBasketCommand(listOf()))
        val command = mapToCreateBasketItemCommand(PRODUCT_BASKET_ITEM_REQUEST_4, basket.id)
        underTest.createBasketItem(command)

        val createdItems = basketItemJooqFinderRepository.findAll()
        assertEquals(1, createdItems.size)
        val createdItem = createdItems.first()

        createdItem.let {
            val expectedPrice = PRODUCT_5.price.times(command.quantity.toBigDecimal())
            assertBasketItemEquals(PRODUCT_BASKET_ITEM_REQUEST_4, expectedPrice, it)
        }
        // create packaging deposit basket item explicitly
        val createPackagingDepositCommand = CreatePackagingDepositBasketItemCommand(
            basketId = basket.id,
            quantity = 4,
            productId = PRODUCT_4.id
        )
        underTest.createPackagingDepositBasketItem(createPackagingDepositCommand)

        val basketItems = basketItemJooqFinderRepository.findAllNonDeletedByBasketId(basket.id)
        val createdDepositItem = basketItems.first { it.productId == PRODUCT_4.id }.let {
            assertEquals(createPackagingDepositCommand.quantity, it.quantity)
            assertTrue(PRODUCT_4.price.times(createPackagingDepositCommand.quantity.toBigDecimal()) isEqualTo it.price)
            it
        }

        // update basket item with packaging deposit and verify published event
        val patchCommand = PatchBasketItemCommand(
            basketId = basket.id,
            basketItemId = createdItem.id,
            quantity = 3
        )
        underTest.patchBasketItem(patchCommand)

        basketItemJooqFinderRepository.findNonDeletedById(createdItem.id)!!.let {
            assertEquals(BasketItemType.PRODUCT, it.type)
            assertTrue(PRODUCT_5.price.times(patchCommand.quantity!!.toBigDecimal()) isEqualTo it.price)
            assertEquals(patchCommand.quantity, it.quantity)
            assertEquals(PRODUCT_5.id, it.productId)
        }

        verify {
            applicationEventPublisherMock.publishEvent(
                ProductWithPackagingDepositFoundEvent(
                    packagingDepositProductId = PRODUCT_4.id,
                    quantity = 4,
                    basketId = basket.id
                )
            )
            applicationEventPublisherMock.publishEvent(
                BasketItemWithPackagingDepositUpdatedEvent(
                    basketId = basket.id,
                    packagingDepositItemId = createdDepositItem.id,
                    packagingDepositItemQuantity = 6
                )
            )
        }
    }

    @Test
    fun `test deleteBasketItem - product item with package deposit - should publish event for package deposit basket item update`() {
        // prepare testing data
        val basket = basketService.initBasket(mapInputItemsToInitBasketCommand(listOf()))
        val createItemCommand = mapToCreateBasketItemCommand(PRODUCT_BASKET_ITEM_REQUEST_4, basket.id)
        underTest.createBasketItem(createItemCommand)

        val createdItems = basketItemJooqFinderRepository.findAll()
        assertEquals(1, createdItems.size)
        val createdItem = createdItems.first()

        createdItem.let {
            val expectedPrice = PRODUCT_5.price.times(createItemCommand.quantity.toBigDecimal())
            assertBasketItemEquals(PRODUCT_BASKET_ITEM_REQUEST_4, expectedPrice, it)
        }
        // create packaging deposit basket item explicitly
        val createPackagingDepositCommand = CreatePackagingDepositBasketItemCommand(
            basketId = basket.id,
            quantity = 4,
            productId = PRODUCT_4.id
        )
        underTest.createPackagingDepositBasketItem(createPackagingDepositCommand)

        val basketItems = basketItemJooqFinderRepository.findAllNonDeletedByBasketId(basket.id)
        val createdDepositItem = basketItems.first { it.productId == PRODUCT_4.id }.let {
            assertEquals(createPackagingDepositCommand.quantity, it.quantity)
            assertTrue(PRODUCT_4.price.times(createPackagingDepositCommand.quantity.toBigDecimal()) isEqualTo it.price)
            it
        }

        // delete basket item with packaging deposit and verify published event
        underTest.deleteBasketItem(DeleteBasketItemCommand(basketId = basket.id, basketItemId = createdItem.id))

        assertNotNull(basketItemJooqFinderRepository.findById(createdItem.id)!!.deletedAt)

        verify {
            applicationEventPublisherMock.publishEvent(
                ProductWithPackagingDepositFoundEvent(
                    packagingDepositProductId = PRODUCT_4.id,
                    quantity = 4,
                    basketId = basket.id
                )
            )
            applicationEventPublisherMock.publishEvent(
                BasketItemWithPackagingDepositUpdatedEvent(
                    basketId = basket.id,
                    packagingDepositItemId = createdDepositItem.id,
                    packagingDepositItemQuantity = 0
                )
            )
        }
    }

    @Test
    fun `test create3dGlassesBasketItem - regular 3D screening - should create 3D glasses basket item`() {
        val basket = basketService.initBasket(mapInputItemsToInitBasketCommand(listOf()))
        val command = Create3dGlassesBasketItemCommand(
            basketId = basket.id,
            quantity = 2,
            screeningId = SCREENING_1.id
        )

        underTest.create3dGlassesBasketItem(command)

        val createdItems = basketItemJooqFinderRepository.findAllNonDeletedByBasketId(basket.id)
        assertEquals(1, createdItems.size)

        val createdItem = createdItems.first()
        assertEquals(BasketItemType.PRODUCT, createdItem.type)
        assertEquals(GLASSES_3D_PRODUCT.id, createdItem.productId)
        assertEquals(2, createdItem.quantity)
        assertTrue(GLASSES_3D_PRODUCT.price.times(command.quantity.toBigDecimal()) isEqualTo createdItem.price)
        assertEquals(TEST_PRINCIPAL_USERNAME, createdItem.createdBy)
        assertEquals(TEST_PRINCIPAL_USERNAME, createdItem.updatedBy)
        assertNotNull(createdItem.createdAt)
        assertNotNull(createdItem.updatedAt)
        assertNull(createdItem.deletedAt)
    }

    @Test
    fun `test create3dGlassesBasketItem - IMAX screening - should create IMAX glasses basket item`() {
        val basket = basketService.initBasket(mapInputItemsToInitBasketCommand(listOf()))
        val command = Create3dGlassesBasketItemCommand(
            basketId = basket.id,
            quantity = 1,
            screeningId = SCREENING_2.id
        )

        underTest.create3dGlassesBasketItem(command)

        val createdItems = basketItemJooqFinderRepository.findAllNonDeletedByBasketId(basket.id)
        assertEquals(1, createdItems.size)

        val createdItem = createdItems.first()
        assertEquals(BasketItemType.PRODUCT, createdItem.type)
        assertEquals(GLASSES_IMAX_PRODUCT.id, createdItem.productId)
        assertEquals(1, createdItem.quantity)
        assertTrue(GLASSES_IMAX_PRODUCT.price.times(command.quantity.toBigDecimal()) isEqualTo createdItem.price)
        assertEquals(TEST_PRINCIPAL_USERNAME, createdItem.createdBy)
        assertEquals(TEST_PRINCIPAL_USERNAME, createdItem.updatedBy)
        assertNotNull(createdItem.createdAt)
        assertNotNull(createdItem.updatedAt)
        assertNull(createdItem.deletedAt)
    }

    @Test
    fun `test updateTicketBasketItemsIncludes3dGlasses - no glasses products in basket - shouldn't mark tickets with includes3dGlasses`() {
        val basket = basketService.initBasket(mapInputItemsToInitBasketCommand(listOf()))

        underTest.createBasketItem(mapToCreateBasketItemCommand(TICKET_BASKET_ITEM_REQUEST_1, basket.id))
        underTest.createBasketItem(mapToCreateBasketItemCommand(TICKET_BASKET_ITEM_REQUEST_2, basket.id))

        ticketJooqFinderService.findAll().forEach {
            assertFalse(it.includes3dGlasses)
        }

        underTest.updateTicketBasketItemsIncludes3dGlasses(
            UpdateTicketBasketItemsIncludes3dGlasses(
                basketId = basket.id
            )
        )

        ticketJooqFinderService.findAll().forEach {
            assertFalse(it.includes3dGlasses)
        }
    }

    @Test
    fun `test updateTicketBasketItemsIncludes3dGlasses - some glasses products in basket - should mark some tickets with includes3dGlasses`() {
        val basket = basketService.initBasket(mapInputItemsToInitBasketCommand(listOf()))

        val basketItem1 = underTest.createBasketItem(mapToCreateBasketItemCommand(TICKET_BASKET_ITEM_REQUEST_1, basket.id))
        val basketItem2 = underTest.createBasketItem(mapToCreateBasketItemCommand(TICKET_BASKET_ITEM_REQUEST_2, basket.id))
        underTest.createBasketItem(
            mapToCreateBasketItemCommand(
                basketId = basket.id,
                input = CreateBasketItemInput(
                    type = BasketItemType.PRODUCT,
                    quantity = 1,
                    product = CreateProductInput(
                        productId = GLASSES_3D_PRODUCT.id
                    )
                )
            )
        )

        ticketJooqFinderService.findAll().forEach {
            assertFalse(it.includes3dGlasses)
        }

        underTest.updateTicketBasketItemsIncludes3dGlasses(
            UpdateTicketBasketItemsIncludes3dGlasses(
                basketId = basket.id
            )
        )

        assertTrue(ticketJooqFinderService.getById(basketItem1.ticketId!!).includes3dGlasses)
        assertFalse(ticketJooqFinderService.getById(basketItem2.ticketId!!).includes3dGlasses)
    }

    @Test
    fun `test updateTicketBasketItemsIncludes3dGlasses - all tickets have glasses products - should mark all tickets with includes3dGlasses`() {
        val basket = basketService.initBasket(mapInputItemsToInitBasketCommand(listOf()))

        underTest.createBasketItem(mapToCreateBasketItemCommand(TICKET_BASKET_ITEM_REQUEST_1, basket.id))
        underTest.createBasketItem(mapToCreateBasketItemCommand(TICKET_BASKET_ITEM_REQUEST_2, basket.id))
        underTest.createBasketItem(
            mapToCreateBasketItemCommand(
                basketId = basket.id,
                input = CreateBasketItemInput(
                    type = BasketItemType.PRODUCT,
                    quantity = 2,
                    product = CreateProductInput(
                        productId = GLASSES_IMAX_PRODUCT.id
                    )
                )
            )
        )

        ticketJooqFinderService.findAll().forEach {
            assertFalse(it.includes3dGlasses)
        }

        underTest.updateTicketBasketItemsIncludes3dGlasses(
            UpdateTicketBasketItemsIncludes3dGlasses(
                basketId = basket.id
            )
        )

        ticketJooqFinderService.findAll().forEach {
            assertTrue(it.includes3dGlasses)
        }
    }

    @Test
    fun `test messagingCancelBasketItem - ticket and product item exist - should correctly create cancelled basket items`() {
        val command = MessagingCreateBasketCommand(
            id = UUID.randomUUID(),
            totalPrice = 23.5.toBigDecimal(),
            state = BasketState.PAID,
            paidAt = LocalDateTime.now(),
            paymentType = PaymentType.CASHLESS,
            variableSymbol = null,
            branchCode = BRANCH_1.code,
            items = listOf(
                MessagingCreateBasketCommand.BasketItem(
                    id = 1.toUUID(),
                    type = BasketItemType.TICKET,
                    price = 7.125.toBigDecimal(),
                    vatAmount = 1.33.toBigDecimal(),
                    quantity = 1,
                    isCancelled = false,
                    discountCardCode = null,
                    ticket = MessagingCreateBasketCommand.BasketTicketItemInput(
                        id = 11.toUUID(),
                        screeningId = SCREENING_1.id,
                        auditoriumOriginalCode = AUDITORIUM_1.originalCode,
                        seatId = SEAT_1.id,
                        ticketReceiptNumber = "100000001",
                        ticketDiscountPrimaryCode = null,
                        ticketDiscountSecondaryCode = TICKET_DISCOUNT_2.code,
                        isGroupTicket = false,
                        isUsed = false,
                        includes3dGlasses = false,
                        ticketPrice = BasketTicketItemTicketPriceInput(
                            basePrice = 14.0.toBigDecimal(),
                            basePriceItemNumber = PriceCategoryItemNumber.PRICE_1,
                            basePriceBeforeDiscount = 18.0.toBigDecimal(),
                            seatSurcharge = 3.0.toBigDecimal(),
                            seatSurchargeType = SeatSurchargeType.PREMIUM_PLUS,
                            auditoriumSurcharge = 4.0.toBigDecimal(),
                            auditoriumSurchargeType = AuditoriumSurchargeType.IMAX,
                            seatServiceFee = 1.5.toBigDecimal(),
                            seatServiceFeeType = SeatServiceFeeType.PREMIUM_PLUS,
                            auditoriumServiceFee = 2.5.toBigDecimal(),
                            auditoriumServiceFeeType = AuditoriumServiceFeeType.IMAX,
                            serviceFeeGeneral = 0.75.toBigDecimal(),
                            primaryDiscountPercentage = 15,
                            primaryDiscountAmount = 2.7.toBigDecimal(),
                            secondaryDiscountPercentage = 10,
                            secondaryDiscountAmount = 1.8.toBigDecimal(),
                            freeTicket = false,
                            zeroFees = false,
                            totalPrice = 21.25.toBigDecimal()
                        )
                    ),
                    product = null
                ),
                MessagingCreateBasketCommand.BasketItem(
                    id = 2.toUUID(),
                    type = BasketItemType.PRODUCT,
                    price = 10.toBigDecimal(),
                    vatAmount = 1.60.toBigDecimal(),
                    quantity = 2,
                    isCancelled = false,
                    discountCardCode = null,
                    ticket = null,
                    product = MessagingCreateBasketCommand.BasketProductItemInput(
                        productCode = PRODUCT_1.code,
                        productReceiptNumber = "123456789"
                    )
                )
            )
        )

        basketService.messagingCreateBasket(command)

        val basket = basketJpaFinderService.findById(command.id)
        assertNotNull(basket)
        val createdItems = basketItemRepository.findAllByBasketId(basket.id)
        assertEquals(2, createdItems.size)
        val item1 = createdItems[0]
        val item2 = createdItems[1]
        assertEquals(1.toUUID(), item1.id)
        assertEquals(2.toUUID(), item2.id)

        underTest.messagingCreateCancelledBasketItem(
            MessagingCreateCancelledBasketItemCommand(
                cancelledBasketItemId = createdItems[0].id,
                basketId = basket.id
            )
        )
        underTest.messagingCreateCancelledBasketItem(
            MessagingCreateCancelledBasketItemCommand(
                cancelledBasketItemId = createdItems[1].id,
                basketId = basket.id
            )
        )

        val allItems = basketItemRepository.findAll()
        assertEquals(4, allItems.size)

        allItems.first { it.cancelledBasketItemId == item1.id }.also {
            assertCancelledBasketItemEquals(it, item1)
        }
        allItems.first { it.id == item1.id }.also {
            assertTrue(it.isCancelled)
        }
        allItems.first { it.cancelledBasketItemId == item2.id }.also {
            assertCancelledBasketItemEquals(it, item2)
        }
        allItems.first { it.id == item2.id }.also {
            assertTrue(it.isCancelled)
        }
    }

    private fun assertBasketItemEquals(
        expected: CreateBasketItemRequest,
        expectedPrice: BigDecimal,
        actual: BasketItem,
    ) {
        assertEquals(expected.type, actual.type)
        assertTrue(expectedPrice isEqualTo actual.price)
        assertEquals(expected.quantity, actual.quantity)
        assertEquals(expected.product?.productId, actual.productId)
        assertNotNull(actual.createdAt)
        assertNotNull(actual.updatedAt)
        assertNotNull(actual.createdBy)
        assertNotNull(actual.updatedBy)
    }

    private fun assertCancelledBasketItemEquals(expected: BasketItem, actual: BasketItem) {
        assertNotNull(expected.id)
        assertNotEquals(expected.id, actual.id)
        assertTrue(expected.createdAt.isAfter(actual.createdAt))
        assertEquals(expected.originalId, actual.originalId)
        assertEquals(expected.basketId, actual.basketId)
        assertEquals(expected.ticketId, actual.ticketId)
        assertEquals(expected.productId, actual.productId)
        assertEquals(expected.type, actual.type)
        assertTrue(expected.price isEqualTo actual.price)
        assertFalse(actual.isCancelled)
        assertEquals(expected.quantity, actual.quantity)
        assertEquals(expected.productReceiptNumber, actual.productReceiptNumber)
    }

    private fun initDiscountCardTestData(): DiscountCardDeactivationData {
        // necessary DB inserts
        ticketDiscountService.syncCreateOrUpdateTicketDiscount(
            mapToCreateOrUpdateTicketDiscountCommand(
                TICKET_DISCOUNT_1
            )
        )
        ticketDiscountService.syncCreateOrUpdateTicketDiscount(
            mapToCreateOrUpdateTicketDiscountCommand(
                TICKET_DISCOUNT_2
            )
        )
        discountCardMssqlService.createOrUpdateDiscountCard(mapToCreateOrUpdateDiscountCardCommand(DISCOUNT_CARD_1))
        discountCardMssqlService.createOrUpdateDiscountCard(mapToCreateOrUpdateDiscountCardCommand(DISCOUNT_CARD_2))

        // init basket containing one product and two tickets
        val basket = basketService.initBasket(
            mapToInitBasketCommand(
                items = listOf(
                    PRODUCT_BASKET_ITEM_REQUEST_1,
                    TICKET_BASKET_ITEM_REQUEST_1,
                    TICKET_BASKET_ITEM_REQUEST_2
                )
            )
        )
        val initBasketItems =
            basketItemJooqFinderRepository.findAllNonDeletedByBasketId(basket.id).sortedBy { it.createdAt }
        val createdTicketItem1 = initBasketItems[1]
        val createdTicketItem2 = initBasketItems[2]
        val createdTicket2 = ticketJooqFinderService.getNonDeletedById(createdTicketItem2.ticketId!!)
        val createdTicketItem2TicketPrice = ticketPriceJooqFinderService.getById(createdTicket2.ticketPriceId)

        // scan discount card 1
        val discountCard1Usage = discountCardUsageService.createDiscountCardUsage(
            CreateDiscountCardUsageCommand(
                discountCardId = DISCOUNT_CARD_1.id,
                basketId = basket.id,
                posConfigurationId = POS_CONFIGURATION_ID
            )
        )
        assertNull(discountCard1Usage.deletedAt)
        // apply ticket discount from discount card 1 on first ticket
        underTest.patchBasketItem(
            PatchBasketItemCommand(
                basketId = basket.id,
                basketItemId = createdTicketItem1.id,
                primaryTicketDiscountId = Optional.of(TICKET_DISCOUNT_1.id),
                primaryTicketDiscountCardId = DISCOUNT_CARD_1.id
            )
        )
        val updatedTicketItem1 = basketItemJooqFinderRepository.findNonDeletedById(createdTicketItem1.id)!!
        val updatedTicketItem1Ticket = ticketJooqFinderService.getNonDeletedById(updatedTicketItem1.ticketId!!)
        val basketTotalPrice = basketJpaFinderService.getNonDeletedById(basket.id).totalPrice

        // scan discount card 2
        val discountCard2Usage = discountCardUsageService.createDiscountCardUsage(
            CreateDiscountCardUsageCommand(
                discountCardId = DISCOUNT_CARD_2.id,
                basketId = basket.id,
                posConfigurationId = POS_CONFIGURATION_ID
            )
        )
        assertNull(discountCard2Usage.deletedAt)
        // add product discount from discount card 2
        val basketItemProductDiscount = underTest.createBasketItem(
            mapToCreateBasketItemCommand(
                request = CreateBasketItemRequest(
                    type = BasketItemType.PRODUCT_DISCOUNT,
                    quantity = 1,
                    product = CreateProductRequest(
                        productId = PRODUCT_DISCOUNT_2.id,
                        discountCardId = DISCOUNT_CARD_2.id
                    )
                ),
                basketId = basket.id
            )
        )

        // apply ticket discount from discount card 2 on second ticket
        underTest.patchBasketItem(
            PatchBasketItemCommand(
                basketId = basket.id,
                basketItemId = createdTicketItem2.id,
                primaryTicketDiscountId = Optional.of(TICKET_DISCOUNT_2.id),
                primaryTicketDiscountCardId = DISCOUNT_CARD_2.id
            )
        )
        verifyOrder {
            applicationEventPublisherMock.publishEvent(
                BasketItemWithDiscountCardUpdatedEvent(
                    discountCardId = DISCOUNT_CARD_1.id,
                    basketId = basket.id,
                    basketItemId = createdTicketItem1.id
                )
            )
            applicationEventPublisherMock.publishEvent(
                BasketItemModifiedEvent(basketId = basket.id)
            )
            applicationEventPublisherMock.publishEvent(
                BasketItemWithDiscountCardUpdatedEvent(
                    discountCardId = DISCOUNT_CARD_2.id,
                    basketId = basket.id,
                    basketItemId = basketItemProductDiscount.id
                )
            )
            applicationEventPublisherMock.publishEvent(
                BasketItemModifiedEvent(basketId = basket.id)
            )
            applicationEventPublisherMock.publishEvent(
                BasketItemWithDiscountCardUpdatedEvent(
                    discountCardId = DISCOUNT_CARD_2.id,
                    basketId = basket.id,
                    basketItemId = createdTicketItem2.id
                )
            )
            applicationEventPublisherMock.publishEvent(
                BasketItemModifiedEvent(basketId = basket.id)
            )
        }

        // update discount card usages explicitly (simulates behaviour of BasketItemDiscountCardUsageEventListener)
        discountCardUsageService.updateDiscountCardUsage(
            UpdateDiscountCardUsageCommand(
                discountCardUsageId = discountCard1Usage.id,
                basketItemId = createdTicketItem1.id
            )
        )
        discountCardUsageService.updateDiscountCardUsage(
            UpdateDiscountCardUsageCommand(
                discountCardUsageId = discountCard2Usage.id,
                basketItemId = basketItemProductDiscount.id
            )
        )
        discountCardUsageService.updateDiscountCardUsage(
            UpdateDiscountCardUsageCommand(
                discountCardUsageId = discountCard2Usage.id,
                basketItemId = createdTicketItem2.id
            )
        )
        val updatedDiscountCard1Usage = discountCardUsageFinderService.findNonDeletedById(discountCard1Usage.id)

        val updatedTicketItem2 = basketItemJooqFinderRepository.findNonDeletedById(createdTicketItem2.id)!!
        val updatedTicket2 = ticketJooqFinderService.getNonDeletedById(createdTicketItem2.ticketId!!)
        assertEquals(updatedTicket2.ticketDiscountPrimaryId, TICKET_DISCOUNT_2.id)
        assertNull(updatedTicket2.ticketDiscountSecondaryId)
        assertTrue(createdTicketItem1.price.isGreaterThan(updatedTicketItem2.price))

        return DiscountCardDeactivationData(
            basket = basket,
            basketTotalPrice = basketTotalPrice,
            updatedTicketItem1 = updatedTicketItem1,
            updatedTicketItem1Ticket = updatedTicketItem1Ticket,
            createdTicketItem2 = createdTicketItem2,
            createdTicketItem2TicketPrice = createdTicketItem2TicketPrice,
            basketItemProductDiscount = basketItemProductDiscount,
            discountCardUsage1 = updatedDiscountCard1Usage!!,
            discountCardUsage2 = discountCard2Usage
        )
    }

    private fun verifyDiscountCardUsageRemainsUnaffected(discountCard1Usage: DiscountCardUsage) {
        val discountCard1Usage2 = discountCardUsageFinderService.getNonDeletedById(discountCard1Usage.id)
        assertNull(discountCard1Usage2.deletedAt)
        assertEquals(discountCard1Usage.ticketBasketItemId, discountCard1Usage2.ticketBasketItemId)
        assertEquals(discountCard1Usage.productBasketItemId, discountCard1Usage2.productBasketItemId)
        assertEquals(discountCard1Usage.productDiscountBasketItemId, discountCard1Usage2.productDiscountBasketItemId)
    }

    companion object {
        @JvmStatic
        fun invalidInitBasketItemCommandProvider(): Stream<Arguments> {
            // basketId can be arbitrary when testing constraint violation
            val command = mapToCreateBasketItemCommand(PRODUCT_BASKET_ITEM_REQUEST_1, UUID.randomUUID())
            return Stream.of(
                Arguments.of(command.copy(quantity = -3)),
                Arguments.of(command.copy(quantity = 0))
            )
        }
    }

    data class DiscountCardDeactivationData(
        val basket: Basket,
        val basketTotalPrice: BigDecimal,
        val updatedTicketItem1: BasketItem,
        val updatedTicketItem1Ticket: Ticket,
        val createdTicketItem2: BasketItem,
        val createdTicketItem2TicketPrice: TicketPrice,
        val basketItemProductDiscount: BasketItem,
        val discountCardUsage1: DiscountCardUsage,
        val discountCardUsage2: DiscountCardUsage,
    )
}

private val POS_CONFIGURATION_ID = UUID.fromString("895c03dc-1ef6-4008-9dab-3c9d6b82b92d")
private val DISTRIBUTOR_1 = createDistributor()
private val AUDITORIUM_1 = createAuditorium(
    originalId = 1,
    code = "A",
    title = "SÁLA A - CINEMAX BRATISLAVA"
)
private val AUDITORIUM_2 = createAuditorium(
    originalId = 2,
    code = "IMAX",
    title = "IMAX - CINEMAX BRATISLAVA"
)
private val AUDITORIUM_LAYOUT_1 = createAuditoriumLayout(
    originalId = 1,
    auditoriumId = AUDITORIUM_1.id
)
private val AUDITORIUM_LAYOUT_2 = createAuditoriumLayout(
    originalId = 2,
    auditoriumId = AUDITORIUM_2.id,
    code = "01"
)

private val MOVIE_1 = createMovie(
    originalId = 1,
    title = "Star Wars: Episode I – The Phantom Menace",
    releaseYear = 2001,
    distributorId = DISTRIBUTOR_1.id
)
private val PRICE_CATEGORY_1 = createPriceCategory(
    originalId = 1,
    title = "ARTMAX PO 17",
    active = true
)
private val PRICE_CATEGORY_ITEM_1 = createPriceCategoryItem(
    priceCategoryId = PRICE_CATEGORY_1.id,
    number = PriceCategoryItemNumber.PRICE_1,
    title = "Dospely",
    price = 10.toBigDecimal()
)
private val PRICE_CATEGORY_ITEM_2 = createPriceCategoryItem(
    priceCategoryId = PRICE_CATEGORY_1.id,
    number = PriceCategoryItemNumber.PRICE_20,
    title = "Online Dospely",
    price = 10.toBigDecimal()
)
private val SCREENING_1 = createScreening(
    originalId = 1,
    auditoriumId = AUDITORIUM_1.id,
    auditoriumLayoutId = AUDITORIUM_LAYOUT_1.id,
    movieId = MOVIE_1.id,
    priceCategoryId = PRICE_CATEGORY_1.id
)
private val SCREENING_2 = createScreening(
    originalId = 2,
    auditoriumId = AUDITORIUM_2.id,
    auditoriumLayoutId = AUDITORIUM_LAYOUT_2.id,
    movieId = MOVIE_1.id,
    priceCategoryId = PRICE_CATEGORY_1.id
)
private val SCREENING_FEE_1 = createScreeningFee(
    originalScreeningId = 1,
    screeningId = SCREENING_1.id
)
private val SEAT_1 = createSeat(
    originalId = 1,
    auditoriumId = AUDITORIUM_1.id,
    auditoriumLayoutId = AUDITORIUM_LAYOUT_1.id,
    row = "A",
    number = "6",
    positionTop = 62
)
private val SEAT_2 = createSeat(
    originalId = 2,
    auditoriumId = AUDITORIUM_1.id,
    auditoriumLayoutId = AUDITORIUM_LAYOUT_1.id,
    row = "5",
    number = "7",
    positionLeft = 25,
    positionTop = 40
)
private val PRODUCT_CATEGORY_1 = createProductCategory(
    originalId = 1,
    code = "A1",
    title = "Snacks - doplnky"
)
private val PRODUCT_CATEGORY_2 = createProductCategory(
    originalId = 2,
    code = "A2",
    title = "Slevy",
    type = ProductCategoryType.DISCOUNT
)
private val PRODUCT_CATEGORY_3 = createProductCategory(
    originalId = 3,
    code = "03",
    title = "Záloha",
    type = ProductCategoryType.PRODUCT,
    taxRate = STANDARD_TAX_RATE
)
private val GLASSES_PRODUCT_CATEGORY = createProductCategory(
    originalId = 36,
    code = "36",
    title = "3D Glasses",
    type = ProductCategoryType.PRODUCT,
    taxRate = STANDARD_TAX_RATE
)
private val PRODUCT_DISCOUNT_1 = createProduct(
    originalId = 10,
    code = "03X",
    productCategoryId = PRODUCT_CATEGORY_2.id,
    title = "Slevova karta -10%",
    type = ProductType.PRODUCT,
    price = 0L.toBigDecimal(),
    discountPercentage = 10
)
private val PRODUCT_DISCOUNT_2 = createProduct(
    originalId = 11,
    code = "04X",
    productCategoryId = PRODUCT_CATEGORY_2.id,
    title = "FILM karta -20%",
    type = ProductType.PRODUCT,
    price = 0L.toBigDecimal(),
    discountPercentage = 20
)
private val PRODUCT_1 = createProduct(
    originalId = 1,
    code = "01",
    productCategoryId = PRODUCT_CATEGORY_1.id,
    title = "Popcorn XXL",
    price = 10.toBigDecimal()
)
private val PRODUCT_2 = createProduct(
    originalId = 2,
    code = "02",
    productCategoryId = PRODUCT_CATEGORY_1.id,
    title = "Coca Cola 0,33L",
    price = 1.toBigDecimal()
)
private val PRODUCT_3 = createProduct(
    originalId = 3,
    code = "03",
    productCategoryId = PRODUCT_CATEGORY_1.id,
    title = "Popcorn Klasik 2.4l",
    price = 5.0.toBigDecimal()
)
private val PRODUCT_4 = createProduct(
    id = 4.toUUID(),
    originalId = 4,
    code = "04",
    productCategoryId = PRODUCT_CATEGORY_3.id,
    title = "Záloha na obal",
    type = ProductType.ADDITIONAL_SALE,
    price = 0.15.toBigDecimal(),
    isPackagingDeposit = true
)
private val PRODUCT_5 = createProduct(
    id = 5.toUUID(),
    originalId = 5,
    code = "05",
    productCategoryId = PRODUCT_CATEGORY_1.id,
    title = "Double CocaCola PET 0.3 dcl",
    type = ProductType.PRODUCT,
    price = 2.20.toBigDecimal(),
    isPackagingDeposit = false
)
private val GLASSES_3D_PRODUCT = createProduct(
    id = 6.toUUID(),
    originalId = 6,
    code = "88888",
    productCategoryId = GLASSES_PRODUCT_CATEGORY.id,
    title = "3D okuliare",
    type = ProductType.PRODUCT,
    price = 1.30.toBigDecimal(),
    active = true
)
private val GLASSES_IMAX_PRODUCT = createProduct(
    id = 7.toUUID(),
    originalId = 7,
    code = "90002",
    productCategoryId = GLASSES_PRODUCT_CATEGORY.id,
    title = "IMAX Okuliare",
    type = ProductType.PRODUCT,
    price = 1.80.toBigDecimal(),
    active = true
)
private val PRODUCT_BASKET_ITEM_REQUEST_1 = CreateBasketItemRequest(
    type = BasketItemType.PRODUCT,
    quantity = 1,
    product = CreateProductRequest(
        productId = PRODUCT_1.id
    )
)
private val PRODUCT_BASKET_ITEM_REQUEST_2 = CreateBasketItemRequest(
    type = BasketItemType.PRODUCT,
    quantity = 2,
    product = CreateProductRequest(
        productId = PRODUCT_2.id
    )
)
private val PRODUCT_BASKET_ITEM_REQUEST_4 = CreateBasketItemRequest(
    type = BasketItemType.PRODUCT,
    quantity = 2,
    product = CreateProductRequest(
        productId = PRODUCT_5.id
    )
)
private val PRODUCT_BASKET_ITEM_REQUEST_5 = CreateBasketItemRequest(
    type = BasketItemType.PRODUCT_DISCOUNT,
    quantity = 1,
    product = CreateProductRequest(
        productId = PRODUCT_DISCOUNT_1.id
    )
)
private val TICKET_BASKET_ITEM_REQUEST_1 = CreateBasketItemRequest(
    type = BasketItemType.TICKET,
    quantity = 1,
    ticket = CreateTicketRequest(
        ticketPrice = CreateTicketPriceRequest(
            // proper one is calculated via TicketPriceService and set in relevant test cases
            priceCategoryItemNumber = PriceCategoryItemNumber.PRICE_1
        ),
        reservation = CreateReservationRequest(
            seatId = SEAT_1.id
        ),
        screeningId = SCREENING_1.id
    )
)
private val TICKET_BASKET_ITEM_REQUEST_2 = CreateBasketItemRequest(
    type = BasketItemType.TICKET,
    quantity = 1,
    ticket = CreateTicketRequest(
        ticketPrice = CreateTicketPriceRequest(
            // proper one is calculated via TicketPriceService and set in relevant test cases
            priceCategoryItemNumber = PriceCategoryItemNumber.PRICE_1
        ),
        reservation = CreateReservationRequest(
            seatId = SEAT_2.id
        ),
        screeningId = SCREENING_1.id
    )
)
private val PRODUCT_COMPONENT_CATEGORY_1 = createProductComponentCategory()
private val PRODUCT_COMPONENT_1 = createProductComponent(
    originalId = 1,
    code = "01",
    title = "Kukurica",
    unit = ProductComponentUnit.KG,
    stockQuantity = 180.0.toBigDecimal(),
    productComponentCategoryId = PRODUCT_COMPONENT_CATEGORY_1.id
)
private val PRODUCT_COMPONENT_2 = createProductComponent(
    originalId = 2,
    code = "02",
    title = "Soľ",
    unit = ProductComponentUnit.KG,
    stockQuantity = 230.8.toBigDecimal(),
    productComponentCategoryId = PRODUCT_COMPONENT_CATEGORY_1.id
)
private val PRODUCT_COMPONENT_3 = createProductComponent(
    originalId = 3,
    code = "03",
    title = "Tuk",
    unit = ProductComponentUnit.KG,
    stockQuantity = 750.55.toBigDecimal(),
    productComponentCategoryId = PRODUCT_COMPONENT_CATEGORY_1.id
)
private val PRODUCT_COMPONENT_4 = createProductComponent(
    originalId = 4,
    code = "04",
    title = "Coca Cola 0.33l",
    unit = ProductComponentUnit.KS,
    stockQuantity = 125.toBigDecimal(),
    productComponentCategoryId = PRODUCT_COMPONENT_CATEGORY_1.id
)
private val PRODUCT_COMPONENT_5 = createProductComponent(
    originalId = 5,
    code = "05",
    title = "3D okuliare",
    unit = ProductComponentUnit.KS,
    stockQuantity = 100.toBigDecimal(),
    productComponentCategoryId = PRODUCT_COMPONENT_CATEGORY_1.id
)
private val PRODUCT_COMPONENT_6 = createProductComponent(
    originalId = 6,
    code = "06",
    title = "IMAX okuliare",
    unit = ProductComponentUnit.KS,
    stockQuantity = 50.toBigDecimal(),
    productComponentCategoryId = PRODUCT_COMPONENT_CATEGORY_1.id
)
private val PRODUCT_COMPOSITION_1 = createProductComposition(
    originalId = 1,
    productId = PRODUCT_1.id,
    productComponentId = PRODUCT_COMPONENT_1.id,
    amount = 0.25.toBigDecimal()
)
private val PRODUCT_COMPOSITION_2 = createProductComposition(
    originalId = 2,
    productId = PRODUCT_1.id,
    productComponentId = PRODUCT_COMPONENT_2.id,
    amount = 0.00838.toBigDecimal()
)
private val PRODUCT_COMPOSITION_3 = createProductComposition(
    originalId = 3,
    productId = PRODUCT_1.id,
    productComponentId = PRODUCT_COMPONENT_3.id,
    amount = 0.06798.toBigDecimal()
)
private val PRODUCT_COMPOSITION_4 = createProductComposition(
    originalId = 3,
    productId = PRODUCT_2.id,
    productComponentId = PRODUCT_COMPONENT_4.id,
    amount = 1.toBigDecimal()
)
private val PRODUCT_COMPOSITION_5 = createProductComposition(
    originalId = 5,
    productId = PRODUCT_3.id,
    productComponentId = PRODUCT_COMPONENT_1.id,
    amount = 0.25.toBigDecimal()
)
private val PRODUCT_COMPOSITION_6 = createProductComposition(
    originalId = 6,
    productId = PRODUCT_3.id,
    productComponentId = PRODUCT_COMPONENT_2.id,
    amount = 0.00838.toBigDecimal()
)
private val PRODUCT_COMPOSITION_7 = createProductComposition(
    originalId = 7,
    productId = PRODUCT_3.id,
    productComponentId = PRODUCT_COMPONENT_3.id,
    amount = 0.06798.toBigDecimal()
)
private val PRODUCT_COMPOSITION_8 = createProductComposition(
    originalId = 8,
    productId = PRODUCT_5.id,
    productComponentId = PRODUCT_COMPONENT_4.id,
    productInProductId = null,
    amount = 2.toBigDecimal()
)
private val PRODUCT_COMPOSITION_9 = createProductComposition(
    originalId = 9,
    productId = PRODUCT_5.id,
    productComponentId = null,
    productInProductId = PRODUCT_4.id,
    amount = 2.toBigDecimal()
)
private val PRODUCT_COMPOSITION_10 = createProductComposition(
    originalId = 10,
    productId = GLASSES_3D_PRODUCT.id,
    productComponentId = PRODUCT_COMPONENT_5.id,
    productInProductId = null,
    amount = 1.toBigDecimal()
)
private val PRODUCT_COMPOSITION_11 = createProductComposition(
    originalId = 11,
    productId = GLASSES_IMAX_PRODUCT.id,
    productComponentId = PRODUCT_COMPONENT_6.id,
    productInProductId = null,
    amount = 1.toBigDecimal()
)
private val TICKET_DISCOUNT_1 = createTicketDiscount(
    originalId = 1,
    code = "01X",
    title = "Sleva 15%",
    type = TicketDiscountType.PERCENTAGE,
    percentage = 15
)
private val TICKET_DISCOUNT_2 = createTicketDiscount(
    originalId = 2,
    code = "02X",
    title = "Sleva 30%",
    type = TicketDiscountType.PERCENTAGE,
    percentage = 30
)
private val TICKET_DISCOUNT_3 = createTicketDiscount(
    originalId = 3,
    code = "03X",
    title = "Sleva 30%",
    type = TicketDiscountType.PERCENTAGE,
    percentage = 30,
    usageType = TicketDiscountUsageType.SECONDARY
)
private val TICKET_DISCOUNT_4 = createTicketDiscount(
    originalId = 4,
    code = "AA",
    percentage = 100,
    amount = 2.80.toBigDecimal(),
    type = TicketDiscountType.ABSOLUTE,
    usageType = TicketDiscountUsageType.PRIMARY
)
private val DISCOUNT_CARD_1 = createDiscountCard(
    originalId = 123435,
    ticketDiscountId = TICKET_DISCOUNT_1.id,
    productDiscountId = PRODUCT_DISCOUNT_1.id,
    title = "VIP karta",
    code = "67900000"
)
private val DISCOUNT_CARD_2 = createDiscountCard(
    originalId = 456789,
    ticketDiscountId = TICKET_DISCOUNT_2.id,
    productDiscountId = PRODUCT_DISCOUNT_2.id,
    title = "FILM karta",
    code = "67900089"
)
private val DISCOUNT_CARD_3 = createDiscountCard(
    originalId = 4567866,
    ticketDiscountId = TICKET_DISCOUNT_3.id,
    title = "FILM karta",
    code = "67900090"
)
private val DISCOUNT_CARD_3_ISOLATED_GROUP = createDiscountCard(
    originalId = 789123,
    productDiscountId = PRODUCT_DISCOUNT_1.id,
    productId = PRODUCT_1.id,
    title = "Voucher Popcorn XXL 10% sleva",
    code = "679077590",
    type = DiscountCardType.VOUCHER
)
private val DISCOUNT_CARD_4_PRODUCT_AND_TICKET_DISCOUNT = createDiscountCard(
    originalId = 142536,
    ticketDiscountId = TICKET_DISCOUNT_1.id,
    productId = PRODUCT_3.id,
    title = "Voucher Popcorn Klasik vstupenky 15%",
    code = "67802345",
    type = DiscountCardType.VOUCHER
)
private val DISCOUNT_CARD_5_ISOLATED_GROUP = createDiscountCard(
    originalId = 78919923,
    productDiscountId = PRODUCT_DISCOUNT_2.id,
    productId = PRODUCT_2.id,
    title = "Coca Cola 20% sleva",
    code = "6790775807",
    type = DiscountCardType.VOUCHER
)
private val POS_CONFIGURATION_COMMAND = CreateOrUpdatePosConfigurationCommand(
    id = POS_CONFIGURATION_ID,
    macAddress = "AA-BB-CC-DD-EE",
    title = "dummyPOS",
    receiptsDirectory = "/dummy/directory",
    ticketSalesEnabled = false,
    productModes = setOf(ProductMode.STANDARD),
    tablesType = TablesType.NO_TABLES,
    seatsEnabled = false
)
private val PRODUCT_BASKET_ITEM_ISOLATED_PRODUCT_1_REQUEST = CreateBasketItemRequest(
    type = BasketItemType.PRODUCT,
    quantity = 1,
    product = CreateProductRequest(
        productId = PRODUCT_1.id,
        productIsolatedWithId = PRODUCT_DISCOUNT_1.id,
        discountCardId = DISCOUNT_CARD_3_ISOLATED_GROUP.id
    )
)
private val PRODUCT_BASKET_ITEM_ISOLATED_PRODUCT_DISCOUNT_1_REQUEST = CreateBasketItemRequest(
    type = BasketItemType.PRODUCT_DISCOUNT,
    quantity = 1,
    product = CreateProductRequest(
        productId = PRODUCT_DISCOUNT_1.id,
        productIsolatedWithId = PRODUCT_1.id,
        discountCardId = DISCOUNT_CARD_3_ISOLATED_GROUP.id
    )
)
private val PRODUCT_BASKET_ITEM_ISOLATED_PRODUCT_2_REQUEST = CreateBasketItemRequest(
    type = BasketItemType.PRODUCT,
    quantity = 1,
    product = CreateProductRequest(
        productId = PRODUCT_2.id,
        productIsolatedWithId = PRODUCT_DISCOUNT_2.id,
        discountCardId = DISCOUNT_CARD_5_ISOLATED_GROUP.id
    )
)
private val PRODUCT_BASKET_ITEM_ISOLATED_PRODUCT_DISCOUNT_2_REQUEST = CreateBasketItemRequest(
    type = BasketItemType.PRODUCT_DISCOUNT,
    quantity = 1,
    product = CreateProductRequest(
        productId = PRODUCT_DISCOUNT_2.id,
        productIsolatedWithId = PRODUCT_2.id,
        discountCardId = DISCOUNT_CARD_5_ISOLATED_GROUP.id
    )
)
private val PRODUCT_BASKET_ITEM_REQUEST_3 = CreateBasketItemRequest(
    type = BasketItemType.PRODUCT,
    quantity = 1,
    product = CreateProductRequest(
        productId = PRODUCT_3.id,
        discountCardId = DISCOUNT_CARD_4_PRODUCT_AND_TICKET_DISCOUNT.id
    )
)
private val EXISTING_ONLINE_RESERVATION_1 = createReservation(
    originalId = 1,
    screeningId = SCREENING_1.id,
    seatId = SEAT_1.id,
    state = ReservationState.UNAVAILABLE
)
private val BRANCH_1 = createBranch()
