package com.cleevio.cinemax.api.module.screening.service

import com.cleevio.cinemax.api.DatabaseCleanup
import com.cleevio.cinemax.api.IntegrationDataTestHelper
import com.cleevio.cinemax.api.IntegrationTest
import com.cleevio.cinemax.api.module.basket.constant.BasketState
import com.cleevio.cinemax.api.module.basket.event.BasketItemModifiedEvent
import com.cleevio.cinemax.api.module.basket.exception.InvalidBasketStateException
import com.cleevio.cinemax.api.module.basket.service.BasketRepository
import com.cleevio.cinemax.api.module.basketitem.constant.BasketItemType
import com.cleevio.cinemax.api.module.basketitem.service.BasketItemRepository
import com.cleevio.cinemax.api.module.groupreservation.constant.GroupReservationType
import com.cleevio.cinemax.api.module.groupreservation.service.GroupReservationRepository
import com.cleevio.cinemax.api.module.reservation.constant.ReservationState
import com.cleevio.cinemax.api.module.reservation.service.ReservationRepository
import com.cleevio.cinemax.api.module.screening.service.command.WebDeleteGroupReservationCommand
import com.cleevio.cinemax.api.module.ticket.service.TicketRepository
import com.cleevio.cinemax.api.util.toUUID
import io.kotest.assertions.throwables.shouldThrow
import io.kotest.matchers.shouldBe
import io.mockk.Runs
import io.mockk.every
import io.mockk.just
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import java.math.BigDecimal

class WebDeleteGroupReservationServiceIT @Autowired constructor(
    private val underTest: WebDeleteGroupReservationService,
    private val groupReservationRepository: GroupReservationRepository,
    private val reservationRepository: ReservationRepository,
    private val basketRepository: BasketRepository,
    private val basketItemRepository: BasketItemRepository,
    private val ticketRepository: TicketRepository,
    private val integrationDataTestHelper: IntegrationDataTestHelper,
) : IntegrationTest(DatabaseCleanup.AFTER_EACH) {

    @BeforeEach
    fun setUp() {
        every { applicationEventPublisherMock.publishEvent(any()) } just Runs
        every { applicationEventPublisherMock.publishEvent(any<BasketItemModifiedEvent>()) } just Runs
    }

    @Test
    fun `test delete group reservation - should delete group reservation, reservations, basket and tickets`() {
        val auditoriumLayout = integrationDataTestHelper.getAuditoriumLayout(
            auditoriumId = integrationDataTestHelper.getAuditorium(
                id = 1.toUUID()
            )
        )

        val seat1 = integrationDataTestHelper.getSeat(
            id = 10.toUUID(),
            originalId = 1,
            row = "1",
            number = "1",
            auditoriumId = auditoriumLayout.auditoriumId,
            auditoriumLayoutId = auditoriumLayout.id
        )
        val seat2 = integrationDataTestHelper.getSeat(
            id = 21.toUUID(),
            originalId = 21,
            row = "2",
            number = "2",
            auditoriumId = auditoriumLayout.auditoriumId,
            auditoriumLayoutId = auditoriumLayout.id
        )

        val screening = integrationDataTestHelper.getScreening(
            id = 100.toUUID(),
            originalId = 100,
            auditoriumId = auditoriumLayout.auditoriumId,
            auditoriumLayoutId = auditoriumLayout.id,
            priceCategoryId = integrationDataTestHelper.getPriceCategory(
                id = 1000.toUUID()
            ).id
        )
        integrationDataTestHelper.getScreeningFee(
            screeningId = screening.id,
            originalScreeningId = screening.originalId
        )
        integrationDataTestHelper.getPriceCategoryItem(
            priceCategoryId = screening.priceCategoryId
        )

        val groupReservation = integrationDataTestHelper.getGroupReservation(
            id = 1000.toUUID(),
            type = GroupReservationType.ONLINE
        )
        val reservation1 = integrationDataTestHelper.getReservation(
            id = 100.toUUID(),
            groupReservationId = groupReservation.id,
            screeningId = screening.id,
            seatId = seat1.id,
            state = ReservationState.ONLINE_RESERVED
        )

        val ticket1 = integrationDataTestHelper.getTicket(
            id = 2222.toUUID(),
            originalId = 2222,
            screeningId = screening.id,
            reservationId = reservation1.id,
            ticketPriceId = integrationDataTestHelper.getTicketPrice(
                seatId = seat1.id,
                screeningId = screening.id,
                totalPrice = BigDecimal.TEN
            ).id
        )

        val reservation2 = integrationDataTestHelper.getReservation(
            id = 200.toUUID(),
            groupReservationId = groupReservation.id,
            screeningId = screening.id,
            seatId = seat2.id,
            state = ReservationState.ONLINE_RESERVED
        )

        val ticket2 = integrationDataTestHelper.getTicket(
            id = 2222.toUUID(),
            originalId = 2222,
            screeningId = screening.id,
            reservationId = reservation2.id,
            ticketPriceId = integrationDataTestHelper.getTicketPrice(
                seatId = seat2.id,
                screeningId = screening.id,
                totalPrice = BigDecimal.TEN
            ).id
        )

        val basket = integrationDataTestHelper.getBasket(state = BasketState.OPEN_ONLINE)
        integrationDataTestHelper.getBasketItem(
            basketId = basket.id,
            ticketId = ticket1.id,
            type = BasketItemType.TICKET,
            price = BigDecimal.TEN
        )
        integrationDataTestHelper.getBasketItem(
            basketId = basket.id,
            ticketId = ticket2.id,
            type = BasketItemType.TICKET,
            price = BigDecimal.TEN
        )

        underTest(
            WebDeleteGroupReservationCommand(
                groupReservationId = groupReservation.id,
                basketId = basket.id
            )
        )

        val updatedGroupReservations = groupReservationRepository.findAll()
        updatedGroupReservations.size shouldBe 1
        updatedGroupReservations[0].isDeleted() shouldBe true

        val baskets = basketRepository.findAll()
        baskets.forEach { basket ->
            basket.isDeleted() shouldBe true
        }

        val basketItems = basketItemRepository.findAll()
        basketItems.forEach { basketItem ->
            basketItem.isDeleted() shouldBe true
        }

        val tickets = ticketRepository.findAll()
        tickets.forEach { ticket ->
            ticket.isDeleted() shouldBe true
        }

        val reservations = reservationRepository.findAll()
        reservations.forEach { reservation ->
            reservation.isDeleted() shouldBe true
        }
    }

    @Test
    fun `test delete group reservation - should not delete group reservation when basket already paid`() {
        val auditoriumLayout = integrationDataTestHelper.getAuditoriumLayout(
            auditoriumId = integrationDataTestHelper.getAuditorium(
                id = 1.toUUID()
            )
        )

        val seat1 = integrationDataTestHelper.getSeat(
            id = 10.toUUID(),
            originalId = 1,
            row = "1",
            number = "1",
            auditoriumId = auditoriumLayout.auditoriumId,
            auditoriumLayoutId = auditoriumLayout.id
        )
        val screening = integrationDataTestHelper.getScreening(
            id = 100.toUUID(),
            originalId = 100,
            auditoriumId = auditoriumLayout.auditoriumId,
            auditoriumLayoutId = auditoriumLayout.id,
            priceCategoryId = integrationDataTestHelper.getPriceCategory(
                id = 1000.toUUID()
            ).id
        )
        integrationDataTestHelper.getScreeningFee(
            screeningId = screening.id,
            originalScreeningId = screening.originalId
        )
        integrationDataTestHelper.getPriceCategoryItem(
            priceCategoryId = screening.priceCategoryId
        )

        val groupReservation = integrationDataTestHelper.getGroupReservation(
            id = 1000.toUUID(),
            type = GroupReservationType.ONLINE
        )
        val reservation1 = integrationDataTestHelper.getReservation(
            id = 100.toUUID(),
            groupReservationId = groupReservation.id,
            screeningId = screening.id,
            seatId = seat1.id,
            state = ReservationState.ONLINE_RESERVED
        )

        val ticket1 = integrationDataTestHelper.getTicket(
            id = 2222.toUUID(),
            originalId = 2222,
            screeningId = screening.id,
            reservationId = reservation1.id,
            ticketPriceId = integrationDataTestHelper.getTicketPrice(
                seatId = seat1.id,
                screeningId = screening.id,
                totalPrice = BigDecimal.TEN
            ).id
        )

        val basket = integrationDataTestHelper.getBasket(state = BasketState.PAID_ONLINE)
        integrationDataTestHelper.getBasketItem(
            basketId = basket.id,
            ticketId = ticket1.id,
            type = BasketItemType.TICKET,
            price = BigDecimal.TEN
        )

        shouldThrow<InvalidBasketStateException> {
            underTest(
                WebDeleteGroupReservationCommand(
                    groupReservationId = groupReservation.id,
                    basketId = basket.id
                )
            )
        }
    }
}
