package com.cleevio.cinemax.api.module.file.controller

import com.cleevio.cinemax.api.ControllerTest
import com.cleevio.cinemax.api.common.constant.ApiVersion
import com.cleevio.cinemax.api.common.util.jsonContent
import com.cleevio.cinemax.api.module.employee.constant.EmployeeRole
import com.cleevio.cinemax.api.module.file.constant.FileType
import com.cleevio.cinemax.api.module.file.service.command.UploadFileCommand
import com.cleevio.cinemax.api.util.mockAccessToken
import com.cleevio.cinemax.api.util.toUUID
import io.mockk.every
import io.mockk.verify
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest
import org.springframework.http.HttpHeaders
import org.springframework.http.MediaType
import org.springframework.mock.web.MockMultipartFile
import org.springframework.test.web.servlet.MockMvc
import org.springframework.test.web.servlet.multipart
import java.nio.charset.Charset

@WebMvcTest(AdminFileController::class)
class FileControllerTest @Autowired constructor(
    private val mvc: MockMvc,
) : ControllerTest() {

    @Test
    fun `test uploadFile, should serialize and deserialize correctly`() {
        val fileId = 1.toUUID()
        val imageMockFile = MockMultipartFile(
            "file",
            "image.png",
            MediaType.IMAGE_PNG_VALUE,
            "test-image-content".toByteArray(Charset.forName("UTF-8"))
        )

        every { fileService.uploadFile(any<UploadFileCommand>()) } returns fileId

        mvc.multipart("$BASE_FILES_PATH/upload/PRODUCT_IMAGE") {
            header(HttpHeaders.ACCEPT, ApiVersion.VERSION_1_JSON)
            header(HttpHeaders.AUTHORIZATION, mockAccessToken(role = EmployeeRole.MANAGER))
            file(imageMockFile)
        }.andExpect {
            status { isOk() }
            content { contentType(ApiVersion.VERSION_1_JSON) }
            jsonContent(
                """
                {
                  "id": "$fileId"
                }
            """
            )
        }

        verify {
            fileService.uploadFile(
                UploadFileCommand(
                    multipartFile = imageMockFile,
                    type = FileType.PRODUCT_IMAGE
                )
            )
        }
    }
}

private const val BASE_FILES_PATH = "/manager-app/files"
