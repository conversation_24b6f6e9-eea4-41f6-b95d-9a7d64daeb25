package com.cleevio.cinemax.api.module.screening.service

import com.cleevio.cinemax.api.DatabaseCleanup
import com.cleevio.cinemax.api.INTEGRATION_TEST_DATE_TIME
import com.cleevio.cinemax.api.IntegrationTest
import com.cleevio.cinemax.api.module.auditorium.service.AuditoriumService
import com.cleevio.cinemax.api.module.auditoriumlayout.service.AuditoriumLayoutRepository
import com.cleevio.cinemax.api.module.distributor.service.DistributorService
import com.cleevio.cinemax.api.module.movie.service.MovieService
import com.cleevio.cinemax.api.module.pricecategory.service.PriceCategoryService
import com.cleevio.cinemax.api.module.pricecategoryitem.constant.PriceCategoryItemNumber
import com.cleevio.cinemax.api.module.pricecategoryitem.service.PriceCategoryItemRepository
import com.cleevio.cinemax.api.module.screening.constant.ScreeningState
import com.cleevio.cinemax.api.module.screening.exception.ScreeningNotFoundException
import com.cleevio.cinemax.api.module.screening.exception.ScreeningWebPriceNotFoundException
import com.cleevio.cinemax.api.module.screening.service.query.WebGetAuditoriumQuery
import com.cleevio.cinemax.api.module.screeningfee.service.ScreeningFeeRepository
import com.cleevio.cinemax.api.module.seat.constant.SeatObjectType
import com.cleevio.cinemax.api.module.seat.constant.SeatType
import com.cleevio.cinemax.api.module.seat.service.SeatRepository
import com.cleevio.cinemax.api.util.createAuditorium
import com.cleevio.cinemax.api.util.createAuditoriumLayout
import com.cleevio.cinemax.api.util.createDistributor
import com.cleevio.cinemax.api.util.createMovie
import com.cleevio.cinemax.api.util.createPriceCategory
import com.cleevio.cinemax.api.util.createPriceCategoryItem
import com.cleevio.cinemax.api.util.createScreening
import com.cleevio.cinemax.api.util.createScreeningFee
import com.cleevio.cinemax.api.util.createSeat
import com.cleevio.cinemax.api.util.mapToCreateOrUpdateAuditoriumCommand
import com.cleevio.cinemax.api.util.mapToCreateOrUpdateDistributorCommand
import com.cleevio.cinemax.api.util.mapToCreateOrUpdateMovieCommand
import com.cleevio.cinemax.api.util.mapToSyncCreateOrUpdatePriceCategoryCommand
import io.kotest.assertions.throwables.shouldThrow
import io.kotest.matchers.comparables.shouldBeEqualComparingTo
import io.kotest.matchers.shouldBe
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import java.time.LocalTime

class WebGetAuditoriumQueryServiceIT @Autowired constructor(
    private val underTest: WebGetAuditoriumQueryService,
    private val screeningRepository: ScreeningRepository,
    private val auditoriumService: AuditoriumService,
    private val auditoriumLayoutRepository: AuditoriumLayoutRepository,
    private val priceCategoryService: PriceCategoryService,
    private val priceCategoryItemRepository: PriceCategoryItemRepository,
    private val screeningFeeRepository: ScreeningFeeRepository,
    private val seatRepository: SeatRepository,
    private val movieService: MovieService,
    private val distributorService: DistributorService,
) : IntegrationTest(DatabaseCleanup.AFTER_EACH) {

    @Test
    fun `test WebGetAuditoriumQuery - screening not found - throws ScreeningNotFoundException`() {
        shouldThrow<ScreeningNotFoundException> {
            underTest(WebGetAuditoriumQuery(screeningOriginalId = 999))
        }
    }

    @Test
    fun `test WebGetAuditoriumQuery - price category item not found - throws ScreeningWebPriceNotFoundException`() {
        val priceCategoryNoItem = createPriceCategory(
            originalId = 2,
            title = "ARTMAX NO Price",
            active = true
        )
        priceCategoryService.syncCreateOrUpdatePriceCategory(
            mapToSyncCreateOrUpdatePriceCategoryCommand(priceCategoryNoItem)
        )

        val screening = createScreening(
            originalId = 11,
            auditoriumId = AUDITORIUM_REGULAR.id,
            auditoriumLayoutId = AUDITORIUM_LAYOUT_REGULAR_1.id,
            priceCategoryId = priceCategoryNoItem.id,
            movieId = MOVIE_1.id,
            stopped = false,
            cancelled = false,
            publishOnline = true,
            state = ScreeningState.PUBLISHED,
            date = INTEGRATION_TEST_DATE_TIME.toLocalDate(),
            time = LOCAL_TIME
        )
        screeningRepository.save(screening)

        shouldThrow<ScreeningWebPriceNotFoundException> {
            underTest(WebGetAuditoriumQuery(screeningOriginalId = 11))
        }
    }

    @Test
    fun `test WebGetAuditoriumQuery - successful retrieval - returns correct response`() {
        val query = WebGetAuditoriumQuery(screeningOriginalId = 1)

        val result = underTest(query)

        result.originalId shouldBe 1L
        result.priceCategoryItems.size shouldBe 2
        result.priceCategoryItems[0].number shouldBe PriceCategoryItemNumber.PRICE_20
        result.priceCategoryItems[0].price shouldBeEqualComparingTo 10.00.toBigDecimal()
        result.priceCategoryItems[1].number shouldBe PriceCategoryItemNumber.PRICE_19
        result.priceCategoryItems[1].price shouldBeEqualComparingTo 8.00.toBigDecimal()

        result.fees.surchargeVip shouldBeEqualComparingTo 2.00.toBigDecimal()
        result.fees.surchargePremium shouldBeEqualComparingTo 1.00.toBigDecimal()
        result.fees.surchargeDBox shouldBeEqualComparingTo 5.00.toBigDecimal()
        result.fees.surchargeImax shouldBeEqualComparingTo 0.toBigDecimal()
        result.fees.surchargeUltraX shouldBeEqualComparingTo 0.toBigDecimal()
        result.fees.serviceFeeVip shouldBeEqualComparingTo 2.50.toBigDecimal()
        result.fees.serviceFeePremium shouldBeEqualComparingTo 1.75.toBigDecimal()
        result.fees.serviceFeeGeneral shouldBeEqualComparingTo 0.50.toBigDecimal()
        result.fees.serviceFeeImax shouldBeEqualComparingTo 0.toBigDecimal()
        result.fees.serviceFeeUltraX shouldBeEqualComparingTo 0.toBigDecimal()

        result.auditorium.originalCode shouldBe AUDITORIUM_REGULAR.originalCode
        result.auditorium.isImax shouldBe false

        result.auditorium.seats.size shouldBe 6

        val regularSeat = result.auditorium.seats.find { it.type == SeatType.REGULAR && it.objectType == SeatObjectType.SEAT }!!
        regularSeat.originalId shouldBe SEAT_REGULAR.originalId
        regularSeat.row shouldBe "A"
        regularSeat.number shouldBe "1"
        regularSeat.positionLeft shouldBe 10
        regularSeat.positionTop shouldBe 20
        regularSeat.price?.shouldBeEqualComparingTo(10.50.toBigDecimal())

        val vipSeat = result.auditorium.seats.find { it.type == SeatType.VIP && it.objectType == SeatObjectType.SEAT }!!
        vipSeat.originalId shouldBe SEAT_VIP.originalId
        vipSeat.row shouldBe "B"
        vipSeat.number shouldBe "2"
        vipSeat.positionLeft shouldBe 30
        vipSeat.positionTop shouldBe 40
        vipSeat.price?.shouldBeEqualComparingTo(15.00.toBigDecimal())

        val dboxSeat = result.auditorium.seats.find { it.type == SeatType.DBOX && it.objectType == SeatObjectType.SEAT }!!
        dboxSeat.originalId shouldBe SEAT_DBOX.originalId
        dboxSeat.row shouldBe "C"
        dboxSeat.number shouldBe "3"
        dboxSeat.positionLeft shouldBe 30
        dboxSeat.positionTop shouldBe 80
        dboxSeat.price?.shouldBeEqualComparingTo(15.50.toBigDecimal())

        val premiumSeat = result.auditorium.seats.find { it.type == SeatType.PREMIUM_PLUS && it.objectType == SeatObjectType.SEAT }!!
        premiumSeat.originalId shouldBe SEAT_PREMIUM.originalId
        premiumSeat.row shouldBe "D"
        premiumSeat.number shouldBe "4"
        premiumSeat.positionLeft shouldBe 70
        premiumSeat.positionTop shouldBe 80
        premiumSeat.price?.shouldBeEqualComparingTo(13.25.toBigDecimal())

        val rowSeat = result.auditorium.seats.find { it.objectType == SeatObjectType.ROW }!!
        rowSeat.originalId shouldBe SEAT_ROW.originalId
        rowSeat.row shouldBe "R"
        rowSeat.number shouldBe "1"
        rowSeat.positionLeft shouldBe 100
        rowSeat.positionTop shouldBe 200
        rowSeat.price shouldBe null

        val imageSeat = result.auditorium.seats.find { it.objectType == SeatObjectType.IMAGE }!!
        imageSeat.originalId shouldBe SEAT_IMAGE.originalId
        imageSeat.row shouldBe "I"
        imageSeat.number shouldBe "1"
        imageSeat.positionLeft shouldBe 50
        imageSeat.positionTop shouldBe 150
        imageSeat.price shouldBe null
    }

    @Test
    fun `test WebGetAuditoriumQuery - DBox with zero surcharge - remaps to PREMIUM_PLUS including its surcharge and fee`() {
        val query = WebGetAuditoriumQuery(screeningOriginalId = SCREENING_IMAX_ULTRA_X.originalId!!)

        val result = underTest(query)

        result.originalId shouldBe 2
        result.priceCategoryItems.size shouldBe 2
        result.priceCategoryItems[0].number shouldBe PriceCategoryItemNumber.PRICE_20
        result.priceCategoryItems[0].price shouldBeEqualComparingTo 10.00.toBigDecimal()
        result.priceCategoryItems[1].number shouldBe PriceCategoryItemNumber.PRICE_19
        result.priceCategoryItems[1].price shouldBeEqualComparingTo 8.00.toBigDecimal()

        result.fees.surchargeVip shouldBeEqualComparingTo 0.toBigDecimal()
        result.fees.surchargePremium shouldBeEqualComparingTo 1.0.toBigDecimal()
        result.fees.surchargeDBox shouldBeEqualComparingTo 0.toBigDecimal()
        result.fees.surchargeImax shouldBeEqualComparingTo 3.00.toBigDecimal()
        result.fees.surchargeUltraX shouldBeEqualComparingTo 2.50.toBigDecimal()
        result.fees.serviceFeeVip shouldBeEqualComparingTo 0.toBigDecimal()
        result.fees.serviceFeePremium shouldBeEqualComparingTo 1.0.toBigDecimal()
        result.fees.serviceFeeGeneral shouldBeEqualComparingTo 0.toBigDecimal()
        result.fees.serviceFeeImax shouldBeEqualComparingTo 3.25.toBigDecimal()
        result.fees.serviceFeeUltraX shouldBeEqualComparingTo 2.25.toBigDecimal()

        result.auditorium.originalCode shouldBe AUDITORIUM_IMAX.originalCode
        result.auditorium.isImax shouldBe true

        result.auditorium.seats.size shouldBe 3

        val regularSeat = result.auditorium.seats.find { it.type == SeatType.REGULAR }!!
        regularSeat.type shouldBe SeatType.REGULAR
        regularSeat.row shouldBe "E"
        regularSeat.number shouldBe "5"
        regularSeat.positionLeft shouldBe 10
        regularSeat.positionTop shouldBe 30
        regularSeat.price?.shouldBeEqualComparingTo(21.00.toBigDecimal())

        val dboxSeat = result.auditorium.seats.find { it.row == "F" }!!
        dboxSeat.type shouldBe SeatType.PREMIUM_PLUS
        dboxSeat.row shouldBe "F"
        dboxSeat.number shouldBe "6"
        dboxSeat.positionLeft shouldBe 10
        dboxSeat.positionTop shouldBe 40
        dboxSeat.price?.shouldBeEqualComparingTo(23.00.toBigDecimal())

        val dboxImageSeat = result.auditorium.seats.find { it.row == "J" }
        dboxImageSeat shouldBe null

        val vipImageSeat = result.auditorium.seats.find { it.row == "K" }!!
        vipImageSeat.type shouldBe SeatType.VIP
        vipImageSeat.row shouldBe "K"
        vipImageSeat.number shouldBe "9"
        vipImageSeat.positionLeft shouldBe 170
        vipImageSeat.positionTop shouldBe 520
        vipImageSeat.price shouldBe null
    }

    @BeforeEach
    fun setUp() {
        distributorService.syncCreateOrUpdateDistributor(
            mapToCreateOrUpdateDistributorCommand(DISTRIBUTOR_1)
        )

        movieService.syncCreateOrUpdateMovie(
            mapToCreateOrUpdateMovieCommand(MOVIE_1)
        )

        setOf(AUDITORIUM_REGULAR, AUDITORIUM_IMAX).forEach {
            auditoriumService.syncCreateOrUpdateAuditorium(
                mapToCreateOrUpdateAuditoriumCommand(it)
            )
        }

        setOf(AUDITORIUM_LAYOUT_REGULAR_1, AUDITORIUM_LAYOUT_REGULAR_2, AUDITORIUM_LAYOUT_IMAX).forEach {
            auditoriumLayoutRepository.save(it)
        }

        priceCategoryService.syncCreateOrUpdatePriceCategory(
            mapToSyncCreateOrUpdatePriceCategoryCommand(PRICE_CATEGORY_1)
        )

        priceCategoryItemRepository.saveAll(setOf(PRICE_CATEGORY_ITEM_1, PRICE_CATEGORY_ITEM_19, PRICE_CATEGORY_ITEM_20))

        screeningRepository.saveAll(
            setOf(
                SCREENING_REGULAR,
                SCREENING_DBOX,
                SCREENING_IMAX_ULTRA_X
            )
        )

        screeningFeeRepository.saveAll(
            setOf(
                SCREENING_FEE_REGULAR,
                SCREENING_FEE_ULTRA_X_IMAX
            )
        )

        seatRepository.saveAll(
            setOf(
                SEAT_REGULAR,
                SEAT_DBOX,
                SEAT_VIP,
                SEAT_PREMIUM,
                SEAT_AUDITORIUM_IMAX,
                SEAT_DBOX_AUDITORIUM_IMAX,
                SEAT_ROW,
                SEAT_IMAGE,
                SEAT_DBOX_IMAGE,
                SEAT_VIP_IMAGE
            )
        )
    }

    private val LOCAL_TIME = LocalTime.of(20, 0, 0)

    private val DISTRIBUTOR_1 = createDistributor(
        originalId = 1,
        title = "Test Distributor"
    )

    private val MOVIE_1 = createMovie(
        originalId = 1,
        title = "Test Movie",
        releaseYear = 2023,
        distributorId = DISTRIBUTOR_1.id
    )

    private val AUDITORIUM_REGULAR = createAuditorium(
        originalId = 1,
        code = "A",
        title = "SÁLA A - CINEMAX BRATISLAVA",
        originalCode = 11
    )
    private val AUDITORIUM_IMAX = createAuditorium(
        originalId = 2,
        code = "IMAX",
        title = "IMAX",
        originalCode = 22
    )

    private val AUDITORIUM_LAYOUT_REGULAR_1 = createAuditoriumLayout(
        originalId = 1,
        auditoriumId = AUDITORIUM_REGULAR.id
    )

    private val AUDITORIUM_LAYOUT_REGULAR_2 = createAuditoriumLayout(
        originalId = 2,
        auditoriumId = AUDITORIUM_REGULAR.id,
        title = "Special"
    )

    private val AUDITORIUM_LAYOUT_IMAX = createAuditoriumLayout(
        originalId = 3,
        auditoriumId = AUDITORIUM_IMAX.id,
        title = "IMAX"
    )

    private val PRICE_CATEGORY_1 = createPriceCategory(
        originalId = 1,
        title = "ARTMAX PO 17",
        active = true
    )

    private val PRICE_CATEGORY_ITEM_1 = createPriceCategoryItem(
        priceCategoryId = PRICE_CATEGORY_1.id,
        number = PriceCategoryItemNumber.PRICE_1,
        price = 10.toBigDecimal()
    )

    private val PRICE_CATEGORY_ITEM_19 = createPriceCategoryItem(
        priceCategoryId = PRICE_CATEGORY_1.id,
        number = PriceCategoryItemNumber.PRICE_19,
        price = 8.toBigDecimal()
    )

    private val PRICE_CATEGORY_ITEM_20 = createPriceCategoryItem(
        priceCategoryId = PRICE_CATEGORY_1.id,
        number = PriceCategoryItemNumber.PRICE_20,
        price = 10.toBigDecimal()
    )

    // regular screening with normal fees
    private val SCREENING_REGULAR = createScreening(
        originalId = 1,
        auditoriumId = AUDITORIUM_REGULAR.id,
        auditoriumLayoutId = AUDITORIUM_LAYOUT_REGULAR_1.id,
        priceCategoryId = PRICE_CATEGORY_1.id,
        movieId = MOVIE_1.id,
        stopped = false,
        cancelled = false,
        publishOnline = true,
        state = ScreeningState.PUBLISHED,
        date = INTEGRATION_TEST_DATE_TIME.toLocalDate(),
        time = LOCAL_TIME
    )

    // IMAX screening with normal fees
    private val SCREENING_IMAX_ULTRA_X = createScreening(
        originalId = 2,
        auditoriumId = AUDITORIUM_IMAX.id,
        auditoriumLayoutId = AUDITORIUM_LAYOUT_IMAX.id,
        priceCategoryId = PRICE_CATEGORY_1.id,
        movieId = MOVIE_1.id,
        stopped = false,
        cancelled = false,
        publishOnline = true,
        state = ScreeningState.PUBLISHED,
        date = INTEGRATION_TEST_DATE_TIME.toLocalDate(),
        time = LOCAL_TIME
    )

    // Screening with zero DBox auditorium surcharge
    private val SCREENING_DBOX = createScreening(
        originalId = 3,
        auditoriumId = AUDITORIUM_REGULAR.id,
        auditoriumLayoutId = AUDITORIUM_LAYOUT_REGULAR_1.id,
        priceCategoryId = PRICE_CATEGORY_1.id,
        movieId = MOVIE_1.id,
        stopped = false,
        cancelled = false,
        publishOnline = true,
        state = ScreeningState.PUBLISHED,
        date = INTEGRATION_TEST_DATE_TIME.toLocalDate(),
        time = LOCAL_TIME.plusHours(2)
    )

    private val SCREENING_FEE_REGULAR = createScreeningFee(
        originalScreeningId = 1,
        screeningId = SCREENING_REGULAR.id,
        surchargeVip = 2.00.toBigDecimal(),
        surchargePremium = 1.00.toBigDecimal(),
        surchargeImax = 0.toBigDecimal(),
        surchargeUltraX = 0.toBigDecimal(),
        surchargeDBox = 5.00.toBigDecimal(),
        serviceFeeVip = 2.50.toBigDecimal(),
        serviceFeePremium = 1.75.toBigDecimal(),
        serviceFeeImax = 0.toBigDecimal(),
        serviceFeeUltraX = 0.toBigDecimal(),
        serviceFeeGeneral = 0.50.toBigDecimal()
    )

    private val SCREENING_FEE_ULTRA_X_IMAX = createScreeningFee(
        originalScreeningId = 2,
        screeningId = SCREENING_IMAX_ULTRA_X.id,
        surchargeVip = 0.toBigDecimal(),
        surchargePremium = 1.0.toBigDecimal(),
        surchargeImax = 3.00.toBigDecimal(),
        surchargeUltraX = 2.50.toBigDecimal(),
        surchargeDBox = 0.toBigDecimal(),
        serviceFeeVip = 0.toBigDecimal(),
        serviceFeePremium = 1.0.toBigDecimal(),
        serviceFeeImax = 3.25.toBigDecimal(),
        serviceFeeUltraX = 2.25.toBigDecimal(),
        serviceFeeGeneral = 0.toBigDecimal()
    )

    private val SEAT_REGULAR = createSeat(
        originalId = 1,
        auditoriumId = AUDITORIUM_REGULAR.id,
        auditoriumLayoutId = AUDITORIUM_LAYOUT_REGULAR_1.id,
        type = SeatType.REGULAR,
        row = "A",
        number = "1",
        positionLeft = 10,
        positionTop = 20
    )

    private val SEAT_VIP = createSeat(
        originalId = 2,
        auditoriumId = AUDITORIUM_REGULAR.id,
        auditoriumLayoutId = AUDITORIUM_LAYOUT_REGULAR_1.id,
        type = SeatType.VIP,
        row = "B",
        number = "2",
        positionLeft = 30,
        positionTop = 40
    )

    private val SEAT_DBOX = createSeat(
        originalId = 3,
        auditoriumId = AUDITORIUM_REGULAR.id,
        auditoriumLayoutId = AUDITORIUM_LAYOUT_REGULAR_1.id,
        type = SeatType.DBOX,
        row = "C",
        number = "3",
        positionLeft = 30,
        positionTop = 80
    )

    private val SEAT_PREMIUM = createSeat(
        originalId = 4,
        auditoriumId = AUDITORIUM_REGULAR.id,
        auditoriumLayoutId = AUDITORIUM_LAYOUT_REGULAR_1.id,
        type = SeatType.PREMIUM_PLUS,
        row = "D",
        number = "4",
        positionLeft = 70,
        positionTop = 80
    )

    private val SEAT_AUDITORIUM_IMAX = createSeat(
        originalId = 5,
        auditoriumId = AUDITORIUM_IMAX.id,
        auditoriumLayoutId = AUDITORIUM_LAYOUT_IMAX.id,
        type = SeatType.REGULAR,
        row = "E",
        number = "5",
        positionLeft = 10,
        positionTop = 30
    )

    private val SEAT_DBOX_AUDITORIUM_IMAX = createSeat(
        originalId = 6,
        auditoriumId = AUDITORIUM_IMAX.id,
        auditoriumLayoutId = AUDITORIUM_LAYOUT_IMAX.id,
        type = SeatType.DBOX,
        row = "F",
        number = "6",
        positionLeft = 10,
        positionTop = 40
    )

    private val SEAT_ROW = createSeat(
        originalId = 7,
        auditoriumId = AUDITORIUM_REGULAR.id,
        auditoriumLayoutId = AUDITORIUM_LAYOUT_REGULAR_1.id,
        type = SeatType.REGULAR,
        objectType = SeatObjectType.ROW,
        row = "R",
        number = "1",
        positionLeft = 100,
        positionTop = 200
    )

    private val SEAT_IMAGE = createSeat(
        originalId = 8,
        auditoriumId = AUDITORIUM_REGULAR.id,
        auditoriumLayoutId = AUDITORIUM_LAYOUT_REGULAR_1.id,
        type = SeatType.PREMIUM_PLUS,
        objectType = SeatObjectType.IMAGE,
        row = "I",
        number = "1",
        positionLeft = 50,
        positionTop = 150
    )

    private val SEAT_DBOX_IMAGE = createSeat(
        originalId = 9,
        auditoriumId = AUDITORIUM_IMAX.id,
        auditoriumLayoutId = AUDITORIUM_LAYOUT_IMAX.id,
        type = SeatType.DBOX,
        objectType = SeatObjectType.IMAGE,
        row = "J",
        number = "2",
        positionLeft = 150,
        positionTop = 100
    )

    private val SEAT_VIP_IMAGE = createSeat(
        originalId = 10,
        auditoriumId = AUDITORIUM_IMAX.id,
        auditoriumLayoutId = AUDITORIUM_LAYOUT_IMAX.id,
        type = SeatType.VIP,
        objectType = SeatObjectType.IMAGE,
        row = "K",
        number = "9",
        positionLeft = 170,
        positionTop = 520
    )
}
