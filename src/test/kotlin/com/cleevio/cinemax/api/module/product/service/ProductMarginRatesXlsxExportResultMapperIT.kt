package com.cleevio.cinemax.api.module.product.service

import com.cleevio.cinemax.api.DatabaseCleanup
import com.cleevio.cinemax.api.IntegrationTest
import com.cleevio.cinemax.api.common.service.model.ExportResultModel
import com.cleevio.cinemax.api.common.util.toDateString
import com.cleevio.cinemax.api.module.product.service.model.ProductMarginRatesExportModel
import org.apache.poi.ss.usermodel.IndexedColors
import org.apache.poi.xssf.usermodel.XSSFCellStyle
import org.apache.poi.xssf.usermodel.XSSFWorkbook
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import java.io.ByteArrayInputStream
import java.time.LocalDate
import kotlin.test.assertContains

class ProductMarginRatesXlsxExportResultMapperIT @Autowired constructor(
    private val underTest: ProductMarginRatesXlsxExportResultMapper,
) : IntegrationTest(DatabaseCleanup.AFTER_EACH) {

    @Test
    fun `test mapToExportResultModel - should map all details correctly and generate Excel file with summary row in Slovak`() {
        val fixedDateFrom = LocalDate.of(2025, 2, 1)
        val fixedDateTo = LocalDate.of(2025, 2, 28)
        val username = "testuser_sk"

        val data = listOf(
            ProductMarginRatesExportModel(
                code = "P101",
                title = "Produkt X",
                totalCount = 20,
                costs = 2000.toBigDecimal(),
                cashSales = 3000.toBigDecimal(),
                cashlessSales = 1000.toBigDecimal(),
                grossRevenue = 4000.toBigDecimal(),
                vatPercentage = "20",
                vatAmount = 800.toBigDecimal(),
                netRevenue = 3200.toBigDecimal(),
                profit = 1200.toBigDecimal()
            ),
            ProductMarginRatesExportModel(
                code = "P102",
                title = "Produkt Y",
                totalCount = 8,
                costs = 800.toBigDecimal(),
                cashSales = 1200.toBigDecimal(),
                cashlessSales = 400.toBigDecimal(),
                grossRevenue = 1600.toBigDecimal(),
                vatPercentage = "20",
                vatAmount = 320.toBigDecimal(),
                netRevenue = 1280.toBigDecimal(),
                profit = 480.toBigDecimal()
            )
        )

        val expectedTotalRow = ProductMarginRatesExportModel(
            code = "Celkovo",
            title = "",
            totalCount = 28,
            costs = 2800.toBigDecimal(),
            cashSales = 4200.toBigDecimal(),
            cashlessSales = 1400.toBigDecimal(),
            grossRevenue = 5600.toBigDecimal(),
            vatPercentage = "0",
            vatAmount = 1120.toBigDecimal(),
            netRevenue = 4480.toBigDecimal(),
            profit = 1680.toBigDecimal()
        )

        val exportResult: ExportResultModel = underTest.mapToExportResultModel(
            dateFrom = fixedDateFrom,
            dateTo = fixedDateTo,
            data = data,
            username = username
        )
        val byteArray = exportResult.inputStream.readAllBytes()
        assert(byteArray.isNotEmpty())

        // Open the generated Excel file from the byte array
        val workbook = XSSFWorkbook(ByteArrayInputStream(byteArray))
        val sheet = workbook.getSheet("Maržovosť produktov")

        // Verify main header
        val mainHeaderRow = sheet.getRow(0)
        val mainHeaderRow1 = mainHeaderRow.getCell(0).stringCellValue
        val mainHeaderRow2 = mainHeaderRow.getCell(2).stringCellValue
        val mainHeaderRow3 = mainHeaderRow.getCell(sheet.getRow(0).lastCellNum - 1).stringCellValue

        assertEquals(
            "Kino: Bratislava Bory\nPoužívateľ: testuser_sk",
            mainHeaderRow1
        )
        assertEquals(
            "Maržovosť produktov\nOd: ${fixedDateFrom.toDateString()} Do: ${fixedDateTo.toDateString()} ",
            mainHeaderRow2
        )
        assertContains(mainHeaderRow3, "Dátum: ${LocalDate.now().toDateString()}\nČas:")

        // Verify column headers
        val columnHeaders = sheet.getRow(4)
        val expectedSlovakHeaders = listOf(
            "Číslo produktu",
            "Produkt",
            "Počet",
            "Náklady",
            "Hotovosť",
            "Bezhotovosť",
            "Hrubá tržba",
            "% DPH",
            "Výška DPH",
            "Čistá tržba",
            "Zisk"
        )

        for (i in expectedSlovakHeaders.indices) {
            assertEquals(expectedSlovakHeaders[i], columnHeaders.getCell(i).stringCellValue)
        }

        // Verify data rows
        data.forEachIndexed { index, record ->
            val row = sheet.getRow(5 + index)
            assertEquals(record.code, row.getCell(0).stringCellValue)
            assertEquals(record.title, row.getCell(1).stringCellValue)
            assertEquals(record.totalCount, row.getCell(2).numericCellValue.toInt())
            assertEquals(record.costs.toDouble(), row.getCell(3).numericCellValue)
            assertEquals(record.cashSales.toDouble(), row.getCell(4).numericCellValue)
            assertEquals(record.cashlessSales.toDouble(), row.getCell(5).numericCellValue)
            assertEquals(record.grossRevenue.toDouble(), row.getCell(6).numericCellValue)
            assertEquals(record.vatPercentage, row.getCell(7).stringCellValue)
            assertEquals(record.vatAmount.toDouble(), row.getCell(8).numericCellValue)
            assertEquals(record.netRevenue.toDouble(), row.getCell(9).numericCellValue)
            assertEquals(record.profit.toDouble(), row.getCell(10).numericCellValue)
        }

        // Verify total summary row
        val totalRow = sheet.getRow(5 + data.size)
        assertEquals(expectedTotalRow.code, totalRow.getCell(0).stringCellValue)
        assertEquals(expectedTotalRow.title, totalRow.getCell(1).stringCellValue)
        assertEquals(expectedTotalRow.totalCount, totalRow.getCell(2).numericCellValue.toInt())
        assertEquals(expectedTotalRow.costs.toDouble(), totalRow.getCell(3).numericCellValue)
        assertEquals(expectedTotalRow.cashSales.toDouble(), totalRow.getCell(4).numericCellValue)
        assertEquals(expectedTotalRow.cashlessSales.toDouble(), totalRow.getCell(5).numericCellValue)
        assertEquals(expectedTotalRow.grossRevenue.toDouble(), totalRow.getCell(6).numericCellValue)
        assertEquals("", totalRow.getCell(7).stringCellValue)
        assertEquals(expectedTotalRow.vatAmount.toDouble(), totalRow.getCell(8).numericCellValue)
        assertEquals(expectedTotalRow.netRevenue.toDouble(), totalRow.getCell(9).numericCellValue)
        assertEquals(expectedTotalRow.profit.toDouble(), totalRow.getCell(10).numericCellValue)

        // Verify the total row has the highlighted style
        val cellStyle = totalRow.getCell(0).cellStyle as XSSFCellStyle
        assertEquals(IndexedColors.LIGHT_YELLOW.index, cellStyle.fillForegroundColor)
    }

    @Test
    fun `test mapToExportResultModel - should handle empty data and still generate Excel with headers in Slovak`() {
        val fixedDateFrom = LocalDate.of(2025, 3, 1)
        val fixedDateTo = LocalDate.of(2025, 3, 31)
        val username = "emptyuser_sk"

        val data = emptyList<ProductMarginRatesExportModel>()

        // Act
        val exportResult: ExportResultModel = underTest.mapToExportResultModel(
            dateFrom = fixedDateFrom,
            dateTo = fixedDateTo,
            data = data,
            username = username
        )
        val byteArray = exportResult.inputStream.readAllBytes()
        assert(byteArray.isNotEmpty())

        // Open the generated Excel file from the byte array
        val workbook = XSSFWorkbook(ByteArrayInputStream(byteArray))
        val sheet = workbook.getSheet("Maržovosť produktov")

        // Verify main header
        val mainHeaderRow = sheet.getRow(0)
        val mainHeaderRow1 = mainHeaderRow.getCell(0).stringCellValue
        val mainHeaderRow2 = mainHeaderRow.getCell(2).stringCellValue
        val mainHeaderRow3 = mainHeaderRow.getCell(sheet.getRow(0).lastCellNum - 1).stringCellValue

        assertEquals(
            "Kino: Bratislava Bory\nPoužívateľ: emptyuser_sk",
            mainHeaderRow1
        )
        assertEquals(
            "Maržovosť produktov\nOd: ${fixedDateFrom.toDateString()} Do: ${fixedDateTo.toDateString()} ",
            mainHeaderRow2
        )
        assertContains(mainHeaderRow3, "Dátum: ${LocalDate.now().toDateString()}\nČas:")

        // Verify column headers
        val columnHeaders = sheet.getRow(4)
        val expectedSlovakHeaders = listOf(
            "Číslo produktu",
            "Produkt",
            "Počet",
            "Náklady",
            "Hotovosť",
            "Bezhotovosť",
            "Hrubá tržba",
            "% DPH",
            "Výška DPH",
            "Čistá tržba",
            "Zisk"
        )

        for (i in expectedSlovakHeaders.indices) {
            assertEquals(expectedSlovakHeaders[i], columnHeaders.getCell(i).stringCellValue)
        }

        val lastRowNum = sheet.lastRowNum
        assertEquals(5, lastRowNum)
    }
}
