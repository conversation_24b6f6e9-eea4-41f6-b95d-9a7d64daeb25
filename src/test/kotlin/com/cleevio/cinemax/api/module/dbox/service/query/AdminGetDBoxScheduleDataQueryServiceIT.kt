package com.cleevio.cinemax.api.module.dbox.service.query

import com.cleevio.cinemax.api.DatabaseCleanup
import com.cleevio.cinemax.api.INTEGRATION_TEST_DATE_TIME
import com.cleevio.cinemax.api.IntegrationTest
import com.cleevio.cinemax.api.module.auditorium.service.AuditoriumRepository
import com.cleevio.cinemax.api.module.auditoriumlayout.service.AuditoriumLayoutRepository
import com.cleevio.cinemax.api.module.distributor.service.DistributorRepository
import com.cleevio.cinemax.api.module.movie.entity.Movie
import com.cleevio.cinemax.api.module.movie.service.MovieRepository
import com.cleevio.cinemax.api.module.pricecategory.service.PriceCategoryRepository
import com.cleevio.cinemax.api.module.screening.constant.ScreeningState
import com.cleevio.cinemax.api.module.screening.entity.Screening
import com.cleevio.cinemax.api.module.screening.service.ScreeningRepository
import com.cleevio.cinemax.api.module.seat.constant.SeatType
import com.cleevio.cinemax.api.module.seat.service.SeatRepository
import com.cleevio.cinemax.api.util.createAuditorium
import com.cleevio.cinemax.api.util.createAuditoriumLayout
import com.cleevio.cinemax.api.util.createDistributor
import com.cleevio.cinemax.api.util.createMovie
import com.cleevio.cinemax.api.util.createPriceCategory
import com.cleevio.cinemax.api.util.createScreening
import com.cleevio.cinemax.api.util.createSeat
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import java.time.LocalDate
import java.time.LocalTime
import java.util.UUID
import kotlin.test.assertEquals
import kotlin.test.assertTrue

class AdminGetDBoxScheduleDataQueryServiceIT @Autowired constructor(
    private val underTest: AdminGetDBoxScheduleDataQueryService,
    private val auditoriumRepository: AuditoriumRepository,
    private val auditoriumLayoutRepository: AuditoriumLayoutRepository,
    private val distributorRepository: DistributorRepository,
    private val movieRepository: MovieRepository,
    private val priceCategoryRepository: PriceCategoryRepository,
    private val screeningRepository: ScreeningRepository,
    private val seatRepository: SeatRepository,
) : IntegrationTest(DatabaseCleanup.AFTER_EACH) {

    private lateinit var movie1: Movie
    private lateinit var movie2: Movie

    @BeforeEach
    fun setUp() {
        auditoriumRepository.saveAll(setOf(AUDITORIUM_1, AUDITORIUM_2, AUDITORIUM_3))
        auditoriumLayoutRepository.saveAll(setOf(AUDITORIUM_LAYOUT_1, AUDITORIUM_LAYOUT_2, AUDITORIUM_LAYOUT_3))
        distributorRepository.save(DISTRIBUTOR_1)

        movie1 = createMovie(
            originalId = 1,
            title = "Dune: Part Two",
            rawTitle = "Dune: Part Two",
            distributorId = DISTRIBUTOR_1.id,
            duration = 166
        )

        movie2 = createMovie(
            originalId = 2,
            title = "Avatar: The Way of Water",
            rawTitle = "Avatar: The Way of Water 3D",
            distributorId = DISTRIBUTOR_1.id,
            duration = 192
        )

        movieRepository.saveAll(setOf(movie1, movie2))
        priceCategoryRepository.save(PRICE_CATEGORY_1)
        seatRepository.saveAll(
            setOf(
                SEAT_1_REGULAR,
                SEAT_2_REGULAR,
                SEAT_3_DBOX,
                SEAT_4_REGULAR,
                SEAT_5_REGULAR,
                SEAT_6_DBOX,
                SEAT_7_REGULAR,
                SEAT_8_REGULAR
            )
        )
    }

    @Test
    fun `test AdminGetDBoxScheduleDataQuery - no screenings - should return empty movies and presentations`() {
        val result = underTest()
        assertTrue(result.movies.movieList.isEmpty())
        assertTrue(result.presentations.presentationList.isEmpty())
    }

    @Test
    fun `test AdminGetDBoxScheduleDataQuery - should filter screenings with D-BOX seats correctly`() {
        // Valid screening with D-BOX seats in auditorium 1 (future date)
        val validScreening1 = createTestScreening(
            originalId = 1,
            time = LocalTime.of(20, 30),
            date = INTEGRATION_TEST_DATE_TIME.plusDays(2).toLocalDate(),
            auditoriumId = AUDITORIUM_1.id,
            auditoriumLayoutId = AUDITORIUM_LAYOUT_1.id,
            movieId = movie1.id
        )

        // Valid screening with D-BOX seats in auditorium 2 (current day, future time)
        val validScreening2 = createTestScreening(
            originalId = 2,
            time = INTEGRATION_TEST_DATE_TIME.toLocalTime().plusHours(1),
            date = INTEGRATION_TEST_DATE_TIME.toLocalDate(),
            auditoriumId = AUDITORIUM_2.id,
            auditoriumLayoutId = AUDITORIUM_LAYOUT_2.id,
            movieId = movie2.id
        )

        // Valid screening with D-BOX seats in auditorium 1 (current day, past time) - should be included
        val validScreening3 = createTestScreening(
            originalId = 3,
            time = INTEGRATION_TEST_DATE_TIME.toLocalTime().minusHours(2),
            date = INTEGRATION_TEST_DATE_TIME.toLocalDate(),
            auditoriumId = AUDITORIUM_1.id,
            auditoriumLayoutId = AUDITORIUM_LAYOUT_1.id,
            movieId = movie1.id
        )

        // Past day screening - should not appear
        createTestScreening(
            originalId = 4,
            date = INTEGRATION_TEST_DATE_TIME.toLocalDate().minusDays(1),
            auditoriumId = AUDITORIUM_1.id,
            auditoriumLayoutId = AUDITORIUM_LAYOUT_1.id,
            movieId = movie1.id
        )

        // Future screening but cancelled - should not appear
        createTestScreening(
            originalId = 5,
            date = INTEGRATION_TEST_DATE_TIME.plusDays(3).toLocalDate(),
            auditoriumId = AUDITORIUM_1.id,
            auditoriumLayoutId = AUDITORIUM_LAYOUT_1.id,
            movieId = movie1.id,
            cancelled = true
        )

        // Future screening but stopped - should not appear
        createTestScreening(
            originalId = 6,
            date = INTEGRATION_TEST_DATE_TIME.plusDays(3).toLocalDate(),
            auditoriumId = AUDITORIUM_1.id,
            auditoriumLayoutId = AUDITORIUM_LAYOUT_1.id,
            movieId = movie1.id,
            stopped = true
        )

        // Future screening but deleted - should not appear
        createTestScreening(
            originalId = 7,
            date = INTEGRATION_TEST_DATE_TIME.plusDays(3).toLocalDate(),
            auditoriumId = AUDITORIUM_1.id,
            auditoriumLayoutId = AUDITORIUM_LAYOUT_1.id,
            movieId = movie1.id,
            deleted = true
        )

        // Future screening but not in PUBLISHED state - should not appear
        createTestScreening(
            originalId = 8,
            date = INTEGRATION_TEST_DATE_TIME.plusDays(3).toLocalDate(),
            auditoriumId = AUDITORIUM_1.id,
            auditoriumLayoutId = AUDITORIUM_LAYOUT_1.id,
            movieId = movie1.id,
            state = ScreeningState.DRAFT
        )

        // Valid future screening but in auditorium without D-BOX seats - should not appear
        createTestScreening(
            originalId = 9,
            date = INTEGRATION_TEST_DATE_TIME.plusDays(1).toLocalDate(),
            time = LocalTime.of(18, 0),
            auditoriumId = AUDITORIUM_3.id,
            auditoriumLayoutId = AUDITORIUM_LAYOUT_3.id,
            movieId = movie1.id
        )

        val result = underTest()

        // Should contain only valid screenings (including past screenings from current day)
        assertEquals(2, result.movies.movieList.size)
        assertEquals(3, result.presentations.presentationList.size)

        // Verify movies
        val movieIds = result.movies.movieList.map { it.id }.toSet()
        assertTrue(movieIds.contains(movie1.originalId.toString()))
        assertTrue(movieIds.contains(movie2.originalId.toString()))

        // Verify presentations
        val presentationIds = result.presentations.presentationList.map { it.id }.toSet()
        assertTrue(presentationIds.contains(validScreening1.originalId.toString()))
        assertTrue(presentationIds.contains(validScreening2.originalId.toString()))
        assertTrue(presentationIds.contains(validScreening3.originalId.toString()))

        // Verify that screening in auditorium without D-BOX seats is not included
        assertTrue(!presentationIds.contains("9"))

        // Verify that past day screening is not included
        assertTrue(!presentationIds.contains("4"))

        // Verify first presentation details
        result.presentations.presentationList.first { it.id == validScreening1.originalId.toString() }
            .let { presentation ->
                assertEquals(validScreening1.originalId.toString(), presentation.id)
                assertEquals(movie1.originalId.toString(), presentation.movieId)
                assertEquals(AUDITORIUM_1.originalId.toString(), presentation.auditoriumId)
                assertEquals((movie1.duration!! + validScreening1.adTimeSlot) * 60, presentation.durationS)
            }

        // Verify second presentation details
        result.presentations.presentationList.first { it.id == validScreening2.originalId.toString() }
            .let { presentation ->
                assertEquals(validScreening2.originalId.toString(), presentation.id)
                assertEquals(movie2.originalId.toString(), presentation.movieId)
                assertEquals(AUDITORIUM_2.originalId.toString(), presentation.auditoriumId)
                assertEquals((movie2.duration!! + validScreening2.adTimeSlot) * 60, presentation.durationS)
            }

        // Verify third presentation details (past screening from current day)
        result.presentations.presentationList.first { it.id == validScreening3.originalId.toString() }
            .let { presentation ->
                assertEquals(validScreening3.originalId.toString(), presentation.id)
                assertEquals(movie1.originalId.toString(), presentation.movieId)
                assertEquals(AUDITORIUM_1.originalId.toString(), presentation.auditoriumId)
                assertEquals((movie1.duration!! + validScreening3.adTimeSlot) * 60, presentation.durationS)
            }

        // Verify first movie details
        result.movies.movieList.first { it.id == movie1.originalId.toString() }.let { movieItem ->
            assertEquals(movie1.originalId.toString(), movieItem.id)
            assertEquals(movie1.rawTitle, movieItem.name)
            assertEquals((movie1.duration!! + validScreening1.adTimeSlot) * 60, movieItem.durationS)
        }

        // Verify second movie details
        result.movies.movieList.first { it.id == movie2.originalId.toString() }.let { movieItem ->
            assertEquals(movie2.originalId.toString(), movieItem.id)
            assertEquals(movie2.rawTitle, movieItem.name)
            assertEquals((movie2.duration!! + validScreening2.adTimeSlot) * 60, movieItem.durationS)
        }
    }

    private fun createTestScreening(
        date: LocalDate = INTEGRATION_TEST_DATE_TIME.toLocalDate(),
        time: LocalTime = LocalTime.of(19, 0),
        stopped: Boolean = false,
        cancelled: Boolean = false,
        deleted: Boolean = false,
        originalId: Int = 1,
        auditoriumId: UUID = AUDITORIUM_1.id,
        auditoriumLayoutId: UUID = AUDITORIUM_LAYOUT_1.id,
        movieId: UUID = movie1.id,
        state: ScreeningState = ScreeningState.PUBLISHED,
    ): Screening {
        val screening = createScreening(
            originalId = originalId,
            auditoriumId = auditoriumId,
            auditoriumLayoutId = auditoriumLayoutId,
            movieId = movieId,
            priceCategoryId = PRICE_CATEGORY_1.id,
            date = date,
            time = time,
            stopped = stopped,
            cancelled = cancelled,
            state = state
        ).also {
            if (deleted) it.markDeleted()
        }
        screeningRepository.save(screening)
        return screening
    }
}

private val AUDITORIUM_1 = createAuditorium(
    originalId = 1,
    code = "A",
    title = "SÁLA A - CINEMAX BRATISLAVA",
    originalCode = 22
)
private val AUDITORIUM_2 = createAuditorium(
    originalId = 2,
    code = "B",
    title = "SÁLA B - CINEMAX BRATISLAVA",
    originalCode = 33
)
private val AUDITORIUM_3 = createAuditorium(
    originalId = 3,
    code = "C",
    title = "SÁLA C - CINEMAX BRATISLAVA",
    originalCode = 44
)
private val DISTRIBUTOR_1 = createDistributor(originalId = 1)
private val AUDITORIUM_LAYOUT_1 = createAuditoriumLayout(originalId = 1, auditoriumId = AUDITORIUM_1.id)
private val AUDITORIUM_LAYOUT_2 = createAuditoriumLayout(originalId = 2, auditoriumId = AUDITORIUM_2.id)
private val AUDITORIUM_LAYOUT_3 = createAuditoriumLayout(originalId = 3, auditoriumId = AUDITORIUM_3.id)
private val PRICE_CATEGORY_1 = createPriceCategory(
    originalId = 1,
    title = "STANDARD",
    active = true
)
private val SEAT_1_REGULAR = createSeat(
    originalId = 1,
    auditoriumId = AUDITORIUM_1.id,
    auditoriumLayoutId = AUDITORIUM_LAYOUT_1.id,
    type = SeatType.REGULAR
)
private val SEAT_2_REGULAR = createSeat(
    originalId = 2,
    auditoriumId = AUDITORIUM_1.id,
    auditoriumLayoutId = AUDITORIUM_LAYOUT_1.id,
    type = SeatType.REGULAR
)
private val SEAT_3_DBOX = createSeat(
    originalId = 3,
    auditoriumId = AUDITORIUM_1.id,
    auditoriumLayoutId = AUDITORIUM_LAYOUT_1.id,
    type = SeatType.DBOX
)
private val SEAT_4_REGULAR = createSeat(
    originalId = 4,
    auditoriumId = AUDITORIUM_2.id,
    auditoriumLayoutId = AUDITORIUM_LAYOUT_2.id,
    type = SeatType.REGULAR
)
private val SEAT_5_REGULAR = createSeat(
    originalId = 5,
    auditoriumId = AUDITORIUM_2.id,
    auditoriumLayoutId = AUDITORIUM_LAYOUT_2.id,
    type = SeatType.REGULAR
)
private val SEAT_6_DBOX = createSeat(
    originalId = 6,
    auditoriumId = AUDITORIUM_2.id,
    auditoriumLayoutId = AUDITORIUM_LAYOUT_2.id,
    type = SeatType.DBOX
)
private val SEAT_7_REGULAR = createSeat(
    originalId = 7,
    auditoriumId = AUDITORIUM_3.id,
    auditoriumLayoutId = AUDITORIUM_LAYOUT_3.id,
    type = SeatType.REGULAR
)
private val SEAT_8_REGULAR = createSeat(
    originalId = 8,
    auditoriumId = AUDITORIUM_3.id,
    auditoriumLayoutId = AUDITORIUM_LAYOUT_3.id,
    type = SeatType.REGULAR
)
