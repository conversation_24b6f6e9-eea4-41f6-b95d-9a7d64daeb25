package com.cleevio.cinemax.api.module.basketitem.controller

import com.cleevio.cinemax.api.ControllerTest
import com.cleevio.cinemax.api.common.constant.ApiVersion
import com.cleevio.cinemax.api.common.constant.ExportFormat
import com.cleevio.cinemax.api.common.constant.PaymentType
import com.cleevio.cinemax.api.common.service.model.ExportResultModel
import com.cleevio.cinemax.api.common.util.jsonContent
import com.cleevio.cinemax.api.module.basket.constant.BasketState
import com.cleevio.cinemax.api.module.basketitem.constant.BasketItemType
import com.cleevio.cinemax.api.module.basketitem.controller.dto.AdminBranchSalesOverviewResponse
import com.cleevio.cinemax.api.module.basketitem.controller.dto.AdminSearchProductBasketItemsFilter
import com.cleevio.cinemax.api.module.basketitem.controller.dto.AdminSearchProductBasketItemsResponse
import com.cleevio.cinemax.api.module.basketitem.controller.dto.AdminSearchProductBasketItemsSummaryResponse
import com.cleevio.cinemax.api.module.basketitem.controller.dto.AdminSearchTicketBasketItemsFilter
import com.cleevio.cinemax.api.module.basketitem.controller.dto.AdminSearchTicketBasketItemsResponse
import com.cleevio.cinemax.api.module.basketitem.controller.dto.AdminSearchTicketBasketItemsSummaryResponse
import com.cleevio.cinemax.api.module.basketitem.controller.dto.BranchSalesOverview
import com.cleevio.cinemax.api.module.basketitem.controller.dto.MonthlySalesOverview
import com.cleevio.cinemax.api.module.basketitem.controller.dto.TotalSalesOverview
import com.cleevio.cinemax.api.module.basketitem.service.NO_DISCOUNT_TITLE
import com.cleevio.cinemax.api.module.basketitem.service.command.CancelBasketItemsCommand
import com.cleevio.cinemax.api.module.basketitem.service.query.AdminBranchSalesOverviewQuery
import com.cleevio.cinemax.api.module.basketitem.service.query.AdminExportProductBasketItemsQuery
import com.cleevio.cinemax.api.module.basketitem.service.query.AdminExportTicketBasketItemsQuery
import com.cleevio.cinemax.api.module.basketitem.service.query.AdminSearchProductBasketItemsQuery
import com.cleevio.cinemax.api.module.basketitem.service.query.AdminSearchTicketBasketItemsQuery
import com.cleevio.cinemax.api.module.discountcard.constant.DiscountCardType
import com.cleevio.cinemax.api.module.employee.constant.EmployeeRole
import com.cleevio.cinemax.api.module.productcategory.constant.ProductCategoryType
import com.cleevio.cinemax.api.module.ticketprice.constant.ServiceFeeType
import com.cleevio.cinemax.api.module.ticketprice.constant.SurchargeType
import com.cleevio.cinemax.api.util.mockAccessToken
import com.cleevio.cinemax.api.util.toUUID
import io.mockk.every
import io.mockk.verify
import io.mockk.verifySequence
import org.hamcrest.CoreMatchers
import org.junit.jupiter.api.Test
import org.junitpioneer.jupiter.RetryingTest
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest
import org.springframework.data.domain.PageImpl
import org.springframework.data.domain.PageRequest
import org.springframework.data.domain.Pageable
import org.springframework.data.domain.Sort
import org.springframework.http.HttpHeaders
import org.springframework.http.MediaType
import org.springframework.test.web.servlet.MockMvc
import org.springframework.test.web.servlet.post
import java.io.ByteArrayInputStream
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.LocalTime
import java.util.UUID

@WebMvcTest(AdminBasketItemController::class)
class AdminBasketItemControllerTest @Autowired constructor(
    private val mvc: MockMvc,
) : ControllerTest() {

    @Test
    fun `test cancelBasketItems, should serialize and deserialize correctly`() {
        val basketId = UUID.fromString("85b74cd4-4f7f-4786-8820-8b3bc60a3df0")
        val basketItemToCancel1 = UUID.fromString("b37b2f51-343a-4168-a63e-551d49aa2ab4")
        val basketItemToCancel2 = UUID.fromString("eb3b6c1a-8152-4e8c-bd70-f6257c6928fa")
        every { basketItemService.cancelBasketItems(any()) } returns setOf(basketId)

        mvc.post("$BASE_PATH/cancel") {
            header(HttpHeaders.ACCEPT, ApiVersion.VERSION_1_JSON)
            header(HttpHeaders.AUTHORIZATION, mockAccessToken(EmployeeRole.MANAGER))
            contentType = MediaType.APPLICATION_JSON
            content = """
                {
                  "basketItemIds": [
                    "$basketItemToCancel1",
                    "$basketItemToCancel2"
                  ],
                  "printReceipt": true
                }
            """.trimIndent()
        }.andExpect {
            status { isOk() }
            content { contentType(ApiVersion.VERSION_1_JSON) }
            jsonContent(
                """
                    {
                      "basketIds": [
                        "$basketId"
                      ]
                    }
                """.trimIndent()
            )
        }

        verifySequence {
            basketItemService.cancelBasketItems(
                CancelBasketItemsCommand(
                    basketItemIds = setOf(basketItemToCancel1, basketItemToCancel2),
                    printReceipt = true
                )
            )
        }
    }

    @Test
    fun `test searchTicketBasketItems - should serialize and deserialize correctly`() {
        val basketId = UUID.fromString("85b74cd4-4f7f-4786-8820-8b3bc60a3df0")
        val basketItemId = UUID.fromString("b0ad5e08-c6b5-4c41-a424-50fb5e911d6e")
        val posConfigurationId = UUID.fromString("f055fbce-f17a-4bb6-82b9-a1d11ceffa3c")
        val ticketId = UUID.fromString("102a9977-0fc0-441c-9eab-130b6108ca9b")
        val ticketPriceId = UUID.fromString("817d27bf-1a31-41df-809b-bf1d00fbc62c")
        val primaryTicketDiscountId = UUID.fromString("1a69acad-00cf-481b-a044-d422f609de21")
        val secondaryTicketDiscountId = UUID.fromString("bb259a48-6633-41d2-b98f-3749bcd8f49b")
        val screeningId = UUID.fromString("76044133-be6a-4dba-8e9e-6f9bfebd65a2")
        val screeningTypeId = UUID.fromString("7664a779-df0f-4887-ac8c-34c795d6a70a")
        val technologyId = UUID.fromString("bf182762-a148-4c20-a01a-d1d3eb447c59")
        val productionId = UUID.fromString("a6b69d3f-8cef-4d1b-92ed-973222d7a040")
        val auditoriumId = UUID.fromString("b6eabf8a-05db-4443-ae1e-e2f564961b4b")
        val moveId = UUID.fromString("99c306b5-ccab-4301-815f-4287e49cfc24")
        val distributorId = UUID.fromString("7f1947db-7c99-413a-ba32-1da7885a54e3")
        val discountCardId = UUID.fromString("5498f8db-f144-460b-8ea2-a288ac7ac852")
        val seatId = UUID.fromString("9878f8db-f144-460b-8ea2-a288ac7ac678")

        val summaryResponse = AdminSearchTicketBasketItemsSummaryResponse(
            sales = AdminSearchTicketBasketItemsSummaryResponse.SalesResponse(
                screeningsCount = 1,
                ticketsCount = 1,
                ticketsUsedCount = 0,
                salesCash = 10.toBigDecimal(),
                salesCashless = 0.toBigDecimal(),
                grossSales = 10.toBigDecimal(),
                netSales = 7.toBigDecimal(),
                proDeduction = 0.1.toBigDecimal(),
                filmFondDeduction = 0.1.toBigDecimal(),
                distributorDeduction = 5.toBigDecimal(),
                taxAmount = 2.toBigDecimal(),
                cancelledTicketsCount = 0,
                cancelledTicketsExpense = 0.toBigDecimal()
            ),
            technologies = AdminSearchTicketBasketItemsSummaryResponse.TechnologiesResponse(
                serviceFees = AdminSearchTicketBasketItemsSummaryResponse.ServiceFeesResponse(
                    general = 1.toBigDecimal(),
                    generalCount = 1,
                    vip = 0.toBigDecimal(),
                    vipCount = 0,
                    premiumPlus = 0.toBigDecimal(),
                    premiumPlusCount = 0,
                    imax = 0.toBigDecimal(),
                    imaxCount = 0,
                    ultraX = 0.toBigDecimal(),
                    ultraXCount = 0,
                    dolbyAtmos = 0.toBigDecimal()
                ),
                surcharges = AdminSearchTicketBasketItemsSummaryResponse.SurchargesResponse(
                    vip = 1.toBigDecimal(),
                    vipCount = 1,
                    dBox = 0.toBigDecimal(),
                    dBoxCount = 0,
                    premiumPlus = 0.toBigDecimal(),
                    premiumPlusCount = 0,
                    imax = 0.toBigDecimal(),
                    imaxCount = 0,
                    ultraX = 0.toBigDecimal(),
                    ultraXCount = 0,
                    dolbyAtmos = 0.toBigDecimal()
                ),
                counts = AdminSearchTicketBasketItemsSummaryResponse.CountsResponse(
                    general = 0,
                    vip = 0,
                    dBox = 0,
                    premiumPlus = 0,
                    imax = 0,
                    dolbyAtmos = 0,
                    total = 0
                )
            ),
            discounts = listOf(
                AdminSearchTicketBasketItemsSummaryResponse.DiscountResponse(
                    title = NO_DISCOUNT_TITLE,
                    count = 1
                )
            )
        )
        val salesResponse = summaryResponse.sales
        val technologiesResponse = summaryResponse.technologies

        every { searchTicketBasketItemSummaryQueryService(any()) } returns summaryResponse
        every { searchTicketBasketItemQueryService(any()) } returns PageImpl(
            listOf(
                AdminSearchTicketBasketItemsResponse(
                    id = basketItemId,
                    type = BasketItemType.TICKET,
                    isCancelled = false,
                    branchName = "Bratislava Bory",
                    basket = AdminSearchTicketBasketItemsResponse.BasketResponse(
                        id = basketId,
                        state = BasketState.PAID,
                        variableSymbol = "123456789",
                        paymentType = PaymentType.CASHLESS,
                        paidAt = LocalDateTime.of(2024, 8, 27, 23, 1, 30),
                        updatedBy = "monika",
                        paymentPosConfiguration = AdminSearchTicketBasketItemsResponse.PosConfigurationResponse(
                            id = posConfigurationId,
                            title = "pokl1"
                        )
                    ),
                    ticket = AdminSearchTicketBasketItemsResponse.TicketResponse(
                        id = ticketId,
                        receiptNumber = "1000000001",
                        isUsed = true,
                        includes3dGlasses = false,
                        ticketPrice = AdminSearchTicketBasketItemsResponse.TicketPriceResponse(
                            id = ticketPriceId,
                            totalPrice = 10.toBigDecimal(),
                            basePrice = 8.toBigDecimal(),
                            surchargesSum = 1.toBigDecimal(),
                            serviceFeesSum = 1.toBigDecimal()
                        ),
                        primaryTicketDiscount = AdminSearchTicketBasketItemsResponse.TicketDiscountResponse(
                            id = primaryTicketDiscountId,
                            title = "Sleva 50%"
                        ),
                        secondaryTicketDiscount = null,
                        screening = AdminSearchTicketBasketItemsResponse.ScreeningResponse(
                            id = screeningId,
                            date = LocalDate.of(2024, 8, 27),
                            time = LocalTime.of(22, 0),
                            saleTimeLimit = 30,
                            auditorium = AdminSearchTicketBasketItemsResponse.AuditoriumResponse(
                                id = auditoriumId,
                                code = "A"
                            ),
                            movie = AdminSearchTicketBasketItemsResponse.MovieResponse(
                                id = moveId,
                                rawTitle = "Interstellar 2D",
                                distributor = AdminSearchTicketBasketItemsResponse.DistributorResponse(
                                    id = distributorId,
                                    title = "Best movies ever, s.r.o."
                                )
                            )
                        ),
                        seat = AdminSearchTicketBasketItemsResponse.SeatResponse(
                            id = seatId,
                            row = "11",
                            number = "3"
                        )
                    ),
                    discountCard = AdminSearchTicketBasketItemsResponse.DiscountCardResponse(
                        id = discountCardId,
                        type = DiscountCardType.CARD,
                        code = "123456678",
                        title = "FILM karta"
                    ),
                    card = null
                )
            )
        )

        mvc.post("$BASE_PATH/tickets-search?page=1&size=10&sort=distributor.title") {
            header(HttpHeaders.ACCEPT, ApiVersion.VERSION_1_JSON)
            header(HttpHeaders.AUTHORIZATION, mockAccessToken(EmployeeRole.MANAGER))
            contentType = MediaType.APPLICATION_JSON
            content = """
                {
                  "basketPaidAtFrom": "2023-08-15T14:30:00Z",
                  "basketPaidAtTo": "2023-08-20T18:45:00Z",
                  "basketUpdatedBy": ["monika"],
                  "basketStates": [
                    "PAID",
                    "PAID_ONLINE"
                  ],
                  "basketVariableSymbol": "123456789",
                  "screeningDateTimeFrom": "2023-08-25T12:00:00Z",
                  "screeningDateTimeTo": "2023-08-25T14:30:00Z",
                  "auditoriumIds": [
                    "$auditoriumId"
                  ],
                  "screeningIds": [
                    "$screeningId"
                  ],
                  "movieIds": [
                    "$moveId"
                  ],
                  "distributorIds": [
                    "$distributorId"
                  ],
                  "posConfigurationIds": [
                    "$posConfigurationId"
                  ],
                  "paymentTypes": [
                    "CASHLESS"
                  ],
                  "ticketUsed": true,
                  "ticketIncludes3dGlasses": false,
                  "surchargeTypes": [
                    "VIP",
                    "IMAX"
                  ],
                  "serviceFeeTypes": [
                    "PREMIUM_PLUS",
                    "GENERAL"
                  ],
                  "screeningTypeIds": [
                    "$screeningTypeId"
                  ],
                  "technologyIds": [
                    "$technologyId"
                  ],
                  "productionIds": [
                    "$productionId"
                  ],
                  "ticketReceiptNumbers": [
                    "1000000001"
                  ],
                  "discountCardCodes": [
                    "123456678"
                  ],
                  "discountCardTitles": [
                    "Slovak Telekom"
                  ],
                  "isDiscounted": true,
                  "primaryTicketDiscountIds": [
                    "$primaryTicketDiscountId"
                  ],
                  "secondaryTicketDiscountIds": [
                    "$secondaryTicketDiscountId"
                  ],
                  "isCancelled": false
                }
            """.trimIndent()
        }.andExpect {
            status { isOk() }
            content { contentType(ApiVersion.VERSION_1_JSON) }
            jsonContent(
                // "dBox" keys are omitted from expected JSON intentionally - AssertionError is throw during their evaluation
                """
                    {
                        "summary": {
                          "sales": {
                            "screeningsCount": ${salesResponse.screeningsCount},
                            "ticketsCount": ${salesResponse.ticketsCount},
                            "ticketsUsedCount": ${salesResponse.ticketsUsedCount},
                            "salesCash": ${salesResponse.salesCash},
                            "salesCashless": ${salesResponse.salesCashless},
                            "grossSales": ${salesResponse.grossSales},
                            "netSales": ${salesResponse.netSales},
                            "distributorDeduction": ${salesResponse.distributorDeduction},
                            "proDeduction": ${salesResponse.proDeduction},
                            "filmFondDeduction": ${salesResponse.filmFondDeduction},
                            "taxAmount": ${salesResponse.taxAmount},
                            "cancelledTicketsCount": ${salesResponse.cancelledTicketsCount},
                            "cancelledTicketsExpense": ${salesResponse.cancelledTicketsExpense}
                          },
                          "technologies": {
                            "serviceFees": {
                              "general": ${technologiesResponse.serviceFees.general},
                              "generalCount": ${technologiesResponse.serviceFees.generalCount},
                              "vip": ${technologiesResponse.serviceFees.vip},
                              "vipCount": ${technologiesResponse.serviceFees.vipCount},
                              "premiumPlus": ${technologiesResponse.serviceFees.premiumPlus},
                              "premiumPlusCount": ${technologiesResponse.serviceFees.premiumPlusCount}, 
                              "imax": ${technologiesResponse.serviceFees.imax},
                              "imaxCount": ${technologiesResponse.serviceFees.imaxCount},
                              "ultraX": ${technologiesResponse.serviceFees.ultraX},
                              "ultraXCount": ${technologiesResponse.serviceFees.ultraXCount},
                              "dolbyAtmos": ${technologiesResponse.serviceFees.dolbyAtmos}
                            },
                            "surcharges": {
                              "vip": ${technologiesResponse.surcharges.vip},
                              "vipCount": ${technologiesResponse.surcharges.vipCount},
                              "premiumPlus": ${technologiesResponse.surcharges.premiumPlus},
                              "premiumPlusCount": ${technologiesResponse.surcharges.premiumPlusCount},
                              "imax": ${technologiesResponse.surcharges.imax},
                              "imaxCount": ${technologiesResponse.surcharges.imaxCount},
                              "ultraX": ${technologiesResponse.surcharges.ultraX},
                              "ultraXCount": ${technologiesResponse.surcharges.ultraXCount},
                              "dolbyAtmos": ${technologiesResponse.surcharges.dolbyAtmos}
                            },
                            "counts": {
                              "general": ${technologiesResponse.counts.general},
                              "vip": ${technologiesResponse.counts.vip},
                              "premiumPlus": ${technologiesResponse.counts.premiumPlus},
                              "imax": ${technologiesResponse.counts.imax},
                              "dolbyAtmos": ${technologiesResponse.counts.dolbyAtmos},
                              "total": ${technologiesResponse.counts.total}
                            }
                          },
                          "discounts": [
                            {
                               "title": "Bez slevy",
                               "count": 1
                            }
                          ]
                        },
                        "results": {
                          "content": [
                            {
                              "id": "$basketItemId",
                              "type": "TICKET",
                              "isCancelled": false,
                              "branchName": "Bratislava Bory",
                              "basket": {
                                "id": "$basketId",
                                "paymentType": "CASHLESS",
                                "state": "PAID",
                                "variableSymbol": "123456789",
                                "paidAt": "2024-08-27T21:01:30Z",
                                "updatedBy": "monika",
                                "paymentPosConfiguration": {
                                  "id": "$posConfigurationId",
                                  "title": "pokl1"
                                }
                              },
                              "ticket": {
                                "id": "$ticketId",
                                "receiptNumber": "1000000001",
                                "isUsed": true,
                                "includes3dGlasses": false,
                                "ticketPrice": {
                                  "id": "$ticketPriceId",
                                  "totalPrice": 10.00,
                                  "basePrice": 8.00,
                                  "surchargesSum": 1.00,
                                  "serviceFeesSum": 1.00
                                },
                                "primaryTicketDiscount": {
                                  "id": "$primaryTicketDiscountId",
                                  "title": "Sleva 50%"
                                },
                                "secondaryTicketDiscount": null,
                                "screening": {
                                  "id": "$screeningId",
                                  "date": "2024-08-27",
                                  "time": "22:00:00",
                                  "saleTimeLimit": 30,
                                  "auditorium": {
                                    "id": "$auditoriumId",
                                    "code": "A"
                                  },
                                  "movie": {
                                    "id": "$moveId",
                                    "rawTitle": "Interstellar 2D",
                                    "distributor": {
                                      "id": "$distributorId",
                                      "title": "Best movies ever, s.r.o."
                                    }
                                  }
                                },
                                "seat": {
                                  "id": "$seatId",
                                  "row": "11",
                                  "number": "3"
                                }
                              },
                              "discountCard": {
                                "id": "$discountCardId",
                                "type": "CARD",
                                "code": "123456678",
                                "title": "FILM karta"
                              }
                            }
                          ],
                          "totalElements": 1,
                          "totalPages": 1
                        }
                    }
                """.trimIndent()
            )
        }

        verifySequence {
            searchTicketBasketItemSummaryQueryService(
                query = AdminSearchTicketBasketItemsQuery(
                    pageable = PageRequest.of(1, 10, Sort.by("distributor.title")),
                    filter = AdminSearchTicketBasketItemsFilter(
                        basketPaidAtFrom = LocalDateTime.of(2023, 8, 15, 16, 30, 0),
                        basketPaidAtTo = LocalDateTime.of(2023, 8, 20, 20, 45, 0),
                        basketUpdatedBy = setOf("monika"),
                        basketStates = setOf(BasketState.PAID, BasketState.PAID_ONLINE),
                        basketVariableSymbol = "123456789",
                        screeningDateTimeFrom = LocalDateTime.of(2023, 8, 25, 14, 0, 0),
                        screeningDateTimeTo = LocalDateTime.of(2023, 8, 25, 16, 30, 0),
                        auditoriumIds = setOf(auditoriumId),
                        screeningIds = setOf(screeningId),
                        movieIds = setOf(moveId),
                        distributorIds = setOf(distributorId),
                        posConfigurationIds = setOf(posConfigurationId),
                        paymentTypes = setOf(PaymentType.CASHLESS),
                        ticketUsed = true,
                        ticketIncludes3dGlasses = false,
                        surchargeTypes = setOf(SurchargeType.VIP, SurchargeType.IMAX),
                        serviceFeeTypes = setOf(ServiceFeeType.PREMIUM_PLUS, ServiceFeeType.GENERAL),
                        screeningTypeIds = setOf(screeningTypeId),
                        technologyIds = setOf(technologyId),
                        productionIds = setOf(productionId),
                        ticketReceiptNumbers = setOf("1000000001"),
                        discountCardCodes = setOf("123456678"),
                        discountCardTitles = setOf("Slovak Telekom"),
                        isDiscounted = true,
                        primaryTicketDiscountIds = setOf(primaryTicketDiscountId),
                        secondaryTicketDiscountIds = setOf(secondaryTicketDiscountId),
                        isCancelled = false
                    )
                )
            )
            searchTicketBasketItemQueryService(
                query = AdminSearchTicketBasketItemsQuery(
                    pageable = PageRequest.of(1, 10, Sort.by("distributor.title")),
                    filter = AdminSearchTicketBasketItemsFilter(
                        basketPaidAtFrom = LocalDateTime.of(2023, 8, 15, 16, 30, 0),
                        basketPaidAtTo = LocalDateTime.of(2023, 8, 20, 20, 45, 0),
                        basketUpdatedBy = setOf("monika"),
                        basketStates = setOf(BasketState.PAID, BasketState.PAID_ONLINE),
                        basketVariableSymbol = "123456789",
                        screeningDateTimeFrom = LocalDateTime.of(2023, 8, 25, 14, 0, 0),
                        screeningDateTimeTo = LocalDateTime.of(2023, 8, 25, 16, 30, 0),
                        auditoriumIds = setOf(auditoriumId),
                        screeningIds = setOf(screeningId),
                        movieIds = setOf(moveId),
                        distributorIds = setOf(distributorId),
                        posConfigurationIds = setOf(posConfigurationId),
                        paymentTypes = setOf(PaymentType.CASHLESS),
                        ticketUsed = true,
                        ticketIncludes3dGlasses = false,
                        surchargeTypes = setOf(SurchargeType.VIP, SurchargeType.IMAX),
                        serviceFeeTypes = setOf(ServiceFeeType.PREMIUM_PLUS, ServiceFeeType.GENERAL),
                        screeningTypeIds = setOf(screeningTypeId),
                        technologyIds = setOf(technologyId),
                        productionIds = setOf(productionId),
                        ticketReceiptNumbers = setOf("1000000001"),
                        discountCardCodes = setOf("123456678"),
                        discountCardTitles = setOf("Slovak Telekom"),
                        isDiscounted = true,
                        primaryTicketDiscountIds = setOf(primaryTicketDiscountId),
                        secondaryTicketDiscountIds = setOf(secondaryTicketDiscountId),
                        isCancelled = false
                    )
                )
            )
        }
    }

    @Test
    fun `test searchProductBasketItems - should serialize and deserialize correctly`() {
        val basketId = UUID.fromString("85b74cd4-4f7f-4786-8820-8b3bc60a3df0")
        val basketItemId = UUID.fromString("b0ad5e08-c6b5-4c41-a424-50fb5e911d6e")
        val posConfigurationId = UUID.fromString("f055fbce-f17a-4bb6-82b9-a1d11ceffa3c")
        val productId = UUID.fromString("b6eabf8a-05db-4443-ae1e-e2f564961b4b")
        val productCategoryId = UUID.fromString("99c306b5-ccab-4301-815f-4287e49cfc24")
        val discountCardId = UUID.fromString("5498f8db-f144-460b-8ea2-a288ac7ac852")

        every { searchProductBasketItemSummaryQueryService(any()) } returns AdminSearchProductBasketItemsSummaryResponse(
            sales = AdminSearchProductBasketItemsSummaryResponse.SalesResponse(
                productsCount = 2,
                basketsCount = 1,
                purchasePriceSum = 1.095.toBigDecimal(),
                salesCash = 6.45.toBigDecimal(),
                salesCashless = 0.toBigDecimal(),
                grossSales = 6.45.toBigDecimal(),
                netSales = 5.328.toBigDecimal(),
                taxAmount = 1.122.toBigDecimal(),
                grossRevenue = 5.355.toBigDecimal(),
                netRevenue = 4.233.toBigDecimal(),
                cancelledProductsCount = 0,
                cancelledProductsExpense = 0.toBigDecimal()
            )
        )
        every { searchProductBasketItemQueryService(any()) } returns PageImpl(
            listOf(
                AdminSearchProductBasketItemsResponse(
                    id = basketItemId,
                    type = BasketItemType.PRODUCT,
                    productReceiptNumber = "1000000001",
                    quantity = 2,
                    price = 10.5.toBigDecimal(),
                    isCancelled = false,
                    branchName = "Bratislava Bory",
                    basket = AdminSearchProductBasketItemsResponse.BasketResponse(
                        id = basketId,
                        paidAt = LocalDateTime.of(2024, 8, 27, 23, 1, 30),
                        paymentType = PaymentType.CASHLESS,
                        paymentPosConfiguration = AdminSearchProductBasketItemsResponse.PosConfigurationResponse(
                            id = posConfigurationId,
                            title = "pokl1"
                        ),
                        updatedBy = "monika"
                    ),
                    product = AdminSearchProductBasketItemsResponse.ProductResponse(
                        id = productId,
                        title = "Fanta PET 0.5l",
                        code = "0987",
                        productCategory = AdminSearchProductBasketItemsResponse.ProductCategoryResponse(
                            id = productCategoryId,
                            type = ProductCategoryType.PRODUCT,
                            title = "Napoje"
                        )
                    ),
                    discountCard = AdminSearchProductBasketItemsResponse.DiscountCardResponse(
                        id = discountCardId,
                        type = DiscountCardType.CARD,
                        code = "123456678",
                        title = "FILM karta"
                    ),
                    card = AdminSearchProductBasketItemsResponse.CardResponse(
                        id = discountCardId,
                        code = "123456678",
                        title = "FILM karta"
                    )
                )
            )
        )

        mvc.post("$BASE_PATH/products-search?page=1&size=10&sort=discountCard.code") {
            header(HttpHeaders.ACCEPT, ApiVersion.VERSION_1_JSON)
            header(HttpHeaders.AUTHORIZATION, mockAccessToken(EmployeeRole.MANAGER))
            contentType = MediaType.APPLICATION_JSON
            content = """
                {
                  "basketPaidAtFrom": "2023-08-15T14:30:00Z",
                  "basketPaidAtTo": "2023-08-20T18:45:00Z",
                  "posConfigurationIds": [
                    "$posConfigurationId"
                  ],
                  "basketUpdatedBy": ["monika"],
                  "productReceiptNumber": "1000000001",
                  "productIds": [
                    "$productId"
                  ],
                  "productCategoryIds": [
                    "$productCategoryId"
                  ],
                  "productCode": "123456678",
                  "paymentTypes": [
                    "CASHLESS"
                  ],
                  "discountCardCode": "123456678",
                  "discountCardTypes": [
                    "CARD"
                  ],
                  "discountCardTitle": "Slovak Telekom",
                  "isDiscount": true,
                  "isCancelled": false
                }
            """.trimIndent()
        }.andExpect {
            status { isOk() }
            content { contentType(ApiVersion.VERSION_1_JSON) }
            jsonContent(
                """
                    {
                        "summary": {
                          "sales": {
                              "productsCount": 2,
                              "basketsCount": 1,
                              "purchasePriceSum": 1.095,
                              "salesCash": 6.45,
                              "salesCashless": 0.0,
                              "grossSales": 6.45,
                              "netSales": 5.328,
                              "taxAmount": 1.122,
                              "grossRevenue": 5.355,
                              "netRevenue": 4.233,
                              "cancelledProductsCount": 0,
                              "cancelledProductsExpense": 0.0
                          }
                        },
                        "results": {
                          "content": [
                            {
                              "id": "$basketItemId",
                              "type": "PRODUCT",
                              "productReceiptNumber": "1000000001",
                              "quantity": 2,
                              "price": 10.5,
                              "isCancelled": false,
                              "branchName": "Bratislava Bory",
                              "basket": {
                                "id": "$basketId",
                                "paidAt": "2024-08-27T21:01:30Z",
                                "paymentType": "CASHLESS",
                                "paymentPosConfiguration": {
                                  "id": "$posConfigurationId",
                                  "title": "pokl1"
                                },
                                "updatedBy": "monika"
                              },
                              "product": {
                                "id": "$productId",
                                "title": "Fanta PET 0.5l",
                                "code": "0987",
                                "productCategory": {
                                  "id": "$productCategoryId",
                                  "type": "PRODUCT",
                                  "title": "Napoje"
                                }
                              },
                              "discountCard": {
                                "id": "$discountCardId",
                                "type": "CARD",
                                "code": "123456678",
                                "title": "FILM karta"
                              },
                              "card": {
                                "id": "$discountCardId",
                                "code": "123456678",
                                "title": "FILM karta"
                              }
                            }
                          ],
                          "totalElements": 1,
                          "totalPages": 1
                        }
                    }
                """.trimIndent()
            )
        }

        verifySequence {
            searchProductBasketItemQueryService(
                query = AdminSearchProductBasketItemsQuery(
                    pageable = PageRequest.of(1, 10, Sort.by("discountCard.code")),
                    filter = AdminSearchProductBasketItemsFilter(
                        basketPaidAtFrom = LocalDateTime.of(2023, 8, 15, 16, 30, 0),
                        basketPaidAtTo = LocalDateTime.of(2023, 8, 20, 20, 45, 0),
                        posConfigurationIds = setOf(posConfigurationId),
                        basketUpdatedBy = setOf("monika"),
                        productReceiptNumber = "1000000001",
                        productIds = setOf(productId),
                        productCategoryIds = setOf(productCategoryId),
                        productCode = "123456678",
                        paymentTypes = setOf(PaymentType.CASHLESS),
                        discountCardCode = "123456678",
                        discountCardTypes = setOf(DiscountCardType.CARD),
                        discountCardTitle = "Slovak Telekom",
                        isDiscount = true,
                        isCancelled = false
                    )
                )
            )
        }
    }

    @RetryingTest(5)
    fun `test export ticket basket items, should call service and return excel file`() {
        val posConfigurationId = UUID.fromString("f055fbce-f17a-4bb6-82b9-a1d11ceffa3c")
        val primaryTicketDiscountId = UUID.fromString("1a69acad-00cf-481b-a044-d422f609de21")
        val secondaryTicketDiscountId = UUID.fromString("bb259a48-6633-41d2-b98f-3749bcd8f49b")
        val screeningId = UUID.fromString("76044133-be6a-4dba-8e9e-6f9bfebd65a2")
        val screeningTypeId = UUID.fromString("7664a779-df0f-4887-ac8c-34c795d6a70a")
        val technologyId = UUID.fromString("bf182762-a148-4c20-a01a-d1d3eb447c59")
        val productionId = UUID.fromString("a6b69d3f-8cef-4d1b-92ed-973222d7a040")
        val auditoriumId = UUID.fromString("b6eabf8a-05db-4443-ae1e-e2f564961b4b")
        val moveId = UUID.fromString("99c306b5-ccab-4301-815f-4287e49cfc24")
        val distributorId = UUID.fromString("7f1947db-7c99-413a-ba32-1da7885a54e3")

        val filter = AdminSearchTicketBasketItemsFilter(
            basketPaidAtFrom = LocalDateTime.of(2023, 8, 15, 16, 30, 0),
            basketPaidAtTo = LocalDateTime.of(2023, 8, 20, 20, 45, 0),
            basketUpdatedBy = setOf("monika"),
            screeningDateTimeFrom = LocalDateTime.of(2023, 8, 25, 14, 0, 0),
            screeningDateTimeTo = LocalDateTime.of(2023, 8, 25, 16, 30, 0),
            auditoriumIds = setOf(auditoriumId),
            screeningIds = setOf(screeningId),
            movieIds = setOf(moveId),
            distributorIds = setOf(distributorId),
            posConfigurationIds = setOf(posConfigurationId),
            paymentTypes = setOf(PaymentType.CASHLESS),
            ticketUsed = true,
            surchargeTypes = setOf(SurchargeType.VIP, SurchargeType.IMAX),
            serviceFeeTypes = setOf(ServiceFeeType.PREMIUM_PLUS, ServiceFeeType.GENERAL),
            screeningTypeIds = setOf(screeningTypeId),
            technologyIds = setOf(technologyId),
            productionIds = setOf(productionId),
            ticketReceiptNumbers = setOf("1000000001"),
            discountCardCodes = setOf("123456678"),
            discountCardTitles = setOf("Slovak Telekom"),
            isDiscounted = true,
            primaryTicketDiscountIds = setOf(primaryTicketDiscountId),
            secondaryTicketDiscountIds = setOf(secondaryTicketDiscountId),
            isCancelled = false
        )
        val byteArray = ByteArray(1024)
        val inputStream = ByteArrayInputStream(byteArray)
        val exportResult = ExportResultModel(inputStream, byteArray.size.toLong())

        every { ticketBasketItemExportService.exportTicketBasketItems(any()) } returns exportResult

        mvc.post("$BASE_PATH/tickets-search/export/${ExportFormat.XLSX}?page=1&size=10&sort=distributor.title") {
            contentType = MediaType.APPLICATION_JSON
            header(HttpHeaders.ACCEPT, ApiVersion.VERSION_1_XLSX)
            header(HttpHeaders.AUTHORIZATION, mockAccessToken(role = EmployeeRole.MANAGER))
            content = """
                {
                  "basketPaidAtFrom": "2023-08-15T14:30:00Z",
                  "basketPaidAtTo": "2023-08-20T18:45:00Z",
                  "basketUpdatedBy": ["monika"],
                  "screeningDateTimeFrom": "2023-08-25T12:00:00Z",
                  "screeningDateTimeTo": "2023-08-25T14:30:00Z",
                  "auditoriumIds": [
                    "$auditoriumId"
                  ],
                  "screeningIds": [
                    "$screeningId"
                  ],
                  "movieIds": [
                    "$moveId"
                  ],
                  "distributorIds": [
                    "$distributorId"
                  ],
                  "posConfigurationIds": [
                    "$posConfigurationId"
                  ],
                  "paymentTypes": [
                    "CASHLESS"
                  ],
                  "ticketUsed": true,
                  "surchargeTypes": [
                    "VIP",
                    "IMAX"
                  ],
                  "serviceFeeTypes": [
                    "PREMIUM_PLUS",
                    "GENERAL"
                  ],
                  "screeningTypeIds": [
                    "$screeningTypeId"
                  ],
                  "technologyIds": [
                    "$technologyId"
                  ],
                  "productionIds": [
                    "$productionId"
                  ],
                  "ticketReceiptNumbers": [
                    "1000000001"
                  ],
                  "discountCardCodes": [
                    "123456678"
                  ],
                  "discountCardTitles": [
                    "Slovak Telekom"
                  ],
                  "isDiscounted": true,
                  "primaryTicketDiscountIds": [
                    "$primaryTicketDiscountId"
                  ],
                  "secondaryTicketDiscountIds": [
                    "$secondaryTicketDiscountId"
                  ],
                  "isCancelled": false
                }
            """.trimIndent()
        }.andExpect {
            status { isOk() }
            header {
                string(HttpHeaders.CONTENT_TYPE, ApiVersion.VERSION_1_XLSX)
                string(HttpHeaders.CONTENT_DISPOSITION, CoreMatchers.containsString("attachment"))
                longValue(HttpHeaders.CONTENT_LENGTH, byteArray.size.toLong())
            }
        }

        verify {
            ticketBasketItemExportService.exportTicketBasketItems(
                AdminExportTicketBasketItemsQuery(
                    pageable = Pageable.unpaged(Sort.by("distributor.title")),
                    filter = filter,
                    exportFormat = ExportFormat.XLSX,
                    username = "anonymous"
                )
            )
        }
    }

    @RetryingTest(5)
    fun `test export product basket items, should call service and return excel file`() {
        val posConfigurationId = 1.toUUID()
        val productId = 2.toUUID()
        val productCategoryId = 3.toUUID()

        val filter = AdminSearchProductBasketItemsFilter(
            basketPaidAtFrom = LocalDateTime.of(2023, 8, 15, 16, 30, 0),
            basketPaidAtTo = LocalDateTime.of(2023, 8, 20, 20, 45, 0),
            posConfigurationIds = setOf(posConfigurationId),
            basketUpdatedBy = setOf("monika"),
            productReceiptNumber = "1000000001",
            productIds = setOf(productId),
            productCategoryIds = setOf(productCategoryId),
            productCode = "123456678",
            paymentTypes = setOf(PaymentType.CASHLESS),
            discountCardCode = "987654321",
            discountCardTypes = setOf(DiscountCardType.CARD),
            discountCardTitle = "FILM karta",
            isDiscount = true,
            isCancelled = false
        )

        val byteArray = ByteArray(1024)
        val inputStream = ByteArrayInputStream(byteArray)
        val exportResult = ExportResultModel(inputStream, byteArray.size.toLong())

        every { productBasketItemExportService.exportProductBasketItems(any()) } returns exportResult

        mvc.post("$BASE_PATH/products-search/export/${ExportFormat.XLSX}?page=1&size=10&sort=discountCard.code") {
            contentType = MediaType.APPLICATION_JSON
            header(HttpHeaders.ACCEPT, ApiVersion.VERSION_1_XLSX)
            header(HttpHeaders.AUTHORIZATION, mockAccessToken(role = EmployeeRole.MANAGER))
            content = """
                {
                  "basketPaidAtFrom": "2023-08-15T14:30:00Z",
                  "basketPaidAtTo": "2023-08-20T18:45:00Z",
                  "posConfigurationIds": [
                    "$posConfigurationId"
                  ],
                  "productReceiptNumber": "1000000001",
                  "productIds": [
                    "$productId"
                  ],
                  "productCategoryIds": [
                    "$productCategoryId"
                  ],
                  "basketUpdatedBy": ["monika"],
                  "productCode": "123456678",
                  "paymentTypes": [
                    "CASHLESS"
                  ],
                  "discountCardCode": "987654321",
                  "discountCardTypes": [
                    "CARD"
                  ],
                  "discountCardTitle": "FILM karta",
                  "isDiscount": true,
                  "isCancelled": false
                }
            """.trimIndent()
        }.andExpect {
            status { isOk() }
            header {
                string(HttpHeaders.CONTENT_TYPE, ApiVersion.VERSION_1_XLSX)
                string(HttpHeaders.CONTENT_DISPOSITION, CoreMatchers.containsString("attachment"))
                longValue(HttpHeaders.CONTENT_LENGTH, byteArray.size.toLong())
            }
        }

        verify {
            productBasketItemExportService.exportProductBasketItems(
                AdminExportProductBasketItemsQuery(
                    pageable = Pageable.unpaged(Sort.by("discountCard.code")),
                    filter = filter,
                    exportFormat = ExportFormat.XLSX,
                    username = "anonymous"
                )
            )
        }
    }

    @Test
    fun `test searchBranchSalesOverview - should serialize and deserialize correctly`() {
        val response = AdminBranchSalesOverviewResponse(
            branches = listOf(
                BranchSalesOverview(
                    code = "BRY",
                    title = "Bratislava Bory",
                    screenings = TotalSalesOverview(
                        selectedYearTotal = 1200.toBigDecimal(),
                        previousYearTotal = 1000.toBigDecimal(),
                        change = 200.toBigDecimal(),
                        changePercentage = 20.toBigDecimal(),
                        months = listOf(
                            MonthlySalesOverview(
                                monthStartDate = LocalDate.of(2023, 1, 1),
                                value = 110.toBigDecimal()
                            ),
                            MonthlySalesOverview(
                                monthStartDate = LocalDate.of(2023, 2, 1),
                                value = 120.toBigDecimal()
                            )
                        )
                    ),
                    tickets = TotalSalesOverview(
                        selectedYearTotal = 15000.toBigDecimal(),
                        previousYearTotal = 13000.toBigDecimal(),
                        change = 2000.toBigDecimal(),
                        changePercentage = 15.38.toBigDecimal(),
                        months = listOf(
                            MonthlySalesOverview(
                                monthStartDate = LocalDate.of(2023, 1, 1),
                                value = 1400.toBigDecimal()
                            ),
                            MonthlySalesOverview(
                                monthStartDate = LocalDate.of(2023, 2, 1),
                                value = 1500.toBigDecimal()
                            )
                        )
                    ),
                    ticketsGrossSales = TotalSalesOverview(
                        selectedYearTotal = 150000.toBigDecimal(),
                        previousYearTotal = 130000.toBigDecimal(),
                        change = 20000.toBigDecimal(),
                        changePercentage = 15.38.toBigDecimal(),
                        months = listOf(
                            MonthlySalesOverview(
                                monthStartDate = LocalDate.of(2023, 1, 1),
                                value = 14000.toBigDecimal()
                            ),
                            MonthlySalesOverview(
                                monthStartDate = LocalDate.of(2023, 2, 1),
                                value = 15000.toBigDecimal()
                            )
                        )
                    ),
                    ticketsAverageSales = TotalSalesOverview(
                        selectedYearTotal = 10.toBigDecimal(),
                        previousYearTotal = 10.toBigDecimal(),
                        change = 0.toBigDecimal(),
                        changePercentage = 0.toBigDecimal(),
                        months = listOf(
                            MonthlySalesOverview(
                                monthStartDate = LocalDate.of(2023, 1, 1),
                                value = 10.toBigDecimal()
                            ),
                            MonthlySalesOverview(
                                monthStartDate = LocalDate.of(2023, 2, 1),
                                value = 10.toBigDecimal()
                            )
                        )
                    ),
                    productsGrossSales = TotalSalesOverview(
                        selectedYearTotal = 50000.toBigDecimal(),
                        previousYearTotal = 45000.toBigDecimal(),
                        change = 5000.toBigDecimal(),
                        changePercentage = 11.11.toBigDecimal(),
                        months = listOf(
                            MonthlySalesOverview(
                                monthStartDate = LocalDate.of(2023, 1, 1),
                                value = 4500.toBigDecimal()
                            ),
                            MonthlySalesOverview(
                                monthStartDate = LocalDate.of(2023, 2, 1),
                                value = 5000.toBigDecimal()
                            )
                        )
                    ),
                    productsAverageSales = TotalSalesOverview(
                        selectedYearTotal = 5.toBigDecimal(),
                        previousYearTotal = 5.toBigDecimal(),
                        change = 0.toBigDecimal(),
                        changePercentage = 0.toBigDecimal(),
                        months = listOf(
                            MonthlySalesOverview(
                                monthStartDate = LocalDate.of(2023, 1, 1),
                                value = 5.toBigDecimal()
                            ),
                            MonthlySalesOverview(
                                monthStartDate = LocalDate.of(2023, 2, 1),
                                value = 5.toBigDecimal()
                            )
                        )
                    ),
                    productsVipGrossSales = TotalSalesOverview(
                        selectedYearTotal = 20000.toBigDecimal(),
                        previousYearTotal = 18000.toBigDecimal(),
                        change = 2000.toBigDecimal(),
                        changePercentage = 11.11.toBigDecimal(),
                        months = listOf(
                            MonthlySalesOverview(
                                monthStartDate = LocalDate.of(2023, 1, 1),
                                value = 1800.toBigDecimal()
                            ),
                            MonthlySalesOverview(
                                monthStartDate = LocalDate.of(2023, 2, 1),
                                value = 2000.toBigDecimal()
                            )
                        )
                    ),
                    productsVipAverageSales = TotalSalesOverview(
                        selectedYearTotal = 20.toBigDecimal(),
                        previousYearTotal = 20.toBigDecimal(),
                        change = 0.toBigDecimal(),
                        changePercentage = 0.toBigDecimal(),
                        months = listOf(
                            MonthlySalesOverview(
                                monthStartDate = LocalDate.of(2023, 1, 1),
                                value = 20.toBigDecimal()
                            ),
                            MonthlySalesOverview(
                                monthStartDate = LocalDate.of(2023, 2, 1),
                                value = 20.toBigDecimal()
                            )
                        )
                    ),
                    totalGrossSales = TotalSalesOverview(
                        selectedYearTotal = 220000.toBigDecimal(),
                        previousYearTotal = 193000.toBigDecimal(),
                        change = 27000.toBigDecimal(),
                        changePercentage = 13.99.toBigDecimal(),
                        months = listOf(
                            MonthlySalesOverview(
                                monthStartDate = LocalDate.of(2023, 1, 1),
                                value = 20300.toBigDecimal()
                            ),
                            MonthlySalesOverview(
                                monthStartDate = LocalDate.of(2023, 2, 1),
                                value = 22000.toBigDecimal()
                            )
                        )
                    )
                ),
                BranchSalesOverview(
                    code = "PNY",
                    title = "Piešťany",
                    screenings = TotalSalesOverview(
                        selectedYearTotal = 800.toBigDecimal(),
                        previousYearTotal = 700.toBigDecimal(),
                        change = 100.toBigDecimal(),
                        changePercentage = 14.29.toBigDecimal(),
                        months = listOf(
                            MonthlySalesOverview(
                                monthStartDate = LocalDate.of(2023, 1, 1),
                                value = 80.toBigDecimal()
                            ),
                            MonthlySalesOverview(
                                monthStartDate = LocalDate.of(2023, 2, 1),
                                value = 85.toBigDecimal()
                            )
                        )
                    ),
                    tickets = TotalSalesOverview(
                        selectedYearTotal = 10000.toBigDecimal(),
                        previousYearTotal = 9000.toBigDecimal(),
                        change = 1000.toBigDecimal(),
                        changePercentage = 11.11.toBigDecimal(),
                        months = listOf(
                            MonthlySalesOverview(
                                monthStartDate = LocalDate.of(2023, 1, 1),
                                value = 950.toBigDecimal()
                            ),
                            MonthlySalesOverview(
                                monthStartDate = LocalDate.of(2023, 2, 1),
                                value = 1000.toBigDecimal()
                            )
                        )
                    ),
                    ticketsGrossSales = TotalSalesOverview(
                        selectedYearTotal = 100000.toBigDecimal(),
                        previousYearTotal = 90000.toBigDecimal(),
                        change = 10000.toBigDecimal(),
                        changePercentage = 11.11.toBigDecimal(),
                        months = listOf(
                            MonthlySalesOverview(
                                monthStartDate = LocalDate.of(2023, 1, 1),
                                value = 9500.toBigDecimal()
                            ),
                            MonthlySalesOverview(
                                monthStartDate = LocalDate.of(2023, 2, 1),
                                value = 10000.toBigDecimal()
                            )
                        )
                    ),
                    ticketsAverageSales = TotalSalesOverview(
                        selectedYearTotal = 10.toBigDecimal(),
                        previousYearTotal = 10.toBigDecimal(),
                        change = 0.toBigDecimal(),
                        changePercentage = 0.toBigDecimal(),
                        months = listOf(
                            MonthlySalesOverview(
                                monthStartDate = LocalDate.of(2023, 1, 1),
                                value = 10.toBigDecimal()
                            ),
                            MonthlySalesOverview(
                                monthStartDate = LocalDate.of(2023, 2, 1),
                                value = 10.toBigDecimal()
                            )
                        )
                    ),
                    productsGrossSales = TotalSalesOverview(
                        selectedYearTotal = 35000.toBigDecimal(),
                        previousYearTotal = 30000.toBigDecimal(),
                        change = 5000.toBigDecimal(),
                        changePercentage = 16.67.toBigDecimal(),
                        months = listOf(
                            MonthlySalesOverview(
                                monthStartDate = LocalDate.of(2023, 1, 1),
                                value = 3000.toBigDecimal()
                            ),
                            MonthlySalesOverview(
                                monthStartDate = LocalDate.of(2023, 2, 1),
                                value = 3500.toBigDecimal()
                            )
                        )
                    ),
                    productsAverageSales = TotalSalesOverview(
                        selectedYearTotal = 5.toBigDecimal(),
                        previousYearTotal = 5.toBigDecimal(),
                        change = 0.toBigDecimal(),
                        changePercentage = 0.toBigDecimal(),
                        months = listOf(
                            MonthlySalesOverview(
                                monthStartDate = LocalDate.of(2023, 1, 1),
                                value = 5.toBigDecimal()
                            ),
                            MonthlySalesOverview(
                                monthStartDate = LocalDate.of(2023, 2, 1),
                                value = 5.toBigDecimal()
                            )
                        )
                    ),
                    productsVipGrossSales = TotalSalesOverview(
                        selectedYearTotal = 12000.toBigDecimal(),
                        previousYearTotal = 10000.toBigDecimal(),
                        change = 2000.toBigDecimal(),
                        changePercentage = 20.toBigDecimal(),
                        months = listOf(
                            MonthlySalesOverview(
                                monthStartDate = LocalDate.of(2023, 1, 1),
                                value = 1000.toBigDecimal()
                            ),
                            MonthlySalesOverview(
                                monthStartDate = LocalDate.of(2023, 2, 1),
                                value = 1200.toBigDecimal()
                            )
                        )
                    ),
                    productsVipAverageSales = TotalSalesOverview(
                        selectedYearTotal = 15.toBigDecimal(),
                        previousYearTotal = 15.toBigDecimal(),
                        change = 0.toBigDecimal(),
                        changePercentage = 0.toBigDecimal(),
                        months = listOf(
                            MonthlySalesOverview(
                                monthStartDate = LocalDate.of(2023, 1, 1),
                                value = 15.toBigDecimal()
                            ),
                            MonthlySalesOverview(
                                monthStartDate = LocalDate.of(2023, 2, 1),
                                value = 15.toBigDecimal()
                            )
                        )
                    ),
                    totalGrossSales = TotalSalesOverview(
                        selectedYearTotal = 147000.toBigDecimal(),
                        previousYearTotal = 130000.toBigDecimal(),
                        change = 17000.toBigDecimal(),
                        changePercentage = 13.08.toBigDecimal(),
                        months = listOf(
                            MonthlySalesOverview(
                                monthStartDate = LocalDate.of(2023, 1, 1),
                                value = 13500.toBigDecimal()
                            ),
                            MonthlySalesOverview(
                                monthStartDate = LocalDate.of(2023, 2, 1),
                                value = 14700.toBigDecimal()
                            )
                        )
                    )
                )
            )
        )

        every { adminBranchSalesOverviewQueryService(any()) } returns response

        mvc.post("$BASE_PATH/sales-overview-search") {
            header(HttpHeaders.ACCEPT, ApiVersion.VERSION_1_JSON)
            header(HttpHeaders.AUTHORIZATION, mockAccessToken(EmployeeRole.MANAGER))
            contentType = MediaType.APPLICATION_JSON
            content = """
            {
              "selectedYear": 2023,
              "branchIds": [
                "${1.toUUID()}",
                "${2.toUUID()}"
              ]
            }
            """.trimIndent()
        }.andExpect {
            status { isOk() }
            content { contentType(ApiVersion.VERSION_1_JSON) }
            jsonContent(
                """
                {
                  "branches": [
                    {
                      "code": "BRY",
                      "title": "Bratislava Bory",
                      "screenings": {
                        "selectedYearTotal": 1200,
                        "previousYearTotal": 1000,
                        "change": 200,
                        "changePercentage": 20.00,
                        "months": [
                          {
                            "monthStartDate": "2023-01-01",
                            "value": 110
                          },
                          {
                            "monthStartDate": "2023-02-01",
                            "value": 120
                          }
                        ]
                      },
                      "tickets": {
                        "selectedYearTotal": 15000,
                        "previousYearTotal": 13000,
                        "change": 2000,
                        "changePercentage": 15.38,
                        "months": [
                          {
                            "monthStartDate": "2023-01-01",
                            "value": 1400
                          },
                          {
                            "monthStartDate": "2023-02-01",
                            "value": 1500
                          }
                        ]
                      },
                      "ticketsGrossSales": {
                        "selectedYearTotal": 150000,
                        "previousYearTotal": 130000,
                        "change": 20000,
                        "changePercentage": 15.38,
                        "months": [
                          {
                            "monthStartDate": "2023-01-01",
                            "value": 14000
                          },
                          {
                            "monthStartDate": "2023-02-01",
                            "value": 15000
                          }
                        ]
                      },
                      "ticketsAverageSales": {
                        "selectedYearTotal": 10,
                        "previousYearTotal": 10,
                        "change": 0,
                        "changePercentage": 0.00,
                        "months": [
                          {
                            "monthStartDate": "2023-01-01",
                            "value": 10
                          },
                          {
                            "monthStartDate": "2023-02-01",
                            "value": 10
                          }
                        ]
                      },
                      "productsGrossSales": {
                        "selectedYearTotal": 50000,
                        "previousYearTotal": 45000,
                        "change": 5000,
                        "changePercentage": 11.11,
                        "months": [
                          {
                            "monthStartDate": "2023-01-01",
                            "value": 4500
                          },
                          {
                            "monthStartDate": "2023-02-01",
                            "value": 5000
                          }
                        ]
                      },
                      "productsAverageSales": {
                        "selectedYearTotal": 5,
                        "previousYearTotal": 5,
                        "change": 0,
                        "changePercentage": 0.00,
                        "months": [
                          {
                            "monthStartDate": "2023-01-01",
                            "value": 5
                          },
                          {
                            "monthStartDate": "2023-02-01",
                            "value": 5
                          }
                        ]
                      },
                      "productsVipGrossSales": {
                        "selectedYearTotal": 20000,
                        "previousYearTotal": 18000,
                        "change": 2000,
                        "changePercentage": 11.11,
                        "months": [
                          {
                            "monthStartDate": "2023-01-01",
                            "value": 1800
                          },
                          {
                            "monthStartDate": "2023-02-01",
                            "value": 2000
                          }
                        ]
                      },
                      "productsVipAverageSales": {
                        "selectedYearTotal": 20,
                        "previousYearTotal": 20,
                        "change": 0,
                        "changePercentage": 0.00,
                        "months": [
                          {
                            "monthStartDate": "2023-01-01",
                            "value": 20
                          },
                          {
                            "monthStartDate": "2023-02-01",
                            "value": 20
                          }
                        ]
                      },
                      "totalGrossSales": {
                        "selectedYearTotal": 220000,
                        "previousYearTotal": 193000,
                        "change": 27000,
                        "changePercentage": 13.99,
                        "months": [
                          {
                            "monthStartDate": "2023-01-01",
                            "value": 20300
                          },
                          {
                            "monthStartDate": "2023-02-01",
                            "value": 22000
                          }
                        ]
                      }
                    },
                    {
                      "code": "PNY",
                      "title": "Piešťany",
                      "screenings": {
                        "selectedYearTotal": 800,
                        "previousYearTotal": 700,
                        "change": 100,
                        "changePercentage": 14.29,
                        "months": [
                          {
                            "monthStartDate": "2023-01-01",
                            "value": 80
                          },
                          {
                            "monthStartDate": "2023-02-01",
                            "value": 85
                          }
                        ]
                      },
                      "tickets": {
                        "selectedYearTotal": 10000,
                        "previousYearTotal": 9000,
                        "change": 1000,
                        "changePercentage": 11.11,
                        "months": [
                          {
                            "monthStartDate": "2023-01-01",
                            "value": 950
                          },
                          {
                            "monthStartDate": "2023-02-01",
                            "value": 1000
                          }
                        ]
                      },
                      "ticketsGrossSales": {
                        "selectedYearTotal": 100000,
                        "previousYearTotal": 90000,
                        "change": 10000,
                        "changePercentage": 11.11,
                        "months": [
                          {
                            "monthStartDate": "2023-01-01",
                            "value": 9500
                          },
                          {
                            "monthStartDate": "2023-02-01",
                            "value": 10000
                          }
                        ]
                      },
                      "ticketsAverageSales": {
                        "selectedYearTotal": 10,
                        "previousYearTotal": 10,
                        "change": 0,
                        "changePercentage": 0.00,
                        "months": [
                          {
                            "monthStartDate": "2023-01-01",
                            "value": 10
                          },
                          {
                            "monthStartDate": "2023-02-01",
                            "value": 10
                          }
                        ]
                      },
                      "productsGrossSales": {
                        "selectedYearTotal": 35000,
                        "previousYearTotal": 30000,
                        "change": 5000,
                        "changePercentage": 16.67,
                        "months": [
                          {
                            "monthStartDate": "2023-01-01",
                            "value": 3000
                          },
                          {
                            "monthStartDate": "2023-02-01",
                            "value": 3500
                          }
                        ]
                      },
                      "productsAverageSales": {
                        "selectedYearTotal": 5,
                        "previousYearTotal": 5,
                        "change": 0,
                        "changePercentage": 0.00,
                        "months": [
                          {
                            "monthStartDate": "2023-01-01",
                            "value": 5
                          },
                          {
                            "monthStartDate": "2023-02-01",
                            "value": 5
                          }
                        ]
                      },
                      "productsVipGrossSales": {
                        "selectedYearTotal": 12000,
                        "previousYearTotal": 10000,
                        "change": 2000,
                        "changePercentage": 20.00,
                        "months": [
                          {
                            "monthStartDate": "2023-01-01",
                            "value": 1000
                          },
                          {
                            "monthStartDate": "2023-02-01",
                            "value": 1200
                          }
                        ]
                      },
                      "productsVipAverageSales": {
                        "selectedYearTotal": 15,
                        "previousYearTotal": 15,
                        "change": 0,
                        "changePercentage": 0.00,
                        "months": [
                          {
                            "monthStartDate": "2023-01-01",
                            "value": 15
                          },
                          {
                            "monthStartDate": "2023-02-01",
                            "value": 15
                          }
                        ]
                      },
                      "totalGrossSales": {
                        "selectedYearTotal": 147000,
                        "previousYearTotal": 130000,
                        "change": 17000,
                        "changePercentage": 13.08,
                        "months": [
                          {
                            "monthStartDate": "2023-01-01",
                            "value": 13500
                          },
                          {
                            "monthStartDate": "2023-02-01",
                            "value": 14700
                          }
                        ]
                      }
                    }
                  ]
                }
                """.trimIndent()
            )
        }

        verifySequence {
            adminBranchSalesOverviewQueryService(
                AdminBranchSalesOverviewQuery(
                    selectedYear = 2023,
                    branchIds = setOf(1.toUUID(), 2.toUUID())
                )
            )
        }
    }
}

private const val BASE_PATH = "/manager-app/baskets/basket-items"
