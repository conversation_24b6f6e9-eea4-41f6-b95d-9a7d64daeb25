package com.cleevio.cinemax.api.module.outboxevent.event.listener

import com.cleevio.cinemax.api.module.outboxevent.constant.OutboxEventType
import com.cleevio.cinemax.api.module.outboxevent.service.OutboxEventService
import com.cleevio.cinemax.api.module.outboxevent.service.command.CreateOutboxEventsCommand
import com.cleevio.cinemax.api.module.stockmovement.event.ProductSalesStockMovementsCreatedEvent
import com.cleevio.cinemax.api.util.toUUID
import io.mockk.Runs
import io.mockk.every
import io.mockk.just
import io.mockk.mockk
import io.mockk.verify
import org.junit.jupiter.api.Test

class StockMovementOutboxEventEventListenerTest {

    private val outboxEventService = mockk<OutboxEventService>()
    private val underTest = StockMovementOutboxEventEventListener(
        outboxEventService = outboxEventService
    )

    @Test
    fun `test listenToProductSalesStockMovementsCreatedEvent should create outbox event for all product component ids`() {
        val productComponentIds = setOf(1.toUUID(), 2.toUUID(), 3.toUUID())
        val event = ProductSalesStockMovementsCreatedEvent(productComponentIds)
        every { outboxEventService.createOutboxEvents(any()) } just Runs

        underTest.listenToProductSalesStockMovementsCreatedEvent(event)

        verify(exactly = 1) {
            outboxEventService.createOutboxEvents(
                CreateOutboxEventsCommand(
                    entityIds = productComponentIds,
                    type = OutboxEventType.PRODUCT_SALES_STOCK_MOVEMENTS_CREATED
                )
            )
        }
    }
}
