package com.cleevio.cinemax.api.module.production.controller

import com.cleevio.cinemax.api.ControllerTest
import com.cleevio.cinemax.api.common.constant.ApiVersion
import com.cleevio.cinemax.api.common.descriptor.mssql.DescriptorMssqlResponse
import com.cleevio.cinemax.api.common.util.jsonContent
import com.cleevio.cinemax.api.module.employee.constant.EmployeeRole
import com.cleevio.cinemax.api.module.production.entity.Production
import com.cleevio.cinemax.api.util.mockAccessToken
import io.mockk.every
import io.mockk.verify
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest
import org.springframework.http.HttpHeaders
import org.springframework.test.web.servlet.MockMvc
import org.springframework.test.web.servlet.get

@WebMvcTest(AdminProductionController::class)
class AdminProductionControllerTest @Autowired constructor(
    private val mvc: MockMvc,
) : ControllerTest() {

    @Test
    fun `test getProductions, should serialize and deserialize correctly`() {
        every { productionFinderService.findAll() } returns listOf(PRODUCTION_1, PRODUCTION_2, PRODUCTION_3)
        every { descriptorMssqlResponseMapper.mapList(any()) } returns listOf(
            PRODUCTION_1_RESPONSE,
            PRODUCTION_2_RESPONSE,
            PRODUCTION_3_RESPONSE
        )

        mvc.get(GET_PRODUCTIONS_PATH) {
            header(HttpHeaders.ACCEPT, ApiVersion.VERSION_1_JSON)
            header(HttpHeaders.AUTHORIZATION, mockAccessToken(role = EmployeeRole.MANAGER))
        }.andExpect {
            status { isOk() }
            content { contentType(ApiVersion.VERSION_1_JSON) }
            jsonContent(
                """
                [{
                  "id": "${PRODUCTION_1.id}",
                  "title": "${PRODUCTION_1.title}"
                },
                {
                  "id": "${PRODUCTION_2.id}",
                  "title": "${PRODUCTION_2.title}"
                },
                {
                  "id": "${PRODUCTION_3.id}",
                  "title": "${PRODUCTION_3.title}"
                }]
            """
            )
        }

        verify {
            descriptorMssqlResponseMapper.mapList(listOf(PRODUCTION_1, PRODUCTION_2, PRODUCTION_3))
        }
    }
}

private const val GET_PRODUCTIONS_PATH = "/manager-app/productions"
private val PRODUCTION_1 = Production(
    originalId = 65,
    code = "01",
    title = "Česká Republika"
)
private val PRODUCTION_2 = Production(
    originalId = 66,
    code = "02",
    title = "Slovenská republika"
)
private val PRODUCTION_3 = Production(
    originalId = 141,
    code = "a",
    title = "Ukrajina"
)
private val PRODUCTION_1_RESPONSE = DescriptorMssqlResponse(
    id = PRODUCTION_1.id,
    title = PRODUCTION_1.title
)
private val PRODUCTION_2_RESPONSE = DescriptorMssqlResponse(
    id = PRODUCTION_2.id,
    title = PRODUCTION_2.title
)
private val PRODUCTION_3_RESPONSE = DescriptorMssqlResponse(
    id = PRODUCTION_3.id,
    title = PRODUCTION_3.title
)
