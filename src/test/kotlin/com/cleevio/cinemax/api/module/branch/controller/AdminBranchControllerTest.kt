package com.cleevio.cinemax.api.module.branch.controller

import com.cleevio.cinemax.api.ControllerTest
import com.cleevio.cinemax.api.common.constant.ApiVersion
import com.cleevio.cinemax.api.common.util.jsonContent
import com.cleevio.cinemax.api.module.branch.controller.dto.AdminGetBranchesResponse
import com.cleevio.cinemax.api.module.employee.constant.EmployeeRole
import com.cleevio.cinemax.api.util.createBranch
import com.cleevio.cinemax.api.util.mockAccessToken
import io.mockk.every
import io.mockk.verify
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest
import org.springframework.http.HttpHeaders
import org.springframework.http.MediaType
import org.springframework.test.web.servlet.MockMvc
import org.springframework.test.web.servlet.get

@WebMvcTest(AdminBranchController::class)
class AdminBranchControllerTest @Autowired constructor(
    private val mvc: MockMvc,
) : ControllerTest() {

    @Test
    fun `test getBranches - should serialize and deserialize correctly`() {
        val branch1 = createBranch()
        val branch2 = createBranch(
            originalId = 2,
            productSalesCode = 2,
            name = "Kosice"
        )

        every { adminGetBranchesQueryServiceMock(any()) } returns
            listOf(
                AdminGetBranchesResponse(
                    id = branch1.id,
                    name = branch1.name
                ),
                AdminGetBranchesResponse(
                    id = branch2.id,
                    name = branch2.name
                )
            )

        mvc.get(BASE_BRANCH_PATH) {
            contentType = MediaType.APPLICATION_JSON
            header(HttpHeaders.ACCEPT, ApiVersion.VERSION_1_JSON)
            header(HttpHeaders.AUTHORIZATION, mockAccessToken(role = EmployeeRole.MANAGER))
        }.andExpect {
            status { isOk() }
            content { contentType(ApiVersion.VERSION_1_JSON) }
            jsonContent(
                """
                  [
                    {
                      "id": "${branch1.id}",
                      "name": "${branch1.name}"
                    },
                    {
                      "id": "${branch2.id}",
                      "name": "${branch2.name}"
                    }
                  ]
                """.trimIndent()
            )
        }

        verify { adminGetBranchesQueryServiceMock(any()) }
    }
}

private const val BASE_BRANCH_PATH = "/manager-app/branches"
