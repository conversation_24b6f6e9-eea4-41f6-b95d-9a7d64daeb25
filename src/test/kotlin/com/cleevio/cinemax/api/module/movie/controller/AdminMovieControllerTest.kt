package com.cleevio.cinemax.api.module.movie.controller

import com.cleevio.cinemax.api.ControllerTest
import com.cleevio.cinemax.api.common.constant.ApiVersion
import com.cleevio.cinemax.api.common.util.jsonContent
import com.cleevio.cinemax.api.module.employee.constant.EmployeeRole
import com.cleevio.cinemax.api.module.genre.entity.Genre
import com.cleevio.cinemax.api.module.jso.entity.Jso
import com.cleevio.cinemax.api.module.movie.controller.dto.AdminGetMovieResponse
import com.cleevio.cinemax.api.module.movie.controller.dto.AdminSearchMoviesResponse
import com.cleevio.cinemax.api.module.movie.service.command.CreateOrUpdateMovieCommand
import com.cleevio.cinemax.api.module.movie.service.command.DeleteMovieCommand
import com.cleevio.cinemax.api.module.movie.service.query.AdminGetMovieQuery
import com.cleevio.cinemax.api.module.movie.service.query.AdminSearchMoviesFilter
import com.cleevio.cinemax.api.module.movie.service.query.AdminSearchMoviesQuery
import com.cleevio.cinemax.api.module.production.entity.Production
import com.cleevio.cinemax.api.module.rating.entity.Rating
import com.cleevio.cinemax.api.module.technology.entity.Technology
import com.cleevio.cinemax.api.truncatedAndFormatted
import com.cleevio.cinemax.api.util.createDistributor
import com.cleevio.cinemax.api.util.createMovie
import com.cleevio.cinemax.api.util.mapToAdminGetMovieResponse
import com.cleevio.cinemax.api.util.mapToAdminSearchMoviesResponse
import com.cleevio.cinemax.api.util.mapToGetMovieDescriptorMssqlResponse
import com.cleevio.cinemax.api.util.mapToSearchMovieDescriptorMssqlResponse
import com.cleevio.cinemax.api.util.mockAccessToken
import com.cleevio.cinemax.psql.tables.MovieColumnNames
import io.mockk.Runs
import io.mockk.every
import io.mockk.just
import io.mockk.verifySequence
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest
import org.springframework.data.domain.PageImpl
import org.springframework.data.domain.PageRequest
import org.springframework.data.domain.Sort
import org.springframework.http.HttpHeaders
import org.springframework.http.MediaType
import org.springframework.test.web.servlet.MockMvc
import org.springframework.test.web.servlet.delete
import org.springframework.test.web.servlet.get
import org.springframework.test.web.servlet.post
import org.springframework.test.web.servlet.put
import java.time.LocalDate
import java.util.Optional
import java.util.UUID

@WebMvcTest(AdminMovieController::class)
class AdminMovieControllerTest @Autowired constructor(
    private val mvc: MockMvc,
) : ControllerTest() {

    @Test
    fun `test searchMovies, should serialize and deserialize correctly`() {
        every { adminSearchMoviesQueryService(any()) } returns PageImpl(listOf(MOVIE_SEARCH_RESPONSE))

        mvc.post(SEARCH_MOVIE_PATH) {
            contentType = MediaType.APPLICATION_JSON
            header(HttpHeaders.ACCEPT, ApiVersion.VERSION_1_JSON)
            header(HttpHeaders.AUTHORIZATION, mockAccessToken(role = EmployeeRole.MANAGER))
            content = """
                {
                    "premiereDateFrom": "$SEARCH_DATE_FROM",
                    "premiereDateTo": "$SEARCH_DATE_TO",
                    "title": "$SEARCH_MOVIE_TITLE",
                    "distributorTitle": "$SEARCH_DISTRIBUTOR_TITLE"
                }
            """.trimIndent()
        }.andExpect {
            status { isOk() }
            content { contentType(ApiVersion.VERSION_1_JSON) }
            jsonContent(
                """
                {
                    "content": [
                        {
                            "id": "${MOVIE_1.id}",
                            "rawTitle": "${MOVIE_1.rawTitle}",
                            "originalTitle": "${MOVIE_1.originalTitle}",
                            "title": "${MOVIE_1.title}",
                            "code": "${MOVIE_1.code}",
                            "releaseYear": ${MOVIE_1.releaseYear},
                            "premiereDate": "${MOVIE_1.premiereDate}",
                            "duration": 160,
                            "createdAt": "${MOVIE_1.createdAt.truncatedAndFormatted()}",
                            "updatedAt": "${MOVIE_1.updatedAt.truncatedAndFormatted()}",
                            "distributor": {
                                "id": "${DISTRIBUTOR_1.id}",
                                "title": "${DISTRIBUTOR_1.title}"
                            },
                            "production": {
                                "id": "${PRODUCTION_1.id}",
                                "title": "${PRODUCTION_1.title}"
                            },
                            "primaryGenre": {
                                "id": "${GENRE_1.id}",
                                "title": "${GENRE_1.title}"
                            },
                            "secondaryGenre": null,
                            "rating": {
                                "id": "${RATING_1.id}",
                                "title": "${RATING_1.title}"
                            },
                            "technology": {
                                "id": "${TECHNOLOGY_1.id}",
                                "title": "${TECHNOLOGY_1.title}"
                            },
                            "language": null,
                            "tmsLanguage": null,
                            "jsos": [
                                {
                                    "id": "${JSO_1.id}",
                                    "title": "${JSO_1.title}"
                                },
                                {
                                    "id": "${JSO_2.id}",
                                    "title": "${JSO_2.title}"
                                }
                            ]
                        }
                    ],
                    "totalElements": 1,
                    "totalPages": 1
                }
              """
            )
        }

        verifySequence {
            adminSearchMoviesQueryService(
                AdminSearchMoviesQuery(
                    pageable = PageRequest.of(0, 10, Sort.by(MovieColumnNames.PREMIERE_DATE).descending()),
                    filter = AdminSearchMoviesFilter(
                        premiereDateFrom = SEARCH_DATE_FROM,
                        premiereDateTo = SEARCH_DATE_TO,
                        title = SEARCH_MOVIE_TITLE,
                        originalTitle = null,
                        distributorTitle = SEARCH_DISTRIBUTOR_TITLE
                    )
                )
            )
        }
    }

    @Test
    fun `test getMovie, should serialize and deserialize correctly`() {
        every { adminGetMovieQueryService(any()) } returns GET_MOVIE_RESPONSE

        mvc.get(GET_DELETE_AND_UPDATE_MOVIE_PATH(MOVIE_1.id)) {
            contentType = MediaType.APPLICATION_JSON
            header(HttpHeaders.ACCEPT, ApiVersion.VERSION_1_JSON)
            header(HttpHeaders.AUTHORIZATION, mockAccessToken(role = EmployeeRole.MANAGER))
        }.andExpect {
            status { isOk() }
            content { contentType(ApiVersion.VERSION_1_JSON) }
            jsonContent(
                """
                    {
                        "id": "${MOVIE_1.id}",
                        "rawTitle": "${MOVIE_1.rawTitle}",
                        "originalTitle": "${MOVIE_1.originalTitle}",
                        "title": "${MOVIE_1.title}",
                        "code": "${MOVIE_1.code}",
                        "releaseYear": ${MOVIE_1.releaseYear},
                        "premiereDate": "${MOVIE_1.premiereDate}",
                        "duration": 160,
                        "createdAt": "${MOVIE_1.createdAt.truncatedAndFormatted()}",
                        "updatedAt": "${MOVIE_1.updatedAt.truncatedAndFormatted()}",
                        "distributor": {
                            "id": "${DISTRIBUTOR_1.id}",
                            "title": "${DISTRIBUTOR_1.title}"
                        },
                        "production": {
                            "id": "${PRODUCTION_1.id}",
                            "title": "${PRODUCTION_1.title}"
                        },
                        "primaryGenre": {
                            "id": "${GENRE_1.id}",
                            "title": "${GENRE_1.title}"
                        },
                        "secondaryGenre": null,
                        "rating": {
                            "id": "${RATING_1.id}",
                            "title": "${RATING_1.title}"
                        },
                        "technology": {
                            "id": "${TECHNOLOGY_1.id}",
                            "title": "${TECHNOLOGY_1.title}"
                        },
                        "language": null,
                        "tmsLanguage": null,
                        "jsos": [
                            {
                                "id": "${JSO_1.id}",
                                "title": "${JSO_1.title}"
                            },
                             {
                                "id": "${JSO_2.id}",
                                "title": "${JSO_2.title}"
                            }
                        ]
                    }
                """
            )
        }

        verifySequence {
            adminGetMovieQueryService(
                AdminGetMovieQuery(
                    movieId = MOVIE_1.id
                )
            )
        }
    }

    @Test
    fun `test deleteMovie, should serialize and deserialize correctly`() {
        every { movieService.deleteMovie(any()) } just Runs

        mvc.delete(GET_DELETE_AND_UPDATE_MOVIE_PATH(MOVIE_1.id)) {
            header(HttpHeaders.ACCEPT, ApiVersion.VERSION_1_JSON)
            header(HttpHeaders.AUTHORIZATION, mockAccessToken(role = EmployeeRole.MANAGER))
            contentType = MediaType.APPLICATION_JSON
        }.andExpect {
            status { isNoContent() }
        }

        verifySequence {
            movieService.deleteMovie(
                DeleteMovieCommand(
                    movieId = MOVIE_1.id
                )
            )
        }
    }

    @Test
    fun `test createMovie, should serialize and deserialize correctly`() {
        val movieId = UUID.fromString("9e22ebee-f458-4c8b-a8ee-1503ee5099d7")
        every { movieService.adminCreateOrUpdateMovie(any()) } returns movieId

        mvc.post(BASE_MOVIE_PATH) {
            header(HttpHeaders.ACCEPT, ApiVersion.VERSION_1_JSON)
            header(HttpHeaders.AUTHORIZATION, mockAccessToken(EmployeeRole.MANAGER))
            contentType = MediaType.APPLICATION_JSON
            content = """
                {
                  "rawTitle": "${MOVIE_1.rawTitle}",
                  "originalTitle": "${MOVIE_1.originalTitle}",
                  "premiereDate": "${MOVIE_1.premiereDate}",
                  "duration": ${MOVIE_1.duration},
                  "distributorId": "${MOVIE_1.distributorId}",
                  "productionId": "${MOVIE_1.productionId}",
                  "primaryGenreId": "${MOVIE_1.primaryGenreId}",
                  "secondaryGenreId": "${MOVIE_1.secondaryGenreId}",
                  "ratingId": "${MOVIE_1.ratingId}",
                  "technologyId": "${MOVIE_1.technologyId}",
                  "languageId": "${MOVIE_1.languageId}",
                  "tmsLanguageId": "${MOVIE_1.tmsLanguageId}",
                  "jsoIds": [
                      "${JSO_1.id}",
                      "${JSO_2.id}"
                    ]
                }
            """.trimIndent()
        }.andExpect {
            status { isOk() }
            jsonContent(
                """
                    {
                      "id": "$movieId"
                    }
                """.trimIndent()

            )
        }

        verifySequence {
            movieService.adminCreateOrUpdateMovie(
                CreateOrUpdateMovieCommand(
                    originalId = null,
                    title = null,
                    rawTitle = MOVIE_1.rawTitle,
                    originalTitle = MOVIE_1.originalTitle?.let { Optional.of(it) },
                    code = null,
                    disfilmCode = null,
                    premiereDate = MOVIE_1.premiereDate?.let { Optional.of(it) },
                    duration = MOVIE_1.duration,
                    parsedRating = null,
                    parsedFormat = null,
                    parsedTechnology = null,
                    parsedLanguage = null,
                    distributorId = MOVIE_1.distributorId,
                    productionId = MOVIE_1.productionId?.let { Optional.of(it) },
                    primaryGenreId = MOVIE_1.primaryGenreId?.let { Optional.of(it) },
                    secondaryGenreId = MOVIE_1.secondaryGenreId?.let { Optional.of(it) },
                    ratingId = MOVIE_1.ratingId?.let { Optional.of(it) },
                    technologyId = MOVIE_1.technologyId,
                    languageId = MOVIE_1.languageId,
                    tmsLanguageId = MOVIE_1.tmsLanguageId,
                    jsoIds = setOf(JSO_1.id, JSO_2.id)
                )
            )
        }
    }

    @Test
    fun `test createMovie, should serialize and deserialize correctly with null values or missing properties`() {
        val movieId = UUID.fromString("9e22ebee-f458-4c8b-a8ee-1503ee5099d7")
        every { movieService.adminCreateOrUpdateMovie(any()) } returns movieId

        mvc.post(BASE_MOVIE_PATH) {
            header(HttpHeaders.ACCEPT, ApiVersion.VERSION_1_JSON)
            header(HttpHeaders.AUTHORIZATION, mockAccessToken(EmployeeRole.MANAGER))
            contentType = MediaType.APPLICATION_JSON
            content = """
                {
                  "rawTitle": "${MOVIE_1.rawTitle}",
                  "originalTitle": null,
                  "premiereDate": null,
                  "duration": "${MOVIE_1.duration}",
                  "distributorId": "${MOVIE_1.distributorId}",
                  "technologyId": "${MOVIE_1.technologyId}",
                  "languageId": "${MOVIE_1.languageId}",
                  "tmsLanguageId": "${MOVIE_1.tmsLanguageId}"
                }
            """.trimIndent()
        }.andExpect {
            status { isOk() }
            jsonContent(
                """
                    {
                      "id": "$movieId"
                    }
                """.trimIndent()

            )
        }

        verifySequence {
            movieService.adminCreateOrUpdateMovie(
                CreateOrUpdateMovieCommand(
                    originalId = null,
                    title = null,
                    rawTitle = MOVIE_1.rawTitle,
                    originalTitle = Optional.empty(),
                    code = null,
                    disfilmCode = null,
                    premiereDate = Optional.empty(),
                    duration = MOVIE_1.duration,
                    parsedRating = null,
                    parsedFormat = null,
                    parsedTechnology = null,
                    parsedLanguage = null,
                    distributorId = MOVIE_1.distributorId,
                    productionId = null,
                    primaryGenreId = null,
                    secondaryGenreId = null,
                    ratingId = null,
                    technologyId = MOVIE_1.technologyId,
                    languageId = MOVIE_1.languageId,
                    tmsLanguageId = MOVIE_1.tmsLanguageId,
                    jsoIds = null
                )
            )
        }
    }

    @Test
    fun `test updateMovie, should serialize and deserialize correctly`() {
        val movieId = UUID.fromString("9e22ebee-f458-4c8b-a8ee-1503ee5099d7")
        every { movieService.adminCreateOrUpdateMovie(any()) } returns movieId

        mvc.put(GET_DELETE_AND_UPDATE_MOVIE_PATH(movieId)) {
            header(HttpHeaders.ACCEPT, ApiVersion.VERSION_1_JSON)
            header(HttpHeaders.AUTHORIZATION, mockAccessToken(EmployeeRole.MANAGER))
            contentType = MediaType.APPLICATION_JSON
            content = """
                {
                  "disfilmCode": "${MOVIE_1.disfilmCode}",
                  "rawTitle": "${MOVIE_1.rawTitle}",
                  "originalTitle": "${MOVIE_1.originalTitle}",
                  "premiereDate": "${MOVIE_1.premiereDate}",
                  "duration": ${MOVIE_1.duration},
                  "distributorId": "${MOVIE_1.distributorId}",
                  "productionId": "${MOVIE_1.productionId}",
                  "primaryGenreId": "${MOVIE_1.primaryGenreId}",
                  "secondaryGenreId": "${MOVIE_1.secondaryGenreId}",
                  "ratingId": "${MOVIE_1.ratingId}",
                  "technologyId": "${MOVIE_1.technologyId}",
                  "languageId": "${MOVIE_1.languageId}",
                  "tmsLanguageId": "${MOVIE_1.tmsLanguageId}",
                  "jsoIds": [
                      "${JSO_1.id}",
                      "${JSO_2.id}"
                    ]
                }
            """.trimIndent()
        }.andExpect {
            status { isOk() }
            jsonContent(
                """
                    {
                      "id": "$movieId"
                    }
                """.trimIndent()

            )
        }

        verifySequence {
            movieService.adminCreateOrUpdateMovie(
                CreateOrUpdateMovieCommand(
                    id = movieId,
                    originalId = null,
                    title = null,
                    rawTitle = MOVIE_1.rawTitle,
                    originalTitle = MOVIE_1.originalTitle?.let { Optional.of(it) },
                    code = null,
                    disfilmCode = null,
                    premiereDate = MOVIE_1.premiereDate?.let { Optional.of(it) },
                    duration = MOVIE_1.duration,
                    parsedRating = null,
                    parsedFormat = null,
                    parsedTechnology = null,
                    parsedLanguage = null,
                    distributorId = MOVIE_1.distributorId,
                    productionId = MOVIE_1.productionId?.let { Optional.of(it) },
                    primaryGenreId = MOVIE_1.primaryGenreId?.let { Optional.of(it) },
                    secondaryGenreId = MOVIE_1.secondaryGenreId?.let { Optional.of(it) },
                    ratingId = MOVIE_1.ratingId?.let { Optional.of(it) },
                    technologyId = MOVIE_1.technologyId,
                    languageId = MOVIE_1.languageId,
                    tmsLanguageId = MOVIE_1.tmsLanguageId,
                    jsoIds = setOf(JSO_1.id, JSO_2.id)
                )
            )
        }
    }

    @Test
    fun `test updateMovie, should serialize and deserialize correctly with null values or missing properties`() {
        val movieId = UUID.fromString("9e22ebee-f458-4c8b-a8ee-1503ee5099d7")
        every { movieService.adminCreateOrUpdateMovie(any()) } returns movieId

        mvc.put(GET_DELETE_AND_UPDATE_MOVIE_PATH(movieId)) {
            header(HttpHeaders.ACCEPT, ApiVersion.VERSION_1_JSON)
            header(HttpHeaders.AUTHORIZATION, mockAccessToken(EmployeeRole.MANAGER))
            contentType = MediaType.APPLICATION_JSON
            content = """
                {
                  "disfilmCode": null,
                  "rawTitle": "${MOVIE_1.rawTitle}",
                  "originalTitle": null,
                  "premiereDate": null,
                  "duration": "${MOVIE_1.duration}",
                  "distributorId": "${MOVIE_1.distributorId}",
                  "technologyId": "${MOVIE_1.technologyId}",
                  "languageId": "${MOVIE_1.languageId}",
                  "tmsLanguageId": "${MOVIE_1.tmsLanguageId}"
                }
            """.trimIndent()
        }.andExpect {
            status { isOk() }
            jsonContent(
                """
                    {
                      "id": "$movieId"
                    }
                """.trimIndent()
            )
        }

        verifySequence {
            movieService.adminCreateOrUpdateMovie(
                CreateOrUpdateMovieCommand(
                    id = movieId,
                    originalId = null,
                    title = null,
                    rawTitle = MOVIE_1.rawTitle,
                    originalTitle = Optional.empty(),
                    code = null,
                    disfilmCode = null,
                    premiereDate = Optional.empty(),
                    duration = MOVIE_1.duration,
                    parsedRating = null,
                    parsedFormat = null,
                    parsedTechnology = null,
                    parsedLanguage = null,
                    distributorId = MOVIE_1.distributorId,
                    productionId = null,
                    primaryGenreId = null,
                    secondaryGenreId = null,
                    ratingId = null,
                    technologyId = MOVIE_1.technologyId,
                    languageId = MOVIE_1.languageId,
                    tmsLanguageId = MOVIE_1.tmsLanguageId,
                    jsoIds = null
                )
            )
        }
    }

    @Test
    fun `test syncMoviesFromDISFilm, should serialize and deserialize correctly`() {
        val count = 3
        every { movieDISFilmSynchronizationService.syncMoviesFromDisfilm() } returns count

        mvc.post(SYNC_MOVIES_FROM_DISFILM_PATH) {
            header(HttpHeaders.ACCEPT, ApiVersion.VERSION_1_JSON)
            header(HttpHeaders.AUTHORIZATION, mockAccessToken(EmployeeRole.MANAGER))
            contentType = MediaType.APPLICATION_JSON
        }.andExpect {
            status { isOk() }
            jsonContent(
                """
                    {
                      "count": $count
                    }
                """.trimIndent()
            )
        }

        verifySequence { movieDISFilmSynchronizationService.syncMoviesFromDisfilm() }
    }
}

private const val BASE_MOVIE_PATH = "/manager-app/movies"
private const val SEARCH_MOVIE_PATH = "$BASE_MOVIE_PATH/search"
private const val SYNC_MOVIES_FROM_DISFILM_PATH = "$BASE_MOVIE_PATH/sync-from-disfilm"
private val GET_DELETE_AND_UPDATE_MOVIE_PATH: (UUID) -> String = { movieId: UUID -> "$BASE_MOVIE_PATH/$movieId" }
private val SEARCH_DATE_FROM = LocalDate.of(2023, 8, 1)
private val SEARCH_DATE_TO = LocalDate.of(2023, 12, 30)
private const val SEARCH_MOVIE_TITLE = "Inter"
private const val SEARCH_DISTRIBUTOR_TITLE = "Slovens"
private val LANGUAGE_1_ID = UUID.randomUUID()
private val TMS_LANGUAGE_1_ID = UUID.randomUUID()

private val DISTRIBUTOR_1 = createDistributor()
private val MOVIE_1_PREMIERE_DATE = LocalDate.of(2001, 4, 30)
private val PRODUCTION_1 = Production(
    originalId = 98,
    code = "56",
    title = "USA"
)
private val GENRE_1 = Genre(
    originalId = 2,
    code = "1",
    title = "Dráma"
)
private val GENRE_2 = Genre(
    originalId = 40,
    code = "-",
    title = "Hudobny, muzikal"
)
private val RATING_1 = Rating(
    originalId = 6,
    code = "12",
    title = "12"
)
private val TECHNOLOGY_1 = Technology(
    originalId = 12,
    code = "05",
    title = "IMAX"
)
private val JSO_1 = Jso(
    originalId = 3,
    code = "S",
    title = "Strach"
)
private val JSO_2 = Jso(
    originalId = 6,
    code = "V",
    title = "Vulgarizmy"
)
private val MOVIE_1 = createMovie(
    originalId = 1,
    title = "Star Wars: Episode I – The Phantom Menace",
    releaseYear = 2001,
    premiereDate = MOVIE_1_PREMIERE_DATE,
    distributorId = DISTRIBUTOR_1.id,
    productionId = PRODUCTION_1.id,
    primaryGenreId = GENRE_1.id,
    secondaryGenreId = GENRE_2.id,
    ratingId = RATING_1.id,
    technologyId = TECHNOLOGY_1.id,
    languageId = LANGUAGE_1_ID,
    tmsLanguageId = TMS_LANGUAGE_1_ID
)
private val MOVIE_SEARCH_RESPONSE = mapToAdminSearchMoviesResponse(
    movie = MOVIE_1,
    distributor = AdminSearchMoviesResponse.MovieDistributorSearchResponse(
        id = DISTRIBUTOR_1.id,
        title = DISTRIBUTOR_1.title
    ),
    production = mapToSearchMovieDescriptorMssqlResponse(PRODUCTION_1),
    primaryGenre = mapToSearchMovieDescriptorMssqlResponse(GENRE_1),
    rating = mapToSearchMovieDescriptorMssqlResponse(RATING_1),
    technology = mapToSearchMovieDescriptorMssqlResponse(TECHNOLOGY_1),
    jsos = listOf(
        mapToSearchMovieDescriptorMssqlResponse(JSO_1),
        mapToSearchMovieDescriptorMssqlResponse(JSO_2)
    )
)
private val GET_MOVIE_RESPONSE = mapToAdminGetMovieResponse(
    movie = MOVIE_1,
    distributor = AdminGetMovieResponse.GetMovieDistributorResponse(
        id = DISTRIBUTOR_1.id,
        title = DISTRIBUTOR_1.title
    ),
    production = mapToGetMovieDescriptorMssqlResponse(PRODUCTION_1),
    primaryGenre = mapToGetMovieDescriptorMssqlResponse(GENRE_1),
    rating = mapToGetMovieDescriptorMssqlResponse(RATING_1),
    technology = mapToGetMovieDescriptorMssqlResponse(TECHNOLOGY_1),
    jsos = listOf(
        mapToGetMovieDescriptorMssqlResponse(JSO_1),
        mapToGetMovieDescriptorMssqlResponse(JSO_2)
    )
)
