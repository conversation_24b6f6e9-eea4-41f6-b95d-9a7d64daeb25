package com.cleevio.cinemax.api.module.screening.service

import com.cleevio.cinemax.api.DatabaseCleanup
import com.cleevio.cinemax.api.INTEGRATION_TEST_DATE_TIME
import com.cleevio.cinemax.api.IntegrationTest
import com.cleevio.cinemax.api.common.service.query.SearchQueryDeprecated
import com.cleevio.cinemax.api.module.auditorium.service.AuditoriumService
import com.cleevio.cinemax.api.module.auditoriumlayout.service.AuditoriumLayoutRepository
import com.cleevio.cinemax.api.module.distributor.service.DistributorService
import com.cleevio.cinemax.api.module.language.entity.Language
import com.cleevio.cinemax.api.module.language.service.LanguageRepository
import com.cleevio.cinemax.api.module.movie.service.MovieService
import com.cleevio.cinemax.api.module.pricecategory.service.PriceCategoryService
import com.cleevio.cinemax.api.module.pricecategoryitem.constant.PriceCategoryItemNumber
import com.cleevio.cinemax.api.module.pricecategoryitem.service.PriceCategoryItemRepository
import com.cleevio.cinemax.api.module.rating.entity.Rating
import com.cleevio.cinemax.api.module.rating.service.RatingRepository
import com.cleevio.cinemax.api.module.reservation.constant.ReservationState
import com.cleevio.cinemax.api.module.reservation.service.ReservationRepository
import com.cleevio.cinemax.api.module.screening.constant.ScreeningState
import com.cleevio.cinemax.api.module.screening.service.command.DeleteScreeningCommand
import com.cleevio.cinemax.api.module.screening.service.query.ScreeningFilter
import com.cleevio.cinemax.api.module.screeningfee.service.ScreeningFeeRepository
import com.cleevio.cinemax.api.module.screeningtype.service.ScreeningTypeRepository
import com.cleevio.cinemax.api.module.screeningtypes.service.ScreeningTypesRepository
import com.cleevio.cinemax.api.module.seat.service.SeatRepository
import com.cleevio.cinemax.api.module.technology.entity.Technology
import com.cleevio.cinemax.api.module.technology.service.TechnologyRepository
import com.cleevio.cinemax.api.module.ticket.service.TicketRepository
import com.cleevio.cinemax.api.module.ticketprice.service.TicketPriceRepository
import com.cleevio.cinemax.api.module.tmslanguage.entity.TmsLanguage
import com.cleevio.cinemax.api.module.tmslanguage.service.TmsLanguageRepository
import com.cleevio.cinemax.api.util.createAuditorium
import com.cleevio.cinemax.api.util.createAuditoriumLayout
import com.cleevio.cinemax.api.util.createDistributor
import com.cleevio.cinemax.api.util.createMovie
import com.cleevio.cinemax.api.util.createPriceCategory
import com.cleevio.cinemax.api.util.createPriceCategoryItem
import com.cleevio.cinemax.api.util.createReservation
import com.cleevio.cinemax.api.util.createScreening
import com.cleevio.cinemax.api.util.createScreeningFee
import com.cleevio.cinemax.api.util.createScreeningType
import com.cleevio.cinemax.api.util.createScreeningTypes
import com.cleevio.cinemax.api.util.createSeat
import com.cleevio.cinemax.api.util.createTicket
import com.cleevio.cinemax.api.util.createTicketPrice
import com.cleevio.cinemax.api.util.mapToCreateOrUpdateAuditoriumCommand
import com.cleevio.cinemax.api.util.mapToCreateOrUpdateDistributorCommand
import com.cleevio.cinemax.api.util.mapToCreateOrUpdateMovieCommand
import com.cleevio.cinemax.api.util.mapToSyncCreateOrUpdatePriceCategoryCommand
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.data.domain.PageRequest
import org.springframework.data.domain.Sort
import java.math.BigDecimal
import java.time.LocalTime
import kotlin.test.assertEquals

class ScreeningJooqFinderServiceIT @Autowired constructor(
    private val underTest: ScreeningJooqFinderService,
    private val screeningService: ScreeningService,
    private val auditoriumService: AuditoriumService,
    private val movieService: MovieService,
    private val priceCategoryService: PriceCategoryService,
    private val distributorService: DistributorService,
    private val auditoriumLayoutRepository: AuditoriumLayoutRepository,
    private val ratingRepository: RatingRepository,
    private val technologyRepository: TechnologyRepository,
    private val languageRepository: LanguageRepository,
    private val tmsLanguageRepository: TmsLanguageRepository,
    private val screeningFeeRepository: ScreeningFeeRepository,
    private val screeningTypesRepository: ScreeningTypesRepository,
    private val screeningTypeRepository: ScreeningTypeRepository,
    private val priceCategoryItemRepository: PriceCategoryItemRepository,
    private val seatRepository: SeatRepository,
    private val reservationRepository: ReservationRepository,
    private val ticketRepository: TicketRepository,
    private val ticketPriceRepository: TicketPriceRepository,
    private val screeningRepository: ScreeningRepository,
) : IntegrationTest(DatabaseCleanup.AFTER_EACH) {

    @BeforeEach
    fun setUp() {
        setOf(AUDITORIUM_1, AUDITORIUM_2).forEach {
            auditoriumService.syncCreateOrUpdateAuditorium(
                mapToCreateOrUpdateAuditoriumCommand(it)
            )
        }

        setOf(AUDITORIUM_LAYOUT_1, AUDITORIUM_LAYOUT_2, AUDITORIUM_LAYOUT_3_DELETED).forEach {
            auditoriumLayoutRepository.save(it)
        }
        setOf(DISTRIBUTOR_1, DISTRIBUTOR_2).forEach {
            distributorService.syncCreateOrUpdateDistributor(mapToCreateOrUpdateDistributorCommand(it))
        }
        ratingRepository.saveAll(setOf(RATING_1, RATING_2))
        technologyRepository.saveAll(setOf(TECHNOLOGY_1, TECHNOLOGY_2))
        languageRepository.saveAll(setOf(LANGUAGE_1, LANGUAGE_2))
        tmsLanguageRepository.saveAll(setOf(TMS_LANGUAGE_1, TMS_LANGUAGE_2))
        setOf(MOVIE_1, MOVIE_2).forEach {
            movieService.syncCreateOrUpdateMovie(mapToCreateOrUpdateMovieCommand(it))
        }
        priceCategoryService.syncCreateOrUpdatePriceCategory(
            mapToSyncCreateOrUpdatePriceCategoryCommand(PRICE_CATEGORY_1)
        )
        screeningRepository.saveAll(
            setOf(
                SCREENING_1,
                SCREENING_2,
                SCREENING_3,
                SCREENING_4,
                SCREENING_5,
                SCREENING_6,
                SCREENING_7,
                SCREENING_8
            )
        )
        screeningService.syncDeleteScreening(DeleteScreeningCommand(SCREENING_5.id))
        screeningFeeRepository.save(SCREENING_FEE_1)
        priceCategoryItemRepository.saveAll(setOf(PRICE_CATEGORY_ITEM_1, PRICE_CATEGORY_ITEM_2))
        screeningTypeRepository.saveAll(setOf(SCREENING_TYPE_1, SCREENING_TYPE_2))
        screeningTypesRepository.saveAll(setOf(SCREENING_TYPES_1, SCREENING_TYPES_2))
        seatRepository.saveAll(setOf(SEAT_1, SEAT_2, SEAT_3))
        reservationRepository.saveAll(setOf(RESERVATION_1, RESERVATION_2, RESERVATION_3))
        ticketPriceRepository.saveAll(setOf(TICKET_PRICE_1, TICKET_PRICE_2, TICKET_PRICE_3))
        ticketRepository.saveAll(setOf(TICKET_1, TICKET_2, TICKET_3))
    }

    @Test
    fun `test search - date filter - should find correct screenings`() {
        assertEquals(8, underTest.findAll().size)

        val screenings = underTest.search(
            SearchQueryDeprecated(
                pageable = PageRequest.of(0, 10, Sort.unsorted()),
                filter = ScreeningFilter(
                    date = INTEGRATION_TEST_DATE_TIME.toLocalDate()
                )
            )
        ).sortedBy { it.originalId }

        assertEquals(2, screenings.size)
        assertEquals(SCREENING_1.id, screenings[0].id)
        assertEquals(SCREENING_2.id, screenings[1].id)
    }

    @Test
    fun `test search - all filters - should find correct screenings`() {
        assertEquals(8, underTest.findAll().size)

        val screenings = underTest.search(
            SearchQueryDeprecated(
                pageable = PageRequest.of(0, 10, Sort.unsorted()),
                filter = ScreeningFilter(
                    date = INTEGRATION_TEST_DATE_TIME.toLocalDate(),
                    screeningIds = setOf(SCREENING_2.id)
                )
            )
        ).sortedBy { it.originalId }

        assertEquals(1, screenings.size)
        assertEquals(SCREENING_2.id, screenings[0].id)
    }

    @Test
    fun `test search - draft screenings - should be filtered out`() {
        screeningRepository.save(SCREENING_9)
        assertEquals(ScreeningState.DRAFT, screeningRepository.findAll().first { it.id == SCREENING_9.id }.state)

        val screenings = underTest.search(
            SearchQueryDeprecated(
                pageable = PageRequest.of(0, 10, Sort.unsorted()),
                filter = ScreeningFilter(
                    date = INTEGRATION_TEST_DATE_TIME.toLocalDate(),
                    screeningIds = setOf(SCREENING_2.id, SCREENING_9.id)
                )
            )
        )

        assertEquals(1, screenings.content.size)
        assertEquals(SCREENING_2.id, screenings.content[0].id)
    }

    @Test
    fun `test search - pagination and sorting - should find correct screenings`() {
        assertEquals(8, underTest.findAll().size)

        val screenings = underTest.search(
            SearchQueryDeprecated(
                pageable = PageRequest.of(
                    1,
                    1,
                    Sort.by(Sort.Order.desc("screening.originalId"))
                ),
                filter = ScreeningFilter(
                    date = INTEGRATION_TEST_DATE_TIME.toLocalDate()
                )
            )
        ).sortedBy { it.originalId }

        assertEquals(1, screenings.size)
        assertEquals(SCREENING_1.id, screenings[0].id)
    }
}

private val LOCAL_TIME = LocalTime.of(20, 0, 0)
private val AUDITORIUM_1 = createAuditorium(
    originalId = 1,
    code = "A",
    title = "SÁLA A - CINEMAX BRATISLAVA",
    originalCode = 22
)
private val AUDITORIUM_2 = createAuditorium(
    originalId = 2,
    code = "IMAX",
    title = "IMAX - CINEMAX BRATISLAVA",
    capacity = 150,
    originalCode = 11
)
private val DISTRIBUTOR_1 = createDistributor(originalId = 1)
private val DISTRIBUTOR_2 = createDistributor(originalId = 2, title = "Hello movies, s.r.o.")
private val AUDITORIUM_LAYOUT_1 = createAuditoriumLayout(originalId = 1, auditoriumId = AUDITORIUM_1.id)
private val AUDITORIUM_LAYOUT_2 = createAuditoriumLayout(
    originalId = 2,
    auditoriumId = AUDITORIUM_2.id,
    title = "Special"
)
private val AUDITORIUM_LAYOUT_3_DELETED = createAuditoriumLayout(
    originalId = 3,
    auditoriumId = AUDITORIUM_1.id,
    title = "Deleted"
).also { it.markDeleted() }
private val RATING_1 = Rating(
    originalId = 1,
    code = "13",
    title = "13"
)
private val RATING_2 = Rating(
    originalId = 2,
    code = "16",
    title = "16"
)
private val RATING_3 = Rating(
    originalId = 3,
    code = "8",
    title = "8"
)
private val RATING_4 = Rating(
    originalId = 4,
    code = "V",
    title = "V"
)
private val TECHNOLOGY_1 = Technology(
    originalId = 1,
    title = "2D",
    code = "2DA"
)
private val LANGUAGE_1 = Language(
    originalId = 10,
    title = "slovak",
    code = "SVK"
)
private val TMS_LANGUAGE_1 = TmsLanguage(
    originalId = 1,
    title = "Slovenske zneni",
    code = "TMS2"
)
private val TECHNOLOGY_2 = Technology(
    originalId = 2,
    title = "3D IMAX",
    code = "3I"
)
private val LANGUAGE_2 = Language(
    originalId = 11,
    title = "czech",
    code = "CZE"
)
private val TMS_LANGUAGE_2 = TmsLanguage(
    originalId = 2,
    title = "Ceske zneni",
    code = "TMS1"
)
private val MOVIE_1 = createMovie(
    originalId = 1,
    title = "Star Wars: Episode I – The Phantom Menace",
    releaseYear = 2001,
    distributorId = DISTRIBUTOR_1.id,
    ratingId = RATING_1.id,
    technologyId = TECHNOLOGY_1.id,
    languageId = LANGUAGE_1.id,
    tmsLanguageId = TMS_LANGUAGE_1.id
)
private val PRICE_CATEGORY_1 = createPriceCategory(
    originalId = 1,
    title = "ARTMAX PO 17",
    active = true
)
private val SCREENING_1 = createScreening(
    originalId = 1,
    auditoriumId = AUDITORIUM_1.id,
    auditoriumLayoutId = AUDITORIUM_LAYOUT_1.id,
    movieId = MOVIE_1.id,
    priceCategoryId = PRICE_CATEGORY_1.id,
    time = LOCAL_TIME.plusHours(2).plusMinutes(1)
)
private val SCREENING_2 = createScreening(
    originalId = 2,
    auditoriumId = AUDITORIUM_2.id,
    auditoriumLayoutId = AUDITORIUM_LAYOUT_2.id,
    movieId = MOVIE_1.id,
    priceCategoryId = PRICE_CATEGORY_1.id,
    time = LOCAL_TIME.plusMinutes(20)
)
private val SCREENING_3 = createScreening(
    originalId = 3,
    auditoriumId = AUDITORIUM_1.id,
    auditoriumLayoutId = AUDITORIUM_LAYOUT_1.id,
    movieId = MOVIE_1.id,
    priceCategoryId = PRICE_CATEGORY_1.id,
    stopped = true,
    time = LOCAL_TIME.plusHours(2)
)
private val SCREENING_4 = createScreening(
    originalId = 4,
    auditoriumId = AUDITORIUM_1.id,
    auditoriumLayoutId = AUDITORIUM_LAYOUT_1.id,
    movieId = MOVIE_1.id,
    priceCategoryId = PRICE_CATEGORY_1.id,
    cancelled = true,
    time = LOCAL_TIME.plusHours(2).minusMinutes(1)
)
private val SCREENING_5 = createScreening(
    originalId = 5,
    auditoriumId = AUDITORIUM_1.id,
    auditoriumLayoutId = AUDITORIUM_LAYOUT_1.id,
    movieId = MOVIE_1.id,
    priceCategoryId = PRICE_CATEGORY_1.id
)
private val MOVIE_2 = createMovie(
    originalId = 2,
    code = "873946",
    title = "Matrix",
    rawTitle = "Matrix RAW",
    releaseYear = 2000,
    distributorId = DISTRIBUTOR_2.id,
    duration = 150,
    ratingId = RATING_2.id,
    technologyId = TECHNOLOGY_2.id,
    languageId = LANGUAGE_2.id,
    tmsLanguageId = TMS_LANGUAGE_2.id
)
private val MOVIE_3 = createMovie(
    originalId = 3,
    code = "873945",
    title = "Oppenheimer",
    rawTitle = "Oppenheimer RAW",
    releaseYear = 2023,
    distributorId = DISTRIBUTOR_2.id,
    duration = 150,
    ratingId = RATING_3.id,
    technologyId = TECHNOLOGY_2.id,
    languageId = LANGUAGE_2.id,
    tmsLanguageId = TMS_LANGUAGE_2.id
)
private val MOVIE_4 = createMovie(
    originalId = 4,
    code = "873944",
    title = "Despicable me",
    rawTitle = "Despicable me RAW",
    releaseYear = 2010,
    distributorId = DISTRIBUTOR_2.id,
    duration = 150,
    ratingId = RATING_4.id,
    technologyId = TECHNOLOGY_2.id,
    languageId = LANGUAGE_2.id,
    tmsLanguageId = TMS_LANGUAGE_2.id
)
private val SCREENING_6 = createScreening(
    originalId = 6,
    auditoriumId = AUDITORIUM_1.id,
    auditoriumLayoutId = AUDITORIUM_LAYOUT_1.id,
    movieId = MOVIE_1.id,
    priceCategoryId = PRICE_CATEGORY_1.id,
    state = ScreeningState.DRAFT,
    date = INTEGRATION_TEST_DATE_TIME.toLocalDate().plusDays(5),
    time = LOCAL_TIME.plusHours(3).plusMinutes(10)
)
private val SCREENING_7 = createScreening(
    originalId = 7,
    auditoriumId = AUDITORIUM_2.id,
    auditoriumLayoutId = AUDITORIUM_LAYOUT_2.id,
    movieId = MOVIE_2.id,
    priceCategoryId = PRICE_CATEGORY_1.id,
    state = ScreeningState.PUBLISHED,
    date = INTEGRATION_TEST_DATE_TIME.toLocalDate().plusDays(8),
    time = LOCAL_TIME.plusMinutes(15)
)
private val SCREENING_8 = createScreening(
    originalId = 8,
    auditoriumId = AUDITORIUM_1.id,
    auditoriumLayoutId = AUDITORIUM_LAYOUT_1.id,
    movieId = MOVIE_2.id,
    priceCategoryId = PRICE_CATEGORY_1.id,
    state = ScreeningState.PUBLISHED,
    date = INTEGRATION_TEST_DATE_TIME.toLocalDate().minusDays(1),
    time = LOCAL_TIME.plusHours(3)
)
private val SCREENING_9 = createScreening(
    originalId = 9,
    auditoriumId = AUDITORIUM_1.id,
    auditoriumLayoutId = AUDITORIUM_LAYOUT_1.id,
    movieId = MOVIE_1.id,
    priceCategoryId = PRICE_CATEGORY_1.id,
    state = ScreeningState.DRAFT,
    date = INTEGRATION_TEST_DATE_TIME.toLocalDate(),
    time = LOCAL_TIME.plusHours(3).plusMinutes(10)
)
private val SCREENING_FEE_1 = createScreeningFee(
    originalScreeningId = 1,
    screeningId = SCREENING_7.id,
    surchargeVip = BigDecimal.ONE,
    surchargePremium = BigDecimal.valueOf(0.5),
    surchargeImax = BigDecimal.valueOf(2),
    surchargeUltraX = BigDecimal.valueOf(0.5),
    serviceFeeVip = BigDecimal.ONE,
    serviceFeePremium = BigDecimal.valueOf(0.5),
    serviceFeeImax = BigDecimal.valueOf(2),
    serviceFeeUltraX = BigDecimal.valueOf(0.5),
    surchargeDBox = BigDecimal.valueOf(5),
    serviceFeeGeneral = BigDecimal.valueOf(0.2)
)
private val PRICE_CATEGORY_ITEM_1 = createPriceCategoryItem(
    priceCategoryId = PRICE_CATEGORY_1.id,
    title = "Dospely",
    price = 120.toBigDecimal(),
    number = PriceCategoryItemNumber.PRICE_1
)
private val PRICE_CATEGORY_ITEM_2 = createPriceCategoryItem(
    priceCategoryId = PRICE_CATEGORY_1.id,
    title = null,
    price = 60.toBigDecimal(),
    number = PriceCategoryItemNumber.PRICE_2
)
private val SCREENING_TYPE_1 = createScreeningType(
    originalId = 1,
    title = "Best movies ever, s.r.o."
)
private val SCREENING_TYPE_2 = createScreeningType(
    originalId = 2,
    title = "Worst movies ever, s.r.o."
)
private val SCREENING_TYPES_1 = createScreeningTypes(
    screeningId = SCREENING_7.id,
    screeningTypeId = SCREENING_TYPE_1.id
)
private val SCREENING_TYPES_2 = createScreeningTypes(
    screeningId = SCREENING_7.id,
    screeningTypeId = SCREENING_TYPE_2.id
)
private val SEAT_1 = createSeat(
    originalId = 1,
    auditoriumId = AUDITORIUM_2.id,
    auditoriumLayoutId = AUDITORIUM_LAYOUT_1.id
)
private val SEAT_2 = createSeat(
    originalId = 2,
    auditoriumId = AUDITORIUM_2.id,
    auditoriumLayoutId = AUDITORIUM_LAYOUT_2.id
)
private val SEAT_3 = createSeat(
    originalId = 3,
    auditoriumId = AUDITORIUM_1.id,
    auditoriumLayoutId = AUDITORIUM_LAYOUT_1.id
)
private val RESERVATION_1 = createReservation(
    screeningId = SCREENING_7.id,
    seatId = SEAT_1.id,
    state = ReservationState.UNAVAILABLE
)
private val RESERVATION_2 = createReservation(
    screeningId = SCREENING_7.id,
    seatId = SEAT_2.id,
    state = ReservationState.RESERVED
)
private val RESERVATION_3 = createReservation(
    screeningId = SCREENING_1.id,
    seatId = SEAT_3.id
)
private val TICKET_PRICE_1 = createTicketPrice(
    screeningId = SCREENING_7.id,
    seatId = SEAT_1.id,
    totalPrice = 100.toBigDecimal()
)
private val TICKET_PRICE_2 = createTicketPrice(
    screeningId = SCREENING_7.id,
    seatId = SEAT_2.id,
    totalPrice = 150.toBigDecimal()
)
private val TICKET_PRICE_3 = createTicketPrice(
    screeningId = SCREENING_1.id,
    seatId = SEAT_3.id,
    totalPrice = 50.toBigDecimal()
)
private val TICKET_1 = createTicket(
    originalId = 1,
    screeningId = SCREENING_7.id,
    reservationId = RESERVATION_1.id,
    ticketPriceId = TICKET_PRICE_1.id
)
private val TICKET_2 = createTicket(
    originalId = 2,
    screeningId = SCREENING_7.id,
    reservationId = RESERVATION_2.id,
    ticketPriceId = TICKET_PRICE_2.id
)
private val TICKET_3 = createTicket(
    originalId = 3,
    screeningId = SCREENING_1.id,
    reservationId = RESERVATION_3.id,
    ticketPriceId = TICKET_PRICE_3.id
)
