package com.cleevio.cinemax.api.module.basketitem.service

import com.cleevio.cinemax.api.IntegrationTest
import com.cleevio.cinemax.api.common.constant.ExportableBoolean
import com.cleevio.cinemax.api.common.constant.PaymentType
import com.cleevio.cinemax.api.common.util.isEqualTo
import com.cleevio.cinemax.api.common.util.toDateString
import com.cleevio.cinemax.api.common.util.toTimeString
import com.cleevio.cinemax.api.module.basketitem.model.TicketBasketItemExportRecordModel
import com.cleevio.cinemax.api.module.basketitem.model.TicketBasketItemsSummaryExportRecordModel
import com.cleevio.cinemax.api.module.discountcard.constant.DiscountCardType
import org.apache.poi.xssf.usermodel.XSSFWorkbook
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import java.io.ByteArrayInputStream
import java.math.BigDecimal
import java.time.LocalDate
import java.time.LocalTime
import kotlin.test.assertContains

class TicketBasketItemXlsxExportResultMapperIT @Autowired constructor(
    private val underTest: TicketBasketItemXlsxExportResultMapper,
) : IntegrationTest() {

    @Test
    fun `test mapToExportResultModel - should map all details correctly and generate Excel file`() {
        val exportResult = underTest.mapToExportResultModel(
            data = EXPORT_LIST_DATA to EXPORT_SUMMARY_DATA,
            username = USERNAME,
            basketPaidAtDateFrom = FIXED_DATE.minusDays(30),
            basketPaidAtDateTo = FIXED_DATE.plusDays(30)
        )
        val byteArray = exportResult.inputStream.readAllBytes()
        assert(byteArray.isNotEmpty())

        // open the generated Excel file from the byte array
        val workbook = XSSFWorkbook(ByteArrayInputStream(byteArray))
        val sheet = workbook.getSheet("Prehľad predajov vstupeniek")

        // verify main header
        val mainHeaderRow1 = sheet.getRow(0).getCell(0).stringCellValue
        val mainHeaderRow2 = sheet.getRow(0).getCell(2).stringCellValue
        val mainHeaderRow3 = sheet.getRow(0).getCell(sheet.getRow(0).lastCellNum - 1).stringCellValue

        val fromDate = FIXED_DATE.minusDays(30).toDateString()
        val toDate = FIXED_DATE.plusDays(30).toDateString()
        assertEquals("Kino: Bratislava Bory\nPoužívateľ: $USERNAME", mainHeaderRow1)
        assertEquals("Prehľad predajov vstupeniek\nPredaj od: $fromDate do: $toDate ", mainHeaderRow2)
        assertContains(mainHeaderRow3, "Dátum: ${LocalDate.now().toDateString()}\nČas:")

        // verify column headers
        val columnHeaders = sheet.getRow(4)
        assertEquals("Dátum predaja", columnHeaders.getCell(0).stringCellValue)
        assertEquals("Čas predaja", columnHeaders.getCell(1).stringCellValue)
        assertEquals("Číslo dokladu", columnHeaders.getCell(2).stringCellValue)
        assertEquals("Pokladňa", columnHeaders.getCell(3).stringCellValue)
        assertEquals("Predal", columnHeaders.getCell(4).stringCellValue)
        assertEquals("Sála", columnHeaders.getCell(5).stringCellValue)
        assertEquals("Kino", columnHeaders.getCell(6).stringCellValue)
        assertEquals("Distribútor", columnHeaders.getCell(7).stringCellValue)
        assertEquals("Dátum premiet.", columnHeaders.getCell(8).stringCellValue)
        assertEquals("Čas premiet.", columnHeaders.getCell(9).stringCellValue)
        assertEquals("Film", columnHeaders.getCell(10).stringCellValue)
        assertEquals("Použité", columnHeaders.getCell(11).stringCellValue)
        assertEquals("Platba", columnHeaders.getCell(12).stringCellValue)
        assertEquals("Rad", columnHeaders.getCell(13).stringCellValue)
        assertEquals("Sedadlo", columnHeaders.getCell(14).stringCellValue)
        assertEquals("Zákl. cena", columnHeaders.getCell(15).stringCellValue)
        assertEquals("Prípl. VIP", columnHeaders.getCell(16).stringCellValue)
        assertEquals("Prípl. PP", columnHeaders.getCell(17).stringCellValue)
        assertEquals("Prípl. IMAX", columnHeaders.getCell(18).stringCellValue)
        assertEquals("Prípl. UltraX", columnHeaders.getCell(19).stringCellValue)
        assertEquals("Prípl. DBOX", columnHeaders.getCell(20).stringCellValue)
        assertEquals("Služ. VIP", columnHeaders.getCell(21).stringCellValue)
        assertEquals("Služ. PP", columnHeaders.getCell(22).stringCellValue)
        assertEquals("Služ. IMAX", columnHeaders.getCell(23).stringCellValue)
        assertEquals("Služ. UltraX", columnHeaders.getCell(24).stringCellValue)
        assertEquals("Služ. obec.", columnHeaders.getCell(25).stringCellValue)
        assertEquals("Storno", columnHeaders.getCell(26).stringCellValue)
        assertEquals("Zľava základ", columnHeaders.getCell(27).stringCellValue)
        assertEquals("Zľava štand.", columnHeaders.getCell(28).stringCellValue)
        assertEquals("Zľava neštand.", columnHeaders.getCell(29).stringCellValue)
        assertEquals("Karta/voucher", columnHeaders.getCell(30).stringCellValue)
        assertEquals("Použitá karta", columnHeaders.getCell(31).stringCellValue)
        assertEquals("Číslo karty", columnHeaders.getCell(32).stringCellValue)
        assertEquals("SOZA", columnHeaders.getCell(33).stringCellValue)
        assertEquals("Fond kin.", columnHeaders.getCell(34).stringCellValue)
        assertEquals("Distribútor", columnHeaders.getCell(35).stringCellValue)
        assertEquals("DPH", columnHeaders.getCell(36).stringCellValue)
        assertEquals("3D okuliare", columnHeaders.getCell(37).stringCellValue)

        // verify data rows
        val dataRow = sheet.getRow(5)
        assertEquals(LocalDate.of(2024, 1, 2).toDateString(), dataRow.getCell(0).stringCellValue)
        assertEquals(LocalTime.of(17, 30).toTimeString(), dataRow.getCell(1).stringCellValue)
        assertEquals("569754659", dataRow.getCell(2).stringCellValue)
        assertEquals("pokl11", dataRow.getCell(3).stringCellValue)
        assertEquals("anonymous", dataRow.getCell(4).stringCellValue)
        assertEquals("IMAX", dataRow.getCell(5).stringCellValue)
        assertEquals("Bratislava Bory", dataRow.getCell(6).stringCellValue)
        assertEquals("Hello movies, s.r.o.", dataRow.getCell(7).stringCellValue)
        assertEquals(LocalDate.of(2024, 1, 2).toDateString(), dataRow.getCell(8).stringCellValue)
        assertEquals(LocalTime.of(18, 0).toTimeString(), dataRow.getCell(9).stringCellValue)
        assertEquals("Interstellar", dataRow.getCell(10).stringCellValue)
        assertEquals("áno", dataRow.getCell(11).stringCellValue)
        assertEquals("hotovosť", dataRow.getCell(12).stringCellValue)
        assertEquals("2", dataRow.getCell(13).stringCellValue)
        assertEquals("12", dataRow.getCell(14).stringCellValue)
        assertEquals(11000.11, dataRow.getCell(15).numericCellValue)
        assertTrue(1.00.toBigDecimal() isEqualTo dataRow.getCell(16).numericCellValue.toBigDecimal())
        assertTrue(0.00.toBigDecimal() isEqualTo dataRow.getCell(17).numericCellValue.toBigDecimal())
        assertTrue(0.00.toBigDecimal() isEqualTo dataRow.getCell(18).numericCellValue.toBigDecimal())
        assertTrue(0.00.toBigDecimal() isEqualTo dataRow.getCell(19).numericCellValue.toBigDecimal())
        assertTrue(0.00.toBigDecimal() isEqualTo dataRow.getCell(20).numericCellValue.toBigDecimal())
        assertTrue(1.00.toBigDecimal() isEqualTo dataRow.getCell(21).numericCellValue.toBigDecimal())
        assertTrue(0.00.toBigDecimal() isEqualTo dataRow.getCell(22).numericCellValue.toBigDecimal())
        assertTrue(0.00.toBigDecimal() isEqualTo dataRow.getCell(23).numericCellValue.toBigDecimal())
        assertTrue(0.00.toBigDecimal() isEqualTo dataRow.getCell(24).numericCellValue.toBigDecimal())
        assertTrue(1.00.toBigDecimal() isEqualTo dataRow.getCell(25).numericCellValue.toBigDecimal())
        assertEquals("nie", dataRow.getCell(26).stringCellValue)
        assertEquals("nie", dataRow.getCell(27).stringCellValue)
        assertEquals("W7", dataRow.getCell(28).stringCellValue)
        assertEquals("", dataRow.getCell(29).stringCellValue)
        assertEquals("karta", dataRow.getCell(30).stringCellValue)
        assertEquals("FILM karta", dataRow.getCell(31).stringCellValue)
        assertEquals("12309876", dataRow.getCell(32).stringCellValue)
        assertTrue(0.10.toBigDecimal() isEqualTo dataRow.getCell(33).numericCellValue.toBigDecimal())
        assertTrue(0.10.toBigDecimal() isEqualTo dataRow.getCell(34).numericCellValue.toBigDecimal())
        assertTrue(0.10.toBigDecimal() isEqualTo dataRow.getCell(35).numericCellValue.toBigDecimal())
        assertTrue(1.60.toBigDecimal() isEqualTo dataRow.getCell(36).numericCellValue.toBigDecimal())
        assertEquals("nie", dataRow.getCell(37).stringCellValue)
    }

    @Test
    fun `test mapToExportResultModel - should map summary sheet correctly`() {
        val exportResult = underTest.mapToExportResultModel(
            data = EXPORT_LIST_DATA to EXPORT_SUMMARY_DATA,
            username = USERNAME,
            basketPaidAtDateFrom = FIXED_DATE.minusDays(30),
            basketPaidAtDateTo = FIXED_DATE.plusDays(30)
        )
        val byteArray = exportResult.inputStream.readAllBytes()
        assert(byteArray.isNotEmpty())

        // open the generated Excel file from the byte array
        val workbook = XSSFWorkbook(ByteArrayInputStream(byteArray))
        val summarySheet = workbook.getSheet("Zhrnutie")

        // verify main header in summary sheet
        val mainHeaderRow1 = summarySheet.getRow(0).getCell(0).stringCellValue
        val mainHeaderRow2 = summarySheet.getRow(0).getCell(2).stringCellValue
        val mainHeaderRow3 = summarySheet.getRow(0).getCell(summarySheet.getRow(0).lastCellNum - 1).stringCellValue

        val fromDate = FIXED_DATE.minusDays(30).toDateString()
        val toDate = FIXED_DATE.plusDays(30).toDateString()
        assertEquals("Kino: Bratislava Bory\nPoužívateľ: $USERNAME", mainHeaderRow1)
        assertEquals("Prehľad predajov vstupeniek\nPredaj od: $fromDate do: $toDate ", mainHeaderRow2)
        assertContains(mainHeaderRow3, "Dátum: ${LocalDate.now().toDateString()}\nČas:")

        // verify sales summary section
        assertEquals("Zhrnutie podľa vybraných predajov", summarySheet.getRow(4).getCell(0).stringCellValue)
        assertEquals("Počet premietaní", summarySheet.getRow(5).getCell(0).stringCellValue)
        assertEquals("125", summarySheet.getRow(5).getCell(7).stringCellValue)
        assertEquals("Počet predaných vstupeniek", summarySheet.getRow(6).getCell(0).stringCellValue)
        assertEquals("450", summarySheet.getRow(6).getCell(7).stringCellValue)
        assertEquals("Počet použitých vstupeniek", summarySheet.getRow(7).getCell(0).stringCellValue)
        assertEquals("380", summarySheet.getRow(7).getCell(7).stringCellValue)
        assertEquals("Suma hotovosť", summarySheet.getRow(8).getCell(0).stringCellValue)
        assertEquals("€ 2500,50", summarySheet.getRow(8).getCell(7).stringCellValue)
        assertEquals("Suma bezhotovosť", summarySheet.getRow(9).getCell(0).stringCellValue)
        assertEquals("€ 3750,75", summarySheet.getRow(9).getCell(7).stringCellValue)
        assertEquals("Tržba hrubá", summarySheet.getRow(10).getCell(0).stringCellValue)
        assertEquals("€ 6251,25", summarySheet.getRow(10).getCell(7).stringCellValue)
        assertEquals("Tržba čistá", summarySheet.getRow(11).getCell(0).stringCellValue)
        assertEquals("€ 5250,00", summarySheet.getRow(11).getCell(7).stringCellValue)
        assertEquals("Odvod distribútorovi", summarySheet.getRow(12).getCell(0).stringCellValue)
        assertEquals("€ 625,13", summarySheet.getRow(12).getCell(7).stringCellValue)
        assertEquals("Odvod SOZA", summarySheet.getRow(13).getCell(0).stringCellValue)
        assertEquals("€ 62,51", summarySheet.getRow(13).getCell(7).stringCellValue)
        assertEquals("Odvod fondu kinematografie", summarySheet.getRow(14).getCell(0).stringCellValue)
        assertEquals("€ 62,51", summarySheet.getRow(14).getCell(7).stringCellValue)
        assertEquals("DPH", summarySheet.getRow(15).getCell(0).stringCellValue)
        assertEquals("€ 251,10", summarySheet.getRow(15).getCell(7).stringCellValue)
        assertEquals("Počet storno", summarySheet.getRow(16).getCell(0).stringCellValue)
        assertEquals("15", summarySheet.getRow(16).getCell(7).stringCellValue)
        assertEquals("Suma storno", summarySheet.getRow(17).getCell(0).stringCellValue)
        assertEquals("€ 225,50", summarySheet.getRow(17).getCell(7).stringCellValue)

        // verify ticket types section
        assertEquals("Typy vstupeniek", summarySheet.getRow(19).getCell(0).stringCellValue)
        assertEquals("Služby obecné", summarySheet.getRow(20).getCell(0).stringCellValue)
        assertEquals("€ 1250,00", summarySheet.getRow(20).getCell(7).stringCellValue)
        assertEquals("Služby VIP", summarySheet.getRow(21).getCell(0).stringCellValue)
        assertEquals("€ 750,50", summarySheet.getRow(21).getCell(7).stringCellValue)
        assertEquals("Služby Premium", summarySheet.getRow(22).getCell(0).stringCellValue)
        assertEquals("€ 500,25", summarySheet.getRow(22).getCell(7).stringCellValue)
        assertEquals("Služby IMAX", summarySheet.getRow(23).getCell(0).stringCellValue)
        assertEquals("€ 1200,75", summarySheet.getRow(23).getCell(7).stringCellValue)
        assertEquals("Služby Ultra X", summarySheet.getRow(24).getCell(0).stringCellValue)
        assertEquals("€ 350,25", summarySheet.getRow(24).getCell(7).stringCellValue)
        assertEquals("Príplatky VIP", summarySheet.getRow(25).getCell(0).stringCellValue)
        assertEquals("€ 250,50", summarySheet.getRow(25).getCell(7).stringCellValue)
        assertEquals("Príplatky DBox", summarySheet.getRow(26).getCell(0).stringCellValue)
        assertEquals("€ 175,25", summarySheet.getRow(26).getCell(7).stringCellValue)
        assertEquals("Príplatky Premium", summarySheet.getRow(27).getCell(0).stringCellValue)
        assertEquals("€ 125,75", summarySheet.getRow(27).getCell(7).stringCellValue)
        assertEquals("Príplatky IMAX", summarySheet.getRow(28).getCell(0).stringCellValue)
        assertEquals("€ 300,50", summarySheet.getRow(28).getCell(7).stringCellValue)
        assertEquals("Príplatky Ultra X", summarySheet.getRow(29).getCell(0).stringCellValue)
        assertEquals("€ 150,25", summarySheet.getRow(29).getCell(7).stringCellValue)

        // verify technology counts section
        assertEquals("Počet podľa technológií", summarySheet.getRow(31).getCell(0).stringCellValue)
        assertEquals("Počet obecné", summarySheet.getRow(32).getCell(0).stringCellValue)
        assertEquals("250", summarySheet.getRow(32).getCell(7).stringCellValue)
        assertEquals("Počet VIP", summarySheet.getRow(33).getCell(0).stringCellValue)
        assertEquals("75", summarySheet.getRow(33).getCell(7).stringCellValue)
        assertEquals("Počet Premium", summarySheet.getRow(34).getCell(0).stringCellValue)
        assertEquals("45", summarySheet.getRow(34).getCell(7).stringCellValue)
        assertEquals("Počet IMAX", summarySheet.getRow(35).getCell(0).stringCellValue)
        assertEquals("60", summarySheet.getRow(35).getCell(7).stringCellValue)
        assertEquals("Počet Ultra X", summarySheet.getRow(36).getCell(0).stringCellValue)
        assertEquals("30", summarySheet.getRow(36).getCell(7).stringCellValue)
        assertEquals("Počet DBox", summarySheet.getRow(37).getCell(0).stringCellValue)
        assertEquals("15", summarySheet.getRow(37).getCell(7).stringCellValue)

        // verify discounts section
        assertEquals("Zvýhodnené vstupné", summarySheet.getRow(39).getCell(0).stringCellValue)
        assertEquals("Bez slevy", summarySheet.getRow(40).getCell(0).stringCellValue)
        assertEquals("300", summarySheet.getRow(40).getCell(7).stringCellValue)
        assertEquals("Študentská zľava", summarySheet.getRow(41).getCell(0).stringCellValue)
        assertEquals("85", summarySheet.getRow(41).getCell(7).stringCellValue)
        assertEquals("Seniorská zľava", summarySheet.getRow(42).getCell(0).stringCellValue)
        assertEquals("65", summarySheet.getRow(42).getCell(7).stringCellValue)
    }
}

private const val USERNAME = "username"

private val FIXED_DATE = LocalDate.of(2024, 6, 26)
private val EXPORT_LIST_DATA = listOf(
    TicketBasketItemExportRecordModel(
        basketPaidAtDate = LocalDate.of(2024, 1, 2),
        basketPaidAtTime = LocalTime.of(17, 30),
        basketUpdatedBy = "anonymous",
        receiptNumber = "569754659",
        paymentPosConfigurationTitle = "pokl11",
        auditoriumCode = "IMAX",
        branchName = "Bratislava Bory",
        distributorTitle = "Hello movies, s.r.o.",
        screeningDate = LocalDate.of(2024, 1, 2),
        screeningTime = LocalTime.of(18, 0),
        movieRawTitle = "Interstellar",
        isUsed = ExportableBoolean.TRUE,
        paymentType = PaymentType.CASH,
        seatRow = "2",
        seatNumber = "12",
        basePrice = BigDecimal.valueOf(11000.11),
        surchargeVip = BigDecimal.ONE,
        surchargePremium = BigDecimal.ZERO,
        surchargeImax = BigDecimal.ZERO,
        surchargeUltraX = BigDecimal.ZERO,
        surchargeDBox = BigDecimal.ZERO,
        serviceFeeVip = BigDecimal.ONE,
        serviceFeePremium = BigDecimal.ZERO,
        serviceFeeImax = BigDecimal.ZERO,
        serviceFeeUltraX = BigDecimal.ZERO,
        serviceFeeGeneral = BigDecimal.ONE,
        isCancelled = ExportableBoolean.FALSE,
        isPriceCategoryItemDiscounted = ExportableBoolean.FALSE,
        primaryTicketDiscountCode = "W7",
        discountCardType = DiscountCardType.CARD,
        discountCardTitle = "FILM karta",
        discountCardCode = "12309876",
        proDeduction = 0.1.toBigDecimal(),
        filmFondDeduction = 0.1.toBigDecimal(),
        distributorDeduction = 0.1.toBigDecimal(),
        taxAmount = 1.6.toBigDecimal(),
        includes3dGlasses = ExportableBoolean.FALSE
    )
)

private val EXPORT_SUMMARY_DATA = TicketBasketItemsSummaryExportRecordModel(
    sales = TicketBasketItemsSummaryExportRecordModel.SalesSummary(
        screeningsCount = 125,
        ticketsCount = 450,
        ticketsUsedCount = 380,
        salesCash = 2500.50.toBigDecimal(),
        salesCashless = 3750.75.toBigDecimal(),
        grossSales = 6251.25.toBigDecimal(),
        netSales = 5250.00.toBigDecimal(),
        proDeduction = 62.51.toBigDecimal(),
        filmFondDeduction = 62.51.toBigDecimal(),
        distributorDeduction = 625.13.toBigDecimal(),
        taxAmount = 251.10.toBigDecimal(),
        cancelledTicketsCount = 15,
        cancelledTicketsExpense = 225.50.toBigDecimal()
    ),
    serviceFees = TicketBasketItemsSummaryExportRecordModel.ServiceFeesSummary(
        general = 1250.00.toBigDecimal(),
        generalCount = 250,
        vip = 750.50.toBigDecimal(),
        vipCount = 50,
        premiumPlus = 500.25.toBigDecimal(),
        premiumPlusCount = 20,
        imax = 1200.75.toBigDecimal(),
        imaxCount = 30,
        ultraX = 350.25.toBigDecimal(),
        ultraXCount = 20,
        dolbyAtmos = 350.25.toBigDecimal()
    ),
    surcharges = TicketBasketItemsSummaryExportRecordModel.SurchargesSummary(
        vip = 250.50.toBigDecimal(),
        vipCount = 25,
        dBox = 175.25.toBigDecimal(),
        dBoxCount = 15,
        premiumPlus = 125.75.toBigDecimal(),
        premiumPlusCount = 25,
        imax = 300.50.toBigDecimal(),
        imaxCount = 30,
        ultraX = 150.25.toBigDecimal(),
        ultraXCount = 10,
        dolbyAtmos = 150.25.toBigDecimal()
    ),
    technologyCount = TicketBasketItemsSummaryExportRecordModel.TechnologyCountsSummary(
        general = 250,
        vip = 75,
        dBox = 15,
        premiumPlus = 45,
        imax = 60,
        dolbyAtmos = 30,
        total = 475
    ),
    discounts = listOf(
        TicketBasketItemsSummaryExportRecordModel.DiscountSummary(
            title = "Bez slevy",
            count = 300
        ),
        TicketBasketItemsSummaryExportRecordModel.DiscountSummary(
            title = "Študentská zľava",
            count = 85
        ),
        TicketBasketItemsSummaryExportRecordModel.DiscountSummary(
            title = "Seniorská zľava",
            count = 65
        )
    )
)
