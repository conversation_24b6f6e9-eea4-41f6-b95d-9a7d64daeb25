package com.cleevio.cinemax.api.module.supplier.service

import com.cleevio.cinemax.api.MssqlIntegrationTest
import com.cleevio.cinemax.api.module.supplier.service.command.CreateOrUpdateSupplierCommand
import com.cleevio.cinemax.api.module.synchronizationfrommssql.constant.SynchronizationFromMssqlType
import com.cleevio.cinemax.api.module.synchronizationfrommssql.entity.SynchronizationFromMssql
import com.cleevio.cinemax.api.module.synchronizationfrommssql.service.command.CreateOrUpdateSynchronizationFromMssqlCommand
import io.mockk.Runs
import io.mockk.every
import io.mockk.just
import io.mockk.verifyAll
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.test.context.jdbc.Sql
import org.springframework.test.context.jdbc.SqlConfig
import org.springframework.test.context.jdbc.SqlGroup
import java.time.LocalDateTime
import java.util.Optional
import kotlin.test.assertEquals
import kotlin.test.assertTrue

@SqlGroup(
    Sql(
        scripts = [
            "/db/mssql-buffet/test/init_mssql_supplier.sql"
        ],
        config = SqlConfig(
            dataSource = "mssqlBuffetDataSource",
            transactionManager = "mssqlChainedTransactionManager"
        )
    ),
    Sql(
        scripts = [
            "/db/mssql-buffet/test/drop_mssql_supplier.sql"
        ],
        config = SqlConfig(
            dataSource = "mssqlBuffetDataSource",
            transactionManager = "mssqlChainedTransactionManager"
        ),
        executionPhase = Sql.ExecutionPhase.AFTER_TEST_METHOD
    )
)
class SupplierMssqlSynchronizationServiceIT @Autowired constructor(
    private val underTest: SupplierMssqlSynchronizationService,
    private val supplierMssqlFinderRepository: SupplierMssqlFinderRepository,
) : MssqlIntegrationTest() {

    @Test
    fun `test synchronize all - no PSQL suppliers, 3 MSSQL suppliers - should create 3 suppliers`() {
        every { synchronizationFromMssqlFinderServiceMock.findByType(any()) } returns null
        every { supplierServiceMock.syncCreateOrUpdateSupplier(any()) } just Runs
        every { synchronizationFromMssqlServiceMock.createOrUpdate(any()) } returns SYNCHRONIZATION_FROM_MSSQL

        underTest.synchronizeAll()

        val commandCaptor = mutableListOf<CreateOrUpdateSupplierCommand>()

        verifyAll {
            synchronizationFromMssqlFinderServiceMock.findByType(SynchronizationFromMssqlType.SUPPLIER)
            supplierServiceMock.syncCreateOrUpdateSupplier(capture(commandCaptor))
            synchronizationFromMssqlServiceMock.createOrUpdate(
                CreateOrUpdateSynchronizationFromMssqlCommand(
                    type = SynchronizationFromMssqlType.SUPPLIER,
                    lastSynchronization = SUPPLIER_3_UPDATED_AT
                )
            )
        }

        assertTrue(supplierMssqlFinderRepository.findAllByUpdatedAtGt(null).size == 3)
        assertTrue(commandCaptor.size == 3)
        assertEquals(EXPECTED_COMMAND_1, commandCaptor[0])
        assertEquals(EXPECTED_COMMAND_2, commandCaptor[1])
        assertEquals(EXPECTED_COMMAND_3, commandCaptor[2])
    }

    @Test
    fun `test synchronize all - 2 PSQL suppliers, 3 MSSQL suppliers - should create 1 supplier`() {
        every { synchronizationFromMssqlFinderServiceMock.findByType(any()) } returns SUPPLIER_2_UPDATED_AT
        every { supplierServiceMock.syncCreateOrUpdateSupplier(any()) } just Runs
        every { synchronizationFromMssqlServiceMock.createOrUpdate(any()) } returns SYNCHRONIZATION_FROM_MSSQL

        underTest.synchronizeAll()

        val commandCaptor = mutableListOf<CreateOrUpdateSupplierCommand>()

        verifyAll {
            synchronizationFromMssqlFinderServiceMock.findByType(SynchronizationFromMssqlType.SUPPLIER)
            supplierServiceMock.syncCreateOrUpdateSupplier(capture(commandCaptor))
            synchronizationFromMssqlServiceMock.createOrUpdate(
                CreateOrUpdateSynchronizationFromMssqlCommand(
                    type = SynchronizationFromMssqlType.SUPPLIER,
                    lastSynchronization = SUPPLIER_3_UPDATED_AT
                )
            )
        }

        assertTrue(supplierMssqlFinderRepository.findAllByUpdatedAtGt(SUPPLIER_3_UPDATED_AT).isEmpty())
        assertTrue(commandCaptor.size == 1)
        assertEquals(EXPECTED_COMMAND_3, commandCaptor[0])
    }
}

private val SUPPLIER_3_UPDATED_AT = LocalDateTime.of(2019, 5, 5, 16, 37, 0)
private val SUPPLIER_2_UPDATED_AT = LocalDateTime.of(2019, 5, 4, 16, 37, 0)
private val EXPECTED_COMMAND_1 = CreateOrUpdateSupplierCommand(
    id = null,
    originalId = 1,
    code = "SUP1",
    title = "Supplier One",
    addressStreet = Optional.of("Street 1"),
    addressCity = Optional.of("City 1"),
    addressPostCode = Optional.of("10001"),
    contactName = Optional.of("Contact Name 1"),
    contactPhone = Optional.of("*********"),
    contactEmails = Optional.of(setOf("<EMAIL>")),
    bankName = Optional.of("Bank One"),
    bankAccount = Optional.of("123456"),
    idNumber = Optional.of("1111"),
    taxIdNumber = Optional.of("111-22-3333")
)
private val EXPECTED_COMMAND_2 = CreateOrUpdateSupplierCommand(
    id = null,
    originalId = 2,
    code = "SUP2",
    title = "Supplier Two",
    addressStreet = Optional.of("Street 2"),
    addressCity = Optional.of("City 2"),
    addressPostCode = Optional.of("10002"),
    contactName = Optional.of("Contact Name 2"),
    contactPhone = Optional.of("*********"),
    contactEmails = Optional.of(setOf("<EMAIL>")),
    bankName = Optional.of("Bank Two"),
    bankAccount = Optional.of("654321"),
    idNumber = Optional.of("2222"),
    taxIdNumber = Optional.of("222-33-4444")
)
private val EXPECTED_COMMAND_3 = CreateOrUpdateSupplierCommand(
    id = null,
    originalId = 3,
    code = "SUP3",
    title = "Supplier Three",
    addressStreet = null,
    addressCity = null,
    addressPostCode = null,
    contactName = null,
    contactPhone = null,
    contactEmails = Optional.of(emptySet()),
    bankName = null,
    bankAccount = null,
    idNumber = null,
    taxIdNumber = null
)
private val SYNCHRONIZATION_FROM_MSSQL = SynchronizationFromMssql(
    type = SynchronizationFromMssqlType.SUPPLIER,
    lastSynchronization = LocalDateTime.MIN,
    lastHeartbeat = null
)
