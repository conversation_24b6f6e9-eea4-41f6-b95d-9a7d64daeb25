package com.cleevio.cinemax.api.module.movie.entity

import com.cleevio.cinemax.api.common.util.toUUID
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.CsvSource
import java.time.LocalDate
import kotlin.test.assertEquals

class MovieTest {

    @ParameterizedTest
    @CsvSource(
        "Mlad<PERSON> srdcia 2D (ST), ftx9fx",
        "Mladé srdcia MP4 (ST), cxkaed",
        "Alenka a zázrak z cudzej krajiny 2D (OV), 7f323d",
        "Pink Floyd at Pompeii – MCMLXXII IMAX 2D (ČT), 46a1ke",
        "Hriešnici IMAX 2D (ST), tye36b",
        "Hriešnici, u52jme",
        "Titanic: 25. výročie IMAX 3D (ST), 010mw6"
    )
    fun `test generateMessagingCode, should always generate the same code`(
        rawTitle: String,
        expectedResult: String,
    ) {
        val movie = Movie(
            rawTitle = rawTitle,
            title = rawTitle,
            code = "12345",
            distributorId = "d69d9aa9-5b6f-4d9a-af1d-58e80bb8a351".toUUID(),
            premiereDate = LocalDate.parse("2023-02-09")
        )
        assertEquals(expectedResult, movie.messagingCode)
    }
}
