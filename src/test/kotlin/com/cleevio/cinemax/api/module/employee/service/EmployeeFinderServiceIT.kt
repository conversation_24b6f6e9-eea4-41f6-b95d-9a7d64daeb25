package com.cleevio.cinemax.api.module.employee.service

import com.cleevio.cinemax.api.DatabaseCleanup
import com.cleevio.cinemax.api.IntegrationTest
import com.cleevio.cinemax.api.common.service.query.SearchUnfilteredQueryDeprecated
import com.cleevio.cinemax.api.module.employee.constant.EmployeeRole
import com.cleevio.cinemax.api.util.assertEmployeeEquals
import com.cleevio.cinemax.api.util.createEmployee
import com.cleevio.cinemax.api.util.mapToCreateOrUpdateEmployeeCommand
import com.cleevio.cinemax.psql.tables.EmployeeColumnNames
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.data.domain.PageRequest
import org.springframework.data.domain.Sort
import java.time.LocalDateTime
import kotlin.test.assertEquals

class EmployeeFinderServiceIT @Autowired constructor(
    private val underTest: EmployeeFinderService,
    private val employeeService: EmployeeService,
) : IntegrationTest(DatabaseCleanup.AFTER_EACH) {

    @BeforeEach
    fun setUp() {
        setOf(EMPLOYEE_1, EMPLOYEE_2, EMPLOYEE_3).forEach {
            employeeService.syncCreateOrUpdateEmployee(mapToCreateOrUpdateEmployeeCommand(it))
        }
        setOf(EMPLOYEE_4, EMPLOYEE_5).forEach {
            employeeService.syncCreateOrUpdateEmployee(mapToCreateOrUpdateEmployeeCommand(it))
        }
    }

    @Test
    fun `test search - accessibleAt default sort - should find employees`() {
        assertEquals(5, underTest.findAll().size)

        val employees = underTest.search(
            SearchUnfilteredQueryDeprecated(
                pageable = PageRequest.of(
                    0,
                    10,
                    Sort.by(Sort.Order.desc(EmployeeColumnNames.ACCESSIBLE_AT))
                )
            )
        )

        assertEquals(5, employees.content.size)
        assertEmployeeEquals(EMPLOYEE_1, employees.content[0])
        assertEmployeeEquals(EMPLOYEE_2, employees.content[1])
        assertEmployeeEquals(EMPLOYEE_3, employees.content[2])
        assertEmployeeEquals(EMPLOYEE_4, employees.content[3])
        assertEmployeeEquals(EMPLOYEE_5, employees.content[4])

        assertEquals(1, employees.totalPages)
        assertEquals(5, employees.totalElements)
    }

    @Test
    fun `test search - pagination, sorting on username - should find correct employees`() {
        assertEquals(5, underTest.findAll().size)

        val employees = underTest.search(
            SearchUnfilteredQueryDeprecated(
                pageable = PageRequest.of(
                    0,
                    10,
                    Sort.by(Sort.Order.asc(EmployeeColumnNames.USERNAME))
                )
            )
        )

        assertEquals(5, employees.content.size)
        assertEmployeeEquals(EMPLOYEE_3, employees.content[0])
        assertEmployeeEquals(EMPLOYEE_4, employees.content[1])
        assertEmployeeEquals(EMPLOYEE_5, employees.content[2])
        assertEmployeeEquals(EMPLOYEE_1, employees.content[3])
        assertEmployeeEquals(EMPLOYEE_2, employees.content[4])

        assertEquals(1, employees.totalPages)
        assertEquals(5, employees.totalElements)
    }

    @Test
    fun `test search - pagination, sorting on originalId desc - should find correct employees`() {
        assertEquals(5, underTest.findAll().size)

        val employees = underTest.search(
            SearchUnfilteredQueryDeprecated(
                pageable = PageRequest.of(
                    0,
                    10,
                    Sort.by(Sort.Order.desc(EmployeeColumnNames.ORIGINAL_ID))
                )
            )
        )

        assertEquals(5, employees.content.size)
        assertEmployeeEquals(EMPLOYEE_3, employees.content[0])
        assertEmployeeEquals(EMPLOYEE_2, employees.content[1])
        assertEmployeeEquals(EMPLOYEE_1, employees.content[2])
        assertEmployeeEquals(EMPLOYEE_4, employees.content[3])
        assertEmployeeEquals(EMPLOYEE_5, employees.content[4])

        assertEquals(1, employees.totalPages)
        assertEquals(5, employees.totalElements)
    }
}

private val EMPLOYEE_1 = createEmployee(
    originalId = 1,
    username = "jano",
    fullName = "Jano Janosik",
    accessibleAt = LocalDateTime.of(2024, 4, 30, 10, 0)
)
private val EMPLOYEE_2 = createEmployee(
    originalId = 2,
    username = "jirka",
    fullName = "Jirka Hlavka",
    accessibleAt = LocalDateTime.of(2024, 3, 30, 10, 0)
)
private val EMPLOYEE_3 = createEmployee(
    originalId = 3,
    username = "employee3",
    fullName = "Johhny Cashier",
    accessibleAt = LocalDateTime.of(2024, 2, 28, 10, 0)
)
private val EMPLOYEE_4 = createEmployee(
    originalId = null,
    username = "employee4",
    fullName = "Roy Orbison",
    role = EmployeeRole.MANAGER,
    accessibleAt = LocalDateTime.of(1995, 4, 30, 10, 0)
)
private val EMPLOYEE_5 = createEmployee(
    originalId = null,
    username = "employee5",
    fullName = "Ray Charles",
    role = EmployeeRole.MANAGER,
    accessibleAt = null
)
