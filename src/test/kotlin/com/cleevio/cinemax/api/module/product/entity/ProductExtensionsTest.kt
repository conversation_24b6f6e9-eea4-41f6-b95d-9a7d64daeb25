package com.cleevio.cinemax.api.module.product.entity

import com.cleevio.cinemax.api.common.util.isEqualTo
import com.cleevio.cinemax.api.module.product.event.toMessagingEvent
import com.cleevio.cinemax.api.module.product.service.command.AdminCreateProductCommand
import com.cleevio.cinemax.api.util.createProduct
import com.cleevio.cinemax.api.util.toUUID
import org.junit.jupiter.api.Assertions.assertEquals
import kotlin.test.Test
import kotlin.test.assertNull
import kotlin.test.assertTrue

class ProductExtensionsTest {

    @Test
    fun `test toMessagingEvent - should map Product to AdminProductCreatedOrUpdatedEvent correctly`() {
        val product = createProduct(
            code = "1234",
            productCategoryId = 1.toUUID(),
            title = "Produkt 1"
        )
        val productComponentId = 2.toUUID()
        val productInProductId = 3.toUUID()
        val event = product.toMessagingEvent(
            productCategoryCode = "01",
            imageFileId = null,
            productComposition = listOf(
                AdminCreateProductCommand.AdminCreateProductProductCompositionCommand(
                    productComponentId = productComponentId,
                    productInProductId = productInProductId,
                    productInProductPrice = 3.toBigDecimal(),
                    quantity = 5.toBigDecimal()
                )
            ),
            productComponentIdToCode = mapOf(
                productComponentId to "11"
            ),
            productInProductIdToCode = mapOf(
                productInProductId to "22"
            )
        )

        assertEquals(product.title, event.title)
        assertEquals(product.code, event.code)
        assertEquals(product.active, event.active)
        assertEquals(product.type, event.type)
        assertEquals("01", event.productCategoryCode)
        assertNull(event.imageFileId)
        assertTrue(product.price isEqualTo event.price)
        assertEquals(product.soldInBuffet, event.soldInBuffet)
        assertEquals(product.soldInCafe, event.soldInCafe)
        assertEquals(product.soldInVip, event.soldInVip)
        assertEquals(product.order, event.order)
        assertEquals(product.isPackagingDeposit, event.isPackagingDeposit)
        assertEquals(product.stockQuantityThreshold, event.stockQuantityThreshold)
        assertEquals(product.taxRate, event.taxRate)
        assertTrue(product.discountAmount isEqualTo event.discountAmount)
        assertEquals(product.discountPercentage, event.discountPercentage)

        assertEquals(1, event.productComposition.size)
        val eventProductComposition = event.productComposition[0]
        assertEquals("11", eventProductComposition.productComponentCode)
        assertEquals("22", eventProductComposition.productInProductCode)
        assertTrue(3.toBigDecimal() isEqualTo eventProductComposition.productInProductPrice)
        assertTrue(5.toBigDecimal() isEqualTo eventProductComposition.quantity)
    }
}
