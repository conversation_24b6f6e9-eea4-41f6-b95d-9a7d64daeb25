package com.cleevio.cinemax.api.module.auditorium.service

import com.cleevio.cinemax.api.DatabaseCleanup
import com.cleevio.cinemax.api.IntegrationTest
import com.cleevio.cinemax.api.module.auditorium.entity.Auditorium
import com.cleevio.cinemax.api.module.auditorium.exception.AuditoriumNotFoundException
import com.cleevio.cinemax.api.module.auditoriumdefault.entity.AuditoriumDefault
import com.cleevio.cinemax.api.module.auditoriumdefault.service.AuditoriumDefaultRepository
import com.cleevio.cinemax.api.util.createAuditorium
import com.cleevio.cinemax.api.util.createAuditoriumDefault
import com.cleevio.cinemax.api.util.mapToCreateOrUpdateAuditoriumCommand
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import org.springframework.beans.factory.annotation.Autowired
import java.util.UUID
import kotlin.jvm.optionals.getOrNull
import kotlin.test.assertEquals
import kotlin.test.assertNotNull
import kotlin.test.assertTrue

class AuditoriumServiceIT @Autowired constructor(
    private val underTest: AuditoriumService,
    private val auditoriumRepository: AuditoriumRepository,
    private val auditoriumJooqFinderService: AuditoriumJooqFinderService,
    private val auditoriumDefaultRepository: AuditoriumDefaultRepository,
) : IntegrationTest(DatabaseCleanup.AFTER_EACH) {

    @Test
    fun `test sync create or update auditorium - should create auditorium`() {
        val command = mapToCreateOrUpdateAuditoriumCommand(AUDITORIUM_1)
        underTest.syncCreateOrUpdateAuditorium(command)

        val createdAuditorium = auditoriumJooqFinderService.findByOriginalId(AUDITORIUM_1.originalId!!)
        assertNotNull(createdAuditorium)
        assertAuditoriumEquals(AUDITORIUM_1, createdAuditorium)
    }

    @Test
    fun `test sync create or update auditorium - one auditorium exists - insert equal auditorium so it should update`() {
        val command = mapToCreateOrUpdateAuditoriumCommand(AUDITORIUM_1)
        underTest.syncCreateOrUpdateAuditorium(command)
        underTest.syncCreateOrUpdateAuditorium(command)

        val updatedAuditorium = auditoriumJooqFinderService.findByOriginalId(AUDITORIUM_1.originalId!!)
        assertNotNull(updatedAuditorium)
        assertAuditoriumEquals(AUDITORIUM_1, updatedAuditorium)

        val auditoriums = auditoriumJooqFinderService.findAll()
        assertEquals(auditoriums.size, 1)

        assertTrue { updatedAuditorium.updatedAt.isAfter(AUDITORIUM_1.updatedAt) }
    }

    @Test
    fun `test sync create or update auditorium - two auditoriums - should create two auditoriums`() {
        auditoriumRepository.save(AUDITORIUM_1)

        val command = mapToCreateOrUpdateAuditoriumCommand(AUDITORIUM_2)
        underTest.syncCreateOrUpdateAuditorium(command)

        val auditoriums = auditoriumJooqFinderService.findAll()
        assertEquals(auditoriums.size, 2)
        assertAuditoriumEquals(AUDITORIUM_1, auditoriums.first { it.originalId == AUDITORIUM_1.originalId })
        assertAuditoriumEquals(AUDITORIUM_2, auditoriums.first { it.originalId == AUDITORIUM_2.originalId })
    }

    @Test
    fun `test sync create or update auditorium - code cannot be parsed from title - should throw exception`() {
        val commandWithInvalidTitle = mapToCreateOrUpdateAuditoriumCommand(AUDITORIUM_1).copy(
            title = "123 345 678"
        )
        assertThrows<IllegalStateException> {
            underTest.syncCreateOrUpdateAuditorium(commandWithInvalidTitle)
        }
    }

    @Test
    fun `test admin create or update auditorium - update but auditorium not found - should throw exception`() {
        val commandWithNonExistingAuditoriumId = mapToCreateOrUpdateAuditoriumCommand(AUDITORIUM_1).copy(
            originalId = null,
            id = UUID.fromString("3ec338ab-74be-4c40-9792-84cb7a7fef06")
        )
        assertThrows<AuditoriumNotFoundException> {
            underTest.adminCreateOrUpdateAuditorium(commandWithNonExistingAuditoriumId)
        }
    }

    @Test
    fun `test admin create or update auditorium - valid command - should correctly create auditorium and defaults`() {
        val commandToCreate = mapToCreateOrUpdateAuditoriumCommand(
            auditorium = AUDITORIUM_1,
            auditoriumDefault = AUDITORIUM_DEFAULT(AUDITORIUM_1.id)
        ).copy(id = null)

        assertTrue { auditoriumRepository.findAll().isEmpty() }
        assertTrue { auditoriumDefaultRepository.findAll().isEmpty() }

        val auditoriumId = underTest.adminCreateOrUpdateAuditorium(commandToCreate)

        assertEquals(1, auditoriumRepository.findAll().size)
        auditoriumRepository.findById(auditoriumId).getOrNull().let {
            assertNotNull(it)
            assertAuditoriumEquals(AUDITORIUM_1, it)
        }

        assertEquals(1, auditoriumDefaultRepository.findAll().size)
        auditoriumDefaultRepository.findByAuditoriumId(auditoriumId).let {
            assertNotNull(it)
            assertAuditoriumDefaultsEquals(AUDITORIUM_DEFAULT(auditoriumId), it)
        }
    }

    @Test
    fun `test admin create or update auditorium - valid command - should correctly update auditorium and defaults`() {
        auditoriumRepository.save(AUDITORIUM_1)
        auditoriumDefaultRepository.save(createAuditoriumDefault(auditoriumId = AUDITORIUM_1.id))
        assertEquals(1, auditoriumRepository.findAll().size)
        assertEquals(1, auditoriumDefaultRepository.findAll().size)

        val commandToUpdate = mapToCreateOrUpdateAuditoriumCommand(
            auditorium = AUDITORIUM_2,
            auditoriumDefault = AUDITORIUM_DEFAULT(AUDITORIUM_2.id)
        ).copy(id = AUDITORIUM_1.id)

        val auditoriumId = underTest.adminCreateOrUpdateAuditorium(commandToUpdate)

        assertEquals(1, auditoriumRepository.findAll().size)
        auditoriumRepository.findById(auditoriumId).getOrNull().let {
            assertNotNull(it)
            assertEquals(AUDITORIUM_1.id, it.id)
            assertEquals(AUDITORIUM_1.originalId, it.originalId)
            assertEquals(AUDITORIUM_2.originalCode, it.originalCode)
            assertEquals(AUDITORIUM_2.code, it.code)
            assertEquals(AUDITORIUM_2.title, it.title)
            assertEquals(AUDITORIUM_2.capacity, it.capacity)
            assertEquals(AUDITORIUM_2.city, it.city)
            assertNotNull(AUDITORIUM_1.createdAt)
            assertTrue(it.updatedAt.isAfter(it.createdAt))
        }

        assertEquals(1, auditoriumDefaultRepository.findAll().size)
        auditoriumDefaultRepository.findByAuditoriumId(auditoriumId).let {
            assertNotNull(it)
            assertAuditoriumDefaultsEquals(AUDITORIUM_DEFAULT(auditoriumId), it)
        }
    }

    private fun assertAuditoriumDefaultsEquals(expected: AuditoriumDefault, actual: AuditoriumDefault) {
        assertEquals(expected.saleTimeLimit, actual.saleTimeLimit)
        assertEquals(expected.surchargeVip, actual.surchargeVip)
        assertEquals(expected.surchargePremium, actual.surchargePremium)
        assertEquals(expected.surchargeImax, actual.surchargeImax)
        assertEquals(expected.surchargeUltraX, actual.surchargeUltraX)
        assertEquals(expected.surchargeDBox, actual.surchargeDBox)
        assertEquals(expected.serviceFeeVip, actual.serviceFeeVip)
        assertEquals(expected.serviceFeePremium, actual.serviceFeePremium)
        assertEquals(expected.serviceFeeImax, actual.serviceFeeImax)
        assertEquals(expected.serviceFeeUltraX, actual.serviceFeeUltraX)
        assertEquals(expected.proCommission, actual.proCommission)
        assertEquals(expected.filmFondCommission, actual.filmFondCommission)
        assertEquals(expected.distributorCommission, actual.distributorCommission)
        assertEquals(expected.publishOnline, actual.publishOnline)
    }
}

private fun assertAuditoriumEquals(expected: Auditorium, actual: Auditorium) {
    assertEquals(expected.originalId, actual.originalId)
    assertEquals(expected.originalCode, actual.originalCode)
    assertEquals(expected.code, actual.code)
    assertEquals(expected.title, actual.title)
    assertEquals(expected.capacity, actual.capacity)
    assertEquals(expected.city, actual.city)
    assertNotNull(actual.createdAt)
    assertNotNull(actual.updatedAt)
}

private val AUDITORIUM_1 = createAuditorium(
    originalId = 1,
    code = "A",
    title = "SÁLA A - CINEMAX BRATISLAVA",
    city = "Bratislava",
    capacity = 100
)
private val AUDITORIUM_2 = createAuditorium(
    originalId = 2,
    code = "B",
    title = "SÁLA B - CINEMAX BRATISLAVA",
    city = "Dunajská Streda",
    capacity = 200
)
private val AUDITORIUM_DEFAULT: (UUID) -> AuditoriumDefault = { auditoriumId: UUID ->
    AuditoriumDefault(
        originalId = null,
        auditoriumId = auditoriumId,
        saleTimeLimit = 10,
        surchargeVip = 1.1.toBigDecimal(),
        surchargePremium = 1.2.toBigDecimal(),
        surchargeImax = 1.3.toBigDecimal(),
        surchargeUltraX = 1.4.toBigDecimal(),
        surchargeDBox = 1.5.toBigDecimal(),
        serviceFeeVip = 1.6.toBigDecimal(),
        serviceFeePremium = 1.7.toBigDecimal(),
        serviceFeeImax = 1.8.toBigDecimal(),
        serviceFeeUltraX = 1.9.toBigDecimal(),
        proCommission = 5,
        filmFondCommission = 6,
        distributorCommission = 7,
        publishOnline = false
    )
}
