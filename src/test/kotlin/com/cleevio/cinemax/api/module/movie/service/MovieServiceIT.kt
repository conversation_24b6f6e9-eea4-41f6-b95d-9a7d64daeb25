package com.cleevio.cinemax.api.module.movie.service

import com.cleevio.cinemax.api.DatabaseCleanup
import com.cleevio.cinemax.api.IntegrationTest
import com.cleevio.cinemax.api.module.auditorium.service.AuditoriumService
import com.cleevio.cinemax.api.module.auditoriumlayout.service.AuditoriumLayoutService
import com.cleevio.cinemax.api.module.distributor.exception.DistributorNotFoundException
import com.cleevio.cinemax.api.module.distributor.service.DistributorService
import com.cleevio.cinemax.api.module.genre.entity.Genre
import com.cleevio.cinemax.api.module.genre.service.GenreJpaFinderService
import com.cleevio.cinemax.api.module.jso.entity.Jso
import com.cleevio.cinemax.api.module.jso.service.JsoJpaFinderService
import com.cleevio.cinemax.api.module.language.entity.Language
import com.cleevio.cinemax.api.module.language.service.LanguageJpaFinderService
import com.cleevio.cinemax.api.module.movie.constant.MovieRating
import com.cleevio.cinemax.api.module.movie.entity.Movie
import com.cleevio.cinemax.api.module.movie.event.AdminMovieCreatedOrUpdatedEvent
import com.cleevio.cinemax.api.module.movie.event.AdminMovieDeletedEvent
import com.cleevio.cinemax.api.module.movie.event.MovieCreatedOrUpdatedEvent
import com.cleevio.cinemax.api.module.movie.event.MovieWithJsosCreatedOrUpdatedEvent
import com.cleevio.cinemax.api.module.movie.exception.MovieCodeAlreadyExistsException
import com.cleevio.cinemax.api.module.movie.exception.MovieNotFoundException
import com.cleevio.cinemax.api.module.movie.exception.ScreeningForMovieExistsException
import com.cleevio.cinemax.api.module.movie.service.command.CreateOrUpdateMovieCommand
import com.cleevio.cinemax.api.module.movie.service.command.DeleteMovieCommand
import com.cleevio.cinemax.api.module.movie.service.command.MessagingCreateOrUpdateMovieCommand
import com.cleevio.cinemax.api.module.movie.service.command.MessagingDeleteMovieCommand
import com.cleevio.cinemax.api.module.movie.service.command.UpdateMovieLanguageAndTechnologyCommand
import com.cleevio.cinemax.api.module.movie.service.command.UpdateMovieOriginalIdCommand
import com.cleevio.cinemax.api.module.moviejso.service.MovieJsoJpaFinderService
import com.cleevio.cinemax.api.module.pricecategory.service.PriceCategoryService
import com.cleevio.cinemax.api.module.production.entity.Production
import com.cleevio.cinemax.api.module.production.service.ProductionJpaFinderService
import com.cleevio.cinemax.api.module.rating.entity.Rating
import com.cleevio.cinemax.api.module.rating.service.RatingJpaFinderService
import com.cleevio.cinemax.api.module.screening.constant.MovieFormat
import com.cleevio.cinemax.api.module.screening.constant.MovieLanguage
import com.cleevio.cinemax.api.module.screening.constant.MovieOrigin
import com.cleevio.cinemax.api.module.screening.constant.MovieTechnology
import com.cleevio.cinemax.api.module.screening.service.ScreeningJooqFinderService
import com.cleevio.cinemax.api.module.screening.service.ScreeningService
import com.cleevio.cinemax.api.module.screening.service.command.DeleteScreeningCommand
import com.cleevio.cinemax.api.module.technology.entity.Technology
import com.cleevio.cinemax.api.module.technology.service.TechnologyJpaFinderService
import com.cleevio.cinemax.api.module.tmslanguage.entity.TmsLanguage
import com.cleevio.cinemax.api.module.tmslanguage.service.TmsLanguageJpaFinderService
import com.cleevio.cinemax.api.util.assertCommandToMovieMapping
import com.cleevio.cinemax.api.util.createAuditorium
import com.cleevio.cinemax.api.util.createAuditoriumLayout
import com.cleevio.cinemax.api.util.createDistributor
import com.cleevio.cinemax.api.util.createMovie
import com.cleevio.cinemax.api.util.createPriceCategory
import com.cleevio.cinemax.api.util.createScreening
import com.cleevio.cinemax.api.util.mapToCreateOrUpdateAuditoriumCommand
import com.cleevio.cinemax.api.util.mapToCreateOrUpdateAuditoriumLayoutCommand
import com.cleevio.cinemax.api.util.mapToCreateOrUpdateDistributorCommand
import com.cleevio.cinemax.api.util.mapToCreateOrUpdateMovieCommand
import com.cleevio.cinemax.api.util.mapToCreateOrUpdateScreeningCommand
import com.cleevio.cinemax.api.util.mapToSyncCreateOrUpdatePriceCategoryCommand
import io.mockk.Runs
import io.mockk.every
import io.mockk.just
import io.mockk.verify
import io.mockk.verifySequence
import jakarta.validation.ConstraintViolationException
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.Arguments
import org.junit.jupiter.params.provider.MethodSource
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.test.context.jdbc.Sql
import org.springframework.test.context.jdbc.SqlGroup
import java.time.LocalDate
import java.util.Optional
import java.util.UUID
import java.util.stream.Stream
import kotlin.jvm.optionals.getOrNull
import kotlin.test.assertEquals
import kotlin.test.assertFalse
import kotlin.test.assertNotNull
import kotlin.test.assertNull
import kotlin.test.assertTrue

class MovieServiceIT @Autowired constructor(
    private val underTest: MovieService,
    private val movieRepository: MovieRepository,
    private val movieJpaFinderService: MovieJpaFinderService,
    private val distributorService: DistributorService,
    private val screeningService: ScreeningService,
    private val screeningFinderService: ScreeningJooqFinderService,
    private val auditoriumService: AuditoriumService,
    private val auditoriumLayoutService: AuditoriumLayoutService,
    private val priceCategoryService: PriceCategoryService,
    private val jsoJpaFinderService: JsoJpaFinderService,
    private val movieJsoJpaFinderService: MovieJsoJpaFinderService,
    private val productionJpaFinderService: ProductionJpaFinderService,
    private val genreJpaFinderService: GenreJpaFinderService,
    private val ratingJpaFinderService: RatingJpaFinderService,
    private val technologyJpaFinderService: TechnologyJpaFinderService,
    private val languageJpaFinderService: LanguageJpaFinderService,
    private val tmsLanguageJpaFinderService: TmsLanguageJpaFinderService,
) : IntegrationTest(DatabaseCleanup.AFTER_EACH) {

    @BeforeEach
    fun beforeEach() {
        every { applicationEventPublisherMock.publishEvent(any<MovieCreatedOrUpdatedEvent>()) } just Runs
        every { applicationEventPublisherMock.publishEvent(any<MovieWithJsosCreatedOrUpdatedEvent>()) } just Runs
        every { applicationEventPublisherMock.publishEvent(any<AdminMovieCreatedOrUpdatedEvent>()) } just Runs
        every { applicationEventPublisherMock.publishEvent(any<AdminMovieDeletedEvent>()) } just Runs
    }

    @Test
    fun `test syncCreateOrUpdateMovie - should create movie`() {
        distributorService.syncCreateOrUpdateDistributor(mapToCreateOrUpdateDistributorCommand(DISTRIBUTOR_1))
        val command = mapToCreateOrUpdateMovieCommand(MOVIE_1)
        underTest.syncCreateOrUpdateMovie(command)

        val createdMovie = movieJpaFinderService.findNonDeletedByOriginalId(MOVIE_1.originalId!!)
        assertNotNull(createdMovie)
        assertMovieEquals(MOVIE_1, createdMovie)
    }

    @Test
    fun `test syncCreateOrUpdateMovie - one movie exists - insert equal movie so it should update`() {
        distributorService.syncCreateOrUpdateDistributor(mapToCreateOrUpdateDistributorCommand(DISTRIBUTOR_1))
        val command = mapToCreateOrUpdateMovieCommand(MOVIE_2)
        underTest.syncCreateOrUpdateMovie(command)

        val updatedMovie = movieJpaFinderService.findNonDeletedByOriginalId(MOVIE_2.originalId!!)
        assertNotNull(updatedMovie)
        assertMovieEquals(MOVIE_2, updatedMovie)
        assertTrue { updatedMovie.updatedAt.isAfter(MOVIE_2.updatedAt) }
    }

    @Test
    fun `test syncCreateOrUpdateMovie - two movies - should create two movies`() {
        distributorService.syncCreateOrUpdateDistributor(mapToCreateOrUpdateDistributorCommand(DISTRIBUTOR_1))
        movieRepository.save(MOVIE_1)

        val command = mapToCreateOrUpdateMovieCommand(MOVIE_2)
        underTest.syncCreateOrUpdateMovie(command)

        val movies = movieJpaFinderService.findAll()
        assertEquals(movies.size, 2)
        assertMovieEquals(MOVIE_1, movies.first { it.originalId == MOVIE_1.originalId })
        assertMovieEquals(MOVIE_2, movies.first { it.originalId == MOVIE_2.originalId })
    }

    @Test
    fun `test syncCreateOrUpdateMovie - command with empty optional or null attributes - entity attrs are null`() {
        distributorService.syncCreateOrUpdateDistributor(mapToCreateOrUpdateDistributorCommand(DISTRIBUTOR_1))
        movieRepository.save(MOVIE_2)
        val createdMovie = movieJpaFinderService.findNonDeletedByOriginalId(MOVIE_2.originalId!!)
        assertNotNull(createdMovie)
        assertMovieEquals(MOVIE_2, createdMovie)

        val command = mapToCreateOrUpdateMovieCommand(MOVIE_2)
        val commandWithNullAttrs = command.copy(
            premiereDate = Optional.empty(),
            duration = null,
            parsedTechnology = null
        )
        underTest.syncCreateOrUpdateMovie(commandWithNullAttrs)

        val updatedMovie = movieJpaFinderService.findNonDeletedByOriginalId(MOVIE_2.originalId!!)
        assertNotNull(updatedMovie)
        assertEquals(MOVIE_2.originalId, updatedMovie.originalId)
        assertEquals(MOVIE_2.title, updatedMovie.title)
        assertNull(updatedMovie.premiereDate)
        assertNull(updatedMovie.releaseYear)
        assertNull(updatedMovie.duration)
        assertNull(updatedMovie.parsedTechnology)
        assertNotNull(updatedMovie.createdAt)
        assertTrue { updatedMovie.updatedAt.isAfter(MOVIE_2.updatedAt) }
    }

    @Test
    fun `test syncCreateOrUpdateMovie - update movie disfilmCode - should update properly`() {
        distributorService.syncCreateOrUpdateDistributor(mapToCreateOrUpdateDistributorCommand(DISTRIBUTOR_1))
        movieRepository.save(MOVIE_2)
        val createdMovie = movieJpaFinderService.findNonDeletedByOriginalId(MOVIE_2.originalId!!)
        assertNotNull(createdMovie)
        assertMovieEquals(MOVIE_2, createdMovie)

        val command = mapToCreateOrUpdateMovieCommand(MOVIE_2)
        val updateCommand = command.copy(
            id = null,
            disfilmCode = "AABBCCDD"
        )
        underTest.syncCreateOrUpdateMovie(updateCommand)

        val updatedMovie = movieJpaFinderService.findNonDeletedByOriginalId(MOVIE_2.originalId!!)
        assertNotNull(updatedMovie)
        assertEquals(MOVIE_2.originalId, updatedMovie.originalId)
        assertEquals(MOVIE_2.title, updatedMovie.title)
        assertEquals("AABBCCDD", updatedMovie.disfilmCode)
        assertTrue(updatedMovie.updatedAt.isAfter(MOVIE_2.updatedAt))

        // sync movie from MSSQL once again with null disfilmCode - should not nullify not-null disfilmCode in a record
        underTest.syncCreateOrUpdateMovie(command)

        val updatedUpdatedMovie = movieJpaFinderService.findNonDeletedByOriginalId(MOVIE_2.originalId!!)
        assertNotNull(updatedUpdatedMovie)
        assertEquals(MOVIE_2.originalId, updatedUpdatedMovie.originalId)
        assertEquals(MOVIE_2.title, updatedUpdatedMovie.title)
        assertEquals("AABBCCDD", updatedUpdatedMovie.disfilmCode)
    }

    @Test
    @SqlGroup(
        Sql(
            scripts = [
                "/db/migration/V106__init_rating_table_with_data.sql",
                "/db/migration/V107__init_technology_table_with_data.sql",
                "/db/migration/V108__init_language_table_with_data.sql",
                "/db/migration/V109__init_tms_language_table_with_data.sql"
            ]
        )
    )
    fun `test adminCreateOrUpdateMovie - create, command with null parsedRating and filled ratingId - should parse rating correctly`() {
        distributorService.syncCreateOrUpdateDistributor(mapToCreateOrUpdateDistributorCommand(DISTRIBUTOR_1))
        val rating1 = ratingJpaFinderService.findByCode(RATING_1.code)!!
        val technology1 = technologyJpaFinderService.findByCode(TECHNOLOGY_1.code)!!
        val language1 = languageJpaFinderService.findByCode(LANGUAGE_1.code)!!
        val tmsLanguage1 = tmsLanguageJpaFinderService.findByCode(TMS_LANGUAGE_1.code)!!

        val command = mapToCreateOrUpdateMovieCommand(MOVIE_2)
        val commandWithNullParsedRating = command.copy(
            id = null,
            duration = 100,
            parsedRating = null,
            ratingId = Optional.of(rating1.id),
            technologyId = technology1.id,
            languageId = language1.id,
            tmsLanguageId = tmsLanguage1.id
        )
        underTest.adminCreateOrUpdateMovie(commandWithNullParsedRating)

        val createdMovie = movieJpaFinderService.findNonDeletedByOriginalId(MOVIE_2.originalId!!)
        assertNotNull(createdMovie)
        assertEquals(MOVIE_2.originalId, createdMovie.originalId)
        assertEquals(MOVIE_2.title, createdMovie.title)
        assertEquals(MovieRating.PLUS_12, createdMovie.parsedRating)
        assertEquals(rating1.id, createdMovie.ratingId)
    }

    @Test
    @SqlGroup(
        Sql(
            scripts = [
                "/db/migration/V106__init_rating_table_with_data.sql",
                "/db/migration/V107__init_technology_table_with_data.sql",
                "/db/migration/V108__init_language_table_with_data.sql",
                "/db/migration/V109__init_tms_language_table_with_data.sql"
            ]
        )
    )
    fun `test adminCreateOrUpdateMovie - update and remove ratingId - should update parsedRating and ratingId correctly`() {
        distributorService.syncCreateOrUpdateDistributor(mapToCreateOrUpdateDistributorCommand(DISTRIBUTOR_1))
        val rating1 = ratingJpaFinderService.findByCode(RATING_1.code)!!
        val rating2 = ratingJpaFinderService.findByCode(RATING_2.code)!!
        val technology1 = technologyJpaFinderService.findByCode(TECHNOLOGY_1.code)!!
        val language1 = languageJpaFinderService.findByCode(LANGUAGE_1.code)!!
        val tmsLanguage1 = tmsLanguageJpaFinderService.findByCode(TMS_LANGUAGE_1.code)!!

        val command = mapToCreateOrUpdateMovieCommand(MOVIE_2)
        val commandWithNullParsedRating = command.copy(
            id = null,
            parsedRating = null,
            ratingId = Optional.of(rating1.id),
            technologyId = technology1.id,
            languageId = language1.id,
            tmsLanguageId = tmsLanguage1.id
        )
        underTest.adminCreateOrUpdateMovie(commandWithNullParsedRating)

        val createdMovie = movieJpaFinderService.findNonDeletedByOriginalId(MOVIE_2.originalId!!)
        assertNotNull(createdMovie)
        assertEquals(MovieRating.PLUS_12, createdMovie.parsedRating)
        assertEquals(rating1.id, createdMovie.ratingId)

        // update rating
        val commandWithNewRating = commandWithNullParsedRating.copy(
            id = createdMovie.id,
            parsedRating = null,
            ratingId = Optional.of(rating2.id)
        )
        underTest.adminCreateOrUpdateMovie(commandWithNewRating)

        val updatedMovie = movieJpaFinderService.findNonDeletedByOriginalId(MOVIE_2.originalId!!)
        assertNotNull(updatedMovie)
        assertEquals(MovieRating.PLUS_15, updatedMovie.parsedRating)
        assertEquals(rating2.id, updatedMovie.ratingId)

        // remove rating
        val commandWithEmptyRating = commandWithNullParsedRating.copy(
            id = createdMovie.id,
            parsedRating = null,
            ratingId = Optional.empty()
        )
        underTest.adminCreateOrUpdateMovie(commandWithEmptyRating)

        val twiceUpdatedMovie = movieJpaFinderService.findNonDeletedByOriginalId(MOVIE_2.originalId!!)
        assertNotNull(twiceUpdatedMovie)
        assertNull(twiceUpdatedMovie.parsedRating)
        assertNull(twiceUpdatedMovie.ratingId)
    }

    @Test
    @SqlGroup(
        Sql(
            scripts = [
                "/db/migration/V106__init_rating_table_with_data.sql",
                "/db/migration/V107__init_technology_table_with_data.sql",
                "/db/migration/V108__init_language_table_with_data.sql",
                "/db/migration/V109__init_tms_language_table_with_data.sql"
            ]
        )
    )
    fun `test adminCreateOrUpdateMovie - update but do not modify ratingId - should keep parsedRating and ratingId intact`() {
        distributorService.syncCreateOrUpdateDistributor(mapToCreateOrUpdateDistributorCommand(DISTRIBUTOR_1))
        val rating1 = ratingJpaFinderService.findByCode(RATING_1.code)!!
        val technology1 = technologyJpaFinderService.findByCode(TECHNOLOGY_1.code)!!
        val language1 = languageJpaFinderService.findByCode(LANGUAGE_1.code)!!
        val tmsLanguage1 = tmsLanguageJpaFinderService.findByCode(TMS_LANGUAGE_1.code)!!

        val command = mapToCreateOrUpdateMovieCommand(MOVIE_2)
        val commandWithNullParsedRating = command.copy(
            id = null,
            parsedRating = null,
            ratingId = Optional.of(rating1.id),
            technologyId = technology1.id,
            languageId = language1.id,
            tmsLanguageId = tmsLanguage1.id
        )
        underTest.adminCreateOrUpdateMovie(commandWithNullParsedRating)

        val createdMovie = movieJpaFinderService.findNonDeletedByOriginalId(MOVIE_2.originalId!!)
        assertNotNull(createdMovie)
        assertEquals(MovieRating.PLUS_12, createdMovie.parsedRating)
        assertEquals(rating1.id, createdMovie.ratingId)

        val commandWithNoRatingChanged = commandWithNullParsedRating.copy(
            id = createdMovie.id,
            ratingId = null,
            parsedRating = null,
            title = "Updated title"
        )
        underTest.adminCreateOrUpdateMovie(commandWithNoRatingChanged)

        val updatedMovie = movieJpaFinderService.findNonDeletedByOriginalId(MOVIE_2.originalId!!)
        assertNotNull(updatedMovie)
        assertEquals(MovieRating.PLUS_12, updatedMovie.parsedRating)
        assertEquals(rating1.id, updatedMovie.ratingId)
        assertEquals("Updated title", updatedMovie.title)
    }

    @Test
    fun `test syncCreateOrUpdateMovie - command with blank string - should throw exception`() {
        val command = mapToCreateOrUpdateMovieCommand(MOVIE_2)
        val commandWithBlankString = command.copy(
            title = ""
        )
        assertThrows<ConstraintViolationException> {
            underTest.syncCreateOrUpdateMovie(commandWithBlankString)
        }
    }

    @Test
    fun `test syncCreateOrUpdateMovie - command with non existing distributor - should throw exception`() {
        val command = mapToCreateOrUpdateMovieCommand(MOVIE_2)
        val commandWithNonExistingDistributor = command.copy(
            distributorId = UUID.randomUUID()
        )
        assertThrows<DistributorNotFoundException> {
            underTest.syncCreateOrUpdateMovie(commandWithNonExistingDistributor)
        }
    }

    @Test
    fun `test adminCreateOrUpdateMovie - movie with given id does not exist - should throw exception`() {
        assertThrows<MovieNotFoundException> {
            underTest.adminCreateOrUpdateMovie(
                CreateOrUpdateMovieCommand(
                    id = UUID.fromString("dcd84a85-15fc-407f-b7ed-150913db1f0c"),
                    code = MOVIE_1.code,
                    rawTitle = MOVIE_1.rawTitle,
                    duration = MOVIE_1.duration,
                    distributorId = DISTRIBUTOR_1.id,
                    technologyId = TECHNOLOGY_1.id,
                    languageId = LANGUAGE_1.id,
                    tmsLanguageId = TMS_LANGUAGE_1.id
                )
            )
        }
    }

    @Test
    fun `test adminCreateOrUpdateMovie - duration is less than 1 - should throw exception`() {
        assertThrows<IllegalArgumentException> {
            underTest.adminCreateOrUpdateMovie(
                CreateOrUpdateMovieCommand(
                    id = null,
                    code = MOVIE_1.code,
                    rawTitle = MOVIE_1.rawTitle,
                    duration = 0,
                    distributorId = DISTRIBUTOR_1.id,
                    technologyId = TECHNOLOGY_1.id,
                    languageId = LANGUAGE_1.id,
                    tmsLanguageId = TMS_LANGUAGE_1.id
                )
            )
        }

        verify(exactly = 0) { applicationEventPublisherMock.publishEvent(any()) }
    }

    @ParameterizedTest
    @MethodSource("syncCreateOrUpdateMovieCommandProvider")
    fun `test syncCreateOrUpdateMovie - command with invalid attribute - should throw exception`(
        command: CreateOrUpdateMovieCommand,
    ) {
        assertThrows<IllegalArgumentException> {
            underTest.syncCreateOrUpdateMovie(command)
        }
    }

    @ParameterizedTest
    @MethodSource("adminCreateOrUpdateMovieCommandProvider")
    fun `test adminCreateOrUpdateMovie - command with invalid attribute - should throw exception`(
        command: CreateOrUpdateMovieCommand,
    ) {
        assertThrows<IllegalArgumentException> {
            underTest.adminCreateOrUpdateMovie(command)
        }
    }

    @Test
    @SqlGroup(
        Sql(
            scripts = [
                "/db/migration/V107__init_technology_table_with_data.sql",
                "/db/migration/V108__init_language_table_with_data.sql",
                "/db/migration/V109__init_tms_language_table_with_data.sql"
            ]
        )
    )
    fun `test syncCreateOrUpdateMovie - movie with code is already created in manager app - should throw exception`() {
        distributorService.syncCreateOrUpdateDistributor(mapToCreateOrUpdateDistributorCommand(DISTRIBUTOR_1))
        val technology1 = technologyJpaFinderService.findByCode(TECHNOLOGY_1.code)!!
        val language1 = languageJpaFinderService.findByCode(LANGUAGE_1.code)!!
        val tmsLanguage1 = tmsLanguageJpaFinderService.findByCode(TMS_LANGUAGE_1.code)!!

        val adminCommand = mapToCreateOrUpdateMovieCommand(MOVIE_1).copy(
            id = null,
            technologyId = technology1.id,
            languageId = language1.id,
            tmsLanguageId = tmsLanguage1.id
        )
        underTest.adminCreateOrUpdateMovie(adminCommand)

        val createdMovie = movieRepository.findByCodeAndDeletedAtIsNull(MOVIE_1.code)
        assertNotNull(createdMovie)
        assertMovieEquals(MOVIE_1, createdMovie)

        val syncCommand = CreateOrUpdateMovieCommand(
            originalId = 45,
            code = MOVIE_1.code,
            title = MOVIE_1.title,
            rawTitle = MOVIE_1.title,
            parsedFormat = MOVIE_1.parsedFormat,
            duration = MOVIE_1.duration,
            distributorId = DISTRIBUTOR_1.id
        )

        assertThrows<MovieCodeAlreadyExistsException> {
            underTest.syncCreateOrUpdateMovie(syncCommand)
        }
        verify(exactly = 0) { applicationEventPublisherMock.publishEvent(any()) }
    }

    @Test
    @SqlGroup(
        Sql(
            scripts = [
                "/db/migration/V107__init_technology_table_with_data.sql",
                "/db/migration/V108__init_language_table_with_data.sql",
                "/db/migration/V109__init_tms_language_table_with_data.sql"
            ]
        )
    )
    fun `test syncCreateOrUpdateMovie - synced from MSSQL, create another in manager app, update synced from MSSQL with existing code - should not update`() {
        distributorService.syncCreateOrUpdateDistributor(mapToCreateOrUpdateDistributorCommand(DISTRIBUTOR_1))
        val technology1 = technologyJpaFinderService.findByCode(TECHNOLOGY_1.code)!!
        val language1 = languageJpaFinderService.findByCode(LANGUAGE_1.code)!!
        val tmsLanguage1 = tmsLanguageJpaFinderService.findByCode(TMS_LANGUAGE_1.code)!!

        val command1 = mapToCreateOrUpdateMovieCommand(MOVIE_1).copy(id = null)
        underTest.syncCreateOrUpdateMovie(command1)

        val createdMovie1 = movieRepository.findByCodeAndDeletedAtIsNull(MOVIE_1.code)
        assertNotNull(createdMovie1)
        assertMovieEquals(MOVIE_1, createdMovie1)

        val command2 = mapToCreateOrUpdateMovieCommand(MOVIE_2).copy(
            id = null,
            technologyId = technology1.id,
            languageId = language1.id,
            tmsLanguageId = tmsLanguage1.id
        )
        underTest.adminCreateOrUpdateMovie(command2)

        val createdMovie2 = movieRepository.findByCodeAndDeletedAtIsNull(MOVIE_2.code)
        assertNotNull(createdMovie2)
        assertMovieEquals(MOVIE_2, createdMovie2)

        val command3 = mapToCreateOrUpdateMovieCommand(MOVIE_1).copy(
            id = null,
            code = MOVIE_2.code
        )

        underTest.syncCreateOrUpdateMovie(command3)

        val notUpdatedMovie2 = movieRepository.findByCodeAndDeletedAtIsNull(MOVIE_2.code)!!
        assertMovieEquals(MOVIE_2, notUpdatedMovie2)

        verify(exactly = 0) { applicationEventPublisherMock.publishEvent(any()) }
    }

    @Test
    fun `test syncCreateOrUpdate - exists deleted by originalId - should not update movie`() {
        distributorService.syncCreateOrUpdateDistributor(mapToCreateOrUpdateDistributorCommand(DISTRIBUTOR_1))
        val command = mapToCreateOrUpdateMovieCommand(MOVIE_1)
        underTest.syncCreateOrUpdateMovie(command)

        val createdMovie = movieJpaFinderService.findNonDeletedByOriginalId(MOVIE_1.originalId!!)
        assertNotNull(createdMovie)
        assertMovieEquals(MOVIE_1, createdMovie)

        underTest.deleteMovie(DeleteMovieCommand(createdMovie.id))

        assertNull(movieRepository.findByOriginalIdAndDeletedAtIsNull(MOVIE_1.originalId!!))
        assertTrue(movieRepository.existsByOriginalIdAndDeletedAtIsNotNull(MOVIE_1.originalId!!))

        val updateCommand = mapToCreateOrUpdateMovieCommand(MOVIE_2).copy(originalId = createdMovie.originalId)
        underTest.syncCreateOrUpdateMovie(updateCommand)

        val notUpdatedMovie = movieRepository.findByOriginalIdAndDeletedAtIsNotNull(MOVIE_1.originalId!!)
        assertNotNull(notUpdatedMovie)
        assertMovieEquals(MOVIE_1, notUpdatedMovie)
    }

    @Test
    fun `test syncCreateOrUpdate - exists deleted and non-deleted by originalId - should update movie`() {
        distributorService.syncCreateOrUpdateDistributor(mapToCreateOrUpdateDistributorCommand(DISTRIBUTOR_1))
        val command = mapToCreateOrUpdateMovieCommand(MOVIE_1)
        underTest.syncCreateOrUpdateMovie(command)

        val createdMovie = movieJpaFinderService.findNonDeletedByOriginalId(MOVIE_1.originalId!!)
        assertNotNull(createdMovie)
        assertMovieEquals(MOVIE_1, createdMovie)

        underTest.deleteMovie(DeleteMovieCommand(createdMovie.id))

        assertNull(movieRepository.findByOriginalIdAndDeletedAtIsNull(MOVIE_1.originalId!!))
        assertTrue(movieRepository.existsByOriginalIdAndDeletedAtIsNotNull(MOVIE_1.originalId!!))

        // simulate sync of MOVIE_1 from DISFilm
        movieRepository.save(MOVIE_4)

        val syncedMovie = movieRepository.findByOriginalIdAndDeletedAtIsNull(MOVIE_1.originalId!!)
        assertNotNull(syncedMovie)
        assertEquals(MOVIE_1.originalId, syncedMovie.originalId)
        assertMovieEquals(MOVIE_4, syncedMovie)

        val updateSyncedMovieCommand = mapToCreateOrUpdateMovieCommand(MOVIE_4).copy(duration = 200)
        underTest.syncCreateOrUpdateMovie(updateSyncedMovieCommand)

        val updatedMovie = movieRepository.findByOriginalIdAndDeletedAtIsNull(MOVIE_4.originalId!!)
        assertNotNull(updatedMovie)
        assertEquals(200, updatedMovie.duration)

        assertEquals(MOVIE_4.originalId, updatedMovie.originalId)
        assertEquals(MOVIE_4.rawTitle, updatedMovie.rawTitle)
        assertEquals(MOVIE_4.originalTitle, updatedMovie.originalTitle)
        assertEquals(MOVIE_4.title, updatedMovie.title)
        assertEquals(MOVIE_4.code, updatedMovie.code)
        assertEquals(MOVIE_4.disfilmCode, updatedMovie.disfilmCode)
        assertEquals(MOVIE_4.releaseYear, updatedMovie.releaseYear)
        assertEquals(MOVIE_4.premiereDate, updatedMovie.premiereDate)
        assertEquals(200, updatedMovie.duration)
        assertEquals(MOVIE_4.parsedRating, updatedMovie.parsedRating)
        assertEquals(MOVIE_4.parsedFormat, updatedMovie.parsedFormat)
        assertEquals(MOVIE_4.parsedTechnology, updatedMovie.parsedTechnology)
        assertEquals(MOVIE_4.parsedLanguage, updatedMovie.parsedLanguage)
        assertEquals(MOVIE_4.distributorId, updatedMovie.distributorId)
    }

    @Test
    @SqlGroup(
        Sql(
            scripts = [
                "/db/migration/V107__init_technology_table_with_data.sql",
                "/db/migration/V108__init_language_table_with_data.sql",
                "/db/migration/V109__init_tms_language_table_with_data.sql"
            ]
        )
    )
    fun `test syncCreateOrUpdate - movie with technologyId and languageId synced from MSSQL - should not nullify technologyId and languageId`() {
        distributorService.syncCreateOrUpdateDistributor(mapToCreateOrUpdateDistributorCommand(DISTRIBUTOR_1))
        val technology1 = technologyJpaFinderService.findByCode(TECHNOLOGY_1.code)!!
        val language1 = languageJpaFinderService.findByCode(LANGUAGE_1.code)!!
        val tmsLanguage1 = tmsLanguageJpaFinderService.findByCode(TMS_LANGUAGE_1.code)!!

        val adminCommand = mapToCreateOrUpdateMovieCommand(MOVIE_1).copy(
            id = null,
            originalId = 45,
            technologyId = technology1.id,
            languageId = language1.id,
            tmsLanguageId = tmsLanguage1.id
        )
        underTest.adminCreateOrUpdateMovie(adminCommand)

        val createdMovie = movieRepository.findByCodeAndDeletedAtIsNull(MOVIE_1.code)
        assertNotNull(createdMovie)
        assertEquals(createdMovie.technologyId, technology1.id)
        assertEquals(createdMovie.languageId, language1.id)

        val syncCommand = CreateOrUpdateMovieCommand(
            originalId = 45,
            code = MOVIE_1.code,
            title = MOVIE_1.title,
            rawTitle = MOVIE_1.title,
            distributorId = DISTRIBUTOR_1.id,
            parsedFormat = MOVIE_1.parsedFormat,
            technologyId = null,
            languageId = null
        )
        underTest.syncCreateOrUpdateMovie(syncCommand)

        val updatedMovie = movieRepository.findByCodeAndDeletedAtIsNull(MOVIE_1.code)
        assertNotNull(updatedMovie)
        assertEquals(updatedMovie.technologyId, technology1.id)
        assertEquals(updatedMovie.languageId, language1.id)
    }

    @Test
    @SqlGroup(
        Sql(
            scripts = [
                "/db/migration/V107__init_technology_table_with_data.sql",
                "/db/migration/V108__init_language_table_with_data.sql",
                "/db/migration/V109__init_tms_language_table_with_data.sql",
                "/db/migration/V110__init_jso_table_with_data.sql"
            ]
        )
    )
    fun `test adminCreateOrUpdateMovie - should create movie and related movie JSOs`() {
        distributorService.syncCreateOrUpdateDistributor(mapToCreateOrUpdateDistributorCommand(DISTRIBUTOR_1))
        val technology1 = technologyJpaFinderService.findByCode(TECHNOLOGY_1.code)!!
        val language1 = languageJpaFinderService.findByCode(LANGUAGE_1.code)!!
        val tmsLanguage1 = tmsLanguageJpaFinderService.findByCode(TMS_LANGUAGE_1.code)!!
        val jso1 = jsoJpaFinderService.findByCode(JSO_1.code)!!
        val jso2 = jsoJpaFinderService.findByCode(JSO_2.code)!!

        assertTrue(movieJsoJpaFinderService.findAll().isEmpty())

        val commandMovie1 = mapToCreateOrUpdateMovieCommand(MOVIE_1).copy(
            id = null,
            code = null,
            technologyId = technology1.id,
            languageId = language1.id,
            tmsLanguageId = tmsLanguage1.id,
            jsoIds = setOf(jso1.id, jso2.id)
        )
        val commandMovie2 = mapToCreateOrUpdateMovieCommand(MOVIE_2).copy(
            id = null,
            code = null,
            technologyId = technology1.id,
            languageId = language1.id,
            tmsLanguageId = tmsLanguage1.id,
            jsoIds = setOf(jso1.id, jso2.id)
        )

        val movie1Id = underTest.adminCreateOrUpdateMovie(commandMovie1)
        val movie2Id = underTest.adminCreateOrUpdateMovie(commandMovie2)

        val createdMovie1 = movieJpaFinderService.getNonDeletedById(movie1Id)
        assertNotNull(createdMovie1)
        assertCommandToMovieMapping(expected = commandMovie1, actual = createdMovie1, expectedCode = null)
        assertNull(createdMovie1.productionId)
        assertNull(createdMovie1.primaryGenreId)
        assertNull(createdMovie1.secondaryGenreId)
        assertNull(createdMovie1.ratingId)
        assertEquals(technology1.id, createdMovie1.technologyId)
        assertEquals(language1.id, createdMovie1.languageId)
        assertEquals(tmsLanguage1.id, createdMovie1.tmsLanguageId)

        verifySequence {
            applicationEventPublisherMock.publishEvent(
                MovieWithJsosCreatedOrUpdatedEvent(
                    movieId = movie1Id,
                    jsoIds = setOf(jso1.id, jso2.id)
                )
            )
            applicationEventPublisherMock.publishEvent(MovieCreatedOrUpdatedEvent(movieId = movie1Id))
            applicationEventPublisherMock.publishEvent(any<AdminMovieCreatedOrUpdatedEvent>())
            applicationEventPublisherMock.publishEvent(
                MovieWithJsosCreatedOrUpdatedEvent(
                    movieId = movie2Id,
                    jsoIds = setOf(jso1.id, jso2.id)
                )
            )
            applicationEventPublisherMock.publishEvent(MovieCreatedOrUpdatedEvent(movieId = movie2Id))
            applicationEventPublisherMock.publishEvent(any<AdminMovieCreatedOrUpdatedEvent>())
        }
    }

    @Test
    @SqlGroup(
        Sql(
            scripts = [
                "/db/migration/V107__init_technology_table_with_data.sql",
                "/db/migration/V108__init_language_table_with_data.sql",
                "/db/migration/V109__init_tms_language_table_with_data.sql"
            ]
        )
    )
    fun `test adminCreateOrUpdateMovie - command without title - should create movie and parse title, format, technology and language from rawTitle`() {
        distributorService.syncCreateOrUpdateDistributor(mapToCreateOrUpdateDistributorCommand(DISTRIBUTOR_1))
        val technology1 = technologyJpaFinderService.findByCode(TECHNOLOGY_1.code)!!
        val language1 = languageJpaFinderService.findByCode(LANGUAGE_1.code)!!
        val tmsLanguage1 = tmsLanguageJpaFinderService.findByCode(TMS_LANGUAGE_1.code)!!

        val commandMovie1 = mapToCreateOrUpdateMovieCommand(MOVIE_3).copy(
            id = null,
            title = null,
            technologyId = technology1.id,
            languageId = language1.id,
            tmsLanguageId = tmsLanguage1.id,
            parsedFormat = null,
            parsedLanguage = null,
            parsedTechnology = null
        )
        val commandMovie2 = mapToCreateOrUpdateMovieCommand(MOVIE_2).copy(
            id = null,
            title = null,
            technologyId = technology1.id,
            languageId = language1.id,
            tmsLanguageId = tmsLanguage1.id,
            parsedFormat = null,
            parsedLanguage = null,
            parsedTechnology = null
        )

        val movie1Id = underTest.adminCreateOrUpdateMovie(commandMovie1)
        val movie2Id = underTest.adminCreateOrUpdateMovie(commandMovie2)

        val createdMovie1 = movieJpaFinderService.getNonDeletedById(movie1Id)
        assertNotNull(createdMovie1)
        assertMovieEquals(MOVIE_3, createdMovie1)
        val createdMovie2 = movieJpaFinderService.getNonDeletedById(movie2Id)
        assertNotNull(createdMovie2)
        assertMovieEquals(MOVIE_2, createdMovie2)

        verifySequence {
            applicationEventPublisherMock.publishEvent(MovieCreatedOrUpdatedEvent(movieId = movie1Id))
            applicationEventPublisherMock.publishEvent(any<AdminMovieCreatedOrUpdatedEvent>())
            applicationEventPublisherMock.publishEvent(MovieCreatedOrUpdatedEvent(movieId = movie2Id))
            applicationEventPublisherMock.publishEvent(any<AdminMovieCreatedOrUpdatedEvent>())
        }
    }

    @Test
    @SqlGroup(
        Sql(
            scripts = [
                "/db/migration/V104__init_production_table_with_data.sql",
                "/db/migration/V105__init_genre_table_with_data.sql",
                "/db/migration/V106__init_rating_table_with_data.sql",
                "/db/migration/V107__init_technology_table_with_data.sql",
                "/db/migration/V108__init_language_table_with_data.sql",
                "/db/migration/V109__init_tms_language_table_with_data.sql",
                "/db/migration/V110__init_jso_table_with_data.sql"
            ]
        )
    )
    fun `test adminCreateOrUpdateMovie - should update movie attributes`() {
        distributorService.syncCreateOrUpdateDistributor(mapToCreateOrUpdateDistributorCommand(DISTRIBUTOR_1))
        val genre1 = genreJpaFinderService.findByCode(GENRE_1.code)!!
        val genre2 = genreJpaFinderService.findByCode(GENRE_2.code)!!
        val technology1 = technologyJpaFinderService.findByCode(TECHNOLOGY_1.code)!!
        val language1 = languageJpaFinderService.findByCode(LANGUAGE_1.code)!!
        val tmsLanguage1 = tmsLanguageJpaFinderService.findByCode(TMS_LANGUAGE_1.code)!!
        val jso1 = jsoJpaFinderService.findByCode(JSO_1.code)!!
        val jso2 = jsoJpaFinderService.findByCode(JSO_2.code)!!

        assertTrue(movieJsoJpaFinderService.findAll().isEmpty())

        val createCommandMovie1 = mapToCreateOrUpdateMovieCommand(MOVIE_1).copy(
            id = null,
            code = null,
            primaryGenreId = Optional.of(genre1.id),
            secondaryGenreId = Optional.of(genre2.id),
            technologyId = technology1.id,
            languageId = language1.id,
            tmsLanguageId = tmsLanguage1.id,
            jsoIds = setOf(jso1.id, jso2.id)
        )
        val movie1Id = underTest.adminCreateOrUpdateMovie(createCommandMovie1)

        val createdMovie1 = movieJpaFinderService.getNonDeletedById(movie1Id)
        assertNotNull(createdMovie1)
        assertCommandToMovieMapping(expected = createCommandMovie1, actual = createdMovie1, expectedCode = null)
        assertNull(createdMovie1.productionId)
        assertEquals(genre1.id, createdMovie1.primaryGenreId)
        assertEquals(genre2.id, createdMovie1.secondaryGenreId)
        assertNull(createdMovie1.ratingId)
        assertEquals(technology1.id, createdMovie1.technologyId)
        assertEquals(language1.id, createdMovie1.languageId)
        assertEquals(tmsLanguage1.id, createdMovie1.tmsLanguageId)

        val production1 = productionJpaFinderService.findByCode(PRODUCTION_1.code)!!
        val rating1 = ratingJpaFinderService.findByCode(RATING_1.code)!!

        val updateCommandMovie1 = mapToCreateOrUpdateMovieCommand(MOVIE_1).copy(
            id = movie1Id,
            code = null,
            productionId = Optional.of(production1.id),
            secondaryGenreId = Optional.empty(),
            ratingId = Optional.of(rating1.id),
            technologyId = technology1.id,
            languageId = language1.id,
            tmsLanguageId = tmsLanguage1.id
        )
        underTest.adminCreateOrUpdateMovie(updateCommandMovie1)

        val updatedMovie1 = movieJpaFinderService.getNonDeletedById(movie1Id)

        assertCommandToMovieMapping(expected = updateCommandMovie1, actual = updatedMovie1, expectedCode = createdMovie1.code)
        assertEquals(production1.id, updatedMovie1.productionId)
        assertEquals(genre1.id, updatedMovie1.primaryGenreId)
        assertNull(updatedMovie1.secondaryGenreId)
        assertEquals(rating1.id, updatedMovie1.ratingId)
        assertEquals(technology1.id, updatedMovie1.technologyId)
        assertEquals(language1.id, updatedMovie1.languageId)
        assertEquals(tmsLanguage1.id, updatedMovie1.tmsLanguageId)

        verifySequence {
            applicationEventPublisherMock.publishEvent(
                MovieWithJsosCreatedOrUpdatedEvent(
                    movieId = movie1Id,
                    jsoIds = setOf(jso1.id, jso2.id)
                )
            )
            applicationEventPublisherMock.publishEvent(MovieCreatedOrUpdatedEvent(movieId = movie1Id))
            applicationEventPublisherMock.publishEvent(any<AdminMovieCreatedOrUpdatedEvent>())
            applicationEventPublisherMock.publishEvent(MovieCreatedOrUpdatedEvent(movieId = movie1Id))
            applicationEventPublisherMock.publishEvent(any<AdminMovieCreatedOrUpdatedEvent>())
        }
    }

    @Test
    @SqlGroup(
        Sql(
            scripts = [
                "/db/migration/V107__init_technology_table_with_data.sql",
                "/db/migration/V108__init_language_table_with_data.sql",
                "/db/migration/V109__init_tms_language_table_with_data.sql"
            ]
        )
    )
    fun `test adminCreateOrUpdateMovie - should update movie and parse title, format, technology and language from rawTitle`() {
        distributorService.syncCreateOrUpdateDistributor(mapToCreateOrUpdateDistributorCommand(DISTRIBUTOR_1))
        val createCommand = mapToCreateOrUpdateMovieCommand(MOVIE_1)
        underTest.syncCreateOrUpdateMovie(createCommand)

        val createdMovie = movieJpaFinderService.findNonDeletedByOriginalId(MOVIE_1.originalId!!)
        assertNotNull(createdMovie)
        assertMovieEquals(MOVIE_1, createdMovie)

        val technology1 = technologyJpaFinderService.findByCode(TECHNOLOGY_1.code)!!
        val language1 = languageJpaFinderService.findByCode(LANGUAGE_1.code)!!
        val tmsLanguage1 = tmsLanguageJpaFinderService.findByCode(TMS_LANGUAGE_1.code)!!

        val updateCommand = mapToCreateOrUpdateMovieCommand(MOVIE_1).copy(
            id = createdMovie.id,
            title = null,
            rawTitle = "Star Wars: Episode I 3D ATMOS (CT)",
            technologyId = technology1.id,
            languageId = language1.id,
            tmsLanguageId = tmsLanguage1.id,
            parsedFormat = null,
            parsedLanguage = null,
            parsedTechnology = null
        )

        val movie1Id = underTest.adminCreateOrUpdateMovie(updateCommand)

        val updatedMovie1 = movieJpaFinderService.getNonDeletedById(movie1Id)
        assertNotNull(updatedMovie1)

        assertEquals(MOVIE_1.originalId, updatedMovie1.originalId)
        assertEquals("Star Wars: Episode I 3D ATMOS (CT)", updatedMovie1.rawTitle)
        assertEquals(MOVIE_1.originalTitle, updatedMovie1.originalTitle)
        assertEquals("Star Wars: Episode I", updatedMovie1.title)
        assertEquals(MOVIE_1.code, updatedMovie1.code)
        assertEquals(MOVIE_1.disfilmCode, updatedMovie1.disfilmCode)
        assertEquals(MOVIE_1.releaseYear, updatedMovie1.releaseYear)
        assertEquals(MOVIE_1.premiereDate, updatedMovie1.premiereDate)
        assertEquals(MOVIE_1.duration, updatedMovie1.duration)
        assertEquals(MOVIE_1.parsedRating, updatedMovie1.parsedRating)
        assertEquals(MovieFormat.FORMAT_3D, updatedMovie1.parsedFormat)
        assertEquals(MovieTechnology.DOLBY_ATMOS, updatedMovie1.parsedTechnology)
        assertEquals(MovieLanguage.CT, updatedMovie1.parsedLanguage)
        assertEquals(MOVIE_1.distributorId, updatedMovie1.distributorId)
        assertEquals(technology1.id, updatedMovie1.technologyId)
        assertEquals(language1.id, updatedMovie1.languageId)
        assertEquals(tmsLanguage1.id, updatedMovie1.tmsLanguageId)

        verifySequence {
            applicationEventPublisherMock.publishEvent(MovieCreatedOrUpdatedEvent(movieId = movie1Id))
            applicationEventPublisherMock.publishEvent(any<AdminMovieCreatedOrUpdatedEvent>())
        }
    }

    @Test
    @SqlGroup(
        Sql(
            scripts = [
                "/db/migration/V104__init_production_table_with_data.sql",
                "/db/migration/V105__init_genre_table_with_data.sql",
                "/db/migration/V106__init_rating_table_with_data.sql",
                "/db/migration/V107__init_technology_table_with_data.sql",
                "/db/migration/V108__init_language_table_with_data.sql",
                "/db/migration/V109__init_tms_language_table_with_data.sql",
                "/db/migration/V110__init_jso_table_with_data.sql"
            ]
        )
    )
    fun `test messagingCreateOrUpdateMovie - create movie - should create correctly`() {
        distributorService.syncCreateOrUpdateDistributor(mapToCreateOrUpdateDistributorCommand(DISTRIBUTOR_1))
        val production1 = productionJpaFinderService.findByCode(PRODUCTION_1.code)!!
        val genre1 = genreJpaFinderService.findByCode(GENRE_1.code)!!
        val genre2 = genreJpaFinderService.findByCode(GENRE_2.code)!!
        val rating1 = ratingJpaFinderService.findByCode(RATING_1.code)!!
        val technology1 = technologyJpaFinderService.findByCode(TECHNOLOGY_1.code)!!
        val language1 = languageJpaFinderService.findByCode(LANGUAGE_1.code)!!
        val tmsLanguage1 = tmsLanguageJpaFinderService.findByCode(TMS_LANGUAGE_1.code)!!
        val jso1 = jsoJpaFinderService.findByCode(JSO_1.code)!!
        val jso2 = jsoJpaFinderService.findByCode(JSO_2.code)!!

        assertTrue(movieJsoJpaFinderService.findAll().isEmpty())

        val command = MessagingCreateOrUpdateMovieCommand(
            title = "The Movie",
            rawTitle = "The Movie IMAX 2D (ENG)",
            originalTitle = Optional.of("The Movie IMAX 2D (ENG)"),
            messagingCode = "uzuqab",
            code = "MOV123",
            disfilmCode = "123456",
            premiereDate = Optional.of(LocalDate.of(2025, 1, 1)),
            duration = 123,
            parsedRating = MovieRating.PLUS_18,
            parsedFormat = MovieFormat.FORMAT_2D,
            parsedTechnology = MovieTechnology.IMAX,
            parsedLanguage = MovieLanguage.ENG,
            distributorCode = DISTRIBUTOR_1.code,
            productionCode = Optional.of(production1.code),
            primaryGenreCode = Optional.of(genre1.code),
            secondaryGenreCode = Optional.of(genre2.code),
            ratingCode = Optional.of(rating1.code),
            technologyCode = technology1.code,
            languageCode = language1.code,
            tmsLanguageCode = tmsLanguage1.code,
            jsoCodes = setOf(jso1.code, jso2.code),
            origin = MovieOrigin.DISFILM
        )
        underTest.messagingCreateOrUpdateMovie(command)

        assertEquals(1L, movieRepository.count())

        val movie = movieRepository.findAll().first()
        assertEquals(command.title, movie.title)
        assertEquals(command.rawTitle, movie.rawTitle)
        assertEquals(command.originalTitle?.getOrNull()!!, movie.originalTitle)
        assertEquals(command.messagingCode, movie.messagingCode)
        assertEquals(command.messagingCode, movie.code)
        assertEquals(command.disfilmCode, movie.disfilmCode)
        assertEquals(command.premiereDate?.getOrNull()!!, movie.premiereDate)
        assertEquals(command.duration, movie.duration)
        assertEquals(command.parsedRating, movie.parsedRating)
        assertEquals(command.parsedFormat, movie.parsedFormat)
        assertEquals(command.parsedTechnology, movie.parsedTechnology)
        assertEquals(command.parsedLanguage, movie.parsedLanguage)
        assertEquals(DISTRIBUTOR_1.id, movie.distributorId)
        assertEquals(production1.id, movie.productionId)
        assertEquals(genre1.id, movie.primaryGenreId)
        assertEquals(genre2.id, movie.secondaryGenreId)
        assertEquals(rating1.id, movie.ratingId)
        assertEquals(technology1.id, movie.technologyId)
        assertEquals(language1.id, movie.languageId)
        assertEquals(tmsLanguage1.id, movie.tmsLanguageId)

        verifySequence {
            applicationEventPublisherMock.publishEvent(
                MovieWithJsosCreatedOrUpdatedEvent(
                    movieId = movie.id,
                    jsoIds = setOf(jso1.id, jso2.id)
                )
            )
            applicationEventPublisherMock.publishEvent(MovieCreatedOrUpdatedEvent(movieId = movie.id))
        }
    }

    @Test
    @SqlGroup(
        Sql(
            scripts = [
                "/db/migration/V104__init_production_table_with_data.sql",
                "/db/migration/V105__init_genre_table_with_data.sql",
                "/db/migration/V106__init_rating_table_with_data.sql",
                "/db/migration/V107__init_technology_table_with_data.sql",
                "/db/migration/V108__init_language_table_with_data.sql",
                "/db/migration/V109__init_tms_language_table_with_data.sql",
                "/db/migration/V110__init_jso_table_with_data.sql"
            ]
        )
    )
    fun `test messagingCreateOrUpdateMovie - update movie - should update correctly`() {
        distributorService.syncCreateOrUpdateDistributor(mapToCreateOrUpdateDistributorCommand(DISTRIBUTOR_1))
        val production1 = productionJpaFinderService.findByCode(PRODUCTION_1.code)!!
        val genre1 = genreJpaFinderService.findByCode(GENRE_1.code)!!
        val genre2 = genreJpaFinderService.findByCode(GENRE_2.code)!!
        val rating1 = ratingJpaFinderService.findByCode(RATING_1.code)!!
        val technology1 = technologyJpaFinderService.findByCode(TECHNOLOGY_1.code)!!
        val language1 = languageJpaFinderService.findByCode(LANGUAGE_1.code)!!
        val tmsLanguage1 = tmsLanguageJpaFinderService.findByCode(TMS_LANGUAGE_1.code)!!
        val jso1 = jsoJpaFinderService.findByCode(JSO_1.code)!!
        val jso2 = jsoJpaFinderService.findByCode(JSO_2.code)!!

        assertTrue(movieJsoJpaFinderService.findAll().isEmpty())

        val createCommand = MessagingCreateOrUpdateMovieCommand(
            title = "The Movie",
            rawTitle = "The Movie IMAX 2D (ENG)",
            originalTitle = Optional.of("The Movie IMAX 2D (ENG)"),
            messagingCode = "uzuqab",
            code = "MOV123",
            disfilmCode = "123456",
            premiereDate = Optional.of(LocalDate.of(2025, 1, 1)),
            duration = 123,
            parsedRating = MovieRating.PLUS_18,
            parsedFormat = MovieFormat.FORMAT_2D,
            parsedTechnology = MovieTechnology.IMAX,
            parsedLanguage = MovieLanguage.ENG,
            distributorCode = DISTRIBUTOR_1.code,
            productionCode = Optional.of(production1.code),
            primaryGenreCode = Optional.of(genre1.code),
            secondaryGenreCode = Optional.of(genre2.code),
            ratingCode = Optional.of(rating1.code),
            technologyCode = technology1.code,
            languageCode = language1.code,
            tmsLanguageCode = tmsLanguage1.code,
            jsoCodes = setOf(jso1.code, jso2.code),
            origin = MovieOrigin.DISFILM
        )
        underTest.messagingCreateOrUpdateMovie(createCommand)

        assertEquals(1L, movieRepository.count())

        distributorService.syncCreateOrUpdateDistributor(mapToCreateOrUpdateDistributorCommand(DISTRIBUTOR_2))
        val production2 = productionJpaFinderService.findByCode(PRODUCTION_2.code)!!
        val rating2 = ratingJpaFinderService.findByCode(RATING_2.code)!!
        val technology2 = technologyJpaFinderService.findByCode(TECHNOLOGY_2.code)!!
        val language2 = languageJpaFinderService.findByCode(LANGUAGE_2.code)!!
        val tmsLanguage2 = tmsLanguageJpaFinderService.findByCode(TMS_LANGUAGE_2.code)!!

        val updateCommand = MessagingCreateOrUpdateMovieCommand(
            title = "The Movie 2",
            rawTitle = "The Movie 2 IMAX 2D (ENG)",
            originalTitle = Optional.of("The Movie 2 IMAX 2D (ENG)"),
            messagingCode = "uzuqab",
            code = "MOV123",
            disfilmCode = "654321",
            premiereDate = Optional.of(LocalDate.of(2025, 1, 1)),
            duration = 321,
            parsedRating = MovieRating.PLUS_15,
            parsedFormat = MovieFormat.FORMAT_3D,
            parsedTechnology = MovieTechnology.MP4,
            parsedLanguage = MovieLanguage.SK,
            distributorCode = DISTRIBUTOR_2.code,
            productionCode = Optional.of(production2.code),
            primaryGenreCode = Optional.of(genre2.code),
            secondaryGenreCode = Optional.of(genre1.code),
            ratingCode = Optional.of(rating2.code),
            technologyCode = technology2.code,
            languageCode = language2.code,
            tmsLanguageCode = tmsLanguage2.code,
            jsoCodes = setOf(jso1.code),
            origin = MovieOrigin.DISFILM
        )
        underTest.messagingCreateOrUpdateMovie(updateCommand)

        assertEquals(1L, movieRepository.count())

        val movie = movieRepository.findAll().first()
        assertEquals(updateCommand.title, movie.title)
        assertEquals(updateCommand.rawTitle, movie.rawTitle)
        assertEquals(updateCommand.originalTitle?.getOrNull()!!, movie.originalTitle)
        assertEquals(updateCommand.messagingCode, movie.messagingCode)
        assertEquals(updateCommand.messagingCode, movie.code)
        assertEquals(updateCommand.disfilmCode, movie.disfilmCode)
        assertEquals(updateCommand.premiereDate?.getOrNull()!!, movie.premiereDate)
        assertEquals(updateCommand.duration, movie.duration)
        assertEquals(updateCommand.parsedRating, movie.parsedRating)
        assertEquals(updateCommand.parsedFormat, movie.parsedFormat)
        assertEquals(updateCommand.parsedTechnology, movie.parsedTechnology)
        assertEquals(updateCommand.parsedLanguage, movie.parsedLanguage)
        assertEquals(DISTRIBUTOR_2.id, movie.distributorId)
        assertEquals(production2.id, movie.productionId)
        assertEquals(genre2.id, movie.primaryGenreId)
        assertEquals(genre1.id, movie.secondaryGenreId)
        assertEquals(rating2.id, movie.ratingId)
        assertEquals(technology2.id, movie.technologyId)
        assertEquals(language2.id, movie.languageId)
        assertEquals(tmsLanguage2.id, movie.tmsLanguageId)

        verifySequence {
            applicationEventPublisherMock.publishEvent(
                MovieWithJsosCreatedOrUpdatedEvent(
                    movieId = movie.id,
                    jsoIds = setOf(jso1.id, jso2.id)
                )
            )
            applicationEventPublisherMock.publishEvent(MovieCreatedOrUpdatedEvent(movieId = movie.id))
            applicationEventPublisherMock.publishEvent(
                MovieWithJsosCreatedOrUpdatedEvent(
                    movieId = movie.id,
                    jsoIds = setOf(jso1.id)
                )
            )
            applicationEventPublisherMock.publishEvent(MovieCreatedOrUpdatedEvent(movieId = movie.id))
        }
    }

    @Test
    @SqlGroup(
        Sql(
            scripts = [
                "/db/migration/V104__init_production_table_with_data.sql",
                "/db/migration/V105__init_genre_table_with_data.sql",
                "/db/migration/V106__init_rating_table_with_data.sql",
                "/db/migration/V107__init_technology_table_with_data.sql",
                "/db/migration/V108__init_language_table_with_data.sql",
                "/db/migration/V109__init_tms_language_table_with_data.sql",
                "/db/migration/V110__init_jso_table_with_data.sql"
            ]
        )
    )
    fun `test messagingCreateOrUpdateMovie - movie with different messagingCode but same disfilmCode exists - should update the original disfilmCode movie`() {
        distributorService.syncCreateOrUpdateDistributor(mapToCreateOrUpdateDistributorCommand(DISTRIBUTOR_1))
        val production1 = productionJpaFinderService.findByCode(PRODUCTION_1.code)!!
        val genre1 = genreJpaFinderService.findByCode(GENRE_1.code)!!
        val genre2 = genreJpaFinderService.findByCode(GENRE_2.code)!!
        val rating1 = ratingJpaFinderService.findByCode(RATING_1.code)!!
        val jso1 = jsoJpaFinderService.findByCode(JSO_1.code)!!
        val jso2 = jsoJpaFinderService.findByCode(JSO_2.code)!!

        assertTrue(movieJsoJpaFinderService.findAll().isEmpty())

        val createCommand = MessagingCreateOrUpdateMovieCommand(
            title = "The Movie",
            rawTitle = "The Movie IMAX 2D (ENG)",
            originalTitle = Optional.of("The Movie IMAX 2D (ENG)"),
            messagingCode = "uzuqab",
            code = "MOV123",
            disfilmCode = "123456",
            premiereDate = Optional.of(LocalDate.of(2025, 1, 1)),
            duration = 123,
            parsedRating = MovieRating.PLUS_18,
            parsedFormat = MovieFormat.FORMAT_2D,
            parsedTechnology = MovieTechnology.IMAX,
            parsedLanguage = MovieLanguage.ENG,
            distributorCode = DISTRIBUTOR_1.code,
            productionCode = Optional.of(production1.code),
            primaryGenreCode = Optional.of(genre1.code),
            secondaryGenreCode = null,
            ratingCode = Optional.of(rating1.code),
            technologyCode = null,
            languageCode = null,
            tmsLanguageCode = null,
            jsoCodes = setOf(jso1.code, jso2.code),
            origin = MovieOrigin.DISFILM
        )
        underTest.messagingCreateOrUpdateMovie(createCommand)

        assertEquals(1L, movieRepository.count())

        distributorService.syncCreateOrUpdateDistributor(mapToCreateOrUpdateDistributorCommand(DISTRIBUTOR_2))
        val production2 = productionJpaFinderService.findByCode(PRODUCTION_2.code)!!
        val rating2 = ratingJpaFinderService.findByCode(RATING_2.code)!!
        val technology2 = technologyJpaFinderService.findByCode(TECHNOLOGY_2.code)!!
        val language2 = languageJpaFinderService.findByCode(LANGUAGE_2.code)!!
        val tmsLanguage2 = tmsLanguageJpaFinderService.findByCode(TMS_LANGUAGE_2.code)!!

        val createDuplicateCommand = MessagingCreateOrUpdateMovieCommand(
            title = "The Movie 2",
            rawTitle = "THE MOVIE 2 IMAX 2D (ENG)",
            originalTitle = Optional.of("THE MOVIE 2 IMAX 2D (ENG)"),
            messagingCode = "qbjjlb",
            code = "MOV123",
            disfilmCode = "123456",
            premiereDate = Optional.of(LocalDate.of(2025, 1, 1)),
            duration = 321,
            parsedRating = MovieRating.PLUS_15,
            parsedFormat = MovieFormat.FORMAT_3D,
            parsedTechnology = MovieTechnology.MP4,
            parsedLanguage = MovieLanguage.SK,
            distributorCode = DISTRIBUTOR_2.code,
            productionCode = Optional.of(production2.code),
            primaryGenreCode = Optional.of(genre2.code),
            secondaryGenreCode = null,
            ratingCode = Optional.of(rating2.code),
            technologyCode = technology2.code,
            languageCode = language2.code,
            tmsLanguageCode = tmsLanguage2.code,
            jsoCodes = setOf(jso1.code),
            origin = MovieOrigin.DISFILM
        )
        underTest.messagingCreateOrUpdateMovie(createDuplicateCommand)

        assertEquals(1L, movieRepository.count())

        val movie = movieRepository.findAll().first()
        assertEquals(createDuplicateCommand.title, movie.title)
        assertEquals(createDuplicateCommand.rawTitle, movie.rawTitle)
        assertEquals(createDuplicateCommand.originalTitle?.getOrNull()!!, movie.originalTitle)
        // messaging code should not change after creation
        assertEquals(createCommand.messagingCode, movie.messagingCode)
        assertEquals(createDuplicateCommand.messagingCode, movie.code)
        assertEquals(createDuplicateCommand.disfilmCode, movie.disfilmCode)
        assertEquals(createDuplicateCommand.premiereDate?.getOrNull()!!, movie.premiereDate)
        assertEquals(createDuplicateCommand.duration, movie.duration)
        assertEquals(createDuplicateCommand.parsedRating, movie.parsedRating)
        assertEquals(createDuplicateCommand.parsedFormat, movie.parsedFormat)
        assertEquals(createDuplicateCommand.parsedTechnology, movie.parsedTechnology)
        assertEquals(createDuplicateCommand.parsedLanguage, movie.parsedLanguage)
        assertEquals(DISTRIBUTOR_2.id, movie.distributorId)
        assertEquals(production2.id, movie.productionId)
        assertEquals(genre2.id, movie.primaryGenreId)
        assertNull(movie.secondaryGenreId)
        assertEquals(rating2.id, movie.ratingId)
        assertEquals(technology2.id, movie.technologyId)
        assertEquals(language2.id, movie.languageId)
        assertEquals(tmsLanguage2.id, movie.tmsLanguageId)

        verifySequence {
            applicationEventPublisherMock.publishEvent(
                MovieWithJsosCreatedOrUpdatedEvent(
                    movieId = movie.id,
                    jsoIds = setOf(jso1.id, jso2.id)
                )
            )
            applicationEventPublisherMock.publishEvent(MovieCreatedOrUpdatedEvent(movieId = movie.id))
            applicationEventPublisherMock.publishEvent(
                MovieWithJsosCreatedOrUpdatedEvent(
                    movieId = movie.id,
                    jsoIds = setOf(jso1.id)
                )
            )
            applicationEventPublisherMock.publishEvent(MovieCreatedOrUpdatedEvent(movieId = movie.id))
        }
    }

    @Test
    fun `test existsByCodeAndOriginalIdNotAndDeletedAtIsNull - movie with equal code already exists - returns true`() {
        distributorService.syncCreateOrUpdateDistributor(mapToCreateOrUpdateDistributorCommand(DISTRIBUTOR_1))
        val command = mapToCreateOrUpdateMovieCommand(MOVIE_1)
        underTest.syncCreateOrUpdateMovie(command)

        val createdMovie = movieJpaFinderService.findNonDeletedByOriginalId(MOVIE_1.originalId!!)
        assertNotNull(createdMovie)
        assertMovieEquals(MOVIE_1, createdMovie)

        assertTrue(movieRepository.existsByCodeAndOriginalIdNotAndDeletedAtIsNull(MOVIE_1.code, DIFFERENT_MOVIE_ORIGINAL_ID))
    }

    @Test
    fun `test existsByCodeAndOriginalIdNotAndDeletedAtIsNull - deleted movie with equal code already exists - returns false`() {
        distributorService.syncCreateOrUpdateDistributor(mapToCreateOrUpdateDistributorCommand(DISTRIBUTOR_1))
        val command = mapToCreateOrUpdateMovieCommand(MOVIE_1)
        underTest.syncCreateOrUpdateMovie(command)

        val createdMovie = movieJpaFinderService.findNonDeletedByOriginalId(MOVIE_1.originalId!!)
        assertNotNull(createdMovie)
        assertMovieEquals(MOVIE_1, createdMovie)

        movieRepository.save(
            createdMovie.apply {
                markDeleted()
            }
        )
        assertFalse(movieRepository.existsByCodeAndOriginalIdNotAndDeletedAtIsNull(MOVIE_1.code, DIFFERENT_MOVIE_ORIGINAL_ID))
    }

    @Test
    fun `test deleteMovie - related deleted screening exists - should delete movie`() {
        distributorService.syncCreateOrUpdateDistributor(mapToCreateOrUpdateDistributorCommand(DISTRIBUTOR_1))
        auditoriumService.syncCreateOrUpdateAuditorium(mapToCreateOrUpdateAuditoriumCommand(AUDITORIUM_1))
        auditoriumLayoutService.syncCreateOrUpdateAuditoriumLayout(
            mapToCreateOrUpdateAuditoriumLayoutCommand(AUDITORIUM_LAYOUT_1)
        )
        priceCategoryService.syncCreateOrUpdatePriceCategory(
            mapToSyncCreateOrUpdatePriceCategoryCommand(PRICE_CATEGORY_1)
        )

        val movieCommand1 = mapToCreateOrUpdateMovieCommand(MOVIE_1)
        underTest.syncCreateOrUpdateMovie(movieCommand1)
        val createdMovie1 = movieJpaFinderService.findNonDeletedByOriginalId(MOVIE_1.originalId!!)
        assertNotNull(createdMovie1)
        assertMovieEquals(MOVIE_1, createdMovie1)

        val screeningCommand = mapToCreateOrUpdateScreeningCommand(SCREENING_1)
        screeningService.syncCreateOrUpdateScreening(screeningCommand)
        val createdScreening = screeningFinderService.findByOriginalId(SCREENING_1.originalId!!)
        assertNotNull(createdScreening)

        screeningService.syncDeleteScreening(DeleteScreeningCommand(createdScreening.id))

        underTest.deleteMovie(
            DeleteMovieCommand(
                movieId = createdMovie1.id
            )
        )

        assertNotNull(movieJpaFinderService.getById(createdMovie1.id).deletedAt)
        val deletedMovie = movieJpaFinderService.getById(createdMovie1.id)
        assertNotNull(deletedMovie.deletedAt)

        verifySequence {
            applicationEventPublisherMock.publishEvent(AdminMovieDeletedEvent(messagingCode = createdMovie1.messagingCode))
        }
    }

    @Test
    fun `test messagingDeleteMovie - movie with no screenings - should delete movie`() {
        distributorService.syncCreateOrUpdateDistributor(mapToCreateOrUpdateDistributorCommand(DISTRIBUTOR_1))

        val movieCommand = mapToCreateOrUpdateMovieCommand(MOVIE_1)
        underTest.syncCreateOrUpdateMovie(movieCommand)
        val movie = movieJpaFinderService.findNonDeletedByOriginalId(MOVIE_1.originalId!!)!!

        underTest.messagingDeleteMovie(
            MessagingDeleteMovieCommand(
                messagingCode = movieCommand.code!!
            )
        )

        assertNotNull(movieJpaFinderService.getById(movie.id).deletedAt)
        val deletedMovie = movieJpaFinderService.getById(movie.id)
        assertNotNull(deletedMovie.deletedAt)
    }

    @Test
    fun `test deleteMovie - related screening exists - should throw exception`() {
        distributorService.syncCreateOrUpdateDistributor(mapToCreateOrUpdateDistributorCommand(DISTRIBUTOR_1))
        auditoriumService.syncCreateOrUpdateAuditorium(mapToCreateOrUpdateAuditoriumCommand(AUDITORIUM_1))
        auditoriumLayoutService.syncCreateOrUpdateAuditoriumLayout(
            mapToCreateOrUpdateAuditoriumLayoutCommand(AUDITORIUM_LAYOUT_1)
        )
        priceCategoryService.syncCreateOrUpdatePriceCategory(
            mapToSyncCreateOrUpdatePriceCategoryCommand(PRICE_CATEGORY_1)
        )

        val movieCommand1 = mapToCreateOrUpdateMovieCommand(MOVIE_1)
        underTest.syncCreateOrUpdateMovie(movieCommand1)
        val createdMovie1 = movieJpaFinderService.findNonDeletedByOriginalId(MOVIE_1.originalId!!)
        assertNotNull(createdMovie1)
        assertMovieEquals(MOVIE_1, createdMovie1)

        val screeningCommand = mapToCreateOrUpdateScreeningCommand(SCREENING_1)
        screeningService.syncCreateOrUpdateScreening(screeningCommand)
        val createdScreening = screeningFinderService.findByOriginalId(SCREENING_1.originalId!!)
        assertNotNull(createdScreening)

        assertThrows<ScreeningForMovieExistsException> {
            underTest.deleteMovie(
                DeleteMovieCommand(
                    movieId = createdMovie1.id
                )
            )
        }
    }

    @Test
    fun `test deleteMovie - non-existing movie - should throw exception`() {
        distributorService.syncCreateOrUpdateDistributor(mapToCreateOrUpdateDistributorCommand(DISTRIBUTOR_1))
        val movieCommand1 = mapToCreateOrUpdateMovieCommand(MOVIE_1)
        underTest.syncCreateOrUpdateMovie(movieCommand1)
        val createdMovie1 = movieJpaFinderService.findNonDeletedByOriginalId(MOVIE_1.originalId!!)
        assertNotNull(createdMovie1)
        assertMovieEquals(MOVIE_1, createdMovie1)

        assertThrows<MovieNotFoundException> {
            underTest.deleteMovie(
                DeleteMovieCommand(
                    movieId = UUID.randomUUID()
                )
            )
        }
    }

    @Test
    fun `test updateMovieOriginalId - should correctly update in db`() {
        distributorService.syncCreateOrUpdateDistributor(mapToCreateOrUpdateDistributorCommand(DISTRIBUTOR_1))
        val movie1 = createMovie(
            originalId = null,
            distributorId = DISTRIBUTOR_1.id
        ).also { movieRepository.save(it) }
        assertNull(movieRepository.findById(movie1.id).get().originalId)

        underTest.updateMovieOriginalId(
            UpdateMovieOriginalIdCommand(
                movieId = movie1.id,
                originalId = 5
            )
        )

        assertEquals(5, movieRepository.findById(movie1.id).get().originalId)
    }

    @Test
    @SqlGroup(
        Sql(
            scripts = [
                "/db/migration/V107__init_technology_table_with_data.sql",
                "/db/migration/V108__init_language_table_with_data.sql"
            ]
        )
    )
    fun `test updateMovieLanguageAndTechnology - should correctly update in db`() {
        distributorService.syncCreateOrUpdateDistributor(mapToCreateOrUpdateDistributorCommand(DISTRIBUTOR_1))
        val technology1 = technologyJpaFinderService.findByCode(TECHNOLOGY_1.code)!!
        val language1 = languageJpaFinderService.findByCode(LANGUAGE_1.code)!!

        val command = mapToCreateOrUpdateMovieCommand(MOVIE_1)
        underTest.syncCreateOrUpdateMovie(command)

        val createdMovie = movieRepository.findAll()[0]
        assertNull(createdMovie.languageId)
        assertNull(createdMovie.technologyId)

        underTest.updateMovieLanguageAndTechnology(
            UpdateMovieLanguageAndTechnologyCommand(
                movieId = createdMovie.id,
                languageId = language1.id,
                technologyId = technology1.id
            )
        )

        val updatedMovie = movieRepository.findAll()[0]
        assertNotNull(updatedMovie)
        assertEquals(language1.id, updatedMovie.languageId)
        assertEquals(technology1.id, updatedMovie.technologyId)
    }

    @Test
    @SqlGroup(
        Sql(
            scripts = [
                "/db/migration/V107__init_technology_table_with_data.sql",
                "/db/migration/V108__init_language_table_with_data.sql"
            ]
        )
    )
    fun `test updateMovieLanguageAndTechnology - should correctly set null in db`() {
        distributorService.syncCreateOrUpdateDistributor(mapToCreateOrUpdateDistributorCommand(DISTRIBUTOR_1))
        val technology1 = technologyJpaFinderService.findByCode(TECHNOLOGY_1.code)!!
        val language1 = languageJpaFinderService.findByCode(LANGUAGE_1.code)!!

        val movie1 = createMovie(
            originalId = null,
            distributorId = DISTRIBUTOR_1.id,
            languageId = language1.id,
            technologyId = technology1.id
        ).also { movieRepository.save(it) }

        val createdMovie = movieRepository.findById(movie1.id).getOrNull()
        assertNotNull(createdMovie)
        assertNull(createdMovie.originalId)
        assertEquals(language1.id, createdMovie.languageId)
        assertEquals(technology1.id, createdMovie.technologyId)

        underTest.updateMovieLanguageAndTechnology(
            UpdateMovieLanguageAndTechnologyCommand(
                movieId = createdMovie.id,
                languageId = null,
                technologyId = null
            )
        )

        val updatedMovieWithNulls = movieRepository.findById(movie1.id).getOrNull()
        assertNotNull(updatedMovieWithNulls)
        assertNull(updatedMovieWithNulls.languageId)
        assertNull(updatedMovieWithNulls.technologyId)
    }

    companion object {
        @JvmStatic
        fun syncCreateOrUpdateMovieCommandProvider(): Stream<Arguments> {
            val command = mapToCreateOrUpdateMovieCommand(MOVIE_1).copy(id = null)
            return Stream.of(
                Arguments.of(command.copy(originalId = null)),
                Arguments.of(command.copy(title = null)),
                Arguments.of(command.copy(parsedFormat = null))
            )
        }

        @JvmStatic
        fun adminCreateOrUpdateMovieCommandProvider(): Stream<Arguments> {
            val command = mapToCreateOrUpdateMovieCommand(MOVIE_1).copy(id = null, code = null)
            return Stream.of(
                Arguments.of(command.copy(duration = null)),
                Arguments.of(command.copy(technologyId = null)),
                Arguments.of(command.copy(languageId = null)),
                Arguments.of(command.copy(tmsLanguageId = null))
            )
        }
    }

    private fun assertMovieEquals(expected: Movie, actual: Movie) {
        assertEquals(expected.originalId, actual.originalId)
        assertEquals(expected.rawTitle, actual.rawTitle)
        assertEquals(expected.originalTitle, actual.originalTitle)
        assertEquals(expected.title, actual.title)
        assertEquals(expected.code, actual.code)
        assertEquals(expected.disfilmCode, actual.disfilmCode)
        assertEquals(expected.releaseYear, actual.releaseYear)
        assertEquals(expected.premiereDate, actual.premiereDate)
        assertEquals(expected.duration, actual.duration)
        assertEquals(expected.parsedRating, actual.parsedRating)
        assertEquals(expected.parsedFormat, actual.parsedFormat)
        assertEquals(expected.parsedTechnology, actual.parsedTechnology)
        assertEquals(expected.parsedLanguage, actual.parsedLanguage)
        assertEquals(expected.distributorId, actual.distributorId)
        assertNotNull(actual.createdAt)
        assertNotNull(actual.updatedAt)
    }
}

private const val DIFFERENT_MOVIE_ORIGINAL_ID = 1000
private val DISTRIBUTOR_1 = createDistributor()
private val DISTRIBUTOR_2 = createDistributor(originalId = 2, code = "DIS2")
private val MOVIE_1 = createMovie(
    originalId = 1,
    title = "Star Wars: Episode I",
    rawTitle = "Star Wars: Episode I 2D",
    code = "123456",
    releaseYear = 2001,
    premiereDate = LocalDate.of(2001, 1, 1),
    parsedFormat = MovieFormat.FORMAT_2D,
    parsedLanguage = null,
    distributorId = DISTRIBUTOR_1.id
)
private val MOVIE_2 = createMovie(
    originalId = 2,
    title = "Guardians of the Galaxy",
    rawTitle = "Guardians of the Galaxy 3D (ORIG)",
    code = "789012",
    releaseYear = 2014,
    premiereDate = LocalDate.of(2014, 1, 1),
    parsedFormat = MovieFormat.FORMAT_3D,
    parsedLanguage = MovieLanguage.ORIG,
    distributorId = DISTRIBUTOR_1.id
)
private val MOVIE_3 = createMovie(
    originalId = 3,
    title = "Star Wars: Episode I",
    rawTitle = "Star Wars: Episode I 2D ATMOS (ST)",
    code = "567690",
    releaseYear = 2001,
    premiereDate = LocalDate.of(2001, 1, 1),
    parsedFormat = MovieFormat.FORMAT_2D,
    parsedTechnology = MovieTechnology.DOLBY_ATMOS,
    parsedLanguage = MovieLanguage.ST,
    distributorId = DISTRIBUTOR_1.id
)
private val MOVIE_4 = createMovie( // MOVIE_1 with disfilmCode
    originalId = 1,
    title = "Star Wars: Episode I",
    rawTitle = "Star Wars: Episode I 2D",
    code = "123456",
    releaseYear = 2001,
    premiereDate = LocalDate.of(2001, 1, 1),
    parsedFormat = MovieFormat.FORMAT_2D,
    parsedLanguage = null,
    distributorId = DISTRIBUTOR_1.id,
    disfilmCode = "USA845672"
)
private val AUDITORIUM_1 = createAuditorium(
    originalId = 1,
    code = "A",
    title = "SÁLA A - CINEMAX BRATISLAVA"
)
private val AUDITORIUM_LAYOUT_1 = createAuditoriumLayout(auditoriumId = AUDITORIUM_1.id)
private val PRICE_CATEGORY_1 = createPriceCategory(
    originalId = 1,
    title = "ARTMAX PO 17",
    active = true
)
private val SCREENING_1 = createScreening(
    originalId = 1,
    auditoriumLayoutId = AUDITORIUM_LAYOUT_1.id,
    auditoriumId = AUDITORIUM_1.id,
    movieId = MOVIE_1.id,
    priceCategoryId = PRICE_CATEGORY_1.id
)
private val PRODUCTION_1 = Production(
    originalId = 98,
    code = "56",
    title = "USA"
)
private val PRODUCTION_2 = Production(
    originalId = 99,
    code = "57",
    title = "Japonsko"
)
private val GENRE_1 = Genre(
    originalId = 2,
    code = "1",
    title = "Dráma"
)
private val GENRE_2 = Genre(
    originalId = 5,
    code = "4",
    title = "Sci-Fi"
)
private val RATING_1 = Rating(
    originalId = 6,
    code = "12",
    title = "12"
)
private val RATING_2 = Rating(
    originalId = 7,
    code = "15",
    title = "15"
)
private val TECHNOLOGY_1 = Technology(
    originalId = 12,
    code = "05",
    title = "IMAX"
)
private val TECHNOLOGY_2 = Technology(
    originalId = 13,
    code = "07",
    title = "PC"
)
private val LANGUAGE_1 = Language(
    originalId = 1,
    code = "CZ",
    title = "česká verze"
)
private val LANGUAGE_2 = Language(
    originalId = 2,
    code = "SK",
    title = "slovenská verze"
)
private val TMS_LANGUAGE_1 = TmsLanguage(
    originalId = 13,
    code = "5",
    title = "originálna verzia"
)
private val TMS_LANGUAGE_2 = TmsLanguage(
    originalId = 14,
    code = "S",
    title = "slovenský dabing"
)
private val JSO_1 = Jso(
    originalId = 3,
    code = "S",
    title = "Strach"
)
private val JSO_2 = Jso(
    originalId = 6,
    code = "V",
    title = "Vulgarizmy"
)
