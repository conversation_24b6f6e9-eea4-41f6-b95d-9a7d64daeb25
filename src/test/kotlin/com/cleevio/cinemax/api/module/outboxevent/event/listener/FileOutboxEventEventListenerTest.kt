package com.cleevio.cinemax.api.module.outboxevent.event.listener

import com.cleevio.cinemax.api.module.file.event.FileCreatedEvent
import com.cleevio.cinemax.api.module.outboxevent.constant.OutboxEventType
import com.cleevio.cinemax.api.module.outboxevent.service.OutboxEventService
import com.cleevio.cinemax.api.module.outboxevent.service.command.CreateOutboxEventCommand
import com.cleevio.cinemax.api.util.toUUID
import io.mockk.every
import io.mockk.just
import io.mockk.mockk
import io.mockk.runs
import io.mockk.verify
import org.junit.jupiter.api.Test

class FileOutboxEventEventListenerTest {

    private val outboxEventService = mockk<OutboxEventService>()
    private val underTest = FileOutboxEventEventListener(outboxEventService)

    @Test
    fun `test listenToFileCreatedEvent - should correctly handle FileCreatedEvent`() {
        every { outboxEventService.createOutboxEvent(any()) } just runs

        underTest.listenToFileCreatedEvent(
            FileCreatedEvent(
                fileId = 1.toUUID()
            )
        )

        verify {
            outboxEventService.createOutboxEvent(
                CreateOutboxEventCommand(
                    entityId = 1.toUUID(),
                    type = OutboxEventType.FILE_CREATED
                )
            )
        }
    }
}
