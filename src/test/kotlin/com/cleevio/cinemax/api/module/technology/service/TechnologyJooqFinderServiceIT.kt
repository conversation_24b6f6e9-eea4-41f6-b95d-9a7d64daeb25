package com.cleevio.cinemax.api.module.technology.service

import com.cleevio.cinemax.api.DatabaseCleanup
import com.cleevio.cinemax.api.IntegrationTest
import com.cleevio.cinemax.api.module.technology.entity.Technology
import com.cleevio.cinemax.api.util.assertDescriptorEquals
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.test.context.jdbc.Sql
import org.springframework.test.context.jdbc.SqlGroup
import kotlin.test.assertEquals

@SqlGroup(
    Sql(
        scripts = [
            "/db/migration/V107__init_technology_table_with_data.sql"
        ]
    )
)
class TechnologyJooqFinderServiceIT @Autowired constructor(
    private val underTest: TechnologyJooqFinderService,
) : IntegrationTest(DatabaseCleanup.AFTER_EACH) {

    @Test
    fun `test findAll - should find all technology values`() {
        val technologies = underTest.findAll().sortedBy { it.createdAt }

        assertEquals(20, technologies.size)
        assertDescriptorEquals(TECHNOLOGY_1, technologies[0])
        assertDescriptorEquals(TECHNOLOGY_2, technologies[1])
        assertDescriptorEquals(TECHNOLOGY_3, technologies[4])
    }
}

private val TECHNOLOGY_1 = Technology(
    originalId = 8,
    code = "01",
    title = "Analog"
)
private val TECHNOLOGY_2 = Technology(
    originalId = 9,
    code = "02",
    title = "DVD"
)
private val TECHNOLOGY_3 = Technology(
    originalId = 12,
    code = "05",
    title = "IMAX"
)
