package com.cleevio.cinemax.api.module.basketitem.service

import com.cleevio.cinemax.api.common.constant.REDUCED_TAX_RATE
import com.cleevio.cinemax.api.common.constant.STANDARD_TAX_RATE
import com.cleevio.cinemax.api.module.basketitem.constant.BasketItemType
import com.cleevio.cinemax.api.module.basketitem.model.BasketItemTaxRateModel
import com.cleevio.cinemax.api.module.basketitem.model.swapProductsAndProductDiscounts
import com.cleevio.cinemax.api.module.product.constant.ProductType
import com.cleevio.cinemax.api.module.productcategory.constant.ProductCategoryType
import com.cleevio.cinemax.api.util.createBasketItem
import com.cleevio.cinemax.api.util.createProduct
import com.cleevio.cinemax.api.util.createProductCategory
import com.cleevio.cinemax.api.util.toUUID
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.Arguments
import org.junit.jupiter.params.provider.MethodSource
import java.math.BigDecimal
import java.util.UUID
import java.util.stream.Stream
import kotlin.test.Test
import kotlin.test.assertEquals

class BasketItemTaxRateModelTest {

    @Test
    fun `test swapProductsAndProductDiscounts - should sort properly with two isolated groups applied`() {
        val basketItemTaxRateModels = listOf(
            BASKET_ITEM_1_POPCORN_TAX_RATE_MODEL,
            BASKET_ITEM_3_PRODUCT_DISCOUNT_ISOLATED_TAX_RATE_MODEL,
            BASKET_ITEM_2_PRODUCT_ISOLATED_TAX_RATE_MODEL,
            BASKET_ITEM_4_MARGOT_TAX_RATE_MODEL,
            BASKET_ITEM_6_PRODUCT_DISCOUNT_ISOLATED_TAX_RATE_MODEL,
            BASKET_ITEM_5_PRODUCT_ISOLATED_TAX_RATE_MODEL
        )

        val sorted = basketItemTaxRateModels.swapProductsAndProductDiscounts()

        assertEquals(BASKET_ITEM_1_POPCORN.id, sorted[0].basketItem.id)
        assertEquals(BASKET_ITEM_2_POPCORN_ISOLATED.id, sorted[1].basketItem.id)
        assertEquals(BASKET_ITEM_3_PRODUCT_DISCOUNT_ISOLATED.id, sorted[2].basketItem.id)
        assertEquals(BASKET_ITEM_4_MARGOT.id, sorted[3].basketItem.id)
        assertEquals(BASKET_ITEM_5_POPCORN_ISOLATED.id, sorted[4].basketItem.id)
        assertEquals(BASKET_ITEM_6_PRODUCT_DISCOUNT_ISOLATED.id, sorted[5].basketItem.id)
    }

    @ParameterizedTest
    @MethodSource("basketItemTaxRateModelProvider")
    fun `test swapProductsAndProductDiscounts - should sort properly`(
        basketItemTaxRateModels: List<BasketItemTaxRateModel>,
        sortedBasketItemIds: List<UUID>,
    ) {
        val sorted = basketItemTaxRateModels.swapProductsAndProductDiscounts()

        assertEquals(sorted[0].basketItem.id, sortedBasketItemIds[0])
        assertEquals(sorted[1].basketItem.id, sortedBasketItemIds[1])
        assertEquals(sorted[2].basketItem.id, sortedBasketItemIds[2])
        assertEquals(sorted[3].basketItem.id, sortedBasketItemIds[3])
    }

    companion object {
        @JvmStatic
        fun basketItemTaxRateModelProvider(): Stream<Arguments> {
            return Stream.of(
                Arguments.of(
                    listOf(
                        BASKET_ITEM_3_PRODUCT_DISCOUNT_ISOLATED_TAX_RATE_MODEL,
                        BASKET_ITEM_2_PRODUCT_ISOLATED_TAX_RATE_MODEL,
                        BASKET_ITEM_1_POPCORN_TAX_RATE_MODEL,
                        BASKET_ITEM_4_MARGOT_TAX_RATE_MODEL
                    ),
                    listOf(
                        BASKET_ITEM_2_POPCORN_ISOLATED.id,
                        BASKET_ITEM_3_PRODUCT_DISCOUNT_ISOLATED.id,
                        BASKET_ITEM_1_POPCORN.id,
                        BASKET_ITEM_4_MARGOT.id
                    )
                ),
                Arguments.of(
                    listOf(
                        BASKET_ITEM_1_POPCORN_TAX_RATE_MODEL,
                        BASKET_ITEM_3_PRODUCT_DISCOUNT_ISOLATED_TAX_RATE_MODEL,
                        BASKET_ITEM_2_PRODUCT_ISOLATED_TAX_RATE_MODEL,
                        BASKET_ITEM_4_MARGOT_TAX_RATE_MODEL
                    ),
                    listOf(
                        BASKET_ITEM_1_POPCORN.id,
                        BASKET_ITEM_2_POPCORN_ISOLATED.id,
                        BASKET_ITEM_3_PRODUCT_DISCOUNT_ISOLATED.id,
                        BASKET_ITEM_4_MARGOT.id
                    )
                ),
                Arguments.of(
                    listOf(
                        BASKET_ITEM_4_MARGOT_TAX_RATE_MODEL,
                        BASKET_ITEM_2_PRODUCT_ISOLATED_TAX_RATE_MODEL,
                        BASKET_ITEM_3_PRODUCT_DISCOUNT_ISOLATED_TAX_RATE_MODEL,
                        BASKET_ITEM_1_POPCORN_TAX_RATE_MODEL
                    ),
                    listOf(
                        BASKET_ITEM_4_MARGOT.id,
                        BASKET_ITEM_2_POPCORN_ISOLATED.id,
                        BASKET_ITEM_3_PRODUCT_DISCOUNT_ISOLATED.id,
                        BASKET_ITEM_1_POPCORN.id
                    )
                )
            )
        }
    }
}

private val BASKET_1_ID = 1.toUUID()
private val PRODUCT_CATEGORY_1_PRODUCT_STANDARD_TAX_RATE = createProductCategory(
    originalId = 1,
    code = "A1",
    title = "Popcorn nachos",
    taxRate = STANDARD_TAX_RATE
)
private val PRODUCT_CATEGORY_2_PRODUCT_REDUCED_TAX_RATE_SNACKS = createProductCategory(
    originalId = 2,
    code = "A2",
    title = "Pochúťky",
    taxRate = REDUCED_TAX_RATE
)
private val PRODUCT_CATEGORY_3_PRODUCT_DISCOUNT_NON_CARD = createProductCategory(
    originalId = 3,
    code = "A3",
    title = "Zľava",
    type = ProductCategoryType.DISCOUNT,
    taxRate = STANDARD_TAX_RATE
)
private val PRODUCT_1_POPCORN = createProduct(
    originalId = 1,
    code = "01X",
    productCategoryId = PRODUCT_CATEGORY_1_PRODUCT_STANDARD_TAX_RATE.id,
    title = "Popcorn XXL syrový",
    price = BigDecimal.valueOf(4)
)
private val PRODUCT_2_MARGOT = createProduct(
    originalId = 2,
    code = "02X",
    productCategoryId = PRODUCT_CATEGORY_2_PRODUCT_REDUCED_TAX_RATE_SNACKS.id,
    title = "Margot guličky",
    price = BigDecimal.valueOf(2)
)
private val PRODUCT_3_PRODUCT_DISCOUNT_ISOLATED = createProduct(
    originalId = 3,
    code = "08X",
    productCategoryId = PRODUCT_CATEGORY_3_PRODUCT_DISCOUNT_NON_CARD.id,
    title = "Voucher zlava 100%",
    type = ProductType.ADDITIONAL_SALE,
    price = BigDecimal.ZERO,
    discountPercentage = 20
)
private val BASKET_ITEM_1_POPCORN = createBasketItem(
    id = 1.toUUID(),
    basketId = BASKET_1_ID,
    productId = PRODUCT_1_POPCORN.id,
    type = BasketItemType.PRODUCT,
    price = BigDecimal.valueOf(4),
    quantity = 1,
    productReceiptNumber = "000486505603"
)
private val BASKET_ITEM_2_POPCORN_ISOLATED = createBasketItem(
    id = 2.toUUID(),
    basketId = BASKET_1_ID,
    productId = PRODUCT_1_POPCORN.id,
    productIsolatedWith = PRODUCT_3_PRODUCT_DISCOUNT_ISOLATED.id,
    type = BasketItemType.PRODUCT,
    price = BigDecimal.valueOf(4),
    quantity = 1,
    productReceiptNumber = "000486505603"
)
private val BASKET_ITEM_3_PRODUCT_DISCOUNT_ISOLATED = createBasketItem(
    id = 3.toUUID(),
    basketId = BASKET_1_ID,
    productId = PRODUCT_3_PRODUCT_DISCOUNT_ISOLATED.id,
    productIsolatedWith = PRODUCT_1_POPCORN.id,
    type = BasketItemType.PRODUCT_DISCOUNT,
    price = BigDecimal.valueOf(4).negate(),
    quantity = 1,
    productReceiptNumber = "000486505603"
)
private val BASKET_ITEM_4_MARGOT = createBasketItem(
    id = 4.toUUID(),
    basketId = BASKET_1_ID,
    productId = PRODUCT_2_MARGOT.id,
    type = BasketItemType.PRODUCT,
    price = BigDecimal.valueOf(2),
    quantity = 1,
    productReceiptNumber = "000486505603"
)
private val BASKET_ITEM_5_POPCORN_ISOLATED = createBasketItem(
    id = 5.toUUID(),
    basketId = BASKET_1_ID,
    productId = PRODUCT_1_POPCORN.id,
    productIsolatedWith = PRODUCT_3_PRODUCT_DISCOUNT_ISOLATED.id,
    type = BasketItemType.PRODUCT,
    price = BigDecimal.valueOf(4),
    quantity = 1,
    productReceiptNumber = "000486505603"
)
private val BASKET_ITEM_6_PRODUCT_DISCOUNT_ISOLATED = createBasketItem(
    id = 6.toUUID(),
    basketId = BASKET_1_ID,
    productId = PRODUCT_3_PRODUCT_DISCOUNT_ISOLATED.id,
    productIsolatedWith = PRODUCT_1_POPCORN.id,
    type = BasketItemType.PRODUCT_DISCOUNT,
    price = BigDecimal.valueOf(4).negate(),
    quantity = 1,
    productReceiptNumber = "000486505603"
)
private val BASKET_ITEM_1_POPCORN_TAX_RATE_MODEL = BasketItemTaxRateModel(
    basketItem = BASKET_ITEM_1_POPCORN,
    taxRate = PRODUCT_CATEGORY_1_PRODUCT_STANDARD_TAX_RATE.taxRate
)
private val BASKET_ITEM_2_PRODUCT_ISOLATED_TAX_RATE_MODEL = BasketItemTaxRateModel(
    basketItem = BASKET_ITEM_2_POPCORN_ISOLATED,
    taxRate = PRODUCT_CATEGORY_1_PRODUCT_STANDARD_TAX_RATE.taxRate
)
private val BASKET_ITEM_3_PRODUCT_DISCOUNT_ISOLATED_TAX_RATE_MODEL = BasketItemTaxRateModel(
    basketItem = BASKET_ITEM_3_PRODUCT_DISCOUNT_ISOLATED,
    taxRate = PRODUCT_CATEGORY_3_PRODUCT_DISCOUNT_NON_CARD.taxRate
)
private val BASKET_ITEM_4_MARGOT_TAX_RATE_MODEL = BasketItemTaxRateModel(
    basketItem = BASKET_ITEM_4_MARGOT,
    taxRate = PRODUCT_CATEGORY_2_PRODUCT_REDUCED_TAX_RATE_SNACKS.taxRate
)
private val BASKET_ITEM_5_PRODUCT_ISOLATED_TAX_RATE_MODEL = BasketItemTaxRateModel(
    basketItem = BASKET_ITEM_5_POPCORN_ISOLATED,
    taxRate = PRODUCT_CATEGORY_1_PRODUCT_STANDARD_TAX_RATE.taxRate
)
private val BASKET_ITEM_6_PRODUCT_DISCOUNT_ISOLATED_TAX_RATE_MODEL = BasketItemTaxRateModel(
    basketItem = BASKET_ITEM_6_PRODUCT_DISCOUNT_ISOLATED,
    taxRate = PRODUCT_CATEGORY_3_PRODUCT_DISCOUNT_NON_CARD.taxRate
)
