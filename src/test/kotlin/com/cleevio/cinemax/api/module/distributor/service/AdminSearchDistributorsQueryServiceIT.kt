package com.cleevio.cinemax.api.module.distributor.service

import com.cleevio.cinemax.api.DatabaseCleanup
import com.cleevio.cinemax.api.IntegrationTest
import com.cleevio.cinemax.api.module.distributor.controller.dto.AdminSearchDistributorsResponse
import com.cleevio.cinemax.api.module.distributor.entity.Distributor
import com.cleevio.cinemax.api.module.distributor.service.query.AdminSearchDistributorsQuery
import com.cleevio.cinemax.api.module.distributor.service.query.SearchDistributorsFilter
import com.cleevio.cinemax.api.truncatedToSeconds
import com.cleevio.cinemax.api.util.createDistributor
import com.cleevio.cinemax.psql.tables.DistributorColumnNames
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.data.domain.PageRequest
import org.springframework.data.domain.Pageable
import org.springframework.data.domain.Sort
import kotlin.test.assertEquals

class AdminSearchDistributorsQueryServiceIT @Autowired constructor(
    private val underTest: AdminSearchDistributorsQueryService,
    private val distributorRepository: DistributorRepository,
) : IntegrationTest(DatabaseCleanup.AFTER_EACH) {

    @Test
    fun `test AdminSearchDistributorsQuery - should correctly return all records sorted`() {
        val distributor1 = createDistributor(originalId = 1, code = "OC1").also { distributorRepository.save(it) }
        val distributor2 = createDistributor(originalId = 2, code = "OC2").also { distributorRepository.save(it) }
        val distributor3 = createDistributor(originalId = 3, code = "OC3").also { distributorRepository.save(it) }

        val result = underTest(
            AdminSearchDistributorsQuery(
                filter = SearchDistributorsFilter(),
                pageable = PageRequest.of(0, 10, Sort.by(DistributorColumnNames.CREATED_AT).descending())
            )
        )

        assertEquals(3, result.totalElements)
        assertEquals(1, result.totalPages)
        assertEquals(3, result.content.size)

        assertDistributorEquals(distributor3, result.content[0])
        assertDistributorEquals(distributor2, result.content[1])
        assertDistributorEquals(distributor1, result.content[2])
    }

    @Test
    fun `test AdminSearchDistributorsQuery - should correctly return filtered records`() {
        val distributor1 = createDistributor(
            originalId = 1,
            code = "OC1",
            title = "Hello movies"
        ).also { distributorRepository.save(it) }
        val distributor2 = createDistributor(
            originalId = 2,
            code = "OC2",
            title = "Night screens"
        ).also { distributorRepository.save(it) }
        val distributor3 = createDistributor(
            originalId = 3,
            code = "OC3",
            title = "Night movies"
        ).also { distributorRepository.save(it) }

        val result1 = underTest(
            AdminSearchDistributorsQuery(
                filter = SearchDistributorsFilter(title = "movie"),
                pageable = Pageable.unpaged()
            )
        )
        assertEquals(2, result1.totalElements)
        assertEquals(1, result1.totalPages)
        assertEquals(2, result1.content.size)
        assertDistributorEquals(distributor1, result1.content[0])
        assertDistributorEquals(distributor3, result1.content[1])

        val result2 = underTest(
            AdminSearchDistributorsQuery(
                filter = SearchDistributorsFilter(title = "nigh"),
                pageable = Pageable.unpaged()
            )
        )
        assertEquals(2, result2.totalElements)
        assertEquals(1, result2.totalPages)
        assertEquals(2, result2.content.size)
        assertDistributorEquals(distributor2, result2.content[0])
        assertDistributorEquals(distributor3, result2.content[1])
    }

    private fun assertDistributorEquals(expected: Distributor, actual: AdminSearchDistributorsResponse) {
        assertEquals(expected.id, actual.id)
        assertEquals(expected.title, actual.title)
        assertEquals(expected.createdAt.truncatedToSeconds(), actual.createdAt.truncatedToSeconds())
        assertEquals(expected.updatedAt.truncatedToSeconds(), actual.updatedAt.truncatedToSeconds())
    }
}
