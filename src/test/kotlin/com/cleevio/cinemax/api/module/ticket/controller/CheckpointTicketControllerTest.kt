package com.cleevio.cinemax.api.module.ticket.controller

import com.cleevio.cinemax.api.ControllerTest
import com.cleevio.cinemax.api.common.constant.ApiVersion
import com.cleevio.cinemax.api.common.util.jsonContent
import com.cleevio.cinemax.api.module.discountcard.constant.DiscountCardType
import com.cleevio.cinemax.api.module.employee.constant.EmployeeRole
import com.cleevio.cinemax.api.module.movie.constant.MovieRating
import com.cleevio.cinemax.api.module.screening.constant.MovieFormat
import com.cleevio.cinemax.api.module.screening.constant.MovieLanguage
import com.cleevio.cinemax.api.module.screening.constant.MovieTechnology
import com.cleevio.cinemax.api.module.seat.constant.SeatType
import com.cleevio.cinemax.api.module.ticket.controller.dto.CheckpointGetBasketTicketsMovieResponse
import com.cleevio.cinemax.api.module.ticket.controller.dto.CheckpointGetBasketTicketsResponse
import com.cleevio.cinemax.api.module.ticket.controller.dto.CheckpointGetBasketTicketsScreeningResponse
import com.cleevio.cinemax.api.module.ticket.controller.dto.CheckpointGetBasketTicketsTicketResponse
import com.cleevio.cinemax.api.module.ticket.controller.dto.TicketStatus
import com.cleevio.cinemax.api.module.ticket.service.command.MarkTicketsAsUsedCommand
import com.cleevio.cinemax.api.module.ticket.service.query.CheckpointGetBasketTicketsQuery
import com.cleevio.cinemax.api.util.mockAccessToken
import io.mockk.Runs
import io.mockk.every
import io.mockk.just
import io.mockk.verify
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest
import org.springframework.http.HttpHeaders
import org.springframework.http.MediaType
import org.springframework.test.web.servlet.MockMvc
import org.springframework.test.web.servlet.get
import org.springframework.test.web.servlet.post
import java.time.LocalDate
import java.time.LocalTime
import java.util.UUID

@WebMvcTest(CheckpointTicketController::class)
class CheckpointTicketControllerTest @Autowired constructor(
    private val mvc: MockMvc,
) : ControllerTest() {

    @Test
    fun `test markTicketsAsUsed - should serialize and deserialize correctly`() {
        val ticket1Id = UUID.fromString("85b74cd4-4f7f-4786-8820-8b3bc60a3df0")
        val ticket2Id = UUID.fromString("b0ad5e08-c6b5-4c41-a424-50fb5e911d6e")

        every { ticketService.markTicketsAsUsed(any()) } just Runs

        mvc.post("$BASE_PATH/mark-used") {
            header(HttpHeaders.ACCEPT, ApiVersion.VERSION_1_JSON)
            header(HttpHeaders.AUTHORIZATION, mockAccessToken(EmployeeRole.CASHIER))
            contentType = MediaType.APPLICATION_JSON
            content = """
                {
                    "ticketIds": [
                        "$ticket1Id",
                        "$ticket2Id"
                    ]
                }
            """.trimIndent()
        }.andExpect {
            status { isNoContent() }
        }

        verify {
            ticketService.markTicketsAsUsed(
                MarkTicketsAsUsedCommand(
                    ticketIds = setOf(ticket1Id, ticket2Id)
                )
            )
        }
    }

    @Test
    fun `test getBasketTickets - receipt number - should serialize and deserialize correctly`() {
        val ticket1Id = UUID.fromString("85b74cd4-4f7f-4786-8820-8b3bc60a3df0")
        val ticket2Id = UUID.fromString("b0ad5e08-c6b5-4c41-a424-50fb5e911d6e")

        every { checkpointGetBasketTicketsQueryService(any()) } returns CheckpointGetBasketTicketsResponse(
            screening = CheckpointGetBasketTicketsScreeningResponse(
                date = LocalDate.of(2024, 3, 15),
                time = LocalTime.of(19, 30),
                movie = CheckpointGetBasketTicketsMovieResponse(
                    id = UUID.fromString("550e8400-e29b-41d4-a716-************"),
                    code = "AV2024",
                    title = "Avengers: Endgame",
                    duration = 181,
                    format = MovieFormat.FORMAT_2D,
                    rating = MovieRating.PLUS_12,
                    language = MovieLanguage.ST,
                    technology = MovieTechnology.IMAX
                )
            ),
            tickets = listOf(
                CheckpointGetBasketTicketsTicketResponse(
                    id = UUID.fromString("123e4567-e89b-12d3-a456-************"),
                    receiptNumber = "TKT-2024-001234",
                    includes3dGlasses = true,
                    auditoriumCode = "IMAX",
                    seatRow = "F",
                    seatNumber = "12",
                    seatType = SeatType.REGULAR,
                    priceCategoryItemTitle = "Dospely",
                    discountCardType = DiscountCardType.VOUCHER,
                    status = TicketStatus.OK
                ),
                CheckpointGetBasketTicketsTicketResponse(
                    id = UUID.fromString("987fcdeb-51a2-43d1-b789-123456789abc"),
                    receiptNumber = "TKT-2024-001235",
                    includes3dGlasses = true,
                    auditoriumCode = "IMAX",
                    seatRow = "F",
                    seatNumber = "13",
                    seatType = SeatType.REGULAR,
                    priceCategoryItemTitle = "Student",
                    discountCardType = null,
                    status = TicketStatus.OK
                )
            )
        )

        mvc.get("$BASE_PATH/000000001") {
            header(HttpHeaders.ACCEPT, ApiVersion.VERSION_1_JSON)
            header(HttpHeaders.AUTHORIZATION, mockAccessToken(EmployeeRole.CASHIER))
        }.andExpect {
            status { isOk() }
            content { contentType(ApiVersion.VERSION_1_JSON) }
            jsonContent(
                """
                {
                    "screening": {
                        "date": "2024-03-15",
                        "time": "19:30:00",
                        "movie": {
                            "id": "550e8400-e29b-41d4-a716-************",
                            "code": "AV2024",
                            "title": "Avengers: Endgame",
                            "duration": 181,
                            "format": "FORMAT_2D",
                            "rating": "PLUS_12",
                            "language": "ST",
                            "technology": "IMAX"
                        }
                    },
                    "tickets": [
                        {
                            "id": "123e4567-e89b-12d3-a456-************",
                            "receiptNumber": "TKT-2024-001234",
                            "includes3dGlasses": true,
                            "auditoriumCode": "IMAX",
                            "seatRow": "F",
                            "seatNumber": "12",
                            "seatType": "REGULAR",
                            "priceCategoryItemTitle": "Dospely",
                            "discountCardType": "VOUCHER",
                            "status": "OK"
                        },
                        {
                            "id": "987fcdeb-51a2-43d1-b789-123456789abc",
                            "receiptNumber": "TKT-2024-001235",
                            "includes3dGlasses": true,
                            "auditoriumCode": "IMAX",
                            "seatRow": "F",
                            "seatNumber": "13",
                            "seatType": "REGULAR",
                            "priceCategoryItemTitle": "Student",
                            "discountCardType": null,
                            "status": "OK"
                        }
                    ]
                }
                """.trimIndent()
            )
        }

        verify {
            checkpointGetBasketTicketsQueryService(
                CheckpointGetBasketTicketsQuery(
                    receiptNumberOrVariableSymbol = "000000001"
                )
            )
        }
    }

    @Test
    fun `test getBasketTickets - variable symbol with prefix - should serialize and deserialize correctly`() {
        val ticket1Id = UUID.fromString("85b74cd4-4f7f-4786-8820-8b3bc60a3df0")

        every { checkpointGetBasketTicketsQueryService(any()) } returns CheckpointGetBasketTicketsResponse(
            screening = CheckpointGetBasketTicketsScreeningResponse(
                date = LocalDate.of(2024, 3, 15),
                time = LocalTime.of(19, 30),
                movie = CheckpointGetBasketTicketsMovieResponse(
                    id = UUID.fromString("550e8400-e29b-41d4-a716-************"),
                    code = "AV2024",
                    title = "Avengers: Endgame",
                    duration = 181,
                    format = MovieFormat.FORMAT_2D,
                    rating = MovieRating.PLUS_12,
                    language = MovieLanguage.ST,
                    technology = MovieTechnology.IMAX
                )
            ),
            tickets = listOf(
                CheckpointGetBasketTicketsTicketResponse(
                    id = UUID.fromString("123e4567-e89b-12d3-a456-************"),
                    receiptNumber = "TKT-2024-001234",
                    includes3dGlasses = true,
                    auditoriumCode = "IMAX",
                    seatRow = "F",
                    seatNumber = "12",
                    seatType = SeatType.REGULAR,
                    priceCategoryItemTitle = "Dospely",
                    discountCardType = DiscountCardType.VOUCHER,
                    status = TicketStatus.OK
                )
            )
        )

        mvc.get("$BASE_PATH/VS=3000148137") {
            header(HttpHeaders.ACCEPT, ApiVersion.VERSION_1_JSON)
            header(HttpHeaders.AUTHORIZATION, mockAccessToken(EmployeeRole.CASHIER))
        }.andExpect {
            status { isOk() }
            content { contentType(ApiVersion.VERSION_1_JSON) }
            jsonContent(
                """
                {
                    "screening": {
                        "date": "2024-03-15",
                        "time": "19:30:00",
                        "movie": {
                            "id": "550e8400-e29b-41d4-a716-************",
                            "code": "AV2024",
                            "title": "Avengers: Endgame",
                            "duration": 181,
                            "format": "FORMAT_2D",
                            "rating": "PLUS_12",
                            "language": "ST",
                            "technology": "IMAX"
                        }
                    },
                    "tickets": [
                        {
                            "id": "123e4567-e89b-12d3-a456-************",
                            "receiptNumber": "TKT-2024-001234",
                            "includes3dGlasses": true,
                            "auditoriumCode": "IMAX",
                            "seatRow": "F",
                            "seatNumber": "12",
                            "seatType": "REGULAR",
                            "priceCategoryItemTitle": "Dospely",
                            "discountCardType": "VOUCHER",
                            "status": "OK"
                        }
                    ]
                }
                """.trimIndent()
            )
        }

        verify {
            checkpointGetBasketTicketsQueryService(
                CheckpointGetBasketTicketsQuery(
                    receiptNumberOrVariableSymbol = "3000148137"
                )
            )
        }
    }
}

private const val BASE_PATH = "/pos-app/tickets"
