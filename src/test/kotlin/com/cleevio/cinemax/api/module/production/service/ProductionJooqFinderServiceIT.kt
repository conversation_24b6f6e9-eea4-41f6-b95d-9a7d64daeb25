package com.cleevio.cinemax.api.module.production.service

import com.cleevio.cinemax.api.DatabaseCleanup
import com.cleevio.cinemax.api.IntegrationTest
import com.cleevio.cinemax.api.module.production.entity.Production
import com.cleevio.cinemax.api.util.assertDescriptorEquals
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.test.context.jdbc.Sql
import org.springframework.test.context.jdbc.SqlGroup
import kotlin.test.assertEquals

@SqlGroup(
    Sql(
        scripts = [
            "/db/migration/V104__init_production_table_with_data.sql"
        ]
    )
)
class ProductionJooqFinderServiceIT @Autowired constructor(
    private val underTest: ProductionJooqFinderService,
) : IntegrationTest(DatabaseCleanup.AFTER_EACH) {

    @Test
    fun `test findAll - should find all production values`() {
        val productions = underTest.findAll().sortedBy { it.createdAt }

        assertEquals(83, productions.size)
        assertDescriptorEquals(PRODUCTION_1, productions[0])
        assertDescriptorEquals(PRODUCTION_2, productions[1])
        assertDescriptorEquals(PRODUCTION_3, productions[74])
    }
}

private val PRODUCTION_1 = Production(
    originalId = 65,
    code = "01",
    title = "Česká Republika"
)
private val PRODUCTION_2 = Production(
    originalId = 66,
    code = "02",
    title = "Slovenská Republika"
)
private val PRODUCTION_3 = Production(
    originalId = 141,
    code = "a",
    title = "Ukrajina"
)
