package com.cleevio.cinemax.api.module.screening.service

import com.cleevio.cinemax.api.common.config.CinemaxConfigProperties
import com.cleevio.cinemax.api.common.constant.BusinessCountry
import com.cleevio.cinemax.api.common.constant.ExportFormat
import com.cleevio.cinemax.api.common.service.model.ExportResultModel
import com.cleevio.cinemax.api.module.screening.exception.InvalidDateIntervalExportFilterException
import com.cleevio.cinemax.api.module.screening.service.model.DistributorScreeningsXlsxExportMovieRecordModel
import com.cleevio.cinemax.api.module.screening.service.model.DistributorScreeningsXlsxExportRecordModel
import com.cleevio.cinemax.api.module.screening.service.model.DistributorScreeningsXlsxExportScreeningRecordModel
import com.cleevio.cinemax.api.module.screening.service.model.DistributorScreeningsXmlExportRecordModel
import com.cleevio.cinemax.api.module.screening.service.model.DistributorScreeningsXmlExportScreeningRecordModel
import com.cleevio.cinemax.api.module.screening.service.model.DistributorScreeningsXmlExportTicketRecordModel
import com.cleevio.cinemax.api.module.screening.service.query.AdminExportDistributorScreeningsFilter
import com.cleevio.cinemax.api.module.screening.service.query.AdminExportDistributorScreeningsQuery
import io.mockk.every
import io.mockk.mockk
import io.mockk.verifySequence
import org.junit.jupiter.api.assertThrows
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.CsvSource
import java.io.ByteArrayInputStream
import java.math.BigDecimal
import java.time.LocalDate
import java.util.UUID
import kotlin.test.Test
import kotlin.test.assertEquals

class DistributorScreeningsExportServiceTest {

    private val adminXlsxExportDistributorScreeningsQueryService = mockk<AdminXlsxExportDistributorScreeningsQueryService>()
    private val distributorScreeningsXlsxExportResultMapper = mockk<DistributorScreeningsXlsxExportResultMapper>()
    private val adminXmlExportDistributorScreeningsQueryService = mockk<AdminXmlExportDistributorScreeningsQueryService>()
    private val distributorScreeningsXmlExportResultMapper = mockk<DistributorScreeningsXmlExportResultMapper>()
    private val cinemaxConfigProperties = mockk<CinemaxConfigProperties>()

    private val underTest = DistributorScreeningsExportService(
        adminXlsxExportDistributorScreeningsQueryService = adminXlsxExportDistributorScreeningsQueryService,
        distributorScreeningsXlsxExportResultMapper = distributorScreeningsXlsxExportResultMapper,
        adminXmlExportDistributorScreeningsQueryService = adminXmlExportDistributorScreeningsQueryService,
        distributorScreeningsXmlExportResultMapper = distributorScreeningsXmlExportResultMapper,
        cinemaxConfigProperties = cinemaxConfigProperties
    )

    @Test
    fun `test exportDistributorScreenings - valid query with XLSX format - should call related service and mapper`() {
        every { cinemaxConfigProperties.businessCountry } returns BusinessCountry.SK

        val dateFrom = LocalDate.parse("2024-08-01")
        val dateTo = LocalDate.parse("2024-08-07")
        val username = "username"
        val filter = AdminExportDistributorScreeningsFilter(
            dateFrom = dateFrom,
            dateTo = dateTo,
            distributorIds = setOf(UUID.randomUUID())
        )
        val query = AdminExportDistributorScreeningsQuery(
            filter = filter,
            exportFormat = ExportFormat.XLSX,
            username = username
        )

        val exportData = listOf(
            DistributorScreeningsXlsxExportRecordModel(
                distributorTitle = "Distributor",
                movies = listOf(
                    DistributorScreeningsXlsxExportMovieRecordModel(
                        movieTitle = "Test Movie",
                        screenings = listOf(
                            DistributorScreeningsXlsxExportScreeningRecordModel(
                                screeningDate = dateFrom.plusDays(2),
                                ticketsCount = 100,
                                groTicketSales = BigDecimal(1000),
                                netTicketsSales = BigDecimal(850),
                                distributorCommission = 10
                            )
                        )
                    )
                )
            )
        )
        val byteArray = ByteArray(1024)
        val inputStream = ByteArrayInputStream(byteArray)
        val exportResult = ExportResultModel(inputStream, byteArray.size.toLong())

        every { adminXlsxExportDistributorScreeningsQueryService(query) } returns exportData
        every {
            distributorScreeningsXlsxExportResultMapper.mapToExportResultModel(
                any(),
                any(),
                any(),
                any(),
                any()
            )
        } returns exportResult

        assertEquals(exportResult, underTest.exportDistributorScreenings(query))

        verifySequence {
            adminXlsxExportDistributorScreeningsQueryService(query)
            distributorScreeningsXlsxExportResultMapper.mapToExportResultModel(
                dateFrom = dateFrom,
                dateTo = dateTo,
                data = exportData,
                username = username,
                isDBoxExport = false
            )
        }
    }

    @Test
    fun `test exportDistributorScreenings - valid query with XML format - should call related service and mapper`() {
        every { cinemaxConfigProperties.businessCountry } returns BusinessCountry.SK

        val dateFrom = LocalDate.parse("2024-08-01")
        val dateTo = LocalDate.parse("2024-08-07")
        val username = "username"
        val filter = AdminExportDistributorScreeningsFilter(
            dateFrom = dateFrom,
            dateTo = dateTo,
            distributorIds = setOf(UUID.randomUUID())
        )
        val query = AdminExportDistributorScreeningsQuery(
            filter = filter,
            exportFormat = ExportFormat.XML,
            username = username
        )

        val exportData = listOf(
            DistributorScreeningsXmlExportRecordModel(
                auditoriumOriginalCode = 101,
                auditoriumTitle = "Main Auditorium",
                screenings = listOf(
                    DistributorScreeningsXmlExportScreeningRecordModel(
                        screeningOriginalId = 12345,
                        screeningDateTime = dateFrom.atTime(18, 0),
                        movieCode = "MOV345",
                        movieDisfilmCode = "MOV123",
                        movieRawTitle = "Test Movie XML",
                        movieTechnologyTitle = "DCI2D",
                        movieLanguageCode = "SK",
                        groTicketSales = 1000.toBigDecimal(),
                        ticketsCount = 150,
                        ticketBasePrice = 10.toBigDecimal(),
                        tickets = listOf(
                            DistributorScreeningsXmlExportTicketRecordModel(
                                ticketPrice = 10.toBigDecimal(),
                                ticketsCount = 100
                            ),
                            DistributorScreeningsXmlExportTicketRecordModel(
                                ticketPrice = 15.toBigDecimal(),
                                ticketsCount = 50
                            )
                        )
                    )
                )
            )
        )

        val byteArray = ByteArray(1024)
        val inputStream = ByteArrayInputStream(byteArray)
        val exportResult = ExportResultModel(inputStream, byteArray.size.toLong())

        every { adminXmlExportDistributorScreeningsQueryService(query) } returns exportData
        every {
            distributorScreeningsXmlExportResultMapper.mapToExportResultModel(data = exportData)
        } returns exportResult

        assertEquals(exportResult, underTest.exportDistributorScreenings(query))

        verifySequence {
            adminXmlExportDistributorScreeningsQueryService(query)
            distributorScreeningsXmlExportResultMapper.mapToExportResultModel(data = exportData)
        }
    }

    @ParameterizedTest
    @CsvSource(
        "2024-08-01, 2024-08-06",
        "2024-08-02, 2024-05-08"
    )
    fun `test exportDistributorScreenings - invalid date intervals should throw InvalidDateIntervalExportFilterException`(
        dateFrom: String,
        dateTo: String,
    ) {
        every { cinemaxConfigProperties.businessCountry } returns BusinessCountry.SK

        val username = "username"
        val filter = AdminExportDistributorScreeningsFilter(
            dateFrom = LocalDate.parse(dateFrom),
            dateTo = LocalDate.parse(dateTo),
            distributorIds = null
        )
        val query = AdminExportDistributorScreeningsQuery(
            filter = filter,
            exportFormat = ExportFormat.XLSX,
            username = username
        )

        assertThrows<InvalidDateIntervalExportFilterException> { underTest.exportDistributorScreenings(query) }
    }
}
