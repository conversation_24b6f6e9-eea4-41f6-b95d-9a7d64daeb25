package com.cleevio.cinemax.api.module.ticket.controller

import com.cleevio.cinemax.api.ControllerTest
import com.cleevio.cinemax.api.common.constant.ApiVersion
import com.cleevio.cinemax.api.module.employee.constant.EmployeeRole
import com.cleevio.cinemax.api.module.ticket.controller.dto.AdminMoveTicketRequest
import com.cleevio.cinemax.api.module.ticket.service.command.AdminMoveTicketsCommand
import com.cleevio.cinemax.api.util.mockAccessToken
import io.mockk.Runs
import io.mockk.every
import io.mockk.just
import io.mockk.verify
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest
import org.springframework.http.HttpHeaders
import org.springframework.http.MediaType
import org.springframework.test.web.servlet.MockMvc
import org.springframework.test.web.servlet.post
import java.util.UUID

@WebMvcTest(AdminTicketController::class)
class AdminTicketControllerTest @Autowired constructor(
    private val mvc: MockMvc,
) : ControllerTest() {

    @Test
    fun `test moveTickets - should serialize and deserialize correctly`() {
        val ticket1Id = UUID.fromString("85b74cd4-4f7f-4786-8820-8b3bc60a3df0")
        val ticket2Id = UUID.fromString("b0ad5e08-c6b5-4c41-a424-50fb5e911d6e")
        val screening1Id = UUID.fromString("f055fbce-f17a-4bb6-82b9-a1d11ceffa3c")
        val screening2Id = UUID.fromString("102a9977-0fc0-441c-9eab-130b6108ca9b")
        val seatId = UUID.fromString("817d27bf-1a31-41df-809b-bf1d00fbc62c")

        every { ticketService.moveTickets(any()) } just Runs

        mvc.post("$BASE_PATH/move") {
            header(HttpHeaders.ACCEPT, ApiVersion.VERSION_1_JSON)
            header(HttpHeaders.AUTHORIZATION, mockAccessToken(EmployeeRole.MANAGER))
            contentType = MediaType.APPLICATION_JSON
            content = """
                {
                    "tickets": [
                        {
                            "ticketId": "$ticket1Id",
                            "screeningId": "$screening1Id",
                            "seatId": "$seatId"
                        },
                        {
                            "ticketId": "$ticket2Id",
                            "screeningId": "$screening2Id",
                            "seatId": "$seatId"
                        }
                    ]
                }
            """.trimIndent()
        }.andExpect {
            status { isNoContent() }
        }

        verify {
            ticketService.moveTickets(
                AdminMoveTicketsCommand(
                    ticketRequests = listOf(
                        AdminMoveTicketRequest(
                            ticketId = ticket1Id,
                            screeningId = screening1Id,
                            seatId = seatId
                        ),
                        AdminMoveTicketRequest(
                            ticketId = ticket2Id,
                            screeningId = screening2Id,
                            seatId = seatId
                        )
                    )
                )
            )
        }
    }
}

private const val BASE_PATH = "/manager-app/tickets"
