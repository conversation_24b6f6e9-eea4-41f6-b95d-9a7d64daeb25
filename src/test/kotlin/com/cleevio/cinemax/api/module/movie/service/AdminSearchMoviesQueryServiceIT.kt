package com.cleevio.cinemax.api.module.movie.service

import com.cleevio.cinemax.api.DatabaseCleanup
import com.cleevio.cinemax.api.IntegrationTest
import com.cleevio.cinemax.api.module.distributor.service.DistributorService
import com.cleevio.cinemax.api.module.genre.entity.Genre
import com.cleevio.cinemax.api.module.genre.service.GenreJpaFinderService
import com.cleevio.cinemax.api.module.jso.entity.Jso
import com.cleevio.cinemax.api.module.jso.service.JsoJpaFinderService
import com.cleevio.cinemax.api.module.language.entity.Language
import com.cleevio.cinemax.api.module.language.service.LanguageJpaFinderService
import com.cleevio.cinemax.api.module.movie.service.query.AdminSearchMoviesFilter
import com.cleevio.cinemax.api.module.movie.service.query.AdminSearchMoviesQuery
import com.cleevio.cinemax.api.module.moviejso.service.MovieJsoService
import com.cleevio.cinemax.api.module.moviejso.service.command.DeleteAndCreateMovieJsosCommand
import com.cleevio.cinemax.api.module.production.entity.Production
import com.cleevio.cinemax.api.module.production.service.ProductionJpaFinderService
import com.cleevio.cinemax.api.module.rating.entity.Rating
import com.cleevio.cinemax.api.module.rating.service.RatingJpaFinderService
import com.cleevio.cinemax.api.module.screening.constant.MovieFormat
import com.cleevio.cinemax.api.module.screening.constant.MovieLanguage
import com.cleevio.cinemax.api.module.screening.constant.MovieTechnology
import com.cleevio.cinemax.api.module.technology.entity.Technology
import com.cleevio.cinemax.api.module.technology.service.TechnologyJpaFinderService
import com.cleevio.cinemax.api.module.tmslanguage.entity.TmsLanguage
import com.cleevio.cinemax.api.module.tmslanguage.service.TmsLanguageJpaFinderService
import com.cleevio.cinemax.api.util.assertAdminSearchMoviesResponseEquals
import com.cleevio.cinemax.api.util.createDistributor
import com.cleevio.cinemax.api.util.createMovie
import com.cleevio.cinemax.api.util.mapToCreateOrUpdateDistributorCommand
import com.cleevio.cinemax.api.util.mapToCreateOrUpdateMovieCommand
import com.cleevio.cinemax.psql.tables.MovieColumnNames
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.data.domain.PageRequest
import org.springframework.data.domain.Sort
import org.springframework.test.context.jdbc.Sql
import org.springframework.test.context.jdbc.SqlGroup
import java.time.LocalDate
import kotlin.test.assertEquals

@SqlGroup(
    Sql(
        scripts = [
            "/db/migration/V104__init_production_table_with_data.sql",
            "/db/migration/V105__init_genre_table_with_data.sql",
            "/db/migration/V106__init_rating_table_with_data.sql",
            "/db/migration/V107__init_technology_table_with_data.sql",
            "/db/migration/V108__init_language_table_with_data.sql",
            "/db/migration/V109__init_tms_language_table_with_data.sql",
            "/db/migration/V110__init_jso_table_with_data.sql"
        ]
    )
)
class AdminSearchMoviesQueryServiceIT @Autowired constructor(
    private val underTest: AdminSearchMoviesQueryService,
    private val distributorService: DistributorService,
    private val movieService: MovieService,
    private val movieRepository: MovieRepository,
    private val movieJsoService: MovieJsoService,
    private val productionJpaFinderService: ProductionJpaFinderService,
    private val genreJpaFinderService: GenreJpaFinderService,
    private val ratingJpaFinderService: RatingJpaFinderService,
    private val technologyJpaFinderService: TechnologyJpaFinderService,
    private val languageJpaFinderService: LanguageJpaFinderService,
    private val tmsLanguageJpaFinderService: TmsLanguageJpaFinderService,
    private val jsoJpaFinderService: JsoJpaFinderService,
) : IntegrationTest(DatabaseCleanup.AFTER_EACH) {

    @BeforeEach
    fun setUp() {
        val production1 = productionJpaFinderService.findByCode(PRODUCTION_1.code)!!
        val genre1 = genreJpaFinderService.findByCode(GENRE_1.code)!!
        val genre2 = genreJpaFinderService.findByCode(GENRE_2.code)!!
        val rating1 = ratingJpaFinderService.findByCode(RATING_1.code)!!
        val technology1 = technologyJpaFinderService.findByCode(TECHNOLOGY_1.code)!!
        val language1 = languageJpaFinderService.findByCode(LANGUAGE_1.code)!!
        val tmsLanguage1 = tmsLanguageJpaFinderService.findByCode(TMS_LANGUAGE_1.code)!!
        val jso1 = jsoJpaFinderService.findByCode(JSO_1.code)!!
        val jso2 = jsoJpaFinderService.findByCode(JSO_2.code)!!

        setOf(DISTRIBUTOR_1, DISTRIBUTOR_2).forEach {
            distributorService.syncCreateOrUpdateDistributor(mapToCreateOrUpdateDistributorCommand(it))
        }

        movieService.syncCreateOrUpdateMovie(
            mapToCreateOrUpdateMovieCommand(
                MOVIE_1.apply {
                    productionId = production1.id
                    primaryGenreId = genre1.id
                    secondaryGenreId = genre2.id
                    languageId = language1.id
                }
            )
        )
        movieJsoService.deleteAndCreateMovieJsos(
            DeleteAndCreateMovieJsosCommand(
                movieId = MOVIE_1.id,
                jsoIds = setOf(jso1.id)
            )
        )

        movieService.syncCreateOrUpdateMovie(
            mapToCreateOrUpdateMovieCommand(
                MOVIE_2.apply {
                    productionId = production1.id
                    primaryGenreId = genre2.id
                    ratingId = rating1.id
                    technologyId = technology1.id
                    languageId = language1.id
                    tmsLanguageId = tmsLanguage1.id
                }
            )
        )
        movieJsoService.deleteAndCreateMovieJsos(
            DeleteAndCreateMovieJsosCommand(
                movieId = MOVIE_2.id,
                jsoIds = setOf(jso1.id, jso2.id)
            )
        )

        movieService.syncCreateOrUpdateMovie(mapToCreateOrUpdateMovieCommand(MOVIE_3))
        movieService.syncCreateOrUpdateMovie(mapToCreateOrUpdateMovieCommand(MOVIE_4))
        val movie4 = movieRepository.findByOriginalIdAndDeletedAtIsNull(MOVIE_4.originalId!!)!!
        movieRepository.save(
            movie4.apply {
                markDeleted()
            }
        )
        movieService.syncCreateOrUpdateMovie(
            mapToCreateOrUpdateMovieCommand(
                MOVIE_5.apply {
                    productionId = production1.id
                    primaryGenreId = genre2.id
                    secondaryGenreId = genre1.id
                    ratingId = rating1.id
                    technologyId = technology1.id
                    languageId = language1.id
                    tmsLanguageId = tmsLanguage1.id
                }
            )
        )
    }

    @Test
    fun `test AdminSearchMoviesQuery - no filter - should find all movies`() {
        val moviesPage = underTest(
            AdminSearchMoviesQuery(
                pageable = PageRequest.of(
                    0,
                    10,
                    Sort.by(MovieColumnNames.PREMIERE_DATE, MovieColumnNames.TITLE).descending()
                ),
                filter = AdminSearchMoviesFilter()
            )
        )
        assertEquals(4, moviesPage.totalElements)
        assertEquals(4, moviesPage.content.size)

        assertAdminSearchMoviesResponseEquals(
            expected = MOVIE_3,
            expectedJsoTitles = setOf(),
            actual = moviesPage.content[0]
        )

        assertAdminSearchMoviesResponseEquals(
            expected = MOVIE_5,
            expectedProductionTitle = PRODUCTION_1.title,
            expectedPrimaryGenreTitle = GENRE_2.title,
            expectedSecondaryGenreTitle = GENRE_1.title,
            expectedRatingTitle = RATING_1.title,
            expectedTechnologyTitle = TECHNOLOGY_1.title,
            expectedLanguageTitle = LANGUAGE_1.title,
            expectedTmsLanguageTitle = TMS_LANGUAGE_1.title,
            expectedJsoTitles = setOf(),
            actual = moviesPage.content[1]
        )

        assertAdminSearchMoviesResponseEquals(
            expected = MOVIE_2,
            expectedProductionTitle = PRODUCTION_1.title,
            expectedPrimaryGenreTitle = GENRE_2.title,
            expectedRatingTitle = RATING_1.title,
            expectedTechnologyTitle = TECHNOLOGY_1.title,
            expectedLanguageTitle = LANGUAGE_1.title,
            expectedTmsLanguageTitle = TMS_LANGUAGE_1.title,
            expectedJsoTitles = setOf(JSO_1.title, JSO_2.title),
            actual = moviesPage.content[2]
        )

        assertAdminSearchMoviesResponseEquals(
            expected = MOVIE_1,
            expectedProductionTitle = PRODUCTION_1.title,
            expectedPrimaryGenreTitle = GENRE_1.title,
            expectedSecondaryGenreTitle = GENRE_2.title,
            expectedLanguageTitle = LANGUAGE_1.title,
            expectedJsoTitles = setOf(JSO_1.title),
            actual = moviesPage.content[3]
        )
    }

    @Test
    fun `test AdminSearchMoviesQuery - premiereDateFrom filter - should find corresponding movies`() {
        val moviesPage = underTest(
            AdminSearchMoviesQuery(
                pageable = PageRequest.of(
                    0,
                    10,
                    Sort.by(MovieColumnNames.PREMIERE_DATE, MovieColumnNames.TITLE).descending()
                ),
                filter = AdminSearchMoviesFilter(
                    premiereDateFrom = LocalDate.of(2014, 8, 1),
                    movieIds = setOf()
                )
            )
        )
        assertEquals(3, moviesPage.totalElements)
        assertEquals(3, moviesPage.content.size)

        assertAdminSearchMoviesResponseEquals(
            expected = MOVIE_3,
            expectedJsoTitles = setOf(),
            actual = moviesPage.content[0]
        )
        assertAdminSearchMoviesResponseEquals(
            expected = MOVIE_5,
            expectedProductionTitle = PRODUCTION_1.title,
            expectedPrimaryGenreTitle = GENRE_2.title,
            expectedSecondaryGenreTitle = GENRE_1.title,
            expectedRatingTitle = RATING_1.title,
            expectedTechnologyTitle = TECHNOLOGY_1.title,
            expectedLanguageTitle = LANGUAGE_1.title,
            expectedTmsLanguageTitle = TMS_LANGUAGE_1.title,
            expectedJsoTitles = setOf(),
            actual = moviesPage.content[1]
        )
        assertAdminSearchMoviesResponseEquals(
            expected = MOVIE_2,
            expectedProductionTitle = PRODUCTION_1.title,
            expectedPrimaryGenreTitle = GENRE_2.title,
            expectedRatingTitle = RATING_1.title,
            expectedTechnologyTitle = TECHNOLOGY_1.title,
            expectedLanguageTitle = LANGUAGE_1.title,
            expectedTmsLanguageTitle = TMS_LANGUAGE_1.title,
            expectedJsoTitles = setOf(JSO_1.title, JSO_2.title),
            actual = moviesPage.content[2]
        )
    }

    @Test
    fun `test AdminSearchMoviesQuery - premiereDateTo filter - should find no movies`() {
        val moviesPage = underTest(
            AdminSearchMoviesQuery(
                pageable = PageRequest.of(0, 10, Sort.by(MovieColumnNames.PREMIERE_DATE).descending()),
                filter = AdminSearchMoviesFilter(
                    premiereDateTo = LocalDate.of(1970, 1, 1),
                    movieIds = null
                )
            )
        )
        assertEquals(0, moviesPage.totalElements)
        assertEquals(0, moviesPage.content.size)
    }

    @Test
    fun `test AdminSearchMoviesQuery - premiereDateFrom and title filter - should find corresponding movie`() {
        val moviesPage = underTest(
            AdminSearchMoviesQuery(
                pageable = PageRequest.of(0, 10, Sort.by(MovieColumnNames.PREMIERE_DATE).descending()),
                filter = AdminSearchMoviesFilter(
                    premiereDateFrom = LocalDate.of(2002, 1, 1),
                    title = "AR"
                )
            )
        )
        assertEquals(1, moviesPage.totalElements)
        assertEquals(1, moviesPage.content.size)

        assertAdminSearchMoviesResponseEquals(
            expected = MOVIE_2,
            expectedProductionTitle = PRODUCTION_1.title,
            expectedPrimaryGenreTitle = GENRE_2.title,
            expectedRatingTitle = RATING_1.title,
            expectedTechnologyTitle = TECHNOLOGY_1.title,
            expectedLanguageTitle = LANGUAGE_1.title,
            expectedTmsLanguageTitle = TMS_LANGUAGE_1.title,
            expectedJsoTitles = setOf(JSO_1.title, JSO_2.title),
            actual = moviesPage.content[0]
        )
    }

    @Test
    fun `test AdminSearchMoviesQuery - originalTitle filter - should find corresponding movies`() {
        val moviesPage = underTest(
            AdminSearchMoviesQuery(
                pageable = PageRequest.of(0, 10, Sort.by(MovieColumnNames.PREMIERE_DATE).descending()),
                filter = AdminSearchMoviesFilter(
                    originalTitle = "ar"
                )
            )
        )
        assertEquals(2, moviesPage.totalElements)
        assertEquals(2, moviesPage.content.size)

        assertAdminSearchMoviesResponseEquals(
            expected = MOVIE_2,
            expectedProductionTitle = PRODUCTION_1.title,
            expectedPrimaryGenreTitle = GENRE_2.title,
            expectedRatingTitle = RATING_1.title,
            expectedTechnologyTitle = TECHNOLOGY_1.title,
            expectedLanguageTitle = LANGUAGE_1.title,
            expectedTmsLanguageTitle = TMS_LANGUAGE_1.title,
            expectedJsoTitles = setOf(JSO_1.title, JSO_2.title),
            actual = moviesPage.content[0]
        )

        assertAdminSearchMoviesResponseEquals(
            expected = MOVIE_1,
            expectedProductionTitle = PRODUCTION_1.title,
            expectedPrimaryGenreTitle = GENRE_1.title,
            expectedSecondaryGenreTitle = GENRE_2.title,
            expectedLanguageTitle = LANGUAGE_1.title,
            expectedJsoTitles = setOf(JSO_1.title),
            actual = moviesPage.content[1]
        )
    }

    @Test
    fun `test AdminSearchMoviesQuery - originalTitle and distributorTitle filter - should find corresponding movie`() {
        val moviesPage = underTest(
            AdminSearchMoviesQuery(
                pageable = PageRequest.of(0, 10, Sort.by(MovieColumnNames.PREMIERE_DATE).descending()),
                filter = AdminSearchMoviesFilter(
                    originalTitle = "guard",
                    distributorTitle = "movie"
                )
            )
        )
        assertEquals(1, moviesPage.totalElements)
        assertEquals(1, moviesPage.content.size)

        assertAdminSearchMoviesResponseEquals(
            expected = MOVIE_2,
            expectedProductionTitle = PRODUCTION_1.title,
            expectedPrimaryGenreTitle = GENRE_2.title,
            expectedRatingTitle = RATING_1.title,
            expectedTechnologyTitle = TECHNOLOGY_1.title,
            expectedLanguageTitle = LANGUAGE_1.title,
            expectedTmsLanguageTitle = TMS_LANGUAGE_1.title,
            expectedJsoTitles = setOf(JSO_1.title, JSO_2.title),
            actual = moviesPage.content[0]
        )
    }

    @Test
    fun `test AdminSearchMoviesQuery - originalTitle and distributorTitle filter - should find no movies`() {
        val moviesPage = underTest(
            AdminSearchMoviesQuery(
                pageable = PageRequest.of(0, 10, Sort.by(MovieColumnNames.PREMIERE_DATE).descending()),
                filter = AdminSearchMoviesFilter(
                    originalTitle = "ar",
                    distributorTitle = "slovens"
                )
            )
        )
        assertEquals(0, moviesPage.totalElements)
        assertEquals(0, moviesPage.content.size)
    }

    @Test
    fun `test AdminSearchMoviesQuery - distributorTitle filter with unaccented input - should find one movie`() {
        val moviesPage = underTest(
            AdminSearchMoviesQuery(
                pageable = PageRequest.of(0, 10, Sort.by(MovieColumnNames.PREMIERE_DATE).descending()),
                filter = AdminSearchMoviesFilter(
                    distributorTitle = "spolocnost"
                )
            )
        )
        assertEquals(1, moviesPage.totalElements)
        assertEquals(1, moviesPage.content.size)

        assertAdminSearchMoviesResponseEquals(
            expected = MOVIE_3,
            expectedJsoTitles = setOf(),
            actual = moviesPage.content[0]
        )
    }

    @Test
    fun `test AdminSearchMoviesQuery - movieTitle filter with unaccented input - should find one movie`() {
        val moviesPage = underTest(
            AdminSearchMoviesQuery(
                pageable = PageRequest.of(0, 10, Sort.by(MovieColumnNames.PREMIERE_DATE).descending()),
                filter = AdminSearchMoviesFilter(
                    title = "strazci"
                )
            )
        )
        assertEquals(1, moviesPage.totalElements)
        assertEquals(1, moviesPage.content.size)
        assertAdminSearchMoviesResponseEquals(
            expected = MOVIE_5,
            expectedProductionTitle = PRODUCTION_1.title,
            expectedPrimaryGenreTitle = GENRE_2.title,
            expectedSecondaryGenreTitle = GENRE_1.title,
            expectedRatingTitle = RATING_1.title,
            expectedTechnologyTitle = TECHNOLOGY_1.title,
            expectedLanguageTitle = LANGUAGE_1.title,
            expectedTmsLanguageTitle = TMS_LANGUAGE_1.title,
            expectedJsoTitles = setOf(),
            actual = moviesPage.content[0]
        )
    }

    @Test
    fun `test AdminSearchMoviesQuery - movieTitle filter with accented input, DB record is unaccented - should find one movie`() {
        val moviesPage = underTest(
            AdminSearchMoviesQuery(
                pageable = PageRequest.of(0, 10, Sort.by(MovieColumnNames.PREMIERE_DATE).descending()),
                filter = AdminSearchMoviesFilter(
                    title = "gaľáxie"
                )
            )
        )
        assertEquals(1, moviesPage.totalElements)
        assertEquals(1, moviesPage.content.size)
        assertAdminSearchMoviesResponseEquals(
            expected = MOVIE_5,
            expectedProductionTitle = PRODUCTION_1.title,
            expectedPrimaryGenreTitle = GENRE_2.title,
            expectedSecondaryGenreTitle = GENRE_1.title,
            expectedRatingTitle = RATING_1.title,
            expectedTechnologyTitle = TECHNOLOGY_1.title,
            expectedLanguageTitle = LANGUAGE_1.title,
            expectedTmsLanguageTitle = TMS_LANGUAGE_1.title,
            expectedJsoTitles = setOf(),
            actual = moviesPage.content[0]
        )
    }

    @Test
    fun `test AdminSearchMoviesQuery - sort by distributor#title - should sort movies according to distributor title`() {
        val moviesPage = underTest(
            AdminSearchMoviesQuery(
                pageable = PageRequest.of(
                    0,
                    10,
                    Sort.by("distributor.title", "movie.title").descending()
                ),
                filter = AdminSearchMoviesFilter()
            )
        )
        assertEquals(4, moviesPage.totalElements)
        assertEquals(4, moviesPage.content.size)

        assertAdminSearchMoviesResponseEquals(
            expected = MOVIE_3,
            expectedJsoTitles = setOf(),
            actual = moviesPage.content[0]
        )
        assertAdminSearchMoviesResponseEquals(
            expected = MOVIE_5,
            expectedProductionTitle = PRODUCTION_1.title,
            expectedPrimaryGenreTitle = GENRE_2.title,
            expectedSecondaryGenreTitle = GENRE_1.title,
            expectedRatingTitle = RATING_1.title,
            expectedTechnologyTitle = TECHNOLOGY_1.title,
            expectedLanguageTitle = LANGUAGE_1.title,
            expectedTmsLanguageTitle = TMS_LANGUAGE_1.title,
            expectedJsoTitles = setOf(),
            actual = moviesPage.content[1]
        )
        assertAdminSearchMoviesResponseEquals(
            expected = MOVIE_1,
            expectedProductionTitle = PRODUCTION_1.title,
            expectedPrimaryGenreTitle = GENRE_1.title,
            expectedSecondaryGenreTitle = GENRE_2.title,
            expectedLanguageTitle = LANGUAGE_1.title,
            expectedJsoTitles = setOf(JSO_1.title),
            actual = moviesPage.content[2]
        )
        assertAdminSearchMoviesResponseEquals(
            expected = MOVIE_2,
            expectedProductionTitle = PRODUCTION_1.title,
            expectedPrimaryGenreTitle = GENRE_2.title,
            expectedRatingTitle = RATING_1.title,
            expectedTechnologyTitle = TECHNOLOGY_1.title,
            expectedLanguageTitle = LANGUAGE_1.title,
            expectedTmsLanguageTitle = TMS_LANGUAGE_1.title,
            expectedJsoTitles = setOf(JSO_1.title, JSO_2.title),
            actual = moviesPage.content[3]
        )
    }

    @Test
    fun `test AdminSearchMoviesQuery - sort by genre#title - should sort movies by primary and secondary genre title`() {
        val moviesPage = underTest(
            AdminSearchMoviesQuery(
                pageable = PageRequest.of(
                    0,
                    10,
                    Sort.by("genre.title", "movie.title").ascending()
                ),
                filter = AdminSearchMoviesFilter()
            )
        )
        assertEquals(4, moviesPage.totalElements)
        assertEquals(4, moviesPage.content.size)

        assertAdminSearchMoviesResponseEquals(
            expected = MOVIE_1,
            expectedProductionTitle = PRODUCTION_1.title,
            expectedPrimaryGenreTitle = GENRE_1.title,
            expectedSecondaryGenreTitle = GENRE_2.title,
            expectedLanguageTitle = LANGUAGE_1.title,
            expectedJsoTitles = setOf(JSO_1.title),
            actual = moviesPage.content[0]
        )
        assertAdminSearchMoviesResponseEquals(
            expected = MOVIE_5,
            expectedProductionTitle = PRODUCTION_1.title,
            expectedPrimaryGenreTitle = GENRE_2.title,
            expectedSecondaryGenreTitle = GENRE_1.title,
            expectedRatingTitle = RATING_1.title,
            expectedTechnologyTitle = TECHNOLOGY_1.title,
            expectedLanguageTitle = LANGUAGE_1.title,
            expectedTmsLanguageTitle = TMS_LANGUAGE_1.title,
            expectedJsoTitles = setOf(),
            actual = moviesPage.content[1]
        )
        assertAdminSearchMoviesResponseEquals(
            expected = MOVIE_2,
            expectedProductionTitle = PRODUCTION_1.title,
            expectedPrimaryGenreTitle = GENRE_2.title,
            expectedRatingTitle = RATING_1.title,
            expectedTechnologyTitle = TECHNOLOGY_1.title,
            expectedLanguageTitle = LANGUAGE_1.title,
            expectedTmsLanguageTitle = TMS_LANGUAGE_1.title,
            expectedJsoTitles = setOf(JSO_1.title, JSO_2.title),
            actual = moviesPage.content[2]
        )
        assertAdminSearchMoviesResponseEquals(
            expected = MOVIE_3,
            expectedJsoTitles = setOf(),
            actual = moviesPage.content[3]
        )
    }

    @Test
    fun `test AdminSearchMoviesQuery - sort by primaryGenre#title column - should sort movies by primary genre title`() {
        val moviesPage = underTest(
            AdminSearchMoviesQuery(
                pageable = PageRequest.of(
                    0,
                    10,
                    Sort.by(Sort.Order.asc("primaryGenre.title"), Sort.Order.asc("movie.title"))
                ),
                filter = AdminSearchMoviesFilter()
            )
        )
        assertEquals(4, moviesPage.totalElements)
        assertEquals(4, moviesPage.content.size)

        assertAdminSearchMoviesResponseEquals(
            expected = MOVIE_1,
            expectedProductionTitle = PRODUCTION_1.title,
            expectedPrimaryGenreTitle = GENRE_1.title,
            expectedSecondaryGenreTitle = GENRE_2.title,
            expectedLanguageTitle = LANGUAGE_1.title,
            expectedJsoTitles = setOf(JSO_1.title),
            actual = moviesPage.content[0]
        )
        assertAdminSearchMoviesResponseEquals(
            expected = MOVIE_2,
            expectedProductionTitle = PRODUCTION_1.title,
            expectedPrimaryGenreTitle = GENRE_2.title,
            expectedRatingTitle = RATING_1.title,
            expectedTechnologyTitle = TECHNOLOGY_1.title,
            expectedLanguageTitle = LANGUAGE_1.title,
            expectedTmsLanguageTitle = TMS_LANGUAGE_1.title,
            expectedJsoTitles = setOf(JSO_1.title, JSO_2.title),
            actual = moviesPage.content[1]
        )
        assertAdminSearchMoviesResponseEquals(
            expected = MOVIE_5,
            expectedProductionTitle = PRODUCTION_1.title,
            expectedPrimaryGenreTitle = GENRE_2.title,
            expectedSecondaryGenreTitle = GENRE_1.title,
            expectedRatingTitle = RATING_1.title,
            expectedTechnologyTitle = TECHNOLOGY_1.title,
            expectedLanguageTitle = LANGUAGE_1.title,
            expectedTmsLanguageTitle = TMS_LANGUAGE_1.title,
            expectedJsoTitles = setOf(),
            actual = moviesPage.content[2]
        )
        assertAdminSearchMoviesResponseEquals(
            expected = MOVIE_3,
            expectedJsoTitles = setOf(),
            actual = moviesPage.content[3]
        )
    }

    @Test
    fun `test AdminSearchMoviesQuery - sort by tmsLanguage#title column - should sort movies by TMS language title`() {
        val moviesPage = underTest(
            AdminSearchMoviesQuery(
                pageable = PageRequest.of(
                    0,
                    10,
                    Sort.by("tmsLanguage.title", "movie.title").ascending()
                ),
                filter = AdminSearchMoviesFilter()
            )
        )
        assertEquals(4, moviesPage.totalElements)
        assertEquals(4, moviesPage.content.size)

        assertAdminSearchMoviesResponseEquals(
            expected = MOVIE_2,
            expectedProductionTitle = PRODUCTION_1.title,
            expectedPrimaryGenreTitle = GENRE_2.title,
            expectedRatingTitle = RATING_1.title,
            expectedTechnologyTitle = TECHNOLOGY_1.title,
            expectedLanguageTitle = LANGUAGE_1.title,
            expectedTmsLanguageTitle = TMS_LANGUAGE_1.title,
            expectedJsoTitles = setOf(JSO_1.title, JSO_2.title),
            actual = moviesPage.content[0]
        )
        assertAdminSearchMoviesResponseEquals(
            expected = MOVIE_5,
            expectedProductionTitle = PRODUCTION_1.title,
            expectedPrimaryGenreTitle = GENRE_2.title,
            expectedSecondaryGenreTitle = GENRE_1.title,
            expectedRatingTitle = RATING_1.title,
            expectedTechnologyTitle = TECHNOLOGY_1.title,
            expectedLanguageTitle = LANGUAGE_1.title,
            expectedTmsLanguageTitle = TMS_LANGUAGE_1.title,
            expectedJsoTitles = setOf(),
            actual = moviesPage.content[1]
        )
        assertAdminSearchMoviesResponseEquals(
            expected = MOVIE_3,
            expectedJsoTitles = setOf(),
            actual = moviesPage.content[2]
        )
        assertAdminSearchMoviesResponseEquals(
            expected = MOVIE_1,
            expectedProductionTitle = PRODUCTION_1.title,
            expectedPrimaryGenreTitle = GENRE_1.title,
            expectedSecondaryGenreTitle = GENRE_2.title,
            expectedLanguageTitle = LANGUAGE_1.title,
            expectedJsoTitles = setOf(JSO_1.title),
            actual = moviesPage.content[3]
        )
    }

    @Test
    fun `test AdminSearchMoviesQuery - sort by tmsLanguage#title, movie#title with different orders - should sort correctly`() {
        val moviesPage = underTest(
            AdminSearchMoviesQuery(
                pageable = PageRequest.of(
                    0,
                    10,
                    Sort.by(Sort.Order.asc("tmsLanguage.title"), Sort.Order.desc("movie.title"))
                ),
                filter = AdminSearchMoviesFilter()
            )
        )
        assertEquals(4, moviesPage.totalElements)
        assertEquals(4, moviesPage.content.size)

        assertAdminSearchMoviesResponseEquals(
            expected = MOVIE_5,
            expectedProductionTitle = PRODUCTION_1.title,
            expectedPrimaryGenreTitle = GENRE_2.title,
            expectedSecondaryGenreTitle = GENRE_1.title,
            expectedRatingTitle = RATING_1.title,
            expectedTechnologyTitle = TECHNOLOGY_1.title,
            expectedLanguageTitle = LANGUAGE_1.title,
            expectedTmsLanguageTitle = TMS_LANGUAGE_1.title,
            expectedJsoTitles = setOf(),
            actual = moviesPage.content[0]
        )
        assertAdminSearchMoviesResponseEquals(
            expected = MOVIE_2,
            expectedProductionTitle = PRODUCTION_1.title,
            expectedPrimaryGenreTitle = GENRE_2.title,
            expectedRatingTitle = RATING_1.title,
            expectedTechnologyTitle = TECHNOLOGY_1.title,
            expectedLanguageTitle = LANGUAGE_1.title,
            expectedTmsLanguageTitle = TMS_LANGUAGE_1.title,
            expectedJsoTitles = setOf(JSO_1.title, JSO_2.title),
            actual = moviesPage.content[1]
        )
        assertAdminSearchMoviesResponseEquals(
            expected = MOVIE_1,
            expectedProductionTitle = PRODUCTION_1.title,
            expectedPrimaryGenreTitle = GENRE_1.title,
            expectedSecondaryGenreTitle = GENRE_2.title,
            expectedLanguageTitle = LANGUAGE_1.title,
            expectedJsoTitles = setOf(JSO_1.title),
            actual = moviesPage.content[2]
        )
        assertAdminSearchMoviesResponseEquals(
            expected = MOVIE_3,
            expectedJsoTitles = setOf(),
            actual = moviesPage.content[3]
        )
    }

    @Test
    fun `test AdminSearchMoviesQuery - movieIds filter - should find corresponding movies`() {
        val moviesPage = underTest(
            AdminSearchMoviesQuery(
                pageable = PageRequest.of(0, 10, Sort.by(MovieColumnNames.PREMIERE_DATE).descending()),
                filter = AdminSearchMoviesFilter(
                    movieIds = setOf(MOVIE_5.id, MOVIE_3.id)
                )
            )
        )
        assertEquals(2, moviesPage.totalElements)
        assertEquals(2, moviesPage.content.size)

        assertAdminSearchMoviesResponseEquals(
            expected = MOVIE_3,
            expectedJsoTitles = setOf(),
            actual = moviesPage.content[0]
        )
        assertAdminSearchMoviesResponseEquals(
            expected = MOVIE_5,
            expectedProductionTitle = PRODUCTION_1.title,
            expectedPrimaryGenreTitle = GENRE_2.title,
            expectedSecondaryGenreTitle = GENRE_1.title,
            expectedRatingTitle = RATING_1.title,
            expectedTechnologyTitle = TECHNOLOGY_1.title,
            expectedLanguageTitle = LANGUAGE_1.title,
            expectedTmsLanguageTitle = TMS_LANGUAGE_1.title,
            expectedJsoTitles = setOf(),
            actual = moviesPage.content[1]
        )
    }
}

private val DISTRIBUTOR_1 = createDistributor()
private val DISTRIBUTOR_2 = createDistributor(
    originalId = 2,
    title = "Vychodoslovenská distribučná spoločnosť"
)
private val MOVIE_1 = createMovie(
    originalId = 1,
    title = "Star Wars: Episode I – The Phantom Menace",
    rawTitle = "Star Wars: Episode I – The Phantom Menace",
    releaseYear = 2001,
    premiereDate = LocalDate.of(2001, 4, 30),
    distributorId = DISTRIBUTOR_1.id
)
private val MOVIE_2 = createMovie(
    originalId = 2,
    title = "Guardians of the Galaxy",
    rawTitle = "Guardians of the Galaxy",
    code = "345678",
    releaseYear = 2014,
    premiereDate = LocalDate.of(2014, 8, 1),
    parsedFormat = MovieFormat.FORMAT_3D,
    parsedLanguage = MovieLanguage.ORIG,
    distributorId = DISTRIBUTOR_1.id
)
private val MOVIE_3 = createMovie(
    originalId = 3,
    title = "Oppenheimer",
    rawTitle = "Oppenheimer",
    code = "901234",
    premiereDate = LocalDate.of(2023, 11, 30),
    parsedTechnology = MovieTechnology.IMAX,
    distributorId = DISTRIBUTOR_2.id
)
private val MOVIE_4 = createMovie(
    originalId = 4,
    title = "Twelve Angry Men",
    rawTitle = "Twelve Angry Men",
    code = "567890",
    premiereDate = LocalDate.of(1952, 11, 30),
    distributorId = DISTRIBUTOR_2.id
)
private val MOVIE_5 = createMovie(
    originalId = 5,
    title = "Strážci galaxie",
    rawTitle = "Strážci galaxie",
    code = "783516",
    releaseYear = 2014,
    premiereDate = LocalDate.of(2014, 8, 1),
    parsedFormat = MovieFormat.FORMAT_3D,
    parsedLanguage = MovieLanguage.ORIG,
    distributorId = DISTRIBUTOR_1.id
)
private val PRODUCTION_1 = Production(
    originalId = 98,
    code = "56",
    title = "USA"
)
private val GENRE_1 = Genre(
    originalId = 2,
    code = "1",
    title = "Dráma"
)
private val GENRE_2 = Genre(
    originalId = 5,
    code = "4",
    title = "Sci-Fi"
)
private val RATING_1 = Rating(
    originalId = 6,
    code = "12",
    title = "12"
)
private val TECHNOLOGY_1 = Technology(
    originalId = 12,
    code = "05",
    title = "IMAX"
)
private val LANGUAGE_1 = Language(
    originalId = 1,
    code = "CZ",
    title = "česká verze"
)
private val TMS_LANGUAGE_1 = TmsLanguage(
    originalId = 15,
    code = "C",
    title = "český dabing"
)
private val JSO_1 = Jso(
    originalId = 3,
    code = "S",
    title = "Strach"
)
private val JSO_2 = Jso(
    originalId = 6,
    code = "V",
    title = "Vulgarizmy"
)
