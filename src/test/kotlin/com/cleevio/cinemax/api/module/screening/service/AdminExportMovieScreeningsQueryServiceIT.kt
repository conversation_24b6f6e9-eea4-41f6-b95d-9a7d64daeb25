package com.cleevio.cinemax.api.module.screening.service

import com.cleevio.cinemax.api.DatabaseCleanup
import com.cleevio.cinemax.api.INTEGRATION_TEST_DATE_TIME
import com.cleevio.cinemax.api.IntegrationTest
import com.cleevio.cinemax.api.common.constant.ExportFormat
import com.cleevio.cinemax.api.common.constant.PaymentType
import com.cleevio.cinemax.api.common.util.isEqualTo
import com.cleevio.cinemax.api.common.util.toCents
import com.cleevio.cinemax.api.module.auditorium.service.AuditoriumRepository
import com.cleevio.cinemax.api.module.auditoriumlayout.service.AuditoriumLayoutRepository
import com.cleevio.cinemax.api.module.basket.constant.BasketState
import com.cleevio.cinemax.api.module.basket.service.BasketRepository
import com.cleevio.cinemax.api.module.basketitem.constant.BasketItemType
import com.cleevio.cinemax.api.module.basketitem.service.BasketItemRepository
import com.cleevio.cinemax.api.module.branch.service.BranchRepository
import com.cleevio.cinemax.api.module.distributor.service.DistributorRepository
import com.cleevio.cinemax.api.module.language.entity.Language
import com.cleevio.cinemax.api.module.language.service.LanguageRepository
import com.cleevio.cinemax.api.module.movie.service.MovieRepository
import com.cleevio.cinemax.api.module.posconfiguration.constant.PosConfigurationType
import com.cleevio.cinemax.api.module.posconfiguration.service.PosConfigurationRepository
import com.cleevio.cinemax.api.module.pricecategory.service.PriceCategoryRepository
import com.cleevio.cinemax.api.module.pricecategoryitem.constant.PriceCategoryItemNumber
import com.cleevio.cinemax.api.module.pricecategoryitem.service.PriceCategoryItemRepository
import com.cleevio.cinemax.api.module.rating.entity.Rating
import com.cleevio.cinemax.api.module.rating.service.RatingRepository
import com.cleevio.cinemax.api.module.reservation.constant.ReservationState
import com.cleevio.cinemax.api.module.reservation.service.ReservationRepository
import com.cleevio.cinemax.api.module.screening.constant.ScreeningState
import com.cleevio.cinemax.api.module.screening.service.query.AdminExportMovieScreeningsFilter
import com.cleevio.cinemax.api.module.screening.service.query.AdminExportMovieScreeningsQuery
import com.cleevio.cinemax.api.module.screeningfee.service.ScreeningFeeRepository
import com.cleevio.cinemax.api.module.screeningtype.service.ScreeningTypeRepository
import com.cleevio.cinemax.api.module.screeningtypes.service.ScreeningTypesRepository
import com.cleevio.cinemax.api.module.seat.constant.SeatType
import com.cleevio.cinemax.api.module.seat.service.SeatRepository
import com.cleevio.cinemax.api.module.technology.entity.Technology
import com.cleevio.cinemax.api.module.technology.service.TechnologyRepository
import com.cleevio.cinemax.api.module.ticket.service.TicketRepository
import com.cleevio.cinemax.api.module.ticketdiscount.constant.TicketDiscountType
import com.cleevio.cinemax.api.module.ticketdiscount.service.TicketDiscountRepository
import com.cleevio.cinemax.api.module.ticketprice.constant.SeatSurchargeType
import com.cleevio.cinemax.api.module.ticketprice.service.TicketPriceRepository
import com.cleevio.cinemax.api.module.tmslanguage.entity.TmsLanguage
import com.cleevio.cinemax.api.module.tmslanguage.service.TmsLanguageRepository
import com.cleevio.cinemax.api.util.copyBasketItem
import com.cleevio.cinemax.api.util.createAuditorium
import com.cleevio.cinemax.api.util.createAuditoriumLayout
import com.cleevio.cinemax.api.util.createBasket
import com.cleevio.cinemax.api.util.createBasketItem
import com.cleevio.cinemax.api.util.createBranch
import com.cleevio.cinemax.api.util.createDistributor
import com.cleevio.cinemax.api.util.createMovie
import com.cleevio.cinemax.api.util.createPosConfiguration
import com.cleevio.cinemax.api.util.createPriceCategory
import com.cleevio.cinemax.api.util.createPriceCategoryItem
import com.cleevio.cinemax.api.util.createReservation
import com.cleevio.cinemax.api.util.createScreening
import com.cleevio.cinemax.api.util.createScreeningFee
import com.cleevio.cinemax.api.util.createScreeningType
import com.cleevio.cinemax.api.util.createScreeningTypes
import com.cleevio.cinemax.api.util.createSeat
import com.cleevio.cinemax.api.util.createTicket
import com.cleevio.cinemax.api.util.createTicketDiscount
import com.cleevio.cinemax.api.util.createTicketPrice
import org.junit.jupiter.api.BeforeAll
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import java.math.BigDecimal
import java.time.LocalDate
import java.time.LocalTime
import kotlin.test.assertEquals
import kotlin.test.assertTrue

class AdminExportMovieScreeningsQueryServiceIT @Autowired constructor(
    private val underTest: AdminExportMovieScreeningsQueryService,
    private val auditoriumRepository: AuditoriumRepository,
    private val auditoriumLayoutRepository: AuditoriumLayoutRepository,
    private val ratingRepository: RatingRepository,
    private val distributorRepository: DistributorRepository,
    private val technologyRepository: TechnologyRepository,
    private val tmsLanguageRepository: TmsLanguageRepository,
    private val languageRepository: LanguageRepository,
    private val priceCategoryRepository: PriceCategoryRepository,
    private val priceCategoryItemRepository: PriceCategoryItemRepository,
    private val screeningTypeRepository: ScreeningTypeRepository,
    private val screeningRepository: ScreeningRepository,
    private val screeningTypesRepository: ScreeningTypesRepository,
    private val screeningFeeRepository: ScreeningFeeRepository,
    private val seatRepository: SeatRepository,
    private val reservationRepository: ReservationRepository,
    private val ticketRepository: TicketRepository,
    private val ticketPriceRepository: TicketPriceRepository,
    private val movieRepository: MovieRepository,
    private val basketRepository: BasketRepository,
    private val basketItemRepository: BasketItemRepository,
    private val posConfigurationRepository: PosConfigurationRepository,
    private val ticketDiscountRepository: TicketDiscountRepository,
    private val branchRepository: BranchRepository,
) : IntegrationTest(DatabaseCleanup.AFTER_ALL) {

    @BeforeAll
    fun setUp() {
        branchRepository.saveAll(setOf(BRANCH_1, BRANCH_2))
        auditoriumRepository.saveAll(setOf(AUDITORIUM_1, AUDITORIUM_2))
        auditoriumLayoutRepository.saveAll(setOf(AUDITORIUM_LAYOUT_1, AUDITORIUM_LAYOUT_2))
        distributorRepository.saveAll(setOf(DISTRIBUTOR_1, DISTRIBUTOR_2, DISTRIBUTOR_3))
        ratingRepository.saveAll(setOf(RATING_1, RATING_2))
        technologyRepository.saveAll(setOf(TECHNOLOGY_1, TECHNOLOGY_2))
        languageRepository.saveAll(setOf(LANGUAGE_1, LANGUAGE_2))
        tmsLanguageRepository.saveAll(setOf(TMS_LANGUAGE_1, TMS_LANGUAGE_2))
        movieRepository.saveAll(setOf(MOVIE_1, MOVIE_2, MOVIE_3, MOVIE_4, MOVIE_5))
        priceCategoryRepository.save(PRICE_CATEGORY_1)
        screeningRepository.saveAll(
            setOf(
                SCREENING_1,
                SCREENING_2_DELETED,
                SCREENING_3_CANCELLED,
                SCREENING_4_STOPPED,
                SCREENING_5,
                SCREENING_6,
                SCREENING_7
            )
        )
        screeningFeeRepository.saveAll(setOf(SCREENING_FEE_1, SCREENING_FEE_5, SCREENING_FEE_6))
        priceCategoryItemRepository.saveAll(setOf(PRICE_CATEGORY_ITEM_1, PRICE_CATEGORY_ITEM_2))
        screeningTypeRepository.saveAll(setOf(SCREENING_TYPE_1, SCREENING_TYPE_2))
        screeningTypesRepository.saveAll(setOf(SCREENING_TYPES_1, SCREENING_TYPES_2))
        seatRepository.saveAll(setOf(SEAT_1, SEAT_2, SEAT_3, SEAT_4, SEAT_5))
        reservationRepository.saveAll(
            setOf(
                RESERVATION_1,
                RESERVATION_2,
                RESERVATION_3,
                RESERVATION_4,
                RESERVATION_5,
                RESERVATION_6
            )
        )
        ticketDiscountRepository.saveAll(setOf(TICKET_DISCOUNT_1, TICKET_DISCOUNT_2, TICKET_DISCOUNT_3))
        ticketPriceRepository.saveAll(
            setOf(
                TICKET_PRICE_1,
                TICKET_PRICE_2,
                TICKET_PRICE_3,
                TICKET_PRICE_4,
                TICKET_PRICE_5,
                TICKET_PRICE_6
            )
        )
        ticketRepository.saveAll(setOf(TICKET_1, TICKET_2, TICKET_3, TICKET_4, TICKET_5, TICKET_6))
        posConfigurationRepository.saveAll(setOf(POS_CONFIGURATION_1, POS_CONFIGURATION_2))
        basketRepository.saveAll(setOf(BASKET_1_POS_1, BASKET_1_POS_2))
        basketItemRepository.saveAll(
            setOf(
                BASKET_ITEM_TICKET_1_BASKET_1,
                BASKET_ITEM_TICKET_2_BASKET_1,
                BASKET_ITEM_TICKET_3_BASKET_1,
                BASKET_ITEM_TICKET_4_BASKET_1,
                BASKET_ITEM_TICKET_5_BASKET_1,
                BASKET_ITEM_TICKET_6_BASKET_2
            )
        )
    }

    @Test
    fun `test AdminExportMovieScreeningsQuery - no filter - should return all movies with correct calculations`() {
        val response = underTest(
            AdminExportMovieScreeningsQuery(
                filter = AdminExportMovieScreeningsFilter(
                    screeningDateTimeFrom = null,
                    screeningDateTimeTo = null
                ),
                exportFormat = ExportFormat.XLSX,
                username = USERNAME
            )
        )

        val responseMovies = response.movies
        assertEquals(4, responseMovies.size)

        responseMovies.sortedBy { it.movieTitle }[0].run {
            // movie with screenings with no tickets
            assertEquals("Lego Movie IMAX 2D (ST)", this.movieTitle)
            assertEquals(LocalDate.parse("2024-09-05"), this.firstScreeningDate)
            assertEquals(LocalDate.parse("2024-09-05"), this.lastScreeningDate)
            assertTrue(0.toBigDecimal() isEqualTo this.totalGbo)
            assertEquals(1, this.screeningsCount)
            assertTrue(0.0.toBigDecimal().toCents() isEqualTo this.proFee.toCents())
            assertTrue(0.0.toBigDecimal().toCents() isEqualTo this.filmFondFee.toCents())
            assertEquals(0, this.ticketsCount)
            assertEquals(0, this.discountedTicketsCount)
            assertEquals(0, this.freeTicketsCount)
            assertTrue(0.0.toBigDecimal().toCents() isEqualTo this.netGbo.toCents())
            assertTrue(0.0.toBigDecimal().toCents() isEqualTo this.netBo.toCents())
            assertEquals(30, this.distributorCommission)
            assertTrue(0.0.toBigDecimal().toCents() isEqualTo this.distributorFee.toCents())
        }

        responseMovies.sortedBy { it.movieTitle }[1].run {
            assertEquals("Lego Movie IMAX 3D (SD)", this.movieTitle)
            assertEquals(LocalDate.parse("2024-09-04"), this.firstScreeningDate)
            assertEquals(LocalDate.parse("2024-09-04"), this.lastScreeningDate)
            assertTrue(15.toBigDecimal() isEqualTo this.totalGbo)
            assertEquals(1, this.screeningsCount)
            assertTrue(0.24.toBigDecimal().toCents() isEqualTo this.proFee.toCents())
            assertTrue(0.37.toBigDecimal().toCents() isEqualTo this.filmFondFee.toCents())
            assertEquals(1, this.ticketsCount)
            assertEquals(0, this.discountedTicketsCount)
            assertEquals(0, this.freeTicketsCount)
            assertTrue(14.25.toBigDecimal().toCents() isEqualTo this.netGbo.toCents())
            assertTrue(11.59.toBigDecimal().toCents() isEqualTo this.netBo.toCents())
            assertEquals(30, this.distributorCommission)
            assertTrue(3.48.toBigDecimal().toCents() isEqualTo this.distributorFee.toCents())
        }

        responseMovies.sortedBy { it.movieTitle }[2].run {
            assertEquals("Matrix 2D (ST)", this.movieTitle)
            assertEquals(LocalDate.parse("2024-08-26"), this.firstScreeningDate)
            assertEquals(LocalDate.parse("2024-08-30"), this.lastScreeningDate)
            assertTrue(27.toBigDecimal().toCents() isEqualTo this.totalGbo.toCents())
            assertEquals(4, this.screeningsCount)
            assertTrue(0.66.toBigDecimal().toCents() isEqualTo this.proFee.toCents())
            assertTrue(0.44.toBigDecimal().toCents() isEqualTo this.filmFondFee.toCents())
            assertEquals(4, this.ticketsCount)
            assertEquals(4, this.discountedTicketsCount)
            assertEquals(0, this.freeTicketsCount)
            assertTrue(25.65.toBigDecimal().toCents() isEqualTo this.netGbo.toCents())
            assertTrue(20.85.toBigDecimal().toCents() isEqualTo this.netBo.toCents())
            assertEquals(70, this.distributorCommission)
            assertTrue(4.17.toBigDecimal().toCents() isEqualTo this.distributorFee.toCents())
        }

        responseMovies.sortedBy { it.movieTitle }[3].run {
            assertEquals("Oppenheimer IMAX 2D (ST)", this.movieTitle)
            assertEquals(LocalDate.parse("2024-08-30"), this.firstScreeningDate)
            assertEquals(LocalDate.parse("2024-08-30"), this.lastScreeningDate)
            assertTrue(2.toBigDecimal() isEqualTo this.totalGbo)
            assertEquals(1, this.screeningsCount)
            assertTrue(0.08.toBigDecimal().toCents() isEqualTo this.proFee.toCents())
            assertTrue(0.16.toBigDecimal().toCents() isEqualTo this.filmFondFee.toCents())
            assertEquals(1, this.ticketsCount)
            assertEquals(0, this.discountedTicketsCount)
            assertEquals(0, this.freeTicketsCount)
            assertTrue(1.70.toBigDecimal().toCents() isEqualTo this.netGbo.toCents())
            assertTrue(1.38.toBigDecimal().toCents() isEqualTo this.netBo.toCents())
            assertEquals(60, this.distributorCommission)
            assertTrue(0.83.toBigDecimal().toCents() isEqualTo this.distributorFee.toCents())
        }

        val responseSummary = response.summary
        responseSummary.run {
            assertTrue(44.toBigDecimal() isEqualTo this.totalGbo)
            assertEquals(7, this.screeningsCount)
            assertTrue(0.98.toBigDecimal().toCents() isEqualTo this.proFee.toCents())
            assertTrue(0.97.toBigDecimal().toCents() isEqualTo this.filmFondFee.toCents())
            assertEquals(6, this.ticketsCount)
            assertEquals(4, this.discountedTicketsCount)
            assertEquals(0, this.freeTicketsCount)
            assertTrue(41.60.toBigDecimal().toCents() isEqualTo this.netGbo.toCents())
            assertTrue(33.82.toBigDecimal().toCents() isEqualTo this.netBo.toCents())
            assertTrue(8.48.toBigDecimal().toCents() isEqualTo this.distributorFee.toCents())
            assertTrue(1.94.toBigDecimal().toCents() isEqualTo this.distributorFeeVat.toCents())
            assertTrue(10.42.toBigDecimal().toCents() isEqualTo this.distributorFeeWithVat.toCents())
        }
    }

    @Test
    fun `test AdminExportMovieScreeningsQuery - screeningDateTimeFrom and screeningDateTimeTo filters - should filter correct movies`() {
        val response = underTest(
            AdminExportMovieScreeningsQuery(
                filter = AdminExportMovieScreeningsFilter(
                    screeningDateTimeFrom = LocalDate.parse("2024-08-26").atStartOfDay(),
                    screeningDateTimeTo = LocalDate.parse("2024-08-28").atStartOfDay()
                ),
                exportFormat = ExportFormat.XLSX,
                username = USERNAME
            )
        )

        val responseMovies = response.movies
        assertEquals(1, responseMovies.size)

        responseMovies.sortedBy { it.movieTitle }[0].run {
            assertEquals("Matrix 2D (ST)", this.movieTitle)
            assertEquals(LocalDate.parse("2024-08-26"), this.firstScreeningDate)
            assertEquals(LocalDate.parse("2024-08-26"), this.lastScreeningDate)
            assertTrue(27.toBigDecimal().toCents() isEqualTo this.totalGbo.toCents())
            assertEquals(1, this.screeningsCount)
            assertTrue(0.66.toBigDecimal().toCents() isEqualTo this.proFee.toCents())
            assertTrue(0.44.toBigDecimal().toCents() isEqualTo this.filmFondFee.toCents())
            assertEquals(4, this.ticketsCount)
            assertEquals(4, this.discountedTicketsCount)
            assertEquals(0, this.freeTicketsCount)
            assertTrue(25.65.toBigDecimal().toCents() isEqualTo this.netGbo.toCents())
            assertTrue(20.85.toBigDecimal().toCents() isEqualTo this.netBo.toCents())
            assertEquals(20, this.distributorCommission)
            assertTrue(4.17.toBigDecimal().toCents() isEqualTo this.distributorFee.toCents())
        }

        val responseSummary = response.summary
        responseSummary.run {
            assertTrue(27.toBigDecimal() isEqualTo this.totalGbo)
            assertEquals(1, this.screeningsCount)
            assertTrue(0.66.toBigDecimal().toCents() isEqualTo this.proFee.toCents())
            assertTrue(0.44.toBigDecimal().toCents() isEqualTo this.filmFondFee.toCents())
            assertEquals(4, this.ticketsCount)
            assertEquals(4, this.discountedTicketsCount)
            assertEquals(0, this.freeTicketsCount)
            assertTrue(25.65.toBigDecimal().toCents() isEqualTo this.netGbo.toCents())
            assertTrue(20.85.toBigDecimal().toCents() isEqualTo this.netBo.toCents())
            assertTrue(4.17.toBigDecimal().toCents() isEqualTo this.distributorFee.toCents())
            assertTrue(0.96.toBigDecimal().toCents() isEqualTo this.distributorFeeVat.toCents())
            assertTrue(5.13.toBigDecimal().toCents() isEqualTo this.distributorFeeWithVat.toCents())
        }
    }

    @Test
    fun `test AdminExportMovieScreeningsQuery - movieIds filter - should filter correct movies`() {
        val response = underTest(
            AdminExportMovieScreeningsQuery(
                filter = AdminExportMovieScreeningsFilter(
                    movieIds = setOf(MOVIE_1.id, MOVIE_4.id)
                ),
                exportFormat = ExportFormat.XLSX,
                username = USERNAME
            )
        )

        val responseMovies = response.movies
        assertEquals(2, responseMovies.size)

        responseMovies.sortedBy { it.movieTitle }[0].run {
            assertEquals("Lego Movie IMAX 3D (SD)", this.movieTitle)
            assertEquals(LocalDate.parse("2024-09-04"), this.firstScreeningDate)
            assertEquals(LocalDate.parse("2024-09-04"), this.lastScreeningDate)
            assertTrue(15.toBigDecimal() isEqualTo this.totalGbo)
            assertEquals(1, this.screeningsCount)
            assertTrue(0.24.toBigDecimal().toCents() isEqualTo this.proFee.toCents())
            assertTrue(0.37.toBigDecimal().toCents() isEqualTo this.filmFondFee.toCents())
            assertEquals(1, this.ticketsCount)
            assertEquals(0, this.discountedTicketsCount)
            assertEquals(0, this.freeTicketsCount)
            assertTrue(14.25.toBigDecimal().toCents() isEqualTo this.netGbo.toCents())
            assertTrue(11.59.toBigDecimal().toCents() isEqualTo this.netBo.toCents())
            assertEquals(30, this.distributorCommission)
            assertTrue(3.48.toBigDecimal().toCents() isEqualTo this.distributorFee.toCents())
        }

        responseMovies.sortedBy { it.movieTitle }[1].run {
            assertEquals("Oppenheimer IMAX 2D (ST)", this.movieTitle)
            assertEquals(LocalDate.parse("2024-08-30"), this.firstScreeningDate)
            assertEquals(LocalDate.parse("2024-08-30"), this.lastScreeningDate)
            assertTrue(2.toBigDecimal() isEqualTo this.totalGbo)
            assertEquals(1, this.screeningsCount)
            assertTrue(0.08.toBigDecimal().toCents() isEqualTo this.proFee.toCents())
            assertTrue(0.16.toBigDecimal().toCents() isEqualTo this.filmFondFee.toCents())
            assertEquals(1, this.ticketsCount)
            assertEquals(0, this.discountedTicketsCount)
            assertEquals(0, this.freeTicketsCount)
            assertTrue(1.70.toBigDecimal().toCents() isEqualTo this.netGbo.toCents())
            assertTrue(1.38.toBigDecimal().toCents() isEqualTo this.netBo.toCents())
            assertEquals(60, this.distributorCommission)
            assertTrue(0.83.toBigDecimal().toCents() isEqualTo this.distributorFee.toCents())
        }

        val responseSummary = response.summary
        responseSummary.run {
            assertTrue(17.toBigDecimal() isEqualTo this.totalGbo)
            assertEquals(2, this.screeningsCount)
            assertTrue(0.33.toBigDecimal().toCents() isEqualTo this.proFee.toCents())
            assertTrue(0.53.toBigDecimal().toCents() isEqualTo this.filmFondFee.toCents())
            assertEquals(2, this.ticketsCount)
            assertEquals(0, this.discountedTicketsCount)
            assertEquals(0, this.freeTicketsCount)
            assertTrue(15.95.toBigDecimal().toCents() isEqualTo this.netGbo.toCents())
            assertTrue(12.97.toBigDecimal().toCents() isEqualTo this.netBo.toCents())
            assertTrue(4.30.toBigDecimal().toCents() isEqualTo this.distributorFee.toCents())
            assertTrue(0.99.toBigDecimal().toCents() isEqualTo this.distributorFeeVat.toCents())
            assertTrue(5.29.toBigDecimal().toCents() isEqualTo this.distributorFeeWithVat.toCents())
        }
    }

    @Test
    fun `test AdminExportMovieScreeningsQuery - branchIds filter - should filter correct movies`() {
        val response = underTest(
            AdminExportMovieScreeningsQuery(
                filter = AdminExportMovieScreeningsFilter(
                    branchIds = setOf(BRANCH_2.id)
                ),
                exportFormat = ExportFormat.XLSX,
                username = USERNAME
            )
        )

        val responseMovies = response.movies
        assertEquals(1, responseMovies.size)

        responseMovies.sortedBy { it.movieTitle }[0].run {
            assertEquals("Matrix 2D (ST)", this.movieTitle)
            assertEquals(LocalDate.parse("2024-08-26"), this.firstScreeningDate)
            assertEquals(LocalDate.parse("2024-08-26"), this.lastScreeningDate)
            assertTrue(27.toBigDecimal().toCents() isEqualTo this.totalGbo.toCents())
            assertEquals(1, this.screeningsCount)
            assertTrue(0.66.toBigDecimal().toCents() isEqualTo this.proFee.toCents())
            assertTrue(0.44.toBigDecimal().toCents() isEqualTo this.filmFondFee.toCents())
            assertEquals(4, this.ticketsCount)
            assertEquals(4, this.discountedTicketsCount)
            assertEquals(0, this.freeTicketsCount)
            assertTrue(25.65.toBigDecimal().toCents() isEqualTo this.netGbo.toCents())
            assertTrue(20.85.toBigDecimal().toCents() isEqualTo this.netBo.toCents())
            assertEquals(20, this.distributorCommission)
            assertTrue(4.17.toBigDecimal().toCents() isEqualTo this.distributorFee.toCents())
        }

        val responseSummary = response.summary
        responseSummary.run {
            assertTrue(27.toBigDecimal() isEqualTo this.totalGbo)
            assertEquals(1, this.screeningsCount)
            assertTrue(0.66.toBigDecimal().toCents() isEqualTo this.proFee.toCents())
            assertTrue(0.44.toBigDecimal().toCents() isEqualTo this.filmFondFee.toCents())
            assertEquals(4, this.ticketsCount)
            assertEquals(4, this.discountedTicketsCount)
            assertEquals(0, this.freeTicketsCount)
            assertTrue(25.65.toBigDecimal().toCents() isEqualTo this.netGbo.toCents())
            assertTrue(20.85.toBigDecimal().toCents() isEqualTo this.netBo.toCents())
            assertTrue(4.17.toBigDecimal().toCents() isEqualTo this.distributorFee.toCents())
            assertTrue(0.96.toBigDecimal().toCents() isEqualTo this.distributorFeeVat.toCents())
            assertTrue(5.13.toBigDecimal().toCents() isEqualTo this.distributorFeeWithVat.toCents())
        }
    }

    @Test
    fun `test AdminExportMovieScreeningsQuery - distributor filter - should return correct movies`() {
        val response = underTest(
            AdminExportMovieScreeningsQuery(
                filter = AdminExportMovieScreeningsFilter(
                    distributorIds = setOf(DISTRIBUTOR_2.id)
                ),
                exportFormat = ExportFormat.XLSX,
                username = USERNAME
            )
        )

        val responseMovies = response.movies
        assertEquals(1, responseMovies.size)

        responseMovies.sortedBy { it.movieTitle }[0].run {
            assertEquals("Matrix 2D (ST)", this.movieTitle)
            assertEquals(LocalDate.parse("2024-08-26"), this.firstScreeningDate)
            assertEquals(LocalDate.parse("2024-08-30"), this.lastScreeningDate)
            assertTrue(27.toBigDecimal().toCents() isEqualTo this.totalGbo.toCents())
            assertEquals(4, this.screeningsCount)
            assertTrue(0.66.toBigDecimal().toCents() isEqualTo this.proFee.toCents())
            assertTrue(0.44.toBigDecimal().toCents() isEqualTo this.filmFondFee.toCents())
            assertEquals(4, this.ticketsCount)
            assertEquals(4, this.discountedTicketsCount)
            assertEquals(0, this.freeTicketsCount)
            assertTrue(25.65.toBigDecimal().toCents() isEqualTo this.netGbo.toCents())
            assertTrue(20.85.toBigDecimal().toCents() isEqualTo this.netBo.toCents())
            assertEquals(70, this.distributorCommission)
            assertTrue(4.17.toBigDecimal().toCents() isEqualTo this.distributorFee.toCents())
        }

        val responseSummary = response.summary
        responseSummary.run {
            assertTrue(27.toBigDecimal() isEqualTo this.totalGbo)
            assertEquals(4, this.screeningsCount)
            assertTrue(0.66.toBigDecimal().toCents() isEqualTo this.proFee.toCents())
            assertTrue(0.44.toBigDecimal().toCents() isEqualTo this.filmFondFee.toCents())
            assertEquals(4, this.ticketsCount)
            assertEquals(4, this.discountedTicketsCount)
            assertEquals(0, this.freeTicketsCount)
            assertTrue(25.65.toBigDecimal().toCents() isEqualTo this.netGbo.toCents())
            assertTrue(20.85.toBigDecimal().toCents() isEqualTo this.netBo.toCents())
            assertTrue(4.17.toBigDecimal().toCents() isEqualTo this.distributorFee.toCents())
            assertTrue(0.96.toBigDecimal().toCents() isEqualTo this.distributorFeeVat.toCents())
            assertTrue(5.13.toBigDecimal().toCents() isEqualTo this.distributorFeeWithVat.toCents())
        }
    }

    @Test
    fun `test AdminExportMovieScreeningsQuery - posConfigurationIds filter - should return correct movies`() {
        val response = underTest(
            AdminExportMovieScreeningsQuery(
                filter = AdminExportMovieScreeningsFilter(
                    posConfigurationIds = setOf(POS_CONFIGURATION_2.id)
                ),
                exportFormat = ExportFormat.XLSX,
                username = USERNAME
            )
        )

        val responseMovies = response.movies
        assertEquals(4, responseMovies.size)

        responseMovies.sortedBy { it.movieTitle }[0].run {
            assertEquals("Lego Movie IMAX 2D (ST)", this.movieTitle)
            assertEquals(LocalDate.parse("2024-09-05"), this.firstScreeningDate)
            assertEquals(LocalDate.parse("2024-09-05"), this.lastScreeningDate)
            assertTrue(0.toBigDecimal() isEqualTo this.totalGbo)
            assertEquals(1, this.screeningsCount)
            assertTrue(0.0.toBigDecimal().toCents() isEqualTo this.proFee.toCents())
            assertTrue(0.0.toBigDecimal().toCents() isEqualTo this.filmFondFee.toCents())
            assertEquals(0, this.ticketsCount)
            assertEquals(0, this.discountedTicketsCount)
            assertEquals(0, this.freeTicketsCount)
            assertTrue(0.0.toBigDecimal().toCents() isEqualTo this.netGbo.toCents())
            assertTrue(0.0.toBigDecimal().toCents() isEqualTo this.netBo.toCents())
            assertEquals(30, this.distributorCommission)
            assertTrue(0.0.toBigDecimal().toCents() isEqualTo this.distributorFee.toCents())
        }

        responseMovies.sortedBy { it.movieTitle }[1].run {
            assertEquals("Lego Movie IMAX 3D (SD)", this.movieTitle)
            assertEquals(LocalDate.parse("2024-09-04"), this.firstScreeningDate)
            assertEquals(LocalDate.parse("2024-09-04"), this.lastScreeningDate)
            assertTrue(0.toBigDecimal() isEqualTo this.totalGbo)
            assertEquals(1, this.screeningsCount)
            assertTrue(0.0.toBigDecimal().toCents() isEqualTo this.proFee.toCents())
            assertTrue(0.0.toBigDecimal().toCents() isEqualTo this.filmFondFee.toCents())
            assertEquals(0, this.ticketsCount)
            assertEquals(0, this.discountedTicketsCount)
            assertEquals(0, this.freeTicketsCount)
            assertTrue(0.0.toBigDecimal().toCents() isEqualTo this.netGbo.toCents())
            assertTrue(0.0.toBigDecimal().toCents() isEqualTo this.netBo.toCents())
            assertEquals(30, this.distributorCommission)
            assertTrue(0.0.toBigDecimal().toCents() isEqualTo this.distributorFee.toCents())
        }

        responseMovies.sortedBy { it.movieTitle }[2].run {
            assertEquals("Matrix 2D (ST)", this.movieTitle)
            assertEquals(LocalDate.parse("2024-08-26"), this.firstScreeningDate)
            assertEquals(LocalDate.parse("2024-08-30"), this.lastScreeningDate)
            assertTrue(1.toBigDecimal().toCents() isEqualTo this.totalGbo.toCents())
            assertEquals(4, this.screeningsCount)
            assertTrue(0.02.toBigDecimal().toCents() isEqualTo this.proFee.toCents())
            assertTrue(0.02.toBigDecimal().toCents() isEqualTo this.filmFondFee.toCents())
            assertEquals(1, this.ticketsCount)
            assertEquals(1, this.discountedTicketsCount)
            assertEquals(0, this.freeTicketsCount)
            assertTrue(0.95.toBigDecimal().toCents() isEqualTo this.netGbo.toCents())
            assertTrue(0.77.toBigDecimal().toCents() isEqualTo this.netBo.toCents())
            assertEquals(70, this.distributorCommission)
            assertTrue(0.15.toBigDecimal().toCents() isEqualTo this.distributorFee.toCents())
        }

        responseMovies.sortedBy { it.movieTitle }[3].run {
            assertEquals("Oppenheimer IMAX 2D (ST)", this.movieTitle)
            assertEquals(LocalDate.parse("2024-08-30"), this.firstScreeningDate)
            assertEquals(LocalDate.parse("2024-08-30"), this.lastScreeningDate)
            assertTrue(0.toBigDecimal() isEqualTo this.totalGbo)
            assertEquals(1, this.screeningsCount)
            assertTrue(0.0.toBigDecimal().toCents() isEqualTo this.proFee.toCents())
            assertTrue(0.0.toBigDecimal().toCents() isEqualTo this.filmFondFee.toCents())
            assertEquals(0, this.ticketsCount)
            assertEquals(0, this.discountedTicketsCount)
            assertEquals(0, this.freeTicketsCount)
            assertTrue(0.0.toBigDecimal().toCents() isEqualTo this.netGbo.toCents())
            assertTrue(0.0.toBigDecimal().toCents() isEqualTo this.netBo.toCents())
            assertEquals(60, this.distributorCommission)
            assertTrue(0.0.toBigDecimal().toCents() isEqualTo this.distributorFee.toCents())
        }
    }

    @Test
    fun `test AdminExportMovieScreeningsQuery - ticket ticketUsed filter - should return correct movies`() {
        val response = underTest(
            AdminExportMovieScreeningsQuery(
                filter = AdminExportMovieScreeningsFilter(
                    ticketUsed = false
                ),
                exportFormat = ExportFormat.XLSX,
                username = USERNAME
            )
        )

        val responseMovies = response.movies
        assertEquals(4, responseMovies.size)

        responseMovies.sortedBy { it.movieTitle }[0].run {
            assertEquals("Lego Movie IMAX 2D (ST)", this.movieTitle)
            assertEquals(LocalDate.parse("2024-09-05"), this.firstScreeningDate)
            assertEquals(LocalDate.parse("2024-09-05"), this.lastScreeningDate)
            assertTrue(0.toBigDecimal() isEqualTo this.totalGbo)
            assertEquals(1, this.screeningsCount)
            assertTrue(0.0.toBigDecimal().toCents() isEqualTo this.proFee.toCents())
            assertTrue(0.0.toBigDecimal().toCents() isEqualTo this.filmFondFee.toCents())
            assertEquals(0, this.ticketsCount)
            assertEquals(0, this.discountedTicketsCount)
            assertEquals(0, this.freeTicketsCount)
            assertTrue(0.0.toBigDecimal().toCents() isEqualTo this.netGbo.toCents())
            assertTrue(0.0.toBigDecimal().toCents() isEqualTo this.netBo.toCents())
            assertEquals(30, this.distributorCommission)
            assertTrue(0.0.toBigDecimal().toCents() isEqualTo this.distributorFee.toCents())
        }

        responseMovies.sortedBy { it.movieTitle }[1].run {
            assertEquals("Lego Movie IMAX 3D (SD)", this.movieTitle)
            assertEquals(LocalDate.parse("2024-09-04"), this.firstScreeningDate)
            assertEquals(LocalDate.parse("2024-09-04"), this.lastScreeningDate)
            assertTrue(0.toBigDecimal() isEqualTo this.totalGbo)
            assertEquals(1, this.screeningsCount)
            assertTrue(0.0.toBigDecimal().toCents() isEqualTo this.proFee.toCents())
            assertTrue(0.0.toBigDecimal().toCents() isEqualTo this.filmFondFee.toCents())
            assertEquals(0, this.ticketsCount)
            assertEquals(0, this.discountedTicketsCount)
            assertEquals(0, this.freeTicketsCount)
            assertTrue(0.0.toBigDecimal().toCents() isEqualTo this.netGbo.toCents())
            assertTrue(0.0.toBigDecimal().toCents() isEqualTo this.netBo.toCents())
            assertEquals(30, this.distributorCommission)
            assertTrue(0.0.toBigDecimal().toCents() isEqualTo this.distributorFee.toCents())
        }

        responseMovies.sortedBy { it.movieTitle }[2].run {
            assertEquals("Matrix 2D (ST)", this.movieTitle)
            assertEquals(LocalDate.parse("2024-08-26"), this.firstScreeningDate)
            assertEquals(LocalDate.parse("2024-08-30"), this.lastScreeningDate)
            assertTrue(0.toBigDecimal() isEqualTo this.totalGbo)
            assertEquals(4, this.screeningsCount)
            assertTrue(0.0.toBigDecimal().toCents() isEqualTo this.proFee.toCents())
            assertTrue(0.0.toBigDecimal().toCents() isEqualTo this.filmFondFee.toCents())
            assertEquals(0, this.ticketsCount)
            assertEquals(0, this.discountedTicketsCount)
            assertEquals(0, this.freeTicketsCount)
            assertTrue(0.0.toBigDecimal().toCents() isEqualTo this.netGbo.toCents())
            assertTrue(0.0.toBigDecimal().toCents() isEqualTo this.netBo.toCents())
            assertEquals(70, this.distributorCommission)
            assertTrue(0.0.toBigDecimal().toCents() isEqualTo this.distributorFee.toCents())
        }

        responseMovies.sortedBy { it.movieTitle }[3].run {
            assertEquals("Oppenheimer IMAX 2D (ST)", this.movieTitle)
            assertEquals(LocalDate.parse("2024-08-30"), this.firstScreeningDate)
            assertEquals(LocalDate.parse("2024-08-30"), this.lastScreeningDate)
            assertTrue(2.toBigDecimal() isEqualTo this.totalGbo)
            assertEquals(1, this.screeningsCount)
            assertTrue(0.08.toBigDecimal().toCents() isEqualTo this.proFee.toCents())
            assertTrue(0.16.toBigDecimal().toCents() isEqualTo this.filmFondFee.toCents())
            assertEquals(1, this.ticketsCount)
            assertEquals(0, this.discountedTicketsCount)
            assertEquals(0, this.freeTicketsCount)
            assertTrue(1.70.toBigDecimal().toCents() isEqualTo this.netGbo.toCents())
            assertTrue(1.38.toBigDecimal().toCents() isEqualTo this.netBo.toCents())
            assertEquals(60, this.distributorCommission)
            assertTrue(0.83.toBigDecimal().toCents() isEqualTo this.distributorFee.toCents())
        }
    }

    @Test
    fun `test AdminExportMovieScreeningsQuery - basketPaidAtFrom and basketPaidAtTo filters - should return correct movies`() {
        val response = underTest(
            AdminExportMovieScreeningsQuery(
                filter = AdminExportMovieScreeningsFilter(
                    basketPaidAtFrom = BASKET_1_POS_1.paidAt?.minusMinutes(1),
                    basketPaidAtTo = BASKET_1_POS_1.paidAt?.plusMinutes(1)
                ),
                exportFormat = ExportFormat.XLSX,
                username = USERNAME
            )
        )

        val responseMovies = response.movies
        assertEquals(4, responseMovies.size)

        responseMovies.sortedBy { it.movieTitle }[0].run {
            // movie with screenings with no tickets
            assertEquals("Lego Movie IMAX 2D (ST)", this.movieTitle)
            assertEquals(LocalDate.parse("2024-09-05"), this.firstScreeningDate)
            assertEquals(LocalDate.parse("2024-09-05"), this.lastScreeningDate)
            assertTrue(0.toBigDecimal() isEqualTo this.totalGbo)
            assertEquals(1, this.screeningsCount)
            assertTrue(0.0.toBigDecimal().toCents() isEqualTo this.proFee.toCents())
            assertTrue(0.0.toBigDecimal().toCents() isEqualTo this.filmFondFee.toCents())
            assertEquals(0, this.ticketsCount)
            assertEquals(0, this.discountedTicketsCount)
            assertEquals(0, this.freeTicketsCount)
            assertTrue(0.0.toBigDecimal().toCents() isEqualTo this.netGbo.toCents())
            assertTrue(0.0.toBigDecimal().toCents() isEqualTo this.netBo.toCents())
            assertEquals(30, this.distributorCommission)
            assertTrue(0.0.toBigDecimal().toCents() isEqualTo this.distributorFee.toCents())
        }

        responseMovies.sortedBy { it.movieTitle }[1].run {
            assertEquals("Lego Movie IMAX 3D (SD)", this.movieTitle)
            assertEquals(LocalDate.parse("2024-09-04"), this.firstScreeningDate)
            assertEquals(LocalDate.parse("2024-09-04"), this.lastScreeningDate)
            assertTrue(15.toBigDecimal() isEqualTo this.totalGbo)
            assertEquals(1, this.screeningsCount)
            assertTrue(0.24.toBigDecimal().toCents() isEqualTo this.proFee.toCents())
            assertTrue(0.37.toBigDecimal().toCents() isEqualTo this.filmFondFee.toCents())
            assertEquals(1, this.ticketsCount)
            assertEquals(0, this.discountedTicketsCount)
            assertEquals(0, this.freeTicketsCount)
            assertTrue(14.25.toBigDecimal().toCents() isEqualTo this.netGbo.toCents())
            assertTrue(11.59.toBigDecimal().toCents() isEqualTo this.netBo.toCents())
            assertEquals(30, this.distributorCommission)
            assertTrue(3.48.toBigDecimal().toCents() isEqualTo this.distributorFee.toCents())
        }

        responseMovies.sortedBy { it.movieTitle }[2].run {
            assertEquals("Matrix 2D (ST)", this.movieTitle)
            assertEquals(LocalDate.parse("2024-08-26"), this.firstScreeningDate)
            assertEquals(LocalDate.parse("2024-08-30"), this.lastScreeningDate)
            assertTrue(26.toBigDecimal().toCents() isEqualTo this.totalGbo.toCents())
            assertEquals(4, this.screeningsCount)
            assertTrue(0.63.toBigDecimal().toCents() isEqualTo this.proFee.toCents())
            assertTrue(0.42.toBigDecimal().toCents() isEqualTo this.filmFondFee.toCents())
            assertEquals(3, this.ticketsCount)
            assertEquals(3, this.discountedTicketsCount)
            assertEquals(0, this.freeTicketsCount)
            assertTrue(24.70.toBigDecimal().toCents() isEqualTo this.netGbo.toCents())
            assertTrue(20.08.toBigDecimal().toCents() isEqualTo this.netBo.toCents())
            assertEquals(70, this.distributorCommission)
            assertTrue(4.02.toBigDecimal().toCents() isEqualTo this.distributorFee.toCents())
        }

        responseMovies.sortedBy { it.movieTitle }[3].run {
            assertEquals("Oppenheimer IMAX 2D (ST)", this.movieTitle)
            assertEquals(LocalDate.parse("2024-08-30"), this.firstScreeningDate)
            assertEquals(LocalDate.parse("2024-08-30"), this.lastScreeningDate)
            assertTrue(2.toBigDecimal() isEqualTo this.totalGbo)
            assertEquals(1, this.screeningsCount)
            assertTrue(0.08.toBigDecimal().toCents() isEqualTo this.proFee.toCents())
            assertTrue(0.16.toBigDecimal().toCents() isEqualTo this.filmFondFee.toCents())
            assertEquals(1, this.ticketsCount)
            assertEquals(0, this.discountedTicketsCount)
            assertEquals(0, this.freeTicketsCount)
            assertTrue(1.70.toBigDecimal().toCents() isEqualTo this.netGbo.toCents())
            assertTrue(1.38.toBigDecimal().toCents() isEqualTo this.netBo.toCents())
            assertEquals(60, this.distributorCommission)
            assertTrue(0.83.toBigDecimal().toCents() isEqualTo this.distributorFee.toCents())
        }
    }

    @Test
    fun `test AdminExportMovieScreeningsQuery - auditoriumIds filter - should return correct movies`() {
        val response = underTest(
            AdminExportMovieScreeningsQuery(
                filter = AdminExportMovieScreeningsFilter(
                    auditoriumIds = setOf(AUDITORIUM_2.id)
                ),
                exportFormat = ExportFormat.XLSX,
                username = USERNAME
            )
        )

        val responseMovies = response.movies
        assertEquals(1, responseMovies.size)

        responseMovies.sortedBy { it.movieTitle }[0].run {
            assertEquals("Matrix 2D (ST)", this.movieTitle)
            assertEquals(LocalDate.parse("2024-08-26"), this.firstScreeningDate)
            assertEquals(LocalDate.parse("2024-08-26"), this.lastScreeningDate)
            assertTrue(27.toBigDecimal().toCents() isEqualTo this.totalGbo.toCents())
            assertEquals(1, this.screeningsCount)
            assertTrue(0.66.toBigDecimal().toCents() isEqualTo this.proFee.toCents())
            assertTrue(0.44.toBigDecimal().toCents() isEqualTo this.filmFondFee.toCents())
            assertEquals(4, this.ticketsCount)
            assertEquals(4, this.discountedTicketsCount)
            assertEquals(0, this.freeTicketsCount)
            assertTrue(25.65.toBigDecimal().toCents() isEqualTo this.netGbo.toCents())
            assertTrue(20.85.toBigDecimal().toCents() isEqualTo this.netBo.toCents())
            assertEquals(20, this.distributorCommission)
            assertTrue(4.17.toBigDecimal().toCents() isEqualTo this.distributorFee.toCents())
        }
    }

    @Test
    fun `test AdminExportMovieScreeningsQuery - screeningTypeIds filter - should return correct movies`() {
        val response = underTest(
            AdminExportMovieScreeningsQuery(
                filter = AdminExportMovieScreeningsFilter(
                    screeningTypeIds = setOf(SCREENING_TYPE_1.id)
                ),
                exportFormat = ExportFormat.XLSX,
                username = USERNAME
            )
        )

        val responseMovies = response.movies
        assertEquals(1, responseMovies.size)

        responseMovies.sortedBy { it.movieTitle }[0].run {
            assertEquals("Matrix 2D (ST)", this.movieTitle)
            assertEquals(LocalDate.parse("2024-08-26"), this.firstScreeningDate)
            assertEquals(LocalDate.parse("2024-08-26"), this.lastScreeningDate)
            assertTrue(27.toBigDecimal().toCents() isEqualTo this.totalGbo.toCents())
            assertEquals(1, this.screeningsCount)
            assertTrue(0.66.toBigDecimal().toCents() isEqualTo this.proFee.toCents())
            assertTrue(0.44.toBigDecimal().toCents() isEqualTo this.filmFondFee.toCents())
            assertEquals(4, this.ticketsCount)
            assertEquals(4, this.discountedTicketsCount)
            assertEquals(0, this.freeTicketsCount)
            assertTrue(25.65.toBigDecimal().toCents() isEqualTo this.netGbo.toCents())
            assertTrue(20.85.toBigDecimal().toCents() isEqualTo this.netBo.toCents())
            assertEquals(20, this.distributorCommission)
            assertTrue(4.17.toBigDecimal().toCents() isEqualTo this.distributorFee.toCents())
        }
    }
}

private const val USERNAME = "username"
private val LOCAL_TIME = LocalTime.of(20, 0, 0)
private val BRANCH_1 = createBranch(
    originalId = 1,
    productSalesCode = 1,
    auditoriumOriginalCodePrefix = "5100",
    code = "511111",
    name = "Bratislava Bory"
)
private val BRANCH_2 = createBranch(
    originalId = 2,
    productSalesCode = 2,
    auditoriumOriginalCodePrefix = "5200",
    code = "522222",
    name = "Košice"
)
private val AUDITORIUM_1 = createAuditorium(
    originalId = 1,
    code = "A",
    title = "SÁLA A - CINEMAX BRATISLAVA",
    originalCode = 22,
    branchId = BRANCH_1.id
)
private val AUDITORIUM_2 = createAuditorium(
    originalId = 2,
    code = "IMAX",
    title = "IMAX - CINEMAX BRATISLAVA",
    capacity = 150,
    originalCode = 11,
    branchId = BRANCH_2.id
)
private val DISTRIBUTOR_1 = createDistributor(originalId = 1)
private val DISTRIBUTOR_2 = createDistributor(originalId = 2, title = "Hello movies, s.r.o.")
private val DISTRIBUTOR_3 = createDistributor(originalId = 3, title = "GoodBye movies, s.r.o.")
private val AUDITORIUM_LAYOUT_1 = createAuditoriumLayout(originalId = 1, auditoriumId = AUDITORIUM_1.id)
private val AUDITORIUM_LAYOUT_2 = createAuditoriumLayout(
    originalId = 2,
    auditoriumId = AUDITORIUM_2.id,
    title = "Special"
)
private val RATING_1 = Rating(
    originalId = 1,
    code = "13",
    title = "13"
)
private val RATING_2 = Rating(
    originalId = 2,
    code = "16",
    title = "16"
)
private val TECHNOLOGY_1 = Technology(
    originalId = 1,
    title = "2D",
    code = "2DA"
)
private val LANGUAGE_1 = Language(
    originalId = 10,
    title = "slovak",
    code = "SVK"
)
private val TMS_LANGUAGE_1 = TmsLanguage(
    originalId = 1,
    title = "Slovenske zneni",
    code = "TMS2"
)
private val TECHNOLOGY_2 = Technology(
    originalId = 2,
    title = "3D IMAX",
    code = "3I"
)
private val LANGUAGE_2 = Language(
    originalId = 11,
    title = "czech",
    code = "CZE"
)
private val TMS_LANGUAGE_2 = TmsLanguage(
    originalId = 2,
    title = "Ceske zneni",
    code = "TMS1"
)
private val MOVIE_1 = createMovie(
    originalId = 1,
    title = "Oppenheimer",
    rawTitle = "Oppenheimer IMAX 2D (ST)",
    releaseYear = 2001,
    distributorId = DISTRIBUTOR_1.id,
    ratingId = RATING_1.id,
    technologyId = TECHNOLOGY_1.id,
    languageId = LANGUAGE_1.id,
    tmsLanguageId = TMS_LANGUAGE_1.id
)
private val MOVIE_2 = createMovie(
    originalId = 2,
    code = "873946",
    title = "Matrix",
    rawTitle = "Matrix 2D (ST)",
    releaseYear = 2000,
    distributorId = DISTRIBUTOR_2.id,
    duration = 150,
    ratingId = RATING_2.id,
    technologyId = TECHNOLOGY_2.id,
    languageId = LANGUAGE_2.id,
    tmsLanguageId = TMS_LANGUAGE_2.id
)
private val MOVIE_3 = createMovie(
    originalId = 3,
    code = "873947",
    title = "Without Screening",
    rawTitle = "Without Screening 2D (ST)",
    releaseYear = 2000,
    distributorId = DISTRIBUTOR_1.id,
    duration = 150,
    ratingId = RATING_2.id,
    technologyId = TECHNOLOGY_2.id,
    languageId = LANGUAGE_2.id,
    tmsLanguageId = TMS_LANGUAGE_2.id
)
private val MOVIE_4 = createMovie(
    originalId = 4,
    code = "873999",
    title = "Lego Movie",
    rawTitle = "Lego Movie IMAX 3D (SD)",
    releaseYear = 2010,
    distributorId = DISTRIBUTOR_3.id,
    duration = 150,
    ratingId = RATING_2.id,
    technologyId = TECHNOLOGY_2.id,
    languageId = LANGUAGE_2.id,
    tmsLanguageId = TMS_LANGUAGE_2.id
)
private val MOVIE_5 = createMovie(
    originalId = 5,
    code = "874000",
    title = "Lego Movie",
    rawTitle = "Lego Movie IMAX 2D (ST)",
    releaseYear = 2010,
    distributorId = DISTRIBUTOR_3.id,
    duration = 150,
    ratingId = RATING_2.id,
    technologyId = TECHNOLOGY_2.id,
    languageId = LANGUAGE_2.id,
    tmsLanguageId = TMS_LANGUAGE_2.id
)
private val PRICE_CATEGORY_1 = createPriceCategory(
    originalId = 1,
    title = "ARTMAX PO 17",
    active = true
)
private val SCREENING_1 = createScreening(
    originalId = 1,
    auditoriumId = AUDITORIUM_1.id,
    auditoriumLayoutId = AUDITORIUM_LAYOUT_1.id,
    movieId = MOVIE_1.id,
    priceCategoryId = PRICE_CATEGORY_1.id,
    date = INTEGRATION_TEST_DATE_TIME.toLocalDate(),
    time = LOCAL_TIME.plusHours(2).plusMinutes(1),
    proCommission = 5,
    filmFondCommission = 10,
    distributorCommission = 60
)
private val SCREENING_2_DELETED = createScreening(
    originalId = 2,
    auditoriumId = AUDITORIUM_1.id,
    auditoriumLayoutId = AUDITORIUM_LAYOUT_1.id,
    movieId = MOVIE_2.id,
    priceCategoryId = PRICE_CATEGORY_1.id,
    state = ScreeningState.PUBLISHED,
    stopped = true
).apply { markDeleted() }
private val SCREENING_3_CANCELLED = createScreening(
    originalId = 3,
    auditoriumId = AUDITORIUM_1.id,
    auditoriumLayoutId = AUDITORIUM_LAYOUT_1.id,
    movieId = MOVIE_2.id,
    priceCategoryId = PRICE_CATEGORY_1.id,
    state = ScreeningState.PUBLISHED,
    cancelled = true,
    distributorCommission = 50
)
private val SCREENING_4_STOPPED = createScreening(
    originalId = 4,
    auditoriumId = AUDITORIUM_1.id,
    auditoriumLayoutId = AUDITORIUM_LAYOUT_1.id,
    movieId = MOVIE_2.id,
    priceCategoryId = PRICE_CATEGORY_1.id,
    state = ScreeningState.PUBLISHED,
    stopped = true,
    distributorCommission = 40
)
private val SCREENING_5 = createScreening(
    originalId = 5,
    auditoriumId = AUDITORIUM_1.id,
    auditoriumLayoutId = AUDITORIUM_LAYOUT_1.id,
    movieId = MOVIE_4.id,
    priceCategoryId = PRICE_CATEGORY_1.id,
    state = ScreeningState.PUBLISHED,
    date = INTEGRATION_TEST_DATE_TIME.toLocalDate().plusDays(5),
    time = LOCAL_TIME.plusHours(3).plusMinutes(10),
    proCommission = 2,
    filmFondCommission = 3,
    distributorCommission = 30
)
private val SCREENING_6 = createScreening(
    originalId = 6,
    auditoriumId = AUDITORIUM_2.id,
    auditoriumLayoutId = AUDITORIUM_LAYOUT_2.id,
    movieId = MOVIE_2.id,
    priceCategoryId = PRICE_CATEGORY_1.id,
    state = ScreeningState.PUBLISHED,
    date = INTEGRATION_TEST_DATE_TIME.toLocalDate().minusDays(4),
    time = LOCAL_TIME.plusMinutes(15),
    proCommission = 3,
    filmFondCommission = 2,
    distributorCommission = 20
)
private val SCREENING_7 = createScreening(
    originalId = 7,
    auditoriumId = AUDITORIUM_1.id,
    auditoriumLayoutId = AUDITORIUM_LAYOUT_1.id,
    movieId = MOVIE_5.id,
    priceCategoryId = PRICE_CATEGORY_1.id,
    state = ScreeningState.PUBLISHED,
    date = INTEGRATION_TEST_DATE_TIME.toLocalDate().plusDays(6),
    time = LOCAL_TIME.plusHours(2).plusMinutes(10),
    proCommission = 2,
    filmFondCommission = 3,
    distributorCommission = 30
)
private val SCREENING_FEE_1 = createScreeningFee(
    originalScreeningId = 1,
    screeningId = SCREENING_1.id,
    surchargeVip = 1.toBigDecimal(),
    surchargePremium = 0.5.toBigDecimal(),
    surchargeImax = 2.toBigDecimal(),
    surchargeUltraX = 0.5.toBigDecimal(),
    serviceFeeVip = 1.toBigDecimal(),
    serviceFeePremium = 0.5.toBigDecimal(),
    serviceFeeImax = 2.toBigDecimal(),
    serviceFeeUltraX = 0.5.toBigDecimal(),
    surchargeDBox = 0.toBigDecimal(),
    serviceFeeGeneral = 0.2.toBigDecimal()
)
private val SCREENING_FEE_5 = createScreeningFee(
    originalScreeningId = 5,
    screeningId = SCREENING_5.id,
    surchargeVip = 1.toBigDecimal(),
    surchargePremium = 0.5.toBigDecimal(),
    surchargeImax = 2.toBigDecimal(),
    surchargeUltraX = 0.5.toBigDecimal(),
    serviceFeeVip = 1.toBigDecimal(),
    serviceFeePremium = 0.5.toBigDecimal(),
    serviceFeeImax = 2.toBigDecimal(),
    serviceFeeUltraX = 0.5.toBigDecimal(),
    surchargeDBox = 5.toBigDecimal(),
    serviceFeeGeneral = 0.2.toBigDecimal()
)
private val SCREENING_FEE_6 = createScreeningFee(
    originalScreeningId = 6,
    screeningId = SCREENING_6.id,
    surchargeVip = 1.toBigDecimal(),
    surchargePremium = 0.5.toBigDecimal(),
    surchargeImax = 2.toBigDecimal(),
    surchargeUltraX = 0.5.toBigDecimal(),
    serviceFeeVip = 1.toBigDecimal(),
    serviceFeePremium = 0.5.toBigDecimal(),
    serviceFeeImax = 2.toBigDecimal(),
    serviceFeeUltraX = 0.5.toBigDecimal(),
    surchargeDBox = 5.toBigDecimal(),
    serviceFeeGeneral = 0.2.toBigDecimal()
)
private val PRICE_CATEGORY_ITEM_1 = createPriceCategoryItem(
    priceCategoryId = PRICE_CATEGORY_1.id,
    title = "Dospely",
    price = 12.toBigDecimal(),
    number = PriceCategoryItemNumber.PRICE_1
)
private val PRICE_CATEGORY_ITEM_2 = createPriceCategoryItem(
    priceCategoryId = PRICE_CATEGORY_1.id,
    title = null,
    price = 6.toBigDecimal(),
    number = PriceCategoryItemNumber.PRICE_2
)
private val SCREENING_TYPE_1 = createScreeningType(
    originalId = 1,
    title = "Imax"
)
private val SCREENING_TYPE_2 = createScreeningType(
    originalId = 2,
    title = "Babska jazda"
)
private val SCREENING_TYPES_1 = createScreeningTypes(
    screeningId = SCREENING_6.id,
    screeningTypeId = SCREENING_TYPE_1.id
)
private val SCREENING_TYPES_2 = createScreeningTypes(
    screeningId = SCREENING_6.id,
    screeningTypeId = SCREENING_TYPE_2.id
)
private val SEAT_1 = createSeat(
    originalId = 1,
    auditoriumId = AUDITORIUM_2.id,
    auditoriumLayoutId = AUDITORIUM_LAYOUT_1.id
)
private val SEAT_2 = createSeat(
    originalId = 2,
    auditoriumId = AUDITORIUM_2.id,
    auditoriumLayoutId = AUDITORIUM_LAYOUT_2.id,
    type = SeatType.VIP
)
private val SEAT_3 = createSeat(
    originalId = 3,
    auditoriumId = AUDITORIUM_1.id,
    auditoriumLayoutId = AUDITORIUM_LAYOUT_1.id,
    type = SeatType.DBOX
)
private val SEAT_4 = createSeat(
    originalId = 4,
    auditoriumId = AUDITORIUM_2.id,
    auditoriumLayoutId = AUDITORIUM_LAYOUT_1.id,
    type = SeatType.PREMIUM_PLUS
)
private val SEAT_5 = createSeat(
    originalId = 5,
    auditoriumId = AUDITORIUM_2.id,
    auditoriumLayoutId = AUDITORIUM_LAYOUT_1.id
)

private val RESERVATION_1 = createReservation(
    screeningId = SCREENING_6.id,
    seatId = SEAT_1.id,
    state = ReservationState.UNAVAILABLE
)
private val RESERVATION_2 = createReservation(
    screeningId = SCREENING_6.id,
    seatId = SEAT_2.id,
    state = ReservationState.RESERVED
)
private val RESERVATION_3 = createReservation(
    screeningId = SCREENING_1.id,
    seatId = SEAT_3.id
)
private val RESERVATION_4 = createReservation(
    screeningId = SCREENING_5.id,
    seatId = SEAT_3.id
)
private val RESERVATION_5 = createReservation(
    screeningId = SCREENING_6.id,
    seatId = SEAT_4.id,
    state = ReservationState.RESERVED
)
private val RESERVATION_6 = createReservation(
    screeningId = SCREENING_6.id,
    seatId = SEAT_5.id,
    state = ReservationState.RESERVED
)
private val TICKET_PRICE_1 = createTicketPrice(
    screeningId = SCREENING_6.id,
    seatId = SEAT_1.id,
    totalPrice = 10.toBigDecimal(),
    basePrice = 5.toBigDecimal(),
    seatSurcharge = 1.5.toBigDecimal(),
    auditoriumSurcharge = 1.3.toBigDecimal()
)
private val TICKET_PRICE_2 = createTicketPrice(
    screeningId = SCREENING_6.id,
    seatId = SEAT_2.id,
    totalPrice = 15.toBigDecimal(),
    basePrice = 7.toBigDecimal(),
    seatSurcharge = 5.5.toBigDecimal(),
    seatSurchargeType = SeatSurchargeType.VIP,
    auditoriumSurcharge = 2.8.toBigDecimal()
)
private val TICKET_PRICE_3 = createTicketPrice(
    screeningId = SCREENING_1.id,
    seatId = SEAT_3.id,
    totalPrice = 5.toBigDecimal(),
    basePrice = 3.toBigDecimal(),
    seatSurcharge = BigDecimal.ZERO,
    seatSurchargeType = SeatSurchargeType.PREMIUM_PLUS,
    auditoriumSurcharge = 1.toBigDecimal(),
    serviceFeeGeneral = 1.toBigDecimal(),
    seatServiceFee = 1.toBigDecimal(),
    auditoriumServiceFee = 1.toBigDecimal(),
    freeTicket = true
)
private val TICKET_PRICE_4 = createTicketPrice(
    screeningId = SCREENING_5.id,
    seatId = SEAT_3.id,
    totalPrice = 15.toBigDecimal(),
    basePrice = 10.toBigDecimal(),
    seatSurchargeType = SeatSurchargeType.DBOX,
    seatSurcharge = 1.5.toBigDecimal(),
    auditoriumSurcharge = 1.5.toBigDecimal()
)
private val TICKET_PRICE_5 = createTicketPrice(
    screeningId = SCREENING_6.id,
    seatId = SEAT_4.id,
    totalPrice = 1.toBigDecimal(),
    basePrice = 5.toBigDecimal(),
    basePriceBeforeDiscount = 5.toBigDecimal(),
    seatSurcharge = 1.toBigDecimal(),
    seatSurchargeType = SeatSurchargeType.PREMIUM_PLUS,
    auditoriumSurcharge = 1.toBigDecimal(),
    // fixed price discount
    primaryDiscountPercentage = 100,
    primaryDiscountAmount = 1.toBigDecimal()
)
private val TICKET_PRICE_6 = createTicketPrice(
    screeningId = SCREENING_6.id,
    seatId = SEAT_5.id,
    totalPrice = 1.toBigDecimal(),
    basePrice = 5.toBigDecimal(),
    basePriceBeforeDiscount = 5.toBigDecimal(),
    seatSurcharge = 2.toBigDecimal(),
    auditoriumSurcharge = 2.toBigDecimal(),
    // fixed price discount
    primaryDiscountPercentage = 100,
    primaryDiscountAmount = 1.toBigDecimal()
)
private val TICKET_DISCOUNT_1 = createTicketDiscount(
    originalId = 1,
    code = "VK1",
    type = TicketDiscountType.ABSOLUTE,
    percentage = 100
)
private val TICKET_DISCOUNT_2 = createTicketDiscount(
    originalId = 2,
    code = "VK2",
    type = TicketDiscountType.ABSOLUTE,
    percentage = 50
)
private val TICKET_DISCOUNT_3 = createTicketDiscount(
    originalId = 3,
    code = "VK3",
    type = TicketDiscountType.PERCENTAGE,
    percentage = 100
)

private val TICKET_1 = createTicket(
    originalId = 1,
    screeningId = SCREENING_6.id,
    reservationId = RESERVATION_1.id,
    ticketPriceId = TICKET_PRICE_1.id,
    ticketDiscountPrimaryId = TICKET_DISCOUNT_3.id,
    isUsed = true
)
private val TICKET_2 = createTicket(
    originalId = 2,
    screeningId = SCREENING_6.id,
    reservationId = RESERVATION_2.id,
    ticketPriceId = TICKET_PRICE_2.id,
    ticketDiscountSecondaryId = TICKET_DISCOUNT_2.id,
    isUsed = true
)
private val TICKET_3 = createTicket(
    originalId = 3,
    screeningId = SCREENING_1.id,
    reservationId = RESERVATION_3.id, // SEAT_3
    ticketPriceId = TICKET_PRICE_3.id,
    isUsed = false
)
private val TICKET_4 = createTicket(
    originalId = 4,
    screeningId = SCREENING_5.id,
    reservationId = RESERVATION_4.id, // SEAT_3
    ticketPriceId = TICKET_PRICE_4.id,
    isUsed = true
)
private val TICKET_5 = createTicket(
    originalId = 5,
    screeningId = SCREENING_6.id,
    reservationId = RESERVATION_5.id,
    ticketPriceId = TICKET_PRICE_5.id,
    ticketDiscountPrimaryId = TICKET_DISCOUNT_1.id,
    isUsed = true
)
private val TICKET_6 = createTicket(
    originalId = 6,
    screeningId = SCREENING_6.id,
    reservationId = RESERVATION_6.id,
    ticketPriceId = TICKET_PRICE_6.id,
    ticketDiscountSecondaryId = TICKET_DISCOUNT_1.id,
    isUsed = true
)

// POS CONFIGURATIONS
private val POS_CONFIGURATION_1 = createPosConfiguration(macAddress = "AA:BB:CC:DD:EE")
private val POS_CONFIGURATION_2 = createPosConfiguration(
    macAddress = "XX:XX:XX:XX:XX",
    title = "Online POS",
    type = PosConfigurationType.ONLINE
)

// BASKETS
private val BASKET_1_POS_1 = createBasket(
    state = BasketState.PAID,
    paymentType = PaymentType.CASH,
    paymentPosConfigurationId = POS_CONFIGURATION_1.id,
    paidAt = INTEGRATION_TEST_DATE_TIME
)
private val BASKET_1_POS_2 = createBasket(
    state = BasketState.PAID_ONLINE,
    paymentType = PaymentType.CASHLESS,
    paymentPosConfigurationId = POS_CONFIGURATION_2.id,
    paidAt = INTEGRATION_TEST_DATE_TIME.plusHours(1)
)

// BASKET ITEMS
private val BASKET_ITEM_TICKET_1_BASKET_1 = createBasketItem(
    basketId = BASKET_1_POS_1.id,
    type = BasketItemType.TICKET,
    price = 10.toBigDecimal(),
    quantity = 1,
    ticketId = TICKET_1.id
)
private val BASKET_ITEM_TICKET_2_BASKET_1 = copyBasketItem(BASKET_ITEM_TICKET_1_BASKET_1) {
    it.ticketId = TICKET_2.id
}
private val BASKET_ITEM_TICKET_3_BASKET_1 = copyBasketItem(BASKET_ITEM_TICKET_1_BASKET_1) {
    it.ticketId = TICKET_3.id
}
private val BASKET_ITEM_TICKET_4_BASKET_1 = copyBasketItem(BASKET_ITEM_TICKET_1_BASKET_1) {
    it.ticketId = TICKET_4.id
}
private val BASKET_ITEM_TICKET_5_BASKET_1 = copyBasketItem(BASKET_ITEM_TICKET_1_BASKET_1) {
    it.ticketId = TICKET_5.id
}
private val BASKET_ITEM_TICKET_6_BASKET_2 = copyBasketItem(BASKET_ITEM_TICKET_1_BASKET_1) {
    it.basketId = BASKET_1_POS_2.id
    it.ticketId = TICKET_6.id
}
