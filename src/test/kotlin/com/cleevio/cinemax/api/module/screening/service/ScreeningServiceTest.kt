package com.cleevio.cinemax.api.module.screening.service

import com.cleevio.cinemax.api.INTEGRATION_TEST_DATE_TIME
import com.cleevio.cinemax.api.common.constant.SCREENING
import com.cleevio.cinemax.api.module.auditorium.service.AuditoriumJpaFinderService
import com.cleevio.cinemax.api.module.auditoriumdefault.service.AuditoriumDefaultJpaFinderService
import com.cleevio.cinemax.api.module.auditoriumlayout.service.AuditoriumLayoutJpaFinderService
import com.cleevio.cinemax.api.module.movie.service.MovieJpaFinderService
import com.cleevio.cinemax.api.module.pricecategory.service.PriceCategoryAutoSelectService
import com.cleevio.cinemax.api.module.pricecategory.service.PriceCategoryJpaFinderService
import com.cleevio.cinemax.api.module.reservation.event.ScreeningWithNonDefaultLayoutSyncedFromMssqlEvent
import com.cleevio.cinemax.api.module.screening.constant.ScreeningLockValues.CREATE_OR_UPDATE_SCREENING
import com.cleevio.cinemax.api.module.screening.constant.ScreeningState
import com.cleevio.cinemax.api.module.screening.entity.Screening
import com.cleevio.cinemax.api.module.screening.event.ScreeningsPublishedEvent
import com.cleevio.cinemax.api.module.screening.event.ScreeningsSwitchedToDraftOrDeletedEvent
import com.cleevio.cinemax.api.module.screening.service.command.UpdateScreeningStatesCommand
import com.cleevio.cinemax.api.module.screeningfee.service.ScreeningFeeJpaFinderService
import com.cleevio.cinemax.api.module.screeningfee.service.ScreeningFeeService
import com.cleevio.cinemax.api.module.screeningtype.service.ScreeningTypeJpaFinderService
import com.cleevio.cinemax.api.module.screeningtypes.service.ScreeningTypesJpaFinderService
import com.cleevio.cinemax.api.module.screeningtypes.service.ScreeningTypesService
import com.cleevio.cinemax.api.util.createAuditorium
import com.cleevio.cinemax.api.util.createAuditoriumLayout
import com.cleevio.cinemax.api.util.createMovie
import com.cleevio.cinemax.api.util.createPriceCategory
import com.cleevio.cinemax.api.util.createScreening
import com.cleevio.cinemax.api.util.createScreeningWithCustomDateTimeLimit
import com.cleevio.cinemax.api.util.mapToCreateOrUpdateScreeningCommand
import com.cleevio.library.lockinghandler.service.LocalLockService
import io.mockk.Called
import io.mockk.Runs
import io.mockk.every
import io.mockk.just
import io.mockk.mockk
import io.mockk.verify
import io.mockk.verifySequence
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.springframework.context.ApplicationEventPublisher
import java.time.Clock
import java.time.LocalDate
import java.time.LocalTime
import java.time.ZoneId
import java.time.ZoneOffset
import java.util.UUID
import kotlin.test.assertEquals

class ScreeningServiceTest {

    private val screeningRepository = mockk<ScreeningRepository>()
    private val screeningJpaFinderService = mockk<ScreeningJpaFinderService>()
    private val auditoriumLayoutJpaFinderService = mockk<AuditoriumLayoutJpaFinderService>()
    private val auditoriumJpaFinderService = mockk<AuditoriumJpaFinderService>()
    private val auditoriumDefaultJpaFinderService = mockk<AuditoriumDefaultJpaFinderService>()
    private val movieJpaFinderService = mockk<MovieJpaFinderService>()
    private val priceCategoryJpaFinderService = mockk<PriceCategoryJpaFinderService>()
    private val priceCategoryAutoSelectService = mockk<PriceCategoryAutoSelectService>()
    private val screeningTypeJpaFinderService = mockk<ScreeningTypeJpaFinderService>()
    private val screeningTypesJpaFinderService = mockk<ScreeningTypesJpaFinderService>()
    private val screeningFeeJpaFinderService = mockk<ScreeningFeeJpaFinderService>()
    private val screeningFeeService = mockk<ScreeningFeeService>()
    private val screeningTypesService = mockk<ScreeningTypesService>()
    private val applicationEventPublisher = mockk<ApplicationEventPublisher>()
    private val lockService = LocalLockService()
    private val clock = mockk<Clock>()

    private val underTest = ScreeningService(
        screeningRepository = screeningRepository,
        screeningJpaFinderService = screeningJpaFinderService,
        auditoriumLayoutJpaFinderService = auditoriumLayoutJpaFinderService,
        auditoriumJpaFinderService = auditoriumJpaFinderService,
        auditoriumDefaultJpaFinderService = auditoriumDefaultJpaFinderService,
        movieJpaFinderService = movieJpaFinderService,
        priceCategoryJpaFinderService = priceCategoryJpaFinderService,
        priceCategoryAutoSelectService = priceCategoryAutoSelectService,
        screeningTypeJpaFinderService = screeningTypeJpaFinderService,
        screeningTypesJpaFinderService = screeningTypesJpaFinderService,
        screeningFeeJpaFinderService = screeningFeeJpaFinderService,
        screeningFeeService = screeningFeeService,
        screeningTypesService = screeningTypesService,
        applicationEventPublisher = applicationEventPublisher,
        lockService = lockService,
        clock = clock
    )

    @BeforeEach
    fun setUp() {
        every { clock.instant() } returns INTEGRATION_TEST_DATE_TIME.toInstant(ZoneOffset.UTC)
        every { clock.zone } returns ZoneId.systemDefault()
    }

    @Test
    fun `test syncCreateOrUpdateScreening - update screening in future - should call accordingly`() {
        every { auditoriumLayoutJpaFinderService.existsNonDeletedById(any()) } returns true
        every { auditoriumJpaFinderService.existsById(any()) } returns true
        every { movieJpaFinderService.existsNonDeletedById(any()) } returns true
        every { priceCategoryJpaFinderService.existsNonDeletedById(any()) } returns true
        every { screeningJpaFinderService.findByOriginalId(any()) } returns SCREENING_1
        every { screeningRepository.save(any()) } returns SCREENING_1
        every { auditoriumLayoutJpaFinderService.existsNonDeletedByIdAndIsNotDefault(any()) } returns false
        every { applicationEventPublisher.publishEvent(any()) } just Runs

        underTest.syncCreateOrUpdateScreening(SCREENING_1_AUDITORIUM_UPDATE_COMMAND)

        verifySequence {
            screeningJpaFinderService.findByOriginalId(SCREENING_1.originalId!!)
            auditoriumLayoutJpaFinderService.existsNonDeletedById(SCREENING_1.auditoriumLayoutId)
            auditoriumJpaFinderService.existsById(AUDITORIUM_2.id)
            movieJpaFinderService.existsNonDeletedById(MOVIE_1.id)
            priceCategoryJpaFinderService.existsNonDeletedById(PRICE_CATEGORY_1.id)
            screeningRepository.save(SCREENING_1_AUDITORIUM_UPDATE)
            auditoriumLayoutJpaFinderService.existsNonDeletedByIdAndIsNotDefault(SCREENING_1.auditoriumLayoutId)
            applicationEventPublisher wasNot Called
        }
    }

    @Test
    fun `test syncCreateOrUpdateScreening - update screening in future with attribute changed - should call accordingly`() {
        every { auditoriumLayoutJpaFinderService.existsNonDeletedById(any()) } returns true
        every { auditoriumJpaFinderService.existsById(any()) } returns true
        every { movieJpaFinderService.existsNonDeletedById(any()) } returns true
        every { priceCategoryJpaFinderService.existsNonDeletedById(any()) } returns true
        every { screeningJpaFinderService.findByOriginalId(any()) } returns SCREENING_2
        every { screeningRepository.save(any()) } returns SCREENING_2
        every { auditoriumLayoutJpaFinderService.existsNonDeletedByIdAndIsNotDefault(any()) } returns true
        every { applicationEventPublisher.publishEvent(any<ScreeningWithNonDefaultLayoutSyncedFromMssqlEvent>()) } just Runs

        underTest.syncCreateOrUpdateScreening(SCREENING_2_PRICE_CATEGORY_UPDATE_COMMAND)

        verifySequence {
            screeningJpaFinderService.findByOriginalId(SCREENING_2.originalId!!)
            auditoriumLayoutJpaFinderService.existsNonDeletedById(SCREENING_2.auditoriumLayoutId)
            auditoriumJpaFinderService.existsById(AUDITORIUM_1.id)
            movieJpaFinderService.existsNonDeletedById(MOVIE_1.id)
            priceCategoryJpaFinderService.existsNonDeletedById(PRICE_CATEGORY_2.id)
            screeningRepository.save(SCREENING_2_PRICE_CATEGORY_UPDATE)
            auditoriumLayoutJpaFinderService.existsNonDeletedByIdAndIsNotDefault(SCREENING_2.auditoriumLayoutId)
            applicationEventPublisher.publishEvent(
                ScreeningWithNonDefaultLayoutSyncedFromMssqlEvent(SCREENING_2.originalId!!)
            )
        }
    }

    @Test
    fun `test syncCreateOrUpdateScreening - deleted screening - should not persist`() {
        every { screeningJpaFinderService.findByOriginalId(any()) } returns SCREENING_3
        underTest.syncCreateOrUpdateScreening(SCREENING_3_CREATE_DELETED_COMMAND)

        verifySequence {
            screeningJpaFinderService.findByOriginalId(SCREENING_3.originalId!!)
            screeningRepository wasNot Called
        }
    }

    @Test
    fun `test updateScreeningStates - screenings to be published - should validate datetime conflicts and publish event`() {
        every { screeningJpaFinderService.getNonDeletedById(any()) } returns SCREENING_5
        every {
            screeningJpaFinderService.existsPublishedInRequestedTimeRange(
                any(),
                any(),
                any(),
                any(),
                any(),
                any(),
                any()
            )
        } returns false
        every { screeningRepository.save(any()) } returns SCREENING_5
        every { applicationEventPublisher.publishEvent(any<ScreeningsPublishedEvent>()) } just Runs

        underTest.updateScreeningStates(
            UpdateScreeningStatesCommand(screeningIds = setOf(SCREENING_5.id), screeningState = ScreeningState.PUBLISHED)
        )

        val screeningCaptor = mutableListOf<Screening>()
        verifySequence {
            screeningJpaFinderService.getNonDeletedById(SCREENING_5.id)
            screeningJpaFinderService.existsPublishedInRequestedTimeRange(
                auditoriumId = SCREENING_5.auditoriumId,
                date = SCREENING_5.date,
                time = SCREENING_5.time,
                movieId = SCREENING_5.movieId,
                adTimeSlot = SCREENING_5.adTimeSlot,
                screeningId = SCREENING_5.id,
                cancelOrStopScreening = SCREENING_5.cancelled || SCREENING_5.stopped
            )
            screeningRepository.save(capture(screeningCaptor))
            applicationEventPublisher.publishEvent(ScreeningsPublishedEvent(setOf(SCREENING_5.id)))
            lockService.obtainBlockingLock(
                module = SCREENING,
                lockName = CREATE_OR_UPDATE_SCREENING,
                SCREENING_5.id.toString()
            )
        }

        assertEquals(ScreeningState.PUBLISHED, screeningCaptor[0].state)
        assertEquals(SCREENING_5.id, screeningCaptor[0].id)
    }

    @Test
    fun `test updateScreeningStates - screenings to be drafted - should validate sold tickets and publish event`() {
        every { screeningJpaFinderService.getNonDeletedById(any()) } returns SCREENING_4
        every { screeningJpaFinderService.existSoldTicketsForScreening(any()) } returns false
        every { screeningRepository.save(any()) } returns SCREENING_4
        every { screeningRepository.findAllAlreadySyncedAndNonDeletedIdsIn(any()) } returns listOf(SCREENING_4.id)
        every { applicationEventPublisher.publishEvent(any<ScreeningsSwitchedToDraftOrDeletedEvent>()) } just Runs

        underTest.updateScreeningStates(
            UpdateScreeningStatesCommand(screeningIds = setOf(SCREENING_4.id), screeningState = ScreeningState.DRAFT)
        )

        val screeningCaptor = mutableListOf<Screening>()
        verifySequence {
            screeningJpaFinderService.getNonDeletedById(SCREENING_4.id)
            screeningJpaFinderService.existSoldTicketsForScreening(SCREENING_4.id)
            screeningRepository.save(capture(screeningCaptor))
            lockService.obtainBlockingLock(
                module = SCREENING,
                lockName = CREATE_OR_UPDATE_SCREENING,
                SCREENING_4.id.toString()
            )
            screeningRepository.findAllAlreadySyncedAndNonDeletedIdsIn(setOf(SCREENING_4.id))
        }

        verify(exactly = 0) {
            applicationEventPublisher.publishEvent(ScreeningsPublishedEvent(setOf(SCREENING_4.id)))
        }
        verify(exactly = 1) {
            applicationEventPublisher.publishEvent(ScreeningsSwitchedToDraftOrDeletedEvent(setOf(SCREENING_4.id)))
        }

        assertEquals(ScreeningState.DRAFT, screeningCaptor[0].state)
        assertEquals(SCREENING_4.id, screeningCaptor[0].id)
    }
}

private val DISTRIBUTOR_1_ID = UUID.randomUUID()
private val AUDITORIUM_1 = createAuditorium(
    originalId = 1,
    code = "A",
    title = "SÁLA A - CINEMAX BRATISLAVA"
)
private val AUDITORIUM_2 = createAuditorium(
    originalId = 2,
    code = "B",
    title = "SÁLA B - CINEMAX BRATISLAVA"
)
private val AUDITORIUM_LAYOUT_1 = createAuditoriumLayout(originalId = 1, auditoriumId = AUDITORIUM_1.id)
private val MOVIE_1 = createMovie(
    originalId = 1,
    title = "Star Wars: Episode I – The Phantom Menace",
    releaseYear = 2001,
    distributorId = DISTRIBUTOR_1_ID
)
private val PRICE_CATEGORY_1 = createPriceCategory(
    originalId = 1,
    title = "ARTMAX PO 17",
    active = true
)
private val PRICE_CATEGORY_2 = createPriceCategory(
    originalId = 2,
    title = "Silvestr extra",
    active = true
)

// It is necessary to use one SCREENING per update test case.
// SCREENING is modified via mapToExistingScreening() function,
// so it leads to unexpected behaviour when this class level constant is used in more than one test case.
private val SCREENING_1 = createScreeningWithCustomDateTimeLimit(
    originalId = 1,
    auditoriumId = AUDITORIUM_1.id,
    priceCategoryId = PRICE_CATEGORY_1.id,
    date = LocalDate.now().plusDays(1),
    time = LocalTime.now(),
    movieId = MOVIE_1.id,
    saleTimeLimit = 30
)
private val SCREENING_2 = createScreeningWithCustomDateTimeLimit(
    originalId = 2,
    auditoriumId = AUDITORIUM_1.id,
    priceCategoryId = PRICE_CATEGORY_1.id,
    date = LocalDate.now().plusDays(1),
    time = LocalTime.now(),
    movieId = MOVIE_1.id,
    saleTimeLimit = 30
)
private val SCREENING_3 = createScreeningWithCustomDateTimeLimit(
    originalId = 3,
    auditoriumId = AUDITORIUM_1.id,
    priceCategoryId = PRICE_CATEGORY_1.id,
    date = LocalDate.now().plusDays(1),
    time = LocalTime.now(),
    movieId = MOVIE_1.id,
    saleTimeLimit = 30
).apply {
    markDeleted()
}
private val SCREENING_1_AUDITORIUM_UPDATE = Screening(
    id = SCREENING_1.id,
    originalId = SCREENING_1.originalId,
    auditoriumId = AUDITORIUM_2.id,
    auditoriumLayoutId = SCREENING_1.auditoriumLayoutId,
    priceCategoryId = SCREENING_1.priceCategoryId,
    date = SCREENING_1.date,
    time = SCREENING_1.time,
    movieId = SCREENING_1.movieId,
    saleTimeLimit = SCREENING_1.saleTimeLimit,
    stopped = false,
    cancelled = false,
    proCommission = 3,
    filmFondCommission = 6,
    distributorCommission = 70,
    publishOnline = true,
    state = ScreeningState.PUBLISHED
)
private val SCREENING_2_PRICE_CATEGORY_UPDATE = Screening(
    id = SCREENING_2.id,
    originalId = SCREENING_2.originalId,
    auditoriumId = SCREENING_2.auditoriumId,
    auditoriumLayoutId = SCREENING_2.auditoriumLayoutId,
    priceCategoryId = PRICE_CATEGORY_2.id,
    date = SCREENING_2.date,
    time = SCREENING_2.time,
    movieId = SCREENING_2.movieId,
    saleTimeLimit = SCREENING_2.saleTimeLimit,
    stopped = false,
    cancelled = false,
    proCommission = 3,
    filmFondCommission = 6,
    distributorCommission = 70,
    publishOnline = true,
    state = ScreeningState.PUBLISHED
)
private val SCREENING_4 = createScreening(
    originalId = 4,
    auditoriumId = AUDITORIUM_1.id,
    auditoriumLayoutId = AUDITORIUM_LAYOUT_1.id,
    movieId = MOVIE_1.id,
    priceCategoryId = PRICE_CATEGORY_1.id,
    date = LocalDate.now().plusDays(1),
    time = LocalTime.of(12, 0),
    state = ScreeningState.PUBLISHED
)
private val SCREENING_5 = createScreening(
    originalId = 4,
    auditoriumId = AUDITORIUM_1.id,
    auditoriumLayoutId = AUDITORIUM_LAYOUT_1.id,
    movieId = MOVIE_1.id,
    priceCategoryId = PRICE_CATEGORY_1.id,
    date = LocalDate.now().plusDays(2),
    time = LocalTime.of(12, 0),
    state = ScreeningState.DRAFT
)
private val SCREENING_1_AUDITORIUM_UPDATE_COMMAND =
    mapToCreateOrUpdateScreeningCommand(SCREENING_1_AUDITORIUM_UPDATE).copy(id = null)
private val SCREENING_2_PRICE_CATEGORY_UPDATE_COMMAND =
    mapToCreateOrUpdateScreeningCommand(SCREENING_2_PRICE_CATEGORY_UPDATE).copy(id = null)
private val SCREENING_3_CREATE_DELETED_COMMAND = mapToCreateOrUpdateScreeningCommand(SCREENING_3).copy(id = null)
