package com.cleevio.cinemax.api.module.language.controller

import com.cleevio.cinemax.api.ControllerTest
import com.cleevio.cinemax.api.common.constant.ApiVersion
import com.cleevio.cinemax.api.common.descriptor.mssql.DescriptorMssqlResponse
import com.cleevio.cinemax.api.common.util.jsonContent
import com.cleevio.cinemax.api.module.employee.constant.EmployeeRole
import com.cleevio.cinemax.api.module.language.entity.Language
import com.cleevio.cinemax.api.util.mockAccessToken
import io.mockk.every
import io.mockk.verify
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest
import org.springframework.http.HttpHeaders
import org.springframework.test.web.servlet.MockMvc
import org.springframework.test.web.servlet.get

@WebMvcTest(AdminLanguageController::class)
class AdminLanguageControllerTest @Autowired constructor(
    private val mvc: MockMvc,
) : ControllerTest() {

    @Test
    fun `test getLanguages, should serialize and deserialize correctly`() {
        every { languageJooqFinderService.findAll() } returns listOf(LANGUAGE_1, LANGUAGE_2, LANGUAGE_3)
        every { descriptorMssqlResponseMapper.mapList(any()) } returns listOf(
            LANGUAGE_1_RESPONSE,
            LANGUAGE_2_RESPONSE,
            LANGUAGE_3_RESPONSE
        )

        mvc.get(GET_LANGUAGES_PATH) {
            header(HttpHeaders.ACCEPT, ApiVersion.VERSION_1_JSON)
            header(HttpHeaders.AUTHORIZATION, mockAccessToken(role = EmployeeRole.MANAGER))
        }.andExpect {
            status { isOk() }
            content { contentType(ApiVersion.VERSION_1_JSON) }
            jsonContent(
                """
                [{
                  "id": "${LANGUAGE_1.id}",
                  "title": "${LANGUAGE_1.title}"
                },
                {
                  "id": "${LANGUAGE_2.id}",
                  "title": "${LANGUAGE_2.title}"
                },
                {
                  "id": "${LANGUAGE_3.id}",
                  "title": "${LANGUAGE_3.title}"
                }]
            """
            )
        }

        verify {
            descriptorMssqlResponseMapper.mapList(listOf(LANGUAGE_1, LANGUAGE_2, LANGUAGE_3))
        }
    }
}

private const val GET_LANGUAGES_PATH = "/manager-app/languages"
private val LANGUAGE_1 = Language(
    originalId = 1,
    code = "CZ",
    title = "česká verze"
)
private val LANGUAGE_2 = Language(
    originalId = 2,
    code = "SK",
    title = "slovenská verze"
)
private val LANGUAGE_3 = Language(
    originalId = 8,
    code = "HU",
    title = "maďarská verze"
)
private val LANGUAGE_1_RESPONSE = DescriptorMssqlResponse(
    id = LANGUAGE_1.id,
    title = LANGUAGE_1.title
)
private val LANGUAGE_2_RESPONSE = DescriptorMssqlResponse(
    id = LANGUAGE_2.id,
    title = LANGUAGE_2.title
)
private val LANGUAGE_3_RESPONSE = DescriptorMssqlResponse(
    id = LANGUAGE_3.id,
    title = LANGUAGE_3.title
)
