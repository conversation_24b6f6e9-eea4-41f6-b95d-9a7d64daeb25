package com.cleevio.cinemax.api.module.outboxevent.event.listener

import com.cleevio.cinemax.api.module.outboxevent.service.OutboxEventService
import com.cleevio.cinemax.api.module.reservation.constant.ReservationState
import com.cleevio.cinemax.api.module.reservation.event.ReservationStateChangedEvent
import io.mockk.Called
import io.mockk.every
import io.mockk.just
import io.mockk.mockk
import io.mockk.runs
import io.mockk.verify
import org.junit.jupiter.api.Test
import java.util.UUID

class ReservationOutboxEventEventListenerTest {

    private val outboxEventService = mockk<OutboxEventService>()
    private val underTest = ReservationOutboxEventEventListener(outboxEventService)

    @Test
    fun `test listenToReservationStateChangedEvent - should not call outboxEventService if seat originalId is null`() {
        every { outboxEventService.createOutboxEvent(any()) } just runs

        underTest.listenToReservationStateChangedEvent(
            ReservationStateChangedEvent(
                reservationId = UUID.randomUUID(),
                newState = ReservationState.RESERVED,
                originalScreeningId = 123456,
                originalSeatId = null,
                updatedBy = "anonymous"
            )
        )

        verify {
            outboxEventService wasNot Called
        }
    }
}
