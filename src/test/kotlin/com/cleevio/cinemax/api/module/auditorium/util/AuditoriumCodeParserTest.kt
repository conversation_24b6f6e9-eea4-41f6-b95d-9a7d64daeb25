package com.cleevio.cinemax.api.module.auditorium.util

import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.Arguments
import org.junit.jupiter.params.provider.MethodSource
import java.util.stream.Stream
import kotlin.test.assertEquals

class AuditoriumCodeParserTest {

    @ParameterizedTest
    @MethodSource("auditoriumTitlesProvider")
    fun `test parseAuditoriumCode, should parse title correctly`(
        input: String,
        expected: String,
    ) {
        assertEquals(expected, parseAuditoriumCode(input))
    }

    companion object {
        @JvmStatic
        fun auditoriumTitlesProvider(): Stream<Arguments> {
            return Stream.of(
                Arguments.of("Sála A - Dunajská Streda", "A"),
                Arguments.of("Sála B - Dunajská Streda", "B"),
                Arguments.of("Sála C - Dunajská Streda", "C"),
                Arguments.of("Sála A", "A"),
                Arguments.of("<PERSON>ála Z", "Z"),
                Arguments.of("IMAX - CINEMAX BRATISLAVA", "IMAX"),
                Arguments.of("SÁLA I - CINEMAX BRATISLAVA", "I"),
                Arguments.of("SÁLA J - CINEMAX BRATISLAVA", "J"),
                Arguments.of("SÁLA K - CINEMAX BRATISLAVA", "K"),
                Arguments.of("SÁLA VIP - CINEMAX BRATISLAVA", "VIP")
            )
        }
    }
}
