package com.cleevio.cinemax.api.module.ticket.service

import com.cleevio.cinemax.api.MssqlIntegrationTest
import com.cleevio.cinemax.api.common.constant.PaymentType
import com.cleevio.cinemax.api.common.util.isEqualTo
import com.cleevio.cinemax.api.module.basket.constant.BasketState
import com.cleevio.cinemax.api.module.basket.service.command.CreateTicketSalesBasketCommand
import com.cleevio.cinemax.api.module.posconfiguration.constant.PosConfigurationType
import com.cleevio.cinemax.api.module.pricecategoryitem.constant.PriceCategoryItemNumber
import com.cleevio.cinemax.api.module.ticketprice.constant.AuditoriumServiceFeeType
import com.cleevio.cinemax.api.module.ticketprice.constant.AuditoriumSurchargeType
import com.cleevio.cinemax.api.module.ticketprice.constant.SeatServiceFeeType
import com.cleevio.cinemax.api.module.ticketprice.constant.SeatSurchargeType
import com.cleevio.cinemax.api.util.createAuditorium
import com.cleevio.cinemax.api.util.createBranch
import com.cleevio.cinemax.api.util.createDiscountCard
import com.cleevio.cinemax.api.util.createPosConfiguration
import com.cleevio.cinemax.api.util.createScreening
import com.cleevio.cinemax.api.util.createSeat
import com.cleevio.cinemax.api.util.createTicketDiscount
import io.mockk.Runs
import io.mockk.every
import io.mockk.just
import io.mockk.verifyAll
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.test.context.jdbc.Sql
import org.springframework.test.context.jdbc.SqlConfig
import org.springframework.test.context.jdbc.SqlGroup
import java.time.LocalDate
import java.time.LocalDateTime
import java.util.UUID
import kotlin.test.assertEquals
import kotlin.test.assertTrue

@SqlGroup(
    Sql(
        scripts = [
            "/db/mssql-cinemax/test/init_mssql_ticket.sql"
        ],
        config = SqlConfig(
            dataSource = "mssqlCinemaxDataSource",
            transactionManager = "mssqlCinemaxTransactionManager"
        )
    ),
    Sql(
        scripts = [
            "/db/mssql-cinemax/test/drop_mssql_ticket.sql"
        ],
        config = SqlConfig(
            dataSource = "mssqlCinemaxDataSource",
            transactionManager = "mssqlCinemaxTransactionManager"
        ),
        executionPhase = Sql.ExecutionPhase.AFTER_TEST_METHOD
    )
)
class TicketSaleMssqlSynchronizationServiceIT @Autowired constructor(
    private val underTest: TicketSaleMssqlSynchronizationService,
    private val ticketMssqlFinderRepository: TicketMssqlFinderRepository,
) : MssqlIntegrationTest() {

    @Test
    fun `test synchronizeAll - should correctly call createTicketSalesBaskets`() {
        every { branchJpaFinderServiceMock.findAll() } returns listOf(BRANCH_1, BRANCH_2)
        every { posConfigurationJpaFinderServiceMock.findAll() } returns listOf(
            POS_CONFIGURATION_1,
            POS_CONFIGURATION_2,
            POS_CONFIGURATION_3
        )
        every { auditoriumJpaFinderServiceMock.findAll() } returns listOf(
            AUDITORIUM_1,
            AUDITORIUM_2,
            AUDITORIUM_3,
            AUDITORIUM_4
        )
        every { screeningJpaFinderServiceMock.findAllNonDeletedByOriginalIdIn(any()) } returns listOf(
            SCREENING_1,
            SCREENING_2,
            SCREENING_3,
            SCREENING_4,
            SCREENING_5,
            SCREENING_6,
            SCREENING_7
        )
        every { seatJpaFinderServiceMock.findAllByOriginalIdIn(any()) } returns listOf(
            SEAT_1,
            SEAT_2,
            SEAT_3,
            SEAT_4,
            SEAT_5,
            SEAT_6,
            SEAT_7,
            SEAT_8,
            SEAT_9,
            SEAT_10
        )
        every { ticketDiscountJpaFinderServiceMock.findAllNonDeletedByCodeIn(any()) } returns listOf(
            TICKET_DISCOUNT_1
        )
        every { discountCardJpaFinderServiceMock.findAllByCodeIn(any()) } returns listOf(
            DISCOUNT_CARD_1
        )
        every { basketServiceMock.createTicketSalesBaskets(any()) } just Runs

        underTest.synchronizeAll(
            syncStartDate = LocalDate.parse("2022-07-09"),
            syncEndDate = LocalDate.parse("2023-11-02"),
            batchDaySpan = 730
        )

        val screeningOriginalIdCaptor = mutableListOf<Set<Int>>()
        val seatOriginalIdCaptor = mutableListOf<Set<Int>>()
        val ticketDiscountCodeCaptor = mutableListOf<Set<String>>()
        val discountCardCodeCaptor = mutableListOf<Set<String>>()
        val createTicketSalesBasketCommandCaptor = mutableListOf<List<CreateTicketSalesBasketCommand>>()

        verifyAll {
            branchJpaFinderServiceMock.findAll()
            posConfigurationJpaFinderServiceMock.findAll()
            auditoriumJpaFinderServiceMock.findAll()
            screeningJpaFinderServiceMock.findAllNonDeletedByOriginalIdIn(capture(screeningOriginalIdCaptor))
            seatJpaFinderServiceMock.findAllByOriginalIdIn(capture(seatOriginalIdCaptor))
            ticketDiscountJpaFinderServiceMock.findAllNonDeletedByCodeIn(capture(ticketDiscountCodeCaptor))
            discountCardJpaFinderServiceMock.findAllByCodeIn(capture(discountCardCodeCaptor))
            basketServiceMock.createTicketSalesBaskets(capture(createTicketSalesBasketCommandCaptor))
        }

        assertEquals(10, ticketMssqlFinderRepository.findAll().size)

        val recordedCreateTicketCommands = createTicketSalesBasketCommandCaptor.first()
        assertEquals(10, recordedCreateTicketCommands.size)
        assertCommandsEqual(EXPECTED_CREATE_TICKET_SALES_BASKET_COMMAND_1, recordedCreateTicketCommands[0])
        assertCommandsEqual(EXPECTED_CREATE_TICKET_SALES_BASKET_COMMAND_2, recordedCreateTicketCommands[1])
        assertCommandsEqual(EXPECTED_CREATE_TICKET_SALES_BASKET_COMMAND_3, recordedCreateTicketCommands[2])
        assertCommandsEqual(EXPECTED_CREATE_TICKET_SALES_BASKET_COMMAND_4, recordedCreateTicketCommands[3])
        assertCommandsEqual(EXPECTED_CREATE_TICKET_SALES_BASKET_COMMAND_5, recordedCreateTicketCommands[4])
        assertCommandsEqual(EXPECTED_CREATE_TICKET_SALES_BASKET_COMMAND_6, recordedCreateTicketCommands[5])
        assertCommandsEqual(EXPECTED_CREATE_TICKET_SALES_BASKET_COMMAND_7, recordedCreateTicketCommands[6])
        assertCommandsEqual(EXPECTED_CREATE_TICKET_SALES_BASKET_COMMAND_8, recordedCreateTicketCommands[7])
        assertCommandsEqual(EXPECTED_CREATE_TICKET_SALES_BASKET_COMMAND_9, recordedCreateTicketCommands[8])
        assertCommandsEqual(EXPECTED_CREATE_TICKET_SALES_BASKET_COMMAND_10, recordedCreateTicketCommands[9])

        assertEquals(7, screeningOriginalIdCaptor.first().size)
        assertTrue(
            screeningOriginalIdCaptor.first().containsAll(
                listOf(
                    SCREENING_1.originalId,
                    SCREENING_2.originalId,
                    SCREENING_2.originalId,
                    SCREENING_3.originalId,
                    SCREENING_4.originalId,
                    SCREENING_4.originalId,
                    SCREENING_5.originalId,
                    SCREENING_6.originalId,
                    SCREENING_7.originalId
                )
            )
        )

        assertEquals(10, seatOriginalIdCaptor.first().size)
        assertTrue(
            seatOriginalIdCaptor.first().containsAll(
                listOf(
                    SEAT_1.originalId,
                    SEAT_2.originalId,
                    SEAT_3.originalId,
                    SEAT_4.originalId,
                    SEAT_5.originalId,
                    SEAT_6.originalId,
                    SEAT_7.originalId,
                    SEAT_8.originalId,
                    SEAT_9.originalId,
                    SEAT_10.originalId
                )
            )
        )

        assertEquals(1, ticketDiscountCodeCaptor.first().size)
        assertTrue(ticketDiscountCodeCaptor.first().containsAll(listOf(TICKET_DISCOUNT_1.code)))
        assertEquals(1, discountCardCodeCaptor.first().size)
        assertTrue(discountCardCodeCaptor.first().containsAll(listOf(DISCOUNT_CARD_1.code)))
    }

    fun assertCommandsEqual(expected: CreateTicketSalesBasketCommand, actual: CreateTicketSalesBasketCommand) {
        assertEquals(expected.variableSymbol, actual.variableSymbol)
        assertEquals(expected.paidAt, actual.paidAt)
        assertEquals(expected.paymentType, actual.paymentType)
        assertEquals(expected.paymentPosConfigurationId, actual.paymentPosConfigurationId)
        assertEquals(expected.originalId, actual.originalId)
        assertEquals(expected.screeningId, actual.screeningId)
        assertEquals(expected.priceCategoryItemNumber, actual.priceCategoryItemNumber)
        assertEquals(expected.receiptNumber, actual.receiptNumber)
        assertEquals(expected.ticketDiscountPrimaryId, actual.ticketDiscountPrimaryId)
        assertEquals(expected.ticketDiscountSecondaryId, actual.ticketDiscountSecondaryId)
        assertEquals(expected.discountCardId, actual.discountCardId)
        assertEquals(expected.isUsed, actual.isUsed)
        assertEquals(expected.isCancellationItem, actual.isCancellationItem)
        assertEquals(expected.includes3dGlasses, actual.includes3dGlasses)
        assertEquals(expected.branchId, actual.branchId)
        assertTrue(expected.basePrice isEqualTo actual.basePrice)
        assertTrue(expected.basePriceBeforeDiscount isEqualTo actual.basePriceBeforeDiscount)
        assertTrue(expected.seatSurcharge isEqualTo actual.seatSurcharge)
        assertEquals(expected.seatSurchargeType, actual.seatSurchargeType)
        assertTrue(expected.auditoriumSurcharge isEqualTo actual.auditoriumSurcharge)
        assertEquals(expected.auditoriumSurchargeType, actual.auditoriumSurchargeType)
        assertTrue(expected.seatServiceFee isEqualTo actual.seatServiceFee)
        assertEquals(expected.seatServiceFeeType, actual.seatServiceFeeType)
        assertTrue(expected.auditoriumServiceFee isEqualTo actual.auditoriumServiceFee)
        assertEquals(expected.auditoriumServiceFeeType, actual.auditoriumServiceFeeType)
        assertTrue(expected.serviceFeeGeneral isEqualTo actual.serviceFeeGeneral)
        assertEquals(expected.freeTicket, actual.freeTicket)
        assertTrue(expected.totalPrice isEqualTo actual.totalPrice)
    }
}

private val POS_CONFIGURATION_1 = createPosConfiguration(
    type = PosConfigurationType.PHYSICAL,
    macAddress = "AA-BB-CC-DD-EE",
    title = "pokl1"
)
private val POS_CONFIGURATION_2 = createPosConfiguration(
    type = PosConfigurationType.PHYSICAL,
    macAddress = "BB-CC-DD-EE-FF",
    title = "pokl3"
)
private val POS_CONFIGURATION_3 = createPosConfiguration(
    type = PosConfigurationType.ONLINE,
    macAddress = "XX-XX-XX-XX-XX",
    title = "Internet"
)
private val BRANCH_1 =
    createBranch(originalId = 1, auditoriumOriginalCodePrefix = "5100", code = "511111", name = "Bratislava Bory")
private val BRANCH_2 = createBranch(originalId = 2, auditoriumOriginalCodePrefix = "5200", code = "522222", name = "Košice")
private val AUDITORIUM_1 = createAuditorium(
    originalId = 1,
    originalCode = 510000,
    branchId = BRANCH_1.id,
    code = "A",
    title = "SÁLA A - CINEMAX BRATISLAVA",
    city = "Bratislava"
)
private val AUDITORIUM_2 = createAuditorium(
    originalId = 2,
    originalCode = 510030,
    branchId = BRANCH_1.id,
    code = "B",
    title = "SÁLA B - CINEMAX BRATISLAVA",
    city = "Bratislava"
)
private val AUDITORIUM_3 = createAuditorium(
    originalId = 3,
    originalCode = 520000,
    branchId = BRANCH_2.id,
    code = "A",
    title = "SÁLA A - CINEMAX Košice",
    city = "Košice"
)
private val AUDITORIUM_4 = createAuditorium(
    originalId = 4,
    originalCode = 520060,
    branchId = BRANCH_2.id,
    code = "B",
    title = "SÁLA B - CINEMAX Košice",
    city = "Košice"
)

private val SCREENING_1 = createScreening(originalId = 111860, auditoriumId = AUDITORIUM_2.id, movieId = UUID.randomUUID())
private val SCREENING_2 = createScreening(originalId = 111880, auditoriumId = AUDITORIUM_1.id, movieId = UUID.randomUUID())
private val SCREENING_3 = createScreening(originalId = 112948, auditoriumId = AUDITORIUM_2.id, movieId = UUID.randomUUID())
private val SCREENING_4 = createScreening(originalId = 111837, auditoriumId = AUDITORIUM_3.id, movieId = UUID.randomUUID())
private val SCREENING_5 = createScreening(originalId = 105100, auditoriumId = AUDITORIUM_4.id, movieId = UUID.randomUUID())
private val SCREENING_6 = createScreening(originalId = 102900, auditoriumId = AUDITORIUM_1.id, movieId = UUID.randomUUID())
private val SCREENING_7 = createScreening(originalId = 103054, auditoriumId = AUDITORIUM_3.id, movieId = UUID.randomUUID())
private val SEAT_1 = createSeat(originalId = 967, auditoriumId = UUID.randomUUID(), auditoriumLayoutId = UUID.randomUUID())
private val SEAT_2 = createSeat(originalId = 2533, auditoriumId = UUID.randomUUID(), auditoriumLayoutId = UUID.randomUUID())
private val SEAT_3 = createSeat(originalId = 2534, auditoriumId = UUID.randomUUID(), auditoriumLayoutId = UUID.randomUUID())
private val SEAT_4 = createSeat(originalId = 1948, auditoriumId = UUID.randomUUID(), auditoriumLayoutId = UUID.randomUUID())
private val SEAT_5 = createSeat(originalId = 1386, auditoriumId = UUID.randomUUID(), auditoriumLayoutId = UUID.randomUUID())
private val SEAT_6 = createSeat(originalId = 1387, auditoriumId = UUID.randomUUID(), auditoriumLayoutId = UUID.randomUUID())
private val SEAT_7 = createSeat(originalId = 1556, auditoriumId = UUID.randomUUID(), auditoriumLayoutId = UUID.randomUUID())
private val SEAT_8 = createSeat(originalId = 448, auditoriumId = UUID.randomUUID(), auditoriumLayoutId = UUID.randomUUID())
private val SEAT_9 = createSeat(originalId = 1755, auditoriumId = UUID.randomUUID(), auditoriumLayoutId = UUID.randomUUID())
private val SEAT_10 = createSeat(originalId = 1756, auditoriumId = UUID.randomUUID(), auditoriumLayoutId = UUID.randomUUID())

private val TICKET_DISCOUNT_1 = createTicketDiscount(code = "FP")
private val DISCOUNT_CARD_1 = createDiscountCard(code = "67120097")

private val EXPECTED_CREATE_TICKET_SALES_BASKET_COMMAND_1 = CreateTicketSalesBasketCommand(
    variableSymbol = "4800427267",
    paidAt = LocalDateTime.of(2022, 7, 9, 8, 37, 0),
    paymentPosConfigurationId = POS_CONFIGURATION_3.id,
    paymentType = PaymentType.CASHLESS,
    state = BasketState.PAID_ONLINE,
    originalId = 1,
    screeningId = SCREENING_1.id,
    seatId = SEAT_1.id,
    priceCategoryItemNumber = PriceCategoryItemNumber.PRICE_19,
    receiptNumber = "0002104035",
    ticketDiscountPrimaryId = null,
    ticketDiscountSecondaryId = null,
    discountCardId = null,
    isUsed = true,
    isCancellationItem = false,
    includes3dGlasses = false,
    branchId = BRANCH_1.id,
    basePrice = 8.95.toBigDecimal(),
    basePriceBeforeDiscount = 8.95.toBigDecimal(),
    seatSurcharge = null,
    seatSurchargeType = null,
    auditoriumSurcharge = null,
    auditoriumSurchargeType = null,
    seatServiceFee = null,
    seatServiceFeeType = null,
    auditoriumServiceFee = null,
    auditoriumServiceFeeType = null,
    serviceFeeGeneral = null,
    freeTicket = false,
    totalPrice = 8.95.toBigDecimal()
)
private val EXPECTED_CREATE_TICKET_SALES_BASKET_COMMAND_2 = CreateTicketSalesBasketCommand(
    variableSymbol = "4800427249",
    paidAt = LocalDateTime.of(2022, 7, 9, 14, 41, 0),
    paymentPosConfigurationId = POS_CONFIGURATION_3.id,
    paymentType = PaymentType.CASHLESS,
    state = BasketState.PAID_ONLINE,
    originalId = 2,
    screeningId = SCREENING_2.id,
    seatId = SEAT_2.id,
    priceCategoryItemNumber = PriceCategoryItemNumber.PRICE_20,
    receiptNumber = "0002103985",
    ticketDiscountPrimaryId = null,
    ticketDiscountSecondaryId = null,
    discountCardId = null,
    isUsed = false,
    isCancellationItem = false,
    includes3dGlasses = true,
    branchId = BRANCH_1.id,
    basePrice = 8.95.toBigDecimal(),
    basePriceBeforeDiscount = 8.95.toBigDecimal(),
    seatSurcharge = null,
    seatSurchargeType = null,
    auditoriumSurcharge = 4.00.toBigDecimal(),
    auditoriumSurchargeType = AuditoriumSurchargeType.IMAX,
    seatServiceFee = null,
    seatServiceFeeType = null,
    auditoriumServiceFee = null,
    auditoriumServiceFeeType = null,
    serviceFeeGeneral = null,
    freeTicket = false,
    totalPrice = 12.95.toBigDecimal()
)
private val EXPECTED_CREATE_TICKET_SALES_BASKET_COMMAND_3 = CreateTicketSalesBasketCommand(
    variableSymbol = "4800427249",
    paidAt = LocalDateTime.of(2022, 7, 9, 14, 41, 0),
    paymentPosConfigurationId = POS_CONFIGURATION_3.id,
    paymentType = PaymentType.CASHLESS,
    state = BasketState.PAID_ONLINE,
    originalId = 3,
    screeningId = SCREENING_2.id,
    seatId = SEAT_3.id,
    priceCategoryItemNumber = PriceCategoryItemNumber.PRICE_20,
    receiptNumber = "0002103986",
    ticketDiscountPrimaryId = null,
    ticketDiscountSecondaryId = null,
    discountCardId = null,
    isUsed = false,
    isCancellationItem = false,
    includes3dGlasses = true,
    branchId = BRANCH_1.id,
    basePrice = 8.95.toBigDecimal(),
    basePriceBeforeDiscount = 8.95.toBigDecimal(),
    seatSurcharge = null,
    seatSurchargeType = null,
    auditoriumSurcharge = 4.00.toBigDecimal(),
    auditoriumSurchargeType = AuditoriumSurchargeType.IMAX,
    seatServiceFee = null,
    seatServiceFeeType = null,
    auditoriumServiceFee = null,
    auditoriumServiceFeeType = null,
    serviceFeeGeneral = null,
    freeTicket = false,
    totalPrice = 12.95.toBigDecimal()
)
private val EXPECTED_CREATE_TICKET_SALES_BASKET_COMMAND_4 = CreateTicketSalesBasketCommand(
    variableSymbol = "4800427036",
    paidAt = LocalDateTime.of(2022, 7, 9, 21, 15, 0),
    paymentPosConfigurationId = POS_CONFIGURATION_3.id,
    paymentType = PaymentType.CASHLESS,
    state = BasketState.PAID_ONLINE,
    originalId = 4,
    screeningId = SCREENING_3.id,
    seatId = SEAT_4.id,
    priceCategoryItemNumber = PriceCategoryItemNumber.PRICE_19,
    receiptNumber = "0002103041",
    ticketDiscountPrimaryId = TICKET_DISCOUNT_1.id,
    ticketDiscountSecondaryId = null,
    discountCardId = DISCOUNT_CARD_1.id,
    isUsed = true,
    isCancellationItem = false,
    includes3dGlasses = false,
    branchId = BRANCH_1.id,
    basePrice = 6.0.toBigDecimal(),
    basePriceBeforeDiscount = 9.2.toBigDecimal(),
    seatSurcharge = null,
    seatSurchargeType = null,
    auditoriumSurcharge = null,
    auditoriumSurchargeType = null,
    seatServiceFee = null,
    seatServiceFeeType = null,
    auditoriumServiceFee = null,
    auditoriumServiceFeeType = null,
    serviceFeeGeneral = null,
    freeTicket = false,
    totalPrice = 6.0.toBigDecimal()
)
private val EXPECTED_CREATE_TICKET_SALES_BASKET_COMMAND_5 = CreateTicketSalesBasketCommand(
    variableSymbol = "4800426868",
    paidAt = LocalDateTime.of(2022, 7, 9, 23, 48, 0),
    paymentPosConfigurationId = POS_CONFIGURATION_3.id,
    paymentType = PaymentType.CASHLESS,
    state = BasketState.PAID_ONLINE,
    originalId = 5,
    screeningId = SCREENING_4.id,
    seatId = SEAT_5.id,
    priceCategoryItemNumber = PriceCategoryItemNumber.PRICE_19,
    receiptNumber = "0002102493",
    ticketDiscountPrimaryId = null,
    ticketDiscountSecondaryId = null,
    discountCardId = null,
    isUsed = true,
    isCancellationItem = false,
    includes3dGlasses = false,
    branchId = BRANCH_2.id,
    basePrice = 7.20.toBigDecimal(),
    basePriceBeforeDiscount = 7.20.toBigDecimal(),
    seatSurcharge = 1.toBigDecimal(),
    seatSurchargeType = SeatSurchargeType.VIP,
    auditoriumSurcharge = null,
    auditoriumSurchargeType = null,
    seatServiceFee = 5.toBigDecimal(),
    seatServiceFeeType = SeatServiceFeeType.VIP,
    auditoriumServiceFee = 1.toBigDecimal(),
    auditoriumServiceFeeType = AuditoriumServiceFeeType.ULTRA_X,
    serviceFeeGeneral = null,
    freeTicket = false,
    totalPrice = 14.20.toBigDecimal()
)
private val EXPECTED_CREATE_TICKET_SALES_BASKET_COMMAND_6 = CreateTicketSalesBasketCommand(
    variableSymbol = "4800426868",
    paidAt = LocalDateTime.of(2022, 7, 9, 23, 48, 0),
    paymentPosConfigurationId = POS_CONFIGURATION_3.id,
    paymentType = PaymentType.CASHLESS,
    state = BasketState.PAID_ONLINE,
    originalId = 6,
    screeningId = SCREENING_4.id,
    seatId = SEAT_6.id,
    priceCategoryItemNumber = PriceCategoryItemNumber.PRICE_20,
    receiptNumber = "0002102494",
    ticketDiscountPrimaryId = null,
    ticketDiscountSecondaryId = null,
    discountCardId = null,
    isUsed = true,
    isCancellationItem = false,
    includes3dGlasses = false,
    branchId = BRANCH_2.id,
    basePrice = 8.95.toBigDecimal(),
    basePriceBeforeDiscount = 8.95.toBigDecimal(),
    seatSurcharge = 1.toBigDecimal(),
    seatSurchargeType = SeatSurchargeType.VIP,
    auditoriumSurcharge = null,
    auditoriumSurchargeType = null,
    seatServiceFee = 5.toBigDecimal(),
    seatServiceFeeType = SeatServiceFeeType.VIP,
    auditoriumServiceFee = 1.toBigDecimal(),
    auditoriumServiceFeeType = AuditoriumServiceFeeType.ULTRA_X,
    serviceFeeGeneral = null,
    freeTicket = false,
    totalPrice = 15.95.toBigDecimal()
)
private val EXPECTED_CREATE_TICKET_SALES_BASKET_COMMAND_7 = CreateTicketSalesBasketCommand(
    variableSymbol = "4800393744",
    paidAt = LocalDateTime.of(2023, 2, 1, 8, 37, 0),
    paymentPosConfigurationId = POS_CONFIGURATION_3.id,
    paymentType = PaymentType.CASHLESS,
    state = BasketState.PAID_ONLINE,
    originalId = 7,
    screeningId = SCREENING_5.id,
    seatId = SEAT_7.id,
    priceCategoryItemNumber = PriceCategoryItemNumber.PRICE_20,
    receiptNumber = "0001980804",
    ticketDiscountPrimaryId = null,
    ticketDiscountSecondaryId = null,
    discountCardId = null,
    isUsed = true,
    isCancellationItem = false,
    includes3dGlasses = false,
    branchId = BRANCH_2.id,
    basePrice = 6.2.toBigDecimal(),
    basePriceBeforeDiscount = 6.2.toBigDecimal(),
    seatSurcharge = null,
    seatSurchargeType = null,
    auditoriumSurcharge = null,
    auditoriumSurchargeType = null,
    seatServiceFee = 1.toBigDecimal(),
    seatServiceFeeType = SeatServiceFeeType.PREMIUM_PLUS,
    auditoriumServiceFee = null,
    auditoriumServiceFeeType = null,
    serviceFeeGeneral = 3.8.toBigDecimal(),
    freeTicket = false,
    totalPrice = 11.toBigDecimal()
)
private val EXPECTED_CREATE_TICKET_SALES_BASKET_COMMAND_8 = CreateTicketSalesBasketCommand(
    variableSymbol = null,
    paidAt = LocalDateTime.of(2023, 11, 1, 9, 1, 0),
    paymentPosConfigurationId = POS_CONFIGURATION_2.id,
    paymentType = PaymentType.CASHLESS,
    state = BasketState.PAID,
    originalId = 8,
    screeningId = SCREENING_6.id,
    seatId = SEAT_8.id,
    priceCategoryItemNumber = PriceCategoryItemNumber.PRICE_1,
    receiptNumber = "0001948986",
    ticketDiscountPrimaryId = null,
    ticketDiscountSecondaryId = null,
    discountCardId = null,
    isUsed = false,
    isCancellationItem = false,
    includes3dGlasses = false,
    branchId = BRANCH_1.id,
    basePrice = 8.45.toBigDecimal(),
    basePriceBeforeDiscount = 8.45.toBigDecimal(),
    seatSurcharge = null,
    seatSurchargeType = null,
    auditoriumSurcharge = 4.toBigDecimal(),
    auditoriumSurchargeType = AuditoriumSurchargeType.IMAX,
    seatServiceFee = null,
    seatServiceFeeType = null,
    auditoriumServiceFee = null,
    auditoriumServiceFeeType = null,
    serviceFeeGeneral = null,
    freeTicket = false,
    totalPrice = 12.45.toBigDecimal()
)
private val EXPECTED_CREATE_TICKET_SALES_BASKET_COMMAND_9 = CreateTicketSalesBasketCommand(
    variableSymbol = null,
    paidAt = LocalDateTime.of(2023, 11, 1, 9, 8, 0),
    paymentPosConfigurationId = POS_CONFIGURATION_1.id,
    paymentType = PaymentType.CASH,
    state = BasketState.PAID,
    originalId = 9,
    screeningId = SCREENING_7.id,
    seatId = SEAT_9.id,
    priceCategoryItemNumber = PriceCategoryItemNumber.PRICE_2,
    receiptNumber = "0001948987",
    ticketDiscountPrimaryId = null,
    ticketDiscountSecondaryId = null,
    discountCardId = null,
    isUsed = true,
    isCancellationItem = false,
    includes3dGlasses = false,
    branchId = BRANCH_2.id,
    basePrice = 8.45.toBigDecimal(),
    basePriceBeforeDiscount = 8.45.toBigDecimal(),
    seatSurcharge = null,
    seatSurchargeType = null,
    auditoriumSurcharge = null,
    auditoriumSurchargeType = null,
    seatServiceFee = null,
    seatServiceFeeType = null,
    auditoriumServiceFee = null,
    auditoriumServiceFeeType = null,
    serviceFeeGeneral = null,
    freeTicket = false,
    totalPrice = 8.45.toBigDecimal()
)
private val EXPECTED_CREATE_TICKET_SALES_BASKET_COMMAND_10 = CreateTicketSalesBasketCommand(
    variableSymbol = null,
    paidAt = LocalDateTime.of(2023, 11, 1, 9, 8, 0),
    paymentPosConfigurationId = POS_CONFIGURATION_1.id,
    paymentType = PaymentType.CASH,
    state = BasketState.PAID,
    originalId = 10,
    screeningId = SCREENING_7.id,
    seatId = SEAT_10.id,
    priceCategoryItemNumber = PriceCategoryItemNumber.PRICE_2,
    receiptNumber = "0001948988",
    ticketDiscountPrimaryId = null,
    ticketDiscountSecondaryId = null,
    discountCardId = null,
    isUsed = true,
    isCancellationItem = false,
    includes3dGlasses = false,
    branchId = BRANCH_2.id,
    basePrice = 8.45.toBigDecimal(),
    basePriceBeforeDiscount = 8.45.toBigDecimal(),
    seatSurcharge = null,
    seatSurchargeType = null,
    auditoriumSurcharge = null,
    auditoriumSurchargeType = null,
    seatServiceFee = null,
    seatServiceFeeType = null,
    auditoriumServiceFee = null,
    auditoriumServiceFeeType = null,
    serviceFeeGeneral = null,
    freeTicket = false,
    totalPrice = 8.45.toBigDecimal()
)
