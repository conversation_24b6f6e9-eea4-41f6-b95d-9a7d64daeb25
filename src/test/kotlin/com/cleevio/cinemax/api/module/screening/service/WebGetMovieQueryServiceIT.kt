package com.cleevio.cinemax.api.module.screening.service

import com.cleevio.cinemax.api.DatabaseCleanup
import com.cleevio.cinemax.api.INTEGRATION_TEST_DATE_TIME
import com.cleevio.cinemax.api.IntegrationTest
import com.cleevio.cinemax.api.module.auditorium.service.AuditoriumService
import com.cleevio.cinemax.api.module.auditoriumlayout.service.AuditoriumLayoutRepository
import com.cleevio.cinemax.api.module.distributor.service.DistributorService
import com.cleevio.cinemax.api.module.genre.entity.Genre
import com.cleevio.cinemax.api.module.genre.service.GenreRepository
import com.cleevio.cinemax.api.module.language.entity.Language
import com.cleevio.cinemax.api.module.language.service.LanguageRepository
import com.cleevio.cinemax.api.module.movie.service.MovieService
import com.cleevio.cinemax.api.module.pricecategory.service.PriceCategoryService
import com.cleevio.cinemax.api.module.rating.entity.Rating
import com.cleevio.cinemax.api.module.rating.service.RatingRepository
import com.cleevio.cinemax.api.module.screening.constant.ScreeningState
import com.cleevio.cinemax.api.module.screening.exception.ScreeningCancelledException
import com.cleevio.cinemax.api.module.screening.exception.ScreeningNotFoundException
import com.cleevio.cinemax.api.module.screening.exception.ScreeningNotPublishedException
import com.cleevio.cinemax.api.module.screening.exception.ScreeningStoppedException
import com.cleevio.cinemax.api.module.screening.service.query.WebGetScreeningMovieQuery
import com.cleevio.cinemax.api.module.screeningtype.service.ScreeningTypeRepository
import com.cleevio.cinemax.api.module.screeningtypes.service.ScreeningTypesRepository
import com.cleevio.cinemax.api.module.technology.entity.Technology
import com.cleevio.cinemax.api.module.technology.service.TechnologyRepository
import com.cleevio.cinemax.api.module.tmslanguage.entity.TmsLanguage
import com.cleevio.cinemax.api.module.tmslanguage.service.TmsLanguageRepository
import com.cleevio.cinemax.api.util.createAuditorium
import com.cleevio.cinemax.api.util.createAuditoriumLayout
import com.cleevio.cinemax.api.util.createDistributor
import com.cleevio.cinemax.api.util.createMovie
import com.cleevio.cinemax.api.util.createPriceCategory
import com.cleevio.cinemax.api.util.createScreening
import com.cleevio.cinemax.api.util.createScreeningType
import com.cleevio.cinemax.api.util.createScreeningTypes
import com.cleevio.cinemax.api.util.mapToCreateOrUpdateAuditoriumCommand
import com.cleevio.cinemax.api.util.mapToCreateOrUpdateDistributorCommand
import com.cleevio.cinemax.api.util.mapToCreateOrUpdateMovieCommand
import com.cleevio.cinemax.api.util.mapToSyncCreateOrUpdatePriceCategoryCommand
import com.cleevio.cinemax.api.util.toUUID
import io.kotest.assertions.throwables.shouldThrow
import io.kotest.matchers.shouldBe
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import java.time.LocalTime

class WebGetMovieQueryServiceIT @Autowired constructor(
    private val underTest: WebGetScreeningMovieQueryService,
    private val auditoriumService: AuditoriumService,
    private val movieService: MovieService,
    private val priceCategoryService: PriceCategoryService,
    private val distributorService: DistributorService,
    private val auditoriumLayoutRepository: AuditoriumLayoutRepository,
    private val ratingRepository: RatingRepository,
    private val technologyRepository: TechnologyRepository,
    private val languageRepository: LanguageRepository,
    private val tmsLanguageRepository: TmsLanguageRepository,
    private val screeningRepository: ScreeningRepository,
    private val screeningTypesRepository: ScreeningTypesRepository,
    private val screeningTypeRepository: ScreeningTypeRepository,
    private val genreRepository: GenreRepository,
) : IntegrationTest(DatabaseCleanup.AFTER_EACH) {

    @Test
    fun `test WebGetScreeningMovieQuery - should find screening when it exists and meets all conditions`() {
        val query = WebGetScreeningMovieQuery(screeningOriginalId = 1)

        val result = underTest(query)

        result.run {
            id shouldBe SCREENING_1.id
            originalId shouldBe SCREENING_1.originalId
            date shouldBe SCREENING_1.date
            time shouldBe SCREENING_1.time
            primaryScreeningTypeCode shouldBe null

            movie.id shouldBe MOVIE_1.id
            movie.rawTitle shouldBe MOVIE_1.rawTitle
            movie.originalTitle shouldBe MOVIE_1.originalTitle
            movie.duration shouldBe MOVIE_1.duration
            movie.ratingTitle shouldBe RATING_1.title
            movie.tmsLanguageTitle shouldBe TMS_LANGUAGE_1.title
            movie.primaryGenreTitle shouldBe null

            auditorium.id shouldBe AUDITORIUM_1.id
            auditorium.originalCode shouldBe AUDITORIUM_1.originalCode
            auditorium.title shouldBe AUDITORIUM_1.title
            auditorium.isImax shouldBe false
        }
    }

    @Test
    fun `test WebGetScreeningMovieQuery - should find screening which doesn't have R rated screening type`() {
        val query = WebGetScreeningMovieQuery(screeningOriginalId = 2)

        val result = underTest(query)

        result.run {
            id shouldBe SCREENING_2.id
            originalId shouldBe SCREENING_2.originalId
            date shouldBe SCREENING_2.date
            time shouldBe SCREENING_2.time
            primaryScreeningTypeCode shouldBe SCREENING_TYPE_1.code

            movie.id shouldBe MOVIE_1.id
            movie.rawTitle shouldBe MOVIE_1.rawTitle
            movie.originalTitle shouldBe MOVIE_1.originalTitle
            movie.duration shouldBe MOVIE_1.duration
            movie.ratingTitle shouldBe RATING_1.title
            movie.tmsLanguageTitle shouldBe TMS_LANGUAGE_1.title
            movie.primaryGenreTitle shouldBe null

            auditorium.id shouldBe AUDITORIUM_1.id
            auditorium.originalCode shouldBe AUDITORIUM_1.originalCode
            auditorium.title shouldBe AUDITORIUM_1.title
            auditorium.isImax shouldBe false
        }
    }

    @Test
    fun `test WebGetScreeningMovieQuery - should find screening which has R rated screening type`() {
        val query = WebGetScreeningMovieQuery(screeningOriginalId = 3)

        val result = underTest(query)

        result.run {
            id shouldBe SCREENING_3.id
            originalId shouldBe SCREENING_3.originalId
            date shouldBe SCREENING_3.date
            time shouldBe SCREENING_3.time
            primaryScreeningTypeCode shouldBe SCREENING_TYPE_2.code

            movie.id shouldBe MOVIE_2.id
            movie.rawTitle shouldBe MOVIE_2.rawTitle
            movie.originalTitle shouldBe MOVIE_2.originalTitle
            movie.duration shouldBe MOVIE_2.duration
            movie.ratingTitle shouldBe "18"
            movie.tmsLanguageTitle shouldBe TMS_LANGUAGE_1.title
            movie.primaryGenreTitle shouldBe GENRE_1.title

            auditorium.id shouldBe AUDITORIUM_1.id
            auditorium.originalCode shouldBe AUDITORIUM_1.originalCode
            auditorium.title shouldBe AUDITORIUM_1.title
            auditorium.isImax shouldBe false
        }
    }

    @Test
    fun `test WebGetScreeningMovieQuery - should find screening when movie has null tmsLanguageId`() {
        val query = WebGetScreeningMovieQuery(screeningOriginalId = 4)

        val result = underTest(query)

        result.run {
            originalId shouldBe SCREENING_4.originalId
            date shouldBe SCREENING_4.date
            time shouldBe SCREENING_4.time
            primaryScreeningTypeCode shouldBe null

            movie.rawTitle shouldBe MOVIE_3.rawTitle
            movie.originalTitle shouldBe MOVIE_3.originalTitle
            movie.duration shouldBe MOVIE_3.duration
            movie.ratingTitle shouldBe RATING_1.title
            movie.tmsLanguageTitle shouldBe null
            movie.primaryGenreTitle shouldBe null

            auditorium.originalCode shouldBe AUDITORIUM_1.originalCode
            auditorium.title shouldBe AUDITORIUM_1.title
            auditorium.isImax shouldBe false
        }
    }

    @Test
    fun `test WebGetScreeningMovieQuery - should throw ScreeningNotFoundException when no screening with given originalId exists`() {
        val query = WebGetScreeningMovieQuery(screeningOriginalId = 999)

        shouldThrow<ScreeningNotFoundException> { underTest(query) }
    }

    @Test
    fun `test WebGetScreeningMovieQuery - should throw ScreeningStoppedException when screening is stopped`() {
        val query = WebGetScreeningMovieQuery(screeningOriginalId = 5)

        shouldThrow<ScreeningStoppedException> { underTest(query) }
    }

    @Test
    fun `test WebGetScreeningMovieQuery - should throw ScreeningCancelledException when screening is cancelled`() {
        val query = WebGetScreeningMovieQuery(screeningOriginalId = 6)

        shouldThrow<ScreeningCancelledException> { underTest(query) }
    }

    @Test
    fun `test WebGetScreeningMovieQuery - should throw ScreeningNotPublishedException when screening is not published online`() {
        val query = WebGetScreeningMovieQuery(screeningOriginalId = 7)

        shouldThrow<ScreeningNotPublishedException> { underTest(query) }
    }

    @Test
    fun `test WebGetScreeningMovieQuery - should throw ScreeningNotPublishedException when screening is not in PUBLISHED state`() {
        val query = WebGetScreeningMovieQuery(screeningOriginalId = 8)

        shouldThrow<ScreeningNotPublishedException> { underTest(query) }
    }

    @BeforeEach
    fun setUp() {
        setOf(AUDITORIUM_1).forEach {
            auditoriumService.syncCreateOrUpdateAuditorium(
                mapToCreateOrUpdateAuditoriumCommand(it)
            )
        }
        setOf(AUDITORIUM_LAYOUT_1).forEach {
            auditoriumLayoutRepository.save(it)
        }
        setOf(DISTRIBUTOR_1).forEach {
            distributorService.syncCreateOrUpdateDistributor(mapToCreateOrUpdateDistributorCommand(it))
        }
        ratingRepository.saveAll(setOf(RATING_1, RATING_2))
        technologyRepository.saveAll(setOf(TECHNOLOGY_1, TECHNOLOGY_2))
        languageRepository.saveAll(setOf(LANGUAGE_1, LANGUAGE_2))
        tmsLanguageRepository.saveAll(setOf(TMS_LANGUAGE_1, TMS_LANGUAGE_2))
        genreRepository.saveAll(setOf(GENRE_1, GENRE_2))
        setOf(MOVIE_1, MOVIE_2, MOVIE_3).forEach {
            movieService.syncCreateOrUpdateMovie(mapToCreateOrUpdateMovieCommand(it))
        }
        priceCategoryService.syncCreateOrUpdatePriceCategory(
            mapToSyncCreateOrUpdatePriceCategoryCommand(PRICE_CATEGORY_1)
        )
        screeningRepository.saveAll(
            setOf(
                SCREENING_1,
                SCREENING_2,
                SCREENING_3,
                SCREENING_4,
                SCREENING_STOPPED,
                SCREENING_CANCELLED,
                SCREENING_NOT_PUBLISHED_ONLINE,
                SCREENING_NOT_PUBLISHED_STATE
            )
        )
        screeningTypeRepository.saveAll(setOf(SCREENING_TYPE_1, SCREENING_TYPE_2))
        screeningTypesRepository.saveAll(setOf(SCREENING_TYPES_1, SCREENING_TYPES_2))
    }

    private val LOCAL_TIME = LocalTime.of(20, 0, 0)
    private val AUDITORIUM_1 = createAuditorium(
        originalId = 1,
        code = "A",
        title = "SÁLA A - CINEMAX BRATISLAVA",
        originalCode = 22
    )
    private val DISTRIBUTOR_1 = createDistributor(originalId = 1)
    private val AUDITORIUM_LAYOUT_1 = createAuditoriumLayout(originalId = 1, auditoriumId = AUDITORIUM_1.id)
    private val RATING_1 = Rating(
        originalId = 1,
        code = "13",
        title = "13"
    )
    private val RATING_2 = Rating(
        originalId = 2,
        code = "16",
        title = "16"
    )
    private val TECHNOLOGY_1 = Technology(
        originalId = 1,
        title = "2D",
        code = "2DA"
    )
    private val LANGUAGE_1 = Language(
        originalId = 10,
        title = "slovak",
        code = "SVK"
    )
    private val TMS_LANGUAGE_1 = TmsLanguage(
        originalId = 1,
        title = "Slovenske zneni",
        code = "TMS2"
    )
    private val TECHNOLOGY_2 = Technology(
        originalId = 2,
        title = "3D IMAX",
        code = "3I"
    )
    private val LANGUAGE_2 = Language(
        originalId = 11,
        title = "czech",
        code = "CZE"
    )
    private val TMS_LANGUAGE_2 = TmsLanguage(
        originalId = 2,
        title = "Ceske zneni",
        code = "TMS1"
    )
    private val GENRE_1 = Genre(
        originalId = 100,
        code = "aaa",
        title = "Sci-fi"
    )
    private val GENRE_2 = Genre(
        originalId = 101,
        code = "bbb",
        title = "Drama"
    )
    private val MOVIE_1 = createMovie(
        originalId = 1,
        title = "Star Wars: Episode I – The Phantom Menace",
        originalTitle = "Star Wars: Episode I – The Phantom Menace",
        rawTitle = "Star Wars: Episode I – The Phantom Menace",
        releaseYear = 2001,
        distributorId = DISTRIBUTOR_1.id,
        ratingId = RATING_1.id,
        technologyId = TECHNOLOGY_1.id,
        languageId = LANGUAGE_1.id,
        tmsLanguageId = TMS_LANGUAGE_1.id,
        duration = 136
    )
    private val MOVIE_2 = createMovie(
        originalId = 2,
        title = "Star Wars: Episode I – The Phantom Menace",
        originalTitle = "Star Wars: Episode I – The Phantom Menace",
        rawTitle = "Star Wars: Episode I – The Phantom Menace",
        releaseYear = 2001,
        distributorId = DISTRIBUTOR_1.id,
        ratingId = RATING_1.id,
        technologyId = TECHNOLOGY_1.id,
        languageId = LANGUAGE_1.id,
        tmsLanguageId = TMS_LANGUAGE_1.id,
        primaryGenreId = GENRE_1.id,
        secondaryGenreId = GENRE_2.id,
        duration = 136
    )

    // without TMS language and genres
    private val MOVIE_3 = createMovie(
        originalId = 3,
        title = "Star Wars: Episode I – The Phantom Menace",
        originalTitle = "Star Wars: Episode I – The Phantom Menace",
        rawTitle = "Star Wars: Episode I – The Phantom Menace",
        releaseYear = 2001,
        distributorId = DISTRIBUTOR_1.id,
        ratingId = RATING_1.id,
        technologyId = TECHNOLOGY_1.id,
        languageId = LANGUAGE_1.id,
        tmsLanguageId = null,
        primaryGenreId = null,
        secondaryGenreId = null,
        duration = 136
    )
    private val PRICE_CATEGORY_1 = createPriceCategory(
        originalId = 1,
        title = "ARTMAX PO 17",
        active = true
    )

    // should be found, has no screening types
    private val SCREENING_1 = createScreening(
        id = 1.toUUID(),
        originalId = 1,
        auditoriumId = AUDITORIUM_1.id,
        auditoriumLayoutId = AUDITORIUM_LAYOUT_1.id,
        movieId = MOVIE_1.id,
        priceCategoryId = PRICE_CATEGORY_1.id,
        stopped = false,
        cancelled = false,
        publishOnline = true,
        state = ScreeningState.PUBLISHED,
        date = INTEGRATION_TEST_DATE_TIME.toLocalDate(),
        time = LOCAL_TIME.plusHours(2).plusMinutes(1)
    )

    // should be found, doesn't have screening type with R rating
    private val SCREENING_2 = createScreening(
        id = 2.toUUID(),
        originalId = 2,
        auditoriumId = AUDITORIUM_1.id,
        auditoriumLayoutId = AUDITORIUM_LAYOUT_1.id,
        movieId = MOVIE_1.id,
        priceCategoryId = PRICE_CATEGORY_1.id,
        stopped = false,
        cancelled = false,
        publishOnline = true,
        state = ScreeningState.PUBLISHED,
        date = INTEGRATION_TEST_DATE_TIME.toLocalDate(),
        time = LOCAL_TIME.plusHours(2).plusMinutes(1)
    )

    // should be found, has screening type with R rating
    private val SCREENING_3 = createScreening(
        id = 3.toUUID(),
        originalId = 3,
        auditoriumId = AUDITORIUM_1.id,
        auditoriumLayoutId = AUDITORIUM_LAYOUT_1.id,
        movieId = MOVIE_2.id,
        priceCategoryId = PRICE_CATEGORY_1.id,
        stopped = false,
        cancelled = false,
        publishOnline = true,
        state = ScreeningState.PUBLISHED,
        date = INTEGRATION_TEST_DATE_TIME.toLocalDate(),
        time = LOCAL_TIME.plusHours(2).plusMinutes(1)
    )

    // should be found, has no screening types
    private val SCREENING_4 = createScreening(
        id = 4.toUUID(),
        originalId = 4,
        auditoriumId = AUDITORIUM_1.id,
        auditoriumLayoutId = AUDITORIUM_LAYOUT_1.id,
        movieId = MOVIE_3.id,
        priceCategoryId = PRICE_CATEGORY_1.id,
        stopped = false,
        cancelled = false,
        publishOnline = true,
        state = ScreeningState.PUBLISHED,
        date = INTEGRATION_TEST_DATE_TIME.toLocalDate(),
        time = LOCAL_TIME.plusHours(2).plusMinutes(1)
    )

    private val SCREENING_STOPPED = createScreening(
        id = 5.toUUID(),
        originalId = 5,
        auditoriumId = AUDITORIUM_1.id,
        auditoriumLayoutId = AUDITORIUM_LAYOUT_1.id,
        movieId = MOVIE_1.id,
        priceCategoryId = PRICE_CATEGORY_1.id,
        stopped = true,
        cancelled = false,
        publishOnline = true,
        state = ScreeningState.PUBLISHED,
        date = INTEGRATION_TEST_DATE_TIME.toLocalDate(),
        time = LOCAL_TIME.plusHours(2)
    )

    // cancelled screening
    private val SCREENING_CANCELLED = createScreening(
        id = 6.toUUID(),
        originalId = 6,
        auditoriumId = AUDITORIUM_1.id,
        auditoriumLayoutId = AUDITORIUM_LAYOUT_1.id,
        movieId = MOVIE_1.id,
        priceCategoryId = PRICE_CATEGORY_1.id,
        stopped = false,
        cancelled = true,
        publishOnline = true,
        state = ScreeningState.PUBLISHED,
        date = INTEGRATION_TEST_DATE_TIME.toLocalDate(),
        time = LOCAL_TIME.plusHours(5)
    )

    // not published online screening
    private val SCREENING_NOT_PUBLISHED_ONLINE = createScreening(
        id = 7.toUUID(),
        originalId = 7,
        auditoriumId = AUDITORIUM_1.id,
        auditoriumLayoutId = AUDITORIUM_LAYOUT_1.id,
        movieId = MOVIE_1.id,
        priceCategoryId = PRICE_CATEGORY_1.id,
        stopped = false,
        cancelled = false,
        publishOnline = false,
        state = ScreeningState.PUBLISHED,
        date = INTEGRATION_TEST_DATE_TIME.toLocalDate(),
        time = LOCAL_TIME.plusHours(6)
    )

    // not in PUBLISHED state screening
    private val SCREENING_NOT_PUBLISHED_STATE = createScreening(
        id = 8.toUUID(),
        originalId = 8,
        auditoriumId = AUDITORIUM_1.id,
        auditoriumLayoutId = AUDITORIUM_LAYOUT_1.id,
        movieId = MOVIE_1.id,
        priceCategoryId = PRICE_CATEGORY_1.id,
        stopped = false,
        cancelled = false,
        publishOnline = true,
        state = ScreeningState.DRAFT,
        date = INTEGRATION_TEST_DATE_TIME.toLocalDate(),
        time = LOCAL_TIME.plusHours(7)
    )

    private val SCREENING_TYPE_1 = createScreeningType(
        originalId = 1,
        code = "02",
        title = "Detske kino"
    )
    private val SCREENING_TYPE_2 = createScreeningType(
        originalId = 2,
        code = "03",
        title = "Babska jazda"
    )
    private val SCREENING_TYPES_1 = createScreeningTypes(
        screeningId = SCREENING_2.id,
        screeningTypeId = SCREENING_TYPE_1.id
    )
    private val SCREENING_TYPES_2 = createScreeningTypes(
        screeningId = SCREENING_3.id,
        screeningTypeId = SCREENING_TYPE_2.id
    )
}
