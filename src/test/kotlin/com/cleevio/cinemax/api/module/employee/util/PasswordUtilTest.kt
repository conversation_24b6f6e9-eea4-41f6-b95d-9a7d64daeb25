package com.cleevio.cinemax.api.module.employee.util

import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.Arguments
import org.junit.jupiter.params.provider.MethodSource
import java.util.stream.Stream
import kotlin.test.assertEquals

class PasswordUtilTest {

    @ParameterizedTest
    @MethodSource("encodedAndDecodedPasswordsProvider")
    fun `test decodePassword, should decode correctly`(
        encoded: String,
        decoded: String,
    ) {
        assertEquals(decoded, decodePassword(encoded))
    }

    @ParameterizedTest
    @MethodSource("encodedAndDecodedPasswordsProvider")
    fun `test encodePassword, should encode correctly`(
        encoded: String,
        decoded: String,
    ) {
        assertEquals(encoded, encodePassword(decoded))
    }

    companion object {
        @JvmStatic
        fun encodedAndDecodedPasswordsProvider(): Stream<Arguments> {
            return Stream.of(
                Arguments.of("`", "a"),
                Arguments.of("lmkef[,/)", "monika372"),
                Arguments.of("uwmkq_]", "vypoved"),
                Arguments.of("12-3", "2407"),
                Arguments.of("l_opdhl", "martins")
            )
        }
    }
}
