package com.cleevio.cinemax.api.module.supplier.event.listener

import com.cleevio.cinemax.api.common.integration.pubsub.dto.MessagePayload
import com.cleevio.cinemax.api.common.service.PublisherService
import com.cleevio.cinemax.api.module.supplier.event.AdminSupplierCreatedOrUpdatedEvent
import com.cleevio.cinemax.api.module.supplier.event.AdminSupplierDeletedEvent
import io.mockk.every
import io.mockk.just
import io.mockk.mockk
import io.mockk.runs
import io.mockk.verify
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test

class SupplierMessagingEventListenerTest {

    private val publisherService: PublisherService = mockk<PublisherService>()
    private val underTest = SupplierMessagingEventListener(publisherService)

    @BeforeEach
    fun setUp() {
        every { publisherService.publish(any()) } just runs
    }

    @Test
    fun `listenToAdminSupplierCreatedOrUpdatedEvent - all event fields present - should publish message`() {
        val event = AdminSupplierCreatedOrUpdatedEvent(
            code = "1234",
            title = "Supplier Co."
        )

        underTest.listenToAdminSupplierCreatedOrUpdatedEvent(event)
        verify(exactly = 1) { publisherService.publish(any<MessagePayload>()) }
    }

    @Test
    fun `listenToAdminSupplierDeletedEvent - should publish message with correct type and data`() {
        val event = AdminSupplierDeletedEvent(code = "1234")

        underTest.listenToAdminSupplierDeletedEvent(event)

        verify(exactly = 1) { publisherService.publish(any<MessagePayload>()) }
    }
}
