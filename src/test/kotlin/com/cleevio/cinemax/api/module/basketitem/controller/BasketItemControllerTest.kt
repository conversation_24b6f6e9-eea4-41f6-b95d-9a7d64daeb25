package com.cleevio.cinemax.api.module.basketitem.controller

import com.cleevio.cinemax.api.ControllerTest
import com.cleevio.cinemax.api.common.constant.ApiVersion
import com.cleevio.cinemax.api.common.util.jsonContent
import com.cleevio.cinemax.api.module.basket.constant.BasketState
import com.cleevio.cinemax.api.module.basket.entity.Basket
import com.cleevio.cinemax.api.module.basketitem.constant.BasketItemType
import com.cleevio.cinemax.api.module.basketitem.controller.dto.BasketItemProductResponse
import com.cleevio.cinemax.api.module.basketitem.controller.dto.BasketItemReservationResponse
import com.cleevio.cinemax.api.module.basketitem.controller.dto.BasketItemResponse
import com.cleevio.cinemax.api.module.basketitem.controller.dto.BasketItemSeatResponse
import com.cleevio.cinemax.api.module.basketitem.controller.dto.BasketItemTicketResponse
import com.cleevio.cinemax.api.module.basketitem.controller.dto.CreateReservationRequest
import com.cleevio.cinemax.api.module.basketitem.controller.dto.CreateTicketBasketItemsRequest
import com.cleevio.cinemax.api.module.basketitem.controller.dto.CreateTicketPriceRequest
import com.cleevio.cinemax.api.module.basketitem.controller.dto.CreateTicketRequest
import com.cleevio.cinemax.api.module.basketitem.entity.BasketItem
import com.cleevio.cinemax.api.module.basketitem.service.command.CreateBasketItemsFromGroupReservationCommand
import com.cleevio.cinemax.api.module.basketitem.service.command.CreateTicketBasketItemsCommand
import com.cleevio.cinemax.api.module.basketitem.service.command.DeleteBasketItemCommand
import com.cleevio.cinemax.api.module.basketitem.service.command.PatchBasketItemCommand
import com.cleevio.cinemax.api.module.pricecategoryitem.constant.PriceCategoryItemNumber
import com.cleevio.cinemax.api.module.product.constant.ProductType
import com.cleevio.cinemax.api.module.reservation.constant.ReservationState
import com.cleevio.cinemax.api.module.ticketprice.constant.SeatSurchargeType
import com.cleevio.cinemax.api.truncatedAndFormatted
import com.cleevio.cinemax.api.util.createBasket
import com.cleevio.cinemax.api.util.createBasketItem
import com.cleevio.cinemax.api.util.createProduct
import com.cleevio.cinemax.api.util.createReservation
import com.cleevio.cinemax.api.util.createTicket
import com.cleevio.cinemax.api.util.createTicketPrice
import com.cleevio.cinemax.api.util.mapToBasketItemRequest
import com.cleevio.cinemax.api.util.mapToBasketItemResponse
import com.cleevio.cinemax.api.util.mapToBasketItemTicketPriceResponse
import com.cleevio.cinemax.api.util.mapToBasketResponse
import com.cleevio.cinemax.api.util.mapToCreateBasketItemCommand
import com.cleevio.cinemax.api.util.mockAccessToken
import com.cleevio.cinemax.api.util.toUUID
import io.mockk.Runs
import io.mockk.every
import io.mockk.just
import io.mockk.verifySequence
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest
import org.springframework.http.HttpHeaders
import org.springframework.http.MediaType
import org.springframework.test.web.servlet.MockMvc
import org.springframework.test.web.servlet.delete
import org.springframework.test.web.servlet.patch
import org.springframework.test.web.servlet.post
import java.math.BigDecimal
import java.util.UUID

@WebMvcTest(BasketItemController::class)
class BasketItemControllerTest @Autowired constructor(
    private val mvc: MockMvc,
) : ControllerTest() {

    @Test
    fun `test createBasketItem, missing accept header, should fall back to latest version content type`() {
        every { basketItemService.createBasketItem(any()) } returns BASKET_ITEM_3
        every { basketJpaFinderService.getNonDeletedById(any()) } returns BASKET
        every { basketResponseMapper.mapSingle(any()) } returns BASKET_RESPONSE_THREE_ITEMS

        mvc.post(CREATE_BASKET_ITEMS_PATH(BASKET.id)) {
            header(HttpHeaders.ACCEPT, ApiVersion.VERSION_1_JSON)
            header(HttpHeaders.AUTHORIZATION, mockAccessToken())
            contentType = MediaType.APPLICATION_JSON
            content = """
                {
                  "type": "${BASKET_ITEM_REQUEST_3.type}",
                  "quantity": ${BASKET_ITEM_REQUEST_3.quantity}
                }
            """.trimIndent()
        }.andExpect {
            content { contentType(ApiVersion.VERSION_1_JSON) }
            status { isOk() }
        }

        verifySequence {
            basketItemService.createBasketItem(any())
            basketJpaFinderService.getNonDeletedById(any())
            basketResponseMapper.mapSingle(any())
        }
    }

    @Test
    fun `test createBasketItem, missing content type, should return 415`() {
        mvc.post(CREATE_BASKET_ITEMS_PATH(BASKET.id)) {
            header(HttpHeaders.ACCEPT, ApiVersion.VERSION_1_JSON)
            header(HttpHeaders.AUTHORIZATION, mockAccessToken())
            content = """{}"""
        }.andExpect {
            status { isUnsupportedMediaType() }
        }
    }

    @Test
    fun `test createBasketItem, invalid content type, should return 415`() {
        mvc.post(CREATE_BASKET_ITEMS_PATH(BASKET.id)) {
            header(HttpHeaders.ACCEPT, ApiVersion.VERSION_1_JSON)
            header(HttpHeaders.AUTHORIZATION, mockAccessToken())
            contentType = MediaType.APPLICATION_PDF
            content = """{}"""
        }.andExpect {
            status { isUnsupportedMediaType() }
        }
    }

    @Test
    fun `test createBasketItem with seatId, should serialize and deserialize correctly`() {
        every { basketItemService.createBasketItem(any()) } returns BASKET_ITEM_2
        every { basketJpaFinderService.getNonDeletedById(any()) } returns BASKET_NO_ITEM_3
        every { basketResponseMapper.mapSingle(any()) } returns BASKET_RESPONSE_NO_ITEM_3

        mvc.post(CREATE_BASKET_ITEMS_PATH(BASKET_NO_ITEM_3.id)) {
            contentType = MediaType.APPLICATION_JSON
            header(HttpHeaders.ACCEPT, ApiVersion.VERSION_1_JSON)
            header(HttpHeaders.AUTHORIZATION, mockAccessToken())
            content = """
                {
                  "type": "${BASKET_ITEM_REQUEST_2.type}",
                  "quantity": ${BASKET_ITEM_REQUEST_2.quantity},
                  "ticket": {
                    "reservation": {
                      "seatId": "${BASKET_ITEM_REQUEST_2.ticket?.reservation?.seatId}"
                    },
                    "ticketPrice": {
                      "priceCategoryItemNumber": "${BASKET_ITEM_REQUEST_2.ticket?.ticketPrice?.priceCategoryItemNumber}"
                    },
                    "screeningId": "$SCREENING_1_ID"
                  }
                }
            """.trimIndent()
        }.andExpect {
            status { isOk() }
            content { contentType(ApiVersion.VERSION_1_JSON) }
            jsonContent(
                """
                {
                  "id": "${BASKET_NO_ITEM_3.id}",
                  "createdAt": "${BASKET_NO_ITEM_3.createdAt.truncatedAndFormatted()}",
                  "tableId": "$TABLE_1_ID",
                  "state": "${BASKET_NO_ITEM_3.state}",
                  "totalPrice": $BASKET_NO_ITEM_3_TOTAL_PRICE,
                  "items": [
                    {
                      "id": "${BASKET_ITEM_1.id}",
                      "type": "${BASKET_ITEM_1.type}",
                      "price": ${BASKET_ITEM_1.price},
                      "quantity": ${BASKET_ITEM_1.quantity},
                      "ticket": {
                        "ticketPrice": {
                          "id": "${TICKET_PRICE_1.id}",
                          "basePrice": ${TICKET_PRICE_1.basePrice},
                          "basePriceItemNumber": "${TICKET_PRICE_1.basePriceItemNumber}",
                          "totalPrice": ${TICKET_PRICE_1.totalPrice}
                        },
                        "reservation": {
                          "seat": {
                            "id": "${RESERVATION_1.seatId}",
                            "row": "${RESERVATION_RESPONSE_1.seat.row}",
                            "number": "${RESERVATION_RESPONSE_1.seat.number}"
                          },
                          "state": "${RESERVATION_1.state}"
                        }
                      }
                    },
                    {
                      "id": "${BASKET_ITEM_2.id}",
                      "type": "${BASKET_ITEM_2.type}",
                      "price": ${BASKET_ITEM_2.price},
                      "quantity": ${BASKET_ITEM_2.quantity},
                      "ticket": {
                        "ticketPrice": {
                          "id": "${TICKET_PRICE_2.id}",
                          "basePrice": ${TICKET_PRICE_2.basePrice},
                          "basePriceItemNumber": "${TICKET_PRICE_2.basePriceItemNumber}",
                          "totalPrice": ${TICKET_PRICE_2.totalPrice}
                        },
                        "reservation": {
                          "seat": {
                            "id": "${RESERVATION_2.seatId}",
                            "row": "${RESERVATION_RESPONSE_2.seat.row}",
                            "number": "${RESERVATION_RESPONSE_2.seat.number}"
                          },
                          "state": "${RESERVATION_2.state}"
                        }
                      }
                    }
                  ]
                }
              """
            )
        }

        verifySequence {
            basketItemService.createBasketItem(
                mapToCreateBasketItemCommand(
                    request = BASKET_ITEM_REQUEST_2.copy(
                        ticket = CreateTicketRequest(
                            ticketPrice = CreateTicketPriceRequest(
                                priceCategoryItemNumber = TICKET_PRICE_2.basePriceItemNumber
                            ),
                            reservation = CreateReservationRequest(
                                seatId = RESERVATION_2.seatId
                            ),
                            screeningId = SCREENING_1_ID,
                            isGroupTicket = false
                        )
                    ),
                    basketId = BASKET_ITEM_2.basketId
                )
            )
            basketJpaFinderService.getNonDeletedById(BASKET_NO_ITEM_3.id)
            basketResponseMapper.mapSingle(BASKET_NO_ITEM_3)
        }
    }

    @Test
    fun `test createBasketItem with productId, should serialize and deserialize correctly`() {
        every { basketItemService.createBasketItem(any()) } returns BASKET_ITEM_3
        every { basketJpaFinderService.getNonDeletedById(any()) } returns BASKET
        every { basketResponseMapper.mapSingle(any()) } returns BASKET_RESPONSE_THREE_ITEMS

        mvc.post(CREATE_BASKET_ITEMS_PATH(BASKET.id)) {
            contentType = MediaType.APPLICATION_JSON
            header(HttpHeaders.ACCEPT, ApiVersion.VERSION_1_JSON)
            header(HttpHeaders.AUTHORIZATION, mockAccessToken())
            content = """
                {
                  "type": "${BASKET_ITEM_REQUEST_3.type}",
                  "quantity": ${BASKET_ITEM_REQUEST_3.quantity},
                  "product": {
                      "productId": "${BASKET_ITEM_REQUEST_3.product?.productId}"
                  }
                }
            """.trimIndent()
        }.andExpect {
            status { isOk() }
            content { contentType(ApiVersion.VERSION_1_JSON) }
            jsonContent(
                """
                {
                  "id": "${BASKET.id}",
                  "createdAt": "${BASKET.createdAt.truncatedAndFormatted()}",
                  "tableId": "$TABLE_1_ID",
                  "state": "${BASKET.state}",
                  "totalPrice": ${BASKET.totalPrice},
                  "items": [
                    {
                      "id": "${BASKET_ITEM_1.id}",
                      "type": "${BASKET_ITEM_1.type}",
                      "price": ${BASKET_ITEM_1.price},
                      "quantity": ${BASKET_ITEM_1.quantity},
                      "ticket": {
                        "ticketPrice": {
                          "id": "${TICKET_PRICE_1.id}",
                          "basePrice": ${TICKET_PRICE_1.basePrice},
                          "basePriceItemNumber": "${TICKET_PRICE_1.basePriceItemNumber}",
                          "totalPrice": ${TICKET_PRICE_1.totalPrice}
                        },
                        "reservation": {
                          "seat": {
                            "id": "${RESERVATION_1.seatId}",
                            "row": "${RESERVATION_RESPONSE_1.seat.row}",
                            "number": "${RESERVATION_RESPONSE_1.seat.number}"
                          },
                          "state": "${RESERVATION_1.state}"
                        }
                      }
                    },
                    {
                      "id": "${BASKET_ITEM_2.id}",
                      "type": "${BASKET_ITEM_2.type}",
                      "price": ${BASKET_ITEM_2.price},
                      "quantity": ${BASKET_ITEM_2.quantity},
                      "ticket": {
                        "ticketPrice": {
                          "id": "${TICKET_PRICE_2.id}",
                          "basePrice": ${TICKET_PRICE_2.basePrice},
                          "basePriceItemNumber": "${TICKET_PRICE_2.basePriceItemNumber}",
                          "totalPrice": ${TICKET_PRICE_2.totalPrice}
                        },
                        "reservation": {
                          "seat": {
                            "id": "${RESERVATION_2.seatId}",
                            "row": "${RESERVATION_RESPONSE_2.seat.row}",
                            "number": "${RESERVATION_RESPONSE_2.seat.number}"
                          },
                          "state": "${RESERVATION_2.state}"
                        }
                      }
                    },
                    {
                      "id": "${BASKET_ITEM_3.id}",
                      "type": "${BASKET_ITEM_3.type}",
                      "price": ${BASKET_ITEM_3.price},
                      "quantity": ${BASKET_ITEM_3.quantity},
                      "product": {
                        "id": "${BASKET_ITEM_3.productId}",
                        "title": "${PRODUCT_1.title}",
                        "price": ${PRODUCT_1.price}
                      }
                    }
                  ]
                }
              """
            )
        }

        verifySequence {
            basketItemService.createBasketItem(
                mapToCreateBasketItemCommand(BASKET_ITEM_REQUEST_3, BASKET_ITEM_3.basketId)
            )
            basketJpaFinderService.getNonDeletedById(BASKET.id)
            basketResponseMapper.mapSingle(BASKET)
        }
    }

    @Test
    fun `test deleteBasketItem, should serialize and deserialize correctly`() {
        every { basketItemService.deleteBasketItem(any()) } just Runs
        every { basketJpaFinderService.getNonDeletedById(any()) } returns BASKET_NO_ITEM_2
        every { basketResponseMapper.mapSingle(any()) } returns BASKET_RESPONSE_NO_ITEM_2

        mvc.delete(DELETE_AND_PATCH_PATH(BASKET_NO_ITEM_2.id, BASKET_ITEM_2.id)) {
            contentType = MediaType.APPLICATION_JSON
            header(HttpHeaders.ACCEPT, ApiVersion.VERSION_1_JSON)
            header(HttpHeaders.AUTHORIZATION, mockAccessToken())
        }.andExpect {
            status { isOk() }
            content { contentType(ApiVersion.VERSION_1_JSON) }
            jsonContent(
                """
                {
                  "id": "${BASKET_NO_ITEM_2.id}",
                  "createdAt": "${BASKET_NO_ITEM_2.createdAt.truncatedAndFormatted()}",
                  "tableId": "$TABLE_1_ID",
                  "state": "${BASKET_NO_ITEM_2.state}",
                  "totalPrice": $BASKET_NO_ITEM_2_TOTAL_PRICE,
                  "items": [
                    {
                      "id": "${BASKET_ITEM_1.id}",
                      "type": "${BASKET_ITEM_1.type}",
                      "price": ${BASKET_ITEM_1.price},
                      "quantity": ${BASKET_ITEM_1.quantity},
                      "ticket": {
                        "ticketPrice": {
                          "id": "${TICKET_PRICE_1.id}",
                          "basePrice": ${TICKET_PRICE_1.basePrice},
                          "basePriceItemNumber": "${TICKET_PRICE_1.basePriceItemNumber}",
                          "totalPrice": ${TICKET_PRICE_1.totalPrice}
                        },
                        "reservation": {
                          "seat": {
                            "id": "${RESERVATION_1.seatId}",
                            "row": "${RESERVATION_RESPONSE_1.seat.row}",
                            "number": "${RESERVATION_RESPONSE_1.seat.number}"
                          },
                          "state": "${RESERVATION_1.state}"
                        }
                      }
                    },
                    {
                      "id": "${BASKET_ITEM_3.id}",
                      "type": "${BASKET_ITEM_3.type}",
                      "price": ${BASKET_ITEM_3.price},
                      "quantity": ${BASKET_ITEM_3.quantity}
                    }
                  ]
                }
              """
            )
        }

        verifySequence {
            basketItemService.deleteBasketItem(
                DeleteBasketItemCommand(BASKET_NO_ITEM_2.id, BASKET_ITEM_2.id)
            )
            basketJpaFinderService.getNonDeletedById(BASKET_NO_ITEM_2.id)
            basketResponseMapper.mapSingle(BASKET_NO_ITEM_2)
        }
    }

    @Test
    fun `test patchBasketItem - increase quantity - should serialize and deserialize correctly`() {
        every { basketItemService.patchBasketItem(any()) } just Runs
        every { basketJpaFinderService.getNonDeletedById(any()) } returns BASKET_BASKET_ITEM_3_INC_QUANTITY
        every { basketResponseMapper.mapSingle(any()) } returns BASKET_RESPONSE_ITEM_3_INC_QUANTITY

        mvc.patch(DELETE_AND_PATCH_PATH(BASKET_BASKET_ITEM_3_INC_QUANTITY.id, BASKET_ITEM_3.id)) {
            contentType = MediaType.APPLICATION_JSON
            header(HttpHeaders.ACCEPT, ApiVersion.VERSION_1_JSON)
            header(HttpHeaders.AUTHORIZATION, mockAccessToken())
            content = """
                {
                  "quantity": $QUANTITY_2
                }
            """.trimIndent()
        }.andExpect {
            status { isOk() }
            content { contentType(ApiVersion.VERSION_1_JSON) }
            jsonContent(
                """
                {
                  "id": "${BASKET_BASKET_ITEM_3_INC_QUANTITY.id}",
                  "createdAt": "${BASKET_BASKET_ITEM_3_INC_QUANTITY.createdAt.truncatedAndFormatted()}",
                  "tableId": "$TABLE_1_ID",
                  "state": "${BASKET_BASKET_ITEM_3_INC_QUANTITY.state}",
                  "totalPrice": ${BASKET_BASKET_ITEM_3_INC_QUANTITY.totalPrice},
                  "items": [
                    {
                      "id": "${BASKET_ITEM_1.id}",
                      "type": "${BASKET_ITEM_1.type}",
                      "price": ${BASKET_ITEM_1.price},
                      "quantity": ${BASKET_ITEM_1.quantity},
                      "ticket": {
                        "ticketPrice": {
                          "id": "${TICKET_PRICE_1.id}",
                          "basePrice": ${TICKET_PRICE_1.basePrice},
                          "basePriceItemNumber": "${TICKET_PRICE_1.basePriceItemNumber}",
                          "totalPrice": ${TICKET_PRICE_1.totalPrice}
                        },
                        "reservation": {
                          "seat": {
                            "id": "${RESERVATION_1.seatId}",
                            "row": "${RESERVATION_RESPONSE_1.seat.row}",
                            "number": "${RESERVATION_RESPONSE_1.seat.number}"
                          },
                          "state": "${RESERVATION_1.state}"
                        }
                      }
                    },
                    {
                      "id": "${BASKET_ITEM_2.id}",
                      "type": "${BASKET_ITEM_2.type}",
                      "price": ${BASKET_ITEM_2.price},
                      "quantity": ${BASKET_ITEM_2.quantity},
                      "ticket": {
                        "ticketPrice": {
                          "id": "${TICKET_PRICE_2.id}",
                          "basePrice": ${TICKET_PRICE_2.basePrice},
                          "basePriceItemNumber": "${TICKET_PRICE_2.basePriceItemNumber}",
                          "totalPrice": ${TICKET_PRICE_2.totalPrice}
                        },
                        "reservation": {
                          "seat": {
                            "id": "${RESERVATION_2.seatId}",
                            "row": "${RESERVATION_RESPONSE_2.seat.row}",
                            "number": "${RESERVATION_RESPONSE_2.seat.number}"
                          },
                          "state": "${RESERVATION_2.state}"
                        }
                      }
                    },
                    {
                      "id": "${BASKET_ITEM_3.id}",
                      "type": "${BASKET_ITEM_3.type}",
                      "price": ${BASKET_ITEM_3.price},
                      "quantity": $QUANTITY_2
                    }
                  ]
                }
              """
            )
        }

        verifySequence {
            basketItemService.patchBasketItem(
                PatchBasketItemCommand(
                    basketId = BASKET_BASKET_ITEM_3_INC_QUANTITY.id,
                    basketItemId = BASKET_ITEM_3.id,
                    quantity = QUANTITY_2
                )
            )
            basketJpaFinderService.getNonDeletedById(BASKET_BASKET_ITEM_3_INC_QUANTITY.id)
            basketResponseMapper.mapSingle(BASKET_BASKET_ITEM_3_INC_QUANTITY)
        }
    }

    @Test
    fun `test createTicketBasketItems, should serialize and deserialize correctly`() {
        every { basketItemService.createTicketBasketItems(any()) } just Runs

        val primaryTicketDiscountId = 1.toUUID()
        val secondaryTicketDiscountId = 2.toUUID()
        val request = CreateTicketBasketItemsRequest(
            screeningId = SCREENING_1_ID,
            seatIds = setOf(SEAT_1_ID, SEAT_2_ID),
            priceCategoryItemNumber = PriceCategoryItemNumber.PRICE_1,
            primaryTicketDiscountId = primaryTicketDiscountId,
            secondaryTicketDiscountId = secondaryTicketDiscountId,
            isGroupTicket = true
        )

        mvc.post("${CREATE_BASKET_ITEMS_PATH(BASKET.id)}/batch") {
            contentType = MediaType.APPLICATION_JSON
            header(HttpHeaders.ACCEPT, ApiVersion.VERSION_1_JSON)
            header(HttpHeaders.AUTHORIZATION, mockAccessToken())
            content = """
            {
              "screeningId": "$SCREENING_1_ID",
              "seatIds": ["$SEAT_1_ID", "$SEAT_2_ID"],
              "priceCategoryItemNumber": "${PriceCategoryItemNumber.PRICE_1}",
              "primaryTicketDiscountId": "$primaryTicketDiscountId",
              "secondaryTicketDiscountId": "$secondaryTicketDiscountId",
              "isGroupTicket": true
            }
            """.trimIndent()
        }.andExpect {
            status { isNoContent() }
        }

        verifySequence {
            basketItemService.createTicketBasketItems(
                CreateTicketBasketItemsCommand(
                    basketId = BASKET.id,
                    screeningId = request.screeningId,
                    priceCategoryItemNumber = request.priceCategoryItemNumber,
                    seatIds = request.seatIds,
                    primaryTicketDiscountId = primaryTicketDiscountId,
                    secondaryTicketDiscountId = secondaryTicketDiscountId,
                    isGroupTicket = true
                )
            )
        }
    }

    @Test
    fun `test createBasketItemsFromGroupReservation, should serialize and deserialize correctly`() {
        every { basketItemService.createBasketItemsFromGroupReservation(any()) } just Runs
        every { basketJpaFinderService.getNonDeletedById(any()) } returns BASKET
        every { basketResponseMapper.mapSingle(any()) } returns BASKET_RESPONSE_THREE_ITEMS

        mvc.post("${CREATE_BASKET_ITEMS_PATH(BASKET.id)}/reservations") {
            contentType = MediaType.APPLICATION_JSON
            header(HttpHeaders.ACCEPT, ApiVersion.VERSION_1_JSON)
            header(HttpHeaders.AUTHORIZATION, mockAccessToken())
            content = """
            {
              "groupReservationId": "${1.toUUID()}",
              "priceCategoryItemNumber": "${PriceCategoryItemNumber.PRICE_1}"
            }
            """.trimIndent()
        }.andExpect {
            status { isOk() }
            content { contentType(ApiVersion.VERSION_1_JSON) }
            jsonContent(
                """
            {
              "id": "${BASKET.id}",
              "createdAt": "${BASKET.createdAt.truncatedAndFormatted()}",
              "tableId": "$TABLE_1_ID",
              "state": "${BASKET.state}",
              "totalPrice": ${BASKET.totalPrice},
              "items": [
                {
                  "id": "${BASKET_ITEM_1.id}",
                  "type": "${BASKET_ITEM_1.type}",
                  "price": ${BASKET_ITEM_1.price},
                  "quantity": ${BASKET_ITEM_1.quantity},
                  "ticket": {
                    "ticketPrice": {
                      "id": "${TICKET_PRICE_1.id}",
                      "basePrice": ${TICKET_PRICE_1.basePrice},
                      "basePriceItemNumber": "${TICKET_PRICE_1.basePriceItemNumber}",
                      "totalPrice": ${TICKET_PRICE_1.totalPrice}
                    },
                    "reservation": {
                      "seat": {
                        "id": "${RESERVATION_1.seatId}",
                        "row": "${RESERVATION_RESPONSE_1.seat.row}",
                        "number": "${RESERVATION_RESPONSE_1.seat.number}"
                      },
                      "state": "${RESERVATION_1.state}"
                    }
                  }
                },
                {
                  "id": "${BASKET_ITEM_2.id}",
                  "type": "${BASKET_ITEM_2.type}",
                  "price": ${BASKET_ITEM_2.price},
                  "quantity": ${BASKET_ITEM_2.quantity},
                  "ticket": {
                    "ticketPrice": {
                      "id": "${TICKET_PRICE_2.id}",
                      "basePrice": ${TICKET_PRICE_2.basePrice},
                      "basePriceItemNumber": "${TICKET_PRICE_2.basePriceItemNumber}",
                      "totalPrice": ${TICKET_PRICE_2.totalPrice}
                    },
                    "reservation": {
                      "seat": {
                        "id": "${RESERVATION_2.seatId}",
                        "row": "${RESERVATION_RESPONSE_2.seat.row}",
                        "number": "${RESERVATION_RESPONSE_2.seat.number}"
                      },
                      "state": "${RESERVATION_2.state}"
                    }
                  }
                },
                {
                  "id": "${BASKET_ITEM_3.id}",
                  "type": "${BASKET_ITEM_3.type}",
                  "price": ${BASKET_ITEM_3.price},
                  "quantity": ${BASKET_ITEM_3.quantity},
                  "product": {
                    "id": "${BASKET_ITEM_3.productId}",
                    "title": "${PRODUCT_1.title}",
                    "price": ${PRODUCT_1.price}
                  }
                }
              ]
            }
          """
            )
        }

        verifySequence {
            basketItemService.createBasketItemsFromGroupReservation(
                CreateBasketItemsFromGroupReservationCommand(
                    basketId = BASKET.id,
                    groupReservationId = 1.toUUID(),
                    priceCategoryItemNumber = PriceCategoryItemNumber.PRICE_1
                )
            )
            basketJpaFinderService.getNonDeletedById(BASKET.id)
            basketResponseMapper.mapSingle(BASKET)
        }
    }
}

private val CREATE_BASKET_ITEMS_PATH: (UUID) -> String = { basketId -> "/pos-app/baskets/$basketId/items" }
private val DELETE_AND_PATCH_PATH: (UUID, UUID) -> String = { basketId, basketItemId ->
    "/pos-app/baskets/$basketId/items/$basketItemId"
}

private val SCREENING_1_ID = UUID.fromString("6dcb2299-60bb-4c53-b4ec-359f58b90fe5")
private val TABLE_1_ID = UUID.fromString("2d96de85-8965-44f0-b6ca-c4cff035ace4")
private val SEAT_1_ID = UUID.fromString("5a9b0f40-b793-4184-b9e4-e19f19e09605")
private val SEAT_2_ID = UUID.fromString("285a68f2-2b7b-4e61-8b64-0a6916eb6377")

private val BASKET_NO_ITEM_2_TOTAL_PRICE = BigDecimal(14.0)
private val BASKET_NO_ITEM_3_TOTAL_PRICE = BigDecimal(12.6)
private const val QUANTITY_2 = 2

private val BASKET = createBasket(
    tableId = TABLE_1_ID,
    totalPrice = BigDecimal(12.0),
    state = BasketState.OPEN
)
private val BASKET_NO_ITEM_2 = Basket(
    id = BASKET.id,
    tableId = BASKET.tableId,
    totalPrice = BigDecimal(14.0),
    state = BASKET.state
)
private val BASKET_NO_ITEM_3 = Basket(
    id = BASKET.id,
    tableId = BASKET.tableId,
    totalPrice = BigDecimal(12.6),
    state = BASKET.state
)
private val BASKET_BASKET_ITEM_3_INC_QUANTITY = Basket(
    id = BASKET.id,
    tableId = BASKET.tableId,
    totalPrice = BigDecimal(23.8),
    state = BASKET.state
)
private val RESERVATION_1 = createReservation(
    screeningId = BASKET.id,
    seatId = SEAT_1_ID
)
private val RESERVATION_2 = createReservation(
    screeningId = BASKET.id,
    seatId = SEAT_2_ID
)
private val PRODUCT_1 = createProduct(
    originalId = 1,
    productCategoryId = UUID.randomUUID(),
    title = "Coca Cola 0.33 l",
    order = 23,
    type = ProductType.PRODUCT,
    price = BigDecimal.valueOf(3.5)
)
private val RESERVATION_RESPONSE_1 = BasketItemReservationResponse(
    seat = BasketItemSeatResponse(SEAT_1_ID, "1", "1"),
    state = ReservationState.RESERVED
)
private val RESERVATION_RESPONSE_2 = BasketItemReservationResponse(
    seat = BasketItemSeatResponse(SEAT_2_ID, "1", "2"),
    state = ReservationState.RESERVED
)
private val PRODUCT_RESPONSE_1 = BasketItemProductResponse(
    id = PRODUCT_1.id,
    title = PRODUCT_1.title,
    price = PRODUCT_1.price
)
private val TICKET_PRICE_1 = createTicketPrice(
    screeningId = SCREENING_1_ID,
    seatId = SEAT_1_ID,
    basePrice = BigDecimal.valueOf(5),
    basePriceItemNumber = PriceCategoryItemNumber.PRICE_1,
    seatSurchargeType = SeatSurchargeType.VIP,
    seatSurcharge = BigDecimal.ONE,
    totalPrice = BigDecimal.valueOf(6)
)
private val TICKET_PRICE_2 = createTicketPrice(
    screeningId = SCREENING_1_ID,
    seatId = SEAT_2_ID,
    basePrice = BigDecimal.valueOf(5),
    basePriceItemNumber = PriceCategoryItemNumber.PRICE_1,
    totalPrice = BigDecimal.valueOf(5)
)
private val TICKET_1 = createTicket(
    screeningId = SCREENING_1_ID,
    reservationId = RESERVATION_1.id,
    ticketPriceId = TICKET_PRICE_1.id
)
private val TICKET_2 = createTicket(
    screeningId = SCREENING_1_ID,
    reservationId = RESERVATION_2.id,
    ticketPriceId = TICKET_PRICE_2.id
)
private val BASKET_ITEM_TICKET_RESPONSE_1 = BasketItemTicketResponse(
    ticketPrice = mapToBasketItemTicketPriceResponse(TICKET_PRICE_1),
    reservation = RESERVATION_RESPONSE_1,
    screeningId = SCREENING_1_ID,
    isGroupTicket = false
)
private val BASKET_ITEM_TICKET_RESPONSE_2 = BasketItemTicketResponse(
    ticketPrice = mapToBasketItemTicketPriceResponse(TICKET_PRICE_2),
    reservation = RESERVATION_RESPONSE_2,
    screeningId = SCREENING_1_ID,
    isGroupTicket = false
)
private val BASKET_ITEM_1 = createBasketItem(
    basketId = BASKET.id,
    ticketId = TICKET_1.id,
    type = BasketItemType.TICKET,
    price = BigDecimal(8.4),
    quantity = 1
)
private val BASKET_ITEM_2 = createBasketItem(
    basketId = BASKET.id,
    type = BasketItemType.TICKET,
    ticketId = TICKET_2.id,
    price = BigDecimal(4.2),
    quantity = 1
)
private val BASKET_ITEM_3 = createBasketItem(
    basketId = BASKET.id,
    type = BasketItemType.PRODUCT,
    productId = PRODUCT_1.id,
    price = BigDecimal(5.6),
    quantity = 2
)
private val BASKET_ITEM_3_INC_QUANTITY = BasketItem(
    id = BASKET_ITEM_3.id,
    basketId = BASKET_ITEM_3.basketId,
    type = BASKET_ITEM_3.type,
    price = BASKET_ITEM_3.price,
    vatAmount = 0.toBigDecimal(),
    quantity = QUANTITY_2,
    isCancelled = false
)
private val BASKET_ITEM_REQUEST_2 = mapToBasketItemRequest(
    basketItem = BASKET_ITEM_2,
    ticketPrice = TICKET_PRICE_2,
    screeningId = SCREENING_1_ID
)
private val BASKET_ITEM_REQUEST_3 = mapToBasketItemRequest(
    basketItem = BASKET_ITEM_3,
    productId = PRODUCT_1.id,
    screeningId = SCREENING_1_ID
)
private val BASKET_ITEM_RESPONSE_1 = mapToBasketItemResponse(
    basketItem = BASKET_ITEM_1,
    ticketResponse = BASKET_ITEM_TICKET_RESPONSE_1
)
private val BASKET_ITEM_RESPONSE_2 = mapToBasketItemResponse(
    basketItem = BASKET_ITEM_2,
    ticketResponse = BASKET_ITEM_TICKET_RESPONSE_2
)
private val BASKET_ITEM_RESPONSE_3 = mapToBasketItemResponse(
    basketItem = BASKET_ITEM_3,
    productResponse = PRODUCT_RESPONSE_1
)
private val BASKET_ITEM_RESPONSE_3_INC_QUANTITY = BasketItemResponse(
    id = BASKET_ITEM_3_INC_QUANTITY.id,
    type = BASKET_ITEM_3_INC_QUANTITY.type,
    price = BASKET_ITEM_3_INC_QUANTITY.price,
    quantity = QUANTITY_2,
    originalPrice = BASKET_ITEM_3_INC_QUANTITY.price
)
private val BASKET_RESPONSE_NO_ITEM_3 = mapToBasketResponse(
    basket = BASKET_NO_ITEM_3,
    itemResponses = listOf(
        BASKET_ITEM_RESPONSE_1,
        BASKET_ITEM_RESPONSE_2
    )
)
private val BASKET_RESPONSE_NO_ITEM_2 = mapToBasketResponse(
    basket = BASKET_NO_ITEM_2,
    itemResponses = listOf(
        BASKET_ITEM_RESPONSE_1,
        BASKET_ITEM_RESPONSE_3
    )
)
private val BASKET_RESPONSE_THREE_ITEMS = mapToBasketResponse(
    basket = BASKET,
    itemResponses = listOf(
        BASKET_ITEM_RESPONSE_1,
        BASKET_ITEM_RESPONSE_2,
        BASKET_ITEM_RESPONSE_3
    )
)
private val BASKET_RESPONSE_ITEM_3_INC_QUANTITY = mapToBasketResponse(
    basket = BASKET_BASKET_ITEM_3_INC_QUANTITY,
    itemResponses = listOf(
        BASKET_ITEM_RESPONSE_1,
        BASKET_ITEM_RESPONSE_2,
        BASKET_ITEM_RESPONSE_3_INC_QUANTITY
    )
)
