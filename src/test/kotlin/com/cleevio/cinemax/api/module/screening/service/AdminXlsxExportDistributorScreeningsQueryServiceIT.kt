package com.cleevio.cinemax.api.module.screening.service

import com.cleevio.cinemax.api.DatabaseCleanup
import com.cleevio.cinemax.api.INTEGRATION_TEST_DATE_TIME
import com.cleevio.cinemax.api.IntegrationTest
import com.cleevio.cinemax.api.common.constant.ExportFormat
import com.cleevio.cinemax.api.common.constant.PaymentType
import com.cleevio.cinemax.api.common.util.isEqualTo
import com.cleevio.cinemax.api.module.auditorium.service.AuditoriumRepository
import com.cleevio.cinemax.api.module.auditoriumlayout.service.AuditoriumLayoutRepository
import com.cleevio.cinemax.api.module.basket.constant.BasketState
import com.cleevio.cinemax.api.module.basket.service.BasketRepository
import com.cleevio.cinemax.api.module.basketitem.constant.BasketItemType
import com.cleevio.cinemax.api.module.basketitem.service.BasketItemRepository
import com.cleevio.cinemax.api.module.distributor.service.DistributorRepository
import com.cleevio.cinemax.api.module.language.entity.Language
import com.cleevio.cinemax.api.module.language.service.LanguageRepository
import com.cleevio.cinemax.api.module.movie.service.MovieRepository
import com.cleevio.cinemax.api.module.posconfiguration.service.PosConfigurationRepository
import com.cleevio.cinemax.api.module.pricecategory.service.PriceCategoryRepository
import com.cleevio.cinemax.api.module.pricecategoryitem.constant.PriceCategoryItemNumber
import com.cleevio.cinemax.api.module.pricecategoryitem.service.PriceCategoryItemRepository
import com.cleevio.cinemax.api.module.rating.entity.Rating
import com.cleevio.cinemax.api.module.rating.service.RatingRepository
import com.cleevio.cinemax.api.module.reservation.constant.ReservationState
import com.cleevio.cinemax.api.module.reservation.service.ReservationRepository
import com.cleevio.cinemax.api.module.screening.constant.ScreeningState
import com.cleevio.cinemax.api.module.screening.service.query.AdminExportDistributorScreeningsFilter
import com.cleevio.cinemax.api.module.screening.service.query.AdminExportDistributorScreeningsQuery
import com.cleevio.cinemax.api.module.screeningfee.service.ScreeningFeeRepository
import com.cleevio.cinemax.api.module.screeningtype.service.ScreeningTypeRepository
import com.cleevio.cinemax.api.module.screeningtypes.service.ScreeningTypesRepository
import com.cleevio.cinemax.api.module.seat.constant.SeatType
import com.cleevio.cinemax.api.module.seat.service.SeatRepository
import com.cleevio.cinemax.api.module.technology.entity.Technology
import com.cleevio.cinemax.api.module.technology.service.TechnologyRepository
import com.cleevio.cinemax.api.module.ticket.service.TicketRepository
import com.cleevio.cinemax.api.module.ticketdiscount.constant.TicketDiscountType
import com.cleevio.cinemax.api.module.ticketdiscount.service.TicketDiscountRepository
import com.cleevio.cinemax.api.module.ticketprice.constant.SeatSurchargeType
import com.cleevio.cinemax.api.module.ticketprice.service.TicketPriceRepository
import com.cleevio.cinemax.api.module.tmslanguage.entity.TmsLanguage
import com.cleevio.cinemax.api.module.tmslanguage.service.TmsLanguageRepository
import com.cleevio.cinemax.api.util.copyBasketItem
import com.cleevio.cinemax.api.util.createAuditorium
import com.cleevio.cinemax.api.util.createAuditoriumLayout
import com.cleevio.cinemax.api.util.createBasket
import com.cleevio.cinemax.api.util.createBasketItem
import com.cleevio.cinemax.api.util.createDistributor
import com.cleevio.cinemax.api.util.createMovie
import com.cleevio.cinemax.api.util.createPosConfiguration
import com.cleevio.cinemax.api.util.createPriceCategory
import com.cleevio.cinemax.api.util.createPriceCategoryItem
import com.cleevio.cinemax.api.util.createReservation
import com.cleevio.cinemax.api.util.createScreening
import com.cleevio.cinemax.api.util.createScreeningFee
import com.cleevio.cinemax.api.util.createScreeningType
import com.cleevio.cinemax.api.util.createScreeningTypes
import com.cleevio.cinemax.api.util.createSeat
import com.cleevio.cinemax.api.util.createTicket
import com.cleevio.cinemax.api.util.createTicketDiscount
import com.cleevio.cinemax.api.util.createTicketPrice
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.api.BeforeAll
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import java.math.BigDecimal
import java.math.RoundingMode
import java.time.LocalDate
import java.time.LocalTime
import kotlin.test.assertEquals

class AdminXlsxExportDistributorScreeningsQueryServiceIT @Autowired constructor(
    private val underTest: AdminXlsxExportDistributorScreeningsQueryService,
    private val auditoriumRepository: AuditoriumRepository,
    private val auditoriumLayoutRepository: AuditoriumLayoutRepository,
    private val ratingRepository: RatingRepository,
    private val distributorRepository: DistributorRepository,
    private val technologyRepository: TechnologyRepository,
    private val tmsLanguageRepository: TmsLanguageRepository,
    private val languageRepository: LanguageRepository,
    private val priceCategoryRepository: PriceCategoryRepository,
    private val priceCategoryItemRepository: PriceCategoryItemRepository,
    private val screeningTypeRepository: ScreeningTypeRepository,
    private val screeningRepository: ScreeningRepository,
    private val screeningTypesRepository: ScreeningTypesRepository,
    private val screeningFeeRepository: ScreeningFeeRepository,
    private val seatRepository: SeatRepository,
    private val reservationRepository: ReservationRepository,
    private val ticketRepository: TicketRepository,
    private val ticketPriceRepository: TicketPriceRepository,
    private val movieRepository: MovieRepository,
    private val basketRepository: BasketRepository,
    private val basketItemRepository: BasketItemRepository,
    private val posConfigurationRepository: PosConfigurationRepository,
    private val ticketDiscountRepository: TicketDiscountRepository,
) : IntegrationTest(DatabaseCleanup.AFTER_ALL) {

    @BeforeAll
    fun setUp() {
        auditoriumRepository.saveAll(setOf(AUDITORIUM_1, AUDITORIUM_2))
        auditoriumLayoutRepository.saveAll(setOf(AUDITORIUM_LAYOUT_1, AUDITORIUM_LAYOUT_2))
        distributorRepository.saveAll(setOf(DISTRIBUTOR_1, DISTRIBUTOR_2, DISTRIBUTOR_3))
        ratingRepository.saveAll(setOf(RATING_1, RATING_2))
        technologyRepository.saveAll(setOf(TECHNOLOGY_1, TECHNOLOGY_2))
        languageRepository.saveAll(setOf(LANGUAGE_1, LANGUAGE_2))
        tmsLanguageRepository.saveAll(setOf(TMS_LANGUAGE_1, TMS_LANGUAGE_2))
        movieRepository.saveAll(setOf(MOVIE_1, MOVIE_2, MOVIE_3, MOVIE_4))
        priceCategoryRepository.save(PRICE_CATEGORY_1)
        screeningRepository.saveAll(
            setOf(
                SCREENING_1,
                SCREENING_2_DELETED,
                SCREENING_3_CANCELLED,
                SCREENING_4_STOPPED,
                SCREENING_5,
                SCREENING_6,
                SCREENING_7
            )
        )
        screeningFeeRepository.saveAll(setOf(SCREENING_FEE_1, SCREENING_FEE_5, SCREENING_FEE_6, SCREENING_FEE_7))
        priceCategoryItemRepository.saveAll(setOf(PRICE_CATEGORY_ITEM_1, PRICE_CATEGORY_ITEM_2))
        screeningTypeRepository.saveAll(setOf(SCREENING_TYPE_1, SCREENING_TYPE_2))
        screeningTypesRepository.saveAll(setOf(SCREENING_TYPES_1, SCREENING_TYPES_2))
        seatRepository.saveAll(setOf(SEAT_1, SEAT_2, SEAT_3, SEAT_4, SEAT_5))
        reservationRepository.saveAll(setOf(RESERVATION_1, RESERVATION_2, RESERVATION_3, RESERVATION_4, RESERVATION_5, RESERVATION_6))
        ticketDiscountRepository.saveAll(setOf(TICKET_DISCOUNT_1, TICKET_DISCOUNT_2, TICKET_DISCOUNT_3))
        ticketPriceRepository.saveAll(setOf(TICKET_PRICE_1, TICKET_PRICE_2, TICKET_PRICE_3, TICKET_PRICE_4, TICKET_PRICE_5, TICKET_PRICE_6))
        ticketRepository.saveAll(setOf(TICKET_1, TICKET_2, TICKET_3, TICKET_4, TICKET_5, TICKET_6))
        posConfigurationRepository.save(POS_CONFIGURATION_1)
        basketRepository.save(BASKET_1_POS_1)
        basketItemRepository.saveAll(
            setOf(
                BASKET_ITEM_TICKET_1_BASKET_1,
                BASKET_ITEM_TICKET_2_BASKET_1,
                BASKET_ITEM_TICKET_3_BASKET_1,
                BASKET_ITEM_TICKET_4_BASKET_1,
                BASKET_ITEM_TICKET_5_BASKET_1,
                BASKET_ITEM_TICKET_6_BASKET_1
            )
        )
    }

    @Test
    fun `test AdminExportDistributorScreeningsQuery - distributorIds filter is not empty or null - should return list of records each related to one distributor`() {
        val responseList = underTest(
            AdminExportDistributorScreeningsQuery(
                filter = AdminExportDistributorScreeningsFilter(
                    dateFrom = LocalDate.EPOCH,
                    dateTo = INTEGRATION_TEST_DATE_TIME.toLocalDate().plusYears(1),
                    distributorIds = setOf(DISTRIBUTOR_1.id, DISTRIBUTOR_2.id)
                ),
                exportFormat = ExportFormat.XLSX,
                username = USERNAME
            )
        )

        assertEquals(2, responseList.size)

        responseList.first { it.distributorTitle == DISTRIBUTOR_1.title }.let { distributor1Response ->
            distributor1Response.movies.first { it.movieTitle == MOVIE_1.rawTitle }
                .let { aggregatedMovie1 ->
                    aggregatedMovie1.screenings.first { it.screeningDate == SCREENING_1.date }
                        .let { aggregatedScreening1 ->
                            assertEquals(SCREENING_1.date, aggregatedScreening1.screeningDate)
                            assertEquals(1, aggregatedScreening1.ticketsCount)
                            assertTrue(47.0.toBigDecimal() isEqualTo aggregatedScreening1.groTicketSales)
                            assertTrue(32.4785.toBigDecimal() isEqualTo aggregatedScreening1.netTicketsSales)
                            assertEquals(SCREENING_1.distributorCommission, aggregatedScreening1.distributorCommission)
                        }
                }
        }

        responseList.first { it.distributorTitle == DISTRIBUTOR_2.title }.let { distributor2Response ->
            distributor2Response.movies.first { it.movieTitle == MOVIE_2.rawTitle }
                .let { aggregatedMovie2 ->
                    aggregatedMovie2.screenings.first { it.screeningDate == SCREENING_6.date }
                        .let { aggregatedScreening2 ->
                            assertEquals(SCREENING_6.date, aggregatedScreening2.screeningDate)
                            assertEquals(4, aggregatedScreening2.ticketsCount)
                            assertTrue(280.0.toBigDecimal() isEqualTo aggregatedScreening2.groTicketSales)
                            assertTrue(227.64.toBigDecimal() isEqualTo aggregatedScreening2.netTicketsSales)
                            assertEquals(SCREENING_6.distributorCommission, aggregatedScreening2.distributorCommission)
                        }
                }
        }
    }

    @Test
    fun `test AdminExportDistributorScreeningsQuery - distributorIds filter is null or empty - should return one record with aggregated values for all distributors`() {
        val responseList = underTest(
            AdminExportDistributorScreeningsQuery(
                filter = AdminExportDistributorScreeningsFilter(
                    dateFrom = LocalDate.EPOCH,
                    dateTo = INTEGRATION_TEST_DATE_TIME.toLocalDate().plusYears(1),
                    distributorIds = null
                ),
                exportFormat = ExportFormat.XLSX,
                username = USERNAME
            )
        )

        assertEquals(1, responseList.size)
        val aggregatedResponse = responseList.first()

        assertEquals(3, responseList[0].movies.size)

        aggregatedResponse.movies.first { it.movieTitle == MOVIE_1.rawTitle }.let { movie ->
            movie.screenings.first { it.screeningDate == SCREENING_1.date }.let { screening ->
                assertEquals(SCREENING_1.date, screening.screeningDate)
                assertEquals(1, screening.ticketsCount)
                assertEquals(47.0.toBigDecimal(), screening.groTicketSales)
                assertEquals(32.4785.toBigDecimal(), screening.netTicketsSales)
                assertEquals(SCREENING_1.distributorCommission, screening.distributorCommission)
            }
        }

        aggregatedResponse.movies.first { it.movieTitle == MOVIE_2.rawTitle }.let { movie ->
            movie.screenings.first { it.screeningDate == SCREENING_6.date }.let { screening ->
                assertEquals(SCREENING_6.date, screening.screeningDate)
                assertEquals(4, screening.ticketsCount)
                assertTrue(280.0.toBigDecimal() isEqualTo screening.groTicketSales)
                assertTrue(227.64.toBigDecimal() isEqualTo screening.netTicketsSales)
                assertEquals(SCREENING_6.distributorCommission, screening.distributorCommission)
            }
        }

        aggregatedResponse.movies.first { it.movieTitle == MOVIE_4.rawTitle }.let { movie ->
            movie.screenings.first { it.screeningDate == SCREENING_5.date }.let { screening ->
                assertEquals(SCREENING_5.date, screening.screeningDate)
                assertEquals(1, screening.ticketsCount)
                assertTrue(25.toBigDecimal() isEqualTo screening.groTicketSales)
                assertTrue(20.33.toBigDecimal() isEqualTo screening.netTicketsSales)
                assertEquals(SCREENING_5.distributorCommission, screening.distributorCommission)
            }
        }
    }

    @Test
    fun `test AdminExportDistributorScreeningsQuery - filter with dateTo and dateFrom - should return correct records`() {
        val dateFrom = INTEGRATION_TEST_DATE_TIME.toLocalDate().minusDays(3)
        val dateTo = INTEGRATION_TEST_DATE_TIME.toLocalDate().plusDays(4)

        val responseList = underTest(
            AdminExportDistributorScreeningsQuery(
                filter = AdminExportDistributorScreeningsFilter(
                    dateFrom = dateFrom,
                    dateTo = dateTo,
                    distributorIds = null
                ),
                exportFormat = ExportFormat.XLSX,
                username = USERNAME
            )
        )

        assertEquals(1, responseList.size)
        assertEquals(1, responseList[0].movies.size)
        assertEquals(1, responseList[0].movies[0].screenings.size)
        assertEquals(responseList[0].movies[0].screenings[0].screeningDate, SCREENING_1.date)
    }

    @Test
    fun `test AdminExportDistributorScreeningsQuery - dBoxOnly=true, distributorIds filter is null or empty - should return one record with aggregated values for all distributors`() {
        val responseList = underTest(
            AdminExportDistributorScreeningsQuery(
                filter = AdminExportDistributorScreeningsFilter(
                    dateFrom = LocalDate.EPOCH,
                    dateTo = INTEGRATION_TEST_DATE_TIME.toLocalDate().plusYears(1),
                    distributorIds = null,
                    dBoxOnly = true
                ),
                exportFormat = ExportFormat.XLSX,
                username = USERNAME
            )
        )

        assertEquals(1, responseList.size)
        val aggregatedResponse = responseList.first()

        assertEquals(1, responseList[0].movies.size)

        // SCREENING_1 has no ticket sold on DBOX seat but surchargeDBox=0 -> related movie not present in result
        // SCREENING_5 has tickets sold on DBOX seats -> related movie is present in result
        // SCREENING_6 has no tickets sold on DBOX seats -> related movie not present in result
        // SCREENING_7 has the same movie as SCREENING_5, but not DBox surcharge -> screening count not present in result

        aggregatedResponse.movies.first { it.movieTitle == MOVIE_4.rawTitle }.let { movie ->
            assertEquals(1, movie.screenings.size)

            movie.screenings.first { it.screeningDate == SCREENING_5.date }.let { screening ->
                assertEquals(SCREENING_5.date, screening.screeningDate)
                assertEquals(1, screening.ticketsCount)
                assertTrue(25.0.toBigDecimal() isEqualTo screening.groTicketSales)
                assertTrue(20.33.toBigDecimal() isEqualTo screening.netTicketsSales.setScale(4, RoundingMode.DOWN))
                assertEquals(SCREENING_5.distributorCommission, screening.distributorCommission)
            }
        }
    }
}

private const val USERNAME = "username"
private val LOCAL_TIME = LocalTime.of(20, 0, 0)
private val AUDITORIUM_1 = createAuditorium(
    originalId = 1,
    code = "A",
    title = "SÁLA A - CINEMAX BRATISLAVA",
    originalCode = 22
)
private val AUDITORIUM_2 = createAuditorium(
    originalId = 2,
    code = "IMAX",
    title = "IMAX - CINEMAX BRATISLAVA",
    capacity = 150,
    originalCode = 11
)
private val DISTRIBUTOR_1 = createDistributor(originalId = 1)
private val DISTRIBUTOR_2 = createDistributor(originalId = 2, title = "Hello movies, s.r.o.")
private val DISTRIBUTOR_3 = createDistributor(originalId = 3, title = "GoodBye movies, s.r.o.")
private val AUDITORIUM_LAYOUT_1 = createAuditoriumLayout(originalId = 1, auditoriumId = AUDITORIUM_1.id)
private val AUDITORIUM_LAYOUT_2 = createAuditoriumLayout(
    originalId = 2,
    auditoriumId = AUDITORIUM_2.id,
    title = "Special"
)
private val RATING_1 = Rating(
    originalId = 1,
    code = "13",
    title = "13"
)
private val RATING_2 = Rating(
    originalId = 2,
    code = "16",
    title = "16"
)
private val TECHNOLOGY_1 = Technology(
    originalId = 1,
    title = "2D",
    code = "2DA"
)
private val LANGUAGE_1 = Language(
    originalId = 10,
    title = "slovak",
    code = "SVK"
)
private val TMS_LANGUAGE_1 = TmsLanguage(
    originalId = 1,
    title = "Slovenske zneni",
    code = "TMS2"
)
private val TECHNOLOGY_2 = Technology(
    originalId = 2,
    title = "3D IMAX",
    code = "3I"
)
private val LANGUAGE_2 = Language(
    originalId = 11,
    title = "czech",
    code = "CZE"
)
private val TMS_LANGUAGE_2 = TmsLanguage(
    originalId = 2,
    title = "Ceske zneni",
    code = "TMS1"
)
private val MOVIE_1 = createMovie(
    originalId = 1,
    title = "Star Wars: Episode I – The Phantom Menace",
    releaseYear = 2001,
    distributorId = DISTRIBUTOR_1.id,
    ratingId = RATING_1.id,
    technologyId = TECHNOLOGY_1.id,
    languageId = LANGUAGE_1.id,
    tmsLanguageId = TMS_LANGUAGE_1.id
)
private val MOVIE_2 = createMovie(
    originalId = 2,
    code = "873946",
    title = "Matrix",
    rawTitle = "Matrix 2D (ST)",
    releaseYear = 2000,
    distributorId = DISTRIBUTOR_2.id,
    duration = 150,
    ratingId = RATING_2.id,
    technologyId = TECHNOLOGY_2.id,
    languageId = LANGUAGE_2.id,
    tmsLanguageId = TMS_LANGUAGE_2.id
)
private val MOVIE_3 = createMovie(
    originalId = 3,
    code = "873947",
    title = "Without screening",
    rawTitle = "Without Screening 2D (ST)",
    releaseYear = 2000,
    distributorId = DISTRIBUTOR_1.id,
    duration = 150,
    ratingId = RATING_2.id,
    technologyId = TECHNOLOGY_2.id,
    languageId = LANGUAGE_2.id,
    tmsLanguageId = TMS_LANGUAGE_2.id
)
private val MOVIE_4 = createMovie(
    originalId = 4,
    code = "873999",
    title = "Lego movie",
    rawTitle = "Lego Movie IMAX 3D (SD)",
    releaseYear = 2010,
    distributorId = DISTRIBUTOR_3.id,
    duration = 150,
    ratingId = RATING_2.id,
    technologyId = TECHNOLOGY_2.id,
    languageId = LANGUAGE_2.id,
    tmsLanguageId = TMS_LANGUAGE_2.id
)
private val PRICE_CATEGORY_1 = createPriceCategory(
    originalId = 1,
    title = "ARTMAX PO 17",
    active = true
)
private val SCREENING_1 = createScreening(
    originalId = 1,
    auditoriumId = AUDITORIUM_1.id,
    auditoriumLayoutId = AUDITORIUM_LAYOUT_1.id,
    movieId = MOVIE_1.id,
    priceCategoryId = PRICE_CATEGORY_1.id,
    date = INTEGRATION_TEST_DATE_TIME.toLocalDate(),
    time = LOCAL_TIME.plusHours(2).plusMinutes(1),
    proCommission = 5,
    filmFondCommission = 10
)
private val SCREENING_2_DELETED = createScreening(
    originalId = 2,
    auditoriumId = AUDITORIUM_1.id,
    auditoriumLayoutId = AUDITORIUM_LAYOUT_1.id,
    movieId = MOVIE_2.id,
    priceCategoryId = PRICE_CATEGORY_1.id,
    state = ScreeningState.PUBLISHED,
    stopped = true
).apply { markDeleted() }
private val SCREENING_3_CANCELLED = createScreening(
    originalId = 3,
    auditoriumId = AUDITORIUM_1.id,
    auditoriumLayoutId = AUDITORIUM_LAYOUT_1.id,
    movieId = MOVIE_2.id,
    priceCategoryId = PRICE_CATEGORY_1.id,
    state = ScreeningState.PUBLISHED,
    cancelled = true
)
private val SCREENING_4_STOPPED = createScreening(
    originalId = 4,
    auditoriumId = AUDITORIUM_1.id,
    auditoriumLayoutId = AUDITORIUM_LAYOUT_1.id,
    movieId = MOVIE_2.id,
    priceCategoryId = PRICE_CATEGORY_1.id,
    state = ScreeningState.PUBLISHED,
    stopped = true
)
private val SCREENING_5 = createScreening(
    originalId = 5,
    auditoriumId = AUDITORIUM_1.id,
    auditoriumLayoutId = AUDITORIUM_LAYOUT_1.id,
    movieId = MOVIE_4.id,
    priceCategoryId = PRICE_CATEGORY_1.id,
    state = ScreeningState.PUBLISHED,
    date = INTEGRATION_TEST_DATE_TIME.toLocalDate().plusDays(5),
    time = LOCAL_TIME.plusHours(3).plusMinutes(10),
    proCommission = 0,
    filmFondCommission = 0
)
private val SCREENING_6 = createScreening(
    originalId = 6,
    auditoriumId = AUDITORIUM_2.id,
    auditoriumLayoutId = AUDITORIUM_LAYOUT_2.id,
    movieId = MOVIE_2.id,
    priceCategoryId = PRICE_CATEGORY_1.id,
    state = ScreeningState.PUBLISHED,
    date = INTEGRATION_TEST_DATE_TIME.toLocalDate().minusDays(4),
    time = LOCAL_TIME.plusMinutes(15),
    proCommission = 0,
    filmFondCommission = 0
)
private val SCREENING_7 = createScreening(
    originalId = 7,
    auditoriumId = AUDITORIUM_1.id,
    auditoriumLayoutId = AUDITORIUM_LAYOUT_1.id,
    movieId = MOVIE_4.id,
    priceCategoryId = PRICE_CATEGORY_1.id,
    state = ScreeningState.PUBLISHED,
    date = INTEGRATION_TEST_DATE_TIME.toLocalDate().plusDays(5),
    time = LOCAL_TIME.plusHours(6),
    proCommission = 0,
    filmFondCommission = 0
)
private val SCREENING_FEE_1 = createScreeningFee(
    originalScreeningId = 1,
    screeningId = SCREENING_1.id,
    surchargeVip = 1.toBigDecimal(),
    surchargePremium = 0.5.toBigDecimal(),
    surchargeImax = 2.toBigDecimal(),
    surchargeUltraX = 0.5.toBigDecimal(),
    serviceFeeVip = 1.toBigDecimal(),
    serviceFeePremium = 0.5.toBigDecimal(),
    serviceFeeImax = 2.toBigDecimal(),
    serviceFeeUltraX = 0.5.toBigDecimal(),
    surchargeDBox = 0.toBigDecimal(),
    serviceFeeGeneral = 0.2.toBigDecimal()
)
private val SCREENING_FEE_5 = createScreeningFee(
    originalScreeningId = 5,
    screeningId = SCREENING_5.id,
    surchargeVip = 1.toBigDecimal(),
    surchargePremium = 0.5.toBigDecimal(),
    surchargeImax = 2.toBigDecimal(),
    surchargeUltraX = 0.5.toBigDecimal(),
    serviceFeeVip = 1.toBigDecimal(),
    serviceFeePremium = 0.5.toBigDecimal(),
    serviceFeeImax = 2.toBigDecimal(),
    serviceFeeUltraX = 0.5.toBigDecimal(),
    surchargeDBox = 5.toBigDecimal(),
    serviceFeeGeneral = 0.2.toBigDecimal()
)
private val SCREENING_FEE_6 = createScreeningFee(
    originalScreeningId = 6,
    screeningId = SCREENING_6.id,
    surchargeVip = 1.toBigDecimal(),
    surchargePremium = 0.5.toBigDecimal(),
    surchargeImax = 2.toBigDecimal(),
    surchargeUltraX = 0.5.toBigDecimal(),
    serviceFeeVip = 1.toBigDecimal(),
    serviceFeePremium = 0.5.toBigDecimal(),
    serviceFeeImax = 2.toBigDecimal(),
    serviceFeeUltraX = 0.5.toBigDecimal(),
    surchargeDBox = 5.toBigDecimal(),
    serviceFeeGeneral = 0.2.toBigDecimal()
)
private val SCREENING_FEE_7 = createScreeningFee(
    originalScreeningId = 7,
    screeningId = SCREENING_7.id,
    surchargeVip = 1.toBigDecimal(),
    surchargePremium = 0.5.toBigDecimal(),
    surchargeImax = 2.toBigDecimal(),
    surchargeUltraX = 0.5.toBigDecimal(),
    serviceFeeVip = 1.toBigDecimal(),
    serviceFeePremium = 0.5.toBigDecimal(),
    serviceFeeImax = 2.toBigDecimal(),
    serviceFeeUltraX = 0.5.toBigDecimal(),
    surchargeDBox = 0.toBigDecimal(),
    serviceFeeGeneral = 0.2.toBigDecimal()
)
private val PRICE_CATEGORY_ITEM_1 = createPriceCategoryItem(
    priceCategoryId = PRICE_CATEGORY_1.id,
    title = "Dospely",
    price = 120.toBigDecimal(),
    number = PriceCategoryItemNumber.PRICE_1
)
private val PRICE_CATEGORY_ITEM_2 = createPriceCategoryItem(
    priceCategoryId = PRICE_CATEGORY_1.id,
    title = null,
    price = 60.toBigDecimal(),
    number = PriceCategoryItemNumber.PRICE_2
)
private val SCREENING_TYPE_1 = createScreeningType(
    originalId = 1,
    title = "Best movies ever, s.r.o."
)
private val SCREENING_TYPE_2 = createScreeningType(
    originalId = 2,
    title = "Worst movies ever, s.r.o."
)
private val SCREENING_TYPES_1 = createScreeningTypes(
    screeningId = SCREENING_6.id,
    screeningTypeId = SCREENING_TYPE_1.id
)
private val SCREENING_TYPES_2 = createScreeningTypes(
    screeningId = SCREENING_6.id,
    screeningTypeId = SCREENING_TYPE_2.id
)
private val SEAT_1 = createSeat(
    originalId = 1,
    auditoriumId = AUDITORIUM_2.id,
    auditoriumLayoutId = AUDITORIUM_LAYOUT_1.id
)
private val SEAT_2 = createSeat(
    originalId = 2,
    auditoriumId = AUDITORIUM_2.id,
    auditoriumLayoutId = AUDITORIUM_LAYOUT_2.id,
    type = SeatType.VIP
)
private val SEAT_3 = createSeat(
    originalId = 3,
    auditoriumId = AUDITORIUM_1.id,
    auditoriumLayoutId = AUDITORIUM_LAYOUT_1.id,
    type = SeatType.DBOX
)
private val SEAT_4 = createSeat(
    originalId = 4,
    auditoriumId = AUDITORIUM_2.id,
    auditoriumLayoutId = AUDITORIUM_LAYOUT_1.id,
    type = SeatType.PREMIUM_PLUS
)
private val SEAT_5 = createSeat(
    originalId = 5,
    auditoriumId = AUDITORIUM_2.id,
    auditoriumLayoutId = AUDITORIUM_LAYOUT_1.id
)

private val RESERVATION_1 = createReservation(
    screeningId = SCREENING_6.id,
    seatId = SEAT_1.id,
    state = ReservationState.UNAVAILABLE
)
private val RESERVATION_2 = createReservation(
    screeningId = SCREENING_6.id,
    seatId = SEAT_2.id,
    state = ReservationState.RESERVED
)
private val RESERVATION_3 = createReservation(
    screeningId = SCREENING_1.id,
    seatId = SEAT_3.id
)
private val RESERVATION_4 = createReservation(
    screeningId = SCREENING_5.id,
    seatId = SEAT_3.id
)
private val RESERVATION_5 = createReservation(
    screeningId = SCREENING_6.id,
    seatId = SEAT_4.id,
    state = ReservationState.RESERVED
)
private val RESERVATION_6 = createReservation(
    screeningId = SCREENING_6.id,
    seatId = SEAT_5.id,
    state = ReservationState.RESERVED
)

// SCREENING_FEE_6 has surchargeDBox > 0, but there is no ticket price with seatSurchargeType=DBOX in SCREENING_6 (i.e. no ticket sold for DBOX seat)
private val TICKET_PRICE_1 = createTicketPrice(
    screeningId = SCREENING_6.id,
    seatId = SEAT_1.id,
    totalPrice = 100.toBigDecimal(),
    basePrice = 50.toBigDecimal(),
    seatSurcharge = 15.toBigDecimal(),
    auditoriumSurcharge = 13.toBigDecimal()
)
private val TICKET_PRICE_2 = createTicketPrice(
    screeningId = SCREENING_6.id,
    seatId = SEAT_2.id,
    totalPrice = 150.toBigDecimal(),
    basePrice = 70.toBigDecimal(),
    seatSurcharge = 55.toBigDecimal(),
    seatSurchargeType = SeatSurchargeType.VIP,
    auditoriumSurcharge = 28.toBigDecimal()
)

// SEAT_3 is DBOX but SCREENING_FEE_1 has surchargeDBox=0
//     -> seatSurchargeType is internally set to PREMIUM_PLUS (see TicketPriceUtil#mapSeatSurchargeTypeAndPrice())
private val TICKET_PRICE_3 = createTicketPrice(
    screeningId = SCREENING_1.id,
    seatId = SEAT_3.id,
    totalPrice = 50.toBigDecimal(),
    basePrice = 30.toBigDecimal(),
    seatSurcharge = BigDecimal.ZERO,
    seatSurchargeType = SeatSurchargeType.PREMIUM_PLUS,
    auditoriumSurcharge = 8.toBigDecimal(),
    serviceFeeGeneral = 1.toBigDecimal(),
    seatServiceFee = 1.toBigDecimal(),
    auditoriumServiceFee = 1.toBigDecimal()
)

// SEAT_3 is DBOX and SCREENING_FEE_5 has surchargeDBox>0
//     -> seatSurchargeType is set to DBOX, seatSurcharge obtains ScreeningFee#surchargeDBox value
private val TICKET_PRICE_4 = createTicketPrice(
    screeningId = SCREENING_5.id,
    seatId = SEAT_3.id,
    totalPrice = 25.toBigDecimal(),
    basePrice = 10.toBigDecimal(),
    seatSurchargeType = SeatSurchargeType.DBOX,
    seatSurcharge = 5.toBigDecimal(),
    auditoriumSurcharge = 5.toBigDecimal()
)
private val TICKET_PRICE_5 = createTicketPrice(
    screeningId = SCREENING_6.id,
    seatId = SEAT_4.id,
    totalPrice = 10.toBigDecimal(),
    basePrice = 5.toBigDecimal(),
    basePriceBeforeDiscount = 20.toBigDecimal(),
    seatSurcharge = 1.toBigDecimal(),
    seatSurchargeType = SeatSurchargeType.PREMIUM_PLUS,
    auditoriumSurcharge = 1.toBigDecimal()
)
private val TICKET_PRICE_6 = createTicketPrice(
    screeningId = SCREENING_6.id,
    seatId = SEAT_5.id,
    totalPrice = 20.toBigDecimal(),
    basePrice = 10.toBigDecimal(),
    basePriceBeforeDiscount = 40.toBigDecimal(),
    seatSurcharge = 2.toBigDecimal(),
    auditoriumSurcharge = 2.toBigDecimal()
)
private val TICKET_DISCOUNT_1 = createTicketDiscount(
    originalId = 1,
    code = "VK1",
    type = TicketDiscountType.ABSOLUTE,
    percentage = 100
)
private val TICKET_DISCOUNT_2 = createTicketDiscount(
    originalId = 2,
    code = "VK2",
    type = TicketDiscountType.ABSOLUTE,
    percentage = 50
)
private val TICKET_DISCOUNT_3 = createTicketDiscount(
    originalId = 3,
    code = "VK3",
    type = TicketDiscountType.PERCENTAGE,
    percentage = 100
)

private val TICKET_1 = createTicket(
    originalId = 1,
    screeningId = SCREENING_6.id,
    reservationId = RESERVATION_1.id,
    ticketPriceId = TICKET_PRICE_1.id,
    ticketDiscountPrimaryId = TICKET_DISCOUNT_3.id
)
private val TICKET_2 = createTicket(
    originalId = 2,
    screeningId = SCREENING_6.id,
    reservationId = RESERVATION_2.id,
    ticketPriceId = TICKET_PRICE_2.id,
    ticketDiscountSecondaryId = TICKET_DISCOUNT_2.id
)
private val TICKET_3 = createTicket(
    originalId = 3,
    screeningId = SCREENING_1.id,
    reservationId = RESERVATION_3.id, // SEAT_3
    ticketPriceId = TICKET_PRICE_3.id
)
private val TICKET_4 = createTicket(
    originalId = 4,
    screeningId = SCREENING_5.id,
    reservationId = RESERVATION_4.id, // SEAT_3
    ticketPriceId = TICKET_PRICE_4.id
)
private val TICKET_5 = createTicket(
    originalId = 5,
    screeningId = SCREENING_6.id,
    reservationId = RESERVATION_5.id,
    ticketPriceId = TICKET_PRICE_5.id,
    ticketDiscountPrimaryId = TICKET_DISCOUNT_1.id
)
private val TICKET_6 = createTicket(
    originalId = 6,
    screeningId = SCREENING_6.id,
    reservationId = RESERVATION_6.id,
    ticketPriceId = TICKET_PRICE_6.id,
    ticketDiscountSecondaryId = TICKET_DISCOUNT_1.id
)

// POS CONFIGURATIONS
private val POS_CONFIGURATION_1 = createPosConfiguration(macAddress = "AA:BB:CC:DD:EE")

// BASKETS
private val BASKET_1_POS_1 = createBasket(
    state = BasketState.PAID,
    paymentType = PaymentType.CASH,
    paymentPosConfigurationId = POS_CONFIGURATION_1.id
)

// BASKET ITEMS
private val BASKET_ITEM_TICKET_1_BASKET_1 = createBasketItem(
    basketId = BASKET_1_POS_1.id,
    type = BasketItemType.TICKET,
    price = 10.toBigDecimal(),
    quantity = 1,
    ticketId = TICKET_1.id
)
private val BASKET_ITEM_TICKET_2_BASKET_1 = copyBasketItem(BASKET_ITEM_TICKET_1_BASKET_1) {
    it.ticketId = TICKET_2.id
}
private val BASKET_ITEM_TICKET_3_BASKET_1 = copyBasketItem(BASKET_ITEM_TICKET_1_BASKET_1) {
    it.ticketId = TICKET_3.id
}
private val BASKET_ITEM_TICKET_4_BASKET_1 = copyBasketItem(BASKET_ITEM_TICKET_1_BASKET_1) {
    it.ticketId = TICKET_4.id
}
private val BASKET_ITEM_TICKET_5_BASKET_1 = copyBasketItem(BASKET_ITEM_TICKET_1_BASKET_1) {
    it.ticketId = TICKET_5.id
}
private val BASKET_ITEM_TICKET_6_BASKET_1 = copyBasketItem(BASKET_ITEM_TICKET_1_BASKET_1) {
    it.ticketId = TICKET_6.id
}
