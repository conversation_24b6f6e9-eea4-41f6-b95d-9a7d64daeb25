package com.cleevio.cinemax.api.module.ticket.service

import com.cleevio.cinemax.api.INTEGRATION_TEST_DATE_TIME
import com.cleevio.cinemax.api.module.pricecategoryitem.constant.PriceCategoryItemNumber
import com.cleevio.cinemax.api.module.reservation.service.ReservationJpaFinderService
import com.cleevio.cinemax.api.module.reservation.service.ReservationService
import com.cleevio.cinemax.api.module.screening.exception.ScreeningSaleTimeLimitOverException
import com.cleevio.cinemax.api.module.screening.exception.ScreeningsNotFoundException
import com.cleevio.cinemax.api.module.screening.exception.ScreeningsSaleTimeLimitOverException
import com.cleevio.cinemax.api.module.screening.service.ScreeningJpaFinderService
import com.cleevio.cinemax.api.module.seat.service.SeatJpaFinderService
import com.cleevio.cinemax.api.module.ticket.controller.dto.AdminMoveTicketRequest
import com.cleevio.cinemax.api.module.ticket.exception.ScreeningForTicketNotFoundException
import com.cleevio.cinemax.api.module.ticket.exception.ScreeningInThePastException
import com.cleevio.cinemax.api.module.ticket.exception.SeatForTicketNotFoundException
import com.cleevio.cinemax.api.module.ticket.exception.TicketDiscountForTicketNotFoundException
import com.cleevio.cinemax.api.module.ticket.exception.TicketsNotFoundException
import com.cleevio.cinemax.api.module.ticket.service.command.AdminMoveTicketsCommand
import com.cleevio.cinemax.api.module.ticket.service.command.CreateTicketCommand
import com.cleevio.cinemax.api.module.ticketdiscount.service.TicketDiscountJpaFinderService
import com.cleevio.cinemax.api.module.ticketprice.service.TicketPriceJpaFinderService
import com.cleevio.cinemax.api.module.ticketprice.service.TicketPriceService
import com.cleevio.cinemax.api.util.createAuditoriumLayout
import com.cleevio.cinemax.api.util.createReservation
import com.cleevio.cinemax.api.util.createScreening
import com.cleevio.cinemax.api.util.createScreeningWithCustomDateTimeLimit
import com.cleevio.cinemax.api.util.createSeat
import com.cleevio.cinemax.api.util.createTicket
import com.cleevio.cinemax.api.util.createTicketDiscount
import com.cleevio.library.lockinghandler.service.LockService
import io.mockk.Called
import io.mockk.every
import io.mockk.mockk
import io.mockk.verifySequence
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import org.springframework.context.ApplicationEventPublisher
import java.time.Clock
import java.time.ZoneId
import java.time.ZoneOffset
import java.util.UUID
import kotlin.test.assertContains

class TicketServiceTest {

    private val ticketRepository = mockk<TicketRepository>()
    private val ticketJpaFinderService = mockk<TicketJpaFinderService>()
    private val screeningJpaFinderService = mockk<ScreeningJpaFinderService>()
    private val seatJpaFinderService = mockk<SeatJpaFinderService>()
    private val ticketPriceService = mockk<TicketPriceService>()
    private val ticketPriceJpaFinderService = mockk<TicketPriceJpaFinderService>()
    private val ticketDiscountJpaFinderService = mockk<TicketDiscountJpaFinderService>()
    private val reservationService = mockk<ReservationService>()
    private val reservationJpaFinderService = mockk<ReservationJpaFinderService>()
    private val applicationEventPublisher = mockk<ApplicationEventPublisher>()
    private val lockService = mockk<LockService>()
    private val clock = mockk<Clock>()

    private val underTest = TicketService(
        ticketRepository,
        ticketJpaFinderService,
        screeningJpaFinderService,
        seatJpaFinderService,
        ticketPriceService,
        ticketPriceJpaFinderService,
        ticketDiscountJpaFinderService,
        reservationService,
        reservationJpaFinderService,
        applicationEventPublisher,
        lockService,
        clock
    )

    @BeforeEach
    fun setUp() {
        every { clock.instant() } returns INTEGRATION_TEST_DATE_TIME.toInstant(ZoneOffset.UTC)
        every { clock.zone } returns ZoneId.systemDefault()
    }

    @Test
    fun `test createTicket - command with not existing screening - should throw exception`() {
        every { screeningJpaFinderService.findNonDeletedById(any()) } returns null
        every { seatJpaFinderService.findById(any()) } returns SEAT_1
        every { ticketDiscountJpaFinderService.findNonDeletedById(any()) } returns TICKET_DISCOUNT_1
        every { reservationService.createReservation(any()) } returns RESERVATION_1
        every { ticketRepository.save(any()) } returns TICKET_1

        assertThrows<ScreeningForTicketNotFoundException> {
            underTest.createTicket(CREATE_TICKET_COMMAND_1)
        }

        verifySequence {
            seatJpaFinderService wasNot Called
            ticketPriceJpaFinderService wasNot Called
            ticketDiscountJpaFinderService wasNot Called
            reservationService wasNot Called
            ticketRepository wasNot Called
        }
    }

    @Test
    fun `test createTicket - command with not existing seat - should throw exception`() {
        every { screeningJpaFinderService.findNonDeletedById(any()) } returns SCREENING_1
        every { seatJpaFinderService.findById(any()) } returns null
        every { ticketDiscountJpaFinderService.findNonDeletedById(any()) } returns TICKET_DISCOUNT_1
        every { reservationService.createReservation(any()) } returns RESERVATION_1
        every { ticketRepository.save(any()) } returns TICKET_1

        assertThrows<SeatForTicketNotFoundException> {
            underTest.createTicket(CREATE_TICKET_COMMAND_1)
        }

        verifySequence {
            screeningJpaFinderService.findNonDeletedById(SCREENING_1.id)
            seatJpaFinderService.findById(SEAT_1.id)
            ticketPriceJpaFinderService wasNot Called
            ticketDiscountJpaFinderService wasNot Called
            reservationService wasNot Called
            ticketRepository wasNot Called
        }
    }

    @Test
    fun `test createTicket - command with not existing ticket discount - should throw exception`() {
        every { screeningJpaFinderService.findNonDeletedById(any()) } returns SCREENING_1
        every { seatJpaFinderService.findById(any()) } returns SEAT_1
        every { ticketDiscountJpaFinderService.findNonDeletedById(any()) } returns null
        every { ticketRepository.save(any()) } returns TICKET_1

        assertThrows<TicketDiscountForTicketNotFoundException> {
            underTest.createTicket(CREATE_TICKET_COMMAND_1)
        }

        verifySequence {
            screeningJpaFinderService.findNonDeletedById(SCREENING_1.id)
            seatJpaFinderService.findById(SEAT_1.id)
            ticketDiscountJpaFinderService.findNonDeletedById(TICKET_DISCOUNT_1.id)
            ticketPriceJpaFinderService wasNot Called
            reservationService wasNot Called
            ticketRepository wasNot Called
        }
    }

    @Test
    fun `test createTicket - screening in the past - should throw exception`() {
        every { screeningJpaFinderService.findNonDeletedById(any()) } returns SCREENING_2
        every { seatJpaFinderService.findById(any()) } returns SEAT_1
        every { ticketDiscountJpaFinderService.findNonDeletedById(any()) } returns TICKET_DISCOUNT_1
        every { reservationService.createReservation(any()) } returns RESERVATION_1
        every { ticketRepository.save(any()) } returns TICKET_1

        assertThrows<ScreeningInThePastException> {
            underTest.createTicket(CREATE_TICKET_COMMAND_2)
        }

        verifySequence {
            screeningJpaFinderService.findNonDeletedById(SCREENING_2.id)
            seatJpaFinderService wasNot Called
            ticketPriceJpaFinderService wasNot Called
            ticketDiscountJpaFinderService wasNot Called
            reservationService wasNot Called
            ticketRepository wasNot Called
        }
    }

    @Test
    fun `test createTicket - sale time limit is over - should throw exception`() {
        every { screeningJpaFinderService.findNonDeletedById(any()) } returns SCREENING_3
        every { seatJpaFinderService.findById(any()) } returns SEAT_1
        every { ticketDiscountJpaFinderService.findNonDeletedById(any()) } returns TICKET_DISCOUNT_1
        every { reservationService.createReservation(any()) } returns RESERVATION_1
        every { ticketRepository.save(any()) } returns TICKET_1

        assertThrows<ScreeningSaleTimeLimitOverException> {
            underTest.createTicket(CREATE_TICKET_COMMAND_3)
        }

        verifySequence {
            screeningJpaFinderService.findNonDeletedById(SCREENING_3.id)
            seatJpaFinderService wasNot Called
            ticketPriceJpaFinderService wasNot Called
            ticketDiscountJpaFinderService wasNot Called
            reservationService wasNot Called
            ticketRepository wasNot Called
        }
    }

    @Test
    fun `test moveTickets - command with not existing ticket - should throw exception`() {
        every { ticketRepository.findAllByIdInAndDeletedAtIsNull(any()) } returns listOf(TICKET_1)

        val exceptionMessage = assertThrows<TicketsNotFoundException> {
            underTest.moveTickets(
                AdminMoveTicketsCommand(
                    ticketRequests = listOf(
                        AdminMoveTicketRequest(
                            ticketId = TICKET_1.id,
                            screeningId = SCREENING_4.id,
                            seatId = SEAT_1.id
                        ),
                        AdminMoveTicketRequest(
                            ticketId = INVALID_TICKET_ID,
                            screeningId = SCREENING_4.id,
                            seatId = SEAT_2.id
                        )
                    )
                )
            )
        }.message

        assertContains(exceptionMessage, INVALID_TICKET_ID.toString())

        verifySequence {
            ticketRepository.findAllByIdInAndDeletedAtIsNull(setOf(TICKET_1.id, INVALID_TICKET_ID))
            screeningJpaFinderService wasNot Called
            lockService wasNot Called
            reservationService wasNot Called
        }
    }

    @Test
    fun `test moveTickets - command with not existing screenings - should throw exception`() {
        every { ticketRepository.findAllByIdInAndDeletedAtIsNull(any()) } returns listOf(TICKET_1, TICKET_2)
        every { screeningJpaFinderService.findAllNonDeletedByIdIn(any()) } returns listOf()

        val exceptionMessage = assertThrows<ScreeningsNotFoundException> {
            underTest.moveTickets(
                AdminMoveTicketsCommand(
                    ticketRequests = listOf(
                        AdminMoveTicketRequest(
                            ticketId = TICKET_1.id,
                            screeningId = INVALID_SCREENING_ID_1,
                            seatId = SEAT_1.id
                        ),
                        AdminMoveTicketRequest(
                            ticketId = TICKET_2.id,
                            screeningId = INVALID_SCREENING_ID_2,
                            seatId = SEAT_2.id
                        )
                    )
                )
            )
        }.message

        assertContains(exceptionMessage, INVALID_SCREENING_ID_1.toString())
        assertContains(exceptionMessage, INVALID_SCREENING_ID_2.toString())

        verifySequence {
            ticketRepository.findAllByIdInAndDeletedAtIsNull(setOf(TICKET_1.id, TICKET_2.id))
            screeningJpaFinderService.findAllNonDeletedByIdIn(
                setOf(SCREENING_1.id, INVALID_SCREENING_ID_1, INVALID_SCREENING_ID_2)
            )
            lockService wasNot Called
            reservationService wasNot Called
        }
    }

    @Test
    fun `test moveTickets - source and destination screening is after sale time - should throw exception`() {
        every { ticketRepository.findAllByIdInAndDeletedAtIsNull(any()) } returns listOf(TICKET_1, TICKET_3)
        every { screeningJpaFinderService.findAllNonDeletedByIdIn(any()) } returns listOf(SCREENING_1, SCREENING_2, SCREENING_3)

        val exceptionMessage = assertThrows<ScreeningsSaleTimeLimitOverException> {
            underTest.moveTickets(
                AdminMoveTicketsCommand(
                    ticketRequests = listOf(
                        AdminMoveTicketRequest(
                            ticketId = TICKET_1.id,
                            screeningId = SCREENING_3.id,
                            seatId = SEAT_1.id
                        ),
                        AdminMoveTicketRequest(
                            ticketId = TICKET_3.id,
                            screeningId = SCREENING_3.id,
                            seatId = SEAT_2.id
                        )
                    )
                )
            )
        }.message

        assertContains(exceptionMessage, SCREENING_2.id.toString())
        assertContains(exceptionMessage, SCREENING_3.id.toString())

        verifySequence {
            ticketRepository.findAllByIdInAndDeletedAtIsNull(setOf(TICKET_1.id, TICKET_3.id))
            screeningJpaFinderService.findAllNonDeletedByIdIn(
                setOf(SCREENING_1.id, SCREENING_2.id, SCREENING_3.id)
            )
            lockService wasNot Called
            reservationService wasNot Called
        }
    }
}

private val AUDITORIUM_1_ID = UUID.randomUUID()
private val TICKET_PRICE_1_ID = UUID.randomUUID()
private val TICKET_PRICE_2_ID = UUID.randomUUID()
private val MOVIE_1_ID = UUID.randomUUID()
private val INVALID_TICKET_ID = UUID.randomUUID()
private val INVALID_SCREENING_ID_1 = UUID.randomUUID()
private val INVALID_SCREENING_ID_2 = UUID.randomUUID()
private val AUDITORIUM_LAYOUT_1 = createAuditoriumLayout(originalId = 1, auditoriumId = AUDITORIUM_1_ID, code = "01")

private val SEAT_1 = createSeat(
    originalId = 1,
    auditoriumId = AUDITORIUM_1_ID,
    auditoriumLayoutId = AUDITORIUM_LAYOUT_1.id,
    row = "A",
    number = "6",
    positionTop = 62
)
private val SEAT_2 = createSeat(
    originalId = 2,
    auditoriumId = AUDITORIUM_1_ID,
    auditoriumLayoutId = AUDITORIUM_LAYOUT_1.id,
    row = "A",
    number = "7",
    positionTop = 62
)
private val SCREENING_1 = createScreeningWithCustomDateTimeLimit(
    originalId = 3,
    auditoriumId = AUDITORIUM_1_ID,
    date = INTEGRATION_TEST_DATE_TIME.toLocalDate().plusDays(1),
    time = INTEGRATION_TEST_DATE_TIME.toLocalTime(),
    movieId = MOVIE_1_ID,
    saleTimeLimit = 30
)
private val SCREENING_2 = createScreeningWithCustomDateTimeLimit(
    originalId = 3,
    auditoriumId = AUDITORIUM_1_ID,
    date = INTEGRATION_TEST_DATE_TIME.toLocalDate().minusDays(1),
    time = INTEGRATION_TEST_DATE_TIME.toLocalTime(),
    movieId = MOVIE_1_ID,
    saleTimeLimit = 30
)
private val SCREENING_3 = createScreeningWithCustomDateTimeLimit(
    originalId = 3,
    auditoriumId = AUDITORIUM_1_ID,
    date = INTEGRATION_TEST_DATE_TIME.toLocalDate(),
    time = INTEGRATION_TEST_DATE_TIME.toLocalTime().minusMinutes(1),
    movieId = MOVIE_1_ID,
    saleTimeLimit = 0
)
private val SCREENING_4 = createScreening(
    originalId = 4,
    auditoriumId = AUDITORIUM_1_ID,
    date = INTEGRATION_TEST_DATE_TIME.toLocalDate().plusDays(3),
    time = INTEGRATION_TEST_DATE_TIME.toLocalTime(),
    movieId = MOVIE_1_ID
)
private val RESERVATION_1 = createReservation(
    screeningId = SCREENING_1.id,
    seatId = SEAT_1.id
)
private val RESERVATION_2 = createReservation(
    screeningId = SCREENING_1.id,
    seatId = SEAT_2.id
)
private val TICKET_1 = createTicket(
    screeningId = SCREENING_1.id,
    reservationId = RESERVATION_1.id,
    ticketPriceId = TICKET_PRICE_1_ID
)
private val TICKET_2 = createTicket(
    screeningId = SCREENING_1.id,
    reservationId = RESERVATION_2.id,
    ticketPriceId = TICKET_PRICE_2_ID
)
private val TICKET_3 = createTicket(
    screeningId = SCREENING_2.id,
    reservationId = RESERVATION_2.id,
    ticketPriceId = TICKET_PRICE_2_ID
)
private val TICKET_DISCOUNT_1 = createTicketDiscount(
    originalId = 1,
    code = "01X",
    title = "Artmax FILM karta",
    order = 11
)
private val CREATE_TICKET_COMMAND_1 = CreateTicketCommand(
    screeningId = SCREENING_1.id,
    seatId = SEAT_1.id,
    ticketDiscountPrimaryId = TICKET_DISCOUNT_1.id,
    ticketDiscountSecondaryId = null,
    freeTicket = false,
    isGroupTicket = false,
    includes3dGlasses = false,
    priceCategoryItemNumber = PriceCategoryItemNumber.PRICE_1
)
private val CREATE_TICKET_COMMAND_2 = CreateTicketCommand(
    screeningId = SCREENING_2.id,
    seatId = SEAT_1.id,
    ticketDiscountPrimaryId = TICKET_DISCOUNT_1.id,
    ticketDiscountSecondaryId = null,
    freeTicket = false,
    isGroupTicket = false,
    includes3dGlasses = false,
    priceCategoryItemNumber = PriceCategoryItemNumber.PRICE_1
)
private val CREATE_TICKET_COMMAND_3 = CreateTicketCommand(
    screeningId = SCREENING_3.id,
    seatId = SEAT_1.id,
    ticketDiscountPrimaryId = TICKET_DISCOUNT_1.id,
    ticketDiscountSecondaryId = null,
    freeTicket = false,
    isGroupTicket = false,
    includes3dGlasses = false,
    priceCategoryItemNumber = PriceCategoryItemNumber.PRICE_1
)
