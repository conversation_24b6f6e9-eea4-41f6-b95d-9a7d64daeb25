package com.cleevio.cinemax.api.module.seat.service

import com.cleevio.cinemax.api.MssqlIntegrationTest
import com.cleevio.cinemax.api.module.reservation.constant.ReservationState
import com.cleevio.cinemax.api.module.seat.constant.DoubleSeatType
import com.cleevio.cinemax.api.module.seat.constant.SeatObjectType
import com.cleevio.cinemax.api.module.seat.constant.SeatType
import com.cleevio.cinemax.api.module.seat.service.command.CreateOrUpdateSeatCommand
import com.cleevio.cinemax.api.module.synchronizationfrommssql.constant.SynchronizationFromMssqlType
import com.cleevio.cinemax.api.module.synchronizationfrommssql.entity.SynchronizationFromMssql
import com.cleevio.cinemax.api.module.synchronizationfrommssql.service.command.CreateOrUpdateSynchronizationFromMssqlCommand
import com.cleevio.cinemax.api.util.createAuditorium
import com.cleevio.cinemax.api.util.createAuditoriumLayout
import io.mockk.Called
import io.mockk.Runs
import io.mockk.every
import io.mockk.just
import io.mockk.verify
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.test.context.jdbc.Sql
import org.springframework.test.context.jdbc.SqlConfig
import org.springframework.test.context.jdbc.SqlGroup
import java.time.LocalDateTime
import kotlin.test.assertEquals
import kotlin.test.assertTrue

@SqlGroup(
    Sql(
        scripts = [
            "/db/mssql-cinemax/test/init_mssql_seat.sql"
        ],
        config = SqlConfig(
            dataSource = "mssqlCinemaxDataSource",
            transactionManager = "mssqlCinemaxTransactionManager"
        )
    ),
    Sql(
        scripts = [
            "/db/mssql-cinemax/test/drop_mssql_seat.sql"
        ],
        config = SqlConfig(
            dataSource = "mssqlCinemaxDataSource",
            transactionManager = "mssqlCinemaxTransactionManager"
        ),
        executionPhase = Sql.ExecutionPhase.AFTER_TEST_METHOD
    )
)
class SeatMssqlSynchronizationServiceIT @Autowired constructor(
    private val underTest: SeatMssqlSynchronizationService,
) : MssqlIntegrationTest() {

    @Test
    fun `test synchronize all - no PSQL seats, 8 MSSQL seats - should create 5 seats`() {
        every { synchronizationFromMssqlFinderServiceMock.findByType(any()) } returns null
        every { seatServiceMock.createOrUpdateSeat(any()) } just Runs
        every { auditoriumJooqFinderServiceMock.findByOriginalCode(any()) } returns AUDITORIUM
        every { synchronizationFromMssqlServiceMock.createOrUpdate(any()) } returns SYNCHRONIZATION_FROM_MSSQL
        every {
            auditoriumLayoutJpaFinderServiceMock.findNonDeletedByOriginalId(any())
        } returnsMany listOf(
            AUDITORIUM_LAYOUT_1,
            AUDITORIUM_LAYOUT_2,
            AUDITORIUM_LAYOUT_3,
            AUDITORIUM_LAYOUT_3,
            AUDITORIUM_LAYOUT_2
        )

        underTest.synchronizeAll()

        val originalAuditoriumCodeCaptor = mutableListOf<Int>()
        val commandCaptor = mutableListOf<CreateOrUpdateSeatCommand>()

        verify { synchronizationFromMssqlFinderServiceMock.findByType(SynchronizationFromMssqlType.SEAT) }
        verify { auditoriumJooqFinderServiceMock.findByOriginalCode(capture(originalAuditoriumCodeCaptor)) }
        verify { seatServiceMock.createOrUpdateSeat(capture(commandCaptor)) }
        verify {
            synchronizationFromMssqlServiceMock.createOrUpdate(
                CreateOrUpdateSynchronizationFromMssqlCommand(
                    type = SynchronizationFromMssqlType.SEAT,
                    lastSynchronization = SEAT_3_UPDATED_AT
                )
            )
        }

        assertTrue(originalAuditoriumCodeCaptor.size == 7)
        assertTrue(originalAuditoriumCodeCaptor.containsAll(setOf(ORIGINAL_AUDITORIUM_CODE)))
        assertTrue(commandCaptor.size == 5)
        assertEquals(EXPECTED_COMMAND_1, commandCaptor[0])
        assertEquals(EXPECTED_COMMAND_2, commandCaptor[1])
        assertEquals(EXPECTED_COMMAND_3, commandCaptor[2])
        assertEquals(EXPECTED_COMMAND_4, commandCaptor[3])
        assertEquals(EXPECTED_COMMAND_5, commandCaptor[4])
    }

    @Test
    fun `test synchronize all - 4 PSQL seats, 4 MSSQL seats - should create 1 seat`() {
        every { synchronizationFromMssqlFinderServiceMock.findByType(any()) } returns SEAT_2_UPDATED_AT
        every { seatServiceMock.createOrUpdateSeat(any()) } just Runs
        every { auditoriumJooqFinderServiceMock.findByOriginalCode(any()) } returns AUDITORIUM
        every { synchronizationFromMssqlServiceMock.createOrUpdate(any()) } returns SYNCHRONIZATION_FROM_MSSQL
        every { auditoriumLayoutJpaFinderServiceMock.findNonDeletedByOriginalId(any()) } returns AUDITORIUM_LAYOUT_3

        underTest.synchronizeAll()

        val originalAuditoriumCodeCaptor = mutableListOf<Int>()
        val commandCaptor = mutableListOf<CreateOrUpdateSeatCommand>()

        verify { synchronizationFromMssqlFinderServiceMock.findByType(SynchronizationFromMssqlType.SEAT) }
        verify { auditoriumJooqFinderServiceMock.findByOriginalCode(capture(originalAuditoriumCodeCaptor)) }
        verify { seatServiceMock.createOrUpdateSeat(capture(commandCaptor)) }
        verify {
            synchronizationFromMssqlServiceMock.createOrUpdate(
                CreateOrUpdateSynchronizationFromMssqlCommand(
                    type = SynchronizationFromMssqlType.SEAT,
                    lastSynchronization = SEAT_3_UPDATED_AT
                )
            )
        }

        assertTrue(originalAuditoriumCodeCaptor.size == 1)
        assertTrue(originalAuditoriumCodeCaptor.containsAll(setOf(ORIGINAL_AUDITORIUM_CODE)))
        assertTrue(commandCaptor.size == 1)
        assertEquals(EXPECTED_COMMAND_3, commandCaptor[0])
    }

    @Test
    fun `test synchronize all - no PSQL auditorium - should create no seats`() {
        every { synchronizationFromMssqlFinderServiceMock.findByType(any()) } returns null
        every { seatServiceMock.createOrUpdateSeat(any()) } just Runs
        every { auditoriumJooqFinderServiceMock.findByOriginalCode(any()) } returns null
        every { synchronizationFromMssqlServiceMock.createOrUpdate(any()) } returns SYNCHRONIZATION_FROM_MSSQL

        underTest.synchronizeAll()

        val originalAuditoriumCodeCaptor = mutableListOf<Int>()

        verify { synchronizationFromMssqlFinderServiceMock.findByType(SynchronizationFromMssqlType.SEAT) }
        verify { auditoriumJooqFinderServiceMock.findByOriginalCode(capture(originalAuditoriumCodeCaptor)) }
        verify { seatServiceMock wasNot Called }
        verify {
            synchronizationFromMssqlServiceMock.createOrUpdate(
                CreateOrUpdateSynchronizationFromMssqlCommand(
                    type = SynchronizationFromMssqlType.SEAT,
                    lastSynchronization = SEAT_3_UPDATED_AT
                )
            )
        }

        assertTrue(originalAuditoriumCodeCaptor.size == 7)
        assertTrue(originalAuditoriumCodeCaptor.containsAll(setOf(ORIGINAL_AUDITORIUM_CODE)))
    }
}

private const val ORIGINAL_AUDITORIUM_CODE = 510000

private val SEAT_2_UPDATED_AT = LocalDateTime.of(2022, 2, 23, 16, 48, 0)
private val SEAT_3_UPDATED_AT = LocalDateTime.of(2022, 2, 23, 16, 49, 0)
private val AUDITORIUM = createAuditorium(originalId = ORIGINAL_AUDITORIUM_CODE)
private val AUDITORIUM_LAYOUT_1 = createAuditoriumLayout(originalId = 2, auditoriumId = AUDITORIUM.id)
private val AUDITORIUM_LAYOUT_2 = createAuditoriumLayout(originalId = 4, auditoriumId = AUDITORIUM.id)
private val AUDITORIUM_LAYOUT_3 = createAuditoriumLayout(originalId = 1, auditoriumId = AUDITORIUM.id)
private val EXPECTED_COMMAND_1 = CreateOrUpdateSeatCommand(
    originalId = 1,
    auditoriumId = AUDITORIUM.id,
    auditoriumLayoutId = AUDITORIUM_LAYOUT_1.id,
    doubleSeatType = DoubleSeatType.DOUBLE_SEAT_LEFT,
    type = SeatType.REGULAR,
    objectType = SeatObjectType.SEAT,
    row = "10",
    number = "19",
    positionTop = 367,
    positionLeft = 198,
    webPositionTop = 232,
    webPositionLeft = 85,
    defaultReservationState = ReservationState.DISABLED
)
private val EXPECTED_COMMAND_2 = CreateOrUpdateSeatCommand(
    originalId = 2,
    auditoriumId = AUDITORIUM.id,
    auditoriumLayoutId = AUDITORIUM_LAYOUT_2.id,
    doubleSeatType = null,
    type = SeatType.PREMIUM_PLUS,
    objectType = SeatObjectType.SEAT,
    row = "5",
    number = "2",
    positionTop = 166,
    positionLeft = 320,
    webPositionTop = 136,
    webPositionLeft = 176,
    defaultReservationState = null
)
private val EXPECTED_COMMAND_3 = CreateOrUpdateSeatCommand(
    originalId = 3,
    auditoriumId = AUDITORIUM.id,
    auditoriumLayoutId = AUDITORIUM_LAYOUT_3.id,
    doubleSeatType = null,
    type = SeatType.DISABLED_SEATING,
    objectType = SeatObjectType.SEAT,
    row = "3",
    number = "Inv.",
    positionTop = 62,
    positionLeft = 700,
    webPositionTop = 74,
    webPositionLeft = 135,
    defaultReservationState = null
)
private val EXPECTED_COMMAND_4 = CreateOrUpdateSeatCommand(
    originalId = 4,
    auditoriumId = AUDITORIUM.id,
    auditoriumLayoutId = AUDITORIUM_LAYOUT_3.id,
    doubleSeatType = null,
    type = SeatType.REGULAR,
    objectType = SeatObjectType.ROW,
    row = "1",
    number = "1",
    positionTop = 10,
    positionLeft = 7,
    webPositionTop = 40,
    webPositionLeft = null,
    defaultReservationState = null
)
private val EXPECTED_COMMAND_5 = CreateOrUpdateSeatCommand(
    originalId = 5,
    auditoriumId = AUDITORIUM.id,
    auditoriumLayoutId = AUDITORIUM_LAYOUT_2.id,
    doubleSeatType = null,
    type = SeatType.PREMIUM_PLUS,
    objectType = SeatObjectType.IMAGE,
    row = "5",
    number = "Premi",
    positionTop = 0,
    positionLeft = 0,
    webPositionTop = 136,
    webPositionLeft = 40,
    defaultReservationState = null
)
private val SYNCHRONIZATION_FROM_MSSQL = SynchronizationFromMssql(
    type = SynchronizationFromMssqlType.SEAT,
    lastSynchronization = LocalDateTime.MIN,
    lastHeartbeat = null
)
