package com.cleevio.cinemax.api.module.product.event

import com.cleevio.cinemax.api.common.constant.REDUCED_TAX_RATE
import com.cleevio.cinemax.api.common.util.MESSAGING_MAPPER
import com.cleevio.cinemax.api.module.product.constant.ProductType
import com.cleevio.cinemax.api.util.toUUID
import org.junit.jupiter.api.Test
import kotlin.test.assertEquals
import kotlin.test.assertNotNull

class AdminProductCreatedOrUpdatedEventTest {

    @Test
    fun `test toMessagePayload - all event fields with values - should create message payload with correct type and serialization`() {
        val event = AdminProductCreatedOrUpdatedEvent(
            title = "Product 1",
            code = "1234",
            active = true,
            type = ProductType.PRODUCT_IN_PRODUCT,
            productCategoryCode = "02",
            imageFileId = 1.toUUID(),
            price = 2.toBigDecimal(),
            soldInBuffet = true,
            soldInCafe = true,
            soldInVip = true,
            order = 10,
            isPackagingDeposit = false,
            stockQuantityThreshold = 20,
            taxRate = REDUCED_TAX_RATE,
            discountAmount = 5.toBigDecimal(),
            discountPercentage = 15,
            productComposition = listOf(
                AdminProductCreatedOrUpdatedEvent.AdminProductComposition(
                    productComponentCode = "11",
                    productInProductCode = "22",
                    productInProductPrice = 3.toBigDecimal(),
                    productInProductFlagshipPrice = 4.toBigDecimal(),
                    quantity = 5.toBigDecimal()
                ),
                AdminProductCreatedOrUpdatedEvent.AdminProductComposition(
                    productComponentCode = "33",
                    productInProductCode = "44",
                    productInProductPrice = 0.144396.toBigDecimal(),
                    productInProductFlagshipPrice = 1.144396.toBigDecimal(),
                    quantity = 0.06798.toBigDecimal()
                ),
                AdminProductCreatedOrUpdatedEvent.AdminProductComposition(
                    productComponentCode = "55",
                    productInProductCode = "66",
                    productInProductPrice = 1.234.toBigDecimal(),
                    productInProductFlagshipPrice = 2.234.toBigDecimal(),
                    quantity = 5.55555.toBigDecimal()
                )
            )
        )

        val expectedJson = """
            {
              "title": "Product 1",
              "code": "1234",
              "active": true,
              "type": "PRODUCT_IN_PRODUCT",
              "productCategoryCode": "02",
              "imageFileId": "00000000-0000-0000-0000-000000000001",
              "price": 2.0,
              "soldInBuffet": true,
              "soldInCafe": true,
              "soldInVip": true,
              "order": 10,
              "isPackagingDeposit": false,
              "stockQuantityThreshold": 20,
              "taxRate": 19,
              "discountAmount": 5.0,
              "discountPercentage": 15,
              "productComposition": [
                {
                  "productComponentCode": "11",
                  "productInProductCode": "22",
                  "productInProductPrice": 3.0,
                  "productInProductFlagshipPrice": 4.0,
                  "quantity": 5.0
                },
                {
                  "productComponentCode": "33",
                  "productInProductCode": "44",
                  "productInProductPrice": 0.144396,
                  "productInProductFlagshipPrice": 1.144396,
                  "quantity": 0.06798
                },
                {
                  "productComponentCode": "55",
                  "productInProductCode": "66",
                  "productInProductPrice": 1.234,
                  "productInProductFlagshipPrice": 2.234,
                  "quantity": 5.55555
                }
              ]
            }
        """.trimIndent()

        val messagePayload = event.toMessagePayload()

        assertNotNull(messagePayload.id)
        assertNotNull(messagePayload.createdAt)
        assertEquals("PRODUCT_CREATED_OR_UPDATED", messagePayload.type.name)
        assertEquals(MESSAGING_MAPPER.readTree(expectedJson), MESSAGING_MAPPER.readTree(messagePayload.data))
    }

    @Test
    fun `test toMessagePayload - required event fields all other are null - should create message payload with correct type and serialization`() {
        val event = AdminProductCreatedOrUpdatedEvent(
            title = "Product 1",
            code = "1234",
            active = true,
            type = ProductType.PRODUCT_IN_PRODUCT,
            productCategoryCode = "02",
            price = 2.toBigDecimal(),
            soldInBuffet = true,
            soldInCafe = true,
            soldInVip = true,
            productComposition = emptyList()
        )

        val expectedJson = """
            {
              "title": "Product 1",
              "code": "1234",
              "active": true,
              "type": "PRODUCT_IN_PRODUCT",
              "productCategoryCode": "02",
              "price": 2.0,
              "soldInBuffet": true,
              "soldInCafe": true,
              "soldInVip": true,
              "productComposition": []
            }
        """.trimIndent()

        val messagePayload = event.toMessagePayload()

        assertNotNull(messagePayload.id)
        assertNotNull(messagePayload.createdAt)
        assertEquals("PRODUCT_CREATED_OR_UPDATED", messagePayload.type.name)
        assertEquals(MESSAGING_MAPPER.readTree(expectedJson), MESSAGING_MAPPER.readTree(messagePayload.data))
    }
}
