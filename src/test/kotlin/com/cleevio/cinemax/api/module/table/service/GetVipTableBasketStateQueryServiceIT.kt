package com.cleevio.cinemax.api.module.table.service

import com.cleevio.cinemax.api.DatabaseCleanup
import com.cleevio.cinemax.api.IntegrationDataTestHelper
import com.cleevio.cinemax.api.IntegrationTest
import com.cleevio.cinemax.api.common.constant.PaymentType
import com.cleevio.cinemax.api.module.basket.constant.BasketState
import com.cleevio.cinemax.api.module.basket.exception.BasketNotFoundException
import com.cleevio.cinemax.api.module.posconfiguration.constant.ProductMode
import com.cleevio.cinemax.api.module.table.constant.TableType
import com.cleevio.cinemax.api.module.table.service.query.GetVipTableBasketStateQuery
import com.cleevio.cinemax.api.util.toUUID
import io.kotest.assertions.throwables.shouldThrow
import io.kotest.matchers.shouldBe
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import java.math.BigDecimal

class GetVipTableBasketStateQueryServiceIT @Autowired constructor(
    private val underTest: GetVipTableBasketStateQueryService,
    private val integrationDataTestHelper: IntegrationDataTestHelper,
) : IntegrationTest(DatabaseCleanup.AFTER_EACH) {

    @Test
    fun `test get vip table basket state - should return OPEN state for table with open basket`() {
        val table = integrationDataTestHelper.getTable(
            id = 1.toUUID(),
            originalId = 1,
            title = "VIP Table 1",
            type = TableType.SEAT,
            productMode = ProductMode.VIP
        )
        integrationDataTestHelper.getBasket(
            tableId = table.id,
            totalPrice = 11.toBigDecimal(),
            state = BasketState.PAID,
            paymentType = PaymentType.CASHLESS
        )
        integrationDataTestHelper.getBasket(
            tableId = table.id,
            totalPrice = BigDecimal.TEN,
            state = BasketState.OPEN,
            paymentType = PaymentType.CASHLESS
        )

        val result = underTest.invoke(
            GetVipTableBasketStateQuery(table.id)
        )
        result shouldBe BasketState.OPEN
    }

    @Test
    fun `test get vip table basket state - should throw BasketNotFoundException for non-VIP table`() {
        val table = integrationDataTestHelper.getTable(
            id = 3.toUUID(),
            originalId = 5,
            title = "Standard Table",
            type = TableType.SEAT,
            productMode = ProductMode.STANDARD
        )

        integrationDataTestHelper.getBasket(
            tableId = table.id,
            totalPrice = BigDecimal.TEN,
            state = BasketState.OPEN,
            paymentType = PaymentType.CASH
        )

        shouldThrow<BasketNotFoundException> {
            underTest.invoke(GetVipTableBasketStateQuery(table.id))
        }
    }
}
