package com.cleevio.cinemax.api.module.screening.service

import com.cleevio.cinemax.api.MssqlIntegrationTest
import com.cleevio.cinemax.api.common.util.trimIfNotBlank
import com.cleevio.cinemax.api.module.screening.service.command.UpdateScreeningsLanguageAndTechnologyCommand
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.test.context.jdbc.Sql
import org.springframework.test.context.jdbc.SqlConfig
import org.springframework.test.context.jdbc.SqlGroup
import kotlin.test.assertEquals
import kotlin.test.assertNull

@SqlGroup(
    Sql(
        scripts = [
            "/db/mssql-cinemax/test/init_mssql_screening.sql"
        ],
        config = SqlConfig(
            dataSource = "mssqlCinemaxDataSource",
            transactionManager = "mssqlCinemaxTransactionManager"
        )
    ),
    Sql(
        scripts = [
            "/db/mssql-cinemax/test/drop_mssql_screening.sql"
        ],
        config = SqlConfig(
            dataSource = "mssqlCinemaxDataSource",
            transactionManager = "mssqlCinemaxTransactionManager"
        ),
        executionPhase = Sql.ExecutionPhase.AFTER_TEST_METHOD
    )
)
class ScreeningMssqlServiceIT @Autowired constructor(
    private val underTest: ScreeningMssqlService,
    private val screeningMssqlFinderRepository: ScreeningMssqlFinderRepository,
) : MssqlIntegrationTest() {

    @Test
    fun `test updateScreeningsLanguageAndTechnology - should update 2 screenings`() {
        val screenings = screeningMssqlFinderRepository.findAll()
        assertEquals(3, screenings.size)

        val movieOriginalId = screenings[1].rfilmid
        val relatedScreenings = screenings.filter { it.rfilmid == movieOriginalId }

        relatedScreenings[0].let {
            assertEquals(LANGUAGE_ENG_CODE, it.jazyk.trimIfNotBlank())
            assertNull(it.cfotrm.trimIfNotBlank())
        }
        relatedScreenings[1].let {
            assertEquals(LANGUAGE_CZ_CODE, it.jazyk.trimIfNotBlank())
            assertEquals(TECHNOLOGY_ANALOG_CODE, it.cfotrm.trimIfNotBlank())
        }

        val processResult = underTest.updateScreeningsLanguageAndTechnology(
            UpdateScreeningsLanguageAndTechnologyCommand(
                movieOriginalId = movieOriginalId,
                technologyCode = TECHNOLOGY_IMAX_CODE,
                languageCode = LANGUAGE_SK_CODE
            )
        )

        val updatedScreenings = screeningMssqlFinderRepository.findAll().filter { it.rfilmid == movieOriginalId }
        updatedScreenings.forEach {
            assertEquals(TECHNOLOGY_IMAX_CODE, it.cfotrm.trimIfNotBlank())
            assertEquals(LANGUAGE_SK_CODE, it.jazyk.trimIfNotBlank())
        }
        assertEquals(2, processResult)
    }
}

private const val LANGUAGE_ENG_CODE = "ENG"
private const val LANGUAGE_CZ_CODE = "CZ"
private const val TECHNOLOGY_ANALOG_CODE = "01"
private const val TECHNOLOGY_IMAX_CODE = "05"
private const val LANGUAGE_SK_CODE = "SK"
