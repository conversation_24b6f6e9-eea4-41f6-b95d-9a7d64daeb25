package com.cleevio.cinemax.api.module.screening.entity

import com.cleevio.cinemax.api.util.createScreening
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.CsvSource
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.LocalTime
import java.util.UUID

class ScreeningTest {

    @ParameterizedTest
    @CsvSource(
        "2024-06-11T20:00:00, 2024-06-12, false", // starts tomorrow
        "2024-06-11T19:50:00, 2024-06-11, false", // starts in 10 minutes
        "2024-06-11T20:00:00, 2024-06-11, false", // starts now
        "2024-06-11T20:10:00, 2024-06-11, false", // started 10 minutes before
        "2024-06-11T20:00:00, 2024-06-10, true", // started 1 day before
        "2024-06-11T20:00:00, 2024-06-09, true" // started 2 days before
    )
    fun `test isScreeningDateInThePast - should correctly validate if datetime is in the past`(
        now: String,
        date: String,
        expectedResult: <PERSON><PERSON><PERSON>,
    ) {
        val screening = createScreening(
            auditoriumId = UUID.fromString("123e4567-e89b-12d3-a456-************"),
            movieId = UUID.fromString("123e4567-e89b-12d3-a456-************"),
            date = LocalDate.parse(date),
            time = LocalTime.parse("20:00:00")
        )

        assertEquals(expectedResult, screening.isScreeningDateInThePast(LocalDateTime.parse(now)))
    }

    @ParameterizedTest
    @CsvSource(
        "2024-06-10T21:00:00, 2024-06-10, 20:00:00, true", // started 1 hour before
        "2024-06-10T20:21:00, 2024-06-10, 20:00:00, true", // started 21 minutes before
        "2024-06-10T20:20:00, 2024-06-10, 20:00:00, false", // started 20 minutes before
        "2024-06-10T20:19:00, 2024-06-10, 20:00:00, false", // started 19 minutes before
        "2024-06-10T20:00:00, 2024-06-10, 20:00:00, false", // starts now
        "2024-06-10T19:00:00, 2024-06-10, 20:00:00, false" // starts in 1 hour
    )
    fun `test isSaleTimeLimitOver - should correctly validate if saleTimeLimit is over`(
        now: String,
        date: String,
        time: String,
        expectedResult: Boolean,
    ) {
        val screening = createScreening(
            auditoriumId = UUID.fromString("123e4567-e89b-12d3-a456-************"),
            movieId = UUID.fromString("123e4567-e89b-12d3-a456-************"),
            date = LocalDate.parse(date),
            time = LocalTime.parse(time),
            saleTimeLimit = 20
        )

        assertEquals(expectedResult, screening.isSaleTimeLimitOver(LocalDateTime.parse(now)))
    }
}
