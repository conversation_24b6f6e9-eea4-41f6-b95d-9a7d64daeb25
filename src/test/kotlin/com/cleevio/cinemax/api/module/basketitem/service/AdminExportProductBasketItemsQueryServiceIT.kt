package com.cleevio.cinemax.api.module.basketitem.service

import com.cleevio.cinemax.api.DatabaseCleanup
import com.cleevio.cinemax.api.IntegrationTest
import com.cleevio.cinemax.api.common.constant.ExportFormat
import com.cleevio.cinemax.api.common.constant.ExportableBoolean
import com.cleevio.cinemax.api.common.constant.NO_TAX_RATE
import com.cleevio.cinemax.api.common.constant.PaymentType
import com.cleevio.cinemax.api.common.constant.REDUCED_TAX_RATE
import com.cleevio.cinemax.api.common.constant.STANDARD_TAX_RATE
import com.cleevio.cinemax.api.common.constant.SUPER_REDUCED_TAX_RATE
import com.cleevio.cinemax.api.common.util.isEqualTo
import com.cleevio.cinemax.api.common.util.toCents
import com.cleevio.cinemax.api.module.basket.constant.BasketState
import com.cleevio.cinemax.api.module.basket.service.BasketRepository
import com.cleevio.cinemax.api.module.basketitem.constant.BasketItemType
import com.cleevio.cinemax.api.module.basketitem.controller.dto.AdminSearchProductBasketItemsFilter
import com.cleevio.cinemax.api.module.basketitem.entity.BasketItem
import com.cleevio.cinemax.api.module.basketitem.service.query.AdminExportProductBasketItemsQuery
import com.cleevio.cinemax.api.module.discountcard.constant.DiscountCardType
import com.cleevio.cinemax.api.module.discountcard.entity.DiscountCard
import com.cleevio.cinemax.api.module.discountcard.service.DiscountCardRepository
import com.cleevio.cinemax.api.module.discountcardusage.service.DiscountCardUsageRepository
import com.cleevio.cinemax.api.module.posconfiguration.service.PosConfigurationRepository
import com.cleevio.cinemax.api.module.product.constant.ProductType
import com.cleevio.cinemax.api.module.product.event.ProductCreatedOrUpdatedEvent
import com.cleevio.cinemax.api.module.product.service.ProductRepository
import com.cleevio.cinemax.api.module.product.service.ProductService
import com.cleevio.cinemax.api.module.productcategory.constant.ProductCategoryType
import com.cleevio.cinemax.api.module.productcategory.service.ProductCategoryService
import com.cleevio.cinemax.api.module.productcomponent.constant.ProductComponentUnit
import com.cleevio.cinemax.api.module.productcomponent.service.ProductComponentService
import com.cleevio.cinemax.api.module.productcomponentcategory.service.ProductComponentCategoryRepository
import com.cleevio.cinemax.api.module.productcomposition.service.ProductCompositionService
import com.cleevio.cinemax.api.util.createBasket
import com.cleevio.cinemax.api.util.createBasketItem
import com.cleevio.cinemax.api.util.createDiscountCard
import com.cleevio.cinemax.api.util.createDiscountCardUsage
import com.cleevio.cinemax.api.util.createPosConfiguration
import com.cleevio.cinemax.api.util.createProduct
import com.cleevio.cinemax.api.util.createProductCategory
import com.cleevio.cinemax.api.util.createProductComponent
import com.cleevio.cinemax.api.util.createProductComponentCategory
import com.cleevio.cinemax.api.util.createProductComposition
import com.cleevio.cinemax.api.util.mapToCreateOrUpdateProductCategoryCommand
import com.cleevio.cinemax.api.util.mapToCreateOrUpdateProductComponentCommand
import com.cleevio.cinemax.api.util.mapToCreateOrUpdateProductCompositionCommand
import com.cleevio.cinemax.api.util.mapToSyncCreateOrUpdateProductCommand
import com.cleevio.cinemax.api.util.toUUID
import io.mockk.Runs
import io.mockk.every
import io.mockk.just
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.Arguments
import org.junit.jupiter.params.provider.MethodSource
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.data.domain.Pageable
import org.springframework.data.domain.Sort
import java.math.BigDecimal
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.LocalTime
import java.util.UUID
import java.util.stream.Stream
import kotlin.test.assertEquals
import kotlin.test.assertTrue

class AdminExportProductBasketItemsQueryServiceIT @Autowired constructor(
    private val underTest: AdminExportProductBasketItemsQueryService,
    private val productService: ProductService,
    private val productRepository: ProductRepository,
    private val productComponentService: ProductComponentService,
    private val productCompositionService: ProductCompositionService,
    private val productCategoryService: ProductCategoryService,
    private val productComponentCategoryRepository: ProductComponentCategoryRepository,
    private val basketItemRepository: BasketItemRepository,
    private val basketRepository: BasketRepository,
    private val posConfigurationRepository: PosConfigurationRepository,
    private val discountCardRepository: DiscountCardRepository,
    private val discountCardUsageRepository: DiscountCardUsageRepository,
) : IntegrationTest(DatabaseCleanup.AFTER_EACH) {

    @BeforeEach
    fun setUp() {
        every { applicationEventPublisherMock.publishEvent(any<ProductCreatedOrUpdatedEvent>()) } just Runs
    }

    @Test
    fun `test AdminExportProductBasketItemsQuery - no products found - should return empty list`() {
        val result = underTest(
            AdminExportProductBasketItemsQuery(
                pageable = Pageable.unpaged(),
                filter = AdminSearchProductBasketItemsFilter(),
                exportFormat = ExportFormat.XLSX,
                username = "testUser"
            )
        )

        assertEquals(0, result.size)
    }

    @Test
    fun `test AdminExportProductBasketItemsQuery - no filter - should return all products with correct values`() {
        initData()
        val result = underTest(
            AdminExportProductBasketItemsQuery(
                pageable = Pageable.unpaged(),
                filter = AdminSearchProductBasketItemsFilter(),
                exportFormat = ExportFormat.XLSX,
                username = "testUser"
            )
        )

        assertEquals(8, result.size)

        // Assertions for BASKET_ITEM_PRODUCT_CASH with PRODUCT_1 - Coca Cola 0.33l
        val result1 = result.filter { it.productReceiptNumber!!.endsWith("01") }[0]
        assertEquals(LocalDate.of(2024, 12, 6), result1.paidAtDate)
        assertEquals(LocalTime.of(10, 30), result1.paidAtTime)
        assertEquals("000000000001", result1.productReceiptNumber)
        assertEquals(POS_CONFIGURATION.title, result1.posTitle)
        assertEquals(BASKET_1.updatedBy, result1.basketUpdatedBy)
        assertEquals(PRODUCT_1.title, result1.productTitle)
        assertEquals(PRODUCT_1.code, result1.productCode)
        assertEquals(PRODUCT_CATEGORY_1.title, result1.productCategoryTitle)
        assertEquals(ProductType.PRODUCT, result1.productType)
        assertEquals(1, result1.quantity)
        assertTrue(4.00.toBigDecimal() isEqualTo result1.purchasePrice.toCents())
        assertTrue(23.00.toBigDecimal() isEqualTo result1.sellingPrice.toCents())
        assertEquals(23, result1.vatPercentage)
        assertEquals(PaymentType.CASH, result1.paymentType)
        assertEquals(ExportableBoolean.FALSE, result1.isCancelled)
        assertEquals(ExportableBoolean.FALSE, result1.isDiscounted)
        assertEquals(null, result1.discountCardType)
        assertEquals(null, result1.discountCardCode)

        // Assertions for BASKET_ITEM_PRODUCT_CASHLESS with PRODUCT_3 - Popcorn XXL
        val result3 = result.filter { it.productReceiptNumber!!.endsWith("03") }[0]
        assertEquals(LocalDate.of(2024, 12, 1), result3.paidAtDate)
        assertEquals(LocalTime.of(15, 15), result3.paidAtTime)
        assertEquals("000000000003", result3.productReceiptNumber)
        assertEquals(POS_CONFIGURATION.title, result3.posTitle)
        assertEquals(BASKET_2.updatedBy, result3.basketUpdatedBy)
        assertEquals(PRODUCT_3.title, result3.productTitle)
        assertEquals(PRODUCT_3.code, result3.productCode)
        assertEquals(PRODUCT_CATEGORY_1.title, result3.productCategoryTitle)
        assertEquals(ProductType.PRODUCT, result3.productType)
        assertEquals(2, result3.quantity)
        assertTrue(6.50.toBigDecimal() isEqualTo result3.purchasePrice.toCents())
        assertTrue(25.00.toBigDecimal() isEqualTo result3.sellingPrice.toCents())
        assertEquals(19, result3.vatPercentage)
        assertEquals(PaymentType.CASHLESS, result3.paymentType)
        assertEquals(ExportableBoolean.FALSE, result3.isCancelled)
        assertEquals(ExportableBoolean.FALSE, result3.isDiscounted)
        assertEquals(null, result3.discountCardType)
        assertEquals(null, result3.discountCardCode)

        // Assertions for BASKET_ITEM_PRODUCT_IN_PRODUCT_CASH with PRODUCT_4 combo containing PRODUCT_1 & PRODUCT_3
        val result4 = result.filter { it.productReceiptNumber!!.endsWith("04") }
        assertEquals(2, result4.size)

        val result4Product1 = result4.first { it.productCode == PRODUCT_1.code }
        assertEquals(LocalDate.of(2024, 12, 6), result4Product1.paidAtDate)
        assertEquals(LocalTime.of(10, 30), result4Product1.paidAtTime)
        assertEquals("000000000004", result4Product1.productReceiptNumber)
        assertEquals(POS_CONFIGURATION.title, result4Product1.posTitle)
        assertEquals(BASKET_1.updatedBy, result4Product1.basketUpdatedBy)
        assertEquals(PRODUCT_1.title, result4Product1.productTitle)
        assertEquals(PRODUCT_1.code, result4Product1.productCode)
        assertEquals(PRODUCT_CATEGORY_1.title, result4Product1.productCategoryTitle)
        assertEquals(ProductType.PRODUCT_IN_PRODUCT, result4Product1.productType)
        assertEquals(9, result4Product1.quantity)
        assertTrue(4.toBigDecimal() isEqualTo result4Product1.purchasePrice.toCents())
        assertTrue(20.00.toBigDecimal() isEqualTo result4Product1.sellingPrice.toCents())
        assertEquals(23, result4Product1.vatPercentage)
        assertEquals(PaymentType.CASH, result4Product1.paymentType)
        assertEquals(ExportableBoolean.FALSE, result4Product1.isCancelled)
        assertEquals(ExportableBoolean.FALSE, result4Product1.isDiscounted)
        assertEquals(null, result4Product1.discountCardType)
        assertEquals(null, result4Product1.discountCardCode)

        val result4Product3 = result4.first { it.productCode == PRODUCT_3.code }
        assertEquals(LocalDate.of(2024, 12, 6), result4Product3.paidAtDate)
        assertEquals(LocalTime.of(10, 30), result4Product3.paidAtTime)
        assertEquals("000000000004", result4Product3.productReceiptNumber)
        assertEquals(POS_CONFIGURATION.title, result4Product3.posTitle)
        assertEquals(BASKET_1.updatedBy, result4Product3.basketUpdatedBy)
        assertEquals(PRODUCT_3.title, result4Product3.productTitle)
        assertEquals(PRODUCT_3.code, result4Product3.productCode)
        assertEquals(PRODUCT_CATEGORY_1.title, result4Product3.productCategoryTitle)
        assertEquals(ProductType.PRODUCT_IN_PRODUCT, result4Product3.productType)
        assertEquals(3, result4Product3.quantity)
        assertTrue(6.50.toBigDecimal() isEqualTo result4Product3.purchasePrice.toCents())
        assertTrue(40.00.toBigDecimal() isEqualTo result4Product3.sellingPrice.toCents())
        assertEquals(19, result4Product3.vatPercentage)
        assertEquals(PaymentType.CASH, result4Product3.paymentType)
        assertEquals(ExportableBoolean.FALSE, result4Product3.isCancelled)
        assertEquals(ExportableBoolean.FALSE, result4Product3.isDiscounted)
        assertEquals(null, result4Product3.discountCardType)
        assertEquals(null, result4Product3.discountCardCode)

        // Assertions for BASKET_ITEM_PRODUCT_IN_PRODUCT_CASHLESS with PRODUCT_5 combo containing PRODUCT_2 & PRODUCT_3
        val result5 = result.filter { it.productReceiptNumber!!.endsWith("05") }
        assertEquals(2, result5.size)

        val result5Product2 = result5.first { it.productCode == PRODUCT_2.code }
        assertEquals(LocalDate.of(2024, 12, 1), result5Product2.paidAtDate)
        assertEquals(LocalTime.of(15, 15), result5Product2.paidAtTime)
        assertEquals("000000000005", result5Product2.productReceiptNumber)
        assertEquals(POS_CONFIGURATION.title, result5Product2.posTitle)
        assertEquals(BASKET_2.updatedBy, result5Product2.basketUpdatedBy)
        assertEquals(PRODUCT_2.title, result5Product2.productTitle)
        assertEquals(PRODUCT_2.code, result5Product2.productCode)
        assertEquals(PRODUCT_CATEGORY_1.title, result5Product2.productCategoryTitle)
        assertEquals(ProductType.PRODUCT_IN_PRODUCT, result5Product2.productType)
        assertEquals(1, result5Product2.quantity)
        assertTrue(5.toBigDecimal() isEqualTo result5Product2.purchasePrice)
        assertTrue(25.00.toBigDecimal() isEqualTo result5Product2.sellingPrice)
        assertEquals(23, result5Product2.vatPercentage)
        assertEquals(PaymentType.CASHLESS, result5Product2.paymentType)
        assertEquals(ExportableBoolean.FALSE, result5Product2.isCancelled)
        assertEquals(ExportableBoolean.FALSE, result5Product2.isDiscounted)
        assertEquals(null, result5Product2.discountCardType)
        assertEquals(null, result5Product2.discountCardCode)

        val result5Product3 = result5.first { it.productCode == PRODUCT_3.code }
        assertEquals(LocalDate.of(2024, 12, 1), result5Product3.paidAtDate)
        assertEquals(LocalTime.of(15, 15), result5Product3.paidAtTime)
        assertEquals("000000000005", result5Product3.productReceiptNumber)
        assertEquals(POS_CONFIGURATION.title, result5Product3.posTitle)
        assertEquals(BASKET_2.updatedBy, result5Product3.basketUpdatedBy)
        assertEquals(PRODUCT_3.title, result5Product3.productTitle)
        assertEquals(PRODUCT_3.code, result5Product3.productCode)
        assertEquals(PRODUCT_CATEGORY_1.title, result5Product3.productCategoryTitle)
        assertEquals(ProductType.PRODUCT_IN_PRODUCT, result5Product3.productType)
        assertEquals(1, result5Product3.quantity)
        assertTrue(6.5.toBigDecimal() isEqualTo result5Product3.purchasePrice)
        assertTrue(45.00.toBigDecimal() isEqualTo result5Product3.sellingPrice)
        assertEquals(19, result5Product3.vatPercentage)
        assertEquals(PaymentType.CASHLESS, result5Product3.paymentType)
        assertEquals(ExportableBoolean.FALSE, result5Product3.isCancelled)
        assertEquals(ExportableBoolean.FALSE, result5Product3.isDiscounted)
        assertEquals(null, result5Product3.discountCardType)
        assertEquals(null, result5Product3.discountCardCode)

        // Assertions BASKET_ITEM_PRODUCT_DISCOUNT_CASH with PRODUCT_6
        val result6 = result.filter { it.productReceiptNumber!!.endsWith("06") }[0]
        assertEquals(LocalDate.of(2024, 12, 6), result6.paidAtDate)
        assertEquals(LocalTime.of(10, 30), result6.paidAtTime)
        assertEquals("000000000006", result6.productReceiptNumber)
        assertEquals(POS_CONFIGURATION.title, result6.posTitle)
        assertEquals(BASKET_1.updatedBy, result6.basketUpdatedBy)
        assertEquals(PRODUCT_6.title, result6.productTitle)
        assertEquals(PRODUCT_6.code, result6.productCode)
        assertEquals(PRODUCT_CATEGORY_2.title, result6.productCategoryTitle)
        assertEquals(ProductType.ADDITIONAL_SALE, result6.productType)
        assertEquals(1, result6.quantity)
        assertTrue(0.00.toBigDecimal() isEqualTo result6.purchasePrice)
        assertTrue((-5.5).toBigDecimal() isEqualTo result6.sellingPrice)
        assertEquals(0, result6.vatPercentage)
        assertEquals(PaymentType.CASH, result6.paymentType)
        assertEquals(ExportableBoolean.FALSE, result6.isCancelled)
        assertEquals(ExportableBoolean.TRUE, result6.isDiscounted)
        assertEquals(DiscountCardType.CARD, result6.discountCardType)
        assertEquals("44444", result6.discountCardCode)

        // Assertions for BASKET_ITEM_PRODUCT_DISCOUNT_CASHLESS_CANCELLED with PRODUCT_7
        val result7 = result.filter { it.productReceiptNumber!!.endsWith("07") }[0]
        assertEquals(LocalDate.of(2024, 12, 1), result7.paidAtDate)
        assertEquals(LocalTime.of(15, 15), result7.paidAtTime)
        assertEquals("000000000007", result7.productReceiptNumber)
        assertEquals(POS_CONFIGURATION.title, result7.posTitle)
        assertEquals(BASKET_2.updatedBy, result7.basketUpdatedBy)
        assertEquals(PRODUCT_7.title, result7.productTitle)
        assertEquals(PRODUCT_7.code, result7.productCode)
        assertEquals(PRODUCT_CATEGORY_2.title, result7.productCategoryTitle)
        assertEquals(ProductType.ADDITIONAL_SALE, result7.productType)
        assertEquals(1, result7.quantity)
        assertTrue(0.00.toBigDecimal() isEqualTo result7.purchasePrice)
        assertTrue((-2.25).toBigDecimal() isEqualTo result7.sellingPrice)
        assertEquals(0, result7.vatPercentage)
        assertEquals(PaymentType.CASHLESS, result7.paymentType)
        assertEquals(ExportableBoolean.TRUE, result7.isCancelled)
        assertEquals(ExportableBoolean.TRUE, result7.isDiscounted)
        assertEquals(DiscountCardType.VOUCHER, result7.discountCardType)
        assertEquals("55555", result7.discountCardCode)
    }

    @ParameterizedTest
    @MethodSource("filtersAndExpectedProductCodeProvider")
    fun `test AdminExportProductBasketItemsQuery - single filter - should result correct items`(
        filter: AdminSearchProductBasketItemsFilter,
        expectedResult: Set<Pair<String, String>>,
    ) {
        initData()
        val response = underTest(
            AdminExportProductBasketItemsQuery(
                pageable = Pageable.unpaged(),
                filter = filter,
                exportFormat = ExportFormat.XLSX,
                username = "test-user"
            )
        )

        assertEquals(
            expectedResult,
            response.map { it.productReceiptNumber!!.takeLast(2) to it.productCode }.toSet()
        )
    }

    @ParameterizedTest
    @MethodSource("sortsAndExpectedProductCodeProvider")
    fun `test AdminExportProductBasketItemsQuery - sorting - should result correct items`(
        pageable: Pageable,
        expectedResult: List<Pair<String, String>>,
    ) {
        initData()
        val response = underTest(
            AdminExportProductBasketItemsQuery(
                pageable = pageable,
                filter = AdminSearchProductBasketItemsFilter(),
                exportFormat = ExportFormat.XLSX,
                username = "test-user"
            )
        )

        assertEquals(
            expectedResult,
            response.map { it.productReceiptNumber!!.takeLast(2) to it.productCode }
        )
    }

    companion object {
        @JvmStatic
        fun filtersAndExpectedProductCodeProvider(): Stream<Arguments> {
            return Stream.of(
                Arguments.of(
                    AdminSearchProductBasketItemsFilter(),
                    setOf(
                        "01" to PRODUCT_1.code,
                        "03" to PRODUCT_3.code,
                        "04" to PRODUCT_1.code,
                        "04" to PRODUCT_3.code,
                        "05" to PRODUCT_2.code,
                        "05" to PRODUCT_3.code,
                        "06" to PRODUCT_6.code,
                        "07" to PRODUCT_7.code
                    ).toSet()
                ),
                Arguments.of(
                    AdminSearchProductBasketItemsFilter(
                        basketPaidAtFrom = BASKET_1.paidAt
                    ),
                    setOf(
                        "01" to PRODUCT_1.code,
                        "04" to PRODUCT_1.code,
                        "04" to PRODUCT_3.code,
                        "06" to PRODUCT_6.code
                    ).toSet()
                ),
                Arguments.of(
                    AdminSearchProductBasketItemsFilter(
                        basketPaidAtFrom = BASKET_2.paidAt!!,
                        basketPaidAtTo = BASKET_2.paidAt!!.plusDays(2)
                    ),
                    setOf(
                        "03" to PRODUCT_3.code,
                        "05" to PRODUCT_2.code,
                        "05" to PRODUCT_3.code,
                        "07" to PRODUCT_7.code
                    ).toSet()
                ),
                Arguments.of(
                    AdminSearchProductBasketItemsFilter(
                        posConfigurationIds = setOf(POS_CONFIGURATION.id)
                    ),
                    setOf(
                        "01" to PRODUCT_1.code,
                        "03" to PRODUCT_3.code,
                        "04" to PRODUCT_1.code,
                        "04" to PRODUCT_3.code,
                        "05" to PRODUCT_2.code,
                        "05" to PRODUCT_3.code,
                        "06" to PRODUCT_6.code,
                        "07" to PRODUCT_7.code
                    )
                        .toSet()
                ),
                Arguments.of(
                    AdminSearchProductBasketItemsFilter(
                        posConfigurationIds = setOf(1.toUUID())
                    ),
                    emptySet<String>()
                ),
                Arguments.of(
                    AdminSearchProductBasketItemsFilter(
                        productIds = setOf(1.toUUID())
                    ),
                    emptySet<String>()
                ),
                Arguments.of(
                    AdminSearchProductBasketItemsFilter(
                        productCategoryIds = setOf(PRODUCT_CATEGORY_2.id)
                    ),
                    setOf("06" to PRODUCT_6.code, "07" to PRODUCT_7.code).toSet()
                ),
                Arguments.of(
                    AdminSearchProductBasketItemsFilter(
                        productCode = "3"
                    ),
                    setOf("03" to PRODUCT_3.code).toSet()
                ),
                Arguments.of(
                    AdminSearchProductBasketItemsFilter(
                        paymentTypes = setOf(PaymentType.CASHLESS)
                    ),
                    setOf(
                        "03" to PRODUCT_3.code,
                        "05" to PRODUCT_2.code,
                        "05" to PRODUCT_3.code,
                        "07" to PRODUCT_7.code
                    ).toSet()
                ),
                Arguments.of(
                    AdminSearchProductBasketItemsFilter(
                        discountCardCode = "44444"
                    ),
                    setOf("06" to PRODUCT_6.code).toSet()
                ),
                Arguments.of(
                    AdminSearchProductBasketItemsFilter(
                        discountCardTypes = setOf(DiscountCardType.VOUCHER)
                    ),
                    setOf("07" to PRODUCT_7.code).toSet()
                ),
                Arguments.of(
                    AdminSearchProductBasketItemsFilter(
                        discountCardTitle = "Wonka voucher"
                    ),
                    setOf("07" to PRODUCT_7.code).toSet()
                ),
                Arguments.of(
                    AdminSearchProductBasketItemsFilter(
                        isDiscount = true
                    ),
                    setOf("06" to PRODUCT_6.code, "07" to PRODUCT_7.code).toSet()
                ),
                Arguments.of(
                    AdminSearchProductBasketItemsFilter(
                        isDiscount = false
                    ),
                    setOf(
                        "01" to PRODUCT_1.code,
                        "03" to PRODUCT_3.code,
                        "04" to PRODUCT_1.code,
                        "04" to PRODUCT_3.code,
                        "05" to PRODUCT_2.code,
                        "05" to PRODUCT_3.code
                    ).toSet()
                ),
                Arguments.of(
                    AdminSearchProductBasketItemsFilter(
                        isCancelled = true
                    ),
                    setOf("07" to PRODUCT_7.code).toSet()
                ),
                Arguments.of(
                    AdminSearchProductBasketItemsFilter(
                        isCancelled = false
                    ),
                    setOf(
                        "01" to PRODUCT_1.code,
                        "03" to PRODUCT_3.code,
                        "04" to PRODUCT_1.code,
                        "04" to PRODUCT_3.code,
                        "05" to PRODUCT_2.code,
                        "05" to PRODUCT_3.code,
                        "06" to PRODUCT_6.code
                    ).toSet()
                ),
                Arguments.of(
                    AdminSearchProductBasketItemsFilter(
                        basketUpdatedBy = setOf("monika")
                    ),
                    emptySet<Int>()
                ),
                Arguments.of(
                    AdminSearchProductBasketItemsFilter(
                        basketUpdatedBy = setOf("anonymous")
                    ),
                    setOf(
                        "01" to PRODUCT_1.code,
                        "03" to PRODUCT_3.code,
                        "04" to PRODUCT_1.code,
                        "04" to PRODUCT_3.code,
                        "05" to PRODUCT_2.code,
                        "05" to PRODUCT_3.code,
                        "06" to PRODUCT_6.code,
                        "07" to PRODUCT_7.code
                    ).toSet()
                )
            )
        }

        @JvmStatic
        fun sortsAndExpectedProductCodeProvider(): Stream<Arguments> {
            val defaultSort = arrayOf(
                Sort.Order.asc("basketItem.createdAt"),
                Sort.Order.asc("product.code")
            )

            return Stream.of(
                Arguments.of(
                    Pageable.unpaged(Sort.by(*defaultSort)),
                    listOf(
                        "01" to PRODUCT_1.code,
                        "03" to PRODUCT_3.code,
                        "04" to PRODUCT_1.code,
                        "04" to PRODUCT_3.code,
                        "05" to PRODUCT_2.code,
                        "05" to PRODUCT_3.code,
                        "06" to PRODUCT_6.code,
                        "07" to PRODUCT_7.code
                    )
                ),
                Arguments.of(
                    Pageable.unpaged(Sort.by(Sort.Order.desc("basketItem.type"), *defaultSort)),
                    listOf(
                        "06" to PRODUCT_6.code,
                        "07" to PRODUCT_7.code,
                        "01" to PRODUCT_1.code,
                        "03" to PRODUCT_3.code,
                        "04" to PRODUCT_1.code,
                        "04" to PRODUCT_3.code,
                        "05" to PRODUCT_2.code,
                        "05" to PRODUCT_3.code
                    )
                ),
                Arguments.of(
                    Pageable.unpaged(Sort.by(Sort.Order.asc("basketItem.type"), *defaultSort)),
                    listOf(
                        "01" to PRODUCT_1.code,
                        "03" to PRODUCT_3.code,
                        "04" to PRODUCT_1.code,
                        "04" to PRODUCT_3.code,
                        "05" to PRODUCT_2.code,
                        "05" to PRODUCT_3.code,
                        "06" to PRODUCT_6.code,
                        "07" to PRODUCT_7.code
                    )
                ),
                Arguments.of(
                    Pageable.unpaged(Sort.by(Sort.Order.asc("basketItem.quantity"), *defaultSort)),
                    listOf(
                        "01" to PRODUCT_1.code,
                        "05" to PRODUCT_2.code,
                        "05" to PRODUCT_3.code,
                        "06" to PRODUCT_6.code,
                        "07" to PRODUCT_7.code,
                        "03" to PRODUCT_3.code,
                        "04" to PRODUCT_1.code,
                        "04" to PRODUCT_3.code
                    )
                ),
                Arguments.of(
                    Pageable.unpaged(Sort.by(Sort.Order.desc("basketItem.price"), *defaultSort)),
                    listOf(
                        "05" to PRODUCT_2.code,
                        "05" to PRODUCT_3.code,
                        "04" to PRODUCT_1.code,
                        "04" to PRODUCT_3.code,
                        "03" to PRODUCT_3.code,
                        "01" to PRODUCT_1.code,
                        "07" to PRODUCT_7.code,
                        "06" to PRODUCT_6.code
                    )
                ),
                Arguments.of(
                    Pageable.unpaged(Sort.by(Sort.Order.asc("basketItem.branchName"), *defaultSort)),
                    listOf(
                        "01" to PRODUCT_1.code,
                        "03" to PRODUCT_3.code,
                        "04" to PRODUCT_1.code,
                        "04" to PRODUCT_3.code,
                        "05" to PRODUCT_2.code,
                        "05" to PRODUCT_3.code,
                        "06" to PRODUCT_6.code,
                        "07" to PRODUCT_7.code
                    )
                ),
                Arguments.of(
                    Pageable.unpaged(Sort.by(Sort.Order.desc("basketItem.branchName"), *defaultSort)),
                    listOf(
                        "01" to PRODUCT_1.code,
                        "03" to PRODUCT_3.code,
                        "04" to PRODUCT_1.code,
                        "04" to PRODUCT_3.code,
                        "05" to PRODUCT_2.code,
                        "05" to PRODUCT_3.code,
                        "06" to PRODUCT_6.code,
                        "07" to PRODUCT_7.code
                    )
                ),
                Arguments.of(
                    Pageable.unpaged(Sort.by(Sort.Order.asc("basket.paidAt"), *defaultSort)),
                    listOf(
                        "03" to PRODUCT_3.code,
                        "05" to PRODUCT_2.code,
                        "05" to PRODUCT_3.code,
                        "07" to PRODUCT_7.code,
                        "01" to PRODUCT_1.code,
                        "04" to PRODUCT_1.code,
                        "04" to PRODUCT_3.code,
                        "06" to PRODUCT_6.code
                    )
                ),
                Arguments.of(
                    Pageable.unpaged(Sort.by(Sort.Order.desc("basket.paidAt"), *defaultSort)),
                    listOf(
                        "01" to PRODUCT_1.code,
                        "04" to PRODUCT_1.code,
                        "04" to PRODUCT_3.code,
                        "06" to PRODUCT_6.code,
                        "03" to PRODUCT_3.code,
                        "05" to PRODUCT_2.code,
                        "05" to PRODUCT_3.code,
                        "07" to PRODUCT_7.code
                    )
                ),
                Arguments.of(
                    Pageable.unpaged(Sort.by(Sort.Order.asc("basket.paymentType"), *defaultSort)),
                    listOf(
                        "01" to PRODUCT_1.code,
                        "04" to PRODUCT_1.code,
                        "04" to PRODUCT_3.code,
                        "06" to PRODUCT_6.code,
                        "03" to PRODUCT_3.code,
                        "05" to PRODUCT_2.code,
                        "05" to PRODUCT_3.code,
                        "07" to PRODUCT_7.code
                    )
                ),
                Arguments.of(
                    Pageable.unpaged(Sort.by(Sort.Order.desc("basket.paymentType"), *defaultSort)),
                    listOf(
                        "03" to PRODUCT_3.code,
                        "05" to PRODUCT_2.code,
                        "05" to PRODUCT_3.code,
                        "07" to PRODUCT_7.code,
                        "01" to PRODUCT_1.code,
                        "04" to PRODUCT_1.code,
                        "04" to PRODUCT_3.code,
                        "06" to PRODUCT_6.code
                    )
                ),
                Arguments.of(
                    Pageable.unpaged(Sort.by(Sort.Order.asc("basket.updatedBy"), *defaultSort)),
                    listOf(
                        "01" to PRODUCT_1.code,
                        "03" to PRODUCT_3.code,
                        "04" to PRODUCT_1.code,
                        "04" to PRODUCT_3.code,
                        "05" to PRODUCT_2.code,
                        "05" to PRODUCT_3.code,
                        "06" to PRODUCT_6.code,
                        "07" to PRODUCT_7.code
                    )
                ),
                Arguments.of(
                    Pageable.unpaged(Sort.by(Sort.Order.asc("posConfiguration.title"), *defaultSort)),
                    listOf(
                        "01" to PRODUCT_1.code,
                        "03" to PRODUCT_3.code,
                        "04" to PRODUCT_1.code,
                        "04" to PRODUCT_3.code,
                        "05" to PRODUCT_2.code,
                        "05" to PRODUCT_3.code,
                        "06" to PRODUCT_6.code,
                        "07" to PRODUCT_7.code
                    )
                ),
                Arguments.of(
                    Pageable.unpaged(Sort.by(Sort.Order.desc("posConfiguration.title"), *defaultSort)),
                    listOf(
                        "01" to PRODUCT_1.code,
                        "03" to PRODUCT_3.code,
                        "04" to PRODUCT_1.code,
                        "04" to PRODUCT_3.code,
                        "05" to PRODUCT_2.code,
                        "05" to PRODUCT_3.code,
                        "06" to PRODUCT_6.code,
                        "07" to PRODUCT_7.code
                    )
                ),
                Arguments.of(
                    Pageable.unpaged(Sort.by(Sort.Order.asc("basketItem.isCancelled"), *defaultSort)),
                    listOf(
                        "01" to PRODUCT_1.code,
                        "03" to PRODUCT_3.code,
                        "04" to PRODUCT_1.code,
                        "04" to PRODUCT_3.code,
                        "05" to PRODUCT_2.code,
                        "05" to PRODUCT_3.code,
                        "06" to PRODUCT_6.code,
                        "07" to PRODUCT_7.code
                    )
                ),
                Arguments.of(
                    Pageable.unpaged(Sort.by(Sort.Order.desc("basketItem.isCancelled"), *defaultSort)),
                    listOf(
                        "07" to PRODUCT_7.code,
                        "01" to PRODUCT_1.code,
                        "03" to PRODUCT_3.code,
                        "04" to PRODUCT_1.code,
                        "04" to PRODUCT_3.code,
                        "05" to PRODUCT_2.code,
                        "05" to PRODUCT_3.code,
                        "06" to PRODUCT_6.code
                    )
                ),
                Arguments.of(
                    Pageable.unpaged(Sort.by(Sort.Order.desc("product.title"), *defaultSort)),
                    listOf(
                        "05" to PRODUCT_2.code,
                        "03" to PRODUCT_3.code,
                        "04" to PRODUCT_3.code,
                        "05" to PRODUCT_3.code,
                        "01" to PRODUCT_1.code,
                        "04" to PRODUCT_1.code,
                        "07" to PRODUCT_7.code,
                        "06" to PRODUCT_6.code
                    )
                ),
                Arguments.of(
                    Pageable.unpaged(Sort.by(Sort.Order.asc("product.title"), *defaultSort)),
                    listOf(
                        "06" to PRODUCT_6.code,
                        "07" to PRODUCT_7.code,
                        "01" to PRODUCT_1.code,
                        "04" to PRODUCT_1.code,
                        "03" to PRODUCT_3.code,
                        "04" to PRODUCT_3.code,
                        "05" to PRODUCT_3.code,
                        "05" to PRODUCT_2.code
                    )
                ),
                Arguments.of(
                    Pageable.unpaged(Sort.by(Sort.Order.asc("productCategory.title"), *defaultSort)),
                    listOf(
                        "01" to PRODUCT_1.code,
                        "03" to PRODUCT_3.code,
                        "04" to PRODUCT_1.code,
                        "04" to PRODUCT_3.code,
                        "05" to PRODUCT_2.code,
                        "05" to PRODUCT_3.code,
                        "06" to PRODUCT_6.code,
                        "07" to PRODUCT_7.code
                    )
                ),
                Arguments.of(
                    Pageable.unpaged(Sort.by(Sort.Order.asc("discountCard.title"), *defaultSort)),
                    listOf(
                        "06" to PRODUCT_6.code,
                        "07" to PRODUCT_7.code,
                        "01" to PRODUCT_1.code,
                        "03" to PRODUCT_3.code,
                        "04" to PRODUCT_1.code,
                        "04" to PRODUCT_3.code,
                        "05" to PRODUCT_2.code,
                        "05" to PRODUCT_3.code
                    )
                ),
                Arguments.of(
                    Pageable.unpaged(Sort.by(Sort.Order.desc("discountCard.type"), *defaultSort)),
                    listOf(
                        "07" to PRODUCT_7.code,
                        "06" to PRODUCT_6.code,
                        "01" to PRODUCT_1.code,
                        "03" to PRODUCT_3.code,
                        "04" to PRODUCT_1.code,
                        "04" to PRODUCT_3.code,
                        "05" to PRODUCT_2.code,
                        "05" to PRODUCT_3.code
                    )
                )
            )
        }
    }

    private fun initData() {
        listOf(PRODUCT_CATEGORY_1, PRODUCT_CATEGORY_2).forEach {
            productCategoryService.syncCreateOrUpdateProductCategory(
                mapToCreateOrUpdateProductCategoryCommand(it)
            )
        }

        productComponentCategoryRepository.save(PRODUCT_COMPONENT_CATEGORY_1)
        setOf(
            PRODUCT_COMPONENT_1,
            PRODUCT_COMPONENT_2,
            PRODUCT_COMPONENT_3,
            PRODUCT_COMPONENT_4,
            PRODUCT_COMPONENT_5
        ).forEach {
            productComponentService.syncCreateOrUpdateProductComponent(
                mapToCreateOrUpdateProductComponentCommand(it)
            )
        }

        setOf(
            PRODUCT_1,
            PRODUCT_2,
            PRODUCT_3,
            PRODUCT_4,
            PRODUCT_5
        ).forEach { productService.syncCreateOrUpdateProduct(mapToSyncCreateOrUpdateProductCommand(it, PRODUCT_CATEGORY_1.id)) }
        productRepository.save(PRODUCT_3.apply { taxRate = REDUCED_TAX_RATE })

        setOf(
            PRODUCT_6,
            PRODUCT_7
        ).forEach { productService.syncCreateOrUpdateProduct(mapToSyncCreateOrUpdateProductCommand(it, PRODUCT_CATEGORY_2.id)) }

        setOf(
            PRODUCT_COMPOSITION_1,
            PRODUCT_COMPOSITION_2,
            PRODUCT_COMPOSITION_3,
            PRODUCT_COMPOSITION_4,
            PRODUCT_COMPOSITION_5,
            PRODUCT_COMPOSITION_6,
            PRODUCT_COMPOSITION_7,
            PRODUCT_COMPOSITION_8,
            PRODUCT_COMPOSITION_9
        ).forEach { productCompositionService.createOrUpdateProductComposition(mapToCreateOrUpdateProductCompositionCommand(it)) }

        posConfigurationRepository.save(POS_CONFIGURATION)
        basketRepository.saveAll(
            listOf(
                BASKET_1,
                BASKET_2
            )
        )

        val basketItem1 = BASKET_ITEM_PRODUCT_CASH(PRODUCT_1.id)
        val basketItem2 = BASKET_ITEM_PRODUCT_CASHLESS(PRODUCT_3.id)
        val basketItem3 = BASKET_ITEM_PRODUCT_IN_PRODUCT_CASH(PRODUCT_4.id)
        val basketItem5 = BASKET_ITEM_PRODUCT_IN_PRODUCT_CASHLESS(PRODUCT_5.id)
        val discountedBasketItem6 = BASKET_ITEM_PRODUCT_DISCOUNT_CASH(PRODUCT_6.id)
        val discountedBasketItem7 = BASKET_ITEM_PRODUCT_DISCOUNT_CASHLESS_CANCELLED(PRODUCT_7.id)
        val basketItemDeleted = BASKET_ITEM_PRODUCT_CASH_DELETED(PRODUCT_5.id)

        basketItemRepository.saveAll(
            setOf(
                basketItem1,
                basketItem2,
                basketItem3,
                basketItem5,
                discountedBasketItem6,
                discountedBasketItem7,
                basketItemDeleted
            )
        )

        val discountCard1 = DISCOUNT_CARD_1(PRODUCT_6.id)
        val discountCard2 = DISCOUNT_CARD_2(PRODUCT_7.id)
        discountCardRepository.saveAll(setOf(discountCard1, discountCard2))

        setOf(discountCard1 to discountedBasketItem6, discountCard2 to discountedBasketItem7).forEach {
            discountCardUsageRepository.save(
                createDiscountCardUsage(
                    discountCardId = it.first.id,
                    basketId = it.second.basketId,
                    posConfigurationId = POS_CONFIGURATION.id,
                    productDiscountBasketItemId = it.second.id
                )
            )
        }
    }
}

private val PRODUCT_COMPONENT_CATEGORY_1 = createProductComponentCategory()

private val PRODUCT_CATEGORY_1 = createProductCategory(
    originalId = 1,
    code = "1",
    title = "Kategorie produktu",
    type = ProductCategoryType.PRODUCT,
    taxRate = STANDARD_TAX_RATE
)

private val PRODUCT_CATEGORY_2 = createProductCategory(
    originalId = 2,
    code = "2",
    title = "Kategorie slev",
    type = ProductCategoryType.DISCOUNT,
    taxRate = NO_TAX_RATE
)

private val PRODUCT_1 = createProduct(
    originalId = 1,
    code = "1",
    productCategoryId = PRODUCT_CATEGORY_1.id,
    title = "Coca Cola 0.33l",
    type = ProductType.PRODUCT,
    soldInBuffet = true,
    soldInCafe = false,
    soldInVip = false
)
private val PRODUCT_2 = createProduct(
    originalId = 2,
    code = "2",
    productCategoryId = PRODUCT_CATEGORY_1.id,
    title = "Sprite 0.33l",
    type = ProductType.PRODUCT,
    soldInBuffet = false,
    soldInCafe = true,
    soldInVip = false
)
private val PRODUCT_3 = createProduct(
    originalId = 3,
    code = "3",
    productCategoryId = PRODUCT_CATEGORY_1.id,
    title = "Popcorn XXL",
    type = ProductType.PRODUCT,
    taxRate = REDUCED_TAX_RATE,
    soldInBuffet = false,
    soldInCafe = false,
    soldInVip = true
)
private val PRODUCT_4 = createProduct(
    originalId = 4,
    code = "4",
    productCategoryId = PRODUCT_CATEGORY_1.id,
    title = "Combo Popcorn XXL + 3x Coca Cola 0,33l",
    type = ProductType.PRODUCT_IN_PRODUCT,
    taxRate = SUPER_REDUCED_TAX_RATE,
    soldInBuffet = true,
    soldInCafe = true,
    soldInVip = true
)
private val PRODUCT_5 = createProduct(
    originalId = 5,
    code = "5",
    productCategoryId = PRODUCT_CATEGORY_1.id,
    title = "Combo Popcorn XXL + Sprite 0,33l",
    type = ProductType.PRODUCT_IN_PRODUCT,
    taxRate = STANDARD_TAX_RATE,
    soldInBuffet = true,
    soldInCafe = true,
    soldInVip = true
)
private val PRODUCT_6 = createProduct(
    originalId = 6,
    code = "6",
    productCategoryId = PRODUCT_CATEGORY_2.id,
    title = "Additional Sale",
    type = ProductType.ADDITIONAL_SALE,
    price = 0.toBigDecimal(),
    discountAmount = BigDecimal.valueOf(5),
    soldInBuffet = true,
    soldInCafe = true,
    soldInVip = false
)
private val PRODUCT_7 = createProduct(
    originalId = 7,
    code = "7",
    productCategoryId = PRODUCT_CATEGORY_2.id,
    title = "Additional Sale 100%",
    type = ProductType.ADDITIONAL_SALE,
    price = 0.toBigDecimal(),
    discountAmount = BigDecimal.valueOf(100),
    soldInBuffet = true,
    soldInCafe = false,
    soldInVip = true
)
private val PRODUCT_COMPONENT_1 = createProductComponent(
    originalId = 1,
    code = "1",
    title = "Coca Cola 0,33l",
    unit = ProductComponentUnit.KS,
    stockQuantity = BigDecimal.valueOf(90),
    productComponentCategoryId = PRODUCT_COMPONENT_CATEGORY_1.id,
    purchasePrice = 4.toBigDecimal()
)
private val PRODUCT_COMPONENT_2 = createProductComponent(
    originalId = 2,
    code = "2",
    title = "Sprite 0,33l",
    unit = ProductComponentUnit.KS,
    stockQuantity = BigDecimal.valueOf(5.0),
    productComponentCategoryId = PRODUCT_COMPONENT_CATEGORY_1.id,
    purchasePrice = 5.toBigDecimal()
)
private val PRODUCT_COMPONENT_3 = createProductComponent(
    originalId = 3,
    code = "3",
    title = "Kukurica",
    unit = ProductComponentUnit.KG,
    stockQuantity = BigDecimal.valueOf(1000),
    productComponentCategoryId = PRODUCT_COMPONENT_CATEGORY_1.id,
    purchasePrice = 3.toBigDecimal()
)
private val PRODUCT_COMPONENT_4 = createProductComponent(
    originalId = 4,
    code = "4",
    title = "Soľ",
    unit = ProductComponentUnit.KG,
    stockQuantity = BigDecimal.valueOf(100),
    productComponentCategoryId = PRODUCT_COMPONENT_CATEGORY_1.id,
    purchasePrice = 0.5.toBigDecimal()
)
private val PRODUCT_COMPONENT_5 = createProductComponent(
    originalId = 5,
    code = "5",
    title = "Tuk",
    unit = ProductComponentUnit.KG,
    stockQuantity = BigDecimal.valueOf(50),
    productComponentCategoryId = PRODUCT_COMPONENT_CATEGORY_1.id,
    purchasePrice = 1.toBigDecimal()
)

// Coca Cola 0.33l  - Coca Cola 0.33l (1x)
private val PRODUCT_COMPOSITION_1 = createProductComposition(
    originalId = 1,
    productId = PRODUCT_1.id,
    productComponentId = PRODUCT_COMPONENT_1.id,
    amount = BigDecimal.valueOf(1)
)

// Sprite 0.33l - Sprite 0.33l (1x)
private val PRODUCT_COMPOSITION_2 = createProductComposition(
    originalId = 2,
    productId = PRODUCT_2.id,
    productComponentId = PRODUCT_COMPONENT_2.id,
    amount = BigDecimal.valueOf(1)
)

// Popcorn XXL - Kukurica (1x)
private val PRODUCT_COMPOSITION_3 = createProductComposition(
    originalId = 3,
    productId = PRODUCT_3.id,
    productComponentId = PRODUCT_COMPONENT_3.id,
    amount = BigDecimal.valueOf(1),
    productInProductId = null
)

// Popcorn XXL - Soľ (3x)
private val PRODUCT_COMPOSITION_4 = createProductComposition(
    originalId = 4,
    productId = PRODUCT_3.id,
    productComponentId = PRODUCT_COMPONENT_4.id,
    amount = BigDecimal.valueOf(3)
)

// Popcorn XXL - Tuk (2x)
private val PRODUCT_COMPOSITION_5 = createProductComposition(
    originalId = 5,
    productId = PRODUCT_3.id,
    productComponentId = PRODUCT_COMPONENT_5.id,
    amount = BigDecimal.valueOf(2)
)

// Combo Popcorn XXL + MEGA Coca Cola 0,33l -> Coca Cola 0,33l (3x)
private val PRODUCT_COMPOSITION_6 = createProductComposition(
    originalId = 6,
    productId = PRODUCT_4.id,
    productComponentId = null,
    productInProductId = PRODUCT_1.id,
    amount = BigDecimal.valueOf(3),
    productInProductPrice = 20.toBigDecimal()
)

// Combo Popcorn XXL + MEGA Coca Cola 0,33l -> Popcorn XXL (1x)
private val PRODUCT_COMPOSITION_7 = createProductComposition(
    originalId = 7,
    productId = PRODUCT_4.id,
    productComponentId = null,
    productInProductId = PRODUCT_3.id,
    amount = BigDecimal.valueOf(1),
    productInProductPrice = 40.toBigDecimal()
)

// Combo Popcorn XXL + Sprite 0,33l -> Sprite 0,33l (1x)
private val PRODUCT_COMPOSITION_8 = createProductComposition(
    originalId = 8,
    productId = PRODUCT_5.id,
    productInProductId = PRODUCT_2.id,
    productComponentId = null,
    amount = BigDecimal.valueOf(1),
    productInProductPrice = 25.toBigDecimal()
)

// Combo Popcorn XXL + Sprite 0,33l -> Popcorn XXL (1x)
private val PRODUCT_COMPOSITION_9 = createProductComposition(
    originalId = 9,
    productId = PRODUCT_5.id,
    productInProductId = PRODUCT_3.id,
    productComponentId = null,
    amount = BigDecimal.valueOf(1),
    productInProductPrice = 45.toBigDecimal()
)

// POS CONFIGURATIONS
private val POS_CONFIGURATION = createPosConfiguration(macAddress = "AA:BB:CC:DD:EE")

// BASKETS
private val BASKET_1 = createBasket(
    state = BasketState.PAID,
    paymentType = PaymentType.CASH,
    paymentPosConfigurationId = POS_CONFIGURATION.id,
    paidAt = LocalDateTime.of(2024, 12, 6, 10, 30)
)
private val BASKET_2 = createBasket(
    state = BasketState.PAID,
    paymentType = PaymentType.CASHLESS,
    paymentPosConfigurationId = POS_CONFIGURATION.id,
    paidAt = LocalDateTime.of(2024, 12, 1, 15, 15)
)

// BASKET ITEMS
private val BASKET_ITEM_PRODUCT_CASH: (UUID) -> BasketItem = { productId: UUID ->
    createBasketItem(
        basketId = BASKET_1.id,
        type = BasketItemType.PRODUCT,
        price = 23.toBigDecimal(),
        productId = productId,
        quantity = 1,
        productReceiptNumber = "000000000001"
    )
}
private val BASKET_ITEM_PRODUCT_CASHLESS: (UUID) -> BasketItem = { productId: UUID ->
    createBasketItem(
        basketId = BASKET_2.id,
        type = BasketItemType.PRODUCT,
        price = 25.toBigDecimal(),
        productId = productId,
        quantity = 2,
        productReceiptNumber = "000000000003"
    )
}
private val BASKET_ITEM_PRODUCT_IN_PRODUCT_CASH: (UUID) -> BasketItem = { productId: UUID ->
    createBasketItem(
        basketId = BASKET_1.id,
        type = BasketItemType.PRODUCT,
        price = 60.toBigDecimal(),
        productId = productId,
        quantity = 3,
        productReceiptNumber = "000000000004"
    )
}
private val BASKET_ITEM_PRODUCT_IN_PRODUCT_CASHLESS: (UUID) -> BasketItem = { productId: UUID ->
    createBasketItem(
        basketId = BASKET_2.id,
        type = BasketItemType.PRODUCT,
        price = 70.toBigDecimal(),
        productId = productId,
        quantity = 1,
        productReceiptNumber = "000000000005"
    )
}

private val BASKET_ITEM_PRODUCT_DISCOUNT_CASH: (UUID) -> BasketItem = { productId: UUID ->
    createBasketItem(
        basketId = BASKET_1.id,
        type = BasketItemType.PRODUCT_DISCOUNT,
        price = (-5.5).toBigDecimal(),
        productId = productId,
        quantity = 1,
        productReceiptNumber = "000000000006"
    )
}
private val BASKET_ITEM_PRODUCT_DISCOUNT_CASHLESS_CANCELLED: (UUID) -> BasketItem = { productId: UUID ->
    createBasketItem(
        basketId = BASKET_2.id,
        type = BasketItemType.PRODUCT_DISCOUNT,
        price = (-2.25).toBigDecimal(),
        productId = productId,
        quantity = 1,
        productReceiptNumber = "000000000007"
    ) { it.isCancelled = true }
}
private val BASKET_ITEM_PRODUCT_CASH_DELETED: (UUID) -> BasketItem = { productId: UUID ->
    createBasketItem(
        basketId = BASKET_1.id,
        type = BasketItemType.PRODUCT,
        price = 100.toBigDecimal(),
        productId = productId,
        quantity = 100,
        productReceiptNumber = "000000000008"
    ) { it.markDeleted() }
}

private val DISCOUNT_CARD_1: (UUID) -> DiscountCard = { productId: UUID ->
    createDiscountCard(
        originalId = 1,
        ticketDiscountId = null,
        productDiscountId = productId,
        title = "VIP karta",
        code = "44444",
        type = DiscountCardType.CARD
    )
}
private val DISCOUNT_CARD_2: (UUID) -> DiscountCard = { productId: UUID ->
    createDiscountCard(
        originalId = 2,
        ticketDiscountId = null,
        productDiscountId = productId,
        title = "Wonka voucher",
        code = "55555",
        type = DiscountCardType.VOUCHER
    )
}
