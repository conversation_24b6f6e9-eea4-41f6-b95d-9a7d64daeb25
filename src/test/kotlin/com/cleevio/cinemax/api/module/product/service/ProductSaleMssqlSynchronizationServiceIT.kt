package com.cleevio.cinemax.api.module.product.service

import com.cleevio.cinemax.api.MssqlIntegrationTest
import com.cleevio.cinemax.api.common.constant.PaymentType
import com.cleevio.cinemax.api.common.constant.STANDARD_TAX_RATE
import com.cleevio.cinemax.api.common.util.isEqualTo
import com.cleevio.cinemax.api.common.util.truncatedToSeconds
import com.cleevio.cinemax.api.module.basket.service.command.CreateProductSalesBasketCommand
import com.cleevio.cinemax.api.module.basketitem.constant.BasketItemType
import com.cleevio.cinemax.api.module.branch.service.BranchMssqlFinderRepository
import com.cleevio.cinemax.api.module.product.constant.ProductType
import com.cleevio.cinemax.api.module.productcategory.constant.ProductCategoryType
import com.cleevio.cinemax.api.util.createBranch
import com.cleevio.cinemax.api.util.createProduct
import com.cleevio.cinemax.api.util.createProductCategory
import io.mockk.Runs
import io.mockk.every
import io.mockk.just
import io.mockk.verify
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.test.context.jdbc.Sql
import org.springframework.test.context.jdbc.SqlConfig
import org.springframework.test.context.jdbc.SqlGroup
import java.time.LocalDate
import java.time.LocalDateTime
import java.util.UUID
import kotlin.test.assertEquals
import kotlin.test.assertTrue

@SqlGroup(
    Sql(
        scripts = [
            "/db/mssql-buffet/test/init_mssql_product_sale.sql"
        ],
        config = SqlConfig(
            dataSource = "mssqlBuffetDataSource",
            transactionManager = "mssqlBuffetTransactionManager"
        )
    ),
    Sql(
        scripts = [
            "/db/mssql-buffet/test/drop_mssql_product_sale.sql"
        ],
        config = SqlConfig(
            dataSource = "mssqlBuffetDataSource",
            transactionManager = "mssqlBuffetTransactionManager"
        ),
        executionPhase = Sql.ExecutionPhase.AFTER_TEST_METHOD
    )
)
class ProductSaleMssqlSynchronizationServiceIT @Autowired constructor(
    private val underTest: ProductSaleMssqlSynchronizationService,
    private val branchMssqlFinderRepository: BranchMssqlFinderRepository,
) : MssqlIntegrationTest() {

    @Test
    fun `test synchronizeAll - all products, branches and product categories exist - should init 5 product sale commands`() {
        val productOriginalIds1 = setOf(PRODUCT_1.originalId!!, PRODUCT_2.originalId!!, PRODUCT_3.originalId!!)
        val productOriginalIds2 = setOf(PRODUCT_2.originalId!!, PRODUCT_3.originalId!!)
        val productCategoryIds1 = setOf(PRODUCT_CATEGORY_1.id, PRODUCT_CATEGORY_2.id)
        val productCategoryIds2 = setOf(PRODUCT_CATEGORY_2.id)

        every { branchJpaFinderServiceMock.findAll() } returns listOf(BRANCH_1, BRANCH_2, BRANCH_3)
        every { productJpaFinderServiceMock.findAllByOriginalIdInAndDeletedAtIsNull(productOriginalIds1) } returns
            listOf(PRODUCT_1, PRODUCT_2, PRODUCT_3)
        every { productJpaFinderServiceMock.findAllByOriginalIdInAndDeletedAtIsNull(productOriginalIds2) } returns
            listOf(PRODUCT_2, PRODUCT_3)
        every { productCategoryJpaFinderServiceMock.findAllNonDeletedByIdIn(productCategoryIds1) } returns
            listOf(PRODUCT_CATEGORY_1, PRODUCT_CATEGORY_2)
        every { productCategoryJpaFinderServiceMock.findAllNonDeletedByIdIn(setOf(PRODUCT_CATEGORY_2.id)) } returns
            listOf(PRODUCT_CATEGORY_2)
        every { basketServiceMock.createProductSalesBaskets(any()) } just Runs

        underTest.synchronizeAll(
            syncStartDate = LocalDate.parse("2024-12-20"),
            syncEndDate = LocalDate.parse("2025-01-01"),
            batchDaySpan = 7
        )

        val commandCaptor = mutableListOf<List<CreateProductSalesBasketCommand>>()
        val productOriginalIdCaptor = mutableListOf<Set<Int>>()
        val productCategoryIdCaptor = mutableListOf<Set<UUID>>()

        verify {
            branchJpaFinderServiceMock.findAll()
            productJpaFinderServiceMock.findAllByOriginalIdInAndDeletedAtIsNull(capture(productOriginalIdCaptor))
            productCategoryJpaFinderServiceMock.findAllNonDeletedByIdIn(capture(productCategoryIdCaptor))
            basketServiceMock.createProductSalesBaskets(capture(commandCaptor))
        }

        assertEquals(2, productOriginalIdCaptor.size)
        assertEquals(2, productCategoryIdCaptor.size)
        assertEquals(2, commandCaptor.size)

        val productOriginalIdsBatch1 = productOriginalIdCaptor[0]
        val productOriginalIdsBatch2 = productOriginalIdCaptor[1]
        val productCategoryIdsBatch1 = productCategoryIdCaptor[0]
        val productCategoryIdsBatch2 = productCategoryIdCaptor[1]
        val commandsBatch1 = commandCaptor[0]
        val commandsBatch2 = commandCaptor[1]

        assertTrue(productOriginalIdsBatch1.containsAll(productOriginalIds1))
        assertTrue(productCategoryIdsBatch1.containsAll(productCategoryIds1))
        assertEquals(3, commandsBatch1.size)
        assertCommandEquals(EXPECTED_COMMAND_1, commandsBatch1[0])
        assertCommandEquals(EXPECTED_COMMAND_2, commandsBatch1[1])
        assertCommandEquals(EXPECTED_COMMAND_3, commandsBatch1[2])

        assertTrue(productOriginalIdsBatch2.containsAll(productOriginalIds2))
        assertTrue(productCategoryIdsBatch2.containsAll(productCategoryIds2))
        assertEquals(2, commandsBatch2.size)
        assertCommandEquals(EXPECTED_COMMAND_4, commandsBatch2[0])
        assertCommandEquals(EXPECTED_COMMAND_5, commandsBatch2[1])
    }

    @Test
    fun `test synchronizeAll - all products, BRANCH_2 and PRODUCT_2 does not exist - should init 1 product sale command`() {
        val productOriginalIds1 = setOf(PRODUCT_1.originalId!!, PRODUCT_2.originalId!!, PRODUCT_3.originalId!!)
        val productOriginalIds2 = setOf(PRODUCT_2.originalId!!, PRODUCT_3.originalId!!)
        val productCategoryIds1 = setOf(PRODUCT_CATEGORY_2.id)

        every { branchJpaFinderServiceMock.findAll() } returns listOf(BRANCH_1)
        every { productJpaFinderServiceMock.findAllByOriginalIdInAndDeletedAtIsNull(productOriginalIds1) } returns
            listOf(PRODUCT_1, PRODUCT_3)
        every { productJpaFinderServiceMock.findAllByOriginalIdInAndDeletedAtIsNull(productOriginalIds2) } returns
            listOf(PRODUCT_3)
        every { productCategoryJpaFinderServiceMock.findAllNonDeletedByIdIn(productCategoryIds1) } returns
            listOf(PRODUCT_CATEGORY_2)
        every { basketServiceMock.createProductSalesBaskets(any()) } just Runs

        underTest.synchronizeAll(
            syncStartDate = LocalDate.parse("2024-12-20"),
            syncEndDate = LocalDate.parse("2025-01-01"),
            batchDaySpan = 7
        )

        val commandCaptor = mutableListOf<List<CreateProductSalesBasketCommand>>()
        val productOriginalIdCaptor = mutableListOf<Set<Int>>()
        val productCategoryIdCaptor = mutableListOf<Set<UUID>>()

        verify {
            branchJpaFinderServiceMock.findAll()
            productJpaFinderServiceMock.findAllByOriginalIdInAndDeletedAtIsNull(capture(productOriginalIdCaptor))
            productCategoryJpaFinderServiceMock.findAllNonDeletedByIdIn(capture(productCategoryIdCaptor))
            basketServiceMock.createProductSalesBaskets(capture(commandCaptor))
        }

        assertEquals(2, productOriginalIdCaptor.size)
        assertEquals(1, productCategoryIdCaptor.size)
        assertEquals(1, commandCaptor.size)

        val productOriginalIdsBatch1 = productOriginalIdCaptor[0]
        val productOriginalIdsBatch2 = productOriginalIdCaptor[1]
        val productCategoryIdsBatch1 = productCategoryIdCaptor[0]
        val commandsBatch1 = commandCaptor[0]

        assertTrue(productOriginalIdsBatch1.containsAll(productOriginalIds1))
        assertTrue(productCategoryIdsBatch1.containsAll(productCategoryIds1))
        assertEquals(1, commandsBatch1.size)
        assertCommandEquals(EXPECTED_COMMAND_3, commandsBatch1[0])

        assertTrue(productOriginalIdsBatch2.containsAll(productOriginalIds2))
    }

    private fun assertCommandEquals(expected: CreateProductSalesBasketCommand, actual: CreateProductSalesBasketCommand) {
        assertEquals(expected.paidAt.truncatedToSeconds(), actual.paidAt.truncatedToSeconds())
        assertEquals(expected.paymentType, actual.paymentType)
        assertEquals(expected.receiptNumber, actual.receiptNumber)
        assertEquals(expected.productId, actual.productId)
        assertEquals(expected.type, actual.type)
        assertTrue(expected.price isEqualTo actual.price)
        assertEquals(expected.quantity, actual.quantity)
        assertEquals(expected.isCancelled, actual.isCancelled)
        assertEquals(expected.branchId, actual.branchId)
    }
}

private val BRANCH_1 = createBranch()
private val BRANCH_2 = createBranch(
    originalId = 2,
    productSalesCode = 2,
    name = "Kosice"
)
private val PRODUCT_CATEGORY_1 = createProductCategory(
    originalId = 10,
    code = "01",
    title = "Zlava",
    type = ProductCategoryType.DISCOUNT,
    order = 10,
    taxRate = STANDARD_TAX_RATE
)
private val PRODUCT_CATEGORY_2 = createProductCategory(
    originalId = 20,
    code = "02",
    title = "Popcorn+Napoje",
    type = ProductCategoryType.PRODUCT,
    order = 1,
    taxRate = STANDARD_TAX_RATE
)
private val BRANCH_3 = createBranch(
    originalId = 3,
    productSalesCode = 3,
    name = "Nitra"
)
private val PRODUCT_1 = createProduct(
    originalId = 11,
    code = "01",
    productCategoryId = PRODUCT_CATEGORY_1.id,
    title = "Zlava 2e",
    order = null,
    type = ProductType.ADDITIONAL_SALE,
    price = (-4).toBigDecimal()
)
private val PRODUCT_2 = createProduct(
    originalId = 22,
    code = "02",
    productCategoryId = PRODUCT_CATEGORY_2.id,
    title = "Popcorn XXL",
    order = 25,
    type = ProductType.PRODUCT,
    price = 4.toBigDecimal(),
    soldInBuffet = false
)
private val PRODUCT_3 = createProduct(
    originalId = 33,
    code = "03",
    productCategoryId = PRODUCT_CATEGORY_2.id,
    title = "Coca Cola 0.33l",
    order = 23,
    type = ProductType.PRODUCT,
    price = 3.toBigDecimal(),
    stockQuantityThreshold = 10
)
private val EXPECTED_COMMAND_1 = CreateProductSalesBasketCommand(
    paidAt = LocalDateTime.of(2024, 12, 20, 16, 8),
    paymentType = PaymentType.CASHLESS,
    receiptNumber = "0001485163",
    productId = PRODUCT_1.id,
    type = BasketItemType.PRODUCT_DISCOUNT,
    price = (-4).toBigDecimal(),
    quantity = 1,
    isCancelled = false,
    branchId = BRANCH_2.id
)
private val EXPECTED_COMMAND_2 = CreateProductSalesBasketCommand(
    paidAt = LocalDateTime.of(2024, 12, 20, 16, 8),
    paymentType = PaymentType.CASHLESS,
    receiptNumber = "0001485163",
    productId = PRODUCT_2.id,
    type = BasketItemType.PRODUCT,
    price = 4.toBigDecimal(),
    quantity = 1,
    isCancelled = false,
    branchId = BRANCH_2.id
)
private val EXPECTED_COMMAND_3 = CreateProductSalesBasketCommand(
    paidAt = LocalDateTime.of(2024, 12, 26, 15, 36),
    paymentType = PaymentType.CASH,
    receiptNumber = "0001485165",
    productId = PRODUCT_3.id,
    type = BasketItemType.PRODUCT,
    price = 9.toBigDecimal(),
    quantity = 3,
    isCancelled = false,
    branchId = BRANCH_1.id
)
private val EXPECTED_COMMAND_4 = CreateProductSalesBasketCommand(
    paidAt = LocalDateTime.of(2024, 12, 28, 14, 36),
    paymentType = PaymentType.CASH,
    receiptNumber = "0001485166",
    productId = PRODUCT_2.id,
    type = BasketItemType.PRODUCT,
    price = 4.toBigDecimal(),
    quantity = 1,
    isCancelled = false,
    branchId = BRANCH_1.id
)
private val EXPECTED_COMMAND_5 = CreateProductSalesBasketCommand(
    paidAt = LocalDateTime.of(2024, 12, 31, 15, 45),
    paymentType = PaymentType.CASH,
    receiptNumber = "0001485167",
    productId = PRODUCT_3.id,
    type = BasketItemType.PRODUCT,
    price = 3.toBigDecimal(),
    quantity = 1,
    isCancelled = true,
    branchId = BRANCH_2.id
)
