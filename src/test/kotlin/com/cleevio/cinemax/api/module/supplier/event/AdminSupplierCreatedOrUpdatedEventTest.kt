package com.cleevio.cinemax.api.module.supplier.event

import com.cleevio.cinemax.api.common.util.MESSAGING_MAPPER
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertNotNull
import org.junit.jupiter.api.Test
import java.util.Optional

class AdminSupplierCreatedOrUpdatedEventTest {

    @Test
    fun `test toMessagePayload - all event fields with values - should create message payload with correct type and serialization`() {
        val event = AdminSupplierCreatedOrUpdatedEvent(
            code = "1234",
            title = "Supplier Co.",
            addressStreet = Optional.of("123 Main St"),
            addressCity = Optional.of("Cityville"),
            addressPostCode = Optional.of("12345"),
            contactName = Optional.of("John Doe"),
            contactPhone = Optional.of("*********0"),
            contactEmails = Optional.of(setOf("<EMAIL>", "<EMAIL>")),
            bankName = Optional.of("Bank A"),
            bankAccount = Optional.of("*********"),
            idNumber = Optional.of("ID12345"),
            taxIdNumber = Optional.of("TAX12345")
        )
        val expectedJson = """
            {
                "code": "1234",
                "title": "Supplier Co.",
                "addressStreet": "123 Main St",
                "addressCity": "Cityville",
                "addressPostCode": "12345",
                "contactName": "John Doe",
                "contactPhone": "*********0",
                "contactEmails": ["<EMAIL>", "<EMAIL>"],
                "bankName": "Bank A",
                "bankAccount": "*********",
                "idNumber": "ID12345",
                "taxIdNumber": "TAX12345"
            }
        """.trimIndent()

        val messagePayload = event.toMessagePayload()

        assertNotNull(messagePayload.id)
        assertNotNull(messagePayload.createdAt)
        assertEquals("SUPPLIER_CREATED_OR_UPDATED", messagePayload.type.name)
        assertEquals(
            MESSAGING_MAPPER.readTree(expectedJson),
            MESSAGING_MAPPER.readTree(messagePayload.data)
        )
    }

    @Test
    fun `test toMessagePayload - required event fields present and all optional empty - should create message payload with correct type and serialization`() {
        val event = AdminSupplierCreatedOrUpdatedEvent(
            code = "1234",
            title = "Another Supplier",
            addressStreet = Optional.empty(),
            addressCity = Optional.empty(),
            addressPostCode = Optional.empty(),
            contactName = Optional.empty(),
            contactPhone = Optional.empty(),
            contactEmails = Optional.empty(),
            bankName = Optional.empty(),
            bankAccount = Optional.empty(),
            idNumber = Optional.empty(),
            taxIdNumber = Optional.empty()
        )
        val expectedJson = """
            {
                "code": "1234",
                "title": "Another Supplier",
                "addressStreet": null,
                "addressCity": null,
                "addressPostCode": null,
                "contactName": null,
                "contactPhone": null,
                "contactEmails": null,
                "bankName": null,
                "bankAccount": null,
                "idNumber": null,
                "taxIdNumber": null
            }
        """.trimIndent()

        val messagePayload = event.toMessagePayload()

        assertNotNull(messagePayload.id)
        assertNotNull(messagePayload.createdAt)
        assertEquals("SUPPLIER_CREATED_OR_UPDATED", messagePayload.type.name)
        assertEquals(
            MESSAGING_MAPPER.readTree(expectedJson),
            MESSAGING_MAPPER.readTree(messagePayload.data)
        )
    }

    @Test
    fun `test toMessagePayload - required event fields all other are null - should create message payload with correct type and serialization`() {
        val event = AdminSupplierCreatedOrUpdatedEvent(
            code = "1234",
            title = "Another Supplier"
        )
        val expectedJson = """
            {
                "code": "1234",
                "title": "Another Supplier"
            }
        """.trimIndent()

        val messagePayload = event.toMessagePayload()

        assertNotNull(messagePayload.id)
        assertNotNull(messagePayload.createdAt)
        assertEquals("SUPPLIER_CREATED_OR_UPDATED", messagePayload.type.name)
        assertEquals(
            MESSAGING_MAPPER.readTree(expectedJson),
            MESSAGING_MAPPER.readTree(messagePayload.data)
        )
    }
}
