package com.cleevio.cinemax.api.module.screening.event.listener

import com.cleevio.cinemax.api.module.outboxevent.constant.OutboxEventType
import com.cleevio.cinemax.api.module.outboxevent.constant.OutboxEventType.PUBLISHED_SCREENING_CREATED_OR_UPDATED
import com.cleevio.cinemax.api.module.screening.constant.ScreeningState
import com.cleevio.cinemax.api.module.screening.event.ScreeningSyncedEvent
import com.cleevio.cinemax.api.module.screening.exception.ScreeningNotFoundException
import com.cleevio.cinemax.api.module.screening.service.ScreeningJpaFinderService
import com.cleevio.cinemax.api.module.screening.service.ScreeningWebPublishingService
import com.cleevio.cinemax.api.module.screening.service.command.PublishScreeningsOnlineCommand
import com.cleevio.cinemax.api.module.screening.service.command.UnpublishScreeningsOnlineCommand
import com.cleevio.cinemax.api.util.createScreening
import com.cleevio.cinemax.api.util.toUUID
import io.mockk.Runs
import io.mockk.confirmVerified
import io.mockk.every
import io.mockk.just
import io.mockk.mockk
import io.mockk.verify
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.CsvSource

class ScreeningScreeningWebPublishingListenerTest {

    private val screeningWebPublishingService = mockk<ScreeningWebPublishingService>()
    private val screeningJpaFinderService = mockk<ScreeningJpaFinderService>()

    private val underTest = ScreeningScreeningWebPublishingListener(
        screeningWebPublishingService,
        screeningJpaFinderService
    )

    @AfterEach
    fun afterEach() = confirmVerified(
        screeningWebPublishingService
    )

    @Test
    fun `test listenToScreeningSyncedEvent - should throw ScreeningNotFoundException if screening not found`() {
        val screeningId = 1.toUUID()
        every { screeningJpaFinderService.getNonDeletedById(screeningId) } throws ScreeningNotFoundException()

        assertThrows<ScreeningNotFoundException> {
            underTest.listenToScreeningSyncedEvent(ScreeningSyncedEvent(screeningId, PUBLISHED_SCREENING_CREATED_OR_UPDATED))
        }
    }

    @ParameterizedTest
    @CsvSource(
        value = [
            "PUBLISHED_SCREENING_CREATED_OR_UPDATED, PUBLISHED, true, publish",
            "PUBLISHED_SCREENING_CREATED_OR_UPDATED, PUBLISHED, false, unpublish",
            "PUBLISHED_SCREENING_CREATED_OR_UPDATED, DRAFT, true, unpublish",
            "PUBLISHED_SCREENING_CREATED_OR_UPDATED, DRAFT, false, unpublish",
            "SCREENING_SWITCHED_TO_DRAFT_OR_DELETED, DRAFT, true, unpublish",
            "SCREENING_SWITCHED_TO_DRAFT_OR_DELETED, DRAFT, false, unpublish"
        ]
    )
    fun `test listenToScreeningSyncedEvent - should handle all event types correctly`(
        eventType: OutboxEventType,
        screeningState: ScreeningState,
        publishOnline: Boolean,
        expected: String,
    ) {
        val screening = createScreening(
            id = 1.toUUID(),
            publishOnline = publishOnline,
            state = screeningState,
            auditoriumId = 2.toUUID(),
            movieId = 3.toUUID()
        )

        every { screeningJpaFinderService.getNonDeletedById(any()) } returns screening
        every { screeningWebPublishingService.publishScreeningsOnline(any()) } just Runs
        every { screeningWebPublishingService.unpublishScreeningsOnline(any()) } just Runs

        underTest.listenToScreeningSyncedEvent(ScreeningSyncedEvent(screening.id, eventType))

        if (expected == "publish") {
            verify(exactly = 1) {
                screeningWebPublishingService.publishScreeningsOnline(
                    PublishScreeningsOnlineCommand(setOf(screening.id))
                )
            }
        }
        if (expected == "unpublish") {
            verify(exactly = 1) {
                screeningWebPublishingService.unpublishScreeningsOnline(
                    UnpublishScreeningsOnlineCommand(setOf(screening.id))
                )
            }
        }
    }
}
