package com.cleevio.cinemax.api.module.basketitem.service

import com.cleevio.cinemax.api.IntegrationTest
import com.cleevio.cinemax.api.common.constant.ExportableBoolean
import com.cleevio.cinemax.api.common.constant.PaymentType
import com.cleevio.cinemax.api.common.util.isEqualTo
import com.cleevio.cinemax.api.common.util.toCents
import com.cleevio.cinemax.api.common.util.toDateString
import com.cleevio.cinemax.api.common.util.toTimeString
import com.cleevio.cinemax.api.module.basketitem.model.ProductBasketItemExportRecordModel
import com.cleevio.cinemax.api.module.basketitem.model.ProductBasketItemExportSummaryRecordModel
import com.cleevio.cinemax.api.module.discountcard.constant.DiscountCardType
import com.cleevio.cinemax.api.module.product.constant.ProductType
import org.apache.poi.xssf.usermodel.XSSFWorkbook
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import java.io.ByteArrayInputStream
import java.time.LocalDate
import java.time.LocalTime
import kotlin.test.assertContains

class ProductBasketItemXlsxExportResultMapperIT @Autowired constructor(
    private val underTest: ProductBasketItemXlsxExportResultMapper,
) : IntegrationTest() {

    @Test
    fun `test mapToExportResultModel - should map all details correctly and generate Excel file`() {
        val exportResult = underTest.mapToExportResultModel(
            data = EXPORT_LIST_DATA to EXPORT_SUMMARY_DATA,
            username = USERNAME,
            basketPaidAtDateFrom = FIXED_DATE.minusDays(30),
            basketPaidAtDateTo = FIXED_DATE.plusDays(30)
        )

        val byteArray = exportResult.inputStream.readAllBytes()
        assert(byteArray.isNotEmpty())

        // open the generated Excel file from the byte array
        val workbook = XSSFWorkbook(ByteArrayInputStream(byteArray))
        val sheet = workbook.getSheet("Výsledky bufetu")

        // ---- Verify main header ----
        val mainHeaderRow = sheet.getRow(0)
        val mainHeaderRow1 = mainHeaderRow.getCell(0).stringCellValue
        val mainHeaderRow2 = mainHeaderRow.getCell(2).stringCellValue
        val mainHeaderRow3 = mainHeaderRow.getCell(mainHeaderRow.lastCellNum - 1).stringCellValue

        val fromDate = FIXED_DATE.minusDays(30).toDateString()
        val toDate = FIXED_DATE.plusDays(30).toDateString()
        assertEquals("Kino: Bratislava Bory\nPoužívateľ: $USERNAME", mainHeaderRow1)
        assertEquals("Výsledky bufetu\nPredaj od: $fromDate do: $toDate ", mainHeaderRow2)
        assertContains(mainHeaderRow3, "Dátum: ${LocalDate.now().toDateString()}\nČas:")

        // ---- Verify column headers ----
        val columnHeaders = sheet.getRow(4)

        assertEquals("Dátum predaja", columnHeaders.getCell(0).stringCellValue)
        assertEquals("Čas predaja", columnHeaders.getCell(1).stringCellValue)
        assertEquals("Číslo dokladu", columnHeaders.getCell(2).stringCellValue)
        assertEquals("Pokladňa", columnHeaders.getCell(3).stringCellValue)
        assertEquals("Predal", columnHeaders.getCell(4).stringCellValue)
        assertEquals("Názov produktu", columnHeaders.getCell(5).stringCellValue)
        assertEquals("Číslo produktu", columnHeaders.getCell(6).stringCellValue)
        assertEquals("Kategória produktu", columnHeaders.getCell(7).stringCellValue)
        assertEquals("Typ produktu", columnHeaders.getCell(8).stringCellValue)
        assertEquals("Množstvo", columnHeaders.getCell(9).stringCellValue)
        assertEquals("Nákupná cena", columnHeaders.getCell(10).stringCellValue)
        assertEquals("Predajná cena", columnHeaders.getCell(11).stringCellValue)
        assertEquals("DPH %", columnHeaders.getCell(12).stringCellValue)
        assertEquals("Platba", columnHeaders.getCell(13).stringCellValue)
        assertEquals("Storno", columnHeaders.getCell(14).stringCellValue)
        assertEquals("Zľava", columnHeaders.getCell(15).stringCellValue)
        assertEquals("Typ zľ. karty", columnHeaders.getCell(16).stringCellValue)
        assertEquals("Číslo zľ. karty/voucheru", columnHeaders.getCell(17).stringCellValue)

        // ---- Verify data rows ----
        val dataRow = sheet.getRow(5)
        val data = EXPORT_LIST_DATA.first()
        assertEquals(data.paidAtDate.toDateString(), dataRow.getCell(0).stringCellValue)
        assertEquals(data.paidAtTime.toTimeString(), dataRow.getCell(1).stringCellValue)
        assertEquals(data.productReceiptNumber, dataRow.getCell(2).stringCellValue)
        assertEquals(data.posTitle, dataRow.getCell(3).stringCellValue)
        assertEquals(data.basketUpdatedBy, dataRow.getCell(4).stringCellValue)
        assertEquals(data.productTitle, dataRow.getCell(5).stringCellValue)
        assertEquals(data.productCode, dataRow.getCell(6).stringCellValue)
        assertEquals(data.productCategoryTitle, dataRow.getCell(7).stringCellValue)
        assertEquals("produkt", dataRow.getCell(8).stringCellValue)
        assertEquals(data.quantity.toString(), dataRow.getCell(9).stringCellValue)
        assertTrue(data.purchasePrice.toCents() isEqualTo dataRow.getCell(10).numericCellValue.toBigDecimal())
        assertTrue(data.sellingPrice.toCents() isEqualTo dataRow.getCell(11).numericCellValue.toBigDecimal())
        assertEquals(data.vatPercentage.toString(), dataRow.getCell(12).stringCellValue)
        assertEquals("hotovosť", dataRow.getCell(13).stringCellValue)
        assertEquals("nie", dataRow.getCell(14).stringCellValue) // isCancelled
        assertEquals("áno", dataRow.getCell(15).stringCellValue) // isDiscounted
        assertEquals("karta", dataRow.getCell(16).stringCellValue)
        assertEquals("*********", dataRow.getCell(17).stringCellValue)
    }

    @Test
    fun `test mapToExportResultModel - should map summary sheet correctly`() {
        val exportResult = underTest.mapToExportResultModel(
            data = EXPORT_LIST_DATA to EXPORT_SUMMARY_DATA,
            username = USERNAME,
            basketPaidAtDateFrom = FIXED_DATE.minusDays(30),
            basketPaidAtDateTo = FIXED_DATE.plusDays(30)
        )
        val byteArray = exportResult.inputStream.readAllBytes()
        assert(byteArray.isNotEmpty())

        // open the generated Excel file from the byte array
        val workbook = XSSFWorkbook(ByteArrayInputStream(byteArray))
        val summarySheet = workbook.getSheet("Zhrnutie")

        // verify main header in summary sheet
        val mainHeaderRow1 = summarySheet.getRow(0).getCell(0).stringCellValue
        val mainHeaderRow2 = summarySheet.getRow(0).getCell(2).stringCellValue
        val mainHeaderRow3 = summarySheet.getRow(0)
            .getCell(summarySheet.getRow(0).lastCellNum - 1).stringCellValue

        val fromDate = FIXED_DATE.minusDays(30).toDateString()
        val toDate = FIXED_DATE.plusDays(30).toDateString()
        assertEquals("Kino: Bratislava Bory\nPoužívateľ: $USERNAME", mainHeaderRow1)
        assertEquals("Výsledky bufetu\nPredaj od: $fromDate do: $toDate ", mainHeaderRow2)
        assertContains(mainHeaderRow3, "Dátum: ${LocalDate.now().toDateString()}\nČas:")

        // verify sales summary section
        assertEquals("Zhrnutie podľa vybraných predajov", summarySheet.getRow(4).getCell(0).stringCellValue)
        assertEquals("Počet predaných produktov", summarySheet.getRow(5).getCell(0).stringCellValue)
        assertEquals("125", summarySheet.getRow(5).getCell(7).stringCellValue)
        assertEquals("Počet predajov", summarySheet.getRow(6).getCell(0).stringCellValue)
        assertEquals("45", summarySheet.getRow(6).getCell(7).stringCellValue)
        assertEquals("Celková nákupná cena", summarySheet.getRow(7).getCell(0).stringCellValue)
        assertEquals("€ 1250,50", summarySheet.getRow(7).getCell(7).stringCellValue)
        assertEquals("Suma hotovosť", summarySheet.getRow(8).getCell(0).stringCellValue)
        assertEquals("€ 2500,50", summarySheet.getRow(8).getCell(7).stringCellValue)
        assertEquals("Suma bezhotovosť", summarySheet.getRow(9).getCell(0).stringCellValue)
        assertEquals("€ 3750,75", summarySheet.getRow(9).getCell(7).stringCellValue)
        assertEquals("Tržba hrubá", summarySheet.getRow(10).getCell(0).stringCellValue)
        assertEquals("€ 6251,25", summarySheet.getRow(10).getCell(7).stringCellValue)
        assertEquals("Tržba čistá", summarySheet.getRow(11).getCell(0).stringCellValue)
        assertEquals("€ 5250,00", summarySheet.getRow(11).getCell(7).stringCellValue)
        assertEquals("Suma DPH", summarySheet.getRow(12).getCell(0).stringCellValue)
        assertEquals("€ 1001,25", summarySheet.getRow(12).getCell(7).stringCellValue)
        assertEquals("Hrubý zisk (bez DPH)", summarySheet.getRow(13).getCell(0).stringCellValue)
        assertEquals("€ 5000,75", summarySheet.getRow(13).getCell(7).stringCellValue)
        assertEquals("Čistý zisk (bez DPH)", summarySheet.getRow(14).getCell(0).stringCellValue)
        assertEquals("€ 3999,50", summarySheet.getRow(14).getCell(7).stringCellValue)
        assertEquals("Počet storno", summarySheet.getRow(15).getCell(0).stringCellValue)
        assertEquals("15", summarySheet.getRow(15).getCell(7).stringCellValue)
        assertEquals("Suma storno", summarySheet.getRow(16).getCell(0).stringCellValue)
        assertEquals("€ 225,50", summarySheet.getRow(16).getCell(7).stringCellValue)
    }
}

private const val USERNAME = "username"
private val FIXED_DATE = LocalDate.of(2025, 6, 26)

private val EXPORT_LIST_DATA = listOf(
    ProductBasketItemExportRecordModel(
        paidAtDate = LocalDate.of(2025, 6, 1),
        paidAtTime = LocalTime.of(10, 15),
        productReceiptNumber = "111-222-333",
        posTitle = "Pokladňa 1",
        basketUpdatedBy = "monika",
        productTitle = "Popcorn veľký",
        productCode = "POPCORN-XXL",
        productCategoryTitle = "Občerstvenie",
        productType = ProductType.PRODUCT,
        quantity = 2,
        purchasePrice = 3.00.toBigDecimal(),
        sellingPrice = "3.60".toBigDecimal(),
        vatPercentage = 20,
        paymentType = PaymentType.CASH,
        isCancelled = ExportableBoolean.FALSE,
        isDiscounted = ExportableBoolean.TRUE,
        discountCardType = DiscountCardType.CARD,
        discountCardCode = "*********"
    )
)

private val EXPORT_SUMMARY_DATA =
    ProductBasketItemExportSummaryRecordModel(
        productsCount = 125,
        basketsCount = 45,
        purchasePriceSum = 1250.50.toBigDecimal(),
        salesCash = 2500.50.toBigDecimal(),
        salesCashless = 3750.75.toBigDecimal(),
        grossSales = 6251.25.toBigDecimal(),
        netSales = 5250.00.toBigDecimal(),
        taxAmount = 1001.25.toBigDecimal(),
        grossRevenue = 5000.75.toBigDecimal(),
        netRevenue = 3999.50.toBigDecimal(),
        cancelledProductsCount = 15,
        cancelledProductsExpense = 225.50.toBigDecimal()
    )
