package com.cleevio.cinemax.api.module.receipt.controller

import com.cleevio.cinemax.api.ControllerTest
import com.cleevio.cinemax.api.common.constant.ApiVersion
import com.cleevio.cinemax.api.common.util.jsonContent
import com.cleevio.cinemax.api.module.employee.constant.EmployeeRole
import com.cleevio.cinemax.api.module.receipt.constant.ReceiptPrintState
import com.cleevio.cinemax.api.module.receipt.service.command.PrintReceiptCommand
import com.cleevio.cinemax.api.util.mockAccessToken
import io.mockk.every
import io.mockk.verify
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest
import org.springframework.http.HttpHeaders
import org.springframework.test.web.servlet.MockMvc
import org.springframework.test.web.servlet.post
import java.util.UUID

@WebMvcTest(ReceiptController::class)
class ReceiptControllerTest @Autowired constructor(
    private val mvc: MockMvc,
) : ControllerTest() {

    @Test
    fun `test printReceipt, should serialize and deserialize correctly`() {
        val basketId = UUID.fromString("de1bc366-2c93-416c-8bc0-2beb34ec5e26")

        every { svkXmlReceiptService.printReceipt(any()) } returns ReceiptPrintState.SUCCESS

        mvc.post(PRINT_RECEIPT_PATH(basketId)) {
            header(HttpHeaders.ACCEPT, ApiVersion.VERSION_1_JSON)
            header(HttpHeaders.AUTHORIZATION, mockAccessToken(role = EmployeeRole.MANAGER))
        }.andExpect {
            status { isOk() }
            content { contentType(ApiVersion.VERSION_1_JSON) }
            jsonContent(
                """
                {
                  "state": "${ReceiptPrintState.SUCCESS}"
                }
                """
            )
        }

        verify {
            svkXmlReceiptService.printReceipt(
                PrintReceiptCommand(
                    basketId = basketId
                )
            )
        }
    }
}

private val PRINT_RECEIPT_PATH: (UUID) -> String = { basketId -> "/pos-app/baskets/$basketId/print-receipt" }
