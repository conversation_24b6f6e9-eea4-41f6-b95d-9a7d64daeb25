package com.cleevio.cinemax.api.module.basketitem.service

import com.cleevio.cinemax.api.DatabaseCleanup
import com.cleevio.cinemax.api.IntegrationTest
import com.cleevio.cinemax.api.common.constant.PaymentType
import com.cleevio.cinemax.api.common.util.isEqualTo
import com.cleevio.cinemax.api.module.auditorium.service.AuditoriumRepository
import com.cleevio.cinemax.api.module.auditoriumlayout.service.AuditoriumLayoutRepository
import com.cleevio.cinemax.api.module.basket.constant.BasketState
import com.cleevio.cinemax.api.module.basket.service.BasketRepository
import com.cleevio.cinemax.api.module.basketitem.constant.BasketItemType
import com.cleevio.cinemax.api.module.distributor.service.DistributorRepository
import com.cleevio.cinemax.api.module.movie.service.MovieRepository
import com.cleevio.cinemax.api.module.posconfiguration.service.PosConfigurationRepository
import com.cleevio.cinemax.api.module.pricecategory.service.PriceCategoryRepository
import com.cleevio.cinemax.api.module.reservation.service.ReservationRepository
import com.cleevio.cinemax.api.module.screening.service.ScreeningRepository
import com.cleevio.cinemax.api.module.seat.service.SeatRepository
import com.cleevio.cinemax.api.module.ticket.service.TicketRepository
import com.cleevio.cinemax.api.module.ticketprice.service.TicketPriceRepository
import com.cleevio.cinemax.api.util.copyBasketItem
import com.cleevio.cinemax.api.util.createAuditorium
import com.cleevio.cinemax.api.util.createAuditoriumLayout
import com.cleevio.cinemax.api.util.createBasket
import com.cleevio.cinemax.api.util.createBasketItem
import com.cleevio.cinemax.api.util.createDistributor
import com.cleevio.cinemax.api.util.createMovie
import com.cleevio.cinemax.api.util.createPosConfiguration
import com.cleevio.cinemax.api.util.createPriceCategory
import com.cleevio.cinemax.api.util.createReservation
import com.cleevio.cinemax.api.util.createScreening
import com.cleevio.cinemax.api.util.createSeat
import com.cleevio.cinemax.api.util.createTicket
import com.cleevio.cinemax.api.util.createTicketPrice
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import kotlin.test.assertEquals

class BasketItemJpaFinderServiceIT @Autowired constructor(
    private val underTest: BasketItemJpaFinderService,
    private val basketRepository: BasketRepository,
    private val basketItemRepository: BasketItemRepository,
    private val posConfigurationRepository: PosConfigurationRepository,
    private val screeningRepository: ScreeningRepository,
    private val ticketRepository: TicketRepository,
    private val ticketPriceRepository: TicketPriceRepository,
    private val reservationRepository: ReservationRepository,
    private val movieRepository: MovieRepository,
    private val auditoriumRepository: AuditoriumRepository,
    private val distributorRepository: DistributorRepository,
    private val priceCategoryRepository: PriceCategoryRepository,
    private val auditoriumLayoutRepository: AuditoriumLayoutRepository,
    private val seatRepository: SeatRepository,
) : IntegrationTest(DatabaseCleanup.AFTER_EACH) {

    @BeforeEach
    fun beforeEach() {
        auditoriumRepository.save(AUDITORIUM)
        auditoriumLayoutRepository.save(AUDITORIUM_LAYOUT)
        seatRepository.saveAll(listOf(SEAT_1, SEAT_2, SEAT_3))
        distributorRepository.save(DISTRIBUTOR)
        priceCategoryRepository.save(PRICE_CATEGORY)
        movieRepository.save(MOVIE)
        screeningRepository.save(SCREENING)
        reservationRepository.saveAll(listOf(RESERVATION_1, RESERVATION_2, RESERVATION_3))
        ticketPriceRepository.saveAll(listOf(TICKET_PRICE_1, TICKET_PRICE_2, TICKET_PRICE_3))
        ticketRepository.saveAll(listOf(TICKET_1, TICKET_2, TICKET_3))
        posConfigurationRepository.save(POS_CONFIGURATION)
        basketRepository.saveAll(listOf(BASKET_1, BASKET_2))
        basketItemRepository.saveAll(
            listOf(
                BASKET_ITEM_TICKET_CASH,
                BASKET_ITEM_TICKET_CASHLESS,
                BASKET_ITEM_TICKET_CANCELLED_CASH,
                BASKET_ITEM_TICKET_CANCELLED_CASHLESS,
                BASKET_ITEM_TICKET_ZERO_FEES,
                BASKET_ITEM_PRODUCT_CASH,
                BASKET_ITEM_PRODUCT_CASHLESS,
                BASKET_ITEM_PRODUCT_CANCELLED_CASH,
                BASKET_ITEM_PRODUCT_CANCELLED_CASHLESS,
                BASKET_ITEM_PRODUCT_DISCOUNT_CASH,
                BASKET_ITEM_PRODUCT_DISCOUNT_CASHLESS,
                BASKET_ITEM_PRODUCT_DISCOUNT_CANCELLED_CASH,
                BASKET_ITEM_PRODUCT_DISCOUNT_CANCELLED_CASHLESS,
                BASKET_ITEM_PRODUCT_CASH_DELETED
            )
        )
    }

    @Test
    fun `test getDailyClosingMovementData - should return correct movement data`() {
        val result = underTest.getDailyClosingMovementData(
            setOf(
                BASKET_ITEM_TICKET_CASH.id,
                BASKET_ITEM_TICKET_CASHLESS.id,
                BASKET_ITEM_TICKET_CANCELLED_CASH.id,
                BASKET_ITEM_TICKET_CANCELLED_CASHLESS.id,
                BASKET_ITEM_TICKET_ZERO_FEES.id,
                BASKET_ITEM_PRODUCT_CASH.id,
                BASKET_ITEM_PRODUCT_CASHLESS.id,
                BASKET_ITEM_PRODUCT_CANCELLED_CASH.id,
                BASKET_ITEM_PRODUCT_CANCELLED_CASHLESS.id,
                BASKET_ITEM_PRODUCT_DISCOUNT_CASH.id,
                BASKET_ITEM_PRODUCT_DISCOUNT_CASHLESS.id,
                BASKET_ITEM_PRODUCT_DISCOUNT_CANCELLED_CASH.id,
                BASKET_ITEM_PRODUCT_DISCOUNT_CANCELLED_CASHLESS.id,
                BASKET_ITEM_PRODUCT_CASH_DELETED.id
            )
        )

        assertEquals(13, result.size)

        // BASKET_ITEM_TICKET_CASH assertions
        result.first {
            BasketItemType.TICKET == it.basketItemType &&
                PaymentType.CASH == it.basketPaymentType && !it.isCancelled
        }.let {
            assertEquals(12.toBigDecimal(), it.basketItemPrice)
            assertEquals(12.toBigDecimal(), it.ticketTotalPrice)
            assertEquals(1.toBigDecimal(), it.ticketSeatServiceFee)
            assertEquals(3.toBigDecimal(), it.ticketAuditoriumServiceFee)
            assertEquals(5.toBigDecimal(), it.ticketServiceFeeGeneral)
        }

        // BASKET_ITEM_TICKET_CASHLESS assertions
        result.first {
            BasketItemType.TICKET == it.basketItemType &&
                PaymentType.CASHLESS == it.basketPaymentType && !it.isCancelled
        }.let {
            assertEquals(6.toBigDecimal(), it.basketItemPrice)
            assertEquals(6.toBigDecimal(), it.ticketTotalPrice)
            assertEquals(0.5.toBigDecimal(), it.ticketSeatServiceFee)
            assertEquals(1.5.toBigDecimal(), it.ticketAuditoriumServiceFee)
            assertEquals(2.5.toBigDecimal(), it.ticketServiceFeeGeneral)
        }

        // BASKET_ITEM_TICKET_CANCELLED_CASH assertions
        result.first {
            BasketItemType.TICKET == it.basketItemType &&
                PaymentType.CASH == it.basketPaymentType && it.isCancelled
        }.let {
            assertEquals(8.toBigDecimal(), it.basketItemPrice)
            assertEquals(12.toBigDecimal(), it.ticketTotalPrice) // Same ticket, so price stays
            assertEquals(1.toBigDecimal(), it.ticketSeatServiceFee)
            assertEquals(3.toBigDecimal(), it.ticketAuditoriumServiceFee)
            assertEquals(5.toBigDecimal(), it.ticketServiceFeeGeneral)
        }

        // BASKET_ITEM_TICKET_CANCELLED_CASHLESS assertions
        result.first {
            BasketItemType.TICKET == it.basketItemType &&
                PaymentType.CASHLESS == it.basketPaymentType && it.isCancelled
        }.let {
            assertEquals(4.toBigDecimal(), it.basketItemPrice)
            assertEquals(6.toBigDecimal(), it.ticketTotalPrice) // Same ticket, so price stays
            assertEquals(0.5.toBigDecimal(), it.ticketSeatServiceFee)
            assertEquals(1.5.toBigDecimal(), it.ticketAuditoriumServiceFee)
            assertEquals(2.5.toBigDecimal(), it.ticketServiceFeeGeneral)
        }

        // BASKET_ITEM_TICKET_ZERO_FEES assertions
        result.first {
            BasketItemType.TICKET == it.basketItemType &&
                PaymentType.CASH == it.basketPaymentType && it.ticketTotalPrice isEqualTo 0.toBigDecimal()
        }.let {
            assertEquals(0.toBigDecimal(), it.basketItemPrice)
            assertEquals(0.toBigDecimal(), it.ticketTotalPrice)
            assertEquals(0.toBigDecimal(), it.ticketSeatServiceFee)
            assertEquals(0.toBigDecimal(), it.ticketAuditoriumServiceFee)
            assertEquals(0.toBigDecimal(), it.ticketServiceFeeGeneral)
        }

        // BASKET_ITEM_PRODUCT_CASH assertions
        result.first {
            BasketItemType.PRODUCT == it.basketItemType &&
                PaymentType.CASH == it.basketPaymentType && !it.isCancelled
        }.let {
            assertEquals(23.toBigDecimal(), it.basketItemPrice)
            assertEquals(0.toBigDecimal(), it.ticketTotalPrice)
            assertEquals(0.toBigDecimal(), it.ticketSeatServiceFee)
            assertEquals(0.toBigDecimal(), it.ticketAuditoriumServiceFee)
            assertEquals(0.toBigDecimal(), it.ticketServiceFeeGeneral)
        }

        // BASKET_ITEM_PRODUCT_CASHLESS assertions
        result.first {
            BasketItemType.PRODUCT == it.basketItemType &&
                PaymentType.CASHLESS == it.basketPaymentType && !it.isCancelled
        }.let {
            assertEquals(25.toBigDecimal(), it.basketItemPrice)
            assertEquals(0.toBigDecimal(), it.ticketTotalPrice)
            assertEquals(0.toBigDecimal(), it.ticketSeatServiceFee)
            assertEquals(0.toBigDecimal(), it.ticketAuditoriumServiceFee)
            assertEquals(0.toBigDecimal(), it.ticketServiceFeeGeneral)
        }

        // BASKET_ITEM_PRODUCT_CANCELLED_CASH assertions
        result.first {
            BasketItemType.PRODUCT == it.basketItemType &&
                PaymentType.CASH == it.basketPaymentType && it.isCancelled
        }.let {
            assertEquals(13.toBigDecimal(), it.basketItemPrice)
            assertEquals(0.toBigDecimal(), it.ticketTotalPrice)
            assertEquals(0.toBigDecimal(), it.ticketSeatServiceFee)
            assertEquals(0.toBigDecimal(), it.ticketAuditoriumServiceFee)
            assertEquals(0.toBigDecimal(), it.ticketServiceFeeGeneral)
        }

        // BASKET_ITEM_PRODUCT_CANCELLED_CASHLESS assertions
        result.first {
            BasketItemType.PRODUCT == it.basketItemType &&
                PaymentType.CASHLESS == it.basketPaymentType && it.isCancelled
        }.let {
            assertEquals(15.toBigDecimal(), it.basketItemPrice)
            assertEquals(0.toBigDecimal(), it.ticketTotalPrice)
            assertEquals(0.toBigDecimal(), it.ticketSeatServiceFee)
            assertEquals(0.toBigDecimal(), it.ticketAuditoriumServiceFee)
            assertEquals(0.toBigDecimal(), it.ticketServiceFeeGeneral)
        }

        // BASKET_ITEM_PRODUCT_DISCOUNT_CASH assertions
        result.first {
            BasketItemType.PRODUCT_DISCOUNT == it.basketItemType &&
                PaymentType.CASH == it.basketPaymentType && !it.isCancelled
        }.let {
            assertEquals((-5.5).toBigDecimal(), it.basketItemPrice)
            assertEquals(0.toBigDecimal(), it.ticketTotalPrice)
            assertEquals(0.toBigDecimal(), it.ticketSeatServiceFee)
            assertEquals(0.toBigDecimal(), it.ticketAuditoriumServiceFee)
            assertEquals(0.toBigDecimal(), it.ticketServiceFeeGeneral)
        }

        // BASKET_ITEM_PRODUCT_DISCOUNT_CASHLESS assertions
        result.first {
            BasketItemType.PRODUCT_DISCOUNT == it.basketItemType &&
                PaymentType.CASHLESS == it.basketPaymentType && !it.isCancelled
        }.let {
            assertEquals((-2.25).toBigDecimal(), it.basketItemPrice)
            assertEquals(0.toBigDecimal(), it.ticketTotalPrice)
            assertEquals(0.toBigDecimal(), it.ticketSeatServiceFee)
            assertEquals(0.toBigDecimal(), it.ticketAuditoriumServiceFee)
            assertEquals(0.toBigDecimal(), it.ticketServiceFeeGeneral)
        }

        // BASKET_ITEM_PRODUCT_DISCOUNT_CANCELLED_CASH assertions
        result.first {
            BasketItemType.PRODUCT_DISCOUNT == it.basketItemType &&
                PaymentType.CASH == it.basketPaymentType && it.isCancelled
        }.let {
            assertEquals((-2.5).toBigDecimal(), it.basketItemPrice)
            assertEquals(0.toBigDecimal(), it.ticketTotalPrice)
            assertEquals(0.toBigDecimal(), it.ticketSeatServiceFee)
            assertEquals(0.toBigDecimal(), it.ticketAuditoriumServiceFee)
            assertEquals(0.toBigDecimal(), it.ticketServiceFeeGeneral)
        }

        // BASKET_ITEM_PRODUCT_DISCOUNT_CANCELLED_CASHLESS assertions
        result.first {
            BasketItemType.PRODUCT_DISCOUNT == it.basketItemType &&
                PaymentType.CASHLESS == it.basketPaymentType && it.isCancelled
        }.let {
            assertEquals((-1.5).toBigDecimal(), it.basketItemPrice)
            assertEquals(0.toBigDecimal(), it.ticketTotalPrice)
            assertEquals(0.toBigDecimal(), it.ticketSeatServiceFee)
            assertEquals(0.toBigDecimal(), it.ticketAuditoriumServiceFee)
            assertEquals(0.toBigDecimal(), it.ticketServiceFeeGeneral)
        }
    }
}

private val DISTRIBUTOR = createDistributor()
private val AUDITORIUM = createAuditorium()
private val AUDITORIUM_LAYOUT = createAuditoriumLayout(auditoriumId = AUDITORIUM.id)
private val SEAT_1 = createSeat(originalId = 1, auditoriumLayoutId = AUDITORIUM_LAYOUT.id, auditoriumId = AUDITORIUM.id)
private val SEAT_2 = createSeat(originalId = 2, auditoriumLayoutId = AUDITORIUM_LAYOUT.id, auditoriumId = AUDITORIUM.id)
private val SEAT_3 = createSeat(originalId = 3, auditoriumLayoutId = AUDITORIUM_LAYOUT.id, auditoriumId = AUDITORIUM.id)
private val MOVIE = createMovie(distributorId = DISTRIBUTOR.id)
private val PRICE_CATEGORY = createPriceCategory()
private val SCREENING = createScreening(
    auditoriumId = AUDITORIUM.id,
    movieId = MOVIE.id,
    priceCategoryId = PRICE_CATEGORY.id,
    auditoriumLayoutId = AUDITORIUM_LAYOUT.id
)
private val RESERVATION_1 = createReservation(
    screeningId = SCREENING.id,
    seatId = SEAT_1.id
)
private val TICKET_PRICE_1 = createTicketPrice(
    screeningId = SCREENING.id,
    seatId = SEAT_1.id,
    totalPrice = 12.toBigDecimal(),
    serviceFeeGeneral = 5.toBigDecimal(),
    auditoriumServiceFee = 3.toBigDecimal(),
    seatServiceFee = 1.toBigDecimal()
)
private val RESERVATION_2 = createReservation(
    screeningId = SCREENING.id,
    seatId = SEAT_2.id
)
private val TICKET_PRICE_2 = createTicketPrice(
    screeningId = SCREENING.id,
    seatId = SEAT_2.id,
    totalPrice = 6.toBigDecimal(),
    serviceFeeGeneral = 2.5.toBigDecimal(),
    auditoriumServiceFee = 1.5.toBigDecimal(),
    seatServiceFee = 0.5.toBigDecimal()
)
private val RESERVATION_3 = createReservation(
    screeningId = SCREENING.id,
    seatId = SEAT_3.id
)
private val TICKET_PRICE_3 = createTicketPrice(
    screeningId = SCREENING.id,
    seatId = SEAT_3.id,
    totalPrice = 0.toBigDecimal(),
    serviceFeeGeneral = 0.toBigDecimal(),
    auditoriumServiceFee = 1.5.toBigDecimal(),
    seatServiceFee = 2.5.toBigDecimal(),
    zeroFees = true
)
private val TICKET_1 = createTicket(
    screeningId = SCREENING.id,
    reservationId = RESERVATION_1.id,
    ticketPriceId = TICKET_PRICE_1.id
)
private val TICKET_2 = createTicket(
    screeningId = SCREENING.id,
    reservationId = RESERVATION_2.id,
    ticketPriceId = TICKET_PRICE_2.id
)
private val TICKET_3 = createTicket(
    screeningId = SCREENING.id,
    reservationId = RESERVATION_3.id,
    ticketPriceId = TICKET_PRICE_3.id
)

// POS CONFIGURATIONS
private val POS_CONFIGURATION = createPosConfiguration(macAddress = "AA:BB:CC:DD:EE")

// BASKETS
private val BASKET_1 = createBasket(
    state = BasketState.PAID,
    paymentType = PaymentType.CASH,
    paymentPosConfigurationId = POS_CONFIGURATION.id
)
private val BASKET_2 = createBasket(
    state = BasketState.PAID,
    paymentType = PaymentType.CASHLESS,
    paymentPosConfigurationId = POS_CONFIGURATION.id
)

// BASKET ITEMS
private val BASKET_ITEM_TICKET_CASH = createBasketItem(
    basketId = BASKET_1.id,
    type = BasketItemType.TICKET,
    price = 12.toBigDecimal(),
    ticketId = TICKET_1.id
)
private val BASKET_ITEM_TICKET_CASHLESS = copyBasketItem(BASKET_ITEM_TICKET_CASH) {
    it.basketId = BASKET_2.id
    it.price = 6.toBigDecimal()
    it.ticketId = TICKET_2.id
}
private val BASKET_ITEM_TICKET_CANCELLED_CASH = copyBasketItem(BASKET_ITEM_TICKET_CASH) {
    it.price = 8.toBigDecimal()
    it.isCancelled = true
    it.cancelledBasketItemId = null
}
private val BASKET_ITEM_TICKET_CANCELLED_CASHLESS = copyBasketItem(BASKET_ITEM_TICKET_CASHLESS) {
    it.price = 4.toBigDecimal()
    it.isCancelled = true
    it.cancelledBasketItemId = null
}
private val BASKET_ITEM_PRODUCT_CASH = createBasketItem(
    basketId = BASKET_1.id,
    type = BasketItemType.PRODUCT,
    price = 23.toBigDecimal()
)
private val BASKET_ITEM_PRODUCT_CASHLESS = createBasketItem(
    basketId = BASKET_2.id,
    type = BasketItemType.PRODUCT,
    price = 25.toBigDecimal()
)
private val BASKET_ITEM_TICKET_ZERO_FEES = createBasketItem(
    basketId = BASKET_1.id,
    type = BasketItemType.TICKET,
    price = 0.toBigDecimal(),
    ticketId = TICKET_3.id
)
private val BASKET_ITEM_PRODUCT_CANCELLED_CASH = copyBasketItem(BASKET_ITEM_PRODUCT_CASH) {
    it.price = 13.toBigDecimal()
    it.isCancelled = true
    it.cancelledBasketItemId = null
}
private val BASKET_ITEM_PRODUCT_CANCELLED_CASHLESS = copyBasketItem(BASKET_ITEM_PRODUCT_CASHLESS) {
    it.price = 15.toBigDecimal()
    it.isCancelled = true
    it.cancelledBasketItemId = null
}
private val BASKET_ITEM_PRODUCT_DISCOUNT_CASH = createBasketItem(
    basketId = BASKET_1.id,
    type = BasketItemType.PRODUCT_DISCOUNT,
    price = (-5.5).toBigDecimal()
)
private val BASKET_ITEM_PRODUCT_DISCOUNT_CASHLESS = createBasketItem(
    basketId = BASKET_2.id,
    type = BasketItemType.PRODUCT_DISCOUNT,
    price = (-2.25).toBigDecimal()
)
private val BASKET_ITEM_PRODUCT_DISCOUNT_CANCELLED_CASH =
    copyBasketItem(BASKET_ITEM_PRODUCT_DISCOUNT_CASH) {
        it.price = (-2.5).toBigDecimal()
        it.isCancelled = true
        it.cancelledBasketItemId = null
    }
private val BASKET_ITEM_PRODUCT_DISCOUNT_CANCELLED_CASHLESS =
    copyBasketItem(BASKET_ITEM_PRODUCT_DISCOUNT_CASHLESS) {
        it.price = (-1.5).toBigDecimal()
        it.isCancelled = true
        it.cancelledBasketItemId = null
    }
private val BASKET_ITEM_PRODUCT_CASH_DELETED = createBasketItem(
    basketId = BASKET_1.id,
    type = BasketItemType.PRODUCT,
    price = 100.toBigDecimal()
) { it.markDeleted() }
