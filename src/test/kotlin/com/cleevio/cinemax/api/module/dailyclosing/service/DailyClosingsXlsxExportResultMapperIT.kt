package com.cleevio.cinemax.api.module.dailyclosing.service

import com.cleevio.cinemax.api.DatabaseCleanup
import com.cleevio.cinemax.api.IntegrationTest
import com.cleevio.cinemax.api.common.constant.PaymentType
import com.cleevio.cinemax.api.common.util.toDateString
import com.cleevio.cinemax.api.common.util.toTimeString
import com.cleevio.cinemax.api.module.dailyclosing.model.DailyClosingsExportRecordModel
import com.cleevio.cinemax.api.module.dailyclosing.model.DailyClosingsOtherMovementsExportRecordModel
import com.cleevio.cinemax.api.module.dailyclosingmovement.constant.DailyClosingMovementItemType
import com.cleevio.cinemax.api.module.dailyclosingmovement.constant.DailyClosingMovementType
import org.apache.poi.ss.usermodel.IndexedColors
import org.apache.poi.xssf.usermodel.XSSFCellStyle
import org.apache.poi.xssf.usermodel.XSSFWorkbook
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import java.io.ByteArrayInputStream
import java.math.BigDecimal
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.LocalTime
import kotlin.test.assertContains

class DailyClosingsXlsxExportResultMapperIT @Autowired constructor(
    private val underTest: DailyClosingsXlsxExportResultMapper,
) : IntegrationTest(DatabaseCleanup.AFTER_EACH) {

    @Test
    fun `test mapToExportResultModel - two closings in two days - should map all details correctly and generate Excel`() {
        val fixedDate1 = LocalDateTime.of(2024, 6, 26, 14, 30, 55)
        val fixedDate2 = LocalDateTime.of(2024, 6, 27, 22, 15, 45)
        val closedAtFrom = fixedDate1.toLocalDate().minusDays(30)
        val closedAtTo = fixedDate2.toLocalDate().plusDays(30)
        val username = "testuser"
        val dailyClosingData = listOf(
            // Records for fixedDate1
            DailyClosingsExportRecordModel(
                posConfigurationTitle = "POS1",
                closedAtDate = fixedDate1.toLocalDate(),
                closedAtTime = fixedDate1.toLocalTime(),
                salesCash = BigDecimal("100.00"),
                salesCashless = BigDecimal("200.00"),
                salesTotal = BigDecimal("300.00"),
                serviceFeesCash = BigDecimal("10.00"),
                serviceFeesCashless = BigDecimal("20.00"),
                cancelledCash = BigDecimal("5.00"),
                cancelledCashless = BigDecimal("10.00"),
                otherMovementsRevenues = BigDecimal("0.00"),
                otherMovementsExpenses = BigDecimal("0.00"),
                fixedPriceTicketsAmount = BigDecimal("0.00"),
                deduction = BigDecimal("5.00"),
                netSales = BigDecimal("285.00")
            ),
            DailyClosingsExportRecordModel(
                posConfigurationTitle = "POS2",
                closedAtDate = fixedDate1.toLocalDate(),
                closedAtTime = fixedDate1.toLocalTime(),
                salesCash = BigDecimal("150.00"),
                salesCashless = BigDecimal("250.00"),
                salesTotal = BigDecimal("400.00"),
                serviceFeesCash = BigDecimal("15.00"),
                serviceFeesCashless = BigDecimal("25.00"),
                cancelledCash = BigDecimal("5.00"),
                cancelledCashless = BigDecimal("10.00"),
                otherMovementsRevenues = BigDecimal("0.00"),
                otherMovementsExpenses = BigDecimal("0.00"),
                fixedPriceTicketsAmount = BigDecimal("0.00"),
                deduction = BigDecimal("10.00"),
                netSales = BigDecimal("375.00")
            ),
            // Records for fixedDate2
            DailyClosingsExportRecordModel(
                posConfigurationTitle = "POS1",
                closedAtDate = fixedDate2.toLocalDate(),
                closedAtTime = fixedDate2.toLocalTime(),
                salesCash = BigDecimal("200.00"),
                salesCashless = BigDecimal("300.00"),
                salesTotal = BigDecimal("500.00"),
                serviceFeesCash = BigDecimal("20.00"),
                serviceFeesCashless = BigDecimal("30.00"),
                cancelledCash = BigDecimal("10.00"),
                cancelledCashless = BigDecimal("15.00"),
                otherMovementsRevenues = BigDecimal("0.00"),
                otherMovementsExpenses = BigDecimal("0.00"),
                fixedPriceTicketsAmount = BigDecimal("0.00"),
                deduction = BigDecimal("20.00"),
                netSales = BigDecimal("450.00")
            )
        )
        val otherMovementsData = listOf(
            DailyClosingsOtherMovementsExportRecordModel(
                posConfigurationTitle = "POS1",
                otherMovementTitle = "Prijem Bory",
                createdAtDate = LocalDate.of(2024, 10, 1),
                createdAtTime = LocalTime.of(10, 0),
                itemType = DailyClosingMovementItemType.PRODUCTS,
                type = DailyClosingMovementType.REVENUE,
                otherReceiptNumber = "RCPT001",
                variableSymbol = "VS101",
                paymentType = PaymentType.CASH,
                amount = "€ 100,00"
            ),
            DailyClosingsOtherMovementsExportRecordModel(
                posConfigurationTitle = "POS2",
                otherMovementTitle = "Vydaj odpoledne",
                createdAtDate = LocalDate.of(2024, 10, 2),
                createdAtTime = LocalTime.of(15, 0),
                itemType = DailyClosingMovementItemType.DEDUCTION,
                type = DailyClosingMovementType.EXPENSE,
                otherReceiptNumber = null,
                variableSymbol = null,
                paymentType = PaymentType.CASHLESS,
                amount = "€ 20,00"
            )
        )

        val exportResult = underTest.mapToExportResultModel(
            closedAtFrom = closedAtFrom,
            closedAtTo = closedAtTo,
            data = dailyClosingData to otherMovementsData,
            username = username
        )
        val byteArray = exportResult.inputStream.readAllBytes()
        assert(byteArray.isNotEmpty())

        // Open the generated Excel file from the byte array
        val workbook = XSSFWorkbook(ByteArrayInputStream(byteArray))
        val dailyClosingSheet = workbook.getSheet("Pokladničný výkaz")

        // Verify main header
        val closingsMainHeaderRow = dailyClosingSheet.getRow(0)
        val closingsMainHeaderRow1 = closingsMainHeaderRow.getCell(0).stringCellValue
        val closingsMainHeaderRow2 = closingsMainHeaderRow.getCell(2).stringCellValue
        val closingsMainHeaderRow3 = closingsMainHeaderRow.getCell(dailyClosingSheet.getRow(0).lastCellNum - 1).stringCellValue

        assertEquals(
            "Kino: Bratislava Bory\nPoužívateľ: testuser",
            closingsMainHeaderRow1
        )
        assertEquals(
            "Pokladničný výkaz\nOd: ${fixedDate1.toLocalDate().minusDays(30).toDateString()} Do: ${
                fixedDate2.toLocalDate().plusDays(30).toDateString()
            } ",
            closingsMainHeaderRow2
        )
        assertContains(closingsMainHeaderRow3, "Dátum: ${LocalDate.now().toDateString()}\nČas:")

        // Verify column headers
        val closingsColumnHeaders = dailyClosingSheet.getRow(4)
        assertEquals("Pokladňa", closingsColumnHeaders.getCell(0).stringCellValue)
        assertEquals("Dátum", closingsColumnHeaders.getCell(1).stringCellValue)
        assertEquals("Čas", closingsColumnHeaders.getCell(2).stringCellValue)
        assertEquals("Predaj hot.", closingsColumnHeaders.getCell(3).stringCellValue)
        assertEquals("Predaj bezhot.", closingsColumnHeaders.getCell(4).stringCellValue)
        assertEquals("Σ Predaj", closingsColumnHeaders.getCell(5).stringCellValue)
        assertEquals("Služ. hot.", closingsColumnHeaders.getCell(6).stringCellValue)
        assertEquals("Služ. nh.", closingsColumnHeaders.getCell(7).stringCellValue)
        assertEquals("Vrác. hot.", closingsColumnHeaders.getCell(8).stringCellValue)
        assertEquals("Vr. neh.", closingsColumnHeaders.getCell(9).stringCellValue)
        assertEquals("Ost. př.", closingsColumnHeaders.getCell(10).stringCellValue)
        assertEquals("Ost. v.", closingsColumnHeaders.getCell(11).stringCellValue)
        assertEquals("Pevná cena", closingsColumnHeaders.getCell(12).stringCellValue)
        assertEquals("Odvod", closingsColumnHeaders.getCell(13).stringCellValue)
        assertEquals("Čistá tržba", closingsColumnHeaders.getCell(14).stringCellValue)

        // Verify records and summary rows for fixedDate1
        val closingsDataRow1 = dailyClosingSheet.getRow(5)
        assertEquals("POS1", closingsDataRow1.getCell(0).stringCellValue)
        assertEquals(fixedDate1.toLocalDate().toDateString(), closingsDataRow1.getCell(1).stringCellValue)
        assertEquals(fixedDate1.toLocalTime().toTimeString(), closingsDataRow1.getCell(2).stringCellValue)
        assertEquals(100.00, closingsDataRow1.getCell(3).numericCellValue)
        assertEquals(200.00, closingsDataRow1.getCell(4).numericCellValue)
        assertEquals(300.00, closingsDataRow1.getCell(5).numericCellValue)
        assertEquals(10.00, closingsDataRow1.getCell(6).numericCellValue)
        assertEquals(20.00, closingsDataRow1.getCell(7).numericCellValue)
        assertEquals(5.00, closingsDataRow1.getCell(8).numericCellValue)
        assertEquals(10.00, closingsDataRow1.getCell(9).numericCellValue)
        assertEquals(0.00, closingsDataRow1.getCell(10).numericCellValue)
        assertEquals(0.00, closingsDataRow1.getCell(11).numericCellValue)
        assertEquals(0.00, closingsDataRow1.getCell(12).numericCellValue)
        assertEquals(5.00, closingsDataRow1.getCell(13).numericCellValue)
        assertEquals(285.00, closingsDataRow1.getCell(14).numericCellValue)

        val closingsDataRow2 = dailyClosingSheet.getRow(6)
        assertEquals("POS2", closingsDataRow2.getCell(0).stringCellValue)
        assertEquals(fixedDate1.toLocalDate().toDateString(), closingsDataRow2.getCell(1).stringCellValue)
        assertEquals(fixedDate1.toLocalTime().toTimeString(), closingsDataRow2.getCell(2).stringCellValue)
        assertEquals(150.00, closingsDataRow2.getCell(3).numericCellValue)
        assertEquals(250.00, closingsDataRow2.getCell(4).numericCellValue)
        assertEquals(400.00, closingsDataRow2.getCell(5).numericCellValue)
        assertEquals(15.00, closingsDataRow2.getCell(6).numericCellValue)
        assertEquals(25.00, closingsDataRow2.getCell(7).numericCellValue)
        assertEquals(5.00, closingsDataRow2.getCell(8).numericCellValue)
        assertEquals(10.00, closingsDataRow2.getCell(9).numericCellValue)
        assertEquals(0.00, closingsDataRow2.getCell(10).numericCellValue)
        assertEquals(0.00, closingsDataRow2.getCell(11).numericCellValue)
        assertEquals(0.00, closingsDataRow2.getCell(12).numericCellValue)
        assertEquals(10.00, closingsDataRow2.getCell(13).numericCellValue)
        assertEquals(375.00, closingsDataRow2.getCell(14).numericCellValue)

        // Verify total summary row for fixedDate1
        val totalRow1 = dailyClosingSheet.getRow(7)
        assertEquals("Celkovo", totalRow1.getCell(0).stringCellValue)
        assertEquals(fixedDate1.toLocalDate().toDateString(), totalRow1.getCell(1).stringCellValue)
        assertEquals("", totalRow1.getCell(2).stringCellValue)
        assertEquals(250.00, totalRow1.getCell(3).numericCellValue)
        assertEquals(450.00, totalRow1.getCell(4).numericCellValue)
        assertEquals(700.00, totalRow1.getCell(5).numericCellValue)
        assertEquals(25.00, totalRow1.getCell(6).numericCellValue)
        assertEquals(45.00, totalRow1.getCell(7).numericCellValue)
        assertEquals(10.00, totalRow1.getCell(8).numericCellValue)
        assertEquals(20.00, totalRow1.getCell(9).numericCellValue)
        assertEquals(0.00, totalRow1.getCell(10).numericCellValue)
        assertEquals(0.00, totalRow1.getCell(11).numericCellValue)
        assertEquals(0.00, totalRow1.getCell(12).numericCellValue)
        assertEquals(15.00, totalRow1.getCell(13).numericCellValue)
        assertEquals(660.00, totalRow1.getCell(14).numericCellValue)
        assertEquals(250.00, totalRow1.getCell(3).numericCellValue)
        assertEquals(450.00, totalRow1.getCell(4).numericCellValue)
        assertEquals(700.00, totalRow1.getCell(5).numericCellValue)
        assertEquals(25.00, totalRow1.getCell(6).numericCellValue)
        assertEquals(45.00, totalRow1.getCell(7).numericCellValue)
        assertEquals(10.00, totalRow1.getCell(8).numericCellValue)
        assertEquals(20.00, totalRow1.getCell(9).numericCellValue)
        assertEquals(0.00, totalRow1.getCell(10).numericCellValue)
        assertEquals(0.00, totalRow1.getCell(11).numericCellValue)
        assertEquals(0.00, totalRow1.getCell(12).numericCellValue)
        assertEquals(15.00, totalRow1.getCell(13).numericCellValue)
        assertEquals(660.00, totalRow1.getCell(14).numericCellValue)

        // Verify records for fixedDate2
        val dataRow3 = dailyClosingSheet.getRow(8)
        assertEquals("POS1", dataRow3.getCell(0).stringCellValue)
        assertEquals(fixedDate2.toLocalDate().toDateString(), dataRow3.getCell(1).stringCellValue)
        assertEquals(fixedDate2.toLocalTime().toTimeString(), dataRow3.getCell(2).stringCellValue)
        assertEquals(200.00, dataRow3.getCell(3).numericCellValue)
        assertEquals(300.00, dataRow3.getCell(4).numericCellValue)
        assertEquals(500.00, dataRow3.getCell(5).numericCellValue)
        assertEquals(20.00, dataRow3.getCell(6).numericCellValue)
        assertEquals(30.00, dataRow3.getCell(7).numericCellValue)
        assertEquals(10.00, dataRow3.getCell(8).numericCellValue)
        assertEquals(15.00, dataRow3.getCell(9).numericCellValue)
        assertEquals(0.00, dataRow3.getCell(10).numericCellValue)
        assertEquals(0.00, dataRow3.getCell(11).numericCellValue)
        assertEquals(0.00, dataRow3.getCell(12).numericCellValue)
        assertEquals(20.00, dataRow3.getCell(13).numericCellValue)
        assertEquals(450.00, dataRow3.getCell(14).numericCellValue)

        // Verify total summary row for fixedDate2
        val totalRow2 = dailyClosingSheet.getRow(9)
        assertEquals("Celkovo", totalRow2.getCell(0).stringCellValue)
        assertEquals(fixedDate2.toLocalDate().toDateString(), totalRow2.getCell(1).stringCellValue)
        assertEquals("", totalRow2.getCell(2).stringCellValue)
        assertEquals(200.00, totalRow2.getCell(3).numericCellValue)
        assertEquals(300.00, totalRow2.getCell(4).numericCellValue)
        assertEquals(500.00, totalRow2.getCell(5).numericCellValue)
        assertEquals(20.00, totalRow2.getCell(6).numericCellValue)
        assertEquals(30.00, totalRow2.getCell(7).numericCellValue)
        assertEquals(10.00, totalRow2.getCell(8).numericCellValue)
        assertEquals(15.00, totalRow2.getCell(9).numericCellValue)
        assertEquals(0.00, totalRow2.getCell(10).numericCellValue)
        assertEquals(0.00, totalRow2.getCell(11).numericCellValue)
        assertEquals(0.00, totalRow2.getCell(12).numericCellValue)
        assertEquals(20.00, totalRow2.getCell(13).numericCellValue)
        assertEquals(450.00, totalRow2.getCell(14).numericCellValue)

        // Verify the total rows have the highlighted style
        val cellStyle1 = totalRow1.getCell(0).cellStyle as XSSFCellStyle
        assertEquals(cellStyle1.fillForegroundColor, IndexedColors.LIGHT_YELLOW.index)

        val cellStyle2 = totalRow2.getCell(0).cellStyle as XSSFCellStyle
        assertEquals(cellStyle2.fillForegroundColor, IndexedColors.LIGHT_YELLOW.index)

        // Other movements sheet
        val otherMovementsSheet = workbook.getSheet("Ďalšie pohyby")

        // Verify main header
        val movementsMainHeaderRow = otherMovementsSheet.getRow(0)
        val movementsMainHeaderRow1 = movementsMainHeaderRow.getCell(0).stringCellValue
        val movementsMainHeaderRow2 = movementsMainHeaderRow.getCell(2).stringCellValue
        val movementsMainHeaderRow3 = movementsMainHeaderRow.getCell(otherMovementsSheet.getRow(0).lastCellNum - 1).stringCellValue

        assertEquals(
            "Kino: Bratislava Bory\nPoužívateľ: testuser",
            movementsMainHeaderRow1
        )
        assertEquals(
            "Ďalšie pohyby\nUzávierka od: ${fixedDate1.toLocalDate().minusDays(30).toDateString()} Do: ${
                fixedDate2.toLocalDate().plusDays(30).toDateString()
            } ",
            movementsMainHeaderRow2
        )
        assertContains(movementsMainHeaderRow3, "Dátum: ${LocalDate.now().toDateString()}\nČas:")

        // Verify column headers
        val movementsColumnHeaders = otherMovementsSheet.getRow(4)
        assertEquals("Pokladňa", movementsColumnHeaders.getCell(0).stringCellValue)
        assertEquals("Pohyb", movementsColumnHeaders.getCell(1).stringCellValue)
        assertEquals("Dátum", movementsColumnHeaders.getCell(2).stringCellValue)
        assertEquals("Čas", movementsColumnHeaders.getCell(3).stringCellValue)
        assertEquals("Určenie", movementsColumnHeaders.getCell(4).stringCellValue)
        assertEquals("Typ", movementsColumnHeaders.getCell(5).stringCellValue)
        assertEquals("Doklad", movementsColumnHeaders.getCell(6).stringCellValue)
        assertEquals("Var. symbol", movementsColumnHeaders.getCell(7).stringCellValue)
        assertEquals("Platba", movementsColumnHeaders.getCell(8).stringCellValue)
        assertEquals("Čiastka", movementsColumnHeaders.getCell(9).stringCellValue)

        // Verify movements records
        val movementsDataRow1 = otherMovementsSheet.getRow(5)
        assertEquals("POS1", movementsDataRow1.getCell(0).stringCellValue)
        assertEquals("Prijem Bory", movementsDataRow1.getCell(1).stringCellValue)
        assertEquals(otherMovementsData[0].createdAtDate.toDateString(), movementsDataRow1.getCell(2).stringCellValue)
        assertEquals(otherMovementsData[0].createdAtTime.toTimeString(), movementsDataRow1.getCell(3).stringCellValue)
        assertEquals("Bufet", movementsDataRow1.getCell(4).stringCellValue)
        assertEquals("Príjem", movementsDataRow1.getCell(5).stringCellValue)
        assertEquals("RCPT001", movementsDataRow1.getCell(6).stringCellValue)
        assertEquals("VS101", movementsDataRow1.getCell(7).stringCellValue)
        assertEquals("hotovosť", movementsDataRow1.getCell(8).stringCellValue)
        assertEquals("€ 100,00", movementsDataRow1.getCell(9).stringCellValue)

        val movementsDataRow2 = otherMovementsSheet.getRow(6)
        assertEquals("POS2", movementsDataRow2.getCell(0).stringCellValue)
        assertEquals("Vydaj odpoledne", movementsDataRow2.getCell(1).stringCellValue)
        assertEquals(otherMovementsData[1].createdAtDate.toDateString(), movementsDataRow2.getCell(2).stringCellValue)
        assertEquals(otherMovementsData[1].createdAtTime.toTimeString(), movementsDataRow2.getCell(3).stringCellValue)
        assertEquals("Odvod", movementsDataRow2.getCell(4).stringCellValue)
        assertEquals("Výdaj", movementsDataRow2.getCell(5).stringCellValue)
        assertEquals("", movementsDataRow2.getCell(6).stringCellValue)
        assertEquals("", movementsDataRow2.getCell(7).stringCellValue)
        assertEquals("kartou", movementsDataRow2.getCell(8).stringCellValue)
        assertEquals("€ 20,00", movementsDataRow2.getCell(9).stringCellValue)
    }

    @Test
    fun `test mapToExportResultModel - two closings in two days - should group by date and time into two `() {
        val fixedDate1 = LocalDateTime.of(2024, 6, 26, 11, 12, 30)
        val fixedDate2 = LocalDateTime.of(2024, 6, 26, 23, 16, 51)
        val closedAtFrom = fixedDate1.toLocalDate().minusDays(30)
        val closedAtTo = fixedDate2.toLocalDate().plusDays(30)
        val username = "testuser"
        val dailyClosingData = listOf(
            // Record for fixedDate1
            DailyClosingsExportRecordModel(
                posConfigurationTitle = "POS1",
                closedAtDate = fixedDate1.toLocalDate(),
                closedAtTime = fixedDate1.toLocalTime(),
                salesCash = BigDecimal("100.00"),
                salesCashless = BigDecimal("200.00"),
                salesTotal = BigDecimal("300.00"),
                serviceFeesCash = BigDecimal("10.00"),
                serviceFeesCashless = BigDecimal("20.00"),
                cancelledCash = BigDecimal("5.00"),
                cancelledCashless = BigDecimal("10.00"),
                otherMovementsRevenues = BigDecimal("0.00"),
                otherMovementsExpenses = BigDecimal("0.00"),
                fixedPriceTicketsAmount = BigDecimal("0.00"),
                deduction = BigDecimal("5.00"),
                netSales = BigDecimal("285.00")
            ),
            // Record for fixedDate2
            DailyClosingsExportRecordModel(
                posConfigurationTitle = "POS1",
                closedAtDate = fixedDate2.toLocalDate(),
                closedAtTime = fixedDate2.toLocalTime(),
                salesCash = BigDecimal("200.00"),
                salesCashless = BigDecimal("300.00"),
                salesTotal = BigDecimal("500.00"),
                serviceFeesCash = BigDecimal("20.00"),
                serviceFeesCashless = BigDecimal("30.00"),
                cancelledCash = BigDecimal("10.00"),
                cancelledCashless = BigDecimal("15.00"),
                otherMovementsRevenues = BigDecimal("0.00"),
                otherMovementsExpenses = BigDecimal("0.00"),
                fixedPriceTicketsAmount = BigDecimal("0.00"),
                deduction = BigDecimal("20.00"),
                netSales = BigDecimal("450.00")
            )
        )

        val exportResult = underTest.mapToExportResultModel(
            closedAtFrom = closedAtFrom,
            closedAtTo = closedAtTo,
            data = dailyClosingData to listOf(),
            username = username
        )
        val byteArray = exportResult.inputStream.readAllBytes()
        assert(byteArray.isNotEmpty())

        // Open the generated Excel file from the byte array
        val workbook = XSSFWorkbook(ByteArrayInputStream(byteArray))
        val dailyClosingSheet = workbook.getSheet("Pokladničný výkaz")

        // Verify main header
        val closingsMainHeaderRow = dailyClosingSheet.getRow(0)
        val closingsMainHeaderRow1 = closingsMainHeaderRow.getCell(0).stringCellValue
        val closingsMainHeaderRow2 = closingsMainHeaderRow.getCell(2).stringCellValue
        val closingsMainHeaderRow3 = closingsMainHeaderRow.getCell(dailyClosingSheet.getRow(0).lastCellNum - 1).stringCellValue

        assertEquals(
            "Kino: Bratislava Bory\nPoužívateľ: testuser",
            closingsMainHeaderRow1
        )
        assertEquals(
            "Pokladničný výkaz\nOd: ${fixedDate1.toLocalDate().minusDays(30).toDateString()} Do: ${
                fixedDate2.toLocalDate().plusDays(30).toDateString()
            } ",
            closingsMainHeaderRow2
        )
        assertContains(closingsMainHeaderRow3, "Dátum: ${LocalDate.now().toDateString()}\nČas:")

        // Verify column headers
        val closingsColumnHeaders = dailyClosingSheet.getRow(4)
        assertEquals("Pokladňa", closingsColumnHeaders.getCell(0).stringCellValue)
        assertEquals("Dátum", closingsColumnHeaders.getCell(1).stringCellValue)
        assertEquals("Čas", closingsColumnHeaders.getCell(2).stringCellValue)
        assertEquals("Predaj hot.", closingsColumnHeaders.getCell(3).stringCellValue)
        assertEquals("Predaj bezhot.", closingsColumnHeaders.getCell(4).stringCellValue)
        assertEquals("Σ Predaj", closingsColumnHeaders.getCell(5).stringCellValue)
        assertEquals("Služ. hot.", closingsColumnHeaders.getCell(6).stringCellValue)
        assertEquals("Služ. nh.", closingsColumnHeaders.getCell(7).stringCellValue)
        assertEquals("Vrác. hot.", closingsColumnHeaders.getCell(8).stringCellValue)
        assertEquals("Vr. neh.", closingsColumnHeaders.getCell(9).stringCellValue)
        assertEquals("Ost. př.", closingsColumnHeaders.getCell(10).stringCellValue)
        assertEquals("Ost. v.", closingsColumnHeaders.getCell(11).stringCellValue)
        assertEquals("Pevná cena", closingsColumnHeaders.getCell(12).stringCellValue)
        assertEquals("Odvod", closingsColumnHeaders.getCell(13).stringCellValue)
        assertEquals("Čistá tržba", closingsColumnHeaders.getCell(14).stringCellValue)

        // Verify records and summary rows for fixedDate1
        val closingsDataRow1 = dailyClosingSheet.getRow(5)
        assertEquals("POS1", closingsDataRow1.getCell(0).stringCellValue)
        assertEquals(fixedDate1.toLocalDate().toDateString(), closingsDataRow1.getCell(1).stringCellValue)
        assertEquals(fixedDate1.toLocalTime().toTimeString(), closingsDataRow1.getCell(2).stringCellValue)
        assertEquals(100.00, closingsDataRow1.getCell(3).numericCellValue)
        assertEquals(200.00, closingsDataRow1.getCell(4).numericCellValue)
        assertEquals(300.00, closingsDataRow1.getCell(5).numericCellValue)
        assertEquals(10.00, closingsDataRow1.getCell(6).numericCellValue)
        assertEquals(20.00, closingsDataRow1.getCell(7).numericCellValue)
        assertEquals(5.00, closingsDataRow1.getCell(8).numericCellValue)
        assertEquals(10.00, closingsDataRow1.getCell(9).numericCellValue)
        assertEquals(0.00, closingsDataRow1.getCell(10).numericCellValue)
        assertEquals(0.00, closingsDataRow1.getCell(11).numericCellValue)
        assertEquals(0.00, closingsDataRow1.getCell(12).numericCellValue)
        assertEquals(5.00, closingsDataRow1.getCell(13).numericCellValue)
        assertEquals(285.00, closingsDataRow1.getCell(14).numericCellValue)

        // Verify total summary row for fixedDate1
        val totalRow1 = dailyClosingSheet.getRow(6)
        assertEquals("Celkovo", totalRow1.getCell(0).stringCellValue)
        assertEquals(fixedDate1.toLocalDate().toDateString(), totalRow1.getCell(1).stringCellValue)
        assertEquals("", totalRow1.getCell(2).stringCellValue)
        assertEquals(100.00, totalRow1.getCell(3).numericCellValue)
        assertEquals(200.00, totalRow1.getCell(4).numericCellValue)
        assertEquals(300.00, totalRow1.getCell(5).numericCellValue)
        assertEquals(10.00, totalRow1.getCell(6).numericCellValue)
        assertEquals(20.00, totalRow1.getCell(7).numericCellValue)
        assertEquals(5.00, totalRow1.getCell(8).numericCellValue)
        assertEquals(10.00, totalRow1.getCell(9).numericCellValue)
        assertEquals(0.00, totalRow1.getCell(10).numericCellValue)
        assertEquals(0.00, totalRow1.getCell(11).numericCellValue)
        assertEquals(0.00, totalRow1.getCell(12).numericCellValue)
        assertEquals(5.00, totalRow1.getCell(13).numericCellValue)
        assertEquals(285.00, totalRow1.getCell(14).numericCellValue)

        // Verify records for fixedDate2
        val dataRow2 = dailyClosingSheet.getRow(7)
        assertEquals("POS1", dataRow2.getCell(0).stringCellValue)
        assertEquals(fixedDate2.toLocalDate().toDateString(), dataRow2.getCell(1).stringCellValue)
        assertEquals(fixedDate2.toLocalTime().toTimeString(), dataRow2.getCell(2).stringCellValue)
        assertEquals(200.00, dataRow2.getCell(3).numericCellValue)
        assertEquals(300.00, dataRow2.getCell(4).numericCellValue)
        assertEquals(500.00, dataRow2.getCell(5).numericCellValue)
        assertEquals(20.00, dataRow2.getCell(6).numericCellValue)
        assertEquals(30.00, dataRow2.getCell(7).numericCellValue)
        assertEquals(10.00, dataRow2.getCell(8).numericCellValue)
        assertEquals(15.00, dataRow2.getCell(9).numericCellValue)
        assertEquals(0.00, dataRow2.getCell(10).numericCellValue)
        assertEquals(0.00, dataRow2.getCell(11).numericCellValue)
        assertEquals(0.00, dataRow2.getCell(12).numericCellValue)
        assertEquals(20.00, dataRow2.getCell(13).numericCellValue)
        assertEquals(450.00, dataRow2.getCell(14).numericCellValue)

        // Verify total summary row for fixedDate2
        val totalRow2 = dailyClosingSheet.getRow(8)
        assertEquals("Celkovo", totalRow2.getCell(0).stringCellValue)
        assertEquals(fixedDate2.toLocalDate().toDateString(), totalRow2.getCell(1).stringCellValue)
        assertEquals("", totalRow2.getCell(2).stringCellValue)
        assertEquals(200.00, totalRow2.getCell(3).numericCellValue)
        assertEquals(300.00, totalRow2.getCell(4).numericCellValue)
        assertEquals(500.00, totalRow2.getCell(5).numericCellValue)
        assertEquals(20.00, totalRow2.getCell(6).numericCellValue)
        assertEquals(30.00, totalRow2.getCell(7).numericCellValue)
        assertEquals(10.00, totalRow2.getCell(8).numericCellValue)
        assertEquals(15.00, totalRow2.getCell(9).numericCellValue)
        assertEquals(0.00, totalRow2.getCell(10).numericCellValue)
        assertEquals(0.00, totalRow2.getCell(11).numericCellValue)
        assertEquals(0.00, totalRow2.getCell(12).numericCellValue)
        assertEquals(20.00, totalRow2.getCell(13).numericCellValue)
        assertEquals(450.00, totalRow2.getCell(14).numericCellValue)

        // Verify the total rows have the highlighted style
        val cellStyle1 = totalRow1.getCell(0).cellStyle as XSSFCellStyle
        assertEquals(cellStyle1.fillForegroundColor, IndexedColors.LIGHT_YELLOW.index)

        val cellStyle2 = totalRow2.getCell(0).cellStyle as XSSFCellStyle
        assertEquals(cellStyle2.fillForegroundColor, IndexedColors.LIGHT_YELLOW.index)
    }
}
