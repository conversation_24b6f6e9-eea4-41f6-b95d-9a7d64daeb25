package com.cleevio.cinemax.api.module.screening.service

import com.cleevio.cinemax.api.DatabaseCleanup
import com.cleevio.cinemax.api.INTEGRATION_TEST_DATE_TIME
import com.cleevio.cinemax.api.IntegrationTest
import com.cleevio.cinemax.api.common.constant.ExportFormat
import com.cleevio.cinemax.api.common.constant.PaymentType
import com.cleevio.cinemax.api.common.util.isEqualTo
import com.cleevio.cinemax.api.module.auditorium.service.AuditoriumRepository
import com.cleevio.cinemax.api.module.auditoriumlayout.service.AuditoriumLayoutRepository
import com.cleevio.cinemax.api.module.basket.constant.BasketState
import com.cleevio.cinemax.api.module.basket.service.BasketRepository
import com.cleevio.cinemax.api.module.basketitem.constant.BasketItemType
import com.cleevio.cinemax.api.module.basketitem.service.BasketItemRepository
import com.cleevio.cinemax.api.module.distributor.service.DistributorRepository
import com.cleevio.cinemax.api.module.language.entity.Language
import com.cleevio.cinemax.api.module.language.service.LanguageRepository
import com.cleevio.cinemax.api.module.movie.service.MovieRepository
import com.cleevio.cinemax.api.module.posconfiguration.service.PosConfigurationRepository
import com.cleevio.cinemax.api.module.pricecategory.service.PriceCategoryRepository
import com.cleevio.cinemax.api.module.pricecategoryitem.constant.PriceCategoryItemNumber
import com.cleevio.cinemax.api.module.pricecategoryitem.service.PriceCategoryItemRepository
import com.cleevio.cinemax.api.module.rating.entity.Rating
import com.cleevio.cinemax.api.module.rating.service.RatingRepository
import com.cleevio.cinemax.api.module.reservation.constant.ReservationState
import com.cleevio.cinemax.api.module.reservation.service.ReservationRepository
import com.cleevio.cinemax.api.module.screening.constant.ScreeningState
import com.cleevio.cinemax.api.module.screening.service.query.AdminExportDistributorScreeningsFilter
import com.cleevio.cinemax.api.module.screening.service.query.AdminExportDistributorScreeningsQuery
import com.cleevio.cinemax.api.module.screeningfee.service.ScreeningFeeRepository
import com.cleevio.cinemax.api.module.screeningtype.service.ScreeningTypeRepository
import com.cleevio.cinemax.api.module.screeningtypes.service.ScreeningTypesRepository
import com.cleevio.cinemax.api.module.seat.service.SeatRepository
import com.cleevio.cinemax.api.module.technology.entity.Technology
import com.cleevio.cinemax.api.module.technology.service.TechnologyRepository
import com.cleevio.cinemax.api.module.ticket.service.TicketRepository
import com.cleevio.cinemax.api.module.ticketprice.service.TicketPriceRepository
import com.cleevio.cinemax.api.module.tmslanguage.entity.TmsLanguage
import com.cleevio.cinemax.api.module.tmslanguage.service.TmsLanguageRepository
import com.cleevio.cinemax.api.util.copyBasketItem
import com.cleevio.cinemax.api.util.createAuditorium
import com.cleevio.cinemax.api.util.createAuditoriumLayout
import com.cleevio.cinemax.api.util.createBasket
import com.cleevio.cinemax.api.util.createBasketItem
import com.cleevio.cinemax.api.util.createDistributor
import com.cleevio.cinemax.api.util.createMovie
import com.cleevio.cinemax.api.util.createPosConfiguration
import com.cleevio.cinemax.api.util.createPriceCategory
import com.cleevio.cinemax.api.util.createPriceCategoryItem
import com.cleevio.cinemax.api.util.createReservation
import com.cleevio.cinemax.api.util.createScreening
import com.cleevio.cinemax.api.util.createScreeningFee
import com.cleevio.cinemax.api.util.createScreeningType
import com.cleevio.cinemax.api.util.createScreeningTypes
import com.cleevio.cinemax.api.util.createSeat
import com.cleevio.cinemax.api.util.createTicket
import com.cleevio.cinemax.api.util.createTicketPrice
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import java.math.BigDecimal
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.LocalTime
import kotlin.test.assertEquals
import kotlin.test.assertTrue

class AdminXmlExportDistributorScreeningsQueryServiceIT @Autowired constructor(
    private val underTest: AdminXmlExportDistributorScreeningsQueryService,
    private val auditoriumRepository: AuditoriumRepository,
    private val auditoriumLayoutRepository: AuditoriumLayoutRepository,
    private val ratingRepository: RatingRepository,
    private val distributorRepository: DistributorRepository,
    private val technologyRepository: TechnologyRepository,
    private val tmsLanguageRepository: TmsLanguageRepository,
    private val languageRepository: LanguageRepository,
    private val priceCategoryRepository: PriceCategoryRepository,
    private val priceCategoryItemRepository: PriceCategoryItemRepository,
    private val screeningTypeRepository: ScreeningTypeRepository,
    private val screeningRepository: ScreeningRepository,
    private val screeningTypesRepository: ScreeningTypesRepository,
    private val screeningFeeRepository: ScreeningFeeRepository,
    private val seatRepository: SeatRepository,
    private val reservationRepository: ReservationRepository,
    private val ticketRepository: TicketRepository,
    private val ticketPriceRepository: TicketPriceRepository,
    private val movieRepository: MovieRepository,
    private val basketRepository: BasketRepository,
    private val basketItemRepository: BasketItemRepository,
    private val posConfigurationRepository: PosConfigurationRepository,
) : IntegrationTest(DatabaseCleanup.AFTER_EACH) {

    @BeforeEach
    fun setUp() {
        auditoriumRepository.saveAll(setOf(AUDITORIUM_1, AUDITORIUM_2))
        auditoriumLayoutRepository.saveAll(setOf(AUDITORIUM_LAYOUT_1, AUDITORIUM_LAYOUT_2))
        distributorRepository.saveAll(setOf(DISTRIBUTOR_1, DISTRIBUTOR_2, DISTRIBUTOR_3))
        ratingRepository.saveAll(setOf(RATING_1, RATING_2))
        technologyRepository.saveAll(setOf(TECHNOLOGY_1, TECHNOLOGY_2))
        languageRepository.saveAll(setOf(LANGUAGE_1, LANGUAGE_2))
        tmsLanguageRepository.saveAll(setOf(TMS_LANGUAGE_1, TMS_LANGUAGE_2))
        movieRepository.saveAll(setOf(MOVIE_1, MOVIE_2, MOVIE_3, MOVIE_4))
        priceCategoryRepository.save(PRICE_CATEGORY_1)
        screeningRepository.saveAll(
            setOf(
                SCREENING_1,
                SCREENING_2_DELETED,
                SCREENING_3_CANCELLED,
                SCREENING_4_STOPPED,
                SCREENING_5,
                SCREENING_6
            )
        )
        screeningFeeRepository.save(SCREENING_FEE_1)
        priceCategoryItemRepository.saveAll(setOf(PRICE_CATEGORY_ITEM_1, PRICE_CATEGORY_ITEM_2))
        screeningTypeRepository.saveAll(setOf(SCREENING_TYPE_1, SCREENING_TYPE_2))
        screeningTypesRepository.saveAll(setOf(SCREENING_TYPES_1, SCREENING_TYPES_2))
        seatRepository.saveAll(setOf(SEAT_1, SEAT_2, SEAT_3))
        reservationRepository.saveAll(setOf(RESERVATION_1, RESERVATION_2, RESERVATION_3, RESERVATION_4))
        ticketPriceRepository.saveAll(setOf(TICKET_PRICE_1, TICKET_PRICE_2, TICKET_PRICE_3, TICKET_PRICE_4))
        ticketRepository.saveAll(setOf(TICKET_1, TICKET_2, TICKET_3, TICKET_4))
        posConfigurationRepository.save(POS_CONFIGURATION_1)
        basketRepository.save(BASKET_1_POS_1)
        basketItemRepository.saveAll(
            setOf(
                BASKET_ITEM_TICKET_1_BASKET_1,
                BASKET_ITEM_TICKET_2_BASKET_1,
                BASKET_ITEM_TICKET_3_BASKET_1,
                BASKET_ITEM_TICKET_4_BASKET_1
            )
        )
    }

    @Test
    fun `test AdminXmlExportDistributorScreeningsQuery - no filter - should return all records correctly`() {
        val results = underTest(
            AdminExportDistributorScreeningsQuery(
                filter = AdminExportDistributorScreeningsFilter(
                    dateFrom = LocalDate.EPOCH,
                    dateTo = LocalDate.now().plusYears(1),
                    distributorIds = null
                ),
                exportFormat = ExportFormat.XML,
                username = USERNAME
            )
        )

        assertEquals(2, results.size)

        results.first { it.auditoriumOriginalCode == AUDITORIUM_1.originalCode }.let {
            assertEquals(AUDITORIUM_1.title, it.auditoriumTitle)
            assertEquals(1, it.screenings.size)
            it.screenings.first { screening -> screening.screeningOriginalId == SCREENING_1.originalId }
                .let { screening ->
                    assertEquals(LocalDateTime.of(SCREENING_1.date, SCREENING_1.time), screening.screeningDateTime)
                    assertEquals(1, screening.ticketsCount)
                    assertTrue(50.toBigDecimal() isEqualTo screening.groTicketSales)
                    assertEquals(TICKET_PRICE_3.basePriceBeforeDiscount, screening.ticketBasePrice)
                    assertEquals(1, screening.tickets.size)
                    screening.tickets.first { ticket ->
                        ticket.ticketPrice.isEqualTo(TICKET_PRICE_3.basePriceBeforeDiscount)
                    }.let { ticket -> assertEquals(1, ticket.ticketsCount) }
                }
        }

        results.first { it.auditoriumOriginalCode == AUDITORIUM_2.originalCode }.let {
            assertEquals(AUDITORIUM_2.title, it.auditoriumTitle)
            assertEquals(2, it.screenings.size)
            it.screenings.first { screening -> screening.screeningOriginalId == SCREENING_6.originalId }
                .let { screening ->
                    assertEquals(LocalDateTime.of(SCREENING_6.date, SCREENING_6.time), screening.screeningDateTime)
                    assertEquals(2, screening.ticketsCount)
                    assertTrue(250.toBigDecimal() isEqualTo screening.groTicketSales)
                    assertEquals(TICKET_PRICE_1.basePriceBeforeDiscount, screening.ticketBasePrice)
                    assertEquals(2, screening.tickets.size)
                    screening.tickets.first { ticket ->
                        ticket.ticketPrice.isEqualTo(TICKET_PRICE_1.basePriceBeforeDiscount)
                    }.let { ticket -> assertEquals(1, ticket.ticketsCount) }
                    screening.tickets.first { ticket ->
                        ticket.ticketPrice.isEqualTo(TICKET_PRICE_2.basePriceBeforeDiscount)
                    }.let { ticket -> assertEquals(1, ticket.ticketsCount) }
                }

            it.screenings.first { screening -> screening.screeningOriginalId == SCREENING_5.originalId }
                .let { screening ->
                    assertEquals(LocalDateTime.of(SCREENING_5.date, SCREENING_5.time), screening.screeningDateTime)
                    assertEquals(1, screening.ticketsCount)
                    assertTrue(25.toBigDecimal() isEqualTo screening.groTicketSales)
                    assertEquals(TICKET_PRICE_4.basePriceBeforeDiscount, screening.ticketBasePrice)
                    assertEquals(1, screening.tickets.size)
                    screening.tickets.first { ticket ->
                        ticket.ticketPrice.isEqualTo(TICKET_PRICE_4.basePriceBeforeDiscount)
                    }.let { ticket -> assertEquals(1, ticket.ticketsCount) }
                }
        }
    }
}

private const val USERNAME = "username"
private val LOCAL_TIME = LocalTime.of(20, 0, 0)
private val AUDITORIUM_1 = createAuditorium(
    originalId = 1,
    code = "A",
    title = "SÁLA A - CINEMAX BRATISLAVA",
    originalCode = 22
)
private val AUDITORIUM_2 = createAuditorium(
    originalId = 2,
    code = "IMAX",
    title = "IMAX - CINEMAX BRATISLAVA",
    capacity = 150,
    originalCode = 11
)
private val DISTRIBUTOR_1 = createDistributor(originalId = 1)
private val DISTRIBUTOR_2 = createDistributor(originalId = 2, title = "Hello movies, s.r.o.")
private val DISTRIBUTOR_3 = createDistributor(originalId = 3, title = "GoodBye movies, s.r.o.")
private val AUDITORIUM_LAYOUT_1 = createAuditoriumLayout(originalId = 1, auditoriumId = AUDITORIUM_1.id)
private val AUDITORIUM_LAYOUT_2 = createAuditoriumLayout(
    originalId = 2,
    auditoriumId = AUDITORIUM_2.id,
    title = "Special"
)
private val RATING_1 = Rating(
    originalId = 1,
    code = "13",
    title = "13"
)
private val RATING_2 = Rating(
    originalId = 2,
    code = "16",
    title = "16"
)
private val TECHNOLOGY_1 = Technology(
    originalId = 1,
    title = "2D",
    code = "2DA"
)
private val LANGUAGE_1 = Language(
    originalId = 10,
    title = "slovak",
    code = "SVK"
)
private val TMS_LANGUAGE_1 = TmsLanguage(
    originalId = 1,
    title = "Slovenske zneni",
    code = "TMS2"
)
private val TECHNOLOGY_2 = Technology(
    originalId = 2,
    title = "3D IMAX",
    code = "3I"
)
private val LANGUAGE_2 = Language(
    originalId = 11,
    title = "czech",
    code = "CZE"
)
private val TMS_LANGUAGE_2 = TmsLanguage(
    originalId = 2,
    title = "Ceske zneni",
    code = "TMS1"
)
private val MOVIE_1 = createMovie(
    originalId = 1,
    title = "Star Wars: Episode I – The Phantom Menace",
    releaseYear = 2001,
    distributorId = DISTRIBUTOR_1.id,
    ratingId = RATING_1.id,
    technologyId = TECHNOLOGY_1.id,
    languageId = LANGUAGE_1.id,
    tmsLanguageId = TMS_LANGUAGE_1.id
)
private val MOVIE_2 = createMovie(
    originalId = 2,
    code = "873946",
    title = "Matrix",
    rawTitle = "Matrix RAW",
    releaseYear = 2000,
    distributorId = DISTRIBUTOR_1.id,
    duration = 150,
    ratingId = RATING_2.id,
    technologyId = TECHNOLOGY_2.id,
    languageId = LANGUAGE_2.id,
    tmsLanguageId = TMS_LANGUAGE_2.id
)
private val MOVIE_3 = createMovie(
    originalId = 3,
    code = "873947",
    title = "Without screening",
    rawTitle = "Without screening raw",
    releaseYear = 2000,
    distributorId = DISTRIBUTOR_1.id,
    duration = 150,
    ratingId = RATING_2.id,
    technologyId = TECHNOLOGY_2.id,
    languageId = LANGUAGE_2.id,
    tmsLanguageId = TMS_LANGUAGE_2.id
)
private val MOVIE_4 = createMovie(
    originalId = 4,
    code = "873999",
    title = "Lego movie",
    rawTitle = "Lego movie 3D",
    releaseYear = 2010,
    distributorId = DISTRIBUTOR_3.id,
    duration = 150,
    ratingId = RATING_2.id,
    technologyId = TECHNOLOGY_2.id,
    languageId = LANGUAGE_2.id,
    tmsLanguageId = TMS_LANGUAGE_2.id
)
private val PRICE_CATEGORY_1 = createPriceCategory(
    originalId = 1,
    title = "ARTMAX PO 17",
    active = true
)
private val SCREENING_1 = createScreening(
    originalId = 1,
    auditoriumId = AUDITORIUM_1.id,
    auditoriumLayoutId = AUDITORIUM_LAYOUT_1.id,
    movieId = MOVIE_1.id,
    priceCategoryId = PRICE_CATEGORY_1.id,
    date = INTEGRATION_TEST_DATE_TIME.toLocalDate(),
    time = LOCAL_TIME.plusHours(2).plusMinutes(1)
)
private val SCREENING_2_DELETED = createScreening(
    originalId = 2,
    auditoriumId = AUDITORIUM_1.id,
    auditoriumLayoutId = AUDITORIUM_LAYOUT_1.id,
    movieId = MOVIE_2.id,
    priceCategoryId = PRICE_CATEGORY_1.id,
    state = ScreeningState.PUBLISHED,
    stopped = true
).apply { markDeleted() }
private val SCREENING_3_CANCELLED = createScreening(
    originalId = 3,
    auditoriumId = AUDITORIUM_1.id,
    auditoriumLayoutId = AUDITORIUM_LAYOUT_1.id,
    movieId = MOVIE_2.id,
    priceCategoryId = PRICE_CATEGORY_1.id,
    state = ScreeningState.PUBLISHED,
    cancelled = true
)
private val SCREENING_4_STOPPED = createScreening(
    originalId = 4,
    auditoriumId = AUDITORIUM_2.id,
    auditoriumLayoutId = AUDITORIUM_LAYOUT_1.id,
    movieId = MOVIE_2.id,
    priceCategoryId = PRICE_CATEGORY_1.id,
    state = ScreeningState.PUBLISHED,
    stopped = true
)

private val SCREENING_5 = createScreening(
    originalId = 5,
    auditoriumId = AUDITORIUM_2.id,
    auditoriumLayoutId = AUDITORIUM_LAYOUT_1.id,
    movieId = MOVIE_4.id,
    priceCategoryId = PRICE_CATEGORY_1.id,
    state = ScreeningState.PUBLISHED,
    date = INTEGRATION_TEST_DATE_TIME.toLocalDate().plusDays(5),
    time = LOCAL_TIME.plusHours(3).plusMinutes(10)
)
private val SCREENING_6 = createScreening(
    originalId = 6,
    auditoriumId = AUDITORIUM_2.id,
    auditoriumLayoutId = AUDITORIUM_LAYOUT_2.id,
    movieId = MOVIE_2.id,
    priceCategoryId = PRICE_CATEGORY_1.id,
    state = ScreeningState.PUBLISHED,
    date = INTEGRATION_TEST_DATE_TIME.toLocalDate().minusDays(4),
    time = LOCAL_TIME.plusMinutes(15)
)
private val SCREENING_FEE_1 = createScreeningFee(
    originalScreeningId = 1,
    screeningId = SCREENING_6.id,
    surchargeVip = BigDecimal.ONE,
    surchargePremium = BigDecimal.valueOf(0.5),
    surchargeImax = BigDecimal.valueOf(2),
    surchargeUltraX = BigDecimal.valueOf(0.5),
    serviceFeeVip = BigDecimal.ONE,
    serviceFeePremium = BigDecimal.valueOf(0.5),
    serviceFeeImax = BigDecimal.valueOf(2),
    serviceFeeUltraX = BigDecimal.valueOf(0.5),
    surchargeDBox = BigDecimal.valueOf(5),
    serviceFeeGeneral = BigDecimal.valueOf(0.2)
)
private val PRICE_CATEGORY_ITEM_1 = createPriceCategoryItem(
    priceCategoryId = PRICE_CATEGORY_1.id,
    title = "Dospely",
    price = 120.toBigDecimal(),
    number = PriceCategoryItemNumber.PRICE_1
)
private val PRICE_CATEGORY_ITEM_2 = createPriceCategoryItem(
    priceCategoryId = PRICE_CATEGORY_1.id,
    title = null,
    price = 60.toBigDecimal(),
    number = PriceCategoryItemNumber.PRICE_2
)
private val SCREENING_TYPE_1 = createScreeningType(
    originalId = 1,
    title = "Best movies ever, s.r.o."
)
private val SCREENING_TYPE_2 = createScreeningType(
    originalId = 2,
    title = "Worst movies ever, s.r.o."
)
private val SCREENING_TYPES_1 = createScreeningTypes(
    screeningId = SCREENING_6.id,
    screeningTypeId = SCREENING_TYPE_1.id
)
private val SCREENING_TYPES_2 = createScreeningTypes(
    screeningId = SCREENING_6.id,
    screeningTypeId = SCREENING_TYPE_2.id
)
private val SEAT_1 = createSeat(
    originalId = 1,
    auditoriumId = AUDITORIUM_2.id,
    auditoriumLayoutId = AUDITORIUM_LAYOUT_1.id
)
private val SEAT_2 = createSeat(
    originalId = 2,
    auditoriumId = AUDITORIUM_2.id,
    auditoriumLayoutId = AUDITORIUM_LAYOUT_2.id
)
private val SEAT_3 = createSeat(
    originalId = 3,
    auditoriumId = AUDITORIUM_1.id,
    auditoriumLayoutId = AUDITORIUM_LAYOUT_1.id
)
private val RESERVATION_1 = createReservation(
    screeningId = SCREENING_6.id,
    seatId = SEAT_1.id,
    state = ReservationState.UNAVAILABLE
)
private val RESERVATION_2 = createReservation(
    screeningId = SCREENING_6.id,
    seatId = SEAT_2.id,
    state = ReservationState.RESERVED
)
private val RESERVATION_3 = createReservation(
    screeningId = SCREENING_1.id,
    seatId = SEAT_3.id
)
private val RESERVATION_4 = createReservation(
    screeningId = SCREENING_5.id,
    seatId = SEAT_3.id
)
private val TICKET_PRICE_1 = createTicketPrice(
    screeningId = SCREENING_6.id,
    seatId = SEAT_1.id,
    totalPrice = 100.toBigDecimal(),
    basePrice = 50.toBigDecimal(),
    seatSurcharge = 15.toBigDecimal(),
    auditoriumSurcharge = 13.toBigDecimal()
)
private val TICKET_PRICE_2 = createTicketPrice(
    screeningId = SCREENING_6.id,
    seatId = SEAT_2.id,
    totalPrice = 150.toBigDecimal(),
    basePrice = 70.toBigDecimal(),
    seatSurcharge = 55.toBigDecimal(),
    auditoriumSurcharge = 28.toBigDecimal()
)
private val TICKET_PRICE_3 = createTicketPrice(
    screeningId = SCREENING_1.id,
    seatId = SEAT_3.id,
    totalPrice = 50.toBigDecimal(),
    basePrice = 30.toBigDecimal(),
    seatSurcharge = 5.toBigDecimal(),
    auditoriumSurcharge = 3.toBigDecimal()
)
private val TICKET_PRICE_4 = createTicketPrice(
    screeningId = SCREENING_5.id,
    seatId = SEAT_3.id,
    totalPrice = 25.toBigDecimal(),
    basePrice = 10.toBigDecimal(),
    seatSurcharge = 8.toBigDecimal(),
    auditoriumSurcharge = 2.toBigDecimal()
)
private val TICKET_1 = createTicket(
    originalId = 1,
    screeningId = SCREENING_6.id,
    reservationId = RESERVATION_1.id,
    ticketPriceId = TICKET_PRICE_1.id
)
private val TICKET_2 = createTicket(
    originalId = 2,
    screeningId = SCREENING_6.id,
    reservationId = RESERVATION_2.id,
    ticketPriceId = TICKET_PRICE_2.id
)
private val TICKET_3 = createTicket(
    originalId = 3,
    screeningId = SCREENING_1.id,
    reservationId = RESERVATION_3.id,
    ticketPriceId = TICKET_PRICE_3.id
)
private val TICKET_4 = createTicket(
    originalId = 4,
    screeningId = SCREENING_5.id,
    reservationId = RESERVATION_4.id,
    ticketPriceId = TICKET_PRICE_4.id
)

// POS CONFIGURATIONS
private val POS_CONFIGURATION_1 = createPosConfiguration(macAddress = "AA:BB:CC:DD:EE")

// BASKETS
private val BASKET_1_POS_1 = createBasket(
    state = BasketState.PAID,
    paymentType = PaymentType.CASH,
    paymentPosConfigurationId = POS_CONFIGURATION_1.id
)

// BASKET ITEMS
private val BASKET_ITEM_TICKET_1_BASKET_1 = createBasketItem(
    basketId = BASKET_1_POS_1.id,
    type = BasketItemType.TICKET,
    price = 10.toBigDecimal(),
    quantity = 1,
    ticketId = TICKET_1.id
)
private val BASKET_ITEM_TICKET_2_BASKET_1 = copyBasketItem(BASKET_ITEM_TICKET_1_BASKET_1) {
    it.ticketId = TICKET_2.id
}
private val BASKET_ITEM_TICKET_3_BASKET_1 = copyBasketItem(BASKET_ITEM_TICKET_1_BASKET_1) {
    it.ticketId = TICKET_3.id
}
private val BASKET_ITEM_TICKET_4_BASKET_1 = copyBasketItem(BASKET_ITEM_TICKET_1_BASKET_1) {
    it.ticketId = TICKET_4.id
}
