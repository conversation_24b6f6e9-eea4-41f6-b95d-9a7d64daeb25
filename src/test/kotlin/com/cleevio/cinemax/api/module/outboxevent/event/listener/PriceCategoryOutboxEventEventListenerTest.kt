package com.cleevio.cinemax.api.module.outboxevent.event.listener

import com.cleevio.cinemax.api.module.outboxevent.constant.OutboxEventType
import com.cleevio.cinemax.api.module.outboxevent.service.OutboxEventService
import com.cleevio.cinemax.api.module.outboxevent.service.command.CreateOutboxEventCommand
import com.cleevio.cinemax.api.module.pricecategory.event.PriceCategoryCreatedOrUpdatedEvent
import io.mockk.every
import io.mockk.just
import io.mockk.mockk
import io.mockk.runs
import io.mockk.verify
import org.junit.jupiter.api.Test
import java.util.UUID

class PriceCategoryOutboxEventEventListenerTest {

    private val outboxEventService = mockk<OutboxEventService>()
    private val underTest = PriceCategoryOutboxEventEventListener(outboxEventService)

    @Test
    fun `listen to priceCategory created or updated event - should correctly handle PriceCategoryCreatedOrUpdatedEvent`() {
        every { outboxEventService.createOutboxEvent(any()) } just runs

        val priceCategoryId = UUID.fromString("65aa8e3f-4b86-4eb3-b2e9-5b1482021eda")
        underTest.listenToPriceCategoryCreatedOrUpdatedEvent(
            PriceCategoryCreatedOrUpdatedEvent(priceCategoryId)
        )

        verify {
            outboxEventService.createOutboxEvent(
                CreateOutboxEventCommand(
                    entityId = priceCategoryId,
                    type = OutboxEventType.PRICE_CATEGORY_CREATED_OR_UPDATED
                )
            )
        }
    }
}
