package com.cleevio.cinemax.api.module.distributor.service

import com.cleevio.cinemax.api.MssqlIntegrationTest
import com.cleevio.cinemax.api.module.distributor.service.command.CreateOrUpdateDistributorCommand
import com.cleevio.cinemax.api.module.synchronizationfrommssql.constant.SynchronizationFromMssqlType
import com.cleevio.cinemax.api.module.synchronizationfrommssql.entity.SynchronizationFromMssql
import com.cleevio.cinemax.api.module.synchronizationfrommssql.service.command.CreateOrUpdateSynchronizationFromMssqlCommand
import io.mockk.Runs
import io.mockk.every
import io.mockk.just
import io.mockk.verifyAll
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.test.context.jdbc.Sql
import org.springframework.test.context.jdbc.SqlConfig
import org.springframework.test.context.jdbc.SqlGroup
import java.time.LocalDateTime
import java.util.Optional
import kotlin.test.assertEquals
import kotlin.test.assertTrue

@SqlGroup(
    Sql(
        scripts = [
            "/db/mssql-cinemax/test/init_mssql_distributor.sql"
        ],
        config = SqlConfig(
            dataSource = "mssqlCinemaxDataSource",
            transactionManager = "mssqlCinemaxTransactionManager"
        )
    ),
    Sql(
        scripts = [
            "/db/mssql-cinemax/test/drop_mssql_distributor.sql"
        ],
        config = SqlConfig(
            dataSource = "mssqlCinemaxDataSource",
            transactionManager = "mssqlCinemaxTransactionManager"
        ),
        executionPhase = Sql.ExecutionPhase.AFTER_TEST_METHOD
    )
)
class DistributorMssqlSynchronizationServiceIT @Autowired constructor(
    private val underTest: DistributorMssqlSynchronizationService,
    private val distributorMssqlFinderRepository: DistributorMssqlFinderRepository,
) : MssqlIntegrationTest() {

    @Test
    fun `test synchronize all - no PSQL distributors, 3 MSSQL distributors - should create 3 distributors`() {
        every { synchronizationFromMssqlFinderServiceMock.findByType(any()) } returns null
        every { distributorServiceMock.syncCreateOrUpdateDistributor(any()) } just Runs
        every { synchronizationFromMssqlServiceMock.createOrUpdate(any()) } returns SYNCHRONIZATION_FROM_MSSQL

        underTest.synchronizeAll()

        val commandCaptor = mutableListOf<CreateOrUpdateDistributorCommand>()

        verifyAll {
            synchronizationFromMssqlFinderServiceMock.findByType(SynchronizationFromMssqlType.DISTRIBUTOR)
            distributorServiceMock.syncCreateOrUpdateDistributor(capture(commandCaptor))
            synchronizationFromMssqlServiceMock.createOrUpdate(
                CreateOrUpdateSynchronizationFromMssqlCommand(
                    type = SynchronizationFromMssqlType.DISTRIBUTOR,
                    lastSynchronization = DISTRIBUTOR_3_UPDATED_AT
                )
            )
        }

        assertTrue(distributorMssqlFinderRepository.findAllByUpdatedAtGt(null).size == 3)
        assertTrue(commandCaptor.size == 3)
        assertEquals(EXPECTED_COMMAND_1, commandCaptor[0])
        assertEquals(EXPECTED_COMMAND_2, commandCaptor[1])
        assertEquals(EXPECTED_COMMAND_3, commandCaptor[2])
    }

    @Test
    fun `test synchronize all - 2 PSQL distributors, 3 MSSQL distributors - should create 1 distributor`() {
        every { synchronizationFromMssqlFinderServiceMock.findByType(any()) } returns DISTRIBUTOR_2_UPDATED_AT
        every { distributorServiceMock.syncCreateOrUpdateDistributor(any()) } just Runs
        every { synchronizationFromMssqlServiceMock.createOrUpdate(any()) } returns SYNCHRONIZATION_FROM_MSSQL

        underTest.synchronizeAll()

        val commandCaptor = mutableListOf<CreateOrUpdateDistributorCommand>()

        verifyAll {
            synchronizationFromMssqlFinderServiceMock.findByType(SynchronizationFromMssqlType.DISTRIBUTOR)
            distributorServiceMock.syncCreateOrUpdateDistributor(capture(commandCaptor))
            synchronizationFromMssqlServiceMock.createOrUpdate(
                CreateOrUpdateSynchronizationFromMssqlCommand(
                    type = SynchronizationFromMssqlType.DISTRIBUTOR,
                    lastSynchronization = DISTRIBUTOR_3_UPDATED_AT
                )
            )
        }

        assertTrue(distributorMssqlFinderRepository.findAllByUpdatedAtGt(DISTRIBUTOR_3_UPDATED_AT).isEmpty())
        assertTrue(commandCaptor.size == 1)
        assertEquals(EXPECTED_COMMAND_3, commandCaptor[0])
    }
}

private val DISTRIBUTOR_3_UPDATED_AT = LocalDateTime.of(2019, 5, 5, 16, 37, 0)
private val DISTRIBUTOR_2_UPDATED_AT = LocalDateTime.of(2019, 5, 4, 16, 37, 0)

private val EXPECTED_COMMAND_1 = CreateOrUpdateDistributorCommand(
    id = null,
    originalId = 1,
    code = "SS",
    disfilmCode = null,
    title = "Vertigo Distribution s.r.o.",
    addressStreet = Optional.of("Ul. gen.Klapku 68/43"),
    addressCity = Optional.of("Komárno"),
    addressPostCode = Optional.of("945 01"),
    contactName1 = Optional.of("Tomáš Ficza"),
    contactName2 = Optional.of("Peter Ficza"),
    contactName3 = Optional.of("Jan Ficza"),
    contactPhone1 = Optional.of("+************"),
    contactPhone2 = Optional.of("+************"),
    contactPhone3 = Optional.of("+************"),
    contactEmails = Optional.of(setOf("<EMAIL>", "<EMAIL>")),
    bankName = Optional.of("UniCredit Bank Czech Republic and Slovakia, a.s."),
    bankAccount = Optional.of("**********/1111"),
    idNumber = Optional.of("********"),
    taxIdNumber = Optional.of("**********"),
    vatRate = Optional.of(5),
    note = Optional.of("marketingová akcia -  event Spievankovo")
)

private val EXPECTED_COMMAND_2 = CreateOrUpdateDistributorCommand(
    id = null,
    originalId = 2,
    code = "2G",
    disfilmCode = null,
    title = "IMAX Film Holding Co.",
    addressStreet = Optional.of("12582 West Millennium Drive"),
    addressCity = Optional.of("CA, Los Angeles"),
    addressPostCode = Optional.of("900 94"),
    contactName1 = Optional.of("David King"),
    contactName2 = null,
    contactName3 = null,
    contactPhone1 = Optional.of("**********"),
    contactPhone2 = null,
    contactPhone3 = null,
    contactEmails = Optional.of(setOf("<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>")),
    bankName = Optional.of("WELLS FARGO BANK, N.A."),
    bankAccount = Optional.of("*********"),
    idNumber = null,
    taxIdNumber = null,
    vatRate = null,
    note = null
)

private val EXPECTED_COMMAND_3 = CreateOrUpdateDistributorCommand(
    id = null,
    originalId = 3,
    code = "13",
    disfilmCode = null,
    title = "SATURN ENTERTAINMENT, spol. s r. o.",
    addressStreet = null,
    addressCity = null,
    addressPostCode = null,
    contactName1 = null,
    contactName2 = null,
    contactName3 = null,
    contactPhone1 = null,
    contactPhone2 = null,
    contactPhone3 = null,
    contactEmails = Optional.of(emptySet()),
    bankName = null,
    bankAccount = null,
    idNumber = null,
    taxIdNumber = null,
    vatRate = null,
    note = null
)

private val SYNCHRONIZATION_FROM_MSSQL = SynchronizationFromMssql(
    type = SynchronizationFromMssqlType.DISTRIBUTOR,
    lastSynchronization = LocalDateTime.MIN,
    lastHeartbeat = null
)
