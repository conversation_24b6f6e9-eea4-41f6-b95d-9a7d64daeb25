package com.cleevio.cinemax.api.module.movie.util

import com.cleevio.cinemax.api.module.screening.constant.MovieFormat
import com.cleevio.cinemax.api.module.screening.constant.MovieLanguage
import com.cleevio.cinemax.api.module.screening.constant.MovieTechnology
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.Arguments
import org.junit.jupiter.params.provider.MethodSource
import java.util.stream.Stream
import kotlin.test.assertEquals

class MovieTitleParserTest {

    @ParameterizedTest
    @MethodSource("movieTitlesProvider")
    fun `test parseMovieTitle, should parse title correctly`(
        input: String,
        title: String,
        format: MovieFormat,
        technology: MovieTechnology?,
        language: MovieLanguage?,
    ) {
        val result = parseMovieTitle(input)

        assertEquals(title, result.title)
        assertEquals(format, result.format)
        assertEquals(technology, result.technology)
        assertEquals(language, result.language)
    }

    companion object {
        @JvmStatic
        fun movieTitlesProvider(): Stream<Arguments> {
            // long titles stored as string templates for improved readability of the stream
            val t1 = "Strážcovia galaxie 3"
            val t2 = "Dungeons & Dragons: Cest zlodejov"
            val t3 = "Meg 2: Návrat do hlbín"
            val t4 = "FANTASTICKÉ ZVERY: TAJOMSTVÁ DUMBLEDORA"

            return Stream.of(
                Arguments.of("Kill Bill", "Kill Bill", MovieFormat.FORMAT_2D, null, null),
                Arguments.of("Pán prstenov: Návrat krála", "Pán prstenov: Návrat krála", MovieFormat.FORMAT_2D, null, null),
                Arguments.of("$t1 2D (MD)", t1, MovieFormat.FORMAT_2D, null, MovieLanguage.MD),
                Arguments.of("$t1 IMAX 3D (ST)", t1, MovieFormat.FORMAT_3D, MovieTechnology.IMAX, MovieLanguage.ST),
                Arguments.of("$t1 2D ATMOS (ST)", t1, MovieFormat.FORMAT_2D, MovieTechnology.DOLBY_ATMOS, MovieLanguage.ST),
                Arguments.of("$t1 3D (SD)", t1, MovieFormat.FORMAT_3D, null, MovieLanguage.SD),
                Arguments.of("Pápežov exorcista 2D (ST)", "Pápežov exorcista", MovieFormat.FORMAT_2D, null, MovieLanguage.ST),
                Arguments.of("Air 2D (MD)", "Air", MovieFormat.FORMAT_2D, null, MovieLanguage.MD),
                Arguments.of("Air 2D (ST)", "Air", MovieFormat.FORMAT_2D, null, MovieLanguage.ST),
                Arguments.of("Zachránte tigra 2D (MD)", "Zachránte tigra", MovieFormat.FORMAT_2D, null, MovieLanguage.MD),
                Arguments.of("Zachránte tigra 2D (CD)", "Zachránte tigra", MovieFormat.FORMAT_2D, null, MovieLanguage.CD),
                Arguments.of("Možno poviem áno 2D (MD)", "Možno poviem áno", MovieFormat.FORMAT_2D, null, MovieLanguage.MD),
                Arguments.of("Možno poviem áno 2D (CT)", "Možno poviem áno", MovieFormat.FORMAT_2D, null, MovieLanguage.CT),
                Arguments.of("Alibi na mieru 2 2D (CD)", "Alibi na mieru 2", MovieFormat.FORMAT_2D, null, MovieLanguage.CD),
                Arguments.of("$t2 2D (MD)", t2, MovieFormat.FORMAT_2D, null, MovieLanguage.MD),
                Arguments.of("$t2 IMAX 2D (ST)", t2, MovieFormat.FORMAT_2D, MovieTechnology.IMAX, MovieLanguage.ST),
                Arguments.of("$t2 2D ATMOS (ST)", t2, MovieFormat.FORMAT_2D, MovieTechnology.DOLBY_ATMOS, MovieLanguage.ST),
                Arguments.of("$t3 4D2 (ST)", t3, MovieFormat.FORMAT_4D2, null, MovieLanguage.ST),
                Arguments.of("$t3 2DS (ST)", t3, MovieFormat.FORMAT_2DS, null, MovieLanguage.ST),
                Arguments.of("Nič 2D (ČT)", "Nič", MovieFormat.FORMAT_2D, null, MovieLanguage.CT),
                Arguments.of("Nič 2D (ČD)", "Nič", MovieFormat.FORMAT_2D, null, MovieLanguage.CD),
                Arguments.of("Oppenheimer 35 (ST)", "Oppenheimer", MovieFormat.FORMAT_35, null, MovieLanguage.ST),
                Arguments.of("Nevinný MP4 (ČT)", "Nevinný", MovieFormat.FORMAT_2D, MovieTechnology.MP4, MovieLanguage.CT),
                Arguments.of("Deti Nagana MP4 (OV)", "Deti Nagana", MovieFormat.FORMAT_2D, MovieTechnology.MP4, MovieLanguage.OV),
                Arguments.of("Avatar 3DS (ST)", "Avatar", MovieFormat.FORMAT_3DS, null, MovieLanguage.ST),
                Arguments.of("Avatar 4D3 (ST)", "Avatar", MovieFormat.FORMAT_4D3, null, MovieLanguage.ST),
                Arguments.of("Avatar 3DA (ST)", "Avatar", MovieFormat.FORMAT_3DA, null, MovieLanguage.ST),
                Arguments.of("Chlast VSCR (ČT)", "Chlast", MovieFormat.FORMAT_2D, MovieTechnology.VSCR, MovieLanguage.CT),
                Arguments.of("Modelár VOD (ČD)", "Modelár", MovieFormat.FORMAT_2D, MovieTechnology.VOD, MovieLanguage.CD),
                Arguments.of("ZAKLIATA JASKYŇA 2D (UV)", "ZAKLIATA JASKYŇA", MovieFormat.FORMAT_2D, null, MovieLanguage.UV),
                Arguments.of("$t4 IMAX 2D (S", t4, MovieFormat.FORMAT_2D, MovieTechnology.IMAX, null),
                Arguments.of("Skyfall (MD)", "Skyfall", MovieFormat.FORMAT_2D, null, MovieLanguage.MD),
                Arguments.of("Prometheus 3D", "Prometheus", MovieFormat.FORMAT_3D, null, null),
                Arguments.of("Autá 2 3D", "Autá 2", MovieFormat.FORMAT_3D, null, null),
                Arguments.of("STANKO 2D (ST)", "STANKO", MovieFormat.FORMAT_2D, null, MovieLanguage.ST)
            )
        }
    }
}
