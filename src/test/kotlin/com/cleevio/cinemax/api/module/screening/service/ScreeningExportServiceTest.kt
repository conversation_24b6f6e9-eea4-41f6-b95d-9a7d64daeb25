package com.cleevio.cinemax.api.module.screening.service

import com.cleevio.cinemax.api.common.constant.ExportFormat
import com.cleevio.cinemax.api.common.service.model.ExportResultModel
import com.cleevio.cinemax.api.module.screening.constant.ScreeningState
import com.cleevio.cinemax.api.module.screening.service.model.ScreeningExportRecordModel
import com.cleevio.cinemax.api.module.screening.service.query.AdminExportScreeningsFilter
import com.cleevio.cinemax.api.module.screening.service.query.AdminExportScreeningsQuery
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import io.mockk.verifySequence
import org.junit.jupiter.api.assertThrows
import java.io.ByteArrayInputStream
import java.time.LocalDate
import java.time.LocalTime
import java.util.UUID
import kotlin.test.Test
import kotlin.test.assertEquals

class ScreeningExportServiceTest {

    private val adminExportScreeningsQueryService = mockk<AdminExportScreeningsQueryService>()
    private val screeningXlsxExportResultMapper = mockk<ScreeningXlsxExportResultMapper>()
    private val underTest = ScreeningExportService(
        adminExportScreeningsQueryService = adminExportScreeningsQueryService,
        screeningXlsxExportResultMapper = screeningXlsxExportResultMapper
    )

    @Test
    fun `test exportScreenings - valid query with XLSX format - should call related service and mapper`() {
        val now = LocalDate.now()
        val username = "username"
        val filter = AdminExportScreeningsFilter(
            dateFrom = now.minusDays(1),
            dateTo = now,
            states = setOf(ScreeningState.PUBLISHED),
            auditoriumIds = setOf(UUID.fromString("41fa16ea-c3b9-4a2b-88b0-************")),
            movieIds = setOf(UUID.fromString("41fa16ea-c3b9-4a2b-88b0-************")),
            distributorIds = setOf(UUID.fromString("41fa16ea-c3b9-4a2b-88b0-************"))
        )
        val query = AdminExportScreeningsQuery(
            filter = filter,
            exportFormat = ExportFormat.XLSX,
            username = username
        )

        val exportData = listOf(
            ScreeningExportRecordModel(
                movieTitle = "Matrix",
                distributorTitle = "Hello movies, s.r.o.",
                movieLanguage = "czech",
                movieTechnology = "3D IMAX",
                movieFormat = "3D",
                movieDuration = 150,
                movieRating = "Very good",
                screeningDate = now.plusDays(8),
                screeningTime = LocalTime.of(20, 15, 0),
                screeningEndTime = LocalTime.of(22, 45, 0),
                auditoriumTitle = "IMAX - CINEMAX BRATISLAVA",
                priceCategoryTitle = "Do 17h",
                distributorCommission = 50
            )
        )
        val byteArray = ByteArray(1024)
        val inputStream = ByteArrayInputStream(byteArray)
        val exportResult = ExportResultModel(inputStream, byteArray.size.toLong())

        every { adminExportScreeningsQueryService(query) } returns exportData
        every {
            screeningXlsxExportResultMapper.mapToExportResultModel(
                any(),
                any(),
                any(),
                any()
            )
        } returns exportResult

        assertEquals(exportResult, underTest.exportScreenings(query))

        verifySequence {
            adminExportScreeningsQueryService(query)
            screeningXlsxExportResultMapper.mapToExportResultModel(
                dateFrom = now.minusDays(1),
                dateTo = now,
                data = exportData,
                username = username
            )
        }
    }

    @Test
    fun `test exportScreenings - valid query with XML format - should throw`() {
        val now = LocalDate.now()
        val username = "username"
        val filter = AdminExportScreeningsFilter(
            dateFrom = now.minusDays(1),
            dateTo = now,
            states = setOf(ScreeningState.PUBLISHED),
            auditoriumIds = setOf(UUID.fromString("41fa16ea-c3b9-4a2b-88b0-************")),
            movieIds = setOf(UUID.fromString("41fa16ea-c3b9-4a2b-88b0-************")),
            distributorIds = setOf(UUID.fromString("41fa16ea-c3b9-4a2b-88b0-************"))
        )
        val query = AdminExportScreeningsQuery(
            filter = filter,
            exportFormat = ExportFormat.XML,
            username = username
        )

        assertThrows<UnsupportedOperationException> { underTest.exportScreenings(query) }
        verify(exactly = 0) { adminExportScreeningsQueryService(any()) }
        verify(exactly = 0) { screeningXlsxExportResultMapper.mapToExportResultModel(any(), any(), any(), any()) }
    }
}
