package com.cleevio.cinemax.api.module.outboxevent.event.listener

import com.cleevio.cinemax.api.common.util.toOneLine
import com.cleevio.cinemax.api.module.basket.constant.BasketState
import com.cleevio.cinemax.api.module.basket.service.BasketService
import com.cleevio.cinemax.api.module.basket.service.command.CompleteBasketPaymentCommand
import com.cleevio.cinemax.api.module.outboxevent.constant.OutboxEventState
import com.cleevio.cinemax.api.module.outboxevent.constant.OutboxEventType
import com.cleevio.cinemax.api.module.outboxevent.entity.OutboxEvent
import com.cleevio.cinemax.api.module.outboxevent.event.BasketCancellationReceiptPrintedEvent
import com.cleevio.cinemax.api.module.outboxevent.event.BasketReceiptPrintedEvent
import com.cleevio.cinemax.api.module.outboxevent.service.OutboxEventFinderService
import com.cleevio.cinemax.api.module.outboxevent.service.OutboxEventService
import com.cleevio.cinemax.api.module.outboxevent.service.command.CreateOutboxEventCommand
import com.cleevio.cinemax.api.util.createBasket
import io.mockk.Called
import io.mockk.Runs
import io.mockk.confirmVerified
import io.mockk.every
import io.mockk.just
import io.mockk.mockk
import io.mockk.verifySequence
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.Test
import java.util.UUID

class ReceiptOutboxEventEventListenerTest {

    private val basketService = mockk<BasketService>()
    private val outboxEventService = mockk<OutboxEventService>()
    private val outboxEventFinderService = mockk<OutboxEventFinderService>()

    private val eventListener = ReceiptOutboxEventEventListener(
        basketService,
        outboxEventService,
        outboxEventFinderService
    )

    @AfterEach
    fun afterEach() = confirmVerified(
        basketService,
        outboxEventService,
        outboxEventFinderService
    )

    @Test
    fun `test listenToBasketReceiptPrintedEvent - basket not synced yet - should complete payment and create outbox event`() {
        every { outboxEventFinderService.findAllByEntityId(any()) } returns listOf()
        every { basketService.completeBasketPayment(any()) } returns PAID_BASKET
        every { outboxEventService.createOutboxEvent(any()) } just Runs

        eventListener.listenToBasketReceiptPrintedEvent(
            BasketReceiptPrintedEvent(
                basketId = PAYMENT_IN_PROGRESS_BASKET.id
            )
        )

        verifySequence {
            outboxEventFinderService.findAllByEntityId(PAYMENT_IN_PROGRESS_BASKET.id)
            basketService.completeBasketPayment(CompleteBasketPaymentCommand(PAYMENT_IN_PROGRESS_BASKET.id))
            outboxEventService.createOutboxEvent(
                CreateOutboxEventCommand(
                    entityId = PAYMENT_IN_PROGRESS_BASKET.id,
                    type = OutboxEventType.BASKET_RECEIPT_PRINTED,
                    data = """
                        {
                            "isCancellationReceipt": false
                        }
                    """.toOneLine()
                )
            )
        }
    }

    @Test
    fun `test listenToBasketReceiptPrintedEvent - basket not synced yet but some events exist - should complete payment and create outbox event`() {
        every { outboxEventFinderService.findAllByEntityId(any()) } returns listOf(
            TABLE_BASKET_CREATED_OUTBOX_EVENT,
            TABLE_BASKET_ITEM_UPDATED_OUTBOX_EVENT
        )
        every { basketService.completeBasketPayment(any()) } returns PAID_BASKET
        every { outboxEventService.createOutboxEvent(any()) } just Runs

        eventListener.listenToBasketReceiptPrintedEvent(
            BasketReceiptPrintedEvent(
                basketId = PAYMENT_IN_PROGRESS_BASKET.id
            )
        )

        verifySequence {
            outboxEventFinderService.findAllByEntityId(PAYMENT_IN_PROGRESS_BASKET.id)
            basketService.completeBasketPayment(CompleteBasketPaymentCommand(PAYMENT_IN_PROGRESS_BASKET.id))
            outboxEventService.createOutboxEvent(
                CreateOutboxEventCommand(
                    entityId = PAYMENT_IN_PROGRESS_BASKET.id,
                    type = OutboxEventType.BASKET_RECEIPT_PRINTED,
                    data = """
                        {
                            "isCancellationReceipt": false
                        }
                    """.toOneLine()
                )
            )
        }
    }

    @Test
    fun `test listenToBasketReceiptPrintedEvent - basket sync attempted but failing, some other events exist - should complete payment and create outbox event`() {
        every { outboxEventFinderService.findAllByEntityId(any()) } returns listOf(
            TABLE_BASKET_CREATED_OUTBOX_EVENT,
            TABLE_BASKET_ITEM_UPDATED_OUTBOX_EVENT,
            FAILED_BASKET_RECEIPT_PRINTED_EVENT
        )
        every { basketService.completeBasketPayment(any()) } returns PAID_BASKET
        every { outboxEventService.createOutboxEvent(any()) } just Runs

        eventListener.listenToBasketReceiptPrintedEvent(
            BasketReceiptPrintedEvent(
                basketId = PAYMENT_IN_PROGRESS_BASKET.id
            )
        )

        verifySequence {
            outboxEventFinderService.findAllByEntityId(PAYMENT_IN_PROGRESS_BASKET.id)
            basketService.completeBasketPayment(CompleteBasketPaymentCommand(PAYMENT_IN_PROGRESS_BASKET.id))
            outboxEventService.createOutboxEvent(
                CreateOutboxEventCommand(
                    entityId = PAYMENT_IN_PROGRESS_BASKET.id,
                    type = OutboxEventType.BASKET_RECEIPT_PRINTED,
                    data = """
                        {
                            "isCancellationReceipt": false
                        }
                    """.toOneLine()
                )
            )
        }
    }

    @Test
    fun `test listenToBasketReceiptPrintedEvent - basket already synced - should skip execution`() {
        every { outboxEventFinderService.findAllByEntityId(any()) } returns listOf(
            TABLE_BASKET_CREATED_OUTBOX_EVENT,
            TABLE_BASKET_ITEM_UPDATED_OUTBOX_EVENT,
            SUCCESSFUL_BASKET_RECEIPT_PRINTED_EVENT
        )
        every { basketService.completeBasketPayment(any()) } returns PAID_BASKET
        every { outboxEventService.createOutboxEvent(any()) } just Runs

        eventListener.listenToBasketReceiptPrintedEvent(
            BasketReceiptPrintedEvent(
                basketId = PAYMENT_IN_PROGRESS_BASKET.id
            )
        )

        verifySequence {
            outboxEventFinderService.findAllByEntityId(PAYMENT_IN_PROGRESS_BASKET.id)
            basketService wasNot Called
            outboxEventService wasNot Called
        }
    }

    @Test
    fun `test listenToBasketCancellationReceiptPrintedEvent - basket not synced yet - should create outbox event`() {
        every { outboxEventService.createOutboxEvent(any()) } just Runs

        eventListener.listenToBasketCancellationReceiptPrintedEvent(
            BasketCancellationReceiptPrintedEvent(
                basketId = PAYMENT_IN_PROGRESS_BASKET.id
            )
        )

        verifySequence {
            outboxEventService.createOutboxEvent(
                CreateOutboxEventCommand(
                    entityId = PAYMENT_IN_PROGRESS_BASKET.id,
                    type = OutboxEventType.BASKET_RECEIPT_PRINTED,
                    data = """
                        {
                            "isCancellationReceipt": true
                        }
                    """.toOneLine()
                )
            )
        }
    }
}

private val PAYMENT_IN_PROGRESS_BASKET = createBasket(state = BasketState.PAYMENT_IN_PROGRESS)
private val PAID_BASKET = createBasket(state = BasketState.PAID)
private val TABLE_BASKET_CREATED_OUTBOX_EVENT = OutboxEvent(
    id = UUID.randomUUID(),
    entityId = PAYMENT_IN_PROGRESS_BASKET.id,
    type = OutboxEventType.TABLE_BASKET_CREATED,
    state = OutboxEventState.DONE,
    data = """
        {
            "isCancellationReceipt": false
        }
    """
)
private val TABLE_BASKET_ITEM_UPDATED_OUTBOX_EVENT = OutboxEvent(
    id = UUID.randomUUID(),
    entityId = PAYMENT_IN_PROGRESS_BASKET.id,
    type = OutboxEventType.TABLE_BASKET_ITEM_UPDATED,
    state = OutboxEventState.DONE,
    data = """
        {
            "isCancellationReceipt": false
        }
    """
)
private val SUCCESSFUL_BASKET_RECEIPT_PRINTED_EVENT = OutboxEvent(
    id = UUID.randomUUID(),
    entityId = PAYMENT_IN_PROGRESS_BASKET.id,
    type = OutboxEventType.BASKET_RECEIPT_PRINTED,
    state = OutboxEventState.DONE,
    data = """
        {
            "isCancellationReceipt": false
        }
    """
)
private val FAILED_BASKET_RECEIPT_PRINTED_EVENT = OutboxEvent(
    id = UUID.randomUUID(),
    entityId = PAYMENT_IN_PROGRESS_BASKET.id,
    type = OutboxEventType.BASKET_RECEIPT_PRINTED,
    state = OutboxEventState.FAILED,
    data = """
        {
            "isCancellationReceipt": false
        }
    """
)
