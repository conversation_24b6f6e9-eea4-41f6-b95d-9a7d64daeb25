package com.cleevio.cinemax.api.module.basket.controller

import com.cleevio.cinemax.api.ControllerTest
import com.cleevio.cinemax.api.common.constant.ApiVersion
import com.cleevio.cinemax.api.common.constant.PaymentType
import com.cleevio.cinemax.api.common.util.jsonContent
import com.cleevio.cinemax.api.module.basket.constant.BasketState
import com.cleevio.cinemax.api.module.basket.entity.Basket
import com.cleevio.cinemax.api.module.basket.service.command.DeleteBasketCommand
import com.cleevio.cinemax.api.module.basket.service.command.InitiateBasketPaymentCommand
import com.cleevio.cinemax.api.module.basket.service.command.UpdateBasketPaymentCommand
import com.cleevio.cinemax.api.module.basketitem.constant.BasketItemType
import com.cleevio.cinemax.api.module.basketitem.controller.dto.BasketItemProductResponse
import com.cleevio.cinemax.api.module.basketitem.controller.dto.BasketItemReservationResponse
import com.cleevio.cinemax.api.module.basketitem.controller.dto.BasketItemSeatResponse
import com.cleevio.cinemax.api.module.basketitem.controller.dto.BasketItemTicketResponse
import com.cleevio.cinemax.api.module.pricecategoryitem.constant.PriceCategoryItemNumber
import com.cleevio.cinemax.api.module.product.constant.ProductType
import com.cleevio.cinemax.api.module.reservation.constant.ReservationState
import com.cleevio.cinemax.api.module.ticketprice.constant.SeatSurchargeType
import com.cleevio.cinemax.api.truncatedAndFormatted
import com.cleevio.cinemax.api.util.createBasket
import com.cleevio.cinemax.api.util.createBasketItem
import com.cleevio.cinemax.api.util.createProduct
import com.cleevio.cinemax.api.util.createReservation
import com.cleevio.cinemax.api.util.createTicket
import com.cleevio.cinemax.api.util.createTicketPrice
import com.cleevio.cinemax.api.util.mapInputItemsToInitBasketCommand
import com.cleevio.cinemax.api.util.mapToBasketItemRequest
import com.cleevio.cinemax.api.util.mapToBasketItemResponse
import com.cleevio.cinemax.api.util.mapToBasketItemTicketPriceResponse
import com.cleevio.cinemax.api.util.mapToBasketResponse
import com.cleevio.cinemax.api.util.mapToInitBasketCommand
import com.cleevio.cinemax.api.util.mockAccessToken
import io.mockk.Runs
import io.mockk.every
import io.mockk.just
import io.mockk.verifySequence
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest
import org.springframework.http.HttpHeaders
import org.springframework.http.MediaType
import org.springframework.test.web.servlet.MockMvc
import org.springframework.test.web.servlet.delete
import org.springframework.test.web.servlet.get
import org.springframework.test.web.servlet.post
import org.springframework.test.web.servlet.put
import java.math.BigDecimal
import java.util.UUID

@WebMvcTest(BasketController::class)
class BasketControllerTest @Autowired constructor(
    private val mvc: MockMvc,
) : ControllerTest() {

    @Test
    fun `test initBasket, missing accept header, should fall back to latest version content type`() {
        every { basketService.initBasket(any()) } returns BASKET
        every { basketResponseMapper.mapSingle(any()) } returns BASKET_RESPONSE

        mvc.post(INIT_BASKET_PATH) {
            header(HttpHeaders.ACCEPT, ApiVersion.VERSION_1_JSON)
            header(HttpHeaders.AUTHORIZATION, mockAccessToken())
            contentType = MediaType.APPLICATION_JSON
            content = """
                {
                  "items": [
                    {
                      "type": "${BASKET_ITEM_REQUEST_1.type}",
                      "quantity": ${BASKET_ITEM_REQUEST_1.quantity},
                      "ticket": {
                        "reservation": {
                          "seatId": "${BASKET_ITEM_REQUEST_1.ticket?.reservation?.seatId}"
                        },
                        "ticketPrice": {
                          "priceCategoryItemNumber": "${BASKET_ITEM_REQUEST_1.ticket?.ticketPrice?.priceCategoryItemNumber}"
                        },
                        "screeningId": "$SCREENING_1_ID",
                        "isGroupTicket": false
                      }
                    }
                  ]
                }
            """.trimIndent()
        }.andExpect {
            content { contentType(ApiVersion.VERSION_1_JSON) }
            status { isOk() }
        }

        verifySequence {
            basketService.initBasket(any())
            basketResponseMapper.mapSingle(any())
        }
    }

    @Test
    fun `test initBasket, missing content type, should return 415`() {
        mvc.post(INIT_BASKET_PATH) {
            header(HttpHeaders.ACCEPT, ApiVersion.VERSION_1_JSON)
            header(HttpHeaders.AUTHORIZATION, mockAccessToken())
            content = """{}"""
        }.andExpect {
            status { isUnsupportedMediaType() }
        }
    }

    @Test
    fun `test initBasket, invalid content type, should return 415`() {
        mvc.post(INIT_BASKET_PATH) {
            header(HttpHeaders.ACCEPT, ApiVersion.VERSION_1_JSON)
            header(HttpHeaders.AUTHORIZATION, mockAccessToken())
            contentType = MediaType.APPLICATION_PDF
            content = """{}"""
        }.andExpect {
            status { isUnsupportedMediaType() }
        }
    }

    @Test
    fun `test initBasket, should serialize and deserialize correctly`() {
        every { basketService.initBasket(any()) } returns BASKET
        every { basketResponseMapper.mapSingle(any()) } returns BASKET_RESPONSE

        mvc.post(INIT_BASKET_PATH) {
            contentType = MediaType.APPLICATION_JSON
            header(HttpHeaders.ACCEPT, ApiVersion.VERSION_1_JSON)
            header(HttpHeaders.AUTHORIZATION, mockAccessToken())
            content = """
                {
                  "items": [
                    {
                      "type": "${BASKET_ITEM_1.type}",
                      "price": ${BASKET_ITEM_1.price},
                      "quantity": ${BASKET_ITEM_1.quantity},
                      "ticket": {
                        "reservation": {
                          "seatId": "${BASKET_ITEM_REQUEST_1.ticket?.reservation?.seatId}"
                        },
                        "ticketPrice": {
                          "priceCategoryItemNumber": "${BASKET_ITEM_REQUEST_1.ticket?.ticketPrice?.priceCategoryItemNumber}"
                        },
                        "screeningId": "$SCREENING_1_ID",
                        "isGroupTicket": false
                      }
                    },
                    {
                      "type": "${BASKET_ITEM_2.type}",
                      "price": ${BASKET_ITEM_2.price},
                      "quantity": ${BASKET_ITEM_2.quantity},
                      "ticket": {
                        "reservation": {
                          "seatId": "${BASKET_ITEM_REQUEST_2.ticket?.reservation?.seatId}"
                        },
                        "ticketPrice": {
                          "priceCategoryItemNumber": "${BASKET_ITEM_REQUEST_2.ticket?.ticketPrice?.priceCategoryItemNumber}"
                        },
                        "screeningId": "$SCREENING_1_ID",
                        "isGroupTicket": false
                      }
                    },
                    {
                      "type": "${BASKET_ITEM_3.type}",
                      "price": ${BASKET_ITEM_3.price},
                      "quantity": ${BASKET_ITEM_3.quantity},
                      "product": {
                          "productId": "${BASKET_ITEM_REQUEST_3.product?.productId}"
                      }
                    }
                  ]
                }
            """.trimIndent()
        }.andExpect {
            status { isOk() }
            content { contentType(ApiVersion.VERSION_1_JSON) }
            jsonContent(
                """
                {
                  "id": "${BASKET.id}",
                  "createdAt": "${BASKET.createdAt.truncatedAndFormatted()}",
                  "state": "${BASKET.state}",
                  "totalPrice": ${BASKET.totalPrice},
                  "items": [
                    {
                      "id": "${BASKET_ITEM_1.id}",
                      "type": "${BASKET_ITEM_1.type}",
                      "price": ${BASKET_ITEM_1.price},
                      "quantity": ${BASKET_ITEM_1.quantity},
                      "ticket": {
                        "ticketPrice": {
                          "id": "${TICKET_PRICE_1.id}",
                          "basePrice": ${TICKET_PRICE_1.basePrice},
                          "basePriceItemNumber": "${TICKET_PRICE_1.basePriceItemNumber}",
                          "totalPrice": ${TICKET_PRICE_1.totalPrice}
                        },
                        "reservation": {
                          "seat": {
                            "id": "${RESERVATION_1.seatId}",
                            "row": "${RESERVATION_RESPONSE_1.seat.row}",
                            "number": "${RESERVATION_RESPONSE_1.seat.number}"
                          },
                          "state": "${RESERVATION_1.state}"
                        },
                        "isGroupTicket": false
                      }
                    },
                    {
                      "id": "${BASKET_ITEM_2.id}",
                      "type": "${BASKET_ITEM_2.type}",
                      "price": ${BASKET_ITEM_2.price},
                      "quantity": ${BASKET_ITEM_2.quantity},
                      "ticket": {
                        "ticketPrice": {
                          "id": "${TICKET_PRICE_2.id}",
                          "basePrice": ${TICKET_PRICE_2.basePrice},
                          "basePriceItemNumber": "${TICKET_PRICE_2.basePriceItemNumber}",
                          "totalPrice": ${TICKET_PRICE_2.totalPrice}
                        },
                        "reservation": {
                          "seat": {
                            "id": "${RESERVATION_2.seatId}",
                            "row": "${RESERVATION_RESPONSE_2.seat.row}",
                            "number": "${RESERVATION_RESPONSE_2.seat.number}"
                          },
                          "state": "${RESERVATION_2.state}"
                        },
                        "isGroupTicket": false
                      }
                    },
                    {
                      "id": "${BASKET_ITEM_3.id}",
                      "type": "${BASKET_ITEM_3.type}",
                      "price": ${BASKET_ITEM_3.price},
                      "quantity": ${BASKET_ITEM_3.quantity},
                      "product": {
                        "id": "${BASKET_ITEM_3.productId}",
                        "title": "${PRODUCT_1.title}",
                        "price": ${PRODUCT_1.price}
                      }
                    }
                  ]
                }
              """
            )
        }

        verifySequence {
            basketService.initBasket(
                mapToInitBasketCommand(
                    listOf(
                        BASKET_ITEM_REQUEST_1,
                        BASKET_ITEM_REQUEST_2,
                        BASKET_ITEM_REQUEST_3
                    )
                )
            )
            basketResponseMapper.mapSingle(BASKET)
        }
    }

    @Test
    fun `test initBasket, empty basket, should serialize and deserialize correctly`() {
        every { basketService.initBasket(any()) } returns BASKET_EMPTY
        every { basketResponseMapper.mapSingle(any()) } returns BASKET_EMPTY_RESPONSE

        mvc.post(INIT_BASKET_PATH) {
            contentType = MediaType.APPLICATION_JSON
            header(HttpHeaders.ACCEPT, ApiVersion.VERSION_1_JSON)
            header(HttpHeaders.AUTHORIZATION, mockAccessToken())
            content = """
                {
                  "items": []
                }
            """.trimIndent()
        }.andExpect {
            status { isOk() }
            content { contentType(ApiVersion.VERSION_1_JSON) }
            jsonContent(
                """
                {
                  "id": "${BASKET_EMPTY.id}",
                  "state": "${BASKET_EMPTY.state}",
                  "totalPrice": ${BASKET_EMPTY.totalPrice},
                  "items": []
                }
              """
            )
        }

        verifySequence {
            basketService.initBasket(mapInputItemsToInitBasketCommand(listOf()))
            basketResponseMapper.mapSingle(BASKET_EMPTY)
        }
    }

    @Test
    fun `test deleteBasket, should serialize correctly`() {
        every { basketService.deleteBasket(any()) } just Runs

        mvc.delete(GET_AND_DELETE_BASKET_PATH(BASKET.id)) {
            header(HttpHeaders.ACCEPT, ApiVersion.VERSION_1_JSON)
            header(HttpHeaders.AUTHORIZATION, mockAccessToken())
        }.andExpect {
            status { isNoContent() }
        }

        verifySequence {
            basketService.deleteBasket(DeleteBasketCommand(BASKET.id))
        }
    }

    @Test
    fun `test initiateBasketPayment, should serialize and deserialize correctly`() {
        every { basketService.initiateBasketPayment(any()) } returns BASKET_PAYMENT_IN_PROGRESS
        every { basketResponseMapper.mapSingle(any()) } returns BASKET_PAYMENT_IN_PROGRESS_RESPONSE

        mvc.post(PAYMENT_BASKET_PATH(BASKET_PAYMENT_IN_PROGRESS.id, PaymentType.CASHLESS, POS_CONFIGURATION_ID)) {
            header(HttpHeaders.ACCEPT, ApiVersion.VERSION_1_JSON)
            header(HttpHeaders.AUTHORIZATION, mockAccessToken())
        }.andExpect {
            status { isOk() }
            content { contentType(ApiVersion.VERSION_1_JSON) }
            jsonContent(
                """
                {
                  "id": "${BASKET_PAYMENT_IN_PROGRESS.id}",
                  "createdAt": "${BASKET_PAYMENT_IN_PROGRESS.createdAt.truncatedAndFormatted()}",
                  "state": "${BASKET_PAYMENT_IN_PROGRESS.state}",
                  "totalPrice": ${BASKET_PAYMENT_IN_PROGRESS.totalPrice},
                  "items": [
                    {
                      "id": "${BASKET_ITEM_1.id}",
                      "type": "${BASKET_ITEM_1.type}",
                      "price": ${BASKET_ITEM_1.price},
                      "quantity": ${BASKET_ITEM_1.quantity},
                      "ticket": {
                        "ticketPrice": {
                          "id": "${TICKET_PRICE_1.id}",
                          "basePrice": ${TICKET_PRICE_1.basePrice},
                          "basePriceItemNumber": "${TICKET_PRICE_1.basePriceItemNumber}",
                          "totalPrice": ${TICKET_PRICE_1.totalPrice}
                        },
                        "reservation": {
                          "seat": {
                            "id": "${RESERVATION_1.seatId}",
                            "row": "${RESERVATION_RESPONSE_1.seat.row}",
                            "number": "${RESERVATION_RESPONSE_1.seat.number}"
                          },
                          "state": "${RESERVATION_RESPONSE_1_UNAVAILABLE.state}"
                        },
                        "isGroupTicket": false
                      }
                    },
                    {
                      "id": "${BASKET_ITEM_2.id}",
                      "type": "${BASKET_ITEM_2.type}",
                      "price": ${BASKET_ITEM_2.price},
                      "quantity": ${BASKET_ITEM_2.quantity},
                      "ticket": {
                        "ticketPrice": {
                          "id": "${TICKET_PRICE_2.id}",
                          "basePrice": ${TICKET_PRICE_2.basePrice},
                          "basePriceItemNumber": "${TICKET_PRICE_2.basePriceItemNumber}",
                          "totalPrice": ${TICKET_PRICE_2.totalPrice}
                        },
                        "reservation": {
                          "seat": {
                            "id": "${RESERVATION_2.seatId}",
                            "row": "${RESERVATION_RESPONSE_2.seat.row}",
                            "number": "${RESERVATION_RESPONSE_2.seat.number}"
                          },
                          "state": "${RESERVATION_RESPONSE_2_UNAVAILABLE.state}"
                        },
                        "isGroupTicket": false
                      }
                    },
                    {
                      "id": "${BASKET_ITEM_3.id}",
                      "type": "${BASKET_ITEM_3.type}",
                      "price": ${BASKET_ITEM_3.price},
                      "quantity": ${BASKET_ITEM_3.quantity},
                      "product": {
                        "id": "${BASKET_ITEM_3.productId}",
                        "title": "${PRODUCT_1.title}",
                        "price": ${PRODUCT_1.price}
                      }
                    }
                  ]
                }
              """
            )
        }

        verifySequence {
            basketService.initiateBasketPayment(
                InitiateBasketPaymentCommand(
                    basketId = BASKET_PAYMENT_IN_PROGRESS.id,
                    posConfigurationId = POS_CONFIGURATION_ID,
                    paymentType = PaymentType.CASHLESS
                )
            )
            basketResponseMapper.mapSingle(BASKET_PAYMENT_IN_PROGRESS)
        }
    }

    @Test
    fun `test updateBasketPayment, should serialize and deserialize correctly`() {
        every { basketService.updateBasketPayment(any()) } returns BASKET
        every { basketResponseMapper.mapSingle(any()) } returns BASKET_PAYMENT_IN_PROGRESS_RESPONSE

        mvc.put(UPDATE_PAYMENT_BASKET_PATH(BASKET.id, PaymentType.CASHLESS)) {
            header(HttpHeaders.ACCEPT, ApiVersion.VERSION_1_JSON)
            header(HttpHeaders.AUTHORIZATION, mockAccessToken())
        }.andExpect {
            status { isOk() }
            content { contentType(ApiVersion.VERSION_1_JSON) }
            jsonContent(
                """
                {
                  "id": "${BASKET.id}",
                  "state": "${BASKET_PAYMENT_IN_PROGRESS.state}",
                  "totalPrice": ${BASKET.totalPrice},
                  "items": [
                    {
                      "id": "${BASKET_ITEM_1.id}",
                      "type": "${BASKET_ITEM_1.type}",
                      "price": ${BASKET_ITEM_1.price},
                      "quantity": ${BASKET_ITEM_1.quantity},
                      "ticket": {
                        "ticketPrice": {
                          "id": "${TICKET_PRICE_1.id}",
                          "basePrice": ${TICKET_PRICE_1.basePrice},
                          "basePriceItemNumber": "${TICKET_PRICE_1.basePriceItemNumber}",
                          "totalPrice": ${TICKET_PRICE_1.totalPrice}
                        },
                        "reservation": {
                          "seat": {
                            "id": "${RESERVATION_1.seatId}",
                            "row": "${RESERVATION_RESPONSE_1.seat.row}",
                            "number": "${RESERVATION_RESPONSE_1.seat.number}"
                          },
                          "state": "${RESERVATION_RESPONSE_1_UNAVAILABLE.state}"
                        },
                        "isGroupTicket": false
                      }
                    },
                    {
                      "id": "${BASKET_ITEM_2.id}",
                      "type": "${BASKET_ITEM_2.type}",
                      "price": ${BASKET_ITEM_2.price},
                      "quantity": ${BASKET_ITEM_2.quantity},
                      "ticket": {
                        "ticketPrice": {
                          "id": "${TICKET_PRICE_2.id}",
                          "basePrice": ${TICKET_PRICE_2.basePrice},
                          "basePriceItemNumber": "${TICKET_PRICE_2.basePriceItemNumber}",
                          "totalPrice": ${TICKET_PRICE_2.totalPrice}
                        },
                        "reservation": {
                          "seat": {
                            "id": "${RESERVATION_2.seatId}",
                            "row": "${RESERVATION_RESPONSE_2.seat.row}",
                            "number": "${RESERVATION_RESPONSE_2.seat.number}"
                          },
                          "state": "${RESERVATION_RESPONSE_2_UNAVAILABLE.state}"
                        },
                        "isGroupTicket": false
                      }
                    },
                    {
                      "id": "${BASKET_ITEM_3.id}",
                      "type": "${BASKET_ITEM_3.type}",
                      "price": ${BASKET_ITEM_3.price},
                      "quantity": ${BASKET_ITEM_3.quantity},
                      "product": {
                        "id": "${BASKET_ITEM_3.productId}",
                        "title": "${PRODUCT_1.title}",
                        "price": ${PRODUCT_1.price}
                      }
                    }
                  ]
                }
              """
            )
        }

        verifySequence {
            basketService.updateBasketPayment(
                UpdateBasketPaymentCommand(
                    basketId = BASKET.id,
                    paymentType = PaymentType.CASHLESS
                )
            )
            basketResponseMapper.mapSingle(BASKET_PAYMENT_IN_PROGRESS)
        }
    }

    @Test
    fun `getBasket, should serialize and deserialize correctly`() {
        every { basketJpaFinderService.getNonDeletedById(any()) } returns BASKET
        every { basketResponseMapper.mapSingle(any()) } returns BASKET_RESPONSE

        mvc.get(GET_AND_DELETE_BASKET_PATH(BASKET.id)) {
            header(HttpHeaders.ACCEPT, ApiVersion.VERSION_1_JSON)
            header(HttpHeaders.AUTHORIZATION, mockAccessToken())
        }.andExpect {
            status { isOk() }
            content { contentType(ApiVersion.VERSION_1_JSON) }
            jsonContent(
                """
                {
                  "id": "${BASKET.id}",
                  "createdAt": "${BASKET.createdAt.truncatedAndFormatted()}",
                  "state": "${BASKET.state}",
                  "totalPrice": ${BASKET.totalPrice},
                  "items": [
                    {
                      "id": "${BASKET_ITEM_1.id}",
                      "type": "${BASKET_ITEM_1.type}",
                      "price": ${BASKET_ITEM_1.price},
                      "quantity": ${BASKET_ITEM_1.quantity},
                      "ticket": {
                        "ticketPrice": {
                          "id": "${TICKET_PRICE_1.id}",
                          "basePrice": ${TICKET_PRICE_1.basePrice},
                          "basePriceItemNumber": "${TICKET_PRICE_1.basePriceItemNumber}",
                          "totalPrice": ${TICKET_PRICE_1.totalPrice}
                        },
                        "reservation": {
                          "seat": {
                            "id": "${RESERVATION_1.seatId}",
                            "row": "${RESERVATION_RESPONSE_1.seat.row}",
                            "number": "${RESERVATION_RESPONSE_1.seat.number}"
                          },
                          "state": "${RESERVATION_1.state}"
                        },
                        "isGroupTicket": false
                      }
                    },
                    {
                      "id": "${BASKET_ITEM_2.id}",
                      "type": "${BASKET_ITEM_2.type}",
                      "price": ${BASKET_ITEM_2.price},
                      "quantity": ${BASKET_ITEM_2.quantity},
                      "ticket": {
                        "ticketPrice": {
                          "id": "${TICKET_PRICE_2.id}",
                          "basePrice": ${TICKET_PRICE_2.basePrice},
                          "basePriceItemNumber": "${TICKET_PRICE_2.basePriceItemNumber}",
                          "totalPrice": ${TICKET_PRICE_2.totalPrice}
                        },
                        "reservation": {
                          "seat": {
                            "id": "${RESERVATION_2.seatId}",
                            "row": "${RESERVATION_RESPONSE_2.seat.row}",
                            "number": "${RESERVATION_RESPONSE_2.seat.number}"
                          },
                          "state": "${RESERVATION_2.state}"
                        },
                        "isGroupTicket": false
                      }
                    },
                    {
                      "id": "${BASKET_ITEM_3.id}",
                      "type": "${BASKET_ITEM_3.type}",
                      "price": ${BASKET_ITEM_3.price},
                      "quantity": ${BASKET_ITEM_3.quantity},
                      "product": {
                        "id": "${BASKET_ITEM_3.productId}",
                        "title": "${PRODUCT_1.title}",
                        "price": ${PRODUCT_1.price}
                      }
                    }
                  ]
                }
              """
            )
        }

        verifySequence {
            basketJpaFinderService.getNonDeletedById(BASKET.id)
            basketResponseMapper.mapSingle(BASKET)
        }
    }
}

private const val INIT_BASKET_PATH = "/pos-app/baskets/init"
private val GET_AND_DELETE_BASKET_PATH: (UUID) -> String = { basketId: UUID -> "/pos-app/baskets/$basketId" }
private val PAYMENT_BASKET_PATH: (UUID, PaymentType, UUID) -> String = { basketId, paymentType, posConfigurationId ->
    "/pos-app/baskets/$basketId/payment/$paymentType?posConfigurationId=$posConfigurationId"
}
private val UPDATE_PAYMENT_BASKET_PATH: (UUID, PaymentType) -> String = { basketId, paymentType ->
    "/pos-app/baskets/$basketId/payment/$paymentType"
}

private val POS_CONFIGURATION_ID = UUID.randomUUID()
private val SCREENING_1_ID = UUID.randomUUID()
private val SEAT_1_ID = UUID.randomUUID()
private val SEAT_2_ID = UUID.randomUUID()

private val BASKET = createBasket(
    totalPrice = BigDecimal(12.0),
    state = BasketState.OPEN
)
private val BASKET_PAYMENT_IN_PROGRESS = Basket(
    id = BASKET.id,
    tableId = BASKET.tableId,
    totalPrice = BASKET.totalPrice,
    state = BasketState.PAYMENT_IN_PROGRESS
)
private val BASKET_EMPTY = createBasket(
    totalPrice = BigDecimal(0),
    state = BasketState.OPEN
)
private val RESERVATION_1 = createReservation(
    screeningId = BASKET.id,
    seatId = SEAT_1_ID
)
private val RESERVATION_2 = createReservation(
    screeningId = BASKET.id,
    seatId = SEAT_2_ID
)
private val PRODUCT_1 = createProduct(
    originalId = 1,
    productCategoryId = UUID.randomUUID(),
    title = "Coca Cola 0.33 l",
    order = 23,
    type = ProductType.PRODUCT,
    price = BigDecimal.valueOf(3.5)
)
private val PRODUCT_RESPONSE_1 = BasketItemProductResponse(
    id = PRODUCT_1.id,
    title = PRODUCT_1.title,
    price = PRODUCT_1.price
)
private val RESERVATION_RESPONSE_1 = BasketItemReservationResponse(
    seat = BasketItemSeatResponse(SEAT_1_ID, "1", "1"),
    state = ReservationState.RESERVED
)
private val RESERVATION_RESPONSE_2 = BasketItemReservationResponse(
    seat = BasketItemSeatResponse(SEAT_2_ID, "1", "1"),
    state = ReservationState.RESERVED
)
private val RESERVATION_RESPONSE_1_UNAVAILABLE = BasketItemReservationResponse(
    seat = BasketItemSeatResponse(SEAT_1_ID, "1", "1"),
    state = ReservationState.UNAVAILABLE
)
private val RESERVATION_RESPONSE_2_UNAVAILABLE = BasketItemReservationResponse(
    seat = BasketItemSeatResponse(SEAT_2_ID, "1", "1"),
    state = ReservationState.UNAVAILABLE
)
private val TICKET_PRICE_1 = createTicketPrice(
    screeningId = SCREENING_1_ID,
    seatId = SEAT_1_ID,
    basePrice = BigDecimal.valueOf(5),
    basePriceItemNumber = PriceCategoryItemNumber.PRICE_1,
    seatSurchargeType = SeatSurchargeType.VIP,
    seatSurcharge = BigDecimal.ONE,
    totalPrice = BigDecimal.valueOf(6)
)
private val TICKET_PRICE_2 = createTicketPrice(
    screeningId = SCREENING_1_ID,
    seatId = SEAT_2_ID,
    basePrice = BigDecimal.valueOf(5),
    basePriceItemNumber = PriceCategoryItemNumber.PRICE_1,
    totalPrice = BigDecimal.valueOf(5)
)
private val TICKET_1 = createTicket(
    screeningId = SCREENING_1_ID,
    reservationId = RESERVATION_1.id,
    ticketPriceId = TICKET_PRICE_1.id
)
private val TICKET_2 = createTicket(
    screeningId = SCREENING_1_ID,
    reservationId = RESERVATION_2.id,
    ticketPriceId = TICKET_PRICE_2.id
)
private val BASKET_ITEM_TICKET_RESPONSE_1 = BasketItemTicketResponse(
    ticketPrice = mapToBasketItemTicketPriceResponse(TICKET_PRICE_1),
    reservation = RESERVATION_RESPONSE_1,
    screeningId = SCREENING_1_ID,
    isGroupTicket = false
)
private val BASKET_ITEM_TICKET_RESPONSE_2 = BasketItemTicketResponse(
    ticketPrice = mapToBasketItemTicketPriceResponse(TICKET_PRICE_2),
    reservation = RESERVATION_RESPONSE_2,
    screeningId = SCREENING_1_ID,
    isGroupTicket = false
)
private val BASKET_ITEM_TICKET_RESPONSE_1_PAYMENT_IN_PROGRESS = BasketItemTicketResponse(
    ticketPrice = mapToBasketItemTicketPriceResponse(TICKET_PRICE_1),
    reservation = RESERVATION_RESPONSE_1_UNAVAILABLE,
    screeningId = SCREENING_1_ID,
    isGroupTicket = false
)
private val BASKET_ITEM_TICKET_RESPONSE_2_PAYMENT_IN_PROGRESS = BasketItemTicketResponse(
    ticketPrice = mapToBasketItemTicketPriceResponse(TICKET_PRICE_2),
    reservation = RESERVATION_RESPONSE_2_UNAVAILABLE,
    screeningId = SCREENING_1_ID,
    isGroupTicket = false
)
private val BASKET_ITEM_1 = createBasketItem(
    basketId = BASKET.id,
    ticketId = TICKET_1.id,
    type = BasketItemType.TICKET,
    price = BigDecimal(8.4),
    quantity = 1
)
private val BASKET_ITEM_2 = createBasketItem(
    basketId = BASKET.id,
    ticketId = TICKET_2.id,
    type = BasketItemType.TICKET,
    price = BigDecimal(4.2),
    quantity = 1
)
private val BASKET_ITEM_3 = createBasketItem(
    basketId = BASKET.id,
    productId = PRODUCT_1.id,
    type = BasketItemType.PRODUCT,
    price = BigDecimal(5.6),
    quantity = 1
)
private val BASKET_ITEM_REQUEST_1 = mapToBasketItemRequest(
    basketItem = BASKET_ITEM_1,
    ticketPrice = TICKET_PRICE_1,
    screeningId = SCREENING_1_ID
)
private val BASKET_ITEM_REQUEST_2 = mapToBasketItemRequest(
    basketItem = BASKET_ITEM_2,
    ticketPrice = TICKET_PRICE_2,
    screeningId = SCREENING_1_ID
)
private val BASKET_ITEM_REQUEST_3 = mapToBasketItemRequest(
    basketItem = BASKET_ITEM_3,
    productId = PRODUCT_1.id,
    screeningId = SCREENING_1_ID
)
private val BASKET_ITEM_RESPONSE_1 = mapToBasketItemResponse(
    basketItem = BASKET_ITEM_1,
    ticketResponse = BASKET_ITEM_TICKET_RESPONSE_1
)
private val BASKET_ITEM_RESPONSE_2 = mapToBasketItemResponse(
    basketItem = BASKET_ITEM_2,
    ticketResponse = BASKET_ITEM_TICKET_RESPONSE_2
)
private val BASKET_ITEM_RESPONSE_3 = mapToBasketItemResponse(
    basketItem = BASKET_ITEM_3,
    productResponse = PRODUCT_RESPONSE_1
)
private val BASKET_ITEM_RESPONSE_1_PAYMENT_IN_PROGRESS = mapToBasketItemResponse(
    basketItem = BASKET_ITEM_1,
    ticketResponse = BASKET_ITEM_TICKET_RESPONSE_1_PAYMENT_IN_PROGRESS
)
private val BASKET_ITEM_RESPONSE_2_PAYMENT_IN_PROGRESS = mapToBasketItemResponse(
    basketItem = BASKET_ITEM_2,
    ticketResponse = BASKET_ITEM_TICKET_RESPONSE_2_PAYMENT_IN_PROGRESS
)
private val BASKET_ITEM_RESPONSE_3_PAYMENT_IN_PROGRESS = mapToBasketItemResponse(
    basketItem = BASKET_ITEM_3,
    productResponse = PRODUCT_RESPONSE_1
)
private val BASKET_RESPONSE = mapToBasketResponse(
    basket = BASKET,
    itemResponses = listOf(
        BASKET_ITEM_RESPONSE_1,
        BASKET_ITEM_RESPONSE_2,
        BASKET_ITEM_RESPONSE_3
    )
)
private val BASKET_PAYMENT_IN_PROGRESS_RESPONSE = mapToBasketResponse(
    basket = BASKET_PAYMENT_IN_PROGRESS,
    itemResponses = listOf(
        BASKET_ITEM_RESPONSE_1_PAYMENT_IN_PROGRESS,
        BASKET_ITEM_RESPONSE_2_PAYMENT_IN_PROGRESS,
        BASKET_ITEM_RESPONSE_3_PAYMENT_IN_PROGRESS
    )
)
private val BASKET_EMPTY_RESPONSE = mapToBasketResponse(
    basket = BASKET_EMPTY,
    itemResponses = listOf()
)
