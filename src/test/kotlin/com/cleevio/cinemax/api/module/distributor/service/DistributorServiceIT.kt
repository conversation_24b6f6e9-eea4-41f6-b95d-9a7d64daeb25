package com.cleevio.cinemax.api.module.distributor.service

import com.cleevio.cinemax.api.DatabaseCleanup
import com.cleevio.cinemax.api.IntegrationTest
import com.cleevio.cinemax.api.module.distributor.event.AdminDistributorCreatedOrUpdatedEvent
import com.cleevio.cinemax.api.module.distributor.event.AdminDistributorDeletedEvent
import com.cleevio.cinemax.api.module.distributor.event.DistributorCreatedOrUpdatedEvent
import com.cleevio.cinemax.api.module.distributor.event.toMessagingDeleteEvent
import com.cleevio.cinemax.api.module.distributor.event.toMessagingEvent
import com.cleevio.cinemax.api.module.distributor.exception.DistributorNotFoundException
import com.cleevio.cinemax.api.module.distributor.service.command.DeleteDistributorCommand
import com.cleevio.cinemax.api.module.distributor.service.command.MessagingDeleteDistributorCommand
import com.cleevio.cinemax.api.module.distributor.service.command.UpdateDistributorOriginalIdCommand
import com.cleevio.cinemax.api.util.assertCommandToDistributorMapping
import com.cleevio.cinemax.api.util.assertEqualsTruncated
import com.cleevio.cinemax.api.util.assertMessagingCommandToDistributorMapping
import com.cleevio.cinemax.api.util.createDistributor
import com.cleevio.cinemax.api.util.mapToCreateOrUpdateDistributorCommand
import com.cleevio.cinemax.api.util.mapToMessagingCreateOrUpdateDistributorCommand
import com.cleevio.cinemax.api.util.toUUID
import io.mockk.Runs
import io.mockk.every
import io.mockk.just
import io.mockk.runs
import io.mockk.verify
import jakarta.validation.ConstraintViolationException
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertDoesNotThrow
import org.junit.jupiter.api.assertThrows
import org.springframework.beans.factory.annotation.Autowired
import java.util.Optional
import kotlin.test.assertEquals
import kotlin.test.assertNotNull
import kotlin.test.assertNull
import kotlin.test.assertTrue

class DistributorServiceIT @Autowired constructor(
    private val underTest: DistributorService,
    private val distributorRepository: DistributorRepository,
) : IntegrationTest(DatabaseCleanup.AFTER_EACH) {

    @Test
    fun `test syncCreateOrUpdateDistributor - shouldn't throw if distributor original code already exists`() {
        createDistributor(originalId = 1, code = "DI_1").also { distributorRepository.save(it) }
        val distributor2 = createDistributor(originalId = 2, code = "DI_1")
        val createCommand = mapToCreateOrUpdateDistributorCommand(distributor2).copy(id = null)

        assertDoesNotThrow {
            underTest.syncCreateOrUpdateDistributor(createCommand)
        }

        val updatedDistributor = distributorRepository.findByCode("DI_1")!!
        assertEquals(2, updatedDistributor.originalId)
    }

    @Test
    fun `test syncCreateOrUpdateDistributor - does not exist by code - should create distributor`() {
        val distributor = createDistributor(code = "AB")
        val command = mapToCreateOrUpdateDistributorCommand(distributor)

        assertEquals(0, distributorRepository.findAll().size)

        underTest.syncCreateOrUpdateDistributor(command)

        assertEquals(1, distributorRepository.findAll().size)

        distributorRepository.findAll().first { it.originalId == distributor.originalId }.let {
            assertNotNull(it.id)
            assertCommandToDistributorMapping(command, it)
            assertNotNull(it.createdAt)
            assertEqualsTruncated(it.createdAt, it.updatedAt)
            assertNull(it.deletedAt)
        }
    }

    @Test
    fun `test syncCreateOrUpdateDistributor - exists by code - should update distributor`() {
        val distributor = createDistributor().also { distributorRepository.save(it) }
        assertEquals(1, distributorRepository.findAll().size)

        val updateCommand = mapToCreateOrUpdateDistributorCommand(distributor).copy(
            disfilmCode = Optional.of("DF1"),
            title = "Worst movies ever, s.r.o.",
            addressStreet = Optional.of("Seriálová 58"),
            addressCity = Optional.of("Bratislava"),
            addressPostCode = Optional.of("841 01"),
            contactName1 = Optional.of("Peter"),
            contactName2 = Optional.of("Frank"),
            contactName3 = Optional.of("Anatol"),
            contactPhone1 = Optional.of("+************"),
            contactPhone2 = Optional.of("+************"),
            contactPhone3 = Optional.of("*********"),
            contactEmails = Optional.of(setOf("<EMAIL>", "<EMAIL>")),
            bankName = Optional.of("Tatra banka, a.s."),
            bankAccount = Optional.of("458745/0800"),
            idNumber = Optional.of("********"),
            taxIdNumber = Optional.of("SK2025916874"),
            vatRate = Optional.of(5),
            note = Optional.of("Worst note ever")
        )

        underTest.syncCreateOrUpdateDistributor(updateCommand)
        assertEquals(1, distributorRepository.findAll().size)

        distributorRepository.findAll()[0].let {
            assertEquals(updateCommand.id, it.id)
            assertCommandToDistributorMapping(expected = updateCommand, actual = it, expectedCode = distributor.code)
            assertEqualsTruncated(distributor.createdAt, it.createdAt)
            assertTrue(it.updatedAt.isAfter(distributor.updatedAt))
            assertNull(it.deletedAt)
        }
    }

    @Test
    fun `test syncCreateOrUpdateDistributor - exists deleted by code - should not update distributor`() {
        every { applicationEventPublisherMock.publishEvent(any<AdminDistributorDeletedEvent>()) } just Runs

        val distributor = createDistributor()
        val command = mapToCreateOrUpdateDistributorCommand(distributor)
        underTest.syncCreateOrUpdateDistributor(command)

        assertEquals(1, distributorRepository.findAll().size)
        assertCommandToDistributorMapping(command, distributorRepository.findAll()[0])

        underTest.deleteDistributor(DeleteDistributorCommand(distributor.id))

        val deletedDistributor = distributorRepository.findByOriginalId(distributor.originalId!!)
        assertNotNull(deletedDistributor)
        assertTrue(deletedDistributor.isDeleted())

        val updateCommand = mapToCreateOrUpdateDistributorCommand(distributor).copy(
            title = "Worst movies ever, s.r.o.",
            addressStreet = Optional.of("Seriálová 58"),
            addressCity = Optional.of("Bratislava"),
            addressPostCode = Optional.of("841 01"),
            contactName1 = Optional.of("Peter"),
            contactName2 = Optional.of("Frank"),
            contactName3 = Optional.of("Anatol"),
            contactPhone1 = Optional.of("+************"),
            contactPhone2 = Optional.of("+************"),
            contactPhone3 = Optional.of("*********"),
            contactEmails = Optional.of(setOf("<EMAIL>", "<EMAIL>")),
            bankName = Optional.of("Tatra banka, a.s."),
            bankAccount = Optional.of("458745/0800"),
            idNumber = Optional.of("********"),
            taxIdNumber = Optional.of("SK2025916874"),
            vatRate = Optional.of(5),
            note = Optional.of("Worst note ever")
        )

        underTest.syncCreateOrUpdateDistributor(updateCommand)
        assertEquals(1, distributorRepository.findAll().size)

        distributorRepository.findAll()[0].let {
            assertEquals(updateCommand.id, it.id)
            assertCommandToDistributorMapping(expected = command, actual = it)
            assertTrue(it.isDeleted())
        }
    }

    @Test
    fun `test adminCreateOrUpdateDistributor - does not exist by distributor id - should create distributor`() {
        every { applicationEventPublisherMock.publishEvent(any<DistributorCreatedOrUpdatedEvent>()) } just runs
        every { applicationEventPublisherMock.publishEvent(any<AdminDistributorCreatedOrUpdatedEvent>()) } just runs

        val distributor = createDistributor(originalId = null)
        val command = mapToCreateOrUpdateDistributorCommand(distributor).copy(id = null, code = null)

        assertEquals(0, distributorRepository.findAll().size)

        underTest.adminCreateOrUpdateDistributor(command)

        assertEquals(1, distributorRepository.findAll().size)

        distributorRepository.findAll()[0].let {
            assertNotNull(it.id)
            assertCommandToDistributorMapping(command, it)
            assertNotNull(it.createdAt)
            assertEqualsTruncated(it.createdAt, it.updatedAt)
            assertNull(it.deletedAt)

            verify(exactly = 1) {
                applicationEventPublisherMock.publishEvent(DistributorCreatedOrUpdatedEvent(it.id))
            }
            verify(exactly = 1) { applicationEventPublisherMock.publishEvent(it.toMessagingEvent()) }
        }
    }

    @Test
    fun `test adminCreateOrUpdateDistributor - exists by distributor id - should update distributor`() {
        every { applicationEventPublisherMock.publishEvent(any<DistributorCreatedOrUpdatedEvent>()) } just runs
        every { applicationEventPublisherMock.publishEvent(any<AdminDistributorCreatedOrUpdatedEvent>()) } just runs

        val distributor = createDistributor(originalId = 1).also { distributorRepository.save(it) }
        assertEquals(1, distributorRepository.findAll().size)

        val updateCommand = mapToCreateOrUpdateDistributorCommand(distributor).copy(
            id = distributor.id,
            originalId = 1,
            code = null,
            disfilmCode = Optional.of("01"),
            title = "Worst movies ever, s.r.o.",
            addressStreet = Optional.of("Seriálová 58"),
            addressCity = Optional.of("Bratislava"),
            addressPostCode = Optional.of("841 01"),
            contactName1 = Optional.of("Peter"),
            contactName2 = Optional.of("Frank"),
            contactName3 = Optional.of("Anatol"),
            contactPhone1 = Optional.of("+************"),
            contactPhone2 = Optional.of("+************"),
            contactPhone3 = Optional.of("*********"),
            contactEmails = Optional.of(setOf("<EMAIL>", "<EMAIL>")),
            bankName = Optional.of("Tatra banka, a.s."),
            bankAccount = Optional.of("458745/0800"),
            idNumber = Optional.of("********"),
            taxIdNumber = Optional.of("SK2025916874"),
            vatRate = Optional.of(5),
            note = Optional.of("Worst note ever")
        )

        underTest.adminCreateOrUpdateDistributor(updateCommand)
        assertEquals(1, distributorRepository.findAll().size)

        val updatedDistributor = distributorRepository.findAll()[0].also {
            assertEquals(updateCommand.id, it.id)
            assertCommandToDistributorMapping(expected = updateCommand, actual = it, expectedCode = distributor.code)
            assertEqualsTruncated(distributor.createdAt, it.createdAt)
            assertTrue(it.updatedAt.isAfter(distributor.updatedAt))
            assertNull(it.deletedAt)
        }

        verify(exactly = 1) {
            applicationEventPublisherMock.publishEvent(DistributorCreatedOrUpdatedEvent(distributor.id))
        }
        verify(exactly = 1) { applicationEventPublisherMock.publishEvent(updatedDistributor.toMessagingEvent()) }
    }

    @Test
    fun `test messagingCreateOrUpdateDistributor - does not exist by distributor original code - should create distributor`() {
        every { applicationEventPublisherMock.publishEvent(any<DistributorCreatedOrUpdatedEvent>()) } just runs

        val distributor = createDistributor()
        val command = mapToMessagingCreateOrUpdateDistributorCommand(distributor)

        assertEquals(0, distributorRepository.findAll().size)

        underTest.messagingCreateOrUpdateDistributor(command)

        assertEquals(1, distributorRepository.findAll().size)

        val createdDistributor = distributorRepository.findAll()[0].also {
            assertNotNull(it.id)
            assertMessagingCommandToDistributorMapping(command, it)
            assertNotNull(it.createdAt)
            assertEqualsTruncated(it.createdAt, it.updatedAt)
            assertNull(it.deletedAt)
        }

        verify(exactly = 1) {
            applicationEventPublisherMock.publishEvent(DistributorCreatedOrUpdatedEvent(createdDistributor.id))
        }
    }

    @Test
    fun `test messagingCreateOrUpdateDistributor - exists by distributor code - should update distributor`() {
        every { applicationEventPublisherMock.publishEvent(any<DistributorCreatedOrUpdatedEvent>()) } just runs

        val distributor = createDistributor(id = 1.toUUID(), code = "1234").also { distributorRepository.save(it) }
        assertEquals(1, distributorRepository.findAll().size)

        val updateCommand = mapToMessagingCreateOrUpdateDistributorCommand(distributor).copy(
            disfilmCode = Optional.of("01"),
            title = "Worst movies ever, s.r.o.",
            addressStreet = Optional.of("Seriálová 58"),
            addressCity = Optional.of("Bratislava"),
            addressPostCode = Optional.of("841 01"),
            contactName1 = Optional.of("Peter"),
            contactName2 = Optional.of("Frank"),
            contactName3 = Optional.of("Anatol"),
            contactPhone1 = Optional.of("+************"),
            contactPhone2 = Optional.of("+************"),
            contactPhone3 = Optional.of("*********"),
            contactEmails = Optional.of(setOf("<EMAIL>", "<EMAIL>")),
            bankName = Optional.of("Tatra banka, a.s."),
            bankAccount = Optional.of("458745/0800"),
            idNumber = Optional.of("********"),
            taxIdNumber = Optional.of("SK2025916874"),
            vatRate = Optional.of(5),
            note = Optional.of("Worst note ever")
        )

        underTest.messagingCreateOrUpdateDistributor(updateCommand)
        assertEquals(1, distributorRepository.findAll().size)

        val updatedDistributor = distributorRepository.findAll()[0].also {
            assertEquals(distributor.id, it.id)
            assertMessagingCommandToDistributorMapping(expected = updateCommand, actual = it)
            assertEqualsTruncated(distributor.createdAt, it.createdAt)
            assertTrue(it.updatedAt.isAfter(distributor.updatedAt))
            assertNull(it.deletedAt)
        }

        verify(exactly = 1) {
            applicationEventPublisherMock.publishEvent(DistributorCreatedOrUpdatedEvent(updatedDistributor.id))
        }
    }

    @Test
    fun `test adminCreateOrUpdateDistributor - update with no attribute changed - should not throw`() {
        every { applicationEventPublisherMock.publishEvent(any<DistributorCreatedOrUpdatedEvent>()) } just runs

        val distributor = createDistributor(originalId = 1).also { distributorRepository.save(it) }
        assertEquals(1, distributorRepository.count())

        val updateCommand = mapToCreateOrUpdateDistributorCommand(distributor).copy(
            id = distributor.id,
            originalId = distributor.originalId,
            code = null
        )

        assertDoesNotThrow {
            underTest.adminCreateOrUpdateDistributor(updateCommand)
        }

        distributorRepository.findAll()[0].also {
            assertEquals(updateCommand.id, it.id)
            assertCommandToDistributorMapping(expected = updateCommand, actual = it, expectedCode = distributor.code)
            assertEqualsTruncated(distributor.createdAt, it.createdAt)
            assertNull(it.deletedAt)
        }

        verify(exactly = 1) {
            applicationEventPublisherMock.publishEvent(DistributorCreatedOrUpdatedEvent(distributor.id))
        }
    }

    @Test
    fun `test adminCreateOrUpdateDistributor - should throw if distributor does not exist with given id`() {
        val distributor1 = createDistributor(originalId = 1, code = "DI_1")
        val command = mapToCreateOrUpdateDistributorCommand(distributor1).copy(code = null)

        assertThrows<DistributorNotFoundException> {
            underTest.adminCreateOrUpdateDistributor(command)
        }
    }

    @Test
    fun `test syncCreateOrUpdateDistributor - command with blank string - should throw exception`() {
        val distributor = createDistributor()
        val command = mapToCreateOrUpdateDistributorCommand(distributor)
        listOf(
            command.copy(addressStreet = Optional.ofNullable(" ")),
            command.copy(addressCity = Optional.ofNullable(" ")),
            command.copy(addressPostCode = Optional.ofNullable(" ")),
            command.copy(contactName1 = Optional.ofNullable(" ")),
            command.copy(contactName2 = Optional.ofNullable(" ")),
            command.copy(contactName3 = Optional.ofNullable(" ")),
            command.copy(contactPhone1 = Optional.ofNullable(" ")),
            command.copy(contactPhone2 = Optional.ofNullable(" ")),
            command.copy(contactPhone3 = Optional.ofNullable(" ")),
            command.copy(bankName = Optional.ofNullable(" ")),
            command.copy(bankAccount = Optional.ofNullable(" ")),
            command.copy(idNumber = Optional.ofNullable(" ")),
            command.copy(taxIdNumber = Optional.ofNullable(" ")),
            command.copy(note = Optional.ofNullable(" "))
        ).forEach {
            assertThrows<ConstraintViolationException> {
                underTest.syncCreateOrUpdateDistributor(it)
            }
        }
    }

    @Test
    fun `test deleteDistributor - should successfully soft delete distributor and publish outbox delete event for mssql`() {
        val distributor = createDistributor().also { distributorRepository.save(it) }
        every { applicationEventPublisherMock.publishEvent(any<AdminDistributorDeletedEvent>()) } just runs

        underTest.deleteDistributor(DeleteDistributorCommand(distributor.id))

        distributorRepository.findAll()[0].let {
            assertNotNull(it)
            assertNotNull(it.isDeleted())
        }

        verify(exactly = 1) { applicationEventPublisherMock.publishEvent(distributor.toMessagingDeleteEvent()) }
        // MS_SQL hard delete event - temporarily disabled
        // verify(exactly = 1) {
        //     applicationEventPublisher.publishEvent(DistributorDeletedEvent(distributor.id))
        // }
    }

    @Test
    fun `test messagingDeleteDistributor - should successfully soft delete distributor and publish outbox delete event for mssql`() {
        val distributor = createDistributor(code = "1234").also { distributorRepository.save(it) }
        // every { applicationEventPublisherMock.publishEvent(any<DistributorDeletedEvent>()) } just runs

        underTest.messagingDeleteDistributor(MessagingDeleteDistributorCommand(distributor.code))

        distributorRepository.findAll()[0].let {
            assertNotNull(it)
            assertNotNull(it.isDeleted())
        }

        // MS_SQL hard delete event - temporarily disabled
        // verify(exactly = 1) {
        //     applicationEventPublisher.publishEvent(DistributorDeletedEvent(distributor.id))
        // }
    }

    @Test
    fun `test updateDistributorOriginalId - should correctly update in db`() {
        val distributor1 = createDistributor(originalId = null).also { distributorRepository.save(it) }
        assertNull(distributorRepository.findAll()[0].originalId)

        underTest.updateDistributorOriginalId(
            UpdateDistributorOriginalIdCommand(
                distributorId = distributor1.id,
                originalId = 5
            )
        )

        assertEquals(5, distributorRepository.findAll()[0].originalId)
    }
}
