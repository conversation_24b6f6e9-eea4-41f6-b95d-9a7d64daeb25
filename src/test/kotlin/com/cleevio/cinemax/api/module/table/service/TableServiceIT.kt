package com.cleevio.cinemax.api.module.table.service

import com.cleevio.cinemax.api.DatabaseCleanup
import com.cleevio.cinemax.api.IntegrationTest
import com.cleevio.cinemax.api.common.constant.PaymentType
import com.cleevio.cinemax.api.module.posconfiguration.constant.ProductMode
import com.cleevio.cinemax.api.module.table.constant.TableType
import com.cleevio.cinemax.api.module.table.entity.Table
import com.cleevio.cinemax.api.util.createTable
import com.cleevio.cinemax.api.util.mapToCreateOrUpdateTableCommand
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import kotlin.test.assertEquals
import kotlin.test.assertNotNull
import kotlin.test.assertTrue

class TableServiceIT @Autowired constructor(
    private val underTest: TableService,
    private val tableFinderService: TableFinderService,
) : IntegrationTest(DatabaseCleanup.AFTER_EACH) {

    @Test
    fun `test createOrUpdateTable - no tables exist - should create table`() {
        val command = mapToCreateOrUpdateTableCommand(TABLE_1)
        underTest.createOrUpdateTable(command)

        val createdTable = tableFinderService.findByOriginalId(TABLE_1.originalId)
        assertNotNull(createdTable)
        assertTableEquals(TABLE_1, createdTable)
    }

    @Test
    fun `test createOrUpdateTable - one table exists - insert equal table so it should update`() {
        val createCommand = mapToCreateOrUpdateTableCommand(TABLE_2)
        underTest.createOrUpdateTable(createCommand)

        val updateCommand = createCommand.copy(
            title = "E15, 1211",
            label = "2",
            order = 2,
            type = TableType.SEAT,
            paymentType = PaymentType.CASH,
            productMode = ProductMode.CAFE,
            discountCardCode = "987654321",
            ipAddress = "************"
        )
        underTest.createOrUpdateTable(updateCommand)

        val tables = tableFinderService.findAll()
        assertEquals(tables.size, 1)
        assertNotNull(tables[0])
        assertEquals(updateCommand.originalId, tables[0].originalId)
        assertEquals(updateCommand.title, tables[0].title)
        assertEquals(updateCommand.label, tables[0].label)
        assertEquals(updateCommand.order, tables[0].order)
        assertEquals(updateCommand.type, tables[0].type)
        assertEquals(updateCommand.productMode, tables[0].productMode)
        assertEquals(updateCommand.paymentType, tables[0].paymentType)
        assertEquals(updateCommand.discountCardCode, tables[0].discountCardCode)
        assertEquals(updateCommand.ipAddress, tables[0].ipAddress)
        assertTrue(tables[0].updatedAt.isAfter(tables[0].createdAt))
    }

    @Test
    fun `test createOrUpdateTable - two tables - should create two tables`() {
        val command = mapToCreateOrUpdateTableCommand(TABLE_1)
        underTest.createOrUpdateTable(command)
        val createCommand = mapToCreateOrUpdateTableCommand(TABLE_2)
        underTest.createOrUpdateTable(createCommand)

        val tables = tableFinderService.findAll()
        assertEquals(tables.size, 2)
        assertTableEquals(TABLE_1, tables.first { it.originalId == TABLE_1.originalId })
        assertTableEquals(TABLE_2, tables.first { it.originalId == TABLE_2.originalId })
    }

    private fun assertTableEquals(expected: Table, actual: Table) {
        assertEquals(expected.originalId, actual.originalId)
        assertEquals(expected.title, actual.title)
        assertEquals(expected.label, actual.label)
        assertEquals(expected.order, actual.order)
        assertEquals(expected.type, actual.type)
        assertEquals(expected.productMode, actual.productMode)
        assertEquals(expected.paymentType, actual.paymentType)
        assertEquals(expected.discountCardCode, actual.discountCardCode)
        assertEquals(expected.ipAddress, actual.ipAddress)
        assertNotNull(actual.createdAt)
        assertNotNull(actual.updatedAt)
    }
}

private val TABLE_1 = createTable(
    originalId = 1,
    title = "Stul 1",
    label = "1",
    order = 1,
    type = TableType.TABLE,
    paymentType = PaymentType.CASH,
    productMode = ProductMode.STANDARD
)
private val TABLE_2 = createTable(
    originalId = 2,
    title = "E14, 1211",
    label = "1",
    order = 1,
    type = TableType.SEAT,
    paymentType = PaymentType.CASHLESS,
    productMode = ProductMode.VIP,
    discountCardCode = "123456789",
    ipAddress = "************"
)
