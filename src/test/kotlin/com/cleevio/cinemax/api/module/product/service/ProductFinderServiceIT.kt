package com.cleevio.cinemax.api.module.product.service

import com.cleevio.cinemax.api.DatabaseCleanup
import com.cleevio.cinemax.api.IntegrationTest
import com.cleevio.cinemax.api.common.constant.STANDARD_TAX_RATE
import com.cleevio.cinemax.api.common.util.isEqualTo
import com.cleevio.cinemax.api.module.product.constant.ProductType
import com.cleevio.cinemax.api.module.product.entity.Product
import com.cleevio.cinemax.api.module.product.event.ProductCreatedOrUpdatedEvent
import com.cleevio.cinemax.api.module.productcategory.constant.ProductCategoryType
import com.cleevio.cinemax.api.module.productcategory.service.ProductCategoryService
import com.cleevio.cinemax.api.module.productcomponent.constant.ProductComponentUnit
import com.cleevio.cinemax.api.module.productcomponent.service.ProductComponentService
import com.cleevio.cinemax.api.module.productcomponentcategory.service.ProductComponentCategoryRepository
import com.cleevio.cinemax.api.module.productcomposition.entity.ProductComposition
import com.cleevio.cinemax.api.module.productcomposition.service.ProductCompositionService
import com.cleevio.cinemax.api.util.createProduct
import com.cleevio.cinemax.api.util.createProductCategory
import com.cleevio.cinemax.api.util.createProductComponent
import com.cleevio.cinemax.api.util.createProductComponentCategory
import com.cleevio.cinemax.api.util.createProductComposition
import com.cleevio.cinemax.api.util.mapToCreateOrUpdateProductCategoryCommand
import com.cleevio.cinemax.api.util.mapToCreateOrUpdateProductComponentCommand
import com.cleevio.cinemax.api.util.mapToCreateOrUpdateProductCompositionCommand
import com.cleevio.cinemax.api.util.mapToCreateProductCommand
import com.cleevio.cinemax.api.util.mapToSyncCreateOrUpdateProductCommand
import io.mockk.Runs
import io.mockk.every
import io.mockk.just
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.Arguments
import org.junit.jupiter.params.provider.MethodSource
import org.springframework.beans.factory.annotation.Autowired
import java.math.BigDecimal
import java.util.UUID
import java.util.stream.Stream
import kotlin.test.assertEquals
import kotlin.test.assertNotNull
import kotlin.test.assertTrue

class ProductFinderServiceIT @Autowired constructor(
    private val underTest: ProductJooqFinderService,
    private val productService: ProductService,
    private val productCategoryService: ProductCategoryService,
    private val productCompositionService: ProductCompositionService,
    private val productComponentService: ProductComponentService,
    private val productComponentCategoryRepository: ProductComponentCategoryRepository,
) : IntegrationTest(DatabaseCleanup.AFTER_EACH) {

    @BeforeEach
    fun setUp() {
        setOf(PRODUCT_CATEGORY_1, PRODUCT_CATEGORY_2).forEach {
            productCategoryService.syncCreateOrUpdateProductCategory(mapToCreateOrUpdateProductCategoryCommand(it))
        }

        setOf(
            PRODUCT_1 to PRODUCT_CATEGORY_1.id,
            PRODUCT_2 to PRODUCT_CATEGORY_2.id,
            PRODUCT_3 to PRODUCT_CATEGORY_2.id,
            PRODUCT_4 to PRODUCT_CATEGORY_2.id
        ).forEach { productService.syncCreateOrUpdateProduct(mapToSyncCreateOrUpdateProductCommand(it.first, it.second)) }

        productComponentCategoryRepository.save(PRODUCT_COMPONENT_CATEGORY_1)
        setOf(
            PRODUCT_COMPONENT_1,
            PRODUCT_COMPONENT_2,
            PRODUCT_COMPONENT_3,
            PRODUCT_COMPONENT_4,
            PRODUCT_COMPONENT_5,
            PRODUCT_COMPONENT_6
        ).forEach {
            productComponentService.syncCreateOrUpdateProductComponent(
                mapToCreateOrUpdateProductComponentCommand(it)
            )
        }

        setOf(
            PRODUCT_COMPOSITION_1,
            PRODUCT_COMPOSITION_2,
            PRODUCT_COMPOSITION_3,
            PRODUCT_COMPOSITION_4,
            PRODUCT_COMPOSITION_5,
            PRODUCT_COMPOSITION_6
        ).forEach {
            productCompositionService.createOrUpdateProductComposition(
                mapToCreateOrUpdateProductCompositionCommand(it)
            )
        }
    }

    @Test
    fun `test findAvailableBuffetProductsByCategoryIds - should return relevant products`() {
        val products = underTest.findAvailableBuffetProductsByCategoryIds(
            setOf(PRODUCT_CATEGORY_1.id, PRODUCT_CATEGORY_2.id)
        )

        assertEquals(1, products.size)
        assertProductEquals(PRODUCT_1, products.sortedBy { it.originalId }[0])
    }

    @Test
    fun `test findAllProductStockQuantitiesByIdIn - should return relevant products`() {
        val productsToStockQuantities = underTest.findAllNonDeletedActiveProductStockQuantitiesByIdIn(
            setOf(PRODUCT_1.id, PRODUCT_2.id, PRODUCT_3.id, PRODUCT_4.id)
        )

        assertEquals(3, productsToStockQuantities.size)
        assertTrue(productsToStockQuantities.containsKey(PRODUCT_1.id))
        assertTrue(125.0.toBigDecimal() isEqualTo productsToStockQuantities[PRODUCT_1.id])
        assertTrue(productsToStockQuantities.containsKey(PRODUCT_2.id))
        assertTrue(720.0.toBigDecimal() isEqualTo productsToStockQuantities[PRODUCT_2.id])
        assertTrue(productsToStockQuantities.containsKey(PRODUCT_4.id))
        assertTrue(0.toBigDecimal() isEqualTo productsToStockQuantities[PRODUCT_4.id])
    }

    @ParameterizedTest
    @MethodSource("productIdToHasNonZeroStockQuantityProvider")
    fun `test existsByIdAndHasNonZeroStockQuantity - should return correct booleans`(
        productId: UUID,
        hasNonZeroStockQuantity: Boolean,
    ) {
        assertEquals(hasNonZeroStockQuantity, underTest.existsNonDeletedByIdAndStockQuantityGreaterThanOrEqualOne(productId))
    }

    @Test
    fun `test findAllProductStockQuantitiesByIdIn - should return relevant products and correct stock quantities including product_in_product logic`() {
        every { applicationEventPublisherMock.publishEvent(any<ProductCreatedOrUpdatedEvent>()) } just Runs

        setOf(PRODUCT_CATEGORY_11, PRODUCT_CATEGORY_12).forEach {
            productCategoryService.syncCreateOrUpdateProductCategory(
                mapToCreateOrUpdateProductCategoryCommand(it)
            )
        }

        productComponentCategoryRepository.save(PRODUCT_COMPONENT_CATEGORY_1)
        setOf(
            PRODUCT_COMPONENT_11,
            PRODUCT_COMPONENT_12,
            PRODUCT_COMPONENT_13,
            PRODUCT_COMPONENT_14,
            PRODUCT_COMPONENT_15
        ).forEach {
            productComponentService.syncCreateOrUpdateProductComponent(
                mapToCreateOrUpdateProductComponentCommand(it)
            )
        }

        val product1Id = productService.adminCreateProduct(
            mapToCreateProductCommand(
                product = PRODUCT_11,
                productCategoryId = PRODUCT_CATEGORY_11.id,
                productCompositions = listOf(PRODUCT_COMPOSITION_11)
            )
        )

        val product2Id = productService.adminCreateProduct(
            mapToCreateProductCommand(
                product = PRODUCT_12,
                productCategoryId = PRODUCT_CATEGORY_11.id,
                productCompositions = listOf(PRODUCT_COMPOSITION_12)
            )
        )

        val product3Id = productService.adminCreateProduct(
            mapToCreateProductCommand(
                product = PRODUCT_13,
                productCategoryId = PRODUCT_CATEGORY_11.id,
                productCompositions = listOf(PRODUCT_COMPOSITION_13, PRODUCT_COMPOSITION_14, PRODUCT_COMPOSITION_15)
            )
        )

        val product4Id = productService.adminCreateProduct(
            mapToCreateProductCommand(
                product = PRODUCT_14,
                productCategoryId = PRODUCT_CATEGORY_11.id,
                productCompositions = listOf(PRODUCT_COMPOSITION_16(product1Id), PRODUCT_COMPOSITION_17(product3Id))
            )
        )

        val product5Id = productService.adminCreateProduct(
            mapToCreateProductCommand(
                product = PRODUCT_15,
                productCategoryId = PRODUCT_CATEGORY_11.id,
                productCompositions = listOf(PRODUCT_COMPOSITION_18(product2Id), PRODUCT_COMPOSITION_19(product3Id))
            )
        )

        val product6Id = productService.adminCreateProduct(
            mapToCreateProductCommand(
                product = PRODUCT_16,
                productCategoryId = PRODUCT_CATEGORY_11.id,
                productCompositions = emptyList()
            )
        )

        val productsToStockQuantities = underTest.findAllNonDeletedActiveProductStockQuantitiesByIdIn(
            setOf(product1Id, product2Id, product3Id, product4Id, product5Id, product6Id)
        )

        assertEquals(5, productsToStockQuantities.size)
        assertTrue(
            setOf(product1Id, product2Id, product3Id, product4Id, product5Id)
                .all { productsToStockQuantities.containsKey(it) }
        )

        // Coca Cola 0.33l - limiting is stock quantity of only product component Coca Cola 0.33l
        assertTrue(90.0.toBigDecimal() isEqualTo productsToStockQuantities[product1Id])
        // Sprite 0.33l - limiting is stock quantity of only product component Sprite 0.33l
        assertTrue(15.0.toBigDecimal() isEqualTo productsToStockQuantities[product2Id])
        // Popcorn XXL - limiting is stock quantity of product component Tuk with 50 / 2 stock quantity
        assertTrue(25.toBigDecimal() isEqualTo productsToStockQuantities[product3Id])
        // Combo Popcorn XXL + MEGA Coke (3x Coca Cola 0,33l)
        // - limiting is stock quantity of product component Coca Cola 0,33l with 90 / 1 / 4 stock quantity
        assertTrue(18.toBigDecimal() isEqualTo productsToStockQuantities[product4Id])
        // Combo Popcorn XXL + Sprite 0,33l
        // - limiting is stock quantity of product component Sprite 0,33l with 15 / 1 / 1 stock quantity
        assertTrue(15.toBigDecimal() isEqualTo productsToStockQuantities[product5Id])
    }

    private fun assertProductEquals(expected: Product, actual: Product) {
        assertEquals(expected.originalId, actual.originalId)
        assertEquals(expected.title, actual.title)
        assertEquals(expected.type, actual.type)
        assertTrue(expected.price isEqualTo actual.price)
        assertEquals(expected.active, actual.active)
        assertEquals(expected.order, actual.order)
        assertEquals(expected.soldInBuffet, actual.soldInBuffet)
        assertEquals(expected.soldInCafe, actual.soldInCafe)
        assertEquals(expected.soldInVip, actual.soldInVip)
        assertNotNull(actual.createdAt)
        assertNotNull(actual.updatedAt)
    }

    companion object {
        @JvmStatic
        fun productIdToHasNonZeroStockQuantityProvider(): Stream<Arguments> {
            return Stream.of(
                Arguments.of(PRODUCT_1.id, true),
                Arguments.of(PRODUCT_2.id, true),
                Arguments.of(PRODUCT_3.id, false),
                Arguments.of(PRODUCT_4.id, false)
            )
        }
    }
}

private val PRODUCT_CATEGORY_1 = createProductCategory(
    originalId = 1,
    code = "01",
    title = "Nápoje",
    type = ProductCategoryType.PRODUCT,
    order = 10,
    taxRate = STANDARD_TAX_RATE
)
private val PRODUCT_CATEGORY_2 = createProductCategory(
    originalId = 2,
    code = "02",
    title = "Pochutiny",
    type = ProductCategoryType.PRODUCT,
    order = 11,
    taxRate = STANDARD_TAX_RATE
)
private val PRODUCT_1 = createProduct(
    originalId = 1,
    code = "01",
    productCategoryId = PRODUCT_CATEGORY_1.id,
    title = "Coca Cola 0.33l",
    type = ProductType.PRODUCT,
    price = 3.5.toBigDecimal(),
    order = 23
)
private val PRODUCT_2 = createProduct(
    originalId = 2,
    code = "02",
    productCategoryId = PRODUCT_CATEGORY_2.id,
    title = "Popcorn XXL",
    type = ProductType.PRODUCT,
    price = 6.5.toBigDecimal(),
    order = 25,
    soldInBuffet = false
)
private val PRODUCT_3 = createProduct(
    originalId = 3,
    code = "03",
    productCategoryId = PRODUCT_CATEGORY_2.id,
    title = "Popcorn medium",
    type = ProductType.PRODUCT,
    price = 4.5.toBigDecimal(),
    order = 14
)
private val PRODUCT_4 = createProduct(
    originalId = 4,
    code = "04",
    productCategoryId = PRODUCT_CATEGORY_2.id,
    title = "Mexické nachos so salsou",
    type = ProductType.PRODUCT,
    price = 12.5.toBigDecimal(),
    order = null
)
private val PRODUCT_COMPONENT_CATEGORY_1 = createProductComponentCategory()
private val PRODUCT_COMPONENT_1 = createProductComponent(
    originalId = 1,
    code = "01",
    title = "Coca Cola 0.33l",
    unit = ProductComponentUnit.KS,
    stockQuantity = 125.toBigDecimal(),
    productComponentCategoryId = PRODUCT_COMPONENT_CATEGORY_1.id
)
private val PRODUCT_COMPONENT_2 = createProductComponent(
    originalId = 2,
    code = "02",
    title = "Brčko",
    unit = ProductComponentUnit.KS,
    stockQuantity = 2500.toBigDecimal(),
    productComponentCategoryId = PRODUCT_COMPONENT_CATEGORY_1.id
)
private val PRODUCT_COMPONENT_3 = createProductComponent(
    originalId = 3,
    code = "03",
    title = "Kukurica",
    unit = ProductComponentUnit.KG,
    stockQuantity = 180.0.toBigDecimal(),
    productComponentCategoryId = PRODUCT_COMPONENT_CATEGORY_1.id
)
private val PRODUCT_COMPONENT_4 = createProductComponent(
    originalId = 4,
    code = "04",
    title = "Soľ",
    unit = ProductComponentUnit.KG,
    stockQuantity = 230.8.toBigDecimal(),
    productComponentCategoryId = PRODUCT_COMPONENT_CATEGORY_1.id
)
private val PRODUCT_COMPONENT_5 = createProductComponent(
    originalId = 5,
    code = "05",
    title = "Tuk",
    unit = ProductComponentUnit.KG,
    stockQuantity = 750.55.toBigDecimal(),
    productComponentCategoryId = PRODUCT_COMPONENT_CATEGORY_1.id
)
private val PRODUCT_COMPONENT_6 = createProductComponent(
    originalId = 6,
    code = "06",
    title = "Nachos",
    unit = ProductComponentUnit.KS,
    stockQuantity = 0.toBigDecimal(),
    productComponentCategoryId = PRODUCT_COMPONENT_CATEGORY_1.id
)

// Coca Cola 0.33l - Coca Cola 0.33l (1x)
private val PRODUCT_COMPOSITION_1 = createProductComposition(
    originalId = 1,
    productId = PRODUCT_1.id,
    productComponentId = PRODUCT_COMPONENT_1.id,
    amount = 1.toBigDecimal()
)

// Coca Cola 0.33l - Brčko (1x)
private val PRODUCT_COMPOSITION_2 = createProductComposition(
    originalId = 2,
    productId = PRODUCT_1.id,
    productComponentId = PRODUCT_COMPONENT_2.id,
    amount = 1.toBigDecimal()
)

// Popcorn XXL - Kukurica (0.2268x)
private val PRODUCT_COMPOSITION_3 = createProductComposition(
    originalId = 3,
    productId = PRODUCT_2.id,
    productComponentId = PRODUCT_COMPONENT_3.id,
    amount = 0.25.toBigDecimal()
)

// Popcorn XXL - Soľ (0.00838x)
private val PRODUCT_COMPOSITION_4 = createProductComposition(
    originalId = 4,
    productId = PRODUCT_2.id,
    productComponentId = PRODUCT_COMPONENT_4.id,
    amount = 0.00838.toBigDecimal()
)

// Popcorn XXL - Tuk (0.06798x)
private val PRODUCT_COMPOSITION_5 = createProductComposition(
    originalId = 5,
    productId = PRODUCT_2.id,
    productComponentId = PRODUCT_COMPONENT_5.id,
    amount = 0.06798.toBigDecimal()
)

// Mexické nachos so salsou - Nachos (1x)
private val PRODUCT_COMPOSITION_6 = createProductComposition(
    originalId = 6,
    productId = PRODUCT_4.id,
    productComponentId = PRODUCT_COMPONENT_6.id,
    amount = 1.toBigDecimal()
)

private val PRODUCT_CATEGORY_11 = createProductCategory(
    originalId = 11,
    code = "11",
    title = "Kategorie produktu",
    type = ProductCategoryType.PRODUCT,
    taxRate = STANDARD_TAX_RATE
)

private val PRODUCT_CATEGORY_12 = createProductCategory(
    originalId = 12,
    code = "12",
    title = "Kategorie slev",
    type = ProductCategoryType.DISCOUNT,
    taxRate = STANDARD_TAX_RATE
)

private val PRODUCT_11 = createProduct(
    originalId = 11,
    code = "11",
    productCategoryId = PRODUCT_CATEGORY_11.id,
    title = "Coca Cola 0.33l",
    type = ProductType.PRODUCT
)
private val PRODUCT_12 = createProduct(
    originalId = 12,
    code = "12",
    productCategoryId = PRODUCT_CATEGORY_11.id,
    title = "Sprite 0.33l",
    type = ProductType.PRODUCT
)
private val PRODUCT_13 = createProduct(
    originalId = 13,
    code = "13",
    productCategoryId = PRODUCT_CATEGORY_11.id,
    title = "Popcorn XXL",
    type = ProductType.PRODUCT
)
private val PRODUCT_14 = createProduct(
    originalId = 14,
    code = "14",
    productCategoryId = PRODUCT_CATEGORY_11.id,
    title = "Combo Popcorn XXL + 3x Coca Cola 0,33l",
    type = ProductType.PRODUCT_IN_PRODUCT,
    taxRate = null
)
private val PRODUCT_15 = createProduct(
    originalId = 15,
    code = "15",
    productCategoryId = PRODUCT_CATEGORY_11.id,
    title = "Combo Popcorn XXL + Sprite 0,33l",
    type = ProductType.PRODUCT_IN_PRODUCT,
    taxRate = null
)
private val PRODUCT_16 = createProduct(
    originalId = 16,
    code = "16",
    productCategoryId = PRODUCT_CATEGORY_12.id,
    title = "Additional Sale",
    type = ProductType.ADDITIONAL_SALE,
    discountAmount = 15.toBigDecimal()
)
private val PRODUCT_COMPONENT_11 = createProductComponent(
    originalId = 11,
    code = "11",
    title = "Coca Cola 0,33l",
    unit = ProductComponentUnit.KS,
    stockQuantity = 90.toBigDecimal(),
    productComponentCategoryId = PRODUCT_COMPONENT_CATEGORY_1.id
)
private val PRODUCT_COMPONENT_12 = createProductComponent(
    originalId = 12,
    code = "12",
    title = "Sprite 0,33l",
    unit = ProductComponentUnit.KS,
    stockQuantity = 15.0.toBigDecimal(),
    productComponentCategoryId = PRODUCT_COMPONENT_CATEGORY_1.id
)
private val PRODUCT_COMPONENT_13 = createProductComponent(
    originalId = 13,
    code = "13",
    title = "Kukurica",
    unit = ProductComponentUnit.KG,
    stockQuantity = 1000.toBigDecimal(),
    productComponentCategoryId = PRODUCT_COMPONENT_CATEGORY_1.id
)
private val PRODUCT_COMPONENT_14 = createProductComponent(
    originalId = 14,
    code = "14",
    title = "Soľ",
    unit = ProductComponentUnit.KG,
    stockQuantity = 100.toBigDecimal(),
    productComponentCategoryId = PRODUCT_COMPONENT_CATEGORY_1.id
)
private val PRODUCT_COMPONENT_15 = createProductComponent(
    originalId = 15,
    code = "15",
    title = "Tuk",
    unit = ProductComponentUnit.KG,
    stockQuantity = 50.toBigDecimal(),
    productComponentCategoryId = PRODUCT_COMPONENT_CATEGORY_1.id
)

// Coca Cola 0.33l  - Coca Cola 0.33l (1x)
private val PRODUCT_COMPOSITION_11 = createProductComposition(
    originalId = 11,
    productId = PRODUCT_11.id,
    productComponentId = PRODUCT_COMPONENT_11.id,
    amount = 1.toBigDecimal()
)

// Sprite 0.33l - Sprite 0.33l (1x)
private val PRODUCT_COMPOSITION_12 = createProductComposition(
    originalId = 12,
    productId = PRODUCT_12.id,
    productComponentId = PRODUCT_COMPONENT_12.id,
    amount = 1.toBigDecimal()
)

// Popcorn XXL - Kukurica (1x)
private val PRODUCT_COMPOSITION_13 = createProductComposition(
    originalId = 13,
    productId = PRODUCT_13.id,
    productComponentId = PRODUCT_COMPONENT_13.id,
    amount = 1.toBigDecimal(),
    productInProductId = null
)

// Popcorn XXL - Soľ (3x)
private val PRODUCT_COMPOSITION_14 = createProductComposition(
    originalId = 14,
    productId = PRODUCT_13.id,
    productComponentId = PRODUCT_COMPONENT_14.id,
    amount = 3.toBigDecimal()
)

// Popcorn XXL - Tuk (2x)
private val PRODUCT_COMPOSITION_15 = createProductComposition(
    originalId = 15,
    productId = PRODUCT_13.id,
    productComponentId = PRODUCT_COMPONENT_15.id,
    amount = 2.toBigDecimal()
)

// Combo Popcorn XXL + MEGA Coca Cola 0,33l -> Coca Cola 0,33l (3x)
private val PRODUCT_COMPOSITION_16: (UUID) -> ProductComposition = { productInProductId: UUID ->
    createProductComposition(
        originalId = 16,
        productId = PRODUCT_14.id,
        productInProductId = productInProductId,
        productComponentId = null,
        amount = 5.toBigDecimal(),
        productInProductPrice = BigDecimal.ONE
    )
}

// Combo Popcorn XXL + MEGA Cola 0,33l -> Popcorn XXL (1x)
private val PRODUCT_COMPOSITION_17: (UUID) -> ProductComposition = { productInProductId: UUID ->
    createProductComposition(
        originalId = 17,
        productId = PRODUCT_14.id,
        productInProductId = productInProductId,
        productComponentId = null,
        amount = 1.toBigDecimal(),
        productInProductPrice = 2.toBigDecimal()
    )
}

// Combo Popcorn XXL + Sprite 0,33l -> Sprite 0,33l (1x)
private val PRODUCT_COMPOSITION_18: (UUID) -> ProductComposition = { productInProductId: UUID ->
    createProductComposition(
        originalId = 18,
        productId = PRODUCT_15.id,
        productInProductId = productInProductId,
        productComponentId = null,
        amount = 1.toBigDecimal(),
        productInProductPrice = 1.8.toBigDecimal()
    )
}

// Combo Popcorn XXL + Coca Cola 0,33l -> Popcorn XXL (1x)
private val PRODUCT_COMPOSITION_19: (UUID) -> ProductComposition = { productInProductId: UUID ->
    createProductComposition(
        originalId = 19,
        productId = PRODUCT_15.id,
        productInProductId = productInProductId,
        productComponentId = null,
        amount = 1.toBigDecimal(),
        productInProductPrice = 2.5.toBigDecimal()
    )
}
