package com.cleevio.cinemax.api.module.receipt.service

import com.cleevio.cinemax.api.common.config.CinemaxConfigProperties
import com.cleevio.cinemax.api.common.constant.NO_TAX_RATE
import com.cleevio.cinemax.api.common.constant.PaymentType
import com.cleevio.cinemax.api.common.constant.REDUCED_TAX_RATE
import com.cleevio.cinemax.api.common.constant.STANDARD_TAX_RATE
import com.cleevio.cinemax.api.common.constant.SUPER_REDUCED_TAX_RATE
import com.cleevio.cinemax.api.common.constant.TaxRateType
import com.cleevio.cinemax.api.common.util.isEqualTo
import com.cleevio.cinemax.api.common.util.serializeToXml
import com.cleevio.cinemax.api.module.auditorium.service.AuditoriumJooqFinderService
import com.cleevio.cinemax.api.module.basket.constant.BasketState
import com.cleevio.cinemax.api.module.basket.model.BasketModel
import com.cleevio.cinemax.api.module.basket.service.BasketJpaFinderService
import com.cleevio.cinemax.api.module.basketitem.constant.BasketItemType
import com.cleevio.cinemax.api.module.basketitem.entity.BasketItem
import com.cleevio.cinemax.api.module.basketitem.model.BasketItemTaxRateModel
import com.cleevio.cinemax.api.module.basketitem.service.BasketItemProductService
import com.cleevio.cinemax.api.module.movie.service.MovieJooqFinderService
import com.cleevio.cinemax.api.module.posconfiguration.service.PosConfigurationJooqFinderService
import com.cleevio.cinemax.api.module.pricecategoryitem.constant.PriceCategoryItemNumber
import com.cleevio.cinemax.api.module.product.constant.ProductType
import com.cleevio.cinemax.api.module.productcategory.constant.ProductCategoryType
import com.cleevio.cinemax.api.module.receipt.constant.ReceiptItemType
import com.cleevio.cinemax.api.module.receipt.constant.ReceiptPaymentType
import com.cleevio.cinemax.api.module.receipt.model.request.SvkXmlHeader
import com.cleevio.cinemax.api.module.receipt.model.request.SvkXmlReceipt
import com.cleevio.cinemax.api.module.receipt.model.request.SvkXmlReceiptData
import com.cleevio.cinemax.api.module.receipt.model.request.SvkXmlReceiptItem
import com.cleevio.cinemax.api.module.receipt.model.request.SvkXmlReceiptItems
import com.cleevio.cinemax.api.module.receipt.model.request.SvkXmlReceiptPayment
import com.cleevio.cinemax.api.module.receipt.model.request.SvkXmlReceiptPayments
import com.cleevio.cinemax.api.module.receipt.service.command.GenerateCancellationReceiptCommand
import com.cleevio.cinemax.api.module.receipt.service.command.GenerateReceiptCommand
import com.cleevio.cinemax.api.module.reservation.service.ReservationJooqFinderService
import com.cleevio.cinemax.api.module.screening.service.ScreeningJooqFinderService
import com.cleevio.cinemax.api.module.seat.constant.SeatType
import com.cleevio.cinemax.api.module.seat.service.SeatJooqFinderService
import com.cleevio.cinemax.api.module.ticket.model.TicketModel
import com.cleevio.cinemax.api.module.ticket.service.TicketJooqFinderService
import com.cleevio.cinemax.api.util.createAuditorium
import com.cleevio.cinemax.api.util.createAuditoriumLayout
import com.cleevio.cinemax.api.util.createBasket
import com.cleevio.cinemax.api.util.createBasketItem
import com.cleevio.cinemax.api.util.createMovie
import com.cleevio.cinemax.api.util.createPosConfiguration
import com.cleevio.cinemax.api.util.createProduct
import com.cleevio.cinemax.api.util.createProductCategory
import com.cleevio.cinemax.api.util.createReservation
import com.cleevio.cinemax.api.util.createScreening
import com.cleevio.cinemax.api.util.createSeat
import com.cleevio.cinemax.api.util.createTicket
import com.cleevio.cinemax.api.util.createTicketPrice
import io.mockk.every
import io.mockk.mockk
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import java.time.LocalDate
import java.time.LocalTime
import java.util.UUID
import kotlin.test.assertEquals
import kotlin.test.assertTrue

class SvkXmlReceiptGeneratorTest {

    private val basketJpaFinderService = mockk<BasketJpaFinderService>()
    private val ticketJooqFinderService = mockk<TicketJooqFinderService>()
    private val movieJooqFinderService = mockk<MovieJooqFinderService>()
    private val screeningJooqFinderService = mockk<ScreeningJooqFinderService>()
    private val auditoriumJooqFinderService = mockk<AuditoriumJooqFinderService>()
    private val reservationJooqFinderService = mockk<ReservationJooqFinderService>()
    private val seatJooqFinderService = mockk<SeatJooqFinderService>()
    private val posConfigurationJooqFinderService = mockk<PosConfigurationJooqFinderService>()
    private val basketItemProductService = mockk<BasketItemProductService>()
    private val cinemaxConfigProperties = mockk<CinemaxConfigProperties>()

    private val underTest = SvkXmlReceiptGenerator(
        basketJpaFinderService,
        ticketJooqFinderService,
        movieJooqFinderService,
        screeningJooqFinderService,
        auditoriumJooqFinderService,
        reservationJooqFinderService,
        seatJooqFinderService,
        posConfigurationJooqFinderService,
        basketItemProductService,
        cinemaxConfigProperties
    )

    @BeforeEach
    fun setUp() {
        every { cinemaxConfigProperties.getTaxRate(TaxRateType.STANDARD) } returns STANDARD_TAX_RATE
        every { cinemaxConfigProperties.getTaxRate(TaxRateType.REDUCED) } returns REDUCED_TAX_RATE
        every { cinemaxConfigProperties.getTaxRate(TaxRateType.SUPER_REDUCED) } returns SUPER_REDUCED_TAX_RATE
        every { cinemaxConfigProperties.getTaxRate(TaxRateType.NO_TAX) } returns NO_TAX_RATE
    }

    @Test
    fun `test generateReceipt - basket with two tickets - should generate receipt with two tickets`() {
        prepareTicketData()

        val generatedReceipt = underTest.generateReceipt(GenerateReceiptCommand(basketId = BASKET_1.id))
        assertEquals(EXPECTED_RECEIPT_1_STUDENT_AND_ADULT_TICKET.header.uuid, generatedReceipt.header.uuid)
        assertEquals(EXPECTED_RECEIPT_1_STUDENT_AND_ADULT_TICKET.data.size, generatedReceipt.data.size)

        val expectedAdultTicketReceiptData = EXPECTED_RECEIPT_1_STUDENT_AND_ADULT_TICKET.data[0]
        val expectedAdultTicketReceiptItem = expectedAdultTicketReceiptData.items.items[0]
        val expectedAdultTicketPayment = expectedAdultTicketReceiptData.payments.payment[0]
        val expectedStudentTicketReceiptData = EXPECTED_RECEIPT_1_STUDENT_AND_ADULT_TICKET.data[1]
        val expectedStudentTicketReceiptItem = expectedStudentTicketReceiptData.items.items[0]
        val expectedStudentTicketPayment = expectedStudentTicketReceiptData.payments.payment[0]

        val generatedAdultTicketReceiptData = generatedReceipt.data[0]
        val generatedAdultTicketReceiptItem = generatedAdultTicketReceiptData.items.items[0]
        val generatedAdultTicketPayment = generatedAdultTicketReceiptData.payments.payment[0]
        val generatedStudentTicketReceiptData = generatedReceipt.data[1]
        val generatedStudentTicketReceiptItem = generatedStudentTicketReceiptData.items.items[0]
        val generatedStudentTicketPayment = generatedStudentTicketReceiptData.payments.payment[0]

        assertReceiptDataEquals(expectedAdultTicketReceiptData, generatedAdultTicketReceiptData)
        assertReceiptItemEquals(expectedAdultTicketReceiptItem, generatedAdultTicketReceiptItem)
        assertPaymentEquals(expectedAdultTicketPayment, generatedAdultTicketPayment)

        assertReceiptDataEquals(expectedStudentTicketReceiptData, generatedStudentTicketReceiptData)
        assertReceiptItemEquals(expectedStudentTicketReceiptItem, generatedStudentTicketReceiptItem)
        assertPaymentEquals(expectedStudentTicketPayment, generatedStudentTicketPayment)
    }

    @Test
    fun `test generateReceipt - basket with two group tickets - should generate receipt with two tickets`() {
        prepareTicketData(isGroupTicket = true)

        val generatedReceipt = underTest.generateReceipt(GenerateReceiptCommand(basketId = BASKET_1.id))
        assertEquals(EXPECTED_RECEIPT_2_GROUP_TICKET.header.uuid, generatedReceipt.header.uuid)
        assertEquals(EXPECTED_RECEIPT_2_GROUP_TICKET.data.size, generatedReceipt.data.size)

        val expectedGroupTicketReceiptData = EXPECTED_RECEIPT_2_GROUP_TICKET.data[0]
        val expectedGroupTicketReceiptItem = expectedGroupTicketReceiptData.items.items[0]
        val expectedGroupTicketPayment = expectedGroupTicketReceiptData.payments.payment[0]

        val generatedGroupTicketReceiptData = generatedReceipt.data[0]
        val generatedGroupTicketReceiptItem = generatedGroupTicketReceiptData.items.items[0]
        val generatedGroupTicketPayment = generatedGroupTicketReceiptData.payments.payment[0]

        assertReceiptDataEquals(expectedGroupTicketReceiptData, generatedGroupTicketReceiptData)
        assertReceiptItemEquals(expectedGroupTicketReceiptItem, generatedGroupTicketReceiptItem)
        assertPaymentEquals(expectedGroupTicketPayment, generatedGroupTicketPayment)
    }

    @Test
    fun `test generateReceipt - IMAX ticket with fee, product and product discount (no IG, equal VAT), applied VIP card - should generate receipt with one product discount item`() {
        every { basketJpaFinderService.getNonDeletedWithItemsById(any()) } returns BasketModel(
            basket = BASKET_2,
            basketItems = listOf(
                BASKET_ITEM_3_IMAX_TICKET_DISCOUNTED,
                BASKET_ITEM_4_POPCORN,
                BASKET_ITEM_5_PRODUCT_DISCOUNT_FROM_CARD
            )
        )
        every { ticketJooqFinderService.getTicketModelsByIdIn(any()) } returns listOf(
            TicketModel(
                ticket = TICKET_3_ADULT_IMAX,
                ticketPrice = TICKET_3_TICKET_PRICE,
                isGroupTicket = false
            )
        )
        every { screeningJooqFinderService.findAllNonDeletedByIdIn(any()) } returns listOf(SCREENING_2_IMAX)
        every { movieJooqFinderService.findAllNonDeletedByIdIn(any()) } returns listOf(MOVIE_1)
        every { auditoriumJooqFinderService.findAllByIdIn(any()) } returns listOf(AUDITORIUM_2_IMAX)
        every { reservationJooqFinderService.findAllNonDeletedByIdIn(any()) } returns listOf(RESERVATION_3_IMAX)
        every { seatJooqFinderService.findAllByIdIn(any()) } returns listOf(SEAT_3_IMAX)
        every { basketItemProductService.prepareProductItemsForBasketModel(any()) } returns
            BasketItemProductService.ProductBasketItemsData(
                productItemModels = listOf(
                    BasketItemTaxRateModel(
                        basketItem = BASKET_ITEM_4_POPCORN,
                        taxRate = PRODUCT_CATEGORY_1_PRODUCT_STANDARD_TAX_RATE.taxRate
                    ),
                    BasketItemTaxRateModel(
                        basketItem = BASKET_ITEM_5_PRODUCT_DISCOUNT_FROM_CARD,
                        taxRate = PRODUCT_CATEGORY_2_PRODUCT_DISCOUNT_CARD.taxRate
                    )
                ),
                productIdToProduct = setOf(PRODUCT_1_POPCORN, PRODUCT_4_PRODUCT_DISCOUNT_FROM_CARD).associateBy { it.id },
                activeProductDiscountItem = BASKET_ITEM_5_PRODUCT_DISCOUNT_FROM_CARD
            )

        val generatedReceipt = underTest.generateReceipt(GenerateReceiptCommand(basketId = BASKET_2.id))
        assertEquals(EXPECTED_RECEIPT_3_TICKET_PRODUCT_PRODUCT_DISCOUNT.header.uuid, generatedReceipt.header.uuid)
        assertEquals(EXPECTED_RECEIPT_3_TICKET_PRODUCT_PRODUCT_DISCOUNT.data.size, generatedReceipt.data.size)

        val expectedProductReceiptData = EXPECTED_RECEIPT_3_TICKET_PRODUCT_PRODUCT_DISCOUNT.data[0]
        val expectedProductReceiptItem = expectedProductReceiptData.items.items[0]
        val expectedProductDiscountReceiptItem = expectedProductReceiptData.items.items[1]
        val expectedProductPayment = expectedProductReceiptData.payments.payment[0]
        val expectedTicketReceiptData = EXPECTED_RECEIPT_3_TICKET_PRODUCT_PRODUCT_DISCOUNT.data[1]
        val expectedTicketReceiptItem = expectedTicketReceiptData.items.items[0]
        val expectedTicketPayment = expectedTicketReceiptData.payments.payment[0]

        val generatedProductReceiptData = generatedReceipt.data[0]
        val generatedProductReceiptItem = generatedProductReceiptData.items.items[0]
        val generatedProductDiscountReceiptItem = generatedProductReceiptData.items.items[1]
        val generatedProductPayment = generatedProductReceiptData.payments.payment[0]
        val generatedTicketReceiptData = generatedReceipt.data[1]
        val generatedTicketReceiptItem = generatedTicketReceiptData.items.items[0]
        val generatedTicketPayment = generatedTicketReceiptData.payments.payment[0]

        assertReceiptDataEquals(expectedProductReceiptData, generatedProductReceiptData)
        assertReceiptItemEquals(expectedProductReceiptItem, generatedProductReceiptItem)
        assertReceiptItemEquals(expectedProductDiscountReceiptItem, generatedProductDiscountReceiptItem)
        assertPaymentEquals(expectedProductPayment, generatedProductPayment)

        assertReceiptDataEquals(expectedTicketReceiptData, generatedTicketReceiptData)
        assertReceiptItemEquals(expectedTicketReceiptItem, generatedTicketReceiptItem)
        assertPaymentEquals(expectedTicketPayment, generatedTicketPayment)
    }

    @Test
    fun `test generateReceipt - product item 3 pcs, one taxFree product and applied VIP card - should generate receipt with one product discount, taxFree product is not discounted`() {
        every { basketJpaFinderService.getNonDeletedWithItemsById(any()) } returns BasketModel(
            basket = BASKET_3,
            basketItems = listOf(
                BASKET_ITEM_6_THREE_POPCORNS,
                BASKET_ITEM_7_PRODUCT_DISCOUNT_FROM_CARD,
                BASKET_ITEM_8_PACKAGE_DEPOSIT
            )
        )
        every { ticketJooqFinderService.getTicketModelsByIdIn(any()) } returns listOf()
        every { screeningJooqFinderService.findAllNonDeletedByIdIn(any()) } returns listOf()
        every { movieJooqFinderService.findAllNonDeletedByIdIn(any()) } returns listOf()
        every { auditoriumJooqFinderService.findAllByIdIn(any()) } returns listOf()
        every { reservationJooqFinderService.findAllNonDeletedByIdIn(any()) } returns listOf()
        every { seatJooqFinderService.findAllByIdIn(any()) } returns listOf()
        every { basketItemProductService.prepareProductItemsForBasketModel(any()) } returns
            BasketItemProductService.ProductBasketItemsData(
                productItemModels = listOf(
                    BasketItemTaxRateModel(
                        basketItem = BASKET_ITEM_6_THREE_POPCORNS,
                        taxRate = PRODUCT_CATEGORY_1_PRODUCT_STANDARD_TAX_RATE.taxRate
                    ),
                    BasketItemTaxRateModel(
                        basketItem = BASKET_ITEM_8_PACKAGE_DEPOSIT,
                        taxRate = PRODUCT_CATEGORY_3_NO_TAX_RATE.taxRate
                    ),
                    BasketItemTaxRateModel(
                        basketItem = BASKET_ITEM_7_PRODUCT_DISCOUNT_FROM_CARD,
                        taxRate = PRODUCT_CATEGORY_2_PRODUCT_DISCOUNT_CARD.taxRate
                    )
                ),
                productIdToProduct = setOf(
                    PRODUCT_1_POPCORN,
                    PRODUCT_4_PRODUCT_DISCOUNT_FROM_CARD,
                    PRODUCT_2_PACKAGE_DEPOSIT
                ).associateBy { it.id },
                activeProductDiscountItem = BASKET_ITEM_7_PRODUCT_DISCOUNT_FROM_CARD
            )

        val generatedReceipt = underTest.generateReceipt(GenerateReceiptCommand(basketId = BASKET_3.id))
        assertEquals(EXPECTED_RECEIPT_4_DISCOUNTED_PRODUCTS_AND_TAX_FREE_PRODUCT.header.uuid, generatedReceipt.header.uuid)
        assertEquals(EXPECTED_RECEIPT_4_DISCOUNTED_PRODUCTS_AND_TAX_FREE_PRODUCT.data.size, generatedReceipt.data.size)

        val expectedProductReceiptData = EXPECTED_RECEIPT_4_DISCOUNTED_PRODUCTS_AND_TAX_FREE_PRODUCT.data[0]
        val expectedProductReceiptItem = expectedProductReceiptData.items.items[0]
        val expectedDepositReceiptItem = expectedProductReceiptData.items.items[1]
        val expectedProductDiscountReceiptItem = expectedProductReceiptData.items.items[2]
        val expectedProductPayment = expectedProductReceiptData.payments.payment[0]

        val generatedProductReceiptData = generatedReceipt.data[0]
        val generatedProductReceiptItem = generatedProductReceiptData.items.items[0]
        val generatedDepositReceiptItem = generatedProductReceiptData.items.items[1]
        val generatedProductDiscountReceiptItem = generatedProductReceiptData.items.items[2]
        val generatedProductPayment = generatedProductReceiptData.payments.payment[0]

        assertReceiptDataEquals(expectedProductReceiptData, generatedProductReceiptData)
        assertReceiptItemEquals(expectedProductReceiptItem, generatedProductReceiptItem)
        assertReceiptItemEquals(expectedDepositReceiptItem, generatedDepositReceiptItem)
        assertReceiptItemEquals(expectedProductDiscountReceiptItem, generatedProductDiscountReceiptItem)
        assertPaymentEquals(expectedProductPayment, generatedProductPayment)
    }

    @Test
    fun `test generateReceipt - two products with different VAT, percentage non-discount-card discount applied - should generate receipt with two product discounts with respective VAT`() {
        every { basketJpaFinderService.getNonDeletedWithItemsById(any()) } returns BasketModel(
            basket = BASKET_4,
            basketItems = listOf(
                BASKET_ITEM_4_POPCORN,
                BASKET_ITEM_9_MARGOT,
                BASKET_ITEM_10_PRODUCT_DISCOUNT_NON_CARD
            )
        )
        every { ticketJooqFinderService.getTicketModelsByIdIn(any()) } returns listOf()
        every { screeningJooqFinderService.findAllNonDeletedByIdIn(any()) } returns listOf()
        every { movieJooqFinderService.findAllNonDeletedByIdIn(any()) } returns listOf()
        every { auditoriumJooqFinderService.findAllByIdIn(any()) } returns listOf()
        every { reservationJooqFinderService.findAllNonDeletedByIdIn(any()) } returns listOf()
        every { seatJooqFinderService.findAllByIdIn(any()) } returns listOf()
        every { basketItemProductService.prepareProductItemsForBasketModel(any()) } returns
            BasketItemProductService.ProductBasketItemsData(
                productItemModels = listOf(
                    BasketItemTaxRateModel(
                        basketItem = BASKET_ITEM_4_POPCORN,
                        taxRate = PRODUCT_CATEGORY_1_PRODUCT_STANDARD_TAX_RATE.taxRate
                    ),
                    BasketItemTaxRateModel(
                        basketItem = BASKET_ITEM_9_MARGOT,
                        taxRate = PRODUCT_CATEGORY_4_REDUCED_TAX_RATE.taxRate
                    ),
                    BasketItemTaxRateModel(
                        basketItem = BASKET_ITEM_10_PRODUCT_DISCOUNT_NON_CARD,
                        taxRate = PRODUCT_CATEGORY_1_PRODUCT_STANDARD_TAX_RATE.taxRate
                    ),
                    BasketItemTaxRateModel(
                        basketItem = BASKET_ITEM_24_PRODUCT_DISCOUNT_NON_CARD,
                        taxRate = PRODUCT_CATEGORY_4_REDUCED_TAX_RATE.taxRate
                    )
                ),
                productIdToProduct = setOf(
                    PRODUCT_1_POPCORN,
                    PRODUCT_3_MARGOT,
                    PRODUCT_5_PRODUCT_DISCOUNT_NON_CARD
                ).associateBy { it.id },
                activeProductDiscountItem = BASKET_ITEM_10_PRODUCT_DISCOUNT_NON_CARD
            )

        val generatedReceipt = underTest.generateReceipt(GenerateReceiptCommand(basketId = BASKET_4.id))
        val gen = generatedReceipt.serializeToXml()
        val exp = EXPECTED_RECEIPT_5_PRODUCTS_DIFFERENT_TAX_RATE_AND_NON_CARD_DISCOUNT.serializeToXml()
        assertEquals(
            EXPECTED_RECEIPT_5_PRODUCTS_DIFFERENT_TAX_RATE_AND_NON_CARD_DISCOUNT.header.uuid,
            generatedReceipt.header.uuid
        )
        assertEquals(EXPECTED_RECEIPT_5_PRODUCTS_DIFFERENT_TAX_RATE_AND_NON_CARD_DISCOUNT.data.size, generatedReceipt.data.size)

        val expectedProductReceiptData = EXPECTED_RECEIPT_5_PRODUCTS_DIFFERENT_TAX_RATE_AND_NON_CARD_DISCOUNT.data[0]
        val expectedProduct1ReceiptItem = expectedProductReceiptData.items.items[0]
        val expectedProduct2ReceiptItem = expectedProductReceiptData.items.items[1]
        val expectedProductDiscountReceiptItem = expectedProductReceiptData.items.items[2]
        val expectedProductPayment = expectedProductReceiptData.payments.payment[0]

        val generatedProductReceiptData = generatedReceipt.data[0]
        val generatedProduct1ReceiptItem = generatedProductReceiptData.items.items[0]
        val generatedProduct2ReceiptItem = generatedProductReceiptData.items.items[1]
        val generatedProductDiscountReceiptItem = generatedProductReceiptData.items.items[2]
        val generatedProductPayment = generatedProductReceiptData.payments.payment[0]

        assertReceiptDataEquals(expectedProductReceiptData, generatedProductReceiptData)
        assertReceiptItemEquals(expectedProduct1ReceiptItem, generatedProduct1ReceiptItem)
        assertReceiptItemEquals(expectedProduct2ReceiptItem, generatedProduct2ReceiptItem)
        assertReceiptItemEquals(expectedProductDiscountReceiptItem, generatedProductDiscountReceiptItem)
        assertPaymentEquals(expectedProductPayment, generatedProductPayment)
    }

    @Test
    fun `test generateReceipt - products with VAT that differs from applied VIP card discount VAT - should generate receipt with one product discount with reduced VAT and updated tax attributes`() {
        every { basketJpaFinderService.getNonDeletedWithItemsById(any()) } returns BasketModel(
            basket = BASKET_5,
            basketItems = listOf(
                BASKET_ITEM_9_MARGOT,
                BASKET_ITEM_11_LATTE,
                BASKET_ITEM_12_PRODUCT_DISCOUNT_FROM_CARD
            )
        )
        every { ticketJooqFinderService.getTicketModelsByIdIn(any()) } returns listOf()
        every { screeningJooqFinderService.findAllNonDeletedByIdIn(any()) } returns listOf()
        every { movieJooqFinderService.findAllNonDeletedByIdIn(any()) } returns listOf()
        every { auditoriumJooqFinderService.findAllByIdIn(any()) } returns listOf()
        every { reservationJooqFinderService.findAllNonDeletedByIdIn(any()) } returns listOf()
        every { seatJooqFinderService.findAllByIdIn(any()) } returns listOf()
        every { basketItemProductService.prepareProductItemsForBasketModel(any()) } returns
            BasketItemProductService.ProductBasketItemsData(
                productItemModels = listOf(
                    BasketItemTaxRateModel(
                        basketItem = BASKET_ITEM_9_MARGOT,
                        taxRate = PRODUCT_CATEGORY_4_REDUCED_TAX_RATE.taxRate
                    ),
                    BasketItemTaxRateModel(
                        basketItem = BASKET_ITEM_11_LATTE,
                        taxRate = PRODUCT_CATEGORY_7_PRODUCT_REDUCED_TAX_RATE_COFFEE.taxRate
                    ),
                    BasketItemTaxRateModel(
                        basketItem = BASKET_ITEM_12_PRODUCT_DISCOUNT_FROM_CARD,
                        taxRate = REDUCED_TAX_RATE
                    )
                ),
                productIdToProduct = setOf(
                    PRODUCT_3_MARGOT,
                    PRODUCT_6_LATTE,
                    PRODUCT_4_PRODUCT_DISCOUNT_FROM_CARD
                ).associateBy { it.id },
                activeProductDiscountItem = BASKET_ITEM_12_PRODUCT_DISCOUNT_FROM_CARD
            )

        val generatedReceipt = underTest.generateReceipt(GenerateReceiptCommand(basketId = BASKET_5.id))
        assertEquals(
            EXPECTED_RECEIPT_6_PRODUCTS_REDUCED_TAX_RATE_AND_DISCOUNT_FROM_CARD.header.uuid,
            generatedReceipt.header.uuid
        )
        assertEquals(EXPECTED_RECEIPT_6_PRODUCTS_REDUCED_TAX_RATE_AND_DISCOUNT_FROM_CARD.data.size, generatedReceipt.data.size)

        val expectedProductReceiptData = EXPECTED_RECEIPT_6_PRODUCTS_REDUCED_TAX_RATE_AND_DISCOUNT_FROM_CARD.data[0]
        val expectedProduct1ReceiptItem = expectedProductReceiptData.items.items[0]
        val expectedProduct2ReceiptItem = expectedProductReceiptData.items.items[1]
        val expectedProductDiscountReceiptItem = expectedProductReceiptData.items.items[2]
        val expectedProductPayment = expectedProductReceiptData.payments.payment[0]

        val generatedProductReceiptData = generatedReceipt.data[0]
        val generatedProduct1ReceiptItem = generatedProductReceiptData.items.items[0]
        val generatedProduct2ReceiptItem = generatedProductReceiptData.items.items[1]
        val generatedProductDiscountReceiptItem = generatedProductReceiptData.items.items[2]
        val generatedProductPayment = generatedProductReceiptData.payments.payment[0]

        assertReceiptDataEquals(expectedProductReceiptData, generatedProductReceiptData)
        assertReceiptItemEquals(expectedProduct1ReceiptItem, generatedProduct1ReceiptItem)
        assertReceiptItemEquals(expectedProduct2ReceiptItem, generatedProduct2ReceiptItem)
        assertReceiptItemEquals(expectedProductDiscountReceiptItem, generatedProductDiscountReceiptItem)
        assertPaymentEquals(expectedProductPayment, generatedProductPayment)
    }

    @Test
    fun `test generateReceipt - products with different VAT and discount card applied - should generate receipt with two product discounts and updated tax attributes`() {
        every { basketJpaFinderService.getNonDeletedWithItemsById(any()) } returns BasketModel(
            basket = BASKET_6,
            basketItems = listOf(
                BASKET_ITEM_20_PRODUCT_DISCOUNT_ISOLATED,
                BASKET_ITEM_19_POPCORN_ISOLATED,
                BASKET_ITEM_4_POPCORN,
                BASKET_ITEM_9_MARGOT,
                BASKET_ITEM_13_PRODUCT_DISCOUNT_FROM_CARD
            )
        )
        every { ticketJooqFinderService.getTicketModelsByIdIn(any()) } returns listOf()
        every { screeningJooqFinderService.findAllNonDeletedByIdIn(any()) } returns listOf()
        every { movieJooqFinderService.findAllNonDeletedByIdIn(any()) } returns listOf()
        every { auditoriumJooqFinderService.findAllByIdIn(any()) } returns listOf()
        every { reservationJooqFinderService.findAllNonDeletedByIdIn(any()) } returns listOf()
        every { seatJooqFinderService.findAllByIdIn(any()) } returns listOf()
        every { basketItemProductService.prepareProductItemsForBasketModel(any()) } returns
            BasketItemProductService.ProductBasketItemsData(
                productItemModels = listOf(
                    BasketItemTaxRateModel(
                        basketItem = BASKET_ITEM_19_POPCORN_ISOLATED,
                        taxRate = PRODUCT_CATEGORY_1_PRODUCT_STANDARD_TAX_RATE.taxRate
                    ),
                    BasketItemTaxRateModel(
                        basketItem = BASKET_ITEM_20_PRODUCT_DISCOUNT_ISOLATED,
                        taxRate = PRODUCT_CATEGORY_2_PRODUCT_DISCOUNT_CARD.taxRate
                    ),
                    BasketItemTaxRateModel(
                        basketItem = BASKET_ITEM_4_POPCORN,
                        taxRate = PRODUCT_CATEGORY_1_PRODUCT_STANDARD_TAX_RATE.taxRate
                    ),
                    BasketItemTaxRateModel(
                        basketItem = BASKET_ITEM_9_MARGOT,
                        taxRate = PRODUCT_CATEGORY_4_REDUCED_TAX_RATE.taxRate
                    ),
                    BasketItemTaxRateModel(
                        basketItem = BASKET_ITEM_14_PRODUCT_DISCOUNT_FROM_CARD_STANDARD_VAT,
                        taxRate = PRODUCT_CATEGORY_2_PRODUCT_DISCOUNT_CARD.taxRate
                    ),
                    BasketItemTaxRateModel(
                        basketItem = BASKET_ITEM_15_PRODUCT_DISCOUNT_FROM_CARD_REDUCED_VAT,
                        taxRate = REDUCED_TAX_RATE
                    )
                ),
                productIdToProduct = setOf(
                    PRODUCT_6_PRODUCT_DISCOUNT_ISOLATED,
                    PRODUCT_1_POPCORN,
                    PRODUCT_3_MARGOT,
                    PRODUCT_4_PRODUCT_DISCOUNT_FROM_CARD
                ).associateBy { it.id },
                activeProductDiscountItem = BASKET_ITEM_13_PRODUCT_DISCOUNT_FROM_CARD
            )

        val generatedReceipt = underTest.generateReceipt(GenerateReceiptCommand(basketId = BASKET_6.id))
        assertEquals(
            EXPECTED_RECEIPT_7_PRODUCTS_DIFFERENT_TAX_RATE_AND_DISCOUNT_FROM_CARD.header.uuid,
            generatedReceipt.header.uuid
        )
        assertEquals(EXPECTED_RECEIPT_7_PRODUCTS_DIFFERENT_TAX_RATE_AND_DISCOUNT_FROM_CARD.data.size, generatedReceipt.data.size)

        val expectedProductReceiptData = EXPECTED_RECEIPT_7_PRODUCTS_DIFFERENT_TAX_RATE_AND_DISCOUNT_FROM_CARD.data[0]
        val expectedProduct1ReceiptItem = expectedProductReceiptData.items.items[0]
        val expectedProduct2ReceiptItem = expectedProductReceiptData.items.items[1]
        val expectedProduct3ReceiptItem = expectedProductReceiptData.items.items[2]
        val expectedProduct4ReceiptItem = expectedProductReceiptData.items.items[3]
        val expectedProductDiscount1ReceiptItem = expectedProductReceiptData.items.items[4]
        val expectedProductDiscount2ReceiptItem = expectedProductReceiptData.items.items[5]
        val expectedProductPayment = expectedProductReceiptData.payments.payment[0]

        val generatedProductReceiptData = generatedReceipt.data[0]
        val generatedProduct1ReceiptItem = generatedProductReceiptData.items.items[0]
        val generatedProduct2ReceiptItem = generatedProductReceiptData.items.items[1]
        val generatedProduct3ReceiptItem = generatedProductReceiptData.items.items[2]
        val generatedProduct4ReceiptItem = generatedProductReceiptData.items.items[3]
        val generatedProductDiscount1ReceiptItem = generatedProductReceiptData.items.items[4]
        val generatedProductDiscount2ReceiptItem = generatedProductReceiptData.items.items[5]
        val generatedProductPayment = generatedProductReceiptData.payments.payment[0]

        assertReceiptDataEquals(expectedProductReceiptData, generatedProductReceiptData)
        assertReceiptItemEquals(expectedProduct1ReceiptItem, generatedProduct1ReceiptItem)
        assertReceiptItemEquals(expectedProduct2ReceiptItem, generatedProduct2ReceiptItem)
        assertReceiptItemEquals(expectedProduct3ReceiptItem, generatedProduct3ReceiptItem)
        assertReceiptItemEquals(expectedProduct4ReceiptItem, generatedProduct4ReceiptItem)
        assertReceiptItemEquals(expectedProductDiscount1ReceiptItem, generatedProductDiscount1ReceiptItem)
        assertReceiptItemEquals(expectedProductDiscount2ReceiptItem, generatedProductDiscount2ReceiptItem)
        assertPaymentEquals(expectedProductPayment, generatedProductPayment)
    }

    @Test
    fun `test generateReceipt - basket with two products - should generate receipt with two products`() {
        every { basketJpaFinderService.getNonDeletedWithItemsById(any()) } returns BasketModel(
            basket = BASKET_7,
            basketItems = listOf(
                BASKET_ITEM_4_POPCORN,
                BASKET_ITEM_9_MARGOT
            )
        )
        every { ticketJooqFinderService.getTicketModelsByIdIn(any()) } returns listOf()
        every { screeningJooqFinderService.findAllNonDeletedByIdIn(any()) } returns listOf()
        every { movieJooqFinderService.findAllNonDeletedByIdIn(any()) } returns listOf()
        every { auditoriumJooqFinderService.findAllByIdIn(any()) } returns listOf()
        every { reservationJooqFinderService.findAllNonDeletedByIdIn(any()) } returns listOf()
        every { seatJooqFinderService.findAllByIdIn(any()) } returns listOf()
        every { basketItemProductService.prepareProductItemsForBasketModel(any()) } returns
            BasketItemProductService.ProductBasketItemsData(
                productItemModels = listOf(
                    BasketItemTaxRateModel(
                        basketItem = BASKET_ITEM_4_POPCORN,
                        taxRate = PRODUCT_CATEGORY_1_PRODUCT_STANDARD_TAX_RATE.taxRate
                    ),
                    BasketItemTaxRateModel(
                        basketItem = BASKET_ITEM_9_MARGOT,
                        taxRate = PRODUCT_CATEGORY_4_REDUCED_TAX_RATE.taxRate
                    )
                ),
                productIdToProduct = setOf(
                    PRODUCT_1_POPCORN,
                    PRODUCT_3_MARGOT
                ).associateBy { it.id },
                activeProductDiscountItem = null
            )

        val generatedReceipt = underTest.generateReceipt(GenerateReceiptCommand(basketId = BASKET_7.id))

        assertEquals(EXPECTED_RECEIPT_8_PRODUCTS_DIFFERENT_TAX_RATE.header.uuid, generatedReceipt.header.uuid)
        assertEquals(EXPECTED_RECEIPT_8_PRODUCTS_DIFFERENT_TAX_RATE.data.size, generatedReceipt.data.size)

        val expectedProductReceiptData = EXPECTED_RECEIPT_8_PRODUCTS_DIFFERENT_TAX_RATE.data[0]
        val expectedProduct1ReceiptItem = expectedProductReceiptData.items.items[0]
        val expectedProduct2ReceiptItem = expectedProductReceiptData.items.items[1]
        val expectedProductPayment = expectedProductReceiptData.payments.payment[0]

        val generatedProductReceiptData = generatedReceipt.data[0]
        val generatedProduct1ReceiptItem = generatedProductReceiptData.items.items[0]
        val generatedProduct2ReceiptItem = generatedProductReceiptData.items.items[1]
        val generatedProductPayment = generatedProductReceiptData.payments.payment[0]

        assertReceiptDataEquals(expectedProductReceiptData, generatedProductReceiptData)
        assertReceiptItemEquals(expectedProduct1ReceiptItem, generatedProduct1ReceiptItem)
        assertReceiptItemEquals(expectedProduct2ReceiptItem, generatedProduct2ReceiptItem)
        assertPaymentEquals(expectedProductPayment, generatedProductPayment)
    }

    @Test
    fun `test generateReceipt - basket with products in all tax rates - should generate receipt with products in all rates`() {
        every { basketJpaFinderService.getNonDeletedWithItemsById(any()) } returns BasketModel(
            basket = BASKET_7,
            basketItems = listOf(
                BASKET_ITEM_4_POPCORN,
                BASKET_ITEM_9_MARGOT,
                BASKET_ITEM_23_CAPPUCCINO
            )
        )
        every { ticketJooqFinderService.getTicketModelsByIdIn(any()) } returns listOf()
        every { screeningJooqFinderService.findAllNonDeletedByIdIn(any()) } returns listOf()
        every { movieJooqFinderService.findAllNonDeletedByIdIn(any()) } returns listOf()
        every { auditoriumJooqFinderService.findAllByIdIn(any()) } returns listOf()
        every { reservationJooqFinderService.findAllNonDeletedByIdIn(any()) } returns listOf()
        every { seatJooqFinderService.findAllByIdIn(any()) } returns listOf()
        every { basketItemProductService.prepareProductItemsForBasketModel(any()) } returns
            BasketItemProductService.ProductBasketItemsData(
                productItemModels = listOf(
                    BasketItemTaxRateModel(
                        basketItem = BASKET_ITEM_4_POPCORN,
                        taxRate = PRODUCT_CATEGORY_1_PRODUCT_STANDARD_TAX_RATE.taxRate
                    ),
                    BasketItemTaxRateModel(
                        basketItem = BASKET_ITEM_9_MARGOT,
                        taxRate = PRODUCT_CATEGORY_4_REDUCED_TAX_RATE.taxRate
                    ),
                    BasketItemTaxRateModel(
                        basketItem = BASKET_ITEM_23_CAPPUCCINO,
                        taxRate = PRODUCT_CATEGORY_5_SUPER_REDUCED_TAX_RATE.taxRate
                    )
                ),
                productIdToProduct = setOf(
                    PRODUCT_1_POPCORN,
                    PRODUCT_3_MARGOT,
                    PRODUCT_7_CAPPUCCINO
                ).associateBy { it.id },
                activeProductDiscountItem = null
            )

        val generatedReceipt = underTest.generateReceipt(GenerateReceiptCommand(basketId = BASKET_7.id))

        assertEquals(EXPECTED_RECEIPT_10_ALL_TAX_RATES.header.uuid, generatedReceipt.header.uuid)
        assertEquals(EXPECTED_RECEIPT_10_ALL_TAX_RATES.data.size, generatedReceipt.data.size)

        val expectedProductReceiptData = EXPECTED_RECEIPT_10_ALL_TAX_RATES.data[0]
        val expectedProduct1ReceiptItem = expectedProductReceiptData.items.items[0]
        val expectedProduct2ReceiptItem = expectedProductReceiptData.items.items[1]
        val expectedProduct3ReceiptItem = expectedProductReceiptData.items.items[2]
        val expectedProductPayment = expectedProductReceiptData.payments.payment[0]

        val generatedProductReceiptData = generatedReceipt.data[0]
        val generatedProduct1ReceiptItem = generatedProductReceiptData.items.items[0]
        val generatedProduct2ReceiptItem = generatedProductReceiptData.items.items[1]
        val generatedProduct3ReceiptItem = generatedProductReceiptData.items.items[2]
        val generatedProductPayment = generatedProductReceiptData.payments.payment[0]

        assertReceiptDataEquals(expectedProductReceiptData, generatedProductReceiptData)
        assertReceiptItemEquals(expectedProduct1ReceiptItem, generatedProduct1ReceiptItem)
        assertReceiptItemEquals(expectedProduct2ReceiptItem, generatedProduct2ReceiptItem)
        assertReceiptItemEquals(expectedProduct3ReceiptItem, generatedProduct3ReceiptItem)
        assertPaymentEquals(expectedProductPayment, generatedProductPayment)
    }

    @Test
    fun `test generateReceipt - basket with two products and two isolated groups - should generate receipt`() {
        every { basketJpaFinderService.getNonDeletedWithItemsById(any()) } returns BasketModel(
            basket = BASKET_7,
            basketItems = listOf(
                BASKET_ITEM_20_PRODUCT_DISCOUNT_ISOLATED,
                BASKET_ITEM_19_POPCORN_ISOLATED,
                BASKET_ITEM_4_POPCORN,
                BASKET_ITEM_22_PRODUCT_DISCOUNT_ISOLATED,
                BASKET_ITEM_21_POPCORN_ISOLATED,
                BASKET_ITEM_9_MARGOT
            )
        )
        every { ticketJooqFinderService.getTicketModelsByIdIn(any()) } returns listOf()
        every { screeningJooqFinderService.findAllNonDeletedByIdIn(any()) } returns listOf()
        every { movieJooqFinderService.findAllNonDeletedByIdIn(any()) } returns listOf()
        every { auditoriumJooqFinderService.findAllByIdIn(any()) } returns listOf()
        every { reservationJooqFinderService.findAllNonDeletedByIdIn(any()) } returns listOf()
        every { seatJooqFinderService.findAllByIdIn(any()) } returns listOf()
        every { basketItemProductService.prepareProductItemsForBasketModel(any()) } returns
            BasketItemProductService.ProductBasketItemsData(
                productItemModels = listOf(
                    BasketItemTaxRateModel(
                        basketItem = BASKET_ITEM_19_POPCORN_ISOLATED,
                        taxRate = PRODUCT_CATEGORY_1_PRODUCT_STANDARD_TAX_RATE.taxRate
                    ),
                    BasketItemTaxRateModel(
                        basketItem = BASKET_ITEM_20_PRODUCT_DISCOUNT_ISOLATED,
                        taxRate = PRODUCT_CATEGORY_2_PRODUCT_DISCOUNT_CARD.taxRate
                    ),
                    BasketItemTaxRateModel(
                        basketItem = BASKET_ITEM_4_POPCORN,
                        taxRate = PRODUCT_CATEGORY_1_PRODUCT_STANDARD_TAX_RATE.taxRate
                    ),
                    BasketItemTaxRateModel(
                        basketItem = BASKET_ITEM_9_MARGOT,
                        taxRate = PRODUCT_CATEGORY_4_REDUCED_TAX_RATE.taxRate
                    ),
                    BasketItemTaxRateModel(
                        basketItem = BASKET_ITEM_21_POPCORN_ISOLATED,
                        taxRate = PRODUCT_CATEGORY_1_PRODUCT_STANDARD_TAX_RATE.taxRate
                    ),
                    BasketItemTaxRateModel(
                        basketItem = BASKET_ITEM_22_PRODUCT_DISCOUNT_ISOLATED,
                        taxRate = PRODUCT_CATEGORY_2_PRODUCT_DISCOUNT_CARD.taxRate
                    )
                ),
                productIdToProduct = setOf(
                    PRODUCT_6_PRODUCT_DISCOUNT_ISOLATED,
                    PRODUCT_1_POPCORN,
                    PRODUCT_3_MARGOT
                ).associateBy { it.id },
                activeProductDiscountItem = null
            )

        val generatedReceipt = underTest.generateReceipt(GenerateReceiptCommand(basketId = BASKET_7.id))

        assertEquals(EXPECTED_RECEIPT_9_PRODUCTS_AND_ISOLATED_GROUPS.header.uuid, generatedReceipt.header.uuid)
        assertEquals(EXPECTED_RECEIPT_9_PRODUCTS_AND_ISOLATED_GROUPS.data.size, generatedReceipt.data.size)

        val expectedProductReceiptData = EXPECTED_RECEIPT_9_PRODUCTS_AND_ISOLATED_GROUPS.data[0]
        val expectedProduct1ReceiptItem = expectedProductReceiptData.items.items[0]
        val expectedProduct2ReceiptItem = expectedProductReceiptData.items.items[1]
        val expectedProduct3ReceiptItem = expectedProductReceiptData.items.items[2]
        val expectedProduct4ReceiptItem = expectedProductReceiptData.items.items[3]
        val expectedProduct5ReceiptItem = expectedProductReceiptData.items.items[4]
        val expectedProduct6ReceiptItem = expectedProductReceiptData.items.items[5]
        val expectedProductPayment = expectedProductReceiptData.payments.payment[0]

        val generatedProductReceiptData = generatedReceipt.data[0]
        val generatedProduct1ReceiptItem = generatedProductReceiptData.items.items[0]
        val generatedProduct2ReceiptItem = generatedProductReceiptData.items.items[1]
        val generatedProduct3ReceiptItem = generatedProductReceiptData.items.items[2]
        val generatedProduct4ReceiptItem = generatedProductReceiptData.items.items[3]
        val generatedProduct5ReceiptItem = generatedProductReceiptData.items.items[4]
        val generatedProduct6ReceiptItem = generatedProductReceiptData.items.items[5]
        val generatedProductPayment = generatedProductReceiptData.payments.payment[0]

        assertReceiptDataEquals(expectedProductReceiptData, generatedProductReceiptData)
        assertReceiptItemEquals(expectedProduct1ReceiptItem, generatedProduct1ReceiptItem)
        assertReceiptItemEquals(expectedProduct2ReceiptItem, generatedProduct2ReceiptItem)
        assertReceiptItemEquals(expectedProduct3ReceiptItem, generatedProduct3ReceiptItem)
        assertReceiptItemEquals(expectedProduct4ReceiptItem, generatedProduct4ReceiptItem)
        assertReceiptItemEquals(expectedProduct5ReceiptItem, generatedProduct5ReceiptItem)
        assertReceiptItemEquals(expectedProduct6ReceiptItem, generatedProduct6ReceiptItem)
        assertPaymentEquals(expectedProductPayment, generatedProductPayment)
    }

    @Test
    fun `test generateCancellationReceipt - basket with two tickets - should generate cancellation receipt with two tickets`() {
        prepareTicketData(areItemsCancelled = true)

        val generatedReceipt = underTest.generateCancellationReceipt(
            GenerateCancellationReceiptCommand(basketId = BASKET_1.id)
        )

        assertEquals(EXPECTED_CANCELLATION_RECEIPT_1_STUDENT_AND_ADULT_TICKET.header.uuid, generatedReceipt.header.uuid)
        assertEquals(EXPECTED_CANCELLATION_RECEIPT_1_STUDENT_AND_ADULT_TICKET.data.size, generatedReceipt.data.size)

        val expectedAdultTicketReceiptData = EXPECTED_CANCELLATION_RECEIPT_1_STUDENT_AND_ADULT_TICKET.data[0]
        val expectedAdultTicketReceiptItem = expectedAdultTicketReceiptData.items.items[0]
        val expectedAdultTicketPayment = expectedAdultTicketReceiptData.payments.payment[0]
        val expectedStudentTicketReceiptData = EXPECTED_CANCELLATION_RECEIPT_1_STUDENT_AND_ADULT_TICKET.data[1]
        val expectedStudentTicketReceiptItem = expectedStudentTicketReceiptData.items.items[0]
        val expectedStudentTicketPayment = expectedStudentTicketReceiptData.payments.payment[0]

        val generatedAdultTicketReceiptData = generatedReceipt.data[0]
        val generatedAdultTicketReceiptItem = generatedAdultTicketReceiptData.items.items[0]
        val generatedAdultTicketPayment = generatedAdultTicketReceiptData.payments.payment[0]
        val generatedStudentTicketReceiptData = generatedReceipt.data[1]
        val generatedStudentTicketReceiptItem = generatedStudentTicketReceiptData.items.items[0]
        val generatedStudentTicketPayment = generatedStudentTicketReceiptData.payments.payment[0]

        assertReceiptDataEquals(expectedAdultTicketReceiptData, generatedAdultTicketReceiptData)
        assertReceiptItemEquals(expectedAdultTicketReceiptItem, generatedAdultTicketReceiptItem)
        assertPaymentEquals(expectedAdultTicketPayment, generatedAdultTicketPayment)

        assertReceiptDataEquals(expectedStudentTicketReceiptData, generatedStudentTicketReceiptData)
        assertReceiptItemEquals(expectedStudentTicketReceiptItem, generatedStudentTicketReceiptItem)
        assertPaymentEquals(expectedStudentTicketPayment, generatedStudentTicketPayment)
    }

    @Test
    fun `test generateCancellationReceipt - basket with product - should generate cancellation receipt`() {
        every { basketJpaFinderService.getNonDeletedWithCancelledItemsById(any()) } returns BasketModel(
            basket = BASKET_2,
            basketItems = listOf(
                BASKET_ITEM_18_CANCELLED_POPCORN
            )
        )
        every { ticketJooqFinderService.getTicketModelsByIdIn(any()) } returns listOf()
        every { screeningJooqFinderService.findAllNonDeletedByIdIn(any()) } returns listOf()
        every { movieJooqFinderService.findAllNonDeletedByIdIn(any()) } returns listOf()
        every { auditoriumJooqFinderService.findAllByIdIn(any()) } returns listOf()
        every { reservationJooqFinderService.findAllNonDeletedByIdIn(any()) } returns listOf()
        every { seatJooqFinderService.findAllByIdIn(any()) } returns listOf()
        every { basketItemProductService.prepareCancelledProductItemsForBasketModel(any()) } returns
            BasketItemProductService.ProductBasketItemsData(
                productItemModels = listOf(
                    BasketItemTaxRateModel(
                        basketItem = BASKET_ITEM_18_CANCELLED_POPCORN,
                        taxRate = PRODUCT_CATEGORY_1_PRODUCT_STANDARD_TAX_RATE.taxRate
                    )
                ),
                productIdToProduct = setOf(PRODUCT_1_POPCORN).associateBy { it.id }
            )

        val generatedReceipt = underTest.generateCancellationReceipt(
            GenerateCancellationReceiptCommand(basketId = BASKET_2.id)
        )
        assertEquals(EXPECTED_CANCELLATION_RECEIPT_2_PRODUCT.header.uuid, generatedReceipt.header.uuid)
        assertEquals(EXPECTED_CANCELLATION_RECEIPT_2_PRODUCT.data.size, generatedReceipt.data.size)

        val expectedProductReceiptData = EXPECTED_CANCELLATION_RECEIPT_2_PRODUCT.data[0]
        val expectedProductReceiptItem = expectedProductReceiptData.items.items[0]
        val expectedProductPayment = expectedProductReceiptData.payments.payment[0]

        val generatedProductReceiptData = generatedReceipt.data[0]
        val generatedProductReceiptItem = generatedProductReceiptData.items.items[0]
        val generatedProductPayment = generatedProductReceiptData.payments.payment[0]

        assertReceiptDataEquals(expectedProductReceiptData, generatedProductReceiptData)
        assertReceiptItemEquals(expectedProductReceiptItem, generatedProductReceiptItem)
        assertPaymentEquals(expectedProductPayment, generatedProductPayment)
    }

    @Test
    fun `test generateCancellationReceipt - basket with 2 products - should generate cancellation receipt`() {
        every { basketJpaFinderService.getNonDeletedWithCancelledItemsById(any()) } returns BasketModel(
            basket = BASKET_8,
            basketItems = listOf(
                BASKET_ITEM_18_CANCELLED_POPCORN,
                BASKET_ITEM_25_CANCELLED_CAPPUCCINO
            )
        )
        every { ticketJooqFinderService.getTicketModelsByIdIn(any()) } returns listOf()
        every { screeningJooqFinderService.findAllNonDeletedByIdIn(any()) } returns listOf()
        every { movieJooqFinderService.findAllNonDeletedByIdIn(any()) } returns listOf()
        every { auditoriumJooqFinderService.findAllByIdIn(any()) } returns listOf()
        every { reservationJooqFinderService.findAllNonDeletedByIdIn(any()) } returns listOf()
        every { seatJooqFinderService.findAllByIdIn(any()) } returns listOf()
        every { basketItemProductService.prepareCancelledProductItemsForBasketModel(any()) } returns
            BasketItemProductService.ProductBasketItemsData(
                productItemModels = listOf(
                    BasketItemTaxRateModel(
                        basketItem = BASKET_ITEM_18_CANCELLED_POPCORN,
                        taxRate = PRODUCT_CATEGORY_1_PRODUCT_STANDARD_TAX_RATE.taxRate
                    ),
                    BasketItemTaxRateModel(
                        basketItem = BASKET_ITEM_25_CANCELLED_CAPPUCCINO,
                        taxRate = PRODUCT_CATEGORY_5_SUPER_REDUCED_TAX_RATE.taxRate
                    )
                ),
                productIdToProduct = setOf(PRODUCT_1_POPCORN, PRODUCT_7_CAPPUCCINO).associateBy { it.id }
            )

        val generatedReceipt = underTest.generateCancellationReceipt(
            GenerateCancellationReceiptCommand(basketId = BASKET_8.id)
        )
        assertEquals(EXPECTED_CANCELLATION_RECEIPT_3_PRODUCTS.header.uuid, generatedReceipt.header.uuid)
        assertEquals(EXPECTED_CANCELLATION_RECEIPT_3_PRODUCTS.data.size, generatedReceipt.data.size)

        val expectedProductReceiptData = EXPECTED_CANCELLATION_RECEIPT_3_PRODUCTS.data[0]
        val expectedProductReceiptItem1 = expectedProductReceiptData.items.items[0]
        val expectedProductReceiptItem2 = expectedProductReceiptData.items.items[1]
        val expectedProductPayment = expectedProductReceiptData.payments.payment[0]

        val generatedProductReceiptData = generatedReceipt.data[0]
        val generatedProductReceiptItem1 = generatedProductReceiptData.items.items[0]
        val generatedProductReceiptItem2 = generatedProductReceiptData.items.items[1]
        val generatedProductPayment = generatedProductReceiptData.payments.payment[0]

        assertReceiptDataEquals(expectedProductReceiptData, generatedProductReceiptData)
        assertReceiptItemEquals(expectedProductReceiptItem1, generatedProductReceiptItem1)
        assertReceiptItemEquals(expectedProductReceiptItem2, generatedProductReceiptItem2)
        assertPaymentEquals(expectedProductPayment, generatedProductPayment)
    }

    private fun prepareTicketData(isGroupTicket: Boolean = false, areItemsCancelled: Boolean = false) {
        if (areItemsCancelled) {
            every { basketJpaFinderService.getNonDeletedWithCancelledItemsById(any()) } returns BasketModel(
                basket = BASKET_1,
                basketItems = listOf(BASKET_ITEM_16_CANCELLED_BASKET_ITEM_1, BASKET_ITEM_17_CANCELLED_BASKET_ITEM_2)
            )
        } else {
            every { basketJpaFinderService.getNonDeletedWithItemsById(any()) } returns BasketModel(
                basket = BASKET_1,
                basketItems = listOf(BASKET_ITEM_1_ADULT_TICKET, BASKET_ITEM_2_STUDENT_TICKET)
            )
        }
        every { posConfigurationJooqFinderService.findById(any()) } returns POS_CONFIGURATION
        every { ticketJooqFinderService.getTicketModelsByIdIn(any()) } returns listOf(
            TicketModel(
                ticket = TICKET_1_ADULT,
                ticketPrice = TICKET_1_TICKET_PRICE,
                isGroupTicket = isGroupTicket
            ),
            TicketModel(
                ticket = TICKET_2_STUDENT,
                ticketPrice = TICKET_2_TICKET_PRICE,
                isGroupTicket = isGroupTicket
            )
        )
        every { screeningJooqFinderService.findAllNonDeletedByIdIn(any()) } returns listOf(SCREENING_1)
        every { movieJooqFinderService.findAllNonDeletedByIdIn(any()) } returns listOf(MOVIE_1)
        every { auditoriumJooqFinderService.findAllByIdIn(any()) } returns listOf(AUDITORIUM_1)
        every { reservationJooqFinderService.findAllNonDeletedByIdIn(any()) } returns listOf(RESERVATION_1, RESERVATION_2)
        every { seatJooqFinderService.findAllByIdIn(any()) } returns listOf(SEAT_1, SEAT_2)

        if (areItemsCancelled) {
            every { basketItemProductService.prepareCancelledProductItemsForBasketModel(any()) } returns
                BasketItemProductService.ProductBasketItemsData(
                    productItemModels = listOf(),
                    productIdToProduct = mapOf(),
                    activeProductDiscountItem = null
                )
        } else {
            every { basketItemProductService.prepareProductItemsForBasketModel(any()) } returns
                BasketItemProductService.ProductBasketItemsData(
                    productItemModels = listOf(),
                    productIdToProduct = mapOf(),
                    activeProductDiscountItem = null
                )
        }
    }

    private fun assertReceiptDataEquals(expectedData: SvkXmlReceiptData, generatedData: SvkXmlReceiptData) {
        assertEquals(expectedData.number, generatedData.number)
        assertTrue(expectedData.amount isEqualTo generatedData.amount)
        assertTrue(expectedData.taxBaseBasic isEqualTo generatedData.taxBaseBasic)
        assertTrue(expectedData.taxBaseReduced isEqualTo generatedData.taxBaseReduced)
        assertTrue(expectedData.basicVatAmount isEqualTo generatedData.basicVatAmount)
        assertTrue(expectedData.reducedVatAmount isEqualTo generatedData.reducedVatAmount)
        assertTrue(expectedData.taxFreeAmount isEqualTo generatedData.taxFreeAmount)
    }

    private fun assertReceiptItemEquals(expectedItem: SvkXmlReceiptItem, generatedItem: SvkXmlReceiptItem) {
        assertEquals(expectedItem.name, generatedItem.name)
        assertEquals(expectedItem.qr, generatedItem.qr)
        assertEquals(expectedItem.description, generatedItem.description)
        assertEquals(expectedItem.type, generatedItem.type)
        assertTrue(expectedItem.quantity isEqualTo generatedItem.quantity)
        assertEquals(expectedItem.vatRate, generatedItem.vatRate)
        assertTrue(expectedItem.unitPrice isEqualTo generatedItem.unitPrice)
        assertTrue(expectedItem.price isEqualTo generatedItem.price)
    }

    private fun assertPaymentEquals(expectedPayment: SvkXmlReceiptPayment, generatedPayment: SvkXmlReceiptPayment) {
        assertTrue(expectedPayment.amount isEqualTo generatedPayment.amount)
        assertEquals(expectedPayment.paymentType, generatedPayment.paymentType)
    }
}

private val POS_CONFIGURATION = createPosConfiguration(title = "pokl4")
private val DISTRIBUTOR_1_ID = UUID.randomUUID()
private val AUDITORIUM_1 = createAuditorium(
    code = "A",
    title = "SÁLA A - CINEMAX BRATISLAVA"
)
private val AUDITORIUM_2_IMAX = createAuditorium(
    originalId = 2,
    code = "IMAX",
    title = "IMAX - CINEMAX BRATISLAVA"
)
private val AUDITORIUM_LAYOUT_1 = createAuditoriumLayout(originalId = 1, auditoriumId = AUDITORIUM_1.id, code = "01")
private val AUDITORIUM_LAYOUT_2 = createAuditoriumLayout(originalId = 2, auditoriumId = AUDITORIUM_2_IMAX.id, code = "02")

private val MOVIE_1 = createMovie(
    title = "Minulé životy",
    rawTitle = "Minulé životy 2D (OT)",
    distributorId = DISTRIBUTOR_1_ID
)
private val SCREENING_1 = createScreening(
    auditoriumId = AUDITORIUM_1.id,
    movieId = MOVIE_1.id,
    date = LocalDate.of(2023, 10, 9),
    time = LocalTime.of(20, 0, 0)
)
private val SCREENING_2_IMAX = createScreening(
    auditoriumId = AUDITORIUM_2_IMAX.id,
    movieId = MOVIE_1.id,
    date = LocalDate.of(2023, 10, 10),
    time = LocalTime.of(20, 0, 0)
)
private val SEAT_1 = createSeat(
    auditoriumId = AUDITORIUM_1.id,
    auditoriumLayoutId = AUDITORIUM_LAYOUT_1.id,
    row = "6",
    number = "4",
    type = SeatType.REGULAR
)
private val SEAT_2 = createSeat(
    auditoriumId = AUDITORIUM_1.id,
    auditoriumLayoutId = AUDITORIUM_LAYOUT_1.id,
    row = "6",
    number = "5",
    type = SeatType.REGULAR
)
private val SEAT_3_IMAX = createSeat(
    auditoriumId = AUDITORIUM_2_IMAX.id,
    auditoriumLayoutId = AUDITORIUM_LAYOUT_2.id,
    row = "10",
    number = "1",
    type = SeatType.REGULAR
)
private val RESERVATION_1 = createReservation(
    screeningId = SCREENING_1.id,
    seatId = SEAT_1.id
)
private val RESERVATION_2 = createReservation(
    screeningId = SCREENING_1.id,
    seatId = SEAT_2.id
)
private val RESERVATION_3_IMAX = createReservation(
    screeningId = SCREENING_2_IMAX.id,
    seatId = SEAT_3_IMAX.id
)
private val PRODUCT_CATEGORY_1_PRODUCT_STANDARD_TAX_RATE = createProductCategory(
    originalId = 1,
    code = "A1",
    title = "Popcorn nachos",
    taxRate = STANDARD_TAX_RATE
)
private val PRODUCT_CATEGORY_2_PRODUCT_DISCOUNT_CARD = createProductCategory(
    originalId = 3,
    code = "A4",
    title = "zlava karta",
    type = ProductCategoryType.DISCOUNT,
    taxRate = STANDARD_TAX_RATE
)
private val PRODUCT_CATEGORY_3_NO_TAX_RATE = createProductCategory(
    originalId = 3,
    code = "A3",
    title = "Záloha",
    type = ProductCategoryType.DISCOUNT,
    taxRate = NO_TAX_RATE
)
private val PRODUCT_CATEGORY_4_REDUCED_TAX_RATE = createProductCategory(
    originalId = 4,
    code = "A2",
    title = "Pochúťky",
    taxRate = REDUCED_TAX_RATE
)
private val PRODUCT_CATEGORY_5_SUPER_REDUCED_TAX_RATE = createProductCategory(
    originalId = 5,
    code = "A5",
    title = "VIP",
    taxRate = SUPER_REDUCED_TAX_RATE
)
private val PRODUCT_CATEGORY_6_PRODUCT_DISCOUNT_NON_CARD = createProductCategory(
    originalId = 6,
    code = "A6",
    title = "Zľava",
    type = ProductCategoryType.DISCOUNT,
    taxRate = STANDARD_TAX_RATE
)
private val PRODUCT_CATEGORY_7_PRODUCT_REDUCED_TAX_RATE_COFFEE = createProductCategory(
    originalId = 2,
    code = "A2",
    title = "Káva čaj",
    taxRate = REDUCED_TAX_RATE
)
private val PRODUCT_1_POPCORN = createProduct(
    originalId = 1,
    code = "01X",
    productCategoryId = PRODUCT_CATEGORY_1_PRODUCT_STANDARD_TAX_RATE.id,
    title = "Popcorn XXL syrový",
    price = 5.toBigDecimal()
)
private val PRODUCT_2_PACKAGE_DEPOSIT = createProduct(
    originalId = 2,
    code = "02X",
    productCategoryId = PRODUCT_CATEGORY_3_NO_TAX_RATE.id,
    title = "Záloha za obal",
    price = 0.15.toBigDecimal()
)
private val PRODUCT_3_MARGOT = createProduct(
    originalId = 3,
    code = "03X",
    productCategoryId = PRODUCT_CATEGORY_4_REDUCED_TAX_RATE.id,
    title = "Margot guličky",
    price = 2.toBigDecimal()
)
private val PRODUCT_4_PRODUCT_DISCOUNT_FROM_CARD = createProduct(
    originalId = 4,
    code = "04X",
    productCategoryId = PRODUCT_CATEGORY_2_PRODUCT_DISCOUNT_CARD.id,
    title = "VIP karta -20%",
    type = ProductType.PRODUCT,
    price = 0.toBigDecimal(),
    discountPercentage = 20
)
private val PRODUCT_5_PRODUCT_DISCOUNT_NON_CARD = createProduct(
    originalId = 5,
    code = "05X",
    productCategoryId = PRODUCT_CATEGORY_6_PRODUCT_DISCOUNT_NON_CARD.id,
    title = "Zľava 10%",
    type = ProductType.PRODUCT,
    price = 0.toBigDecimal(),
    discountPercentage = 10
)
private val PRODUCT_6_PRODUCT_DISCOUNT_ISOLATED = createProduct(
    originalId = 6,
    code = "06X",
    productCategoryId = PRODUCT_CATEGORY_2_PRODUCT_DISCOUNT_CARD.id,
    title = "Zlava voucher",
    type = ProductType.ADDITIONAL_SALE,
    price = 0.toBigDecimal(),
    discountPercentage = 100
)
private val PRODUCT_6_LATTE = createProduct(
    originalId = 6,
    code = "06X",
    productCategoryId = PRODUCT_CATEGORY_7_PRODUCT_REDUCED_TAX_RATE_COFFEE.id,
    title = "Latte macchiato",
    type = ProductType.PRODUCT,
    price = 2.toBigDecimal()
)
private val PRODUCT_7_CAPPUCCINO = createProduct(
    originalId = 7,
    code = "07X",
    productCategoryId = PRODUCT_CATEGORY_5_SUPER_REDUCED_TAX_RATE.id,
    title = "Cappuccino",
    price = 3.5.toBigDecimal()
)
private val BASKET_1 = createBasket(
    totalPrice = 22.toBigDecimal(),
    state = BasketState.PAYMENT_IN_PROGRESS,
    paymentPosConfigurationId = POS_CONFIGURATION.id,
    paymentType = PaymentType.CASH
)
private val BASKET_2 = createBasket(
    totalPrice = 9.toBigDecimal(),
    state = BasketState.PAYMENT_IN_PROGRESS,
    paymentType = PaymentType.CASHLESS
)
private val BASKET_3 = createBasket(
    totalPrice = 12.15.toBigDecimal(),
    state = BasketState.PAYMENT_IN_PROGRESS,
    paymentType = PaymentType.CASHLESS
)
private val BASKET_4 = createBasket(
    totalPrice = 6.3.toBigDecimal(),
    state = BasketState.PAYMENT_IN_PROGRESS,
    paymentType = PaymentType.CASH
)
private val BASKET_5 = createBasket(
    totalPrice = 3.2.toBigDecimal(),
    state = BasketState.PAYMENT_IN_PROGRESS,
    paymentType = PaymentType.CASH
)
private val BASKET_6 = createBasket(
    totalPrice = 5.6.toBigDecimal(),
    state = BasketState.PAYMENT_IN_PROGRESS,
    paymentType = PaymentType.CASH
)
private val BASKET_7 = createBasket(
    totalPrice = 7.toBigDecimal(),
    state = BasketState.PAYMENT_IN_PROGRESS,
    paymentType = PaymentType.CASH
)
private val BASKET_8 = createBasket(
    totalPrice = 8.5.toBigDecimal(),
    state = BasketState.PAYMENT_IN_PROGRESS,
    paymentType = PaymentType.CASHLESS
)
private val TICKET_1_TICKET_PRICE = createTicketPrice(
    screeningId = SCREENING_1.id,
    seatId = SEAT_1.id,
    basePrice = 12.toBigDecimal(),
    basePriceItemNumber = PriceCategoryItemNumber.PRICE_1,
    totalPrice = 12.toBigDecimal()
)
private val TICKET_2_TICKET_PRICE = createTicketPrice(
    screeningId = SCREENING_1.id,
    seatId = SEAT_2.id,
    basePrice = 10.toBigDecimal(),
    basePriceItemNumber = PriceCategoryItemNumber.PRICE_2,
    totalPrice = 10.toBigDecimal()
)
private val TICKET_3_TICKET_PRICE = createTicketPrice(
    screeningId = SCREENING_2_IMAX.id,
    seatId = SEAT_3_IMAX.id,
    basePrice = 0.toBigDecimal(),
    basePriceBeforeDiscount = 10.toBigDecimal(),
    basePriceItemNumber = PriceCategoryItemNumber.PRICE_1,
    serviceFeeGeneral = 5.toBigDecimal(),
    totalPrice = 5.toBigDecimal()
)
private val TICKET_1_ADULT = createTicket(
    screeningId = SCREENING_1.id,
    reservationId = RESERVATION_1.id,
    ticketPriceId = TICKET_1_TICKET_PRICE.id,
    receiptNumber = "0002439485"
)
private val TICKET_2_STUDENT = createTicket(
    screeningId = SCREENING_1.id,
    reservationId = RESERVATION_2.id,
    ticketPriceId = TICKET_2_TICKET_PRICE.id,
    receiptNumber = "0002439486"
)
private val TICKET_3_ADULT_IMAX = createTicket(
    screeningId = SCREENING_2_IMAX.id,
    reservationId = RESERVATION_3_IMAX.id,
    ticketPriceId = TICKET_3_TICKET_PRICE.id,
    receiptNumber = "0002439487"
)
private val BASKET_ITEM_1_ADULT_TICKET = createBasketItem(
    basketId = BASKET_1.id,
    ticketId = TICKET_1_ADULT.id,
    type = BasketItemType.TICKET,
    price = 12.toBigDecimal(),
    quantity = 1
)
private val BASKET_ITEM_2_STUDENT_TICKET = createBasketItem(
    basketId = BASKET_1.id,
    ticketId = TICKET_2_STUDENT.id,
    type = BasketItemType.TICKET,
    price = 10.toBigDecimal(),
    quantity = 1
)
private val BASKET_ITEM_3_IMAX_TICKET_DISCOUNTED = createBasketItem(
    basketId = BASKET_1.id,
    ticketId = TICKET_2_STUDENT.id,
    type = BasketItemType.TICKET,
    price = 10.toBigDecimal(),
    quantity = 1
)
private val BASKET_ITEM_4_POPCORN = createBasketItem(
    basketId = BASKET_2.id,
    productId = PRODUCT_1_POPCORN.id,
    type = BasketItemType.PRODUCT,
    price = 5.toBigDecimal(),
    quantity = 1,
    productReceiptNumber = "000486505603"
)
private val BASKET_ITEM_5_PRODUCT_DISCOUNT_FROM_CARD = createBasketItem(
    basketId = BASKET_2.id,
    productId = PRODUCT_4_PRODUCT_DISCOUNT_FROM_CARD.id,
    type = BasketItemType.PRODUCT_DISCOUNT,
    price = (-1).toBigDecimal(),
    quantity = 1,
    productReceiptNumber = "000486505603"
)
private val BASKET_ITEM_6_THREE_POPCORNS = createBasketItem(
    basketId = BASKET_3.id,
    productId = PRODUCT_1_POPCORN.id,
    type = BasketItemType.PRODUCT,
    price = 15.toBigDecimal(),
    quantity = 3,
    productReceiptNumber = "000486505603"
)
private val BASKET_ITEM_7_PRODUCT_DISCOUNT_FROM_CARD = createBasketItem(
    basketId = BASKET_3.id,
    productId = PRODUCT_4_PRODUCT_DISCOUNT_FROM_CARD.id,
    type = BasketItemType.PRODUCT_DISCOUNT,
    price = (-3).toBigDecimal(),
    quantity = 1,
    productReceiptNumber = "000486505603"
)
private val BASKET_ITEM_8_PACKAGE_DEPOSIT = createBasketItem(
    basketId = BASKET_3.id,
    productId = PRODUCT_2_PACKAGE_DEPOSIT.id,
    type = BasketItemType.PRODUCT,
    price = 0.15.toBigDecimal(),
    quantity = 1,
    productReceiptNumber = "000486505603"
)
private val BASKET_ITEM_9_MARGOT = createBasketItem(
    basketId = BASKET_4.id,
    productId = PRODUCT_3_MARGOT.id,
    type = BasketItemType.PRODUCT,
    price = 2.toBigDecimal(),
    quantity = 1,
    productReceiptNumber = "000486505603"
)
private val BASKET_ITEM_10_PRODUCT_DISCOUNT_NON_CARD = createBasketItem(
    basketId = BASKET_4.id,
    productId = PRODUCT_5_PRODUCT_DISCOUNT_NON_CARD.id,
    type = BasketItemType.PRODUCT_DISCOUNT,
    price = (-0.5).toBigDecimal(),
    quantity = 1,
    productReceiptNumber = "000486505603"
)
private val BASKET_ITEM_11_LATTE = createBasketItem(
    basketId = BASKET_5.id,
    productId = PRODUCT_6_LATTE.id,
    type = BasketItemType.PRODUCT,
    price = 2.toBigDecimal(),
    quantity = 1,
    productReceiptNumber = "000486505603"
)
private val BASKET_ITEM_12_PRODUCT_DISCOUNT_FROM_CARD = createBasketItem(
    basketId = BASKET_5.id,
    productId = PRODUCT_4_PRODUCT_DISCOUNT_FROM_CARD.id,
    type = BasketItemType.PRODUCT_DISCOUNT,
    price = (-0.8).toBigDecimal(),
    quantity = 1,
    productReceiptNumber = "000486505603"
)
private val BASKET_ITEM_13_PRODUCT_DISCOUNT_FROM_CARD = createBasketItem(
    basketId = BASKET_6.id,
    productId = PRODUCT_4_PRODUCT_DISCOUNT_FROM_CARD.id,
    type = BasketItemType.PRODUCT_DISCOUNT,
    price = (-1.4).toBigDecimal(),
    quantity = 1,
    productReceiptNumber = "000486505603"
)
private val BASKET_ITEM_14_PRODUCT_DISCOUNT_FROM_CARD_STANDARD_VAT = BasketItem(
    id = UUID.randomUUID(),
    basketId = BASKET_ITEM_13_PRODUCT_DISCOUNT_FROM_CARD.basketId,
    productId = BASKET_ITEM_13_PRODUCT_DISCOUNT_FROM_CARD.productId,
    type = BASKET_ITEM_13_PRODUCT_DISCOUNT_FROM_CARD.type,
    price = (-1).toBigDecimal(),
    vatAmount = (-0.16).toBigDecimal(),
    quantity = 1,
    productReceiptNumber = BASKET_ITEM_13_PRODUCT_DISCOUNT_FROM_CARD.productReceiptNumber,
    isCancelled = false
)
private val BASKET_ITEM_15_PRODUCT_DISCOUNT_FROM_CARD_REDUCED_VAT = BasketItem(
    id = UUID.randomUUID(),
    basketId = BASKET_ITEM_13_PRODUCT_DISCOUNT_FROM_CARD.basketId,
    productId = BASKET_ITEM_13_PRODUCT_DISCOUNT_FROM_CARD.productId,
    type = BASKET_ITEM_13_PRODUCT_DISCOUNT_FROM_CARD.type,
    price = (-0.4).toBigDecimal(),
    vatAmount = 0.06.toBigDecimal(),
    quantity = 1,
    productReceiptNumber = BASKET_ITEM_13_PRODUCT_DISCOUNT_FROM_CARD.productReceiptNumber,
    isCancelled = false
)
private val BASKET_ITEM_16_CANCELLED_BASKET_ITEM_1 = createBasketItem(
    basketId = BASKET_1.id,
    ticketId = TICKET_1_ADULT.id,
    type = BasketItemType.TICKET,
    price = 12.toBigDecimal(),
    quantity = 1,
    cancelledBasketItemId = BASKET_ITEM_1_ADULT_TICKET.id
)
private val BASKET_ITEM_17_CANCELLED_BASKET_ITEM_2 = createBasketItem(
    basketId = BASKET_1.id,
    ticketId = TICKET_2_STUDENT.id,
    type = BasketItemType.TICKET,
    price = 10.toBigDecimal(),
    quantity = 1,
    cancelledBasketItemId = BASKET_ITEM_2_STUDENT_TICKET.id
)
private val BASKET_ITEM_18_CANCELLED_POPCORN = createBasketItem(
    basketId = BASKET_2.id,
    productId = PRODUCT_1_POPCORN.id,
    type = BasketItemType.PRODUCT,
    price = 5.toBigDecimal(),
    quantity = 1,
    productReceiptNumber = "000486505603",
    cancelledBasketItemId = BASKET_ITEM_4_POPCORN.id
)
private val BASKET_ITEM_19_POPCORN_ISOLATED = createBasketItem(
    basketId = BASKET_7.id,
    productId = PRODUCT_1_POPCORN.id,
    productIsolatedWith = PRODUCT_6_PRODUCT_DISCOUNT_ISOLATED.id,
    type = BasketItemType.PRODUCT,
    price = 5.toBigDecimal(),
    quantity = 1,
    productReceiptNumber = "000486505603"
)
private val BASKET_ITEM_20_PRODUCT_DISCOUNT_ISOLATED = createBasketItem(
    basketId = BASKET_7.id,
    productId = PRODUCT_6_PRODUCT_DISCOUNT_ISOLATED.id,
    productIsolatedWith = PRODUCT_1_POPCORN.id,
    type = BasketItemType.PRODUCT_DISCOUNT,
    price = (-5).toBigDecimal(),
    quantity = 1,
    productReceiptNumber = "000486505603"
)
private val BASKET_ITEM_21_POPCORN_ISOLATED = createBasketItem(
    basketId = BASKET_7.id,
    productId = PRODUCT_1_POPCORN.id,
    productIsolatedWith = PRODUCT_6_PRODUCT_DISCOUNT_ISOLATED.id,
    type = BasketItemType.PRODUCT,
    price = 5.toBigDecimal(),
    quantity = 1,
    productReceiptNumber = "000486505603"
)
private val BASKET_ITEM_22_PRODUCT_DISCOUNT_ISOLATED = createBasketItem(
    basketId = BASKET_7.id,
    productId = PRODUCT_6_PRODUCT_DISCOUNT_ISOLATED.id,
    productIsolatedWith = PRODUCT_1_POPCORN.id,
    type = BasketItemType.PRODUCT_DISCOUNT,
    price = (-5).toBigDecimal(),
    quantity = 1,
    productReceiptNumber = "000486505603"
)
private val BASKET_ITEM_23_CAPPUCCINO = createBasketItem(
    basketId = BASKET_7.id,
    productId = PRODUCT_7_CAPPUCCINO.id,
    type = BasketItemType.PRODUCT,
    price = 3.5.toBigDecimal(),
    quantity = 1,
    productReceiptNumber = "000486505603"
)
private val BASKET_ITEM_24_PRODUCT_DISCOUNT_NON_CARD = createBasketItem(
    basketId = BASKET_4.id,
    productId = PRODUCT_5_PRODUCT_DISCOUNT_NON_CARD.id,
    type = BasketItemType.PRODUCT_DISCOUNT,
    price = (-0.2).toBigDecimal(),
    quantity = 1,
    productReceiptNumber = "000486505603"
)
private val BASKET_ITEM_25_CANCELLED_CAPPUCCINO = createBasketItem(
    basketId = BASKET_8.id,
    productId = PRODUCT_7_CAPPUCCINO.id,
    type = BasketItemType.PRODUCT,
    price = 3.5.toBigDecimal(),
    quantity = 1,
    productReceiptNumber = "000486505603",
    cancelledBasketItemId = BASKET_ITEM_23_CAPPUCCINO.id
)
private val EXPECTED_RECEIPT_1_STUDENT_AND_ADULT_TICKET = SvkXmlReceipt(
    header = SvkXmlHeader(uuid = "V-0002439485"),
    data = listOf(
        SvkXmlReceiptData(
            items = SvkXmlReceiptItems(
                soldBy = POS_CONFIGURATION.title,
                items = listOf(
                    SvkXmlReceiptItem(
                        name = "Minulé životy 2D (OT)",
                        qr = "0002439485",
                        description = "SALA A, 09.10.2023 20:00 rad 6 mies. 4",
                        type = ReceiptItemType.K,
                        quantity = 1.toBigDecimal(),
                        vatRate = STANDARD_TAX_RATE,
                        unitPrice = 12.toBigDecimal(),
                        price = 12.toBigDecimal()
                    )
                )
            ),
            payments = SvkXmlReceiptPayments(
                payment = listOf(
                    SvkXmlReceiptPayment(
                        amount = 12.toBigDecimal(),
                        paymentType = ReceiptPaymentType.HO
                    )
                )
            ),
            number = "0002439485",
            amount = 12.toBigDecimal(),
            taxBaseBasic = 9.76.toBigDecimal(),
            taxBaseReduced = 0.toBigDecimal(),
            basicVatAmount = 2.24.toBigDecimal(),
            reducedVatAmount = 0.toBigDecimal(),
            taxFreeAmount = 0.toBigDecimal()
        ),
        SvkXmlReceiptData(
            items = SvkXmlReceiptItems(
                soldBy = POS_CONFIGURATION.title,
                items = listOf(
                    SvkXmlReceiptItem(
                        name = "Minulé životy 2D (OT)",
                        qr = "0002439486",
                        description = "SALA A, 09.10.2023 20:00 rad 6 mies. 5",
                        type = ReceiptItemType.K,
                        quantity = 1.toBigDecimal(),
                        vatRate = STANDARD_TAX_RATE,
                        unitPrice = 10.toBigDecimal(),
                        price = 10.toBigDecimal()
                    )
                )
            ),
            payments = SvkXmlReceiptPayments(
                payment = listOf(
                    SvkXmlReceiptPayment(
                        amount = 10.toBigDecimal(),
                        paymentType = ReceiptPaymentType.HO
                    )
                )
            ),
            number = "0002439486",
            amount = 10.toBigDecimal(),
            taxBaseBasic = 8.13.toBigDecimal(),
            taxBaseReduced = 0.toBigDecimal(),
            basicVatAmount = 1.87.toBigDecimal(),
            reducedVatAmount = 0.toBigDecimal(),
            taxFreeAmount = 0.toBigDecimal()
        )
    )
)
private val EXPECTED_RECEIPT_2_GROUP_TICKET = SvkXmlReceipt(
    header = SvkXmlHeader(uuid = "V-0002439485"),
    data = listOf(
        SvkXmlReceiptData(
            items = SvkXmlReceiptItems(
                soldBy = POS_CONFIGURATION.title,
                items = listOf(
                    SvkXmlReceiptItem(
                        name = "Minulé životy 2D (OT)",
                        qr = "0002439485",
                        description = "SALA A, 09.10.2023 20:00 Celkom:(2)",
                        type = ReceiptItemType.K,
                        quantity = 2.toBigDecimal(),
                        vatRate = STANDARD_TAX_RATE,
                        unitPrice = 11.toBigDecimal(),
                        price = 22.toBigDecimal()
                    )
                )
            ),
            payments = SvkXmlReceiptPayments(
                payment = listOf(
                    SvkXmlReceiptPayment(
                        amount = 22.toBigDecimal(),
                        paymentType = ReceiptPaymentType.HO
                    )
                )
            ),
            number = "0002439485",
            amount = 22.toBigDecimal(),
            taxBaseBasic = 17.89.toBigDecimal(),
            taxBaseReduced = 0.toBigDecimal(),
            basicVatAmount = 4.11.toBigDecimal(),
            reducedVatAmount = 0.toBigDecimal(),
            taxFreeAmount = 0.toBigDecimal()
        )
    )
)
private val EXPECTED_RECEIPT_3_TICKET_PRODUCT_PRODUCT_DISCOUNT = SvkXmlReceipt(
    header = SvkXmlHeader(uuid = "B-000486505603"),
    data = listOf(
        SvkXmlReceiptData(
            items = SvkXmlReceiptItems(
                soldBy = BASKET_2.updatedBy, // principal username
                items = listOf(
                    SvkXmlReceiptItem(
                        name = null,
                        qr = null,
                        description = "Popcorn XXL syrovy",
                        type = ReceiptItemType.K,
                        quantity = 1.toBigDecimal(),
                        vatRate = STANDARD_TAX_RATE,
                        unitPrice = 5.toBigDecimal(),
                        price = 5.toBigDecimal()
                    ),
                    SvkXmlReceiptItem(
                        name = null,
                        qr = null,
                        description = "VIP karta -20%",
                        type = ReceiptItemType.MZ,
                        quantity = 1.toBigDecimal(),
                        vatRate = STANDARD_TAX_RATE,
                        unitPrice = (-1).toBigDecimal(),
                        price = (-1).toBigDecimal()
                    )
                )
            ),
            payments = SvkXmlReceiptPayments(
                payment = listOf(
                    SvkXmlReceiptPayment(
                        amount = 4.toBigDecimal(),
                        paymentType = ReceiptPaymentType.KA
                    )
                )
            ),
            number = "000486505603",
            amount = 4.toBigDecimal(),
            taxBaseBasic = 3.25.toBigDecimal(),
            taxBaseReduced = 0.toBigDecimal(),
            basicVatAmount = 0.75.toBigDecimal(),
            reducedVatAmount = 0.toBigDecimal(),
            taxFreeAmount = 0.toBigDecimal()
        ),
        SvkXmlReceiptData(
            items = SvkXmlReceiptItems(
                soldBy = BASKET_2.updatedBy,
                items = listOf(
                    SvkXmlReceiptItem(
                        name = "Minulé životy 2D (OT)",
                        qr = "0002439487",
                        description = "IMAX, 10.10.2023 20:00 rad 10 mies. 1",
                        type = ReceiptItemType.K,
                        quantity = 1.toBigDecimal(),
                        vatRate = STANDARD_TAX_RATE,
                        unitPrice = 5.toBigDecimal(),
                        price = 5.toBigDecimal()
                    )
                )
            ),
            payments = SvkXmlReceiptPayments(
                payment = listOf(
                    SvkXmlReceiptPayment(
                        amount = 5.toBigDecimal(),
                        paymentType = ReceiptPaymentType.KA
                    )
                )
            ),
            number = "0002439487",
            amount = 5.toBigDecimal(),
            taxBaseBasic = 4.07.toBigDecimal(),
            taxBaseReduced = 0.toBigDecimal(),
            basicVatAmount = 0.93.toBigDecimal(),
            reducedVatAmount = 0.toBigDecimal(),
            taxFreeAmount = 0.toBigDecimal()
        )
    )
)
private val EXPECTED_RECEIPT_4_DISCOUNTED_PRODUCTS_AND_TAX_FREE_PRODUCT = SvkXmlReceipt(
    header = SvkXmlHeader(uuid = "B-000486505603"),
    data = listOf(
        SvkXmlReceiptData(
            items = SvkXmlReceiptItems(
                soldBy = BASKET_3.updatedBy,
                items = listOf(
                    SvkXmlReceiptItem(
                        name = null,
                        qr = null,
                        description = "Popcorn XXL syrovy",
                        type = ReceiptItemType.K,
                        quantity = 3.toBigDecimal(),
                        vatRate = STANDARD_TAX_RATE,
                        unitPrice = 5.toBigDecimal(),
                        price = 15.toBigDecimal()
                    ),
                    SvkXmlReceiptItem(
                        name = null,
                        qr = null,
                        description = "Zaloha za obal",
                        type = ReceiptItemType.K,
                        quantity = 1.toBigDecimal(),
                        vatRate = NO_TAX_RATE,
                        unitPrice = 0.15.toBigDecimal(),
                        price = 0.15.toBigDecimal()
                    ),
                    SvkXmlReceiptItem(
                        name = null,
                        qr = null,
                        description = "VIP karta -20%",
                        type = ReceiptItemType.MZ,
                        quantity = 1.toBigDecimal(),
                        vatRate = STANDARD_TAX_RATE,
                        unitPrice = (-3).toBigDecimal(),
                        price = (-3).toBigDecimal()
                    )
                )
            ),
            payments = SvkXmlReceiptPayments(
                payment = listOf(
                    SvkXmlReceiptPayment(
                        amount = 12.15.toBigDecimal(),
                        paymentType = ReceiptPaymentType.KA
                    )
                )
            ),
            number = "000486505603",
            amount = 12.15.toBigDecimal(),
            taxBaseBasic = 9.76.toBigDecimal(),
            taxBaseReduced = 0.toBigDecimal(),
            basicVatAmount = 2.24.toBigDecimal(),
            reducedVatAmount = 0.toBigDecimal(),
            taxFreeAmount = 0.15.toBigDecimal()
        )
    )
)
private val EXPECTED_RECEIPT_5_PRODUCTS_DIFFERENT_TAX_RATE_AND_NON_CARD_DISCOUNT = SvkXmlReceipt(
    header = SvkXmlHeader(uuid = "B-000486505603"),
    data = listOf(
        SvkXmlReceiptData(
            items = SvkXmlReceiptItems(
                soldBy = BASKET_4.updatedBy,
                items = listOf(
                    SvkXmlReceiptItem(
                        name = null,
                        qr = null,
                        description = "Popcorn XXL syrovy",
                        type = ReceiptItemType.K,
                        quantity = 1.toBigDecimal(),
                        vatRate = STANDARD_TAX_RATE,
                        unitPrice = 5.toBigDecimal(),
                        price = 5.toBigDecimal()
                    ),
                    SvkXmlReceiptItem(
                        name = null,
                        qr = null,
                        description = "Margot gulicky",
                        type = ReceiptItemType.K,
                        quantity = 1.toBigDecimal(),
                        vatRate = REDUCED_TAX_RATE,
                        unitPrice = 2.toBigDecimal(),
                        price = 2.toBigDecimal()
                    ),
                    SvkXmlReceiptItem(
                        name = null,
                        qr = null,
                        description = "Zlava 10%",
                        type = ReceiptItemType.MZ,
                        quantity = 1.toBigDecimal(),
                        vatRate = STANDARD_TAX_RATE,
                        unitPrice = (-0.5).toBigDecimal(),
                        price = (-0.5).toBigDecimal()
                    ),
                    SvkXmlReceiptItem(
                        name = null,
                        qr = null,
                        description = "Zlava 10%",
                        type = ReceiptItemType.MZ,
                        quantity = 1.toBigDecimal(),
                        vatRate = REDUCED_TAX_RATE,
                        unitPrice = (-0.2).toBigDecimal(),
                        price = (-0.2).toBigDecimal()
                    )
                )
            ),
            payments = SvkXmlReceiptPayments(
                payment = listOf(
                    SvkXmlReceiptPayment(
                        amount = 6.3.toBigDecimal(),
                        paymentType = ReceiptPaymentType.HO
                    )
                )
            ),
            number = "000486505603",
            amount = 6.3.toBigDecimal(),
            taxBaseBasic = 3.66.toBigDecimal(),
            taxBaseReduced = 1.51.toBigDecimal(),
            basicVatAmount = 0.84.toBigDecimal(),
            reducedVatAmount = 0.29.toBigDecimal(),
            taxFreeAmount = 0.toBigDecimal()
        )
    )
)
private val EXPECTED_RECEIPT_6_PRODUCTS_REDUCED_TAX_RATE_AND_DISCOUNT_FROM_CARD = SvkXmlReceipt(
    header = SvkXmlHeader(uuid = "B-000486505603"),
    data = listOf(
        SvkXmlReceiptData(
            items = SvkXmlReceiptItems(
                soldBy = BASKET_5.updatedBy,
                items = listOf(
                    SvkXmlReceiptItem(
                        name = null,
                        qr = null,
                        description = "Margot gulicky",
                        type = ReceiptItemType.K,
                        quantity = 1.toBigDecimal(),
                        vatRate = REDUCED_TAX_RATE,
                        unitPrice = 2.toBigDecimal(),
                        price = 2.toBigDecimal()
                    ),
                    SvkXmlReceiptItem(
                        name = null,
                        qr = null,
                        description = "Latte macchiato",
                        type = ReceiptItemType.K,
                        quantity = 1.toBigDecimal(),
                        vatRate = REDUCED_TAX_RATE,
                        unitPrice = 2.toBigDecimal(),
                        price = 2.toBigDecimal()
                    ),
                    SvkXmlReceiptItem(
                        name = null,
                        qr = null,
                        description = "VIP karta -20%",
                        type = ReceiptItemType.MZ,
                        quantity = 1.toBigDecimal(),
                        vatRate = REDUCED_TAX_RATE,
                        unitPrice = (-0.8).toBigDecimal(),
                        price = (-0.8).toBigDecimal()
                    )
                )
            ),
            payments = SvkXmlReceiptPayments(
                payment = listOf(
                    SvkXmlReceiptPayment(
                        amount = 3.2.toBigDecimal(),
                        paymentType = ReceiptPaymentType.HO
                    )
                )
            ),
            number = "000486505603",
            amount = 3.2.toBigDecimal(),
            taxBaseBasic = 0.toBigDecimal(),
            taxBaseReduced = 2.69.toBigDecimal(),
            basicVatAmount = 0.toBigDecimal(),
            reducedVatAmount = 0.51.toBigDecimal(),
            taxFreeAmount = 0.toBigDecimal()
        )
    )
)
private val EXPECTED_RECEIPT_7_PRODUCTS_DIFFERENT_TAX_RATE_AND_DISCOUNT_FROM_CARD = SvkXmlReceipt(
    header = SvkXmlHeader(uuid = "B-000486505603"),
    data = listOf(
        SvkXmlReceiptData(
            items = SvkXmlReceiptItems(
                soldBy = BASKET_6.updatedBy,
                items = listOf(
                    SvkXmlReceiptItem(
                        name = null,
                        qr = null,
                        description = "Popcorn XXL syrovy",
                        type = ReceiptItemType.K,
                        quantity = 1.toBigDecimal(),
                        vatRate = STANDARD_TAX_RATE,
                        unitPrice = 5.toBigDecimal(),
                        price = 5.toBigDecimal()
                    ),
                    SvkXmlReceiptItem(
                        name = null,
                        qr = null,
                        description = "Zlava voucher",
                        type = ReceiptItemType.MZ,
                        quantity = 1.toBigDecimal(),
                        vatRate = STANDARD_TAX_RATE,
                        unitPrice = (-5).toBigDecimal(),
                        price = (-5).toBigDecimal()
                    ),
                    SvkXmlReceiptItem(
                        name = null,
                        qr = null,
                        description = "Popcorn XXL syrovy",
                        type = ReceiptItemType.K,
                        quantity = 1.toBigDecimal(),
                        vatRate = STANDARD_TAX_RATE,
                        unitPrice = 5.toBigDecimal(),
                        price = 5.toBigDecimal()
                    ),
                    SvkXmlReceiptItem(
                        name = null,
                        qr = null,
                        description = "Margot gulicky",
                        type = ReceiptItemType.K,
                        quantity = 1.toBigDecimal(),
                        vatRate = REDUCED_TAX_RATE,
                        unitPrice = 2.toBigDecimal(),
                        price = 2.toBigDecimal()
                    ),
                    SvkXmlReceiptItem(
                        name = null,
                        qr = null,
                        description = "VIP karta -20%",
                        type = ReceiptItemType.MZ,
                        quantity = 1.toBigDecimal(),
                        vatRate = STANDARD_TAX_RATE,
                        unitPrice = (-1).toBigDecimal(),
                        price = (-1).toBigDecimal()
                    ),
                    SvkXmlReceiptItem(
                        name = null,
                        qr = null,
                        description = "VIP karta -20%",
                        type = ReceiptItemType.MZ,
                        quantity = 1.toBigDecimal(),
                        vatRate = REDUCED_TAX_RATE,
                        unitPrice = (-0.4).toBigDecimal(),
                        price = (-0.4).toBigDecimal()
                    )
                )
            ),
            payments = SvkXmlReceiptPayments(
                payment = listOf(
                    SvkXmlReceiptPayment(
                        amount = 5.6.toBigDecimal(),
                        paymentType = ReceiptPaymentType.HO
                    )
                )
            ),
            number = "000486505603",
            amount = 5.6.toBigDecimal(),
            taxBaseBasic = 3.25.toBigDecimal(),
            taxBaseReduced = 1.34.toBigDecimal(),
            basicVatAmount = 0.75.toBigDecimal(),
            reducedVatAmount = 0.26.toBigDecimal(),
            taxFreeAmount = 0.toBigDecimal()
        )
    )
)
private val EXPECTED_RECEIPT_8_PRODUCTS_DIFFERENT_TAX_RATE = SvkXmlReceipt(
    header = SvkXmlHeader(uuid = "B-000486505603"),
    data = listOf(
        SvkXmlReceiptData(
            items = SvkXmlReceiptItems(
                soldBy = BASKET_7.updatedBy,
                items = listOf(
                    SvkXmlReceiptItem(
                        name = null,
                        qr = null,
                        description = "Popcorn XXL syrovy",
                        type = ReceiptItemType.K,
                        quantity = 1.toBigDecimal(),
                        vatRate = STANDARD_TAX_RATE,
                        unitPrice = 5.toBigDecimal(),
                        price = 5.toBigDecimal()
                    ),
                    SvkXmlReceiptItem(
                        name = null,
                        qr = null,
                        description = "Margot gulicky",
                        type = ReceiptItemType.K,
                        quantity = 1.toBigDecimal(),
                        vatRate = REDUCED_TAX_RATE,
                        unitPrice = 2.toBigDecimal(),
                        price = 2.toBigDecimal()
                    )
                )
            ),
            payments = SvkXmlReceiptPayments(
                payment = listOf(
                    SvkXmlReceiptPayment(
                        amount = 7.toBigDecimal(),
                        paymentType = ReceiptPaymentType.HO
                    )
                )
            ),
            number = "000486505603",
            amount = 7.toBigDecimal(),
            taxBaseBasic = 4.07.toBigDecimal(),
            taxBaseReduced = 1.68.toBigDecimal(),
            basicVatAmount = 0.93.toBigDecimal(),
            reducedVatAmount = 0.32.toBigDecimal(),
            taxFreeAmount = 0.toBigDecimal()
        )
    )
)
private val EXPECTED_RECEIPT_9_PRODUCTS_AND_ISOLATED_GROUPS = SvkXmlReceipt(
    header = SvkXmlHeader(uuid = "B-000486505603"),
    data = listOf(
        SvkXmlReceiptData(
            items = SvkXmlReceiptItems(
                soldBy = BASKET_7.updatedBy,
                items = listOf(
                    SvkXmlReceiptItem(
                        name = null,
                        qr = null,
                        description = "Popcorn XXL syrovy",
                        type = ReceiptItemType.K,
                        quantity = 1.toBigDecimal(),
                        vatRate = STANDARD_TAX_RATE,
                        unitPrice = 5.toBigDecimal(),
                        price = 5.toBigDecimal()
                    ),
                    SvkXmlReceiptItem(
                        name = null,
                        qr = null,
                        description = "Zlava voucher",
                        type = ReceiptItemType.MZ,
                        quantity = 1.toBigDecimal(),
                        vatRate = STANDARD_TAX_RATE,
                        unitPrice = (-5).toBigDecimal(),
                        price = (-5).toBigDecimal()
                    ),
                    SvkXmlReceiptItem(
                        name = null,
                        qr = null,
                        description = "Popcorn XXL syrovy",
                        type = ReceiptItemType.K,
                        quantity = 1.toBigDecimal(),
                        vatRate = STANDARD_TAX_RATE,
                        unitPrice = 5.toBigDecimal(),
                        price = 5.toBigDecimal()
                    ),
                    SvkXmlReceiptItem(
                        name = null,
                        qr = null,
                        description = "Margot gulicky",
                        type = ReceiptItemType.K,
                        quantity = 1.toBigDecimal(),
                        vatRate = REDUCED_TAX_RATE,
                        unitPrice = 2.toBigDecimal(),
                        price = 2.toBigDecimal()
                    ),
                    SvkXmlReceiptItem(
                        name = null,
                        qr = null,
                        description = "Popcorn XXL syrovy",
                        type = ReceiptItemType.K,
                        quantity = 1.toBigDecimal(),
                        vatRate = STANDARD_TAX_RATE,
                        unitPrice = 5.toBigDecimal(),
                        price = 5.toBigDecimal()
                    ),
                    SvkXmlReceiptItem(
                        name = null,
                        qr = null,
                        description = "Zlava voucher",
                        type = ReceiptItemType.MZ,
                        quantity = 1.toBigDecimal(),
                        vatRate = STANDARD_TAX_RATE,
                        unitPrice = (-5).toBigDecimal(),
                        price = (-5).toBigDecimal()
                    )
                )
            ),
            payments = SvkXmlReceiptPayments(
                payment = listOf(
                    SvkXmlReceiptPayment(
                        amount = 7.toBigDecimal(),
                        paymentType = ReceiptPaymentType.HO
                    )
                )
            ),
            number = "000486505603",
            amount = 7.toBigDecimal(),
            taxBaseBasic = 4.07.toBigDecimal(),
            taxBaseReduced = 1.68.toBigDecimal(),
            basicVatAmount = 0.93.toBigDecimal(),
            reducedVatAmount = 0.32.toBigDecimal(),
            taxFreeAmount = 0.toBigDecimal()
        )
    )
)
private val EXPECTED_RECEIPT_10_ALL_TAX_RATES = SvkXmlReceipt(
    header = SvkXmlHeader(uuid = "B-000486505603"),
    data = listOf(
        SvkXmlReceiptData(
            items = SvkXmlReceiptItems(
                soldBy = BASKET_7.updatedBy,
                items = listOf(
                    SvkXmlReceiptItem(
                        name = null,
                        qr = null,
                        description = "Popcorn XXL syrovy",
                        type = ReceiptItemType.K,
                        quantity = 1.toBigDecimal(),
                        vatRate = STANDARD_TAX_RATE,
                        unitPrice = 5.toBigDecimal(),
                        price = 5.toBigDecimal()
                    ),
                    SvkXmlReceiptItem(
                        name = null,
                        qr = null,
                        description = "Margot gulicky",
                        type = ReceiptItemType.K,
                        quantity = 1.toBigDecimal(),
                        vatRate = REDUCED_TAX_RATE,
                        unitPrice = 2.toBigDecimal(),
                        price = 2.toBigDecimal()
                    ),
                    SvkXmlReceiptItem(
                        name = null,
                        qr = null,
                        description = "Cappuccino",
                        type = ReceiptItemType.K,
                        quantity = 1.toBigDecimal(),
                        vatRate = SUPER_REDUCED_TAX_RATE,
                        unitPrice = 3.5.toBigDecimal(),
                        price = 3.5.toBigDecimal()
                    )
                )
            ),
            payments = SvkXmlReceiptPayments(
                payment = listOf(
                    SvkXmlReceiptPayment(
                        amount = 10.5.toBigDecimal(),
                        paymentType = ReceiptPaymentType.HO
                    )
                )
            ),
            number = "000486505603",
            amount = 10.5.toBigDecimal(),
            taxBaseBasic = 4.07.toBigDecimal(),
            taxBaseReduced = (1.68 + 3.33).toBigDecimal(),
            basicVatAmount = 0.93.toBigDecimal(),
            reducedVatAmount = (0.32 + 0.17).toBigDecimal(),
            taxFreeAmount = 0.toBigDecimal()
        )
    )
)
private val EXPECTED_CANCELLATION_RECEIPT_1_STUDENT_AND_ADULT_TICKET = SvkXmlReceipt(
    header = SvkXmlHeader(uuid = "V-0002439485"),
    data = listOf(
        SvkXmlReceiptData(
            items = SvkXmlReceiptItems(
                soldBy = POS_CONFIGURATION.title,
                items = listOf(
                    SvkXmlReceiptItem(
                        name = "Minulé životy 2D (OT)",
                        qr = "0002439485",
                        description = "Vst. SALA A, 09.10.2023 20:00 Celkom:(1)",
                        type = ReceiptItemType.V,
                        quantity = 1.toBigDecimal(),
                        vatRate = STANDARD_TAX_RATE,
                        unitPrice = (-12).toBigDecimal(),
                        price = (-12).toBigDecimal()
                    )
                )
            ),
            payments = SvkXmlReceiptPayments(
                payment = listOf(
                    SvkXmlReceiptPayment(
                        amount = (-12).toBigDecimal(),
                        paymentType = ReceiptPaymentType.HO
                    )
                )
            ),
            number = "0002439485",
            amount = (-12).toBigDecimal(),
            taxBaseBasic = (-9.76).toBigDecimal(),
            taxBaseReduced = 0.toBigDecimal(),
            basicVatAmount = (-2.24).toBigDecimal(),
            reducedVatAmount = 0.toBigDecimal(),
            taxFreeAmount = 0.toBigDecimal()
        ),
        SvkXmlReceiptData(
            items = SvkXmlReceiptItems(
                soldBy = POS_CONFIGURATION.title,
                items = listOf(
                    SvkXmlReceiptItem(
                        name = "Minulé životy 2D (OT)",
                        qr = "0002439486",
                        description = "Vst. SALA A, 09.10.2023 20:00 Celkom:(1)",
                        type = ReceiptItemType.V,
                        quantity = 1.toBigDecimal(),
                        vatRate = STANDARD_TAX_RATE,
                        unitPrice = (-10).toBigDecimal(),
                        price = (-10).toBigDecimal()
                    )
                )
            ),
            payments = SvkXmlReceiptPayments(
                payment = listOf(
                    SvkXmlReceiptPayment(
                        amount = (-10).toBigDecimal(),
                        paymentType = ReceiptPaymentType.HO
                    )
                )
            ),
            number = "0002439486",
            amount = (-10).toBigDecimal(),
            taxBaseBasic = (-8.13).toBigDecimal(),
            taxBaseReduced = 0.toBigDecimal(),
            basicVatAmount = (-1.87).toBigDecimal(),
            reducedVatAmount = 0.toBigDecimal(),
            taxFreeAmount = 0.toBigDecimal()
        )
    )
)
private val EXPECTED_CANCELLATION_RECEIPT_2_PRODUCT = SvkXmlReceipt(
    header = SvkXmlHeader(uuid = "B-000486505603"),
    data = listOf(
        SvkXmlReceiptData(
            items = SvkXmlReceiptItems(
                soldBy = BASKET_2.updatedBy,
                items = listOf(
                    SvkXmlReceiptItem(
                        name = null,
                        qr = null,
                        description = "Popcorn XXL syrovy",
                        type = ReceiptItemType.V,
                        quantity = 1.toBigDecimal(),
                        vatRate = STANDARD_TAX_RATE,
                        unitPrice = (-5).toBigDecimal(),
                        price = (-5).toBigDecimal()
                    )
                )
            ),
            payments = SvkXmlReceiptPayments(
                payment = listOf(
                    SvkXmlReceiptPayment(
                        amount = (-5).toBigDecimal(),
                        paymentType = ReceiptPaymentType.KA
                    )
                )
            ),
            number = "000486505603",
            amount = (-5).toBigDecimal(),
            taxBaseBasic = (-4.07).toBigDecimal(),
            taxBaseReduced = 0.toBigDecimal(),
            basicVatAmount = (-0.93).toBigDecimal(),
            reducedVatAmount = 0.toBigDecimal(),
            taxFreeAmount = 0.toBigDecimal()
        )
    )
)
private val EXPECTED_CANCELLATION_RECEIPT_3_PRODUCTS = SvkXmlReceipt(
    header = SvkXmlHeader(uuid = "B-000486505603"),
    data = listOf(
        SvkXmlReceiptData(
            items = SvkXmlReceiptItems(
                soldBy = BASKET_2.updatedBy,
                items = listOf(
                    SvkXmlReceiptItem(
                        name = null,
                        qr = null,
                        description = "Popcorn XXL syrovy",
                        type = ReceiptItemType.V,
                        quantity = 1.toBigDecimal(),
                        vatRate = STANDARD_TAX_RATE,
                        unitPrice = (-5).toBigDecimal(),
                        price = (-5).toBigDecimal()
                    ),
                    SvkXmlReceiptItem(
                        name = null,
                        qr = null,
                        description = "Cappuccino",
                        type = ReceiptItemType.V,
                        quantity = 1.toBigDecimal(),
                        vatRate = SUPER_REDUCED_TAX_RATE,
                        unitPrice = (-3.5).toBigDecimal(),
                        price = (-3.5).toBigDecimal()
                    )
                )
            ),
            payments = SvkXmlReceiptPayments(
                payment = listOf(
                    SvkXmlReceiptPayment(
                        amount = (-8.5).toBigDecimal(),
                        paymentType = ReceiptPaymentType.KA
                    )
                )
            ),
            number = "000486505603",
            amount = (-8.5).toBigDecimal(),
            taxBaseBasic = (-4.07).toBigDecimal(),
            taxBaseReduced = (-3.33).toBigDecimal(),
            basicVatAmount = (-0.93).toBigDecimal(),
            reducedVatAmount = (-0.17).toBigDecimal(),
            taxFreeAmount = 0.toBigDecimal()
        )
    )
)
