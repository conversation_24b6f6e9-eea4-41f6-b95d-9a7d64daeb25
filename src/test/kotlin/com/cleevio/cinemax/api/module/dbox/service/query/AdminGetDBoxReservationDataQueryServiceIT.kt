package com.cleevio.cinemax.api.module.dbox.service.query

import com.cleevio.cinemax.api.DatabaseCleanup
import com.cleevio.cinemax.api.INTEGRATION_TEST_DATE_TIME
import com.cleevio.cinemax.api.IntegrationTest
import com.cleevio.cinemax.api.module.auditorium.service.AuditoriumRepository
import com.cleevio.cinemax.api.module.auditoriumlayout.service.AuditoriumLayoutRepository
import com.cleevio.cinemax.api.module.basket.constant.BasketState
import com.cleevio.cinemax.api.module.basket.entity.Basket
import com.cleevio.cinemax.api.module.basket.service.BasketRepository
import com.cleevio.cinemax.api.module.basketitem.constant.BasketItemType
import com.cleevio.cinemax.api.module.basketitem.service.BasketItemRepository
import com.cleevio.cinemax.api.module.distributor.service.DistributorRepository
import com.cleevio.cinemax.api.module.movie.entity.Movie
import com.cleevio.cinemax.api.module.movie.service.MovieRepository
import com.cleevio.cinemax.api.module.pricecategory.service.PriceCategoryRepository
import com.cleevio.cinemax.api.module.reservation.constant.ReservationState
import com.cleevio.cinemax.api.module.reservation.service.ReservationRepository
import com.cleevio.cinemax.api.module.screening.constant.ScreeningState
import com.cleevio.cinemax.api.module.screening.entity.Screening
import com.cleevio.cinemax.api.module.screening.service.ScreeningRepository
import com.cleevio.cinemax.api.module.seat.constant.SeatType
import com.cleevio.cinemax.api.module.seat.entity.Seat
import com.cleevio.cinemax.api.module.seat.service.SeatRepository
import com.cleevio.cinemax.api.module.ticket.service.TicketRepository
import com.cleevio.cinemax.api.module.ticketprice.constant.SeatSurchargeType
import com.cleevio.cinemax.api.module.ticketprice.service.TicketPriceRepository
import com.cleevio.cinemax.api.util.createAuditorium
import com.cleevio.cinemax.api.util.createAuditoriumLayout
import com.cleevio.cinemax.api.util.createBasket
import com.cleevio.cinemax.api.util.createBasketItem
import com.cleevio.cinemax.api.util.createDistributor
import com.cleevio.cinemax.api.util.createMovie
import com.cleevio.cinemax.api.util.createPriceCategory
import com.cleevio.cinemax.api.util.createReservation
import com.cleevio.cinemax.api.util.createScreening
import com.cleevio.cinemax.api.util.createSeat
import com.cleevio.cinemax.api.util.createTicket
import com.cleevio.cinemax.api.util.createTicketPrice
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import java.time.LocalDate
import java.time.LocalTime
import java.util.UUID
import kotlin.test.assertEquals
import kotlin.test.assertTrue

class AdminGetDBoxReservationDataQueryServiceIT @Autowired constructor(
    private val underTest: AdminGetDBoxReservationDataQueryService,
    private val auditoriumRepository: AuditoriumRepository,
    private val auditoriumLayoutRepository: AuditoriumLayoutRepository,
    private val distributorRepository: DistributorRepository,
    private val movieRepository: MovieRepository,
    private val priceCategoryRepository: PriceCategoryRepository,
    private val screeningRepository: ScreeningRepository,
    private val seatRepository: SeatRepository,
    private val basketRepository: BasketRepository,
    private val basketItemRepository: BasketItemRepository,
    private val ticketRepository: TicketRepository,
    private val reservationRepository: ReservationRepository,
    private val ticketPriceRepository: TicketPriceRepository,
) : IntegrationTest(DatabaseCleanup.AFTER_EACH) {

    private lateinit var movie: Movie

    @BeforeEach
    fun setUp() {
        auditoriumRepository.saveAll(setOf(AUDITORIUM_1, AUDITORIUM_2, AUDITORIUM_3))
        auditoriumLayoutRepository.saveAll(setOf(AUDITORIUM_LAYOUT_1, AUDITORIUM_LAYOUT_2, AUDITORIUM_LAYOUT_3))
        distributorRepository.save(DISTRIBUTOR_1)

        movie = createMovie(
            originalId = 1,
            title = "Dune: Part Two",
            rawTitle = "Dune: Part Two",
            distributorId = DISTRIBUTOR_1.id,
            duration = 166
        )

        movieRepository.save(movie)
        priceCategoryRepository.save(PRICE_CATEGORY_1)
        seatRepository.saveAll(
            setOf(
                SEAT_1_DBOX,
                SEAT_2_DBOX,
                SEAT_3_DBOX,
                SEAT_4_DBOX,
                SEAT_5_DBOX,
                SEAT_6_REGULAR,
                SEAT_7_REGULAR,
                SEAT_8_REGULAR
            )
        )
    }

    @Test
    fun `test AdminGetDBoxReservationDataQuery - no screenings - should return empty reservations`() {
        val result = underTest()
        assertTrue(result.reservations.reservationList.isEmpty())
    }

    @Test
    fun `test AdminGetDBoxReservationDataQuery - should return correct reservation states based on paid tickets`() {
        // Valid screening with D-BOX seats in auditorium 1 (future date)
        val validScreening1 = createTestScreening(
            originalId = 1,
            time = LocalTime.of(20, 30),
            date = INTEGRATION_TEST_DATE_TIME.plusDays(2).toLocalDate(),
            auditoriumId = AUDITORIUM_1.id,
            auditoriumLayoutId = AUDITORIUM_LAYOUT_1.id,
            movieId = movie.id,
            paidSeats = setOf(SEAT_1_DBOX, SEAT_2_DBOX),
            unpaidSeats = setOf(SEAT_3_DBOX)
        )

        // Valid screening with D-BOX seats in auditorium 2 (current day, future time)
        val validScreening2 = createTestScreening(
            originalId = 2,
            time = INTEGRATION_TEST_DATE_TIME.toLocalTime().plusHours(1),
            date = INTEGRATION_TEST_DATE_TIME.toLocalDate(),
            auditoriumId = AUDITORIUM_2.id,
            auditoriumLayoutId = AUDITORIUM_LAYOUT_2.id,
            movieId = movie.id,
            paidSeats = setOf(SEAT_4_DBOX),
            unpaidSeats = setOf(SEAT_5_DBOX)
        )

        // Past day screening - should not appear
        createTestScreening(
            originalId = 3,
            date = INTEGRATION_TEST_DATE_TIME.toLocalDate().minusDays(1),
            auditoriumId = AUDITORIUM_1.id,
            auditoriumLayoutId = AUDITORIUM_LAYOUT_1.id,
            movieId = movie.id,
            paidSeats = setOf(SEAT_1_DBOX)
        )

        // Future screening but cancelled - should not appear
        createTestScreening(
            originalId = 4,
            date = INTEGRATION_TEST_DATE_TIME.plusDays(3).toLocalDate(),
            auditoriumId = AUDITORIUM_1.id,
            auditoriumLayoutId = AUDITORIUM_LAYOUT_1.id,
            movieId = movie.id,
            paidSeats = setOf(SEAT_1_DBOX),
            cancelled = true
        )

        // Future screening but stopped - should not appear
        createTestScreening(
            originalId = 5,
            date = INTEGRATION_TEST_DATE_TIME.plusDays(3).toLocalDate(),
            auditoriumId = AUDITORIUM_1.id,
            auditoriumLayoutId = AUDITORIUM_LAYOUT_1.id,
            movieId = movie.id,
            paidSeats = setOf(SEAT_1_DBOX),
            stopped = true
        )

        // Future screening but deleted - should not appear
        createTestScreening(
            originalId = 6,
            date = INTEGRATION_TEST_DATE_TIME.plusDays(3).toLocalDate(),
            auditoriumId = AUDITORIUM_1.id,
            auditoriumLayoutId = AUDITORIUM_LAYOUT_1.id,
            movieId = movie.id,
            paidSeats = setOf(SEAT_1_DBOX),
            deleted = true
        )

        // Future screening but not in PUBLISHED state - should not appear
        createTestScreening(
            originalId = 7,
            date = INTEGRATION_TEST_DATE_TIME.plusDays(3).toLocalDate(),
            auditoriumId = AUDITORIUM_1.id,
            auditoriumLayoutId = AUDITORIUM_LAYOUT_1.id,
            movieId = movie.id,
            paidSeats = setOf(SEAT_1_DBOX),
            state = ScreeningState.DRAFT
        )

        // Valid future screening but in auditorium without D-BOX seats - should not appear
        createTestScreening(
            originalId = 8,
            date = INTEGRATION_TEST_DATE_TIME.plusDays(1).toLocalDate(),
            time = LocalTime.of(18, 0),
            auditoriumId = AUDITORIUM_3.id,
            auditoriumLayoutId = AUDITORIUM_LAYOUT_3.id,
            movieId = movie.id,
            paidSeats = setOf(SEAT_6_REGULAR, SEAT_7_REGULAR),
            unpaidSeats = setOf(SEAT_8_REGULAR)
        )

        val result = underTest()

        // Should contain only D-BOX seats from valid screenings
        assertEquals(5, result.reservations.reservationList.size)

        // Group reservations by presentation ID
        val reservationsByPresentation = result.reservations.reservationList.groupBy { it.presentationId }

        // Verify first screening reservations
        val screening1Reservations = reservationsByPresentation[validScreening1.originalId.toString()]
        assertEquals(3, screening1Reservations?.size)

        // Count reserved (state="1") and available (state="0") seats in first screening
        val screening1Reserved = screening1Reservations?.count { it.state == "1" } ?: 0
        val screening1Available = screening1Reservations?.count { it.state == "0" } ?: 0
        assertEquals(2, screening1Reserved) // Two paid seats
        assertEquals(1, screening1Available) // One unpaid seat

        // Verify second screening reservations
        val screening2Reservations = reservationsByPresentation[validScreening2.originalId.toString()]
        assertEquals(2, screening2Reservations?.size)

        // Count reserved (state="1") and available (state="0") seats in second screening
        val screening2Reserved = screening2Reservations?.count { it.state == "1" } ?: 0
        val screening2Available = screening2Reservations?.count { it.state == "0" } ?: 0
        assertEquals(1, screening2Reserved) // One paid seat
        assertEquals(1, screening2Available) // One unpaid seat

        // Verify that past day screening is not included
        assertTrue(reservationsByPresentation["3"] == null)

        // Verify that cancelled screening is not included
        assertTrue(reservationsByPresentation["4"] == null)

        // Verify that stopped screening is not included
        assertTrue(reservationsByPresentation["5"] == null)

        // Verify that deleted screening is not included
        assertTrue(reservationsByPresentation["6"] == null)

        // Verify that draft screening is not included
        assertTrue(reservationsByPresentation["7"] == null)

        // Verify that screening with only regular seats is not included
        assertTrue(reservationsByPresentation["8"] == null)

        // Verify specific seat details for first screening
        screening1Reservations?.find { it.seatId == "A1" }?.let { seat ->
            assertEquals(validScreening1.originalId.toString(), seat.presentationId)
            assertEquals("A", seat.seatRow)
            assertEquals("1", seat.seatCol)
            assertEquals("1", seat.state) // Reserved (paid)
        }

        screening1Reservations?.find { it.seatId == "A2" }?.let { seat ->
            assertEquals(validScreening1.originalId.toString(), seat.presentationId)
            assertEquals("A", seat.seatRow)
            assertEquals("2", seat.seatCol)
            assertEquals("1", seat.state) // Reserved (paid)
        }

        screening1Reservations?.find { it.seatId == "A3" }?.let { seat ->
            assertEquals(validScreening1.originalId.toString(), seat.presentationId)
            assertEquals("A", seat.seatRow)
            assertEquals("3", seat.seatCol)
            assertEquals("0", seat.state) // Available (unpaid)
        }

        // Verify specific seat details for second screening
        screening2Reservations?.find { it.seatId == "B1" }?.let { seat ->
            assertEquals(validScreening2.originalId.toString(), seat.presentationId)
            assertEquals("B", seat.seatRow)
            assertEquals("1", seat.seatCol)
            assertEquals("1", seat.state) // Reserved (paid)
        }

        screening2Reservations?.find { it.seatId == "B2" }?.let { seat ->
            assertEquals(validScreening2.originalId.toString(), seat.presentationId)
            assertEquals("B", seat.seatRow)
            assertEquals("2", seat.seatCol)
            assertEquals("0", seat.state) // Available (unpaid)
        }
    }

    private fun createTestScreening(
        date: LocalDate = INTEGRATION_TEST_DATE_TIME.toLocalDate(),
        time: LocalTime = LocalTime.of(19, 0),
        stopped: Boolean = false,
        cancelled: Boolean = false,
        deleted: Boolean = false,
        originalId: Int = 1,
        auditoriumId: UUID = AUDITORIUM_1.id,
        auditoriumLayoutId: UUID = AUDITORIUM_LAYOUT_1.id,
        movieId: UUID = movie.id,
        paidSeats: Set<Seat> = emptySet(),
        unpaidSeats: Set<Seat> = emptySet(),
        state: ScreeningState = ScreeningState.PUBLISHED,
    ): Screening {
        val screening = createScreening(
            originalId = originalId,
            auditoriumId = auditoriumId,
            auditoriumLayoutId = auditoriumLayoutId,
            movieId = movieId,
            priceCategoryId = PRICE_CATEGORY_1.id,
            date = date,
            time = time,
            stopped = stopped,
            cancelled = cancelled,
            state = state
        ).also {
            if (deleted) it.markDeleted()
        }
        screeningRepository.save(screening)

        // Create paid tickets for seats
        if (paidSeats.isNotEmpty()) {
            val paidBasket = createBasket(state = BasketState.PAID)
            basketRepository.save(paidBasket)

            paidSeats.forEach { seat ->
                createTicketWithReservation(screening, seat, paidBasket)
            }
        }

        // Create unpaid reservations for seats
        if (unpaidSeats.isNotEmpty()) {
            val unpaidBasket = createBasket(state = BasketState.PAYMENT_IN_PROGRESS)
            basketRepository.save(unpaidBasket)

            unpaidSeats.forEach { seat ->
                createTicketWithReservation(screening, seat, unpaidBasket)
            }
        }

        return screening
    }

    private fun createTicketWithReservation(
        screening: Screening,
        seat: Seat,
        basket: Basket,
    ) {
        val ticketPrice = createTicketPrice(
            screeningId = screening.id,
            seatId = seat.id,
            totalPrice = 10.toBigDecimal(),
            seatSurchargeType = SeatSurchargeType.DBOX,
            seatSurcharge = 1.toBigDecimal()
        )
        ticketPriceRepository.save(ticketPrice)

        val reservation = createReservation(
            screeningId = screening.id,
            seatId = seat.id,
            state = ReservationState.UNAVAILABLE
        )
        reservationRepository.save(reservation)

        val ticket = createTicket(
            screeningId = screening.id,
            reservationId = reservation.id,
            ticketPriceId = ticketPrice.id
        )
        ticketRepository.save(ticket)

        val basketItem = createBasketItem(
            basketId = basket.id,
            type = BasketItemType.TICKET,
            price = 10.toBigDecimal(),
            ticketId = ticket.id
        )
        basketItemRepository.save(basketItem)
    }
}

private val AUDITORIUM_1 = createAuditorium(
    originalId = 1,
    code = "A",
    title = "SÁLA A - CINEMAX BRATISLAVA",
    originalCode = 22
)
private val AUDITORIUM_2 = createAuditorium(
    originalId = 2,
    code = "B",
    title = "SÁLA B - CINEMAX BRATISLAVA",
    originalCode = 33
)
private val AUDITORIUM_3 = createAuditorium(
    originalId = 3,
    code = "C",
    title = "SÁLA C - CINEMAX BRATISLAVA",
    originalCode = 44
)
private val DISTRIBUTOR_1 = createDistributor(originalId = 1)
private val AUDITORIUM_LAYOUT_1 = createAuditoriumLayout(originalId = 1, auditoriumId = AUDITORIUM_1.id)
private val AUDITORIUM_LAYOUT_2 = createAuditoriumLayout(originalId = 2, auditoriumId = AUDITORIUM_2.id)
private val AUDITORIUM_LAYOUT_3 = createAuditoriumLayout(originalId = 3, auditoriumId = AUDITORIUM_3.id)
private val PRICE_CATEGORY_1 = createPriceCategory(
    originalId = 1,
    title = "STANDARD",
    active = true
)
private val SEAT_1_DBOX = createSeat(
    originalId = 1,
    auditoriumId = AUDITORIUM_1.id,
    auditoriumLayoutId = AUDITORIUM_LAYOUT_1.id,
    type = SeatType.DBOX,
    row = "A",
    number = "1"
)
private val SEAT_2_DBOX = createSeat(
    originalId = 2,
    auditoriumId = AUDITORIUM_1.id,
    auditoriumLayoutId = AUDITORIUM_LAYOUT_1.id,
    type = SeatType.DBOX,
    row = "A",
    number = "2"
)
private val SEAT_3_DBOX = createSeat(
    originalId = 3,
    auditoriumId = AUDITORIUM_1.id,
    auditoriumLayoutId = AUDITORIUM_LAYOUT_1.id,
    type = SeatType.DBOX,
    row = "A",
    number = "3"
)
private val SEAT_4_DBOX = createSeat(
    originalId = 4,
    auditoriumId = AUDITORIUM_2.id,
    auditoriumLayoutId = AUDITORIUM_LAYOUT_2.id,
    type = SeatType.DBOX,
    row = "B",
    number = "1"
)
private val SEAT_5_DBOX = createSeat(
    originalId = 5,
    auditoriumId = AUDITORIUM_2.id,
    auditoriumLayoutId = AUDITORIUM_LAYOUT_2.id,
    type = SeatType.DBOX,
    row = "B",
    number = "2"
)
private val SEAT_6_REGULAR = createSeat(
    originalId = 6,
    auditoriumId = AUDITORIUM_3.id,
    auditoriumLayoutId = AUDITORIUM_LAYOUT_3.id,
    type = SeatType.REGULAR,
    row = "C",
    number = "1"
)
private val SEAT_7_REGULAR = createSeat(
    originalId = 7,
    auditoriumId = AUDITORIUM_3.id,
    auditoriumLayoutId = AUDITORIUM_LAYOUT_3.id,
    type = SeatType.REGULAR,
    row = "C",
    number = "2"
)
private val SEAT_8_REGULAR = createSeat(
    originalId = 8,
    auditoriumId = AUDITORIUM_3.id,
    auditoriumLayoutId = AUDITORIUM_LAYOUT_3.id,
    type = SeatType.REGULAR,
    row = "C",
    number = "3"
)
