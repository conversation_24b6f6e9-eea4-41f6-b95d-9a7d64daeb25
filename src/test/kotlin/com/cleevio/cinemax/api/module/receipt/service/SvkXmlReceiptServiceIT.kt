package com.cleevio.cinemax.api.module.receipt.service

import com.cleevio.cinemax.api.DatabaseCleanup
import com.cleevio.cinemax.api.IntegrationTest
import com.cleevio.cinemax.api.common.constant.PaymentType
import com.cleevio.cinemax.api.common.constant.STANDARD_TAX_RATE
import com.cleevio.cinemax.api.module.basket.event.BasketItemModifiedEvent
import com.cleevio.cinemax.api.module.basket.event.ProductBasketItemAddedToBasketEvent
import com.cleevio.cinemax.api.module.basket.service.BasketRepository
import com.cleevio.cinemax.api.module.basket.service.BasketService
import com.cleevio.cinemax.api.module.basketitem.constant.BasketItemType
import com.cleevio.cinemax.api.module.basketitem.controller.dto.CreateBasketItemRequest
import com.cleevio.cinemax.api.module.basketitem.controller.dto.CreateProductRequest
import com.cleevio.cinemax.api.module.basketitem.service.BasketItemRepository
import com.cleevio.cinemax.api.module.basketitem.service.BasketItemService
import com.cleevio.cinemax.api.module.branch.service.BranchRepository
import com.cleevio.cinemax.api.module.outboxevent.event.BasketCancellationReceiptPrintedEvent
import com.cleevio.cinemax.api.module.outboxevent.event.BasketReceiptPrintedEvent
import com.cleevio.cinemax.api.module.posconfiguration.constant.ProductMode
import com.cleevio.cinemax.api.module.posconfiguration.constant.TablesType
import com.cleevio.cinemax.api.module.posconfiguration.service.PosConfigurationService
import com.cleevio.cinemax.api.module.posconfiguration.service.command.CreateOrUpdatePosConfigurationCommand
import com.cleevio.cinemax.api.module.product.service.ProductService
import com.cleevio.cinemax.api.module.productcategory.service.ProductCategoryService
import com.cleevio.cinemax.api.module.productcomponent.constant.ProductComponentUnit
import com.cleevio.cinemax.api.module.productcomponent.service.ProductComponentService
import com.cleevio.cinemax.api.module.productcomponentcategory.service.ProductComponentCategoryRepository
import com.cleevio.cinemax.api.module.productcomposition.service.ProductCompositionService
import com.cleevio.cinemax.api.module.receipt.constant.ReceiptPrintState
import com.cleevio.cinemax.api.module.receipt.constant.ReceiptType
import com.cleevio.cinemax.api.module.receipt.event.PrintStateDeterminedEvent
import com.cleevio.cinemax.api.module.receipt.exception.BasketWithoutCancelledItemsException
import com.cleevio.cinemax.api.module.receipt.service.command.GenerateAndPersistCancellationReceiptCommand
import com.cleevio.cinemax.api.module.receipt.service.command.GenerateAndPersistReceiptCommand
import com.cleevio.cinemax.api.module.receipt.service.command.PrintCancellationReceiptCommand
import com.cleevio.cinemax.api.module.receipt.service.command.PrintReceiptCommand
import com.cleevio.cinemax.api.util.createBranch
import com.cleevio.cinemax.api.util.createProduct
import com.cleevio.cinemax.api.util.createProductCategory
import com.cleevio.cinemax.api.util.createProductComponent
import com.cleevio.cinemax.api.util.createProductComponentCategory
import com.cleevio.cinemax.api.util.createProductComposition
import com.cleevio.cinemax.api.util.mapInputItemsToInitBasketCommand
import com.cleevio.cinemax.api.util.mapToCreateBasketItemCommand
import com.cleevio.cinemax.api.util.mapToCreateOrUpdateProductCategoryCommand
import com.cleevio.cinemax.api.util.mapToCreateOrUpdateProductComponentCommand
import com.cleevio.cinemax.api.util.mapToCreateOrUpdateProductCompositionCommand
import com.cleevio.cinemax.api.util.mapToSyncCreateOrUpdateProductCommand
import io.mockk.Runs
import io.mockk.every
import io.mockk.just
import io.mockk.verify
import io.mockk.verifyOrder
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import org.springframework.beans.factory.annotation.Autowired
import java.io.File
import java.math.BigDecimal
import java.util.UUID
import kotlin.concurrent.thread
import kotlin.test.assertEquals
import kotlin.test.assertNull

class SvkXmlReceiptServiceIT @Autowired constructor(
    private val underTest: SvkXmlReceiptService,
    private val basketService: BasketService,
    private val basketItemService: BasketItemService,
    private val basketRepository: BasketRepository,
    private val basketItemRepository: BasketItemRepository,
    private val productService: ProductService,
    private val productCategoryService: ProductCategoryService,
    private val productComponentService: ProductComponentService,
    private val productCompositionService: ProductCompositionService,
    private val posConfigurationService: PosConfigurationService,
    private val productComponentCategoryRepository: ProductComponentCategoryRepository,
    private val branchRepository: BranchRepository,
) : IntegrationTest(DatabaseCleanup.AFTER_EACH) {

    @BeforeEach
    fun setUp() {
        branchRepository.save(createBranch())
        productCategoryService.syncCreateOrUpdateProductCategory(mapToCreateOrUpdateProductCategoryCommand(PRODUCT_CATEGORY_1))
        productService.syncCreateOrUpdateProduct(mapToSyncCreateOrUpdateProductCommand(PRODUCT_1, PRODUCT_CATEGORY_1.id))
        productComponentCategoryRepository.save(PRODUCT_COMPONENT_CATEGORY_1)
        productComponentService.syncCreateOrUpdateProductComponent(
            mapToCreateOrUpdateProductComponentCommand(PRODUCT_COMPONENT_1)
        )
        productCompositionService.createOrUpdateProductComposition(
            mapToCreateOrUpdateProductCompositionCommand(PRODUCT_COMPOSITION_1)
        )
        posConfigurationService.createOrUpdatePosConfiguration(POS_CONFIGURATION_COMMAND)
    }

    @Test
    fun `test generateAndPersistReceipt - type RECEIPT - should create correct receipt`() {
        every { applicationEventPublisherMock.publishEvent(any<ProductBasketItemAddedToBasketEvent>()) } just Runs

        val basket = basketService.initBasket(mapInputItemsToInitBasketCommand(listOf()))
        val basketItem = basketItemService.createBasketItem(
            mapToCreateBasketItemCommand(PRODUCT_BASKET_ITEM_REQUEST_1, basket.id)
        )
        basketRepository.save(
            basket.apply {
                paymentType = PaymentType.CASHLESS
                paymentPosConfigurationId = POS_CONFIGURATION_ID
            }
        )
        basketItemRepository.save(
            basketItem.apply {
                productReceiptNumber = DUMMY_RECEIPT_NUMBER
            }
        )

        val createdReceipt = underTest.generateAndPersistReceipt(
            GenerateAndPersistReceiptCommand(
                basketId = basket.id,
                receiptsDirectory = DUMMY_RECEIPTS_DIRECTORY
            )
        )

        assertEquals(basket.id, createdReceipt.basketId)
        assertEquals("/pos$DUMMY_RECEIPTS_DIRECTORY", createdReceipt.directory)
        assertEquals(EXPECTED_RECEIPT, createdReceipt.content.trimIndent())
        assertNull(createdReceipt.lastPrintState)
        assertEquals(ReceiptType.RECEIPT, createdReceipt.type)
    }

    @Test
    fun `test generateAndPersistCancellationReceipt - type CANCELLATION - should create correct receipt`() {
        every { applicationEventPublisherMock.publishEvent(any<ProductBasketItemAddedToBasketEvent>()) } just Runs

        val basket = basketService.initBasket(mapInputItemsToInitBasketCommand(listOf()))
        val basketItem = basketItemService.createBasketItem(
            mapToCreateBasketItemCommand(PRODUCT_BASKET_ITEM_REQUEST_1, basket.id)
        )
        val cancelledBasketItem = basketItemService.createBasketItem(
            mapToCreateBasketItemCommand(PRODUCT_BASKET_ITEM_REQUEST_2, basket.id)
        )
        basketRepository.save(
            basket.apply {
                paymentType = PaymentType.CASHLESS
                paymentPosConfigurationId = POS_CONFIGURATION_ID
            }
        )
        basketItemRepository.save(
            basketItem.apply {
                productReceiptNumber = DUMMY_RECEIPT_NUMBER
            }
        )
        basketItemRepository.save(
            cancelledBasketItem.apply {
                cancelledBasketItemId = basketItem.id
                productReceiptNumber = DUMMY_RECEIPT_NUMBER
            }
        )

        val createdReceipt = underTest.generateAndPersistCancellationReceipt(
            GenerateAndPersistCancellationReceiptCommand(
                basketId = basket.id,
                receiptsDirectory = DUMMY_RECEIPTS_DIRECTORY
            )
        )

        assertEquals(basket.id, createdReceipt.basketId)
        assertEquals("/pos$DUMMY_RECEIPTS_DIRECTORY", createdReceipt.directory)
        assertEquals(EXPECTED_CANCELLATION_RECEIPT, createdReceipt.content)
        assertNull(createdReceipt.lastPrintState)
        assertEquals(ReceiptType.CANCELLATION, createdReceipt.type)
    }

    @Test
    fun `test generateAndPersistCancellationReceipt - basket without cancelled items - should throw exception`() {
        every { applicationEventPublisherMock.publishEvent(any<ProductBasketItemAddedToBasketEvent>()) } just Runs

        val basket = basketService.initBasket(mapInputItemsToInitBasketCommand(listOf()))
        val basketItem = basketItemService.createBasketItem(
            mapToCreateBasketItemCommand(PRODUCT_BASKET_ITEM_REQUEST_1, basket.id)
        )
        basketRepository.save(
            basket.apply {
                paymentType = PaymentType.CASHLESS
                paymentPosConfigurationId = POS_CONFIGURATION_ID
            }
        )
        basketItemRepository.save(
            basketItem.apply {
                productReceiptNumber = DUMMY_RECEIPT_NUMBER
            }
        )

        assertThrows<BasketWithoutCancelledItemsException> {
            underTest.generateAndPersistCancellationReceipt(
                GenerateAndPersistCancellationReceiptCommand(
                    basketId = basket.id,
                    receiptsDirectory = DUMMY_RECEIPTS_DIRECTORY
                )
            )
        }
    }

    @Test
    fun `test printReceipt - input XML file is processed and output XML file with header success=true is created - should return SUCCESS`() {
        every { applicationEventPublisherMock.publishEvent(any<ProductBasketItemAddedToBasketEvent>()) } just Runs
        every { applicationEventPublisherMock.publishEvent(any<PrintStateDeterminedEvent>()) } just Runs
        every { applicationEventPublisherMock.publishEvent(any<BasketReceiptPrintedEvent>()) } just Runs

        val basket = basketService.initBasket(mapInputItemsToInitBasketCommand(listOf()))
        val basketItem = basketItemService.createBasketItem(
            mapToCreateBasketItemCommand(PRODUCT_BASKET_ITEM_REQUEST_1, basket.id)
        )
        basketRepository.save(
            basket.apply {
                paymentType = PaymentType.CASHLESS
                paymentPosConfigurationId = POS_CONFIGURATION_ID
            }
        )
        basketItemRepository.save(
            basketItem.apply {
                productReceiptNumber = DUMMY_RECEIPT_NUMBER
            }
        )

        val receipt = underTest.generateAndPersistReceipt(
            GenerateAndPersistReceiptCommand(
                basketId = basket.id,
                receiptsDirectory = DUMMY_RECEIPTS_DIRECTORY
            )
        )

        val inputFile = File("/pos$DUMMY_RECEIPTS_DIRECTORY/in.xml")
        val outputFile = File("/pos$DUMMY_RECEIPTS_DIRECTORY/out.xml")
        val filesThread = thread {
            Thread.sleep(1000)
            inputFile.delete()
            Thread.sleep(100)
            outputFile.writeText(OUT_XML_RESPONSE_SUCCESS)
        }

        var receiptPrintState = ReceiptPrintState.UNKNOWN
        val executionThread = thread {
            receiptPrintState = underTest.printReceipt(
                PrintReceiptCommand(
                    basketId = basket.id
                )
            )
        }

        filesThread.join()
        executionThread.join()

        assertEquals(ReceiptPrintState.SUCCESS, receiptPrintState)

        verifyOrder {
            applicationEventPublisherMock.publishEvent(ProductBasketItemAddedToBasketEvent(PRODUCT_1.id, 1))
            applicationEventPublisherMock.publishEvent(PrintStateDeterminedEvent(receipt.id, ReceiptPrintState.SUCCESS))
            applicationEventPublisherMock.publishEvent(BasketReceiptPrintedEvent(basket.id))
        }
    }

    @Test
    fun `test printReceipt - input XML file is processed and output XML file with header success=false is created - should return FAILED`() {
        every { applicationEventPublisherMock.publishEvent(any<ProductBasketItemAddedToBasketEvent>()) } just Runs
        every { applicationEventPublisherMock.publishEvent(any<BasketItemModifiedEvent>()) } just Runs
        every { applicationEventPublisherMock.publishEvent(any<PrintStateDeterminedEvent>()) } just Runs

        val basket = basketService.initBasket(mapInputItemsToInitBasketCommand(listOf()))
        val basketItem = basketItemService.createBasketItem(
            mapToCreateBasketItemCommand(PRODUCT_BASKET_ITEM_REQUEST_1, basket.id)
        )
        basketRepository.save(
            basket.apply {
                paymentType = PaymentType.CASHLESS
                paymentPosConfigurationId = POS_CONFIGURATION_ID
            }
        )
        basketItemRepository.save(
            basketItem.apply {
                productReceiptNumber = DUMMY_RECEIPT_NUMBER
            }
        )

        val receipt = underTest.generateAndPersistReceipt(
            GenerateAndPersistReceiptCommand(
                basketId = basket.id,
                receiptsDirectory = DUMMY_RECEIPTS_DIRECTORY
            )
        )

        val inputFile = File("/pos$DUMMY_RECEIPTS_DIRECTORY/in.xml")
        val outputFile = File("/pos$DUMMY_RECEIPTS_DIRECTORY/out.xml")
        val filesThread = thread {
            Thread.sleep(1000)
            inputFile.delete()
            Thread.sleep(100)
            outputFile.writeText(OUT_XML_RESPONSE_FAILURE)
        }

        var receiptPrintState = ReceiptPrintState.UNKNOWN
        val executionThread = thread {
            receiptPrintState = underTest.printReceipt(
                PrintReceiptCommand(
                    basketId = basket.id
                )
            )
        }

        filesThread.join()
        executionThread.join()

        assertEquals(ReceiptPrintState.FAILED, receiptPrintState)

        verifyOrder {
            applicationEventPublisherMock.publishEvent(ProductBasketItemAddedToBasketEvent(PRODUCT_1.id, 1))
            applicationEventPublisherMock.publishEvent(BasketItemModifiedEvent(basket.id))
            applicationEventPublisherMock.publishEvent(PrintStateDeterminedEvent(receipt.id, ReceiptPrintState.FAILED))
        }
        verify(exactly = 0) { applicationEventPublisherMock.publishEvent(BasketReceiptPrintedEvent(basket.id)) }
    }

    @Test
    fun `test printReceipt - multiple receipts - should fetch latest non-cancelled one and return SUCCESS`() {
        every { applicationEventPublisherMock.publishEvent(any<ProductBasketItemAddedToBasketEvent>()) } just Runs
        every { applicationEventPublisherMock.publishEvent(any<BasketItemModifiedEvent>()) } just Runs
        every { applicationEventPublisherMock.publishEvent(any<PrintStateDeterminedEvent>()) } just Runs
        every { applicationEventPublisherMock.publishEvent(any<BasketReceiptPrintedEvent>()) } just Runs

        val basket = basketService.initBasket(mapInputItemsToInitBasketCommand(listOf()))
        val basketItem = basketItemService.createBasketItem(
            mapToCreateBasketItemCommand(PRODUCT_BASKET_ITEM_REQUEST_1, basket.id)
        )
        val cancelledBasketItem = basketItemService.createBasketItem(
            mapToCreateBasketItemCommand(PRODUCT_BASKET_ITEM_REQUEST_2, basket.id)
        )
        basketRepository.save(
            basket.apply {
                paymentType = PaymentType.CASHLESS
                paymentPosConfigurationId = POS_CONFIGURATION_ID
            }
        )
        basketItemRepository.save(
            basketItem.apply {
                productReceiptNumber = DUMMY_RECEIPT_NUMBER
            }
        )
        basketItemRepository.save(
            cancelledBasketItem.apply {
                cancelledBasketItemId = basketItem.id
                productReceiptNumber = DUMMY_RECEIPT_NUMBER
            }
        )

        val receipt1 = underTest.generateAndPersistReceipt(
            GenerateAndPersistReceiptCommand(
                basketId = basket.id,
                receiptsDirectory = DUMMY_RECEIPTS_DIRECTORY
            )
        )
        val receipt2 = underTest.generateAndPersistReceipt(
            GenerateAndPersistReceiptCommand(
                basketId = basket.id,
                receiptsDirectory = DUMMY_RECEIPTS_DIRECTORY
            )
        )
        val receipt3 = underTest.generateAndPersistCancellationReceipt(
            GenerateAndPersistCancellationReceiptCommand(
                basketId = basket.id,
                receiptsDirectory = DUMMY_RECEIPTS_DIRECTORY
            )
        )

        val inputFile = File("/pos$DUMMY_RECEIPTS_DIRECTORY/in.xml")
        val outputFile = File("/pos$DUMMY_RECEIPTS_DIRECTORY/out.xml")
        val filesThread = thread {
            Thread.sleep(1000)
            inputFile.delete()
            Thread.sleep(100)
            outputFile.writeText(OUT_XML_RESPONSE_SUCCESS)
        }

        var receiptPrintState = ReceiptPrintState.UNKNOWN
        val executionThread = thread {
            receiptPrintState = underTest.printReceipt(
                PrintReceiptCommand(
                    basketId = basket.id
                )
            )
        }

        filesThread.join()
        executionThread.join()

        assertEquals(ReceiptPrintState.SUCCESS, receiptPrintState)

        verify {
            applicationEventPublisherMock.publishEvent(ProductBasketItemAddedToBasketEvent(PRODUCT_1.id, 1))
            applicationEventPublisherMock.publishEvent(BasketItemModifiedEvent(basket.id))
            applicationEventPublisherMock.publishEvent(ProductBasketItemAddedToBasketEvent(PRODUCT_1.id, 1))
            applicationEventPublisherMock.publishEvent(BasketItemModifiedEvent(basket.id))
            applicationEventPublisherMock.publishEvent(
                PrintStateDeterminedEvent(
                    receiptId = receipt2.id,
                    printState = ReceiptPrintState.SUCCESS
                )
            )
            applicationEventPublisherMock.publishEvent(BasketReceiptPrintedEvent(basket.id))
        }
    }

    @Test
    fun `test printReceipt - directory contains output XML from previous print - should return SUCCESS`() {
        every { applicationEventPublisherMock.publishEvent(any<ProductBasketItemAddedToBasketEvent>()) } just Runs
        every { applicationEventPublisherMock.publishEvent(any<PrintStateDeterminedEvent>()) } just Runs
        every { applicationEventPublisherMock.publishEvent(any<BasketReceiptPrintedEvent>()) } just Runs

        val basket = basketService.initBasket(mapInputItemsToInitBasketCommand(listOf()))
        val basketItem = basketItemService.createBasketItem(
            mapToCreateBasketItemCommand(PRODUCT_BASKET_ITEM_REQUEST_1, basket.id)
        )
        basketRepository.save(
            basket.apply {
                paymentType = PaymentType.CASHLESS
                paymentPosConfigurationId = POS_CONFIGURATION_ID
            }
        )
        basketItemRepository.save(
            basketItem.apply {
                productReceiptNumber = DUMMY_RECEIPT_NUMBER
            }
        )

        val receipt = underTest.generateAndPersistReceipt(
            GenerateAndPersistReceiptCommand(
                basketId = basket.id,
                receiptsDirectory = DUMMY_RECEIPTS_DIRECTORY
            )
        )

        val inputFile = File("/pos$DUMMY_RECEIPTS_DIRECTORY/in.xml")
        val outputFile = File("/pos$DUMMY_RECEIPTS_DIRECTORY/out.xml")
        val filesThread = thread {
            outputFile.writeText(OUT_XML_RESPONSE_SUCCESS)
            Thread.sleep(1000)
            inputFile.delete()
            Thread.sleep(100)
            outputFile.writeText(OUT_XML_RESPONSE_SUCCESS)
        }

        var receiptPrintState = ReceiptPrintState.UNKNOWN
        val executionThread = thread {
            receiptPrintState = underTest.printReceipt(
                PrintReceiptCommand(
                    basketId = basket.id
                )
            )
        }

        filesThread.join()
        executionThread.join()

        assertEquals(ReceiptPrintState.SUCCESS, receiptPrintState)

        verifyOrder {
            applicationEventPublisherMock.publishEvent(ProductBasketItemAddedToBasketEvent(PRODUCT_1.id, 1))
            applicationEventPublisherMock.publishEvent(PrintStateDeterminedEvent(receipt.id, ReceiptPrintState.SUCCESS))
            applicationEventPublisherMock.publishEvent(BasketReceiptPrintedEvent(basket.id))
        }
    }

    @Test
    fun `test printReceipt - input XML file is processed and two output XML files are created - should return MULTIPLE_FILES`() {
        every { applicationEventPublisherMock.publishEvent(any<ProductBasketItemAddedToBasketEvent>()) } just Runs
        every { applicationEventPublisherMock.publishEvent(any<PrintStateDeterminedEvent>()) } just Runs

        val basket = basketService.initBasket(mapInputItemsToInitBasketCommand(listOf()))
        val basketItem = basketItemService.createBasketItem(
            mapToCreateBasketItemCommand(PRODUCT_BASKET_ITEM_REQUEST_1, basket.id)
        )
        basketRepository.save(
            basket.apply {
                paymentType = PaymentType.CASHLESS
                paymentPosConfigurationId = POS_CONFIGURATION_ID
            }
        )
        basketItemRepository.save(
            basketItem.apply {
                productReceiptNumber = DUMMY_RECEIPT_NUMBER
            }
        )

        val receipt = underTest.generateAndPersistReceipt(
            GenerateAndPersistReceiptCommand(
                basketId = basket.id,
                receiptsDirectory = DUMMY_RECEIPTS_DIRECTORY
            )
        )

        val inputFile = File("/pos$DUMMY_RECEIPTS_DIRECTORY/in.xml")
        val outputFile1 = File("/pos$DUMMY_RECEIPTS_DIRECTORY/out1.xml")
        val outputFile2 = File("/pos$DUMMY_RECEIPTS_DIRECTORY/out2.xml")
        val filesThread = thread {
            Thread.sleep(1000)
            inputFile.delete()
            Thread.sleep(100)
            outputFile1.writeText(OUT_XML_RESPONSE_SUCCESS)
            outputFile2.writeText(OUT_XML_RESPONSE_SUCCESS)
        }

        var receiptPrintState = ReceiptPrintState.UNKNOWN
        val executionThread = thread {
            receiptPrintState = underTest.printReceipt(
                PrintReceiptCommand(
                    basketId = basket.id
                )
            )
        }

        filesThread.join()
        executionThread.join()

        assertEquals(ReceiptPrintState.MULTIPLE_FILES, receiptPrintState)

        verifyOrder {
            applicationEventPublisherMock.publishEvent(ProductBasketItemAddedToBasketEvent(PRODUCT_1.id, 1))
            applicationEventPublisherMock.publishEvent(
                PrintStateDeterminedEvent(
                    receiptId = receipt.id,
                    printState = ReceiptPrintState.MULTIPLE_FILES
                )
            )
        }
    }

    @Test
    fun `test printReceipt - input XML file is stuck processing - should return PROCESSING`() {
        every { applicationEventPublisherMock.publishEvent(any<ProductBasketItemAddedToBasketEvent>()) } just Runs
        every { applicationEventPublisherMock.publishEvent(any<PrintStateDeterminedEvent>()) } just Runs

        val basket = basketService.initBasket(mapInputItemsToInitBasketCommand(listOf()))
        val basketItem = basketItemService.createBasketItem(
            mapToCreateBasketItemCommand(PRODUCT_BASKET_ITEM_REQUEST_1, basket.id)
        )
        basketRepository.save(
            basket.apply {
                paymentType = PaymentType.CASHLESS
                paymentPosConfigurationId = POS_CONFIGURATION_ID
            }
        )
        basketItemRepository.save(
            basketItem.apply {
                productReceiptNumber = DUMMY_RECEIPT_NUMBER
            }
        )

        val receipt = underTest.generateAndPersistReceipt(
            GenerateAndPersistReceiptCommand(
                basketId = basket.id,
                receiptsDirectory = DUMMY_RECEIPTS_DIRECTORY
            )
        )

        val inputFile = File("/pos$DUMMY_RECEIPTS_DIRECTORY/in.xml")
        val inputProcessingFile = File("/pos$DUMMY_RECEIPTS_DIRECTORY/in$.xml")
        val filesThread = thread {
            Thread.sleep(1000)
            inputFile.renameTo(inputProcessingFile)
        }

        var receiptPrintState = ReceiptPrintState.UNKNOWN
        val executionThread = thread {
            receiptPrintState = underTest.printReceipt(
                PrintReceiptCommand(
                    basketId = basket.id
                )
            )
        }

        filesThread.join()
        executionThread.join()

        assertEquals(ReceiptPrintState.PROCESSING, receiptPrintState)

        verifyOrder {
            applicationEventPublisherMock.publishEvent(ProductBasketItemAddedToBasketEvent(PRODUCT_1.id, 1))
            applicationEventPublisherMock.publishEvent(
                PrintStateDeterminedEvent(
                    receiptId = receipt.id,
                    printState = ReceiptPrintState.PROCESSING
                )
            )
        }
    }

    @Test
    fun `test printReceipt - input XML file is processed and output XML file is not created - should return NO_FILES`() {
        every { applicationEventPublisherMock.publishEvent(any<ProductBasketItemAddedToBasketEvent>()) } just Runs
        every { applicationEventPublisherMock.publishEvent(any<PrintStateDeterminedEvent>()) } just Runs

        val basket = basketService.initBasket(mapInputItemsToInitBasketCommand(listOf()))
        val basketItem = basketItemService.createBasketItem(
            mapToCreateBasketItemCommand(PRODUCT_BASKET_ITEM_REQUEST_1, basket.id)
        )
        basketRepository.save(
            basket.apply {
                paymentType = PaymentType.CASHLESS
                paymentPosConfigurationId = POS_CONFIGURATION_ID
            }
        )
        basketItemRepository.save(
            basketItem.apply {
                productReceiptNumber = DUMMY_RECEIPT_NUMBER
            }
        )

        val receipt = underTest.generateAndPersistReceipt(
            GenerateAndPersistReceiptCommand(
                basketId = basket.id,
                receiptsDirectory = DUMMY_RECEIPTS_DIRECTORY
            )
        )

        val inputFile = File("/pos$DUMMY_RECEIPTS_DIRECTORY/in.xml")
        val filesThread = thread {
            Thread.sleep(1000)
            inputFile.delete()
        }

        var receiptPrintState = ReceiptPrintState.UNKNOWN
        val executionThread = thread {
            receiptPrintState = underTest.printReceipt(
                PrintReceiptCommand(
                    basketId = basket.id
                )
            )
        }

        filesThread.join()
        executionThread.join()

        assertEquals(ReceiptPrintState.NO_FILES, receiptPrintState)

        verifyOrder {
            applicationEventPublisherMock.publishEvent(ProductBasketItemAddedToBasketEvent(PRODUCT_1.id, 1))
            applicationEventPublisherMock.publishEvent(
                PrintStateDeterminedEvent(
                    receipt.id,
                    ReceiptPrintState.NO_FILES
                )
            )
        }
    }

    @Test
    fun `test printReceipt - input XML file is not processed and output XML file is not created - should return CREATED`() {
        every { applicationEventPublisherMock.publishEvent(any<ProductBasketItemAddedToBasketEvent>()) } just Runs
        every { applicationEventPublisherMock.publishEvent(any<PrintStateDeterminedEvent>()) } just Runs

        val basket = basketService.initBasket(mapInputItemsToInitBasketCommand(listOf()))
        val basketItem = basketItemService.createBasketItem(
            mapToCreateBasketItemCommand(PRODUCT_BASKET_ITEM_REQUEST_1, basket.id)
        )
        basketRepository.save(
            basket.apply {
                paymentType = PaymentType.CASHLESS
                paymentPosConfigurationId = POS_CONFIGURATION_ID
            }
        )
        basketItemRepository.save(
            basketItem.apply {
                productReceiptNumber = DUMMY_RECEIPT_NUMBER
            }
        )

        val receipt = underTest.generateAndPersistReceipt(
            GenerateAndPersistReceiptCommand(
                basketId = basket.id,
                receiptsDirectory = DUMMY_RECEIPTS_DIRECTORY
            )
        )

        val filesThread = thread {
            Thread.sleep(1000)
        }

        var receiptPrintState = ReceiptPrintState.UNKNOWN
        val executionThread = thread {
            receiptPrintState = underTest.printReceipt(
                PrintReceiptCommand(
                    basketId = basket.id
                )
            )
        }

        filesThread.join()
        executionThread.join()

        assertEquals(ReceiptPrintState.CREATED, receiptPrintState)

        verifyOrder {
            applicationEventPublisherMock.publishEvent(ProductBasketItemAddedToBasketEvent(PRODUCT_1.id, 1))
            applicationEventPublisherMock.publishEvent(PrintStateDeterminedEvent(receipt.id, ReceiptPrintState.CREATED))
        }
    }

    @Test
    fun `test printCancellationReceipt - input XML file is processed and output XML file is created - should return SUCCESS`() {
        every { applicationEventPublisherMock.publishEvent(any<ProductBasketItemAddedToBasketEvent>()) } just Runs
        every { applicationEventPublisherMock.publishEvent(any<PrintStateDeterminedEvent>()) } just Runs
        every { applicationEventPublisherMock.publishEvent(any<BasketReceiptPrintedEvent>()) } just Runs

        val basket = basketService.initBasket(mapInputItemsToInitBasketCommand(listOf()))
        val basketItem = basketItemService.createBasketItem(
            mapToCreateBasketItemCommand(PRODUCT_BASKET_ITEM_REQUEST_1, basket.id)
        )
        val cancelledBasketItem = basketItemService.createBasketItem(
            mapToCreateBasketItemCommand(PRODUCT_BASKET_ITEM_REQUEST_2, basket.id)
        )
        basketRepository.save(
            basket.apply {
                paymentType = PaymentType.CASHLESS
                paymentPosConfigurationId = POS_CONFIGURATION_ID
            }
        )
        basketItemRepository.save(
            basketItem.apply {
                productReceiptNumber = DUMMY_RECEIPT_NUMBER
            }
        )
        basketItemRepository.save(
            cancelledBasketItem.apply {
                cancelledBasketItemId = basketItem.id
                productReceiptNumber = DUMMY_RECEIPT_NUMBER
            }
        )

        val receipt = underTest.generateAndPersistCancellationReceipt(
            GenerateAndPersistCancellationReceiptCommand(
                basketId = basket.id,
                receiptsDirectory = DUMMY_RECEIPTS_DIRECTORY
            )
        )

        val inputFile = File("/pos$DUMMY_RECEIPTS_DIRECTORY/in.xml")
        val outputFile = File("/pos$DUMMY_RECEIPTS_DIRECTORY/out.xml")
        val filesThread = thread {
            Thread.sleep(1000)
            inputFile.delete()
            Thread.sleep(100)
            outputFile.writeText(OUT_XML_RESPONSE_SUCCESS)
        }

        var receiptPrintState = ReceiptPrintState.UNKNOWN
        val executionThread = thread {
            receiptPrintState = underTest.printCancellationReceipt(
                PrintCancellationReceiptCommand(
                    basketId = basket.id
                )
            )
        }

        filesThread.join()
        executionThread.join()

        assertEquals(ReceiptPrintState.SUCCESS, receiptPrintState)

        verifyOrder {
            applicationEventPublisherMock.publishEvent(ProductBasketItemAddedToBasketEvent(PRODUCT_1.id, 1))
            applicationEventPublisherMock.publishEvent(PrintStateDeterminedEvent(receipt.id, ReceiptPrintState.SUCCESS))
            applicationEventPublisherMock.publishEvent(BasketCancellationReceiptPrintedEvent(basket.id))
        }
    }

    @Test
    fun `test printCancellationReceipt - multiple receipts - should fetch latest cancelled one and return SUCCESS`() {
        every { applicationEventPublisherMock.publishEvent(any<ProductBasketItemAddedToBasketEvent>()) } just Runs
        every { applicationEventPublisherMock.publishEvent(any<BasketItemModifiedEvent>()) } just Runs
        every { applicationEventPublisherMock.publishEvent(any<PrintStateDeterminedEvent>()) } just Runs
        every { applicationEventPublisherMock.publishEvent(any<BasketReceiptPrintedEvent>()) } just Runs

        val basket = basketService.initBasket(mapInputItemsToInitBasketCommand(listOf()))
        val basketItem = basketItemService.createBasketItem(
            mapToCreateBasketItemCommand(PRODUCT_BASKET_ITEM_REQUEST_1, basket.id)
        )
        val cancelledBasketItem = basketItemService.createBasketItem(
            mapToCreateBasketItemCommand(PRODUCT_BASKET_ITEM_REQUEST_2, basket.id)
        )
        basketRepository.save(
            basket.apply {
                paymentType = PaymentType.CASHLESS
                paymentPosConfigurationId = POS_CONFIGURATION_ID
            }
        )
        basketItemRepository.save(
            basketItem.apply {
                productReceiptNumber = DUMMY_RECEIPT_NUMBER
            }
        )
        basketItemRepository.save(
            cancelledBasketItem.apply {
                cancelledBasketItemId = basketItem.id
                productReceiptNumber = DUMMY_RECEIPT_NUMBER
            }
        )

        val receipt1 = underTest.generateAndPersistCancellationReceipt(
            GenerateAndPersistCancellationReceiptCommand(
                basketId = basket.id,
                receiptsDirectory = DUMMY_RECEIPTS_DIRECTORY
            )
        )
        val receipt2 = underTest.generateAndPersistCancellationReceipt(
            GenerateAndPersistCancellationReceiptCommand(
                basketId = basket.id,
                receiptsDirectory = DUMMY_RECEIPTS_DIRECTORY
            )
        )
        val receipt3 = underTest.generateAndPersistReceipt(
            GenerateAndPersistReceiptCommand(
                basketId = basket.id,
                receiptsDirectory = DUMMY_RECEIPTS_DIRECTORY
            )
        )

        val inputFile = File("/pos$DUMMY_RECEIPTS_DIRECTORY/in.xml")
        val outputFile = File("/pos$DUMMY_RECEIPTS_DIRECTORY/out.xml")
        val filesThread = thread {
            Thread.sleep(1000)
            inputFile.delete()
            Thread.sleep(100)
            outputFile.writeText(OUT_XML_RESPONSE_SUCCESS)
        }

        var receiptPrintState = ReceiptPrintState.UNKNOWN
        val executionThread = thread {
            receiptPrintState = underTest.printCancellationReceipt(
                PrintCancellationReceiptCommand(
                    basketId = basket.id
                )
            )
        }

        filesThread.join()
        executionThread.join()

        assertEquals(ReceiptPrintState.SUCCESS, receiptPrintState)

        verify {
            applicationEventPublisherMock.publishEvent(ProductBasketItemAddedToBasketEvent(PRODUCT_1.id, 1))
            applicationEventPublisherMock.publishEvent(BasketItemModifiedEvent(basket.id))
            applicationEventPublisherMock.publishEvent(ProductBasketItemAddedToBasketEvent(PRODUCT_1.id, 1))
            applicationEventPublisherMock.publishEvent(BasketItemModifiedEvent(basket.id))
            applicationEventPublisherMock.publishEvent(
                PrintStateDeterminedEvent(
                    receiptId = receipt2.id,
                    printState = ReceiptPrintState.SUCCESS
                )
            )
            applicationEventPublisherMock.publishEvent(BasketCancellationReceiptPrintedEvent(basket.id))
        }
    }
}

private const val DUMMY_RECEIPTS_DIRECTORY = "/receipts/directory"
private const val DUMMY_RECEIPT_NUMBER = "*********"
private const val EXPECTED_RECEIPT = """<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<ekasa:RegisterReceiptRequest xmlns:ekasa="ekasa_cnmx.xsd">
  <ekasa:Header Version="1.0" Uuid="B-*********" ExpectResponse="true" SwId="12200.82 (11.3.2019)"/>
  <ekasa:ReceiptData ReceiptType="PD" ReceiptNumber="*********" Amount="10.00" TaxBaseBasic="8.13" TaxBaseReduced="0.00" BasicVatAmount="1.87" ReducedVatAmount="0.00" TaxFreeAmount="0.00">
    <ekasa:Items predal="dummyPOS">
      <ekasa:Item Name="Popcorn XXL" ItemType="K" Quantity="1.000000" VatRate="23" UnitPrice="10.00" Price="10.00"/>
    </ekasa:Items>
    <ekasa:Payments>
      <ekasa:Payment Amount="10.00" PaymentType="KA"/>
    </ekasa:Payments>
  </ekasa:ReceiptData>
</ekasa:RegisterReceiptRequest>"""
private const val EXPECTED_CANCELLATION_RECEIPT = """<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<ekasa:RegisterReceiptRequest xmlns:ekasa="ekasa_cnmx.xsd">
  <ekasa:Header Version="1.0" Uuid="B-*********" ExpectResponse="true" SwId="12200.82 (11.3.2019)"/>
  <ekasa:ReceiptData ReceiptType="PD" ReceiptNumber="*********" Amount="-10.00" TaxBaseBasic="-8.13" TaxBaseReduced="0.00" BasicVatAmount="-1.87" ReducedVatAmount="0.00" TaxFreeAmount="0.00">
    <ekasa:Items predal="dummyPOS">
      <ekasa:Item Name="Popcorn XXL" ItemType="V" Quantity="1.000000" VatRate="23" UnitPrice="-10.00" Price="-10.00" ReferenceReceiptId="*********"/>
    </ekasa:Items>
    <ekasa:Payments>
      <ekasa:Payment Amount="-10.00" PaymentType="KA"/>
    </ekasa:Payments>
  </ekasa:ReceiptData>
</ekasa:RegisterReceiptRequest>
"""
private const val OUT_XML_RESPONSE_SUCCESS = """
<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<RegisterReceiptResponse xmlns="ekasa_cnmx.xsd">
<Header Success="true" RequestUuid="B-0000606033" EkasaErrorCode="0" Version="1.0" ProtocolID="1074E0DF-CA98-4DD2-8E6C-6CEF88506C18"/>
<ReceiptData Success="true" EkasaErrorCode="0" ReceiptNumber="0000606033" Id="O-DE3E05E8E81349E1BE05E8E81389E199" OKP="D81B5B9C-57AF33B4-B00B793D-50E24C84-A2D5C69C"/>
</RegisterReceiptResponse>
"""
private const val OUT_XML_RESPONSE_FAILURE = """
<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<RegisterReceiptResponse xmlns="ekasa_cnmx.xsd">
<Header Success="false" RequestUuid="B-0000606033" EkasaErrorCode="0" Version="1.0" ProtocolID="1074E0DF-CA98-4DD2-8E6C-6CEF88506C18"/>
<ReceiptData Success="false" EkasaErrorCode="0" ReceiptNumber="1200606033" Id="" OKP=""/>
<ReceiptData Success="true" EkasaErrorCode="0" ReceiptNumber="0000606033" Id="O-DE3E05E8E81349E1BE05E8E81389E199" OKP="D81B5B9C-57AF33B4-B00B793D-50E24C84-A2D5C69C"/>
</RegisterReceiptResponse>
"""

private val PRODUCT_CATEGORY_1 = createProductCategory(
    originalId = 1,
    code = "A1",
    title = "Snacks - doplnky",
    taxRate = STANDARD_TAX_RATE
)
private val PRODUCT_1 = createProduct(
    originalId = 1,
    code = "01",
    productCategoryId = PRODUCT_CATEGORY_1.id,
    title = "Popcorn XXL",
    price = BigDecimal.TEN
)
private val PRODUCT_COMPONENT_CATEGORY_1 = createProductComponentCategory()
private val PRODUCT_COMPONENT_1 = createProductComponent(
    originalId = 1,
    code = "01",
    title = "Kukurica",
    unit = ProductComponentUnit.KG,
    stockQuantity = BigDecimal.valueOf(180.0),
    productComponentCategoryId = PRODUCT_COMPONENT_CATEGORY_1.id
)
private val PRODUCT_COMPOSITION_1 = createProductComposition(
    originalId = 1,
    productId = PRODUCT_1.id,
    productComponentId = PRODUCT_COMPONENT_1.id,
    amount = BigDecimal.valueOf(0.25)
)
private val PRODUCT_BASKET_ITEM_REQUEST_1 = CreateBasketItemRequest(
    type = BasketItemType.PRODUCT,
    quantity = 1,
    product = CreateProductRequest(
        productId = PRODUCT_1.id
    )
)
private val PRODUCT_BASKET_ITEM_REQUEST_2 = CreateBasketItemRequest(
    type = BasketItemType.PRODUCT,
    quantity = 1,
    product = CreateProductRequest(
        productId = PRODUCT_1.id
    )
)
private val POS_CONFIGURATION_ID = UUID.fromString("895c03dc-1ef6-4008-9dab-3c9d6b82b92d")
private val POS_CONFIGURATION_COMMAND = CreateOrUpdatePosConfigurationCommand(
    id = POS_CONFIGURATION_ID,
    macAddress = "AA-BB-CC-DD-EE",
    title = "dummyPOS",
    receiptsDirectory = "/dummy/directory",
    ticketSalesEnabled = false,
    productModes = setOf(ProductMode.STANDARD),
    tablesType = TablesType.NO_TABLES,
    seatsEnabled = false
)
