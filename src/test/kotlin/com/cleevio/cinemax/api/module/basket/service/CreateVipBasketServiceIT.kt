package com.cleevio.cinemax.api.module.basket.service

import com.cleevio.cinemax.api.DatabaseCleanup
import com.cleevio.cinemax.api.IntegrationTest
import com.cleevio.cinemax.api.common.constant.PaymentType
import com.cleevio.cinemax.api.module.basket.constant.BasketState
import com.cleevio.cinemax.api.module.basket.exception.TableForBasketNotFoundException
import com.cleevio.cinemax.api.module.basket.service.command.CreateVipBasketCommand
import com.cleevio.cinemax.api.module.basket.service.command.CreateVipBasketItemInput
import com.cleevio.cinemax.api.module.basketitem.constant.BasketItemType
import com.cleevio.cinemax.api.module.basketitem.service.BasketItemRepository
import com.cleevio.cinemax.api.module.branch.service.BranchRepository
import com.cleevio.cinemax.api.module.discountcard.service.DiscountCardRepository
import com.cleevio.cinemax.api.module.posconfiguration.constant.ProductMode
import com.cleevio.cinemax.api.module.product.constant.ProductType
import com.cleevio.cinemax.api.module.product.service.ProductRepository
import com.cleevio.cinemax.api.module.productcategory.constant.ProductCategoryType
import com.cleevio.cinemax.api.module.productcategory.service.ProductCategoryRepository
import com.cleevio.cinemax.api.module.productcomponent.constant.ProductComponentUnit
import com.cleevio.cinemax.api.module.productcomponent.service.ProductComponentRepository
import com.cleevio.cinemax.api.module.productcomponentcategory.service.ProductComponentCategoryRepository
import com.cleevio.cinemax.api.module.productcomposition.service.ProductCompositionRepository
import com.cleevio.cinemax.api.module.table.constant.TableType
import com.cleevio.cinemax.api.module.table.event.TableBasketInitiatedEvent
import com.cleevio.cinemax.api.module.table.service.TableRepository
import com.cleevio.cinemax.api.util.createBranch
import com.cleevio.cinemax.api.util.createDiscountCard
import com.cleevio.cinemax.api.util.createProduct
import com.cleevio.cinemax.api.util.createProductCategory
import com.cleevio.cinemax.api.util.createProductComponent
import com.cleevio.cinemax.api.util.createProductComponentCategory
import com.cleevio.cinemax.api.util.createProductComposition
import com.cleevio.cinemax.api.util.createTable
import com.cleevio.cinemax.api.util.toUUID
import io.kotest.assertions.throwables.shouldThrow
import io.kotest.matchers.shouldBe
import io.kotest.matchers.shouldNotBe
import io.mockk.Runs
import io.mockk.every
import io.mockk.just
import io.mockk.verify
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import java.math.BigDecimal
import java.util.UUID

class CreateVipBasketServiceIT @Autowired constructor(
    private val underTest: CreateVipBasketService,
    private val basketRepository: BasketRepository,
    private val basketItemRepository: BasketItemRepository,
    private val tableRepository: TableRepository,
    private val productCategoryRepository: ProductCategoryRepository,
    private val productRepository: ProductRepository,
    private val discountCardRepository: DiscountCardRepository,
    private val productComponentCategoryRepository: ProductComponentCategoryRepository,
    private val productComponentRepository: ProductComponentRepository,
    private val productCompositionRepository: ProductCompositionRepository,
    private val branchRepository: BranchRepository,
) : IntegrationTest(DatabaseCleanup.AFTER_EACH) {

    private lateinit var vipTableId: UUID
    private lateinit var vipProduct1Id: UUID
    private lateinit var vipProduct2Id: UUID
    private lateinit var discountCardId: UUID

    @BeforeEach
    fun setUp() {
        branchRepository.save(createBranch())

        // Create a VIP table
        val vipTable = tableRepository.save(
            createTable(
                id = 1.toUUID(),
                originalId = 1,
                title = "VIP Table 1",
                label = "VIP1",
                type = TableType.SEAT,
                productMode = ProductMode.VIP,
                paymentType = PaymentType.CASHLESS,
                ipAddress = "*************"
            )
        )
        vipTableId = vipTable.id

        // Create a product category
        val productCategory = productCategoryRepository.save(
            createProductCategory(
                originalId = 1,
                code = "01",
                title = "Drinks",
                type = ProductCategoryType.PRODUCT,
                order = 1,
                taxRate = 21,
                hexColorCode = "#FF0000"
            )
        )

        // Create VIP products
        val vipProduct1 = productRepository.save(
            createProduct(
                id = 10.toUUID(),
                originalId = 10,
                code = "P10",
                productCategoryId = productCategory.id,
                title = "VIP Cola",
                type = ProductType.PRODUCT,
                price = BigDecimal.valueOf(5.99),
                active = true,
                soldInVip = true,
                tabletOrder = 1
            )
        )
        vipProduct1Id = vipProduct1.id

        val productComponentCategory = createProductComponentCategory().also {
            productComponentCategoryRepository.save(it)
        }
        val productComponent = createProductComponent(
            originalId = 4,
            code = "04",
            title = "Coca Cola 0.33l",
            unit = ProductComponentUnit.KS,
            stockQuantity = BigDecimal.valueOf(125),
            productComponentCategoryId = productComponentCategory.id
        ).also { productComponentRepository.save(it) }

        createProductComposition(
            originalId = 1,
            productId = vipProduct1.id,
            productComponentId = productComponent.id,
            amount = BigDecimal.ONE
        ).also { productCompositionRepository.save(it) }

        val vipProduct2 = productRepository.save(
            createProduct(
                id = 11.toUUID(),
                originalId = 11,
                code = "P11",
                productCategoryId = productCategory.id,
                title = "VIP Water",
                type = ProductType.PRODUCT,
                price = BigDecimal.valueOf(3.50),
                active = true,
                soldInVip = true,
                tabletOrder = 2
            )
        )
        vipProduct2Id = vipProduct2.id

        val productComponent2 = createProductComponent(
            originalId = 5,
            code = "05",
            title = "Voda Rajec 0.33l",
            unit = ProductComponentUnit.KS,
            stockQuantity = BigDecimal.valueOf(125),
            productComponentCategoryId = productComponentCategory.id
        ).also { productComponentRepository.save(it) }

        createProductComposition(
            originalId = 2,
            productId = vipProduct2.id,
            productComponentId = productComponent2.id,
            amount = BigDecimal.ONE
        ).also { productCompositionRepository.save(it) }

        // Create a discount card with product discount
        val discountCard = discountCardRepository.save(
            createDiscountCard(
                id = 100.toUUID(),
                code = "DISCOUNT10",
                productDiscountId = vipProduct1Id
            )
        )
        discountCardId = discountCard.id

        every { applicationEventPublisherMock.publishEvent(any()) } just Runs
        every { applicationEventPublisherMock.publishEvent(any<TableBasketInitiatedEvent>()) } just Runs
    }

    @Test
    fun `test createVipBasket - with single product - should create basket and basket item`() {
        // Given
        val command = CreateVipBasketCommand(
            tableId = vipTableId,
            preferredPaymentType = PaymentType.CASHLESS,
            discountCardId = null,
            basketItems = listOf(
                CreateVipBasketItemInput(
                    productId = vipProduct1Id,
                    quantity = 2
                )
            )
        )

        // When
        underTest.invoke(command)

        // Then
        val baskets = basketRepository.findAll()
        baskets.size shouldBe 1

        val basket = baskets[0]
        basket.tableId shouldBe vipTableId
        basket.state shouldBe BasketState.OPEN
        basket.preferredPaymentType shouldBe PaymentType.CASHLESS
        basket.totalPrice shouldBe BigDecimal.valueOf(11.98) // 5.99 * 2

        val basketItems = basketItemRepository.findAll()
        basketItems.size shouldBe 1

        val basketItem = basketItems[0]
        basketItem.basketId shouldBe basket.id
        basketItem.productId shouldBe vipProduct1Id
        basketItem.type shouldBe BasketItemType.PRODUCT
        basketItem.quantity shouldBe 2
        basketItem.price shouldBe BigDecimal.valueOf(11.98)

        // Verify event was published
        verify { applicationEventPublisherMock.publishEvent(any<TableBasketInitiatedEvent>()) }
    }

    @Test
    fun `test createVipBasket - with multiple products - should create basket with multiple items`() {
        // Given
        val command = CreateVipBasketCommand(
            tableId = vipTableId,
            preferredPaymentType = PaymentType.CASH,
            discountCardId = null,
            basketItems = listOf(
                CreateVipBasketItemInput(
                    productId = vipProduct1Id,
                    quantity = 1
                ),
                CreateVipBasketItemInput(
                    productId = vipProduct2Id,
                    quantity = 3
                )
            )
        )

        // When
        underTest.invoke(command)

        // Then
        val baskets = basketRepository.findAll()
        baskets.size shouldBe 1

        val basket = baskets[0]
        basket.tableId shouldBe vipTableId
        basket.state shouldBe BasketState.OPEN
        basket.preferredPaymentType shouldBe PaymentType.CASH
        basket.totalPrice shouldBe BigDecimal.valueOf(16.49) // 5.99 + (3.50 * 3)

        val basketItems = basketItemRepository.findAll().sortedBy { it.productId }
        basketItems.size shouldBe 2

        // First product
        basketItems[0].basketId shouldBe basket.id
        basketItems[0].productId shouldBe vipProduct1Id
        basketItems[0].type shouldBe BasketItemType.PRODUCT
        basketItems[0].quantity shouldBe 1
        basketItems[0].price shouldBe BigDecimal.valueOf(5.99)

        // Second product
        basketItems[1].basketId shouldBe basket.id
        basketItems[1].productId shouldBe vipProduct2Id
        basketItems[1].type shouldBe BasketItemType.PRODUCT
        basketItems[1].quantity shouldBe 3
        basketItems[1].price shouldBe BigDecimal.valueOf(10.5)
    }

    @Test
    fun `test createVipBasket - with discount card - should create basket with product and discount items`() {
        // Given
        val command = CreateVipBasketCommand(
            tableId = vipTableId,
            preferredPaymentType = PaymentType.CASHLESS,
            discountCardId = discountCardId,
            basketItems = listOf(
                CreateVipBasketItemInput(
                    productId = vipProduct2Id,
                    quantity = 1
                )
            )
        )

        // When
        underTest.invoke(command)

        // Then
        val baskets = basketRepository.findAll()
        baskets.size shouldBe 1

        val basket = baskets[0]
        basket.tableId shouldBe vipTableId
        basket.state shouldBe BasketState.OPEN

        val basketItems = basketItemRepository.findAll().sortedBy { it.type }
        basketItems.size shouldBe 2

        // Product item
        val productItem = basketItems.find { it.type == BasketItemType.PRODUCT }
        productItem shouldNotBe null
        productItem!!.productId shouldBe vipProduct2Id
        productItem.quantity shouldBe 1

        // Discount item
        val discountItem = basketItems.find { it.type == BasketItemType.PRODUCT_DISCOUNT }
        discountItem shouldNotBe null
        discountItem!!.productId shouldBe vipProduct1Id // discount product
        discountItem.quantity shouldBe 1
    }

    @Test
    fun `test createVipBasket - with non-existent table - should throw exception`() {
        // Given
        val command = CreateVipBasketCommand(
            tableId = UUID.randomUUID(),
            preferredPaymentType = PaymentType.CASHLESS,
            discountCardId = null,
            basketItems = listOf(
                CreateVipBasketItemInput(
                    productId = vipProduct1Id,
                    quantity = 1
                )
            )
        )

        // When & Then
        shouldThrow<TableForBasketNotFoundException> {
            underTest.invoke(command)
        }

        // Verify no basket or items were created
        basketRepository.findAll().size shouldBe 0
        basketItemRepository.findAll().size shouldBe 0

        // Verify no events were published
        verify(exactly = 0) { applicationEventPublisherMock.publishEvent(any()) }
    }
}
