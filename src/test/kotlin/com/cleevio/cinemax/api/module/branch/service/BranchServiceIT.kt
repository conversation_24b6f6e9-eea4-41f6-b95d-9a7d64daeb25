package com.cleevio.cinemax.api.module.branch.service

import com.cleevio.cinemax.api.DatabaseCleanup
import com.cleevio.cinemax.api.IntegrationTest
import com.cleevio.cinemax.api.util.assertBranchEquals
import com.cleevio.cinemax.api.util.createBranch
import com.cleevio.cinemax.api.util.mapToCreateOrUpdateBranchCommand
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import kotlin.test.assertEquals
import kotlin.test.assertNotNull

class BranchServiceIT @Autowired constructor(
    private val underTest: BranchService,
    private val branchRepository: BranchRepository,
) : IntegrationTest(DatabaseCleanup.AFTER_EACH) {

    @Test
    fun `test syncCreateOrUpdateBranch - does not exist by originalId - should create branch`() {
        val command = mapToCreateOrUpdateBranchCommand(BRANCH_1).copy(id = null)
        assertEquals(0, branchRepository.findAll().size)

        underTest.syncCreateOrUpdateBranch(command)

        val branches = branchRepository.findAll()
        assertEquals(1, branches.size)
        assertBranchEquals(BRANCH_1, branches[0])
    }

    @Test
    fun `test syncCreateOrUpdateBranch - exists by originalId - should update branch`() {
        val createCommand = mapToCreateOrUpdateBranchCommand(BRANCH_1).copy(id = null)
        underTest.syncCreateOrUpdateBranch(createCommand)
        assertEquals(1, branchRepository.findAll().size)

        val updateCommand = createCommand.copy(
            name = "Olomouc",
            productSalesCode = 3,
            code = "510005"
        )

        underTest.syncCreateOrUpdateBranch(updateCommand)

        val branches = branchRepository.findAll()
        assertEquals(1, branches.size)

        assertEquals(BRANCH_1.originalId, branches[0].originalId)
        assertEquals(3, branches[0].productSalesCode)
        assertEquals("510005", branches[0].code)
        assertEquals("Olomouc", branches[0].name)
        assertNotNull(branches[0].createdAt)
        assertNotNull(branches[0].updatedAt)
        assert(branches[0].createdBy.isNotBlank())
        assert(branches[0].updatedBy.isNotBlank())
    }

    @Test
    fun `test syncCreateOrUpdateBranch - does not exist by originalId - should create two branches`() {
        val command1 = mapToCreateOrUpdateBranchCommand(BRANCH_1).copy(id = null)
        val command2 = mapToCreateOrUpdateBranchCommand(BRANCH_2).copy(id = null)
        assertEquals(0, branchRepository.findAll().size)

        underTest.syncCreateOrUpdateBranch(command1)
        underTest.syncCreateOrUpdateBranch(command2)

        val branches = branchRepository.findAll()
        assertEquals(2, branches.size)
        assertBranchEquals(BRANCH_1, branches[0])
        assertBranchEquals(BRANCH_2, branches[1])
    }
}

private val BRANCH_1 = createBranch()
private val BRANCH_2 = createBranch(
    originalId = 2,
    productSalesCode = 2,
    code = "510001",
    auditoriumOriginalCodePrefix = "6000",
    name = "Kosice"
)
