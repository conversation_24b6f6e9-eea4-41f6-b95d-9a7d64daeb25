package com.cleevio.cinemax.api.module.seat.service

import com.cleevio.cinemax.api.DatabaseCleanup
import com.cleevio.cinemax.api.IntegrationTest
import com.cleevio.cinemax.api.common.util.truncatedToSeconds
import com.cleevio.cinemax.api.module.auditorium.service.AuditoriumService
import com.cleevio.cinemax.api.module.auditoriumlayout.exception.AuditoriumLayoutNotFoundException
import com.cleevio.cinemax.api.module.auditoriumlayout.exception.AuditoriumLayoutSeatsAlreadyExistException
import com.cleevio.cinemax.api.module.auditoriumlayout.service.AuditoriumLayoutRepository
import com.cleevio.cinemax.api.module.reservation.constant.ReservationState
import com.cleevio.cinemax.api.module.seat.constant.DoubleSeatType
import com.cleevio.cinemax.api.module.seat.constant.SeatType
import com.cleevio.cinemax.api.module.seat.entity.Seat
import com.cleevio.cinemax.api.module.seat.exception.AuditoriumForSeatNotFoundException
import com.cleevio.cinemax.api.module.seat.exception.SeatNotFoundException
import com.cleevio.cinemax.api.module.seat.service.command.CopyAuditoriumLayoutSeatsCommand
import com.cleevio.cinemax.api.module.seat.service.command.UpdateSeatDefaultReservationStateCommand
import com.cleevio.cinemax.api.util.createAuditorium
import com.cleevio.cinemax.api.util.createAuditoriumLayout
import com.cleevio.cinemax.api.util.createSeat
import com.cleevio.cinemax.api.util.mapToCreateOrUpdateAuditoriumCommand
import com.cleevio.cinemax.api.util.mapToCreateOrUpdateSeatCommand
import jakarta.validation.ConstraintViolationException
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.Arguments
import org.junit.jupiter.params.provider.MethodSource
import org.springframework.beans.factory.annotation.Autowired
import java.util.UUID
import java.util.stream.Stream
import kotlin.test.assertEquals
import kotlin.test.assertNotNull
import kotlin.test.assertNull
import kotlin.test.assertTrue

class SeatServiceIT @Autowired constructor(
    private val underTest: SeatService,
    private val seatRepository: SeatRepository,
    private val seatJooqFinderService: SeatJooqFinderService,
    private val auditoriumService: AuditoriumService,
    private val auditoriumLayoutRepository: AuditoriumLayoutRepository,
) : IntegrationTest(DatabaseCleanup.AFTER_EACH) {

    @BeforeEach
    fun setUp() {
        auditoriumService.syncCreateOrUpdateAuditorium(mapToCreateOrUpdateAuditoriumCommand(AUDITORIUM_1))
        auditoriumLayoutRepository.saveAll(listOf(AUDITORIUM_LAYOUT_1, AUDITORIUM_LAYOUT_2, AUDITORIUM_LAYOUT_3))
    }

    @Test
    fun `test createOrUpdateSeat - should create seat`() {
        val command = mapToCreateOrUpdateSeatCommand(SEAT_1)
        underTest.createOrUpdateSeat(command)

        val createdSeat = seatJooqFinderService.findByOriginalId(SEAT_1.originalId!!)
        assertNotNull(createdSeat)
        assertSeatEquals(SEAT_1, createdSeat)
    }

    @Test
    fun `test createOrUpdateSeat - one seat exists - insert equal seat so it should update`() {
        val command = mapToCreateOrUpdateSeatCommand(SEAT_2)
        underTest.createOrUpdateSeat(command)
        underTest.createOrUpdateSeat(command)

        val updatedSeat = seatJooqFinderService.findByOriginalId(SEAT_2.originalId!!)
        assertNotNull(updatedSeat)
        assertSeatEquals(SEAT_2, updatedSeat)

        val seats = seatJooqFinderService.findAll()
        assertEquals(seats.size, 1)

        assertTrue { updatedSeat.updatedAt.isAfter(SEAT_2.updatedAt) }
    }

    @Test
    fun `test createOrUpdateSeat - two seats - should create two seats`() {
        seatRepository.save(SEAT_1)

        val command = mapToCreateOrUpdateSeatCommand(SEAT_2)
        underTest.createOrUpdateSeat(command)

        val seats = seatJooqFinderService.findAll()
        assertEquals(seats.size, 2)
        assertSeatEquals(SEAT_1, seats.first { it.originalId == SEAT_1.originalId })
        assertSeatEquals(SEAT_2, seats.first { it.originalId == SEAT_2.originalId })
    }

    @Test
    fun `test createOrUpdateSeat - command with null attribute - entity attr is null`() {
        seatRepository.save(SEAT_3)

        val createdSeat = seatJooqFinderService.findByOriginalId(SEAT_3.originalId!!)
        assertNotNull(createdSeat)
        assertSeatEquals(SEAT_3, createdSeat)

        val command = mapToCreateOrUpdateSeatCommand(SEAT_3)
        val commandWithNullAttr = command.copy(
            doubleSeatType = null
        )
        underTest.createOrUpdateSeat(commandWithNullAttr)

        val updatedSeat = seatJooqFinderService.findByOriginalId(SEAT_3.originalId!!)
        assertNotNull(updatedSeat)
        assertEquals(SEAT_3.originalId, updatedSeat.originalId)
        assertEquals(SEAT_3.auditoriumLayoutId, updatedSeat.auditoriumLayoutId)
        assertEquals(SEAT_3.auditoriumId, updatedSeat.auditoriumId)
        assertEquals(SEAT_3.type, updatedSeat.type)
        assertEquals(SEAT_3.row, updatedSeat.row)
        assertEquals(SEAT_3.number, updatedSeat.number)
        assertEquals(SEAT_3.positionTop, updatedSeat.positionTop)
        assertEquals(SEAT_3.positionLeft, updatedSeat.positionLeft)
        assertNull(updatedSeat.doubleSeatType)
        assertNotNull(updatedSeat.createdAt)
        assertNotNull(updatedSeat.updatedAt)
        assertTrue { updatedSeat.updatedAt.isAfter(SEAT_3.updatedAt) }
    }

    @Test
    fun `test createOrUpdateSeat - command with blank string - should throw exception`() {
        val command = mapToCreateOrUpdateSeatCommand(SEAT_3)
        val commandWithBlankString = command.copy(
            row = ""
        )
        assertThrows<ConstraintViolationException> {
            underTest.createOrUpdateSeat(commandWithBlankString)
        }
    }

    @Test
    fun `test createOrUpdateSeat - command with not existing auditorium - should throw exception`() {
        val command = mapToCreateOrUpdateSeatCommand(SEAT_1)
        val commandWithNotExistAuditorium = command.copy(
            auditoriumId = UUID.randomUUID()
        )
        assertThrows<AuditoriumForSeatNotFoundException> {
            underTest.createOrUpdateSeat(commandWithNotExistAuditorium)
        }
    }

    @Test
    fun `test copyAuditoriumLayoutSeats - should create copies of seats`() {
        val originalSeats = listOf(SEAT_1, SEAT_4)
        seatRepository.saveAll(originalSeats)
        assertEquals(2, seatJooqFinderService.findAll().size)

        underTest.copyAuditoriumLayoutSeats(
            CopyAuditoriumLayoutSeatsCommand(
                sourceAuditoriumLayoutId = AUDITORIUM_LAYOUT_1.id,
                newAuditoriumLayoutId = AUDITORIUM_LAYOUT_2.id
            )
        )

        val createdSeats = seatJooqFinderService.findAll().sortedBy { it.createdAt }
        val copiedSeats = createdSeats.filter { it.originalId == null }
        assertEquals(4, createdSeats.size)
        assertEquals(2, copiedSeats.size)

        copiedSeats.forEachIndexed { i, copiedSeat ->
            assertSeatCopyToSeatEquals(originalSeats[i], copiedSeat, AUDITORIUM_LAYOUT_2.id)
        }
    }

    @ParameterizedTest
    @MethodSource("nonExistingAuditoriumLayoutProvider")
    fun `test copyAuditoriumLayoutSeats - non-existing auditorium layout - should throw`(
        command: CopyAuditoriumLayoutSeatsCommand,
    ) {
        val originalSeats = listOf(SEAT_1, SEAT_4)
        seatRepository.saveAll(originalSeats)
        assertEquals(2, seatJooqFinderService.findAll().size)

        assertThrows<AuditoriumLayoutNotFoundException> {
            underTest.copyAuditoriumLayoutSeats(command)
        }
    }

    @Test
    fun `test copyAuditoriumLayoutSeats - seat in target auditorium layout exist - should throw exception`() {
        seatRepository.saveAll(setOf(SEAT_1, SEAT_2, SEAT_4))
        assertEquals(3, seatJooqFinderService.findAll().size)

        assertThrows<AuditoriumLayoutSeatsAlreadyExistException> {
            underTest.copyAuditoriumLayoutSeats(
                CopyAuditoriumLayoutSeatsCommand(
                    sourceAuditoriumLayoutId = AUDITORIUM_LAYOUT_1.id,
                    newAuditoriumLayoutId = AUDITORIUM_LAYOUT_2.id
                )
            )
        }
    }

    @Test
    fun `test updateSeatDefaultReservationStates - at least one seat does not exists by auditorium layout and auditorium - should throw`() {
        seatRepository.saveAll(setOf(SEAT_1, SEAT_2, SEAT_4))

        assertThrows<SeatNotFoundException> {
            underTest.updateSeatDefaultReservationStates(
                UpdateSeatDefaultReservationStateCommand(
                    auditoriumId = AUDITORIUM_1.id,
                    auditoriumLayoutId = AUDITORIUM_LAYOUT_1.id,
                    seatIdToReservationStateMap = mapOf(
                        SEAT_1.id to ReservationState.DISABLED,
                        SEAT_2.id to ReservationState.DISABLED,
                        SEAT_4.id to ReservationState.UNAVAILABLE
                    )
                )
            )
        }
    }

    @Test
    fun `test updateSeatDefaultReservationStates - seats exist - should update seat default reservation state`() {
        seatRepository.saveAll(setOf(SEAT_1, SEAT_4))

        val originalSeats = seatRepository.findAll()
        assertEquals(2, originalSeats.size)
        assertNull(originalSeats[0].defaultReservationState)
        assertNull(originalSeats[1].defaultReservationState)

        underTest.updateSeatDefaultReservationStates(
            UpdateSeatDefaultReservationStateCommand(
                auditoriumId = AUDITORIUM_1.id,
                auditoriumLayoutId = AUDITORIUM_LAYOUT_1.id,
                seatIdToReservationStateMap = mapOf(
                    SEAT_1.id to ReservationState.DISABLED,
                    SEAT_4.id to ReservationState.UNAVAILABLE
                )
            )
        )

        val updatedSeats = seatRepository.findAll()
        assertEquals(2, updatedSeats.size)
        assertSeatEqualsWithDefaultReservationUpdate(
            expected = SEAT_1,
            actual = updatedSeats.first { it.id == SEAT_1.id },
            expectedDefaultReservationState = ReservationState.DISABLED
        )
        assertSeatEqualsWithDefaultReservationUpdate(
            expected = SEAT_4,
            actual = updatedSeats.first { it.id == SEAT_4.id },
            expectedDefaultReservationState = ReservationState.UNAVAILABLE
        )
    }

    private fun assertSeatEquals(expected: Seat, actual: Seat) {
        assertEquals(expected.originalId, actual.originalId)
        assertEquals(expected.auditoriumLayoutId, actual.auditoriumLayoutId)
        assertEquals(expected.auditoriumId, actual.auditoriumId)
        assertEquals(expected.type, actual.type)
        assertEquals(expected.doubleSeatType, actual.doubleSeatType)
        assertEquals(expected.row, actual.row)
        assertEquals(expected.number, actual.number)
        assertEquals(expected.positionLeft, actual.positionLeft)
        assertEquals(expected.positionTop, actual.positionTop)
        assertEquals(expected.defaultReservationState, actual.defaultReservationState)
        assertNotNull(actual.createdAt)
        assertNotNull(actual.updatedAt)
    }

    private fun assertSeatCopyToSeatEquals(expected: Seat, actual: Seat, auditoriumLayoutId: UUID) {
        assertNull(actual.originalId)
        assertEquals(auditoriumLayoutId, actual.auditoriumLayoutId)
        assertEquals(expected.auditoriumId, actual.auditoriumId)
        assertEquals(expected.type, actual.type)
        assertEquals(expected.doubleSeatType, actual.doubleSeatType)
        assertEquals(expected.row, actual.row)
        assertEquals(expected.number, actual.number)
        assertEquals(expected.positionLeft, actual.positionLeft)
        assertEquals(expected.positionTop, actual.positionTop)
        assertEquals(expected.defaultReservationState, actual.defaultReservationState)
        assertTrue(actual.createdAt.isAfter(expected.createdAt))
        assertNotNull(actual.updatedAt)
    }

    private fun assertSeatEqualsWithDefaultReservationUpdate(
        expected: Seat,
        actual: Seat,
        expectedDefaultReservationState: ReservationState,
    ) {
        assertEquals(expected.originalId, actual.originalId)
        assertEquals(expected.auditoriumLayoutId, actual.auditoriumLayoutId)
        assertEquals(expected.auditoriumId, actual.auditoriumId)
        assertEquals(expected.type, actual.type)
        assertEquals(expected.doubleSeatType, actual.doubleSeatType)
        assertEquals(expected.row, actual.row)
        assertEquals(expected.number, actual.number)
        assertEquals(expected.positionLeft, actual.positionLeft)
        assertEquals(expected.positionTop, actual.positionTop)
        assertEquals(expectedDefaultReservationState, actual.defaultReservationState)
        assertEquals(expected.createdAt.truncatedToSeconds(), actual.createdAt.truncatedToSeconds())
        assertTrue(actual.updatedAt.isAfter(expected.updatedAt))
    }

    companion object {
        @JvmStatic
        fun nonExistingAuditoriumLayoutProvider(): Stream<Arguments> {
            return Stream.of(
                Arguments.of(
                    CopyAuditoriumLayoutSeatsCommand(
                        sourceAuditoriumLayoutId = AUDITORIUM_LAYOUT_1.id,
                        newAuditoriumLayoutId = UUID.randomUUID()
                    )
                ),
                Arguments.of(
                    CopyAuditoriumLayoutSeatsCommand(
                        sourceAuditoriumLayoutId = UUID.randomUUID(),
                        newAuditoriumLayoutId = AUDITORIUM_LAYOUT_2.id
                    )
                )
            )
        }
    }
}

private val AUDITORIUM_1 = createAuditorium(
    originalId = 1,
    code = "A",
    title = "SÁLA A - CINEMAX BRATISLAVA"
)
private val AUDITORIUM_LAYOUT_1 = createAuditoriumLayout(originalId = 1, auditoriumId = AUDITORIUM_1.id, code = "01")
private val AUDITORIUM_LAYOUT_2 = createAuditoriumLayout(originalId = 2, auditoriumId = AUDITORIUM_1.id, code = "02")
private val AUDITORIUM_LAYOUT_3 = createAuditoriumLayout(originalId = 3, auditoriumId = AUDITORIUM_1.id, code = "03")

private val SEAT_1 = createSeat(
    originalId = 1,
    auditoriumLayoutId = AUDITORIUM_LAYOUT_1.id,
    auditoriumId = AUDITORIUM_1.id,
    row = "A",
    number = "6",
    positionTop = 62
)
private val SEAT_2 = createSeat(
    originalId = 2,
    auditoriumLayoutId = AUDITORIUM_LAYOUT_2.id,
    auditoriumId = AUDITORIUM_1.id,
    type = SeatType.PREMIUM_PLUS,
    row = "A",
    number = "8",
    positionTop = 62
)
private val SEAT_3 = createSeat(
    originalId = 3,
    auditoriumLayoutId = AUDITORIUM_LAYOUT_3.id,
    type = SeatType.REGULAR,
    doubleSeatType = DoubleSeatType.DOUBLE_SEAT_RIGHT,
    auditoriumId = AUDITORIUM_1.id,
    row = "5",
    number = "7",
    positionLeft = 25,
    positionTop = 40
)
private val SEAT_4 = createSeat(
    originalId = 4,
    auditoriumLayoutId = AUDITORIUM_LAYOUT_1.id,
    auditoriumId = AUDITORIUM_1.id,
    row = "A",
    number = "7",
    positionTop = 62
)
