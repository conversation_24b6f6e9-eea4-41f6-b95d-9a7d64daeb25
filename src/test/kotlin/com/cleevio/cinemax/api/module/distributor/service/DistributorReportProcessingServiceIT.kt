package com.cleevio.cinemax.api.module.distributor.service

import com.cleevio.cinemax.api.DatabaseCleanup
import com.cleevio.cinemax.api.INTEGRATION_TEST_DATE_TIME
import com.cleevio.cinemax.api.IntegrationTest
import com.cleevio.cinemax.api.common.config.CinemaxConfigProperties
import com.cleevio.cinemax.api.common.mail.constant.MailAttachmentType
import com.cleevio.cinemax.api.common.mail.constant.MailVariable
import com.cleevio.cinemax.api.module.auditorium.service.AuditoriumRepository
import com.cleevio.cinemax.api.module.auditoriumlayout.service.AuditoriumLayoutRepository
import com.cleevio.cinemax.api.module.movie.service.MovieRepository
import com.cleevio.cinemax.api.module.pricecategory.service.PriceCategoryRepository
import com.cleevio.cinemax.api.module.screening.service.ScreeningRepository
import com.cleevio.cinemax.api.module.screeningfee.service.ScreeningFeeRepository
import com.cleevio.cinemax.api.util.createAuditorium
import com.cleevio.cinemax.api.util.createAuditoriumLayout
import com.cleevio.cinemax.api.util.createDistributor
import com.cleevio.cinemax.api.util.createMovie
import com.cleevio.cinemax.api.util.createPriceCategory
import com.cleevio.cinemax.api.util.createScreening
import com.cleevio.cinemax.api.util.createScreeningFee
import io.mockk.clearMocks
import io.mockk.verify
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Disabled
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired

class DistributorReportProcessingServiceIT @Autowired constructor(
    private val underTest: DistributorReportProcessingService,
    private val cinemaxConfigProperties: CinemaxConfigProperties,
    private val distributorRepository: DistributorRepository,
    private val movieRepository: MovieRepository,
    private val screeningRepository: ScreeningRepository,
    private val screeningFeeRepository: ScreeningFeeRepository,
    private val auditoriumRepository: AuditoriumRepository,
    private val auditoriumLayoutRepository: AuditoriumLayoutRepository,
    private val priceCategoryRepository: PriceCategoryRepository,
) : IntegrationTest(DatabaseCleanup.AFTER_EACH) {

    @BeforeEach
    fun setUp() {
        clearMocks(mailService)
    }

    @Disabled
    @Test
    fun `test sendReportMailToDistributors - screenings for distributor's movies exist - should send emails to distributors`() {
        val distributor1 = createDistributor(
            originalId = 1,
            code = "01",
            title = "Test Distributor 1",
            contactEmails = setOf("<EMAIL>", "<EMAIL>")
        )
        val distributor2 = createDistributor(
            originalId = 2,
            code = "02",
            title = "Test Distributor 2",
            contactEmails = setOf("<EMAIL>")
        )
        val distributor3 = createDistributor(
            originalId = 3,
            code = "03",
            title = "Test Distributor 3",
            contactEmails = setOf("<EMAIL>")
        )

        distributorRepository.saveAll(listOf(distributor1, distributor2, distributor3))

        val movie1 = createMovie(originalId = 1, distributorId = distributor1.id, title = "Test Movie 1")
        val movie2 = createMovie(originalId = 2, distributorId = distributor2.id, title = "Test Movie 2")
        val movie3 = createMovie(originalId = 3, distributorId = distributor3.id, title = "Test Movie 3")

        movieRepository.saveAll(listOf(movie1, movie2, movie3))

        val auditorium = createAuditorium()
        auditoriumRepository.save(auditorium)

        val auditoriumLayout = createAuditoriumLayout(auditoriumId = auditorium.id)
        auditoriumLayoutRepository.save(auditoriumLayout)

        val priceCategory = createPriceCategory()
        priceCategoryRepository.save(priceCategory)

        val screening1 = createScreening(
            originalId = 1,
            auditoriumId = auditorium.id,
            auditoriumLayoutId = auditoriumLayout.id,
            movieId = movie1.id,
            priceCategoryId = priceCategory.id,
            date = INTEGRATION_TEST_DATE_TIME.toLocalDate().plusDays(1)
        )
        val screening2 = createScreening(
            originalId = 2,
            auditoriumId = auditorium.id,
            auditoriumLayoutId = auditoriumLayout.id,
            movieId = movie2.id,
            priceCategoryId = priceCategory.id,
            date = INTEGRATION_TEST_DATE_TIME.toLocalDate().plusDays(1)
        )
        // screening is outside of cinema week - distributor 3 shouldn't get email
        val screening3 = createScreening(
            originalId = 3,
            auditoriumId = auditorium.id,
            auditoriumLayoutId = auditoriumLayout.id,
            movieId = movie3.id,
            priceCategoryId = priceCategory.id,
            date = INTEGRATION_TEST_DATE_TIME.toLocalDate().plusDays(7)
        )

        screeningRepository.saveAll(listOf(screening1, screening2, screening3))

        val screeningFee1 = createScreeningFee(
            originalScreeningId = screening1.originalId,
            screeningId = screening1.id,
            surchargeDBox = 5.0.toBigDecimal()
        )
        val screeningFee2 = createScreeningFee(
            originalScreeningId = screening2.originalId,
            screeningId = screening2.id,
            surchargeDBox = 0.toBigDecimal()
        )
        val screeningFee3 = createScreeningFee(
            originalScreeningId = screening3.originalId,
            screeningId = screening3.id,
            surchargeDBox = 0.toBigDecimal()
        )

        screeningFeeRepository.saveAll(listOf(screeningFee1, screeningFee2, screeningFee3))

        underTest.sendReportMailToDistributors()

        // distributor 1 has 2 emails, distributor 2 has 1 email = 3 total calls
        verify(exactly = 3) {
            mailService.sendEmail(
                from = cinemaxConfigProperties.fromEmailAddress,
                to = any(),
                bcc = any(),
                subject = any(),
                body = any(),
                mailVariables = any(),
                attachments = any()
            )
        }

        // verify specific calls for distributor 1 emails (should have DBox attachment)
        verify {
            mailService.sendEmail(
                from = cinemaxConfigProperties.fromEmailAddress,
                to = "<EMAIL>",
                bcc = listOf(),
                subject = "Týždenné výsledky premietanie filmov",
                body = any(),
                mailVariables = match { variables ->
                    variables[MailVariable.EXPORT_TITLE.name] == "Týždenné výsledky premietanie filmov" &&
                        variables[MailVariable.BRANCH_NAME.name] == cinemaxConfigProperties.branchName
                },
                attachments = match { attachments ->
                    attachments.size == 2 && // Regular + DBox export
                        attachments.any { it.type == MailAttachmentType.XLSX_SINGLE_DISTRIBUTOR_WEEKLY_REPORT } &&
                        attachments.any { it.type == MailAttachmentType.XLSX_SINGLE_DISTRIBUTOR_WEEKLY_REPORT_DBOX_ONLY }
                }
            )
        }

        verify {
            mailService.sendEmail(
                from = cinemaxConfigProperties.fromEmailAddress,
                to = "<EMAIL>",
                bcc = listOf(),
                subject = "Týždenné výsledky premietanie filmov",
                body = any(),
                mailVariables = match { variables ->
                    variables[MailVariable.EXPORT_TITLE.name] == "Týždenné výsledky premietanie filmov" &&
                        variables[MailVariable.BRANCH_NAME.name] == cinemaxConfigProperties.branchName
                },
                attachments = match { attachments ->
                    attachments.size == 2 && // regular export + DBox export
                        attachments.any { it.type == MailAttachmentType.XLSX_SINGLE_DISTRIBUTOR_WEEKLY_REPORT } &&
                        attachments.any { it.type == MailAttachmentType.XLSX_SINGLE_DISTRIBUTOR_WEEKLY_REPORT_DBOX_ONLY }
                }
            )
        }

        // verify specific calls for distributor 2 emails (no DBox)
        verify {
            mailService.sendEmail(
                from = cinemaxConfigProperties.fromEmailAddress,
                to = "<EMAIL>",
                bcc = listOf(),
                subject = "Týždenné výsledky premietanie filmov",
                body = any(),
                mailVariables = match { variables ->
                    variables[MailVariable.EXPORT_TITLE.name] == "Týždenné výsledky premietanie filmov" &&
                        variables[MailVariable.BRANCH_NAME.name] == cinemaxConfigProperties.branchName
                },
                attachments = match { attachments ->
                    attachments.size == 1 &&
                        attachments.any { it.type == MailAttachmentType.XLSX_SINGLE_DISTRIBUTOR_WEEKLY_REPORT }
                }
            )
        }
    }

    @Test
    fun `test sendReportMailToDistributors - no distributors found - should not send any emails`() {
        underTest.sendReportMailToDistributors()

        verify(exactly = 0) {
            mailService.sendEmail(any(), any(), any(), any(), any(), any(), any())
        }
    }
}
