package com.cleevio.cinemax.api.module.language.service

import com.cleevio.cinemax.api.DatabaseCleanup
import com.cleevio.cinemax.api.IntegrationTest
import com.cleevio.cinemax.api.module.language.entity.Language
import com.cleevio.cinemax.api.util.assertDescriptorEquals
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.test.context.jdbc.Sql
import org.springframework.test.context.jdbc.SqlGroup
import kotlin.test.assertEquals

@SqlGroup(
    Sql(
        scripts = [
            "/db/migration/V108__init_language_table_with_data.sql"
        ]
    )
)
class LanguageJooqFinderServiceIT @Autowired constructor(
    private val underTest: LanguageJooqFinderService,
) : IntegrationTest(DatabaseCleanup.AFTER_EACH) {

    @Test
    fun `test findAll - should find all language values`() {
        val languages = underTest.findAll().sortedBy { it.createdAt }

        assertEquals(9, languages.size)
        assertDescriptorEquals(LANGUAGE_1, languages[0])
        assertDescriptorEquals(LANGUAGE_2, languages[1])
        assertDescriptorEquals(LANGUAGE_3, languages[8])
    }
}

private val LANGUAGE_1 = Language(
    originalId = 1,
    code = "CZ",
    title = "česká verze"
)
private val LANGUAGE_2 = Language(
    originalId = 2,
    code = "SK",
    title = "slovenská verze"
)
private val LANGUAGE_3 = Language(
    originalId = 9,
    code = "HU",
    title = "maďarská verze"
)
