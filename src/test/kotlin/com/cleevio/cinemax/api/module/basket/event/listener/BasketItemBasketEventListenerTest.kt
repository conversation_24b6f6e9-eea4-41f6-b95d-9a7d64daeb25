package com.cleevio.cinemax.api.module.basket.event.listener

import com.cleevio.cinemax.api.common.integration.cards.constant.CardsPosType
import com.cleevio.cinemax.api.module.basket.event.BasketItemModifiedEvent
import com.cleevio.cinemax.api.module.basket.event.ProductBasketItemAddedToBasketEvent
import com.cleevio.cinemax.api.module.basket.event.ProductBasketItemDeletedFromBasketEvent
import com.cleevio.cinemax.api.module.basket.event.ProductBasketItemQuantityModifiedEvent
import com.cleevio.cinemax.api.module.basket.service.BasketJpaFinderService
import com.cleevio.cinemax.api.module.basket.service.BasketService
import com.cleevio.cinemax.api.module.basket.service.command.ApplyCardsOnBasketCommand
import com.cleevio.cinemax.api.module.basket.service.command.RecalculateBasketProductDiscountPricesCommand
import com.cleevio.cinemax.api.module.basket.service.command.RecalculateBasketTotalPriceCommand
import com.cleevio.cinemax.api.module.basketitem.service.BasketItemService
import com.cleevio.cinemax.api.module.card.service.BasketCinemaxCardsService
import com.cleevio.cinemax.api.module.product.event.ProductStockQuantityChangedEvent
import com.cleevio.cinemax.api.module.product.service.ProductService
import com.cleevio.cinemax.api.module.product.service.command.UpdateProductStockQuantityCommand
import com.cleevio.cinemax.api.util.toUUID
import io.mockk.Runs
import io.mockk.every
import io.mockk.just
import io.mockk.mockk
import io.mockk.runs
import io.mockk.verify
import org.junit.jupiter.api.Test
import org.springframework.context.ApplicationEventPublisher

class BasketItemBasketEventListenerTest {

    private val basketService = mockk<BasketService>()
    private val basketItemService = mockk<BasketItemService>()
    private val basketFinderService = mockk<BasketJpaFinderService>()
    private val productService = mockk<ProductService>()
    private val basketCinemaxCardsService = mockk<BasketCinemaxCardsService>()
    private val applicationEventPublisher = mockk<ApplicationEventPublisher>()

    private val underTest = BasketItemBasketEventListener(
        basketService = basketService,
        basketItemService = basketItemService,
        basketFinderService = basketFinderService,
        productService = productService,
        basketCinemaxCardsService = basketCinemaxCardsService,
        applicationEventPublisher = applicationEventPublisher
    )

    @Test
    fun `test listenToBasketItemModifiedEvent - basket with cards - should call applyCardsOnBasket`() {
        val basketId = 1.toUUID()
        val cardId = 10.toUUID()
        val event = BasketItemModifiedEvent(basketId)
        val applyCardsOnBasketCommand = ApplyCardsOnBasketCommand(basketId, setOf(cardId), CardsPosType.CINEMA)
        val discountPricesCommand = RecalculateBasketProductDiscountPricesCommand(basketId)
        val totalPriceCommand = RecalculateBasketTotalPriceCommand(basketId)

        every { basketFinderService.findAppliedCardIds(any()) } returns setOf(10.toUUID())
        every { basketCinemaxCardsService.applyCardsOnBasket(any()) } just Runs
        every { basketItemService.recalculateBasketProductDiscountPrices(any()) } just runs
        every { basketService.recalculateBasketTotalPrice(any()) } returns mockk()

        underTest.listenToBasketItemModifiedEvent(event)

        verify(exactly = 1) { basketFinderService.findAppliedCardIds(basketId) }
        verify(exactly = 1) { basketCinemaxCardsService.applyCardsOnBasket(applyCardsOnBasketCommand) }
        verify(exactly = 0) { basketItemService.recalculateBasketProductDiscountPrices(discountPricesCommand) }
        verify(exactly = 0) { basketService.recalculateBasketTotalPrice(totalPriceCommand) }
    }

    @Test
    fun `test listenToBasketItemModifiedEvent - basket without cards - should recalculate discount prices and total price`() {
        val basketId = 1.toUUID()
        val event = BasketItemModifiedEvent(basketId)
        val applyCardsOnBasketCommand = ApplyCardsOnBasketCommand(basketId, setOf(), CardsPosType.CINEMA)
        val discountPricesCommand = RecalculateBasketProductDiscountPricesCommand(basketId)
        val totalPriceCommand = RecalculateBasketTotalPriceCommand(basketId)

        every { basketFinderService.findAppliedCardIds(any()) } returns setOf()
        every { basketCinemaxCardsService.applyCardsOnBasket(any()) } just Runs
        every { basketItemService.recalculateBasketProductDiscountPrices(any()) } just runs
        every { basketService.recalculateBasketTotalPrice(any()) } returns mockk()

        underTest.listenToBasketItemModifiedEvent(event)

        verify(exactly = 1) { basketFinderService.findAppliedCardIds(basketId) }
        verify(exactly = 0) { basketCinemaxCardsService.applyCardsOnBasket(applyCardsOnBasketCommand) }
        verify(exactly = 1) { basketItemService.recalculateBasketProductDiscountPrices(discountPricesCommand) }
        verify(exactly = 1) { basketService.recalculateBasketTotalPrice(totalPriceCommand) }
    }

    @Test
    fun `test listenToProductBasketItemAddedToBasketEvent - should update product stock and publish event`() {
        val productId = 1.toUUID()
        val event = ProductBasketItemAddedToBasketEvent(productId, 5)
        val updateCommand = UpdateProductStockQuantityCommand(productId, 5)
        val publishedEvent = ProductStockQuantityChangedEvent(productId)

        every { productService.updateProductStockQuantity(updateCommand) } just runs
        every { applicationEventPublisher.publishEvent(publishedEvent) } just runs

        underTest.listenToProductBasketItemAddedToBasketEvent(event)

        verify(exactly = 1) { productService.updateProductStockQuantity(updateCommand) }
        verify(exactly = 1) { applicationEventPublisher.publishEvent(publishedEvent) }
    }

    @Test
    fun `test listenToProductBasketItemQuantityModifiedEvent - should update product stock and publish event`() {
        val productId = 1.toUUID()
        val event = ProductBasketItemQuantityModifiedEvent(productId, -2)
        val updateCommand = UpdateProductStockQuantityCommand(productId, -2)
        val publishedEvent = ProductStockQuantityChangedEvent(productId)

        every { productService.updateProductStockQuantity(updateCommand) } just runs
        every { applicationEventPublisher.publishEvent(publishedEvent) } just runs

        underTest.listenToProductBasketItemQuantityModifiedEvent(event)

        verify(exactly = 1) { productService.updateProductStockQuantity(updateCommand) }
        verify(exactly = 1) { applicationEventPublisher.publishEvent(publishedEvent) }
    }

    @Test
    fun `test listenToProductBasketItemDeletedFromBasketEvent - should update product stock and publish event`() {
        val productId = 1.toUUID()
        val event = ProductBasketItemDeletedFromBasketEvent(productId, 3)
        val updateCommand = UpdateProductStockQuantityCommand(productId, 3)
        val publishedEvent = ProductStockQuantityChangedEvent(productId)

        every { productService.updateProductStockQuantity(updateCommand) } just runs
        every { applicationEventPublisher.publishEvent(publishedEvent) } just runs

        underTest.listenToProductBasketItemDeletedFromBasketEvent(event)

        verify(exactly = 1) { productService.updateProductStockQuantity(updateCommand) }
        verify(exactly = 1) { applicationEventPublisher.publishEvent(publishedEvent) }
    }
}
