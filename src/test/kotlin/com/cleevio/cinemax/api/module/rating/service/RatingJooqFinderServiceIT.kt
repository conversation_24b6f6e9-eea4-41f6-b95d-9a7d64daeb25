package com.cleevio.cinemax.api.module.rating.service

import com.cleevio.cinemax.api.DatabaseCleanup
import com.cleevio.cinemax.api.IntegrationTest
import com.cleevio.cinemax.api.module.rating.entity.Rating
import com.cleevio.cinemax.api.util.assertDescriptorEquals
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.test.context.jdbc.Sql
import org.springframework.test.context.jdbc.SqlGroup
import kotlin.test.assertEquals

@SqlGroup(
    Sql(
        scripts = [
            "/db/migration/V106__init_rating_table_with_data.sql"
        ]
    )
)
class RatingJooqFinderServiceIT @Autowired constructor(
    private val underTest: RatingJooqFinderService,
) : IntegrationTest(DatabaseCleanup.AFTER_EACH) {

    @Test
    fun `test findAll - should find all rating values`() {
        val ratings = underTest.findAll().sortedBy { it.createdAt }

        assertEquals(5, ratings.size)
        assertDescriptorEquals(RATING_1, ratings[0])
        assertDescriptorEquals(RATING_2, ratings[1])
        assertDescriptorEquals(RATING_3, ratings[2])
    }
}

private val RATING_1 = Rating(
    originalId = 5,
    code = "U",
    title = "U"
)
private val RATING_2 = Rating(
    originalId = 6,
    code = "12",
    title = "12"
)
private val RATING_3 = Rating(
    originalId = 7,
    code = "15",
    title = "15"
)
