package com.cleevio.cinemax.api.module.supplier.service.command

import jakarta.validation.ConstraintViolationException
import jakarta.validation.Validation
import jakarta.validation.Validator
import org.junit.jupiter.api.Assertions.assertThrows
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.Arguments
import org.junit.jupiter.params.provider.MethodSource
import java.util.Optional
import java.util.UUID
import java.util.stream.Stream

class CreateOrUpdateSupplierCommandTest {

    private val validator: Validator = Validation.buildDefaultValidatorFactory().validator

    @ParameterizedTest
    @MethodSource("invalidCommandsProvider")
    fun `createOrUpdateSupplierCommandTest - invalid data - should throw`(command: CreateOrUpdateSupplierCommand) {
        assertThrows(ConstraintViolationException::class.java) {
            val violations = validator.validate(command)
            if (violations.isNotEmpty()) {
                throw ConstraintViolationException(violations)
            }
        }
    }

    companion object {
        @JvmStatic
        fun invalidCommandsProvider(): Stream<Arguments> {
            val validCommand = CreateOrUpdateSupplierCommand(
                id = UUID.randomUUID(),
                originalId = 1,
                code = "SUP1",
                title = "Best supplies ever, s.r.o.",
                addressStreet = Optional.of("Supply Street 58"),
                addressCity = Optional.of("Prague"),
                addressPostCode = Optional.of("150 00"),
                contactName = Optional.of("John Doe"),
                contactPhone = Optional.of("+************"),
                contactEmails = Optional.of(setOf("<EMAIL>", "<EMAIL>")),
                bankName = Optional.of("AirBank, a.s."),
                bankAccount = Optional.of("*********/0300"),
                idNumber = Optional.of("********"),
                taxIdNumber = Optional.of("CZ2022916731")
            )

            return Stream.of(
                Arguments.of(validCommand.copy(code = "")),
                Arguments.of(validCommand.copy(code = "1234567")),
                Arguments.of(validCommand.copy(title = "")),
                Arguments.of(validCommand.copy(title = "a".repeat(31))),
                Arguments.of(validCommand.copy(addressStreet = Optional.of("a".repeat(41)))),
                Arguments.of(validCommand.copy(addressCity = Optional.of("a".repeat(41)))),
                Arguments.of(validCommand.copy(addressPostCode = Optional.of("1234567"))),
                Arguments.of(validCommand.copy(contactName = Optional.of("a".repeat(21)))),
                Arguments.of(validCommand.copy(contactPhone = Optional.of("a".repeat(21)))),
                Arguments.of(validCommand.copy(bankName = Optional.of("a".repeat(41)))),
                Arguments.of(validCommand.copy(bankAccount = Optional.of("a".repeat(16)))),
                Arguments.of(validCommand.copy(idNumber = Optional.of("a".repeat(16)))),
                Arguments.of(validCommand.copy(taxIdNumber = Optional.of("a".repeat(16))))
            )
        }
    }
}
