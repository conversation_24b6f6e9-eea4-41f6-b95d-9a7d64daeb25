package com.cleevio.cinemax.api.module.distributor.controller

import com.cleevio.cinemax.api.ControllerTest
import com.cleevio.cinemax.api.common.constant.ApiVersion
import com.cleevio.cinemax.api.common.util.jsonContent
import com.cleevio.cinemax.api.module.distributor.controller.dto.AdminGetDistributorResponse
import com.cleevio.cinemax.api.module.distributor.controller.dto.AdminSearchDistributorsResponse
import com.cleevio.cinemax.api.module.distributor.service.command.CreateOrUpdateDistributorCommand
import com.cleevio.cinemax.api.module.distributor.service.query.AdminGetDistributorQuery
import com.cleevio.cinemax.api.module.distributor.service.query.AdminSearchDistributorsQuery
import com.cleevio.cinemax.api.module.distributor.service.query.SearchDistributorsFilter
import com.cleevio.cinemax.api.module.employee.constant.EmployeeRole
import com.cleevio.cinemax.api.truncatedAndFormatted
import com.cleevio.cinemax.api.util.createDistributor
import com.cleevio.cinemax.api.util.mockAccessToken
import com.cleevio.cinemax.psql.tables.DistributorColumnNames
import io.mockk.every
import io.mockk.verifySequence
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest
import org.springframework.data.domain.PageImpl
import org.springframework.data.domain.PageRequest
import org.springframework.data.domain.Sort
import org.springframework.http.HttpHeaders
import org.springframework.http.MediaType
import org.springframework.test.web.servlet.MockMvc
import org.springframework.test.web.servlet.get
import org.springframework.test.web.servlet.post
import org.springframework.test.web.servlet.put
import java.util.Optional
import java.util.UUID

@WebMvcTest(AdminDistributorController::class)
class AdminDistributorControllerTest @Autowired constructor(
    private val mvc: MockMvc,
) : ControllerTest() {

    @Test
    fun `test createDistributor, should serialize and deserialize correctly`() {
        val distributorId = UUID.fromString("9e22ebee-f458-4c8b-a8ee-1503ee5099d7")
        every { distributorService.adminCreateOrUpdateDistributor(any()) } returns distributorId

        mvc.post(MANAGER_BASE_DISTRIBUTOR_PATH) {
            header(HttpHeaders.ACCEPT, ApiVersion.VERSION_1_JSON)
            header(HttpHeaders.AUTHORIZATION, mockAccessToken(EmployeeRole.MANAGER))
            contentType = MediaType.APPLICATION_JSON
            content = """
                {
                  "disfilmCode": "01",
                  "title": "Vertigo Distribution s.r.o.",
                  "addressStreet": "Ul. gen.Klapku 68/43",
                  "addressCity": "Komárno",
                  "addressPostCode": "945 01",
                  "contactName1": "Tomáš Ficza",
                  "contactName2": "Peter Ficza",
                  "contactName3": "Jan Ficza",
                  "contactPhone1": "+************",
                  "contactPhone2": "+************",
                  "contactPhone3": "+************",
                  "contactEmails": [
                    "<EMAIL>",
                    "<EMAIL>"
                  ],
                  "bankName": "UniCredit Bank Czech Republic and Slovakia, a.s.",
                  "bankAccount": "**********/1111",
                  "idNumber": "********",
                  "taxIdNumber": "**********",
                  "vatRate": 5,
                  "note": "marketingová akcia -  event Spievankovo"
                }
            """.trimIndent()
        }.andExpect {
            status { isOk() }
            jsonContent(
                """
                    {
                      "id": "$distributorId"
                    }
                """.trimIndent()

            )
        }

        verifySequence {
            distributorService.adminCreateOrUpdateDistributor(
                CreateOrUpdateDistributorCommand(
                    code = null,
                    disfilmCode = Optional.of("01"),
                    title = "Vertigo Distribution s.r.o.",
                    addressStreet = Optional.of("Ul. gen.Klapku 68/43"),
                    addressCity = Optional.of("Komárno"),
                    addressPostCode = Optional.of("945 01"),
                    contactName1 = Optional.of("Tomáš Ficza"),
                    contactName2 = Optional.of("Peter Ficza"),
                    contactName3 = Optional.of("Jan Ficza"),
                    contactPhone1 = Optional.of("+************"),
                    contactPhone2 = Optional.of("+************"),
                    contactPhone3 = Optional.of("+************"),
                    contactEmails = Optional.of(setOf("<EMAIL>", "<EMAIL>")),
                    bankName = Optional.of("UniCredit Bank Czech Republic and Slovakia, a.s."),
                    bankAccount = Optional.of("**********/1111"),
                    idNumber = Optional.of("********"),
                    taxIdNumber = Optional.of("**********"),
                    vatRate = Optional.of(5),
                    note = Optional.of("marketingová akcia -  event Spievankovo")
                )
            )
        }
    }

    @Test
    fun `test createDistributor, should serialize and deserialize correctly with null values or missing properties`() {
        val distributorId = UUID.fromString("9e22ebee-f458-4c8b-a8ee-1503ee5099d7")
        every { distributorService.adminCreateOrUpdateDistributor(any()) } returns distributorId

        mvc.post(MANAGER_BASE_DISTRIBUTOR_PATH) {
            header(HttpHeaders.ACCEPT, ApiVersion.VERSION_1_JSON)
            header(HttpHeaders.AUTHORIZATION, mockAccessToken(EmployeeRole.MANAGER))
            contentType = MediaType.APPLICATION_JSON
            content = """
                {
                  "disfilmCode": null,
                  "title": "Vertigo Distribution s.r.o.",
                  "addressStreet": null,
                  "addressCity": null,
                  "addressPostCode": null,
                  "contactName1": "Tomáš Ficza",
                  "contactPhone1": "+************"
                }
            """.trimIndent()
        }.andExpect {
            status { isOk() }
            jsonContent(
                """
                    {
                      "id": "$distributorId"
                    }
                """.trimIndent()

            )
        }

        verifySequence {
            distributorService.adminCreateOrUpdateDistributor(
                CreateOrUpdateDistributorCommand(
                    code = null,
                    disfilmCode = Optional.empty(),
                    title = "Vertigo Distribution s.r.o.",
                    addressStreet = Optional.empty(),
                    addressCity = Optional.empty(),
                    addressPostCode = Optional.empty(),
                    contactName1 = Optional.of("Tomáš Ficza"),
                    contactName2 = null,
                    contactName3 = null,
                    contactPhone1 = Optional.of("+************"),
                    contactPhone2 = null,
                    contactPhone3 = null,
                    contactEmails = Optional.empty(),
                    bankName = null,
                    bankAccount = null,
                    idNumber = null,
                    taxIdNumber = null,
                    vatRate = null,
                    note = null
                )
            )
        }
    }

    @Test
    fun `test searchDistributors, should serialize and deserialize correctly`() {
        val distributor1 = createDistributor(title = "Distributor title 1")
        val distributor2 = createDistributor(title = "Distributor title 2")

        every { adminSearchDistributorQueryService(any()) } returns PageImpl(
            listOf(
                AdminSearchDistributorsResponse(
                    id = distributor1.id,
                    title = distributor1.title,
                    createdAt = distributor1.createdAt,
                    updatedAt = distributor1.updatedAt
                ),
                AdminSearchDistributorsResponse(
                    id = distributor2.id,
                    title = distributor2.title,
                    createdAt = distributor2.createdAt,
                    updatedAt = distributor2.updatedAt
                )
            )
        )

        mvc.post(SEARCH_DISTRIBUTORS_PATH) {
            contentType = MediaType.APPLICATION_JSON
            header(HttpHeaders.ACCEPT, ApiVersion.VERSION_1_JSON)
            header(HttpHeaders.AUTHORIZATION, mockAccessToken(role = EmployeeRole.MANAGER))
            content = """
                {
                  "title": "movie"
                }
            """.trimIndent()
        }.andExpect {
            status { isOk() }
            content { contentType(ApiVersion.VERSION_1_JSON) }
            jsonContent(
                """
                    {
                      "content": [
                        {
                          "id": "${distributor1.id}",
                          "title": "${distributor1.title}",
                          "createdAt": "${distributor1.createdAt.truncatedAndFormatted()}",
                          "updatedAt": "${distributor1.updatedAt.truncatedAndFormatted()}"
                        },
                        {
                          "id": "${distributor2.id}",
                          "title": "${distributor2.title}",
                          "createdAt": "${distributor2.createdAt.truncatedAndFormatted()}",
                          "updatedAt": "${distributor2.updatedAt.truncatedAndFormatted()}"
                        }
                      ],
                      "totalElements": 2,
                      "totalPages": 1
                    }
                """.trimIndent()
            )
        }

        verifySequence {
            adminSearchDistributorQueryService(
                AdminSearchDistributorsQuery(
                    filter = SearchDistributorsFilter(title = "movie"),
                    pageable = PageRequest.of(0, 10, Sort.by(DistributorColumnNames.TITLE))
                )
            )
        }
    }

    @Test
    fun `test getDistributor, should serialize and deserialize correctly`() {
        val distributorId = UUID.fromString("9e22ebee-f458-4c8b-a8ee-1503ee5099d7")
        val distributor1 = createDistributor(
            id = distributorId,
            originalId = 1,
            code = "DISTR_1",
            disfilmCode = "01",
            title = "Best movies ever, s.r.o.",
            addressStreet = "Filmová 58",
            addressCity = "Praha",
            addressPostCode = "150 00",
            contactName1 = "John",
            contactName2 = "Jerry",
            contactName3 = "Tom",
            contactPhone1 = "+************",
            contactPhone2 = "+************",
            contactPhone3 = "*********",
            contactEmails = setOf("<EMAIL>", "<EMAIL>"),
            bankName = "AirBank, a.s.",
            bankAccount = "***********/0300",
            idNumber = "********",
            taxIdNumber = "SK2022916731",
            vatRate = 20,
            note = "Best note ever"
        )

        every { adminGetDistributorQueryService(any()) } returns AdminGetDistributorResponse(
            id = distributorId,
            code = "DISTR_1",
            disfilmCode = "01",
            title = "Best movies ever, s.r.o.",
            addressStreet = "Filmová 58",
            addressCity = "Praha",
            addressPostCode = "150 00",
            contactName1 = "John",
            contactName2 = "Jerry",
            contactName3 = "Tom",
            contactPhone1 = "+************",
            contactPhone2 = "+************",
            contactPhone3 = "*********",
            contactEmails = setOf("<EMAIL>", "<EMAIL>"),
            bankName = "AirBank, a.s.",
            bankAccount = "***********/0300",
            idNumber = "********",
            taxIdNumber = "SK2022916731",
            vatRate = 20,
            note = "Best note ever"
        )

        mvc.get("$MANAGER_BASE_DISTRIBUTOR_PATH/${distributor1.id}") {
            contentType = MediaType.APPLICATION_JSON
            header(HttpHeaders.ACCEPT, ApiVersion.VERSION_1_JSON)
            header(HttpHeaders.AUTHORIZATION, mockAccessToken(role = EmployeeRole.MANAGER))
        }.andExpect {
            status { isOk() }
            content { contentType(ApiVersion.VERSION_1_JSON) }
            jsonContent(
                """
                    {
                      "id": "${distributor1.id}",
                      "code": "DISTR_1",
                      "disfilmCode": "01",
                      "title": "Best movies ever, s.r.o.",
                      "addressStreet": "Filmová 58",
                      "addressCity": "Praha",
                      "addressPostCode": "150 00",
                      "contactName1": "John",
                      "contactName2": "Jerry",
                      "contactName3": "Tom",
                      "contactPhone1": "+************",
                      "contactPhone2": "+************",
                      "contactPhone3": "*********",
                      "contactEmails": [
                        "<EMAIL>",
                        "<EMAIL>"
                      ],
                      "bankName": "AirBank, a.s.",
                      "bankAccount": "***********/0300",
                      "idNumber": "********",
                      "taxIdNumber": "SK2022916731",
                      "vatRate": 20,
                      "note": "Best note ever"
                    }
                """.trimIndent()
            )
        }

        verifySequence { adminGetDistributorQueryService(AdminGetDistributorQuery(distributor1.id)) }
    }

    @Test
    fun `test updateDistributor, should serialize and deserialize correctly`() {
        val distributorId = UUID.fromString("9e22ebee-f458-4c8b-a8ee-1503ee5099d7")
        every { distributorService.adminCreateOrUpdateDistributor(any()) } returns distributorId

        mvc.put(GET_AND_UPDATE_DISTRIBUTOR_PATH(distributorId)) {
            header(HttpHeaders.ACCEPT, ApiVersion.VERSION_1_JSON)
            header(HttpHeaders.AUTHORIZATION, mockAccessToken(EmployeeRole.MANAGER))
            contentType = MediaType.APPLICATION_JSON
            content = """
                {
                  "disfilmCode": "01",
                  "title": "Vertigo Distribution s.r.o.",
                  "addressStreet": "Ul. gen.Klapku 68/43",
                  "addressCity": "Komárno",
                  "addressPostCode": "945 01",
                  "contactName1": "Tomáš Ficza",
                  "contactName2": "Peter Ficza",
                  "contactName3": "Jan Ficza",
                  "contactPhone1": "+************",
                  "contactPhone2": "+************",
                  "contactPhone3": "+************",
                  "contactEmails": [
                    "<EMAIL>",
                    "<EMAIL>"
                  ],
                  "bankName": "UniCredit Bank Czech Republic and Slovakia, a.s.",
                  "bankAccount": "**********/1111",
                  "idNumber": "********",
                  "taxIdNumber": "**********",
                  "vatRate": 5,
                  "note": "marketingová akcia -  event Spievankovo"
                }
            """.trimIndent()
        }.andExpect {
            status { isOk() }
            jsonContent(
                """
                    {
                      "id": "$distributorId"
                    }
                """.trimIndent()

            )
        }

        verifySequence {
            distributorService.adminCreateOrUpdateDistributor(
                CreateOrUpdateDistributorCommand(
                    id = distributorId,
                    code = null,
                    disfilmCode = Optional.of("01"),
                    title = "Vertigo Distribution s.r.o.",
                    addressStreet = Optional.of("Ul. gen.Klapku 68/43"),
                    addressCity = Optional.of("Komárno"),
                    addressPostCode = Optional.of("945 01"),
                    contactName1 = Optional.of("Tomáš Ficza"),
                    contactName2 = Optional.of("Peter Ficza"),
                    contactName3 = Optional.of("Jan Ficza"),
                    contactPhone1 = Optional.of("+************"),
                    contactPhone2 = Optional.of("+************"),
                    contactPhone3 = Optional.of("+************"),
                    contactEmails = Optional.of(setOf("<EMAIL>", "<EMAIL>")),
                    bankName = Optional.of("UniCredit Bank Czech Republic and Slovakia, a.s."),
                    bankAccount = Optional.of("**********/1111"),
                    idNumber = Optional.of("********"),
                    taxIdNumber = Optional.of("**********"),
                    vatRate = Optional.of(5),
                    note = Optional.of("marketingová akcia -  event Spievankovo")
                )
            )
        }
    }

    @Test
    fun `test updateDistributor, should serialize and deserialize correctly with null values or missing properties`() {
        val distributorId = UUID.fromString("9e22ebee-f458-4c8b-a8ee-1503ee5099d7")
        every { distributorService.adminCreateOrUpdateDistributor(any()) } returns distributorId

        mvc.put(GET_AND_UPDATE_DISTRIBUTOR_PATH(distributorId)) {
            header(HttpHeaders.ACCEPT, ApiVersion.VERSION_1_JSON)
            header(HttpHeaders.AUTHORIZATION, mockAccessToken(EmployeeRole.MANAGER))
            contentType = MediaType.APPLICATION_JSON
            content = """
                {
                  "disfilmCode": null,
                  "title": "Vertigo Distribution s.r.o.",
                  "addressStreet": null,
                  "addressCity": null,
                  "addressPostCode": null,
                  "contactName1": "Tomáš Ficza",
                  "contactPhone1": "+************"
                }
            """.trimIndent()
        }.andExpect {
            status { isOk() }
            jsonContent(
                """
                    {
                      "id": "$distributorId"
                    }
                """.trimIndent()

            )
        }

        verifySequence {
            distributorService.adminCreateOrUpdateDistributor(
                CreateOrUpdateDistributorCommand(
                    id = distributorId,
                    code = null,
                    disfilmCode = Optional.empty(),
                    title = "Vertigo Distribution s.r.o.",
                    addressStreet = Optional.empty(),
                    addressCity = Optional.empty(),
                    addressPostCode = Optional.empty(),
                    contactName1 = Optional.of("Tomáš Ficza"),
                    contactName2 = null,
                    contactName3 = null,
                    contactPhone1 = Optional.of("+************"),
                    contactPhone2 = null,
                    contactPhone3 = null,
                    contactEmails = Optional.empty(),
                    bankName = null,
                    bankAccount = null,
                    idNumber = null,
                    taxIdNumber = null,
                    vatRate = null,
                    note = null
                )
            )
        }
    }
}

private const val MANAGER_BASE_DISTRIBUTOR_PATH = "/manager-app/distributors"
private const val SEARCH_DISTRIBUTORS_PATH = "/manager-app/distributors/search"
private val GET_AND_UPDATE_DISTRIBUTOR_PATH: (UUID) -> String =
    { distributorId: UUID -> "$MANAGER_BASE_DISTRIBUTOR_PATH/$distributorId" }
