package com.cleevio.cinemax.api.module.ticket.event.listener

import com.cleevio.cinemax.api.module.ticket.event.TicketSyncedToMssqlEvent
import com.cleevio.cinemax.api.module.ticket.service.TicketService
import com.cleevio.cinemax.api.module.ticket.service.command.UpdateTicketOriginalIdCommand
import io.mockk.Runs
import io.mockk.confirmVerified
import io.mockk.every
import io.mockk.just
import io.mockk.mockk
import io.mockk.verifySequence
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.Test
import java.util.UUID

class OutboxEventTicketEventListenerTest {

    private val ticketService = mockk<TicketService>()

    private val eventListener = OutboxEventTicketEventListener(ticketService)

    @AfterEach
    fun afterEach() = confirmVerified(
        ticketService
    )

    @Test
    fun `test listenToTicketSyncedToMssqlEvent - should call ticketService`() {
        every { ticketService.updateTicketOriginalId(any()) } just Runs

        eventListener.listenToTicketSyncedToMssqlEvent(
            TicketSyncedToMssqlEvent(
                ticketId = TICKET_1_ID,
                originalTicketId = TICKET_1_ORIGINAL_ID
            )
        )

        verifySequence {
            ticketService.updateTicketOriginalId(
                UpdateTicketOriginalIdCommand(
                    ticketId = TICKET_1_ID,
                    originalId = TICKET_1_ORIGINAL_ID
                )
            )
        }
    }
}

private val TICKET_1_ID = UUID.randomUUID()
private const val TICKET_1_ORIGINAL_ID = 1
