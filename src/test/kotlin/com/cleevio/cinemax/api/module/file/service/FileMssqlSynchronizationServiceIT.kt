package com.cleevio.cinemax.api.module.file.service

import com.cleevio.cinemax.api.MssqlIntegrationTest
import com.cleevio.cinemax.api.module.file.constant.FileType
import com.cleevio.cinemax.api.module.file.service.command.CreateOrUpdateFileCommand
import com.cleevio.cinemax.api.module.synchronizationfrommssql.constant.SynchronizationFromMssqlType
import com.cleevio.cinemax.api.module.synchronizationfrommssql.entity.SynchronizationFromMssql
import com.cleevio.cinemax.api.module.synchronizationfrommssql.service.command.CreateOrUpdateSynchronizationFromMssqlCommand
import io.mockk.Runs
import io.mockk.every
import io.mockk.just
import io.mockk.verifyAll
import org.apache.commons.io.IOUtils
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.test.context.jdbc.Sql
import org.springframework.test.context.jdbc.SqlConfig
import org.springframework.test.context.jdbc.SqlGroup
import java.time.LocalDateTime
import kotlin.test.assertEquals
import kotlin.test.assertTrue

@SqlGroup(
    Sql(
        scripts = [
            "/db/mssql-buffet/test/init_mssql_file.sql"
        ],
        config = SqlConfig(
            dataSource = "mssqlBuffetDataSource",
            transactionManager = "mssqlChainedTransactionManager"
        )
    ),
    Sql(
        scripts = [
            "/db/mssql-buffet/test/drop_mssql_file.sql"
        ],
        config = SqlConfig(
            dataSource = "mssqlBuffetDataSource",
            transactionManager = "mssqlChainedTransactionManager"
        ),
        executionPhase = Sql.ExecutionPhase.AFTER_TEST_METHOD
    )
)
class FileMssqlSynchronizationServiceIT @Autowired constructor(
    private val underTest: FileMssqlSynchronizationService,
) : MssqlIntegrationTest() {

    @Test
    fun `test synchronize all - no PSQL files, 5 MSSQL files - should create 3 files`() {
        every { productMssqlFinderServiceMock.findAllImagesOriginalNames() } returns listOf(
            IMAGE_ORIGINAL_NAME_HARIBO,
            IMAGE_ORIGINAL_NAME_ZACAPA,
            IMAGE_ORIGINAL_NAME_YES
        )
        every { productCategoryMssqlFinderServiceMock.findAllImagesOriginalNames() } returns listOf(
            IMAGE_ORIGINAL_NAME_NAPOJE,
            IMAGE_ORIGINAL_NAME_COMBO
        )
        every { synchronizationFromMssqlFinderServiceMock.findByType(any()) } returns null
        every { fileServiceMock.createOrUpdateFile(any()) } just Runs
        every { synchronizationFromMssqlServiceMock.createOrUpdate(any()) } returns SYNCHRONIZATION_FROM_MSSQL

        underTest.synchronizeAll()

        val commandCaptor = mutableListOf<CreateOrUpdateFileCommand>()

        verifyAll {
            synchronizationFromMssqlFinderServiceMock.findByType(SynchronizationFromMssqlType.FILE)
            fileServiceMock.createOrUpdateFile(capture(commandCaptor))
            synchronizationFromMssqlServiceMock.createOrUpdate(
                CreateOrUpdateSynchronizationFromMssqlCommand(
                    type = SynchronizationFromMssqlType.FILE,
                    lastSynchronization = LAST_RECORD_UPDATED_AT
                )
            )
        }

        assertTrue(commandCaptor.size == 3)
        assertCommandEquals(EXPECTED_COMMAND_1, commandCaptor[0])
        assertCommandEquals(EXPECTED_COMMAND_2, commandCaptor[1])
        assertCommandEquals(EXPECTED_COMMAND_3, commandCaptor[2])
    }

    @Test
    fun `test synchronize all - 2 PSQL files, 5 MSSQL files - should create 1 file`() {
        every { productMssqlFinderServiceMock.findAllImagesOriginalNames() } returns listOf(
            IMAGE_ORIGINAL_NAME_HARIBO,
            IMAGE_ORIGINAL_NAME_ZACAPA,
            IMAGE_ORIGINAL_NAME_YES
        )
        every { productCategoryMssqlFinderServiceMock.findAllImagesOriginalNames() } returns listOf(
            IMAGE_ORIGINAL_NAME_NAPOJE,
            IMAGE_ORIGINAL_NAME_COMBO
        )
        every { synchronizationFromMssqlFinderServiceMock.findByType(any()) } returns FILE_2_UPDATED_AT
        every { fileServiceMock.createOrUpdateFile(any()) } just Runs
        every { synchronizationFromMssqlServiceMock.createOrUpdate(any()) } returns SYNCHRONIZATION_FROM_MSSQL

        underTest.synchronizeAll()

        val commandCaptor = mutableListOf<CreateOrUpdateFileCommand>()

        verifyAll {
            synchronizationFromMssqlFinderServiceMock.findByType(SynchronizationFromMssqlType.FILE)
            fileServiceMock.createOrUpdateFile(capture(commandCaptor))
            synchronizationFromMssqlServiceMock.createOrUpdate(
                CreateOrUpdateSynchronizationFromMssqlCommand(
                    type = SynchronizationFromMssqlType.FILE,
                    lastSynchronization = LAST_RECORD_UPDATED_AT
                )
            )
        }

        assertTrue(commandCaptor.size == 1)
        assertCommandEquals(EXPECTED_COMMAND_3, commandCaptor[0])
    }

    private fun assertCommandEquals(expected: CreateOrUpdateFileCommand, actual: CreateOrUpdateFileCommand) {
        assertEquals(expected.originalId, actual.originalId)
        assertEquals(expected.originalName, actual.originalName)
        assertEquals(expected.type, actual.type)
        assertEquals(expected.extension, actual.extension)
        IOUtils.contentEquals(expected.inputStream, actual.inputStream)
    }
}

private const val IMAGE_ORIGINAL_NAME_HARIBO = "HARIBO.PNG"
private const val IMAGE_ORIGINAL_NAME_ZACAPA = "ZACAPA.JPG"
private const val IMAGE_ORIGINAL_NAME_YES = "YES.PNG"
private const val IMAGE_ORIGINAL_NAME_NAPOJE = "MENU NAPOJE.PNG"
private const val IMAGE_ORIGINAL_NAME_COMBO = "MENU COMBO,PNG"
private const val TEST_IMAGE_BYTE_ARRAY_RAW = "89504E470D0A1A0A0000000D49484452000000640000004B0806000000861822B700" +
    "0000097048597300000B1300000B1301009A9C18000000C549444154789CEDD1310D00300CC0B01EE58FB9F7102C878D20527648D9DF01BC" +
    "0C893124C6901843620C893124C6901843620C893124C6901843620C893124C6901843620C893124C6901843620C893124C6901843620C89" +
    "3124C6901843620C893124C6901843620C893124C6901843620C893124C6901843620C893124C6901843620C893124C6901843620C893124" +
    "C6901843620C893124C6901843620C893124C6901843620C893124C6901843620C893124C6901843620C893124C6901843620C89393A9D01" +
    "2D913CA7FA0000000049454E44AE426082"
private val TEST_IMAGE_BYTE_ARRAY = TEST_IMAGE_BYTE_ARRAY_RAW.chunked(2)
    .map { it.toInt(16).toByte() }
    .toByteArray()
private val FILE_2_UPDATED_AT = LocalDateTime.of(2014, 10, 28, 12, 0, 0)
private val LAST_RECORD_UPDATED_AT = LocalDateTime.of(2017, 9, 29, 15, 0, 0)
private val EXPECTED_COMMAND_1 = CreateOrUpdateFileCommand(
    id = null,
    originalId = 1,
    type = FileType.PRODUCT_IMAGE,
    originalName = IMAGE_ORIGINAL_NAME_HARIBO,
    extension = "PNG",
    inputStream = TEST_IMAGE_BYTE_ARRAY.inputStream()
)
private val EXPECTED_COMMAND_2 = CreateOrUpdateFileCommand(
    id = null,
    originalId = 2,
    type = FileType.PRODUCT_IMAGE,
    originalName = IMAGE_ORIGINAL_NAME_ZACAPA,
    extension = "JPG",
    inputStream = TEST_IMAGE_BYTE_ARRAY.inputStream()
)
private val EXPECTED_COMMAND_3 = CreateOrUpdateFileCommand(
    id = null,
    originalId = 3,
    type = FileType.PRODUCT_CATEGORY_IMAGE,
    originalName = IMAGE_ORIGINAL_NAME_NAPOJE,
    extension = "PNG",
    inputStream = TEST_IMAGE_BYTE_ARRAY.inputStream()
)
private val SYNCHRONIZATION_FROM_MSSQL = SynchronizationFromMssql(
    type = SynchronizationFromMssqlType.FILE,
    lastSynchronization = LocalDateTime.MIN,
    lastHeartbeat = null
)
