package com.cleevio.cinemax.api.module.file.entity

import com.cleevio.cinemax.api.module.file.constant.FileType
import com.cleevio.cinemax.api.module.file.event.toMessagingEvent
import com.cleevio.cinemax.api.util.createFile
import io.kotest.matchers.shouldBe
import org.junit.jupiter.api.Test
import java.util.UUID

class FileExtensionsTest {

    @Test
    fun `test toMessagingEvent - should map File to FileImageUploadedEvent correctly`() {
        val fileId = UUID.randomUUID()
        val originalId = 123
        val type = FileType.PRODUCT_IMAGE
        val originalName = "test-image.jpg"
        val extension = "jpg"
        val base64Content = "base64EncodedContent"

        val file = createFile(
            id = fileId,
            originalId = originalId,
            type = type,
            originalName = originalName,
            extension = extension
        )

        val event = file.toMessagingEvent(base64Content)

        event.fileId shouldBe fileId
        event.type shouldBe type
        event.originalName shouldBe originalName
        event.extension shouldBe extension
        event.base64Content shouldBe base64Content
    }
}
