package com.cleevio.cinemax.api.module.outboxevent.event.listener

import com.cleevio.cinemax.api.module.movie.event.DISFilmMoviesCreatedOrUpdatedEvent
import com.cleevio.cinemax.api.module.movie.event.MovieCreatedOrUpdatedEvent
import com.cleevio.cinemax.api.module.outboxevent.constant.OutboxEventType
import com.cleevio.cinemax.api.module.outboxevent.service.OutboxEventService
import com.cleevio.cinemax.api.module.outboxevent.service.command.CreateOutboxEventCommand
import com.cleevio.cinemax.api.module.outboxevent.service.command.CreateOutboxEventsCommand
import io.mockk.Called
import io.mockk.every
import io.mockk.just
import io.mockk.mockk
import io.mockk.runs
import io.mockk.verify
import io.mockk.verifySequence
import org.junit.jupiter.api.Test
import java.util.UUID

class MovieOutboxEventEventListenerTest {

    private val outboxEventService = mockk<OutboxEventService>()
    private val underTest = MovieOutboxEventEventListener(outboxEventService)

    @Test
    fun `test listenToAdminCreatedOrUpdatedMovieEvent - should correctly handle listenToAdminCreatedOrUpdatedMovieEvent`() {
        every { outboxEventService.createOutboxEvent(any()) } just runs

        val movieId = UUID.fromString("65aa8e3f-4b86-4eb3-b2e9-5b1482021eda")
        underTest.listenToMovieCreatedOrUpdatedEvent(
            MovieCreatedOrUpdatedEvent(movieId)
        )

        verify {
            outboxEventService.createOutboxEvent(
                CreateOutboxEventCommand(
                    entityId = movieId,
                    type = OutboxEventType.MOVIE_CREATED_OR_UPDATED
                )
            )
        }
    }

    @Test
    fun `test listenToDISFilmMoviesCreatedOrUpdatedEvent - should correctly handle listenToDISFilmMoviesCreatedEvent`() {
        every { outboxEventService.createOutboxEvents(any()) } just runs

        val movie1Id = UUID.fromString("65aa8e3f-4b86-4eb3-b2e9-5b1482021eda")
        val movie2Id = UUID.fromString("7f8d36d3-9678-42ce-9b2d-ae8331c34c92")

        underTest.listenToDISFilmMoviesCreatedOrUpdatedEvent(
            DISFilmMoviesCreatedOrUpdatedEvent(setOf(movie1Id, movie2Id))
        )

        verifySequence {
            outboxEventService.createOutboxEvents(
                CreateOutboxEventsCommand(
                    entityIds = setOf(movie1Id, movie2Id),
                    type = OutboxEventType.MOVIE_CREATED_OR_UPDATED
                )
            )
        }
    }

    @Test
    fun `test listenToDISFilmMoviesCreatedOrUpdatedEvent - empty collection - should not call outboxEventService`() {
        every { outboxEventService.createOutboxEvents(any()) } just runs

        underTest.listenToDISFilmMoviesCreatedOrUpdatedEvent(
            DISFilmMoviesCreatedOrUpdatedEvent(setOf())
        )

        verify { outboxEventService wasNot Called }
    }
}
