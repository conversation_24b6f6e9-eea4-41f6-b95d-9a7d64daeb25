package com.cleevio.cinemax.api.module.product.controller

import com.cleevio.cinemax.api.ControllerTest
import com.cleevio.cinemax.api.common.constant.ApiVersion
import com.cleevio.cinemax.api.common.constant.ExportFormat
import com.cleevio.cinemax.api.common.service.model.ExportResultModel
import com.cleevio.cinemax.api.common.util.jsonContent
import com.cleevio.cinemax.api.module.employee.constant.EmployeeRole
import com.cleevio.cinemax.api.module.product.constant.ProductType
import com.cleevio.cinemax.api.module.product.controller.dto.AdminGetProductResponse
import com.cleevio.cinemax.api.module.product.controller.dto.AdminSearchProductsResponse
import com.cleevio.cinemax.api.module.product.service.command.AdminCreateProductCommand
import com.cleevio.cinemax.api.module.product.service.command.AdminCreateProductCommand.AdminCreateProductProductCompositionCommand
import com.cleevio.cinemax.api.module.product.service.command.AdminUpdateProductCommand
import com.cleevio.cinemax.api.module.product.service.command.AdminUpdateProductCommand.AdminUpdateProductProductCompositionCommand
import com.cleevio.cinemax.api.module.product.service.command.DeleteProductCommand
import com.cleevio.cinemax.api.module.product.service.query.AdminExportProductMarginRatesFilter
import com.cleevio.cinemax.api.module.product.service.query.AdminExportProductMarginRatesQuery
import com.cleevio.cinemax.api.module.product.service.query.AdminGetProductQuery
import com.cleevio.cinemax.api.module.product.service.query.AdminSearchProductsFilter
import com.cleevio.cinemax.api.module.product.service.query.AdminSearchProductsQuery
import com.cleevio.cinemax.api.module.productcategory.constant.ProductCategoryType
import com.cleevio.cinemax.api.module.productcomponent.constant.ProductComponentUnit
import com.cleevio.cinemax.api.truncatedAndFormatted
import com.cleevio.cinemax.api.util.mockAccessToken
import com.cleevio.cinemax.api.util.toUUID
import com.cleevio.cinemax.psql.tables.ProductColumnNames
import io.mockk.every
import io.mockk.verify
import io.mockk.verifySequence
import org.hamcrest.CoreMatchers.containsString
import org.junit.jupiter.api.Test
import org.junitpioneer.jupiter.RetryingTest
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest
import org.springframework.data.domain.PageImpl
import org.springframework.data.domain.PageRequest
import org.springframework.data.domain.Sort
import org.springframework.http.HttpHeaders
import org.springframework.http.MediaType
import org.springframework.test.web.servlet.MockMvc
import org.springframework.test.web.servlet.delete
import org.springframework.test.web.servlet.get
import org.springframework.test.web.servlet.post
import org.springframework.test.web.servlet.put
import java.io.ByteArrayInputStream
import java.math.BigDecimal
import java.time.LocalDate
import java.time.LocalDateTime
import java.util.UUID

@WebMvcTest(AdminProductController::class)
class AdminProductControllerTest @Autowired constructor(
    private val mvc: MockMvc,
) : ControllerTest() {

    @Test
    fun `test createProduct, should serialize and deserialize correctly`() {
        val productId = 1.toUUID()
        val productCategoryId = 2.toUUID()
        val imageFileId = 3.toUUID()
        val productComponentId1 = 4.toUUID()
        val productComponentId2 = 5.toUUID()
        val productInProductId1 = 6.toUUID()
        val productInProductId2 = 7.toUUID()
        val productInProductPrice = 5.5.toBigDecimal()

        every { productService.adminCreateProduct(any()) } returns productId

        mvc.post(BASE_PRODUCT_PATH) {
            header(HttpHeaders.ACCEPT, ApiVersion.VERSION_1_JSON)
            header(HttpHeaders.AUTHORIZATION, mockAccessToken(EmployeeRole.MANAGER))
            contentType = MediaType.APPLICATION_JSON
            content = """
                {
                  "title": "New Product",
                  "active": true,
                  "type": "PRODUCT",
                  "productCategoryId": "$productCategoryId",
                  "imageFileId": "$imageFileId",
                  "price": 100.5,
                  "flagshipPrice": 200.5,
                  "soldInBuffet": true,
                  "soldInCafe": false,
                  "soldInVip": true,
                  "order": 1,
                  "tabletOrder": 2,
                  "isPackagingDeposit": false,
                  "stockQuantityThreshold": 10,
                  "taxRate": 15,
                  "discountAmount": 5.0,
                  "discountPercentage": 10,
                  "productComposition": [
                    {
                      "productComponentId": "$productComponentId1",
                      "productInProductId": "$productInProductId1",
                      "quantity": 2.0,
                      "productInProductPrice": "$productInProductPrice"
                    },
                    {
                      "productComponentId": "$productComponentId2",
                      "productInProductId": "$productInProductId2",
                      "quantity": 3.0
                    }
                  ]
                }
            """.trimIndent()
        }.andExpect {
            status { isOk() }
            jsonContent(
                """
                    {
                      "id": "$productId"
                    }
                """.trimIndent()
            )
        }

        verifySequence {
            productService.adminCreateProduct(
                AdminCreateProductCommand(
                    title = "New Product",
                    active = true,
                    type = ProductType.PRODUCT,
                    productCategoryId = productCategoryId,
                    imageFileId = imageFileId,
                    price = 100.5.toBigDecimal(),
                    flagshipPrice = 200.5.toBigDecimal(),
                    soldInBuffet = true,
                    soldInCafe = false,
                    soldInVip = true,
                    order = 1,
                    tabletOrder = 2,
                    isPackagingDeposit = false,
                    stockQuantityThreshold = 10,
                    taxRate = 15,
                    discountAmount = 5.0.toBigDecimal(),
                    discountPercentage = 10,
                    productComposition = listOf(
                        AdminCreateProductProductCompositionCommand(
                            productComponentId = productComponentId1,
                            productInProductId = productInProductId1,
                            quantity = 2.0.toBigDecimal(),
                            productInProductPrice = productInProductPrice
                        ),
                        AdminCreateProductProductCompositionCommand(
                            productComponentId = productComponentId2,
                            productInProductId = productInProductId2,
                            quantity = 3.0.toBigDecimal()
                        )
                    )
                )
            )
        }
    }

    @Test
    fun `test updateProduct, should serialize and deserialize correctly`() {
        val productId = 1.toUUID()
        val productCategoryId = 2.toUUID()
        val imageFileId = 3.toUUID()
        val productComponentId1 = 4.toUUID()
        val productComponentId2 = 5.toUUID()
        val productInProductId1 = 6.toUUID()
        val productInProductId2 = 7.toUUID()
        val productInProductPrice = 5.5.toBigDecimal()

        every { productService.adminUpdateProduct(any()) } returns productId

        mvc.put(GET_AND_UPDATE_AND_DELETE_PRODUCT_PATH(productId)) {
            header(HttpHeaders.ACCEPT, ApiVersion.VERSION_1_JSON)
            header(HttpHeaders.AUTHORIZATION, mockAccessToken(EmployeeRole.MANAGER))
            contentType = MediaType.APPLICATION_JSON
            content = """
                {
                  "title": "Updated Product",
                  "active": true,
                  "productCategoryId": "$productCategoryId",
                  "imageFileId": "$imageFileId",
                  "price": 120.75,
                  "flagshipPrice": 220.75,
                  "soldInBuffet": true,
                  "soldInCafe": false,
                  "soldInVip": true,
                  "order": 1,
                  "tabletOrder": 2,
                  "isPackagingDeposit": false,
                  "stockQuantityThreshold": 20,
                  "taxRate": 18,
                  "discountAmount": 8.0,
                  "discountPercentage": 15,
                  "productComposition": [
                    {
                      "productComponentId": "$productComponentId1",
                      "productInProductId": "$productInProductId1",
                      "quantity": 2.5,
                      "productInProductPrice": "$productInProductPrice"
                    },
                    {
                      "productComponentId": "$productComponentId2",
                      "productInProductId": "$productInProductId2",
                      "quantity": 3.5
                    }
                  ]
                }
            """.trimIndent()
        }.andExpect {
            status { isOk() }
            jsonContent(
                """
                    {
                      "id": "$productId"
                    }
                """.trimIndent()
            )
        }

        verifySequence {
            productService.adminUpdateProduct(
                AdminUpdateProductCommand(
                    id = productId,
                    title = "Updated Product",
                    active = true,
                    productCategoryId = productCategoryId,
                    imageFileId = imageFileId,
                    price = 120.75.toBigDecimal(),
                    flagshipPrice = 220.75.toBigDecimal(),
                    soldInBuffet = true,
                    soldInCafe = false,
                    soldInVip = true,
                    order = 1,
                    tabletOrder = 2,
                    isPackagingDeposit = false,
                    stockQuantityThreshold = 20,
                    taxRate = 18,
                    discountAmount = 8.0.toBigDecimal(),
                    discountPercentage = 15,
                    productComposition = listOf(
                        AdminUpdateProductProductCompositionCommand(
                            productComponentId = productComponentId1,
                            productInProductId = productInProductId1,
                            quantity = 2.5.toBigDecimal(),
                            productInProductPrice = productInProductPrice
                        ),
                        AdminUpdateProductProductCompositionCommand(
                            productComponentId = productComponentId2,
                            productInProductId = productInProductId2,
                            quantity = 3.5.toBigDecimal()
                        )
                    )
                )
            )
        }
    }

    @Test
    fun `test deleteProduct, should call service and return no content`() {
        val productId = 1.toUUID()

        every { productService.deleteProduct(any()) } returns Unit

        mvc.delete(GET_AND_UPDATE_AND_DELETE_PRODUCT_PATH(productId)) {
            header(HttpHeaders.ACCEPT, ApiVersion.VERSION_1_JSON)
            header(HttpHeaders.AUTHORIZATION, mockAccessToken(EmployeeRole.MANAGER))
        }.andExpect {
            status { isNoContent() }
        }

        verifySequence {
            productService.deleteProduct(DeleteProductCommand(productId))
        }
    }

    @Test
    fun `test getProduct, should serialize and deserialize correctly`() {
        val productId = 1.toUUID()
        val productResponse = AdminGetProductResponse(
            id = productId,
            title = "Test Product",
            code = "P1234",
            active = true,
            type = ProductType.PRODUCT,
            price = "100.5".toBigDecimal(),
            flagshipPrice = "200.5".toBigDecimal(),
            priceNoVat = "85.0".toBigDecimal(),
            soldInBuffet = true,
            soldInCafe = false,
            soldInVip = true,
            order = 1,
            tabletOrder = 2,
            isPackagingDeposit = false,
            stockQuantity = "20.0".toBigDecimal(),
            stockQuantityThreshold = 10,
            discountAmount = "5.0".toBigDecimal(),
            discountPercentage = 10,
            taxRate = 12,
            createdAt = LocalDateTime.now(),
            updatedAt = LocalDateTime.now(),
            productCategory = AdminGetProductResponse.ProductCategoryResponse(
                id = 2.toUUID(),
                type = ProductCategoryType.PRODUCT,
                title = "Snacks"
            ),
            imageFile = AdminGetProductResponse.ImageFileResponse(
                id = 3.toUUID(),
                url = "https://example.com/image.jpg"
            ),
            productComposition = listOf(
                AdminGetProductResponse.ProductCompositionResponse(
                    productComponent = AdminGetProductResponse.ProductComponentResponse(
                        id = 4.toUUID(),
                        title = "Component 1",
                        quantity = "2.0".toBigDecimal(),
                        unit = ProductComponentUnit.KG
                    ),
                    productInProduct = null
                ),
                AdminGetProductResponse.ProductCompositionResponse(
                    productComponent = null,
                    productInProduct = AdminGetProductResponse.ProductInProductResponse(
                        id = 5.toUUID(),
                        title = "Sub-product 1",
                        quantity = "10.0".toBigDecimal(),
                        unit = ProductComponentUnit.G,
                        productInProductPrice = 5.5.toBigDecimal(),
                        productInProductFlagshipPrice = 4.5.toBigDecimal()
                    )
                )
            )
        )

        every { adminGetProductQueryService(AdminGetProductQuery(productId)) } returns productResponse

        mvc.get(GET_AND_UPDATE_AND_DELETE_PRODUCT_PATH(productId)) {
            contentType = MediaType.APPLICATION_JSON
            header(HttpHeaders.ACCEPT, ApiVersion.VERSION_1_JSON)
            header(HttpHeaders.AUTHORIZATION, mockAccessToken(role = EmployeeRole.MANAGER))
        }.andExpect {
            status { isOk() }
            content { contentType(ApiVersion.VERSION_1_JSON) }
            jsonContent(
                """
            {
                "id": "${productResponse.id}",
                "title": "${productResponse.title}",
                "code": "${productResponse.code}",
                "active": ${productResponse.active},
                "type": "${productResponse.type}",
                "price": ${productResponse.price},
                "flagshipPrice": ${productResponse.flagshipPrice},
                "priceNoVat": ${productResponse.priceNoVat},
                "soldInBuffet": ${productResponse.soldInBuffet},
                "soldInCafe": ${productResponse.soldInCafe},
                "soldInVip": ${productResponse.soldInVip},
                "order": ${productResponse.order},
                "tabletOrder": ${productResponse.tabletOrder},
                "isPackagingDeposit": ${productResponse.isPackagingDeposit},
                "stockQuantity": ${productResponse.stockQuantity},
                "stockQuantityThreshold": ${productResponse.stockQuantityThreshold},
                "discountAmount": ${productResponse.discountAmount},
                "discountPercentage": ${productResponse.discountPercentage},
                "taxRate": ${productResponse.taxRate},
                "createdAt": "${productResponse.createdAt.truncatedAndFormatted()}",
                "updatedAt": "${productResponse.updatedAt.truncatedAndFormatted()}",
                "productCategory": {
                    "id": "${productResponse.productCategory.id}",
                    "type": "${productResponse.productCategory.type}",
                    "title": "${productResponse.productCategory.title}"
                },
                "imageFile": {
                    "id": "${productResponse.imageFile?.id}",
                    "url": "${productResponse.imageFile?.url}"
                },
                "productComposition": [
                    {
                        "productComponent": {
                            "id": "${productResponse.productComposition[0].productComponent?.id}",
                            "title": "${productResponse.productComposition[0].productComponent?.title}",
                            "quantity": ${productResponse.productComposition[0].productComponent?.quantity},
                            "unit": "${productResponse.productComposition[0].productComponent?.unit}"
                        },
                        "productInProduct": null
                    },
                    {
                        "productComponent": null,
                        "productInProduct": {
                            "id": "${productResponse.productComposition[1].productInProduct?.id}",
                            "title": "${productResponse.productComposition[1].productInProduct?.title}",
                            "quantity": ${productResponse.productComposition[1].productInProduct?.quantity},
                            "unit": "${productResponse.productComposition[1].productInProduct?.unit}",
                            "productInProductPrice": ${productResponse.productComposition[1].productInProduct?.productInProductPrice},
                            "productInProductFlagshipPrice": ${productResponse.productComposition[1].productInProduct?.productInProductFlagshipPrice}
                        }
                    }
                ]
            }
            """
            )
        }

        verifySequence {
            adminGetProductQueryService(AdminGetProductQuery(productId = productId))
        }
    }

    @Test
    fun `test searchProducts, should serialize and deserialize correctly`() {
        val productCategory1 = AdminSearchProductsResponse.ProductCategoryResponse(
            id = 1.toUUID(),
            type = ProductCategoryType.PRODUCT,
            title = "Beverages"
        )

        val product1 = AdminSearchProductsResponse(
            id = 2.toUUID(),
            code = "P1001",
            title = "Coca Cola 0.33l",
            type = ProductType.PRODUCT,
            order = 1,
            price = BigDecimal("1.50"),
            flagshipPrice = BigDecimal("2.50"),
            active = true,
            stockQuantity = BigDecimal("100"),
            isProductComponent = true,
            discountAmount = BigDecimal("0.10"),
            discountPercentage = 5,
            createdAt = LocalDateTime.parse("2023-10-30T12:34:56"),
            updatedAt = LocalDateTime.parse("2023-11-30T12:34:56"),
            productCategory = productCategory1,
            imageFile = AdminSearchProductsResponse.ImageFileResponse(
                id = 10.toUUID(),
                url = "localhost:8080/files/${10.toUUID()}"
            )
        )

        val productCategory2 = AdminSearchProductsResponse.ProductCategoryResponse(
            id = 3.toUUID(),
            type = ProductCategoryType.DISCOUNT,
            title = "Discounts"
        )

        val product2 = AdminSearchProductsResponse(
            id = 4.toUUID(),
            code = "P2002",
            title = "Special Discount",
            type = ProductType.PRODUCT,
            order = null,
            price = BigDecimal("0.00"),
            flagshipPrice = null,
            active = false,
            stockQuantity = 0.toBigDecimal(),
            isProductComponent = false,
            discountAmount = null,
            discountPercentage = null,
            createdAt = LocalDateTime.parse("2023-12-01T08:00:00"),
            updatedAt = LocalDateTime.parse("2023-12-01T08:00:00"),
            productCategory = productCategory2,
            imageFile = null
        )

        every { adminSearchProductsQueryService(any()) } returns PageImpl(listOf(product1, product2))

        mvc.post(SEARCH_PRODUCT_PATH) {
            header(HttpHeaders.ACCEPT, ApiVersion.VERSION_1_JSON)
            header(HttpHeaders.AUTHORIZATION, mockAccessToken(EmployeeRole.MANAGER))
            contentType = MediaType.APPLICATION_JSON
            content = """
                {
                  "title": "Product",
                  "active": true,
                  "categoryIds": []
                }
            """.trimIndent()
        }.andExpect {
            status { isOk() }
            jsonContent(
                """
                    {
                        "content": [
                            {
                                "id": "${product1.id}",
                                "code": "${product1.code}",
                                "title": "${product1.title}",
                                "type": "${product1.type}",
                                "order": ${product1.order},
                                "price": ${product1.price},
                                "flagshipPrice": ${product1.flagshipPrice},
                                "active": ${product1.active},
                                "stockQuantity": ${product1.stockQuantity},
                                "isProductComponent": ${product1.isProductComponent},
                                "discountAmount": ${product1.discountAmount},
                                "discountPercentage": ${product1.discountPercentage},
                                "createdAt": "${product1.createdAt.truncatedAndFormatted()}",
                                "updatedAt": "${product1.updatedAt.truncatedAndFormatted()}",
                                "productCategory": {
                                    "id": "${product1.productCategory.id}",
                                    "type": "${product1.productCategory.type}",
                                    "title": "${product1.productCategory.title}"
                                },
                                "imageFile": {
                                    "id": "${product1.imageFile!!.id}",
                                    "url": "${product1.imageFile!!.url}"
                                }
                            },
                            {
                                "id": "${product2.id}",
                                "code": "${product2.code}",
                                "title": "${product2.title}",
                                "type": "${product2.type}",
                                "order": null,
                                "price": ${product2.price},
                                "flagshipPrice": ${product2.flagshipPrice},
                                "active": ${product2.active},
                                "stockQuantity": 0,
                                "isProductComponent": ${product2.isProductComponent},
                                "discountAmount": null,
                                "discountPercentage": null,
                                "createdAt": "${product2.createdAt.truncatedAndFormatted()}",
                                "updatedAt": "${product2.updatedAt.truncatedAndFormatted()}",
                                "productCategory": {
                                    "id": "${product2.productCategory.id}",
                                    "type": "${product2.productCategory.type}",
                                    "title": "${product2.productCategory.title}"
                                },
                                "imageFile": null
                            }
                        ],
                        "totalElements": 2,
                        "totalPages": 1
                    }
                """.trimIndent()
            )
        }

        verify {
            adminSearchProductsQueryService(
                AdminSearchProductsQuery(
                    filter = AdminSearchProductsFilter(
                        title = "Product",
                        active = true,
                        categoryIds = setOf()
                    ),
                    pageable = PageRequest.of(0, 10, Sort.by(ProductColumnNames.CODE))
                )
            )
        }
    }

    @RetryingTest(5)
    fun `test exportProductMarginRates, should call service and return excel file`() {
        val exportFormat = ExportFormat.XLSX
        val username = "anonymous"
        val byteArray = ByteArray(1024)
        val inputStream = ByteArrayInputStream(byteArray)
        val exportResult = ExportResultModel(inputStream, byteArray.size.toLong())
        val dateFrom = "2023-01-01"
        val dateTo = "2023-12-31"

        val filter = AdminExportProductMarginRatesFilter(
            dateFrom = LocalDate.parse(dateFrom),
            dateTo = LocalDate.parse(dateTo)
        )

        every {
            adminProductMarginRatesExportService.exportMarginRates(any())
        } returns exportResult

        mvc.post(EXPORT_PRODUCT_MARGIN_RATES_PATH(exportFormat)) {
            header(HttpHeaders.ACCEPT, ApiVersion.VERSION_1_XLSX)
            header(HttpHeaders.AUTHORIZATION, mockAccessToken(EmployeeRole.MANAGER))
            contentType = MediaType.APPLICATION_JSON
            content = """
                {
                  "dateFrom": "$dateFrom",
                  "dateTo": "$dateTo"
                }
            """.trimIndent()
        }.andExpect {
            status { isOk() }
            header {
                string(HttpHeaders.CONTENT_TYPE, ApiVersion.VERSION_1_XLSX)
                string(HttpHeaders.CONTENT_DISPOSITION, containsString("attachment"))
                longValue(HttpHeaders.CONTENT_LENGTH, byteArray.size.toLong())
            }
        }

        verifySequence {
            adminProductMarginRatesExportService.exportMarginRates(
                AdminExportProductMarginRatesQuery(
                    filter = filter,
                    exportFormat = exportFormat,
                    username = username
                )
            )
        }
    }
}

private const val BASE_PRODUCT_PATH = "/manager-app/products"
private val GET_AND_UPDATE_AND_DELETE_PRODUCT_PATH: (UUID) -> String =
    { productId: UUID -> "$BASE_PRODUCT_PATH/$productId" }
private const val SEARCH_PRODUCT_PATH = "$BASE_PRODUCT_PATH/search"
private val EXPORT_PRODUCT_MARGIN_RATES_PATH: (ExportFormat) -> String = { exportFormat ->
    "$BASE_PRODUCT_PATH/margin-rates/export/$exportFormat"
}
