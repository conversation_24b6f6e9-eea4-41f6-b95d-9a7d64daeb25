package com.cleevio.cinemax.api.module.branch.service

import com.cleevio.cinemax.api.MssqlIntegrationTest
import com.cleevio.cinemax.api.module.branch.constant.BranchType
import com.cleevio.cinemax.api.module.branch.entity.Branch
import com.cleevio.cinemax.api.module.branch.service.command.CreateOrUpdateBranchCommand
import io.mockk.Called
import io.mockk.Runs
import io.mockk.every
import io.mockk.just
import io.mockk.verify
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.test.context.jdbc.Sql
import org.springframework.test.context.jdbc.SqlConfig
import org.springframework.test.context.jdbc.SqlGroup
import kotlin.test.assertEquals
import kotlin.test.assertTrue

@SqlGroup(
    Sql(
        scripts = [
            "/db/mssql-cinemax/test/init_mssql_branch.sql"
        ],
        config = SqlConfig(
            dataSource = "mssqlCinemaxDataSource",
            transactionManager = "mssqlCinemaxTransactionManager"
        )
    ),
    Sql(
        scripts = [
            "/db/mssql-cinemax/test/drop_mssql_branch.sql"
        ],
        config = SqlConfig(
            dataSource = "mssqlCinemaxDataSource",
            transactionManager = "mssqlCinemaxTransactionManager"
        ),
        executionPhase = Sql.ExecutionPhase.AFTER_TEST_METHOD
    )
)
class BranchMssqlSynchronizationServiceIT @Autowired constructor(
    private val underTest: BranchMssqlSynchronizationService,
    private val branchMssqlFinderRepository: BranchMssqlFinderRepository,
) : MssqlIntegrationTest() {

    @Test
    fun `test synchronizeAll - no PSQL branches, 4 MSSQL branches - should create 3`() {
        every { branchRepositoryMock.findAll() } returns emptyList()
        every { branchRepositoryMock.existsByProductSalesCodeAndOriginalIdNot(any(), any()) } returns false
        every { branchServiceMock.syncCreateOrUpdateBranch(any()) } just Runs

        underTest.synchronizeAll()
        val commandCaptor = mutableListOf<CreateOrUpdateBranchCommand>()
        val codeCaptor = mutableListOf<Int>()
        val originalIdCaptor = mutableListOf<Int>()

        verify {
            branchRepositoryMock.findAll()
            branchRepositoryMock.existsByProductSalesCodeAndOriginalIdNot(capture(codeCaptor), capture(originalIdCaptor))
            branchServiceMock.syncCreateOrUpdateBranch(capture(commandCaptor))
        }

        assertEquals(4, branchMssqlFinderRepository.findAll().size)
        assertEquals(3, commandCaptor.size)
        assertEquals(EXPECTED_COMMAND_1, commandCaptor[0])
        assertEquals(EXPECTED_COMMAND_2, commandCaptor[1])
        assertEquals(EXPECTED_COMMAND_3, commandCaptor[2])

        assertEquals(3, codeCaptor.size)
        assertTrue(codeCaptor.containsAll(listOf(EXPECTED_COMMAND_1.productSalesCode, EXPECTED_COMMAND_2.productSalesCode, EXPECTED_COMMAND_3.productSalesCode)))
        assertEquals(3, originalIdCaptor.size)
        assertTrue(originalIdCaptor.containsAll(listOf(EXPECTED_COMMAND_1.originalId, EXPECTED_COMMAND_2.originalId, EXPECTED_COMMAND_3.originalId)))
    }

    @Test
    fun `test synchronizeAll - 2 PSQL branches, 3 MSSQL branches - should create 1 branch`() {
        every { branchRepositoryMock.findAll() } returns listOf(
            BRANCH_1,
            BRANCH_2
        )
        every { branchRepositoryMock.existsByProductSalesCodeAndOriginalIdNot(any(), any()) } returns false
        every { branchServiceMock.syncCreateOrUpdateBranch(any()) } just Runs

        underTest.synchronizeAll()
        val commandCaptor = mutableListOf<CreateOrUpdateBranchCommand>()
        val codeCaptor = mutableListOf<Int>()
        val originalIdCaptor = mutableListOf<Int>()

        verify {
            branchRepositoryMock.findAll()
            branchRepositoryMock.existsByProductSalesCodeAndOriginalIdNot(capture(codeCaptor), capture(originalIdCaptor))
            branchServiceMock.syncCreateOrUpdateBranch(capture(commandCaptor))
        }

        assertEquals(1, commandCaptor.size)
        assertEquals(EXPECTED_COMMAND_3, commandCaptor[0])

        assertEquals(1, codeCaptor.size)
        assertEquals(EXPECTED_COMMAND_3.productSalesCode, codeCaptor[0])
        assertEquals(1, originalIdCaptor.size)
        assertEquals(EXPECTED_COMMAND_3.originalId, originalIdCaptor[0])
    }

    @Test
    fun `test synchronizeAll - all records exists - one to update - should update one, skip the rest`() {
        val branchWithDifferentAttribute = Branch(
            originalId = BRANCH_3.originalId,
            productSalesCode = BRANCH_3.productSalesCode,
            code = BRANCH_3.code,
            auditoriumOriginalCodePrefix = "5432",
            name = "CINEMAX SKALICA",
            type = BranchType.REGULAR
        )
        every { branchRepositoryMock.findAll() } returns listOf(
            BRANCH_1,
            BRANCH_2,
            branchWithDifferentAttribute
        )
        every { branchRepositoryMock.existsByProductSalesCodeAndOriginalIdNot(any(), any()) } returns false
        every { branchServiceMock.syncCreateOrUpdateBranch(any()) } just Runs

        underTest.synchronizeAll()
        val commandCaptor = mutableListOf<CreateOrUpdateBranchCommand>()
        val codeCaptor = mutableListOf<Int>()
        val originalIdCaptor = mutableListOf<Int>()

        verify {
            branchRepositoryMock.findAll()
            branchRepositoryMock.existsByProductSalesCodeAndOriginalIdNot(capture(codeCaptor), capture(originalIdCaptor))
            branchServiceMock.syncCreateOrUpdateBranch(capture(commandCaptor))
        }

        assertEquals(1, commandCaptor.size)
        assertEquals(EXPECTED_COMMAND_3, commandCaptor[0])

        assertEquals(1, codeCaptor.size)
        assertEquals(EXPECTED_COMMAND_3.productSalesCode, codeCaptor[0])
        assertEquals(1, originalIdCaptor.size)
        assertEquals(EXPECTED_COMMAND_3.originalId, originalIdCaptor[0])
    }

    @Test
    fun `test synchronizeAll - 2 PSQL branches, 3 MSSQL branches - should create no branch because code already exists`() {
        every { branchRepositoryMock.findAll() } returns listOf(
            BRANCH_1,
            BRANCH_2
        )
        every { branchRepositoryMock.existsByProductSalesCodeAndOriginalIdNot(any(), any()) } returns true
        every { branchServiceMock.syncCreateOrUpdateBranch(any()) } just Runs

        underTest.synchronizeAll()
        val codeCaptor = mutableListOf<Int>()
        val originalIdCaptor = mutableListOf<Int>()

        verify {
            branchRepositoryMock.findAll()
            branchRepositoryMock.existsByProductSalesCodeAndOriginalIdNot(capture(codeCaptor), capture(originalIdCaptor))
            branchServiceMock wasNot Called
        }

        assertEquals(1, codeCaptor.size)
        assertEquals(EXPECTED_COMMAND_3.productSalesCode, codeCaptor[0])
        assertEquals(1, originalIdCaptor.size)
        assertEquals(EXPECTED_COMMAND_3.originalId, originalIdCaptor[0])
    }
}

private val EXPECTED_COMMAND_1 = CreateOrUpdateBranchCommand(
    id = null,
    originalId = 1,
    productSalesCode = 1,
    code = "522901",
    auditoriumOriginalCodePrefix = "5226",
    name = "CINEMAX DUNAJSKÁ STREDA"
)
private val EXPECTED_COMMAND_2 = CreateOrUpdateBranchCommand(
    id = null,
    originalId = 2,
    productSalesCode = 2,
    code = "543029",
    auditoriumOriginalCodePrefix = "5430",
    name = "CINEMAX KOŠICE"
)
private val EXPECTED_COMMAND_3 = CreateOrUpdateBranchCommand(
    id = null,
    originalId = 3,
    productSalesCode = 3,
    code = "523905",
    auditoriumOriginalCodePrefix = "5237",
    name = "CINEMAX NITRA"
)
private val BRANCH_1 = Branch(
    originalId = 1,
    productSalesCode = 1,
    code = "522901",
    auditoriumOriginalCodePrefix = "5226",
    name = "CINEMAX DUNAJSKÁ STREDA",
    type = BranchType.REGULAR
)
private val BRANCH_2 = Branch(
    originalId = 2,
    productSalesCode = 2,
    code = "543029",
    auditoriumOriginalCodePrefix = "5430",
    name = "CINEMAX KOŠICE",
    type = BranchType.REGULAR
)
private val BRANCH_3 = Branch(
    originalId = 3,
    productSalesCode = 3,
    code = "523905",
    auditoriumOriginalCodePrefix = "5237",
    name = "CINEMAX NITRA",
    type = BranchType.REGULAR
)
