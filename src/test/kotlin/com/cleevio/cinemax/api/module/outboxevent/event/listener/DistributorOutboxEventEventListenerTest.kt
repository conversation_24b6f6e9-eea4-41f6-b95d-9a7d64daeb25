package com.cleevio.cinemax.api.module.outboxevent.event.listener

import com.cleevio.cinemax.api.module.distributor.event.DistributorCreatedOrUpdatedEvent
import com.cleevio.cinemax.api.module.distributor.event.DistributorDeletedEvent
import com.cleevio.cinemax.api.module.outboxevent.constant.OutboxEventType
import com.cleevio.cinemax.api.module.outboxevent.service.OutboxEventService
import com.cleevio.cinemax.api.module.outboxevent.service.command.CreateOutboxEventCommand
import io.mockk.every
import io.mockk.just
import io.mockk.mockk
import io.mockk.runs
import io.mockk.verify
import org.junit.jupiter.api.Test
import java.util.UUID

class DistributorOutboxEventEventListenerTest {

    private val outboxEventService = mockk<OutboxEventService>()
    private val underTest = DistributorOutboxEventEventListener(outboxEventService)

    @Test
    fun `listen to distributor deleted event - should correctly handle DistributorDeletedEvent`() {
        every { outboxEventService.createOutboxEvent(any()) } just runs

        val distributorId = UUID.fromString("65aa8e3f-4b86-4eb3-b2e9-5b1482021eda")
        underTest.listenToDistributorDeletedEvent(
            DistributorDeletedEvent(distributorId)
        )

        verify {
            outboxEventService.createOutboxEvent(
                CreateOutboxEventCommand(
                    entityId = distributorId,
                    type = OutboxEventType.DISTRIBUTOR_DELETED
                )
            )
        }
    }

    @Test
    fun `listen to distributor created or updated event - should correctly handle DistributorCreatedOrUpdatedEvent`() {
        every { outboxEventService.createOutboxEvent(any()) } just runs

        val distributorId = UUID.fromString("65aa8e3f-4b86-4eb3-b2e9-5b1482021eda")
        underTest.listenToDistributorCreatedOrUpdatedEvent(
            DistributorCreatedOrUpdatedEvent(distributorId)
        )

        verify {
            outboxEventService.createOutboxEvent(
                CreateOutboxEventCommand(
                    entityId = distributorId,
                    type = OutboxEventType.DISTRIBUTOR_CREATED_OR_UPDATED
                )
            )
        }
    }
}
