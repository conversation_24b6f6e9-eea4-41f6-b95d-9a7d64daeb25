package com.cleevio.cinemax.api.module.table.util

import com.cleevio.cinemax.mssql.rp_bufete.dbo.tables.pojos.Stoly
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.Arguments
import org.junit.jupiter.params.provider.MethodSource
import java.time.LocalDateTime
import java.util.stream.Stream
import kotlin.test.assertEquals

class TableAttributeParserTest {

    @ParameterizedTest
    @MethodSource("mssqlTablesProvider")
    fun `test parseLabelAndOrder, should decode correctly`(
        mssqlTable: Stoly,
        expectedResult: Pair<String, Int?>,
    ) {
        assertEquals(expectedResult, parseLabelAndOrder(mssqlTable))
    }

    companion object {
        @JvmStatic
        fun mssqlTablesProvider(): Stream<Arguments> {
            return Stream.of(
                Arguments.of(
                    Stoly(1, "Stol 1", "DISdata", LocalDateTime.now(), "", "", false, false, false, false),
                    Pair("1", 1)
                ),
                Arguments.of(
                    Stoly(2, "Stol 1", "DISdata", LocalDateTime.now(), "", null, false, false, false, false),
                    Pair("1", 1)
                ),
                Arguments.of(
                    Stoly(3, "Stol 5", "DISdata", LocalDateTime.now(), "", "", false, false, false, false),
                    Pair("5", 5)
                ),
                Arguments.of(
                    Stoly(4, "Stůl 10", "DISdata", LocalDateTime.now(), "", "", false, false, false, false),
                    Pair("10", 10)
                ),
                Arguments.of(
                    Stoly(5, "Table A", "DISdata", LocalDateTime.now(), "", "", false, false, false, false),
                    Pair("A", null)
                ),
                Arguments.of(
                    Stoly(6, "E14, 1211", "DISdata", LocalDateTime.now(), "", "10.1.255.137", false, false, false, false),
                    Pair("E14, 1211", null)
                ),
                Arguments.of(
                    Stoly(7, "Buffet", "DISdata", LocalDateTime.now(), "", "", false, false, false, false),
                    Pair("B", null)
                ),
                Arguments.of(
                    Stoly(8, null, "DISdata", LocalDateTime.now(), "", "", false, false, false, false),
                    Pair("N/A", null)
                )
            )
        }
    }
}
