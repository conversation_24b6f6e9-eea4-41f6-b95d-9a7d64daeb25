package com.cleevio.cinemax.api.module.receipt.event.listener

import com.cleevio.cinemax.api.module.basket.constant.BasketState
import com.cleevio.cinemax.api.module.basket.service.BasketJpaFinderService
import com.cleevio.cinemax.api.module.outboxevent.event.BasketItemsCancelledEvent
import com.cleevio.cinemax.api.module.outboxevent.event.BasketItemsCancelledWithoutReceiptPrintEvent
import com.cleevio.cinemax.api.module.posconfiguration.service.PosConfigurationJooqFinderService
import com.cleevio.cinemax.api.module.receipt.service.SvkXmlReceiptService
import com.cleevio.cinemax.api.module.receipt.service.command.GenerateAndPersistCancellationReceiptCommand
import com.cleevio.cinemax.api.util.createBasket
import com.cleevio.cinemax.api.util.createPosConfiguration
import com.cleevio.cinemax.api.util.createReceipt
import io.mockk.every
import io.mockk.just
import io.mockk.mockk
import io.mockk.runs
import io.mockk.verify
import io.mockk.verifyOrder
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import org.springframework.context.ApplicationEventPublisher
import java.util.UUID

class BasketItemReceiptEventListenerTest {

    private val basketJpaFinderService = mockk<BasketJpaFinderService>()
    private val posConfigurationJooqFinderService = mockk<PosConfigurationJooqFinderService>()
    private val svkXmlReceiptService = mockk<SvkXmlReceiptService>()
    private val applicationEventPublisher = mockk<ApplicationEventPublisher>()
    private val underTest = BasketItemReceiptEventListener(
        basketJpaFinderService,
        posConfigurationJooqFinderService,
        svkXmlReceiptService,
        applicationEventPublisher
    )

    @Test
    fun `test listenToBasketItemsCancelledEvent - two baskets, single POS - should correctly handle event`() {
        every { basketJpaFinderService.findAllNonDeletedByIdIn(any()) } returns listOf(BASKET_1, BASKET_2)
        every { posConfigurationJooqFinderService.findAll() } returns listOf(POS_CONFIGURATION_1, POS_CONFIGURATION_2)
        every { svkXmlReceiptService.generateAndPersistCancellationReceipt(any()) } returns RECEIPT

        underTest.listenToBasketItemsCancelledEvent(
            BasketItemsCancelledEvent(
                basketIds = setOf(BASKET_1.id, BASKET_2.id),
                printReceipt = true
            )
        )

        verifyOrder {
            basketJpaFinderService.findAllNonDeletedByIdIn(setOf(BASKET_1.id, BASKET_2.id))
            posConfigurationJooqFinderService.findAll()
            svkXmlReceiptService.generateAndPersistCancellationReceipt(
                GenerateAndPersistCancellationReceiptCommand(
                    basketId = BASKET_1.id,
                    receiptsDirectory = POS_CONFIGURATION_1.receiptsDirectory
                )
            )
            svkXmlReceiptService.generateAndPersistCancellationReceipt(
                GenerateAndPersistCancellationReceiptCommand(
                    basketId = BASKET_2.id,
                    receiptsDirectory = POS_CONFIGURATION_1.receiptsDirectory
                )
            )
        }
    }

    @Test
    fun `test listenToBasketItemsCancelledEvent - two baskets, two POSs - should correctly handle event`() {
        every { basketJpaFinderService.findAllNonDeletedByIdIn(any()) } returns listOf(BASKET_1, BASKET_3)
        every { posConfigurationJooqFinderService.findAll() } returns listOf(POS_CONFIGURATION_1, POS_CONFIGURATION_2)
        every { svkXmlReceiptService.generateAndPersistCancellationReceipt(any()) } returns RECEIPT

        underTest.listenToBasketItemsCancelledEvent(
            BasketItemsCancelledEvent(
                basketIds = setOf(BASKET_1.id, BASKET_3.id),
                printReceipt = true
            )
        )

        verifyOrder {
            basketJpaFinderService.findAllNonDeletedByIdIn(setOf(BASKET_1.id, BASKET_3.id))
            posConfigurationJooqFinderService.findAll()
            svkXmlReceiptService.generateAndPersistCancellationReceipt(
                GenerateAndPersistCancellationReceiptCommand(
                    basketId = BASKET_1.id,
                    receiptsDirectory = POS_CONFIGURATION_1.receiptsDirectory
                )
            )
            svkXmlReceiptService.generateAndPersistCancellationReceipt(
                GenerateAndPersistCancellationReceiptCommand(
                    basketId = BASKET_3.id,
                    receiptsDirectory = POS_CONFIGURATION_2.receiptsDirectory
                )
            )
        }
    }

    @Test
    fun `test listenToBasketItemsCancelledEvent - two baskets, three POSs - should correctly handle event`() {
        every { basketJpaFinderService.findAllNonDeletedByIdIn(any()) } returns listOf(BASKET_1, BASKET_2, BASKET_3)
        every { posConfigurationJooqFinderService.findAll() } returns listOf(POS_CONFIGURATION_1, POS_CONFIGURATION_2)
        every { svkXmlReceiptService.generateAndPersistCancellationReceipt(any()) } returns RECEIPT

        underTest.listenToBasketItemsCancelledEvent(
            BasketItemsCancelledEvent(
                basketIds = setOf(BASKET_1.id, BASKET_2.id, BASKET_3.id),
                printReceipt = true
            )
        )

        verifyOrder {
            basketJpaFinderService.findAllNonDeletedByIdIn(setOf(BASKET_1.id, BASKET_2.id, BASKET_3.id))
            posConfigurationJooqFinderService.findAll()
            svkXmlReceiptService.generateAndPersistCancellationReceipt(
                GenerateAndPersistCancellationReceiptCommand(
                    basketId = BASKET_1.id,
                    receiptsDirectory = POS_CONFIGURATION_1.receiptsDirectory
                )
            )
            svkXmlReceiptService.generateAndPersistCancellationReceipt(
                GenerateAndPersistCancellationReceiptCommand(
                    basketId = BASKET_2.id,
                    receiptsDirectory = POS_CONFIGURATION_1.receiptsDirectory
                )
            )
            svkXmlReceiptService.generateAndPersistCancellationReceipt(
                GenerateAndPersistCancellationReceiptCommand(
                    basketId = BASKET_3.id,
                    receiptsDirectory = POS_CONFIGURATION_2.receiptsDirectory
                )
            )
        }
    }

    @Test
    fun `test listenToBasketItemsCancelledEvent - basket payment at unknown POS - should throw`() {
        every { basketJpaFinderService.findAllNonDeletedByIdIn(any()) } returns listOf(BASKET_4)
        every { posConfigurationJooqFinderService.findAll() } returns listOf(POS_CONFIGURATION_1, POS_CONFIGURATION_2)
        every { svkXmlReceiptService.generateAndPersistCancellationReceipt(any()) } returns RECEIPT

        assertThrows<IllegalStateException> {
            underTest.listenToBasketItemsCancelledEvent(
                BasketItemsCancelledEvent(
                    basketIds = setOf(BASKET_4.id),
                    printReceipt = true
                )
            )
        }
    }

    @Test
    fun `test listenToBasketItemsCancelledEvent - basket in invalid state - should throw`() {
        every { basketJpaFinderService.findAllNonDeletedByIdIn(any()) } returns listOf(BASKET_1, BASKET_5)
        every { posConfigurationJooqFinderService.findAll() } returns listOf(POS_CONFIGURATION_1, POS_CONFIGURATION_2)
        every { svkXmlReceiptService.generateAndPersistCancellationReceipt(any()) } returns RECEIPT

        assertThrows<IllegalStateException> {
            underTest.listenToBasketItemsCancelledEvent(
                BasketItemsCancelledEvent(
                    basketIds = setOf(BASKET_1.id, BASKET_5.id),
                    printReceipt = true
                )
            )
        }
    }

    @Test
    fun `test listenToBasketItemsCancelledEvent - printReceipt is false - should publish events without generating receipts`() {
        every { basketJpaFinderService.findAllNonDeletedByIdIn(any()) } returns emptyList()
        every { applicationEventPublisher.publishEvent(any<BasketItemsCancelledWithoutReceiptPrintEvent>()) } just runs

        underTest.listenToBasketItemsCancelledEvent(
            BasketItemsCancelledEvent(
                basketIds = setOf(BASKET_1.id, BASKET_2.id),
                printReceipt = false
            )
        )

        verifyOrder {
            applicationEventPublisher.publishEvent(
                BasketItemsCancelledWithoutReceiptPrintEvent(
                    basketId = BASKET_1.id
                )
            )
            applicationEventPublisher.publishEvent(
                BasketItemsCancelledWithoutReceiptPrintEvent(
                    basketId = BASKET_2.id
                )
            )
        }

        verify(exactly = 0) {
            basketJpaFinderService.findAllNonDeletedByIdIn(any())
            posConfigurationJooqFinderService.findAll()
            svkXmlReceiptService.generateAndPersistCancellationReceipt(any())
        }
    }

    @Test
    fun `test listenToBasketItemsCancelledEvent - posConfigurationId is provided - should use provided configuration`() {
        every { basketJpaFinderService.findAllNonDeletedByIdIn(any()) } returns listOf(BASKET_1, BASKET_2)
        every { posConfigurationJooqFinderService.findAll() } returns listOf(POS_CONFIGURATION_1, POS_CONFIGURATION_2)
        every { svkXmlReceiptService.generateAndPersistCancellationReceipt(any()) } returns RECEIPT

        underTest.listenToBasketItemsCancelledEvent(
            BasketItemsCancelledEvent(
                basketIds = setOf(BASKET_1.id, BASKET_2.id),
                printReceipt = true,
                posConfigurationId = POS_CONFIGURATION_2.id
            )
        )

        verifyOrder {
            basketJpaFinderService.findAllNonDeletedByIdIn(setOf(BASKET_1.id, BASKET_2.id))
            posConfigurationJooqFinderService.findAll()
            svkXmlReceiptService.generateAndPersistCancellationReceipt(
                GenerateAndPersistCancellationReceiptCommand(
                    basketId = BASKET_1.id,
                    receiptsDirectory = POS_CONFIGURATION_2.receiptsDirectory
                )
            )
            svkXmlReceiptService.generateAndPersistCancellationReceipt(
                GenerateAndPersistCancellationReceiptCommand(
                    basketId = BASKET_2.id,
                    receiptsDirectory = POS_CONFIGURATION_2.receiptsDirectory
                )
            )
        }
    }
}

private val POS_CONFIGURATION_1 = createPosConfiguration()
private val POS_CONFIGURATION_2 = createPosConfiguration(
    title = "POS 2 config",
    receiptsDirectory = "/pos2/receipts"
)
private val BASKET_1 = createBasket(
    state = BasketState.PAID,
    paymentPosConfigurationId = POS_CONFIGURATION_1.id
)
private val BASKET_2 = createBasket(
    state = BasketState.PAID,
    paymentPosConfigurationId = POS_CONFIGURATION_1.id
)
private val BASKET_3 = createBasket(
    state = BasketState.PAID_ONLINE,
    paymentPosConfigurationId = POS_CONFIGURATION_2.id
)
private val BASKET_4 = createBasket(
    state = BasketState.PAID,
    paymentPosConfigurationId = UUID.randomUUID()
)
private val BASKET_5 = createBasket(
    state = BasketState.PAYMENT_IN_PROGRESS,
    paymentPosConfigurationId = POS_CONFIGURATION_2.id
)
private val RECEIPT = createReceipt()
