package com.cleevio.cinemax.api.module.distributor.event

import com.cleevio.cinemax.api.common.util.MESSAGING_MAPPER
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertNotNull
import org.junit.jupiter.api.Test
import java.util.Optional

class AdminDistributorCreatedOrUpdatedEventTest {

    @Test
    fun `test toMessagePayload - all event fields with values - should create message payload with correct type and serialization`() {
        val event = AdminDistributorCreatedOrUpdatedEvent(
            code = "1234",
            title = "Best movies ever, s.r.o.",
            disfilmCode = Optional.of("5678"),
            addressStreet = Optional.of("Filmová 58"),
            addressCity = Optional.of("Praha"),
            addressPostCode = Optional.of("150 00"),
            contactName1 = Optional.of("John"),
            contactName2 = Optional.of("<PERSON>"),
            contactName3 = Optional.of("<PERSON>"),
            contactPhone1 = Optional.of("+************"),
            contactPhone2 = Optional.of("+************"),
            contactPhone3 = Optional.of("*********"),
            contactEmails = Optional.of(setOf("<EMAIL>", "<EMAIL>")),
            bankName = Optional.of("AirBank, a.s."),
            bankAccount = Optional.of("**********/0300"),
            idNumber = Optional.of("********"),
            taxIdNumber = Optional.of("SK2022916731"),
            vatRate = Optional.of(20),
            note = Optional.of("Best note ever")
        )
        val expectedJson = """
        {
            "code": "1234",
            "title": "Best movies ever, s.r.o.",
            "disfilmCode": "5678",
            "addressStreet": "Filmová 58",
            "addressCity": "Praha",
            "addressPostCode": "150 00",
            "contactName1": "John",
            "contactName2": "Jerry",
            "contactName3": "Tom",
            "contactPhone1": "+************",
            "contactPhone2": "+************",
            "contactPhone3": "*********",
            "contactEmails": ["<EMAIL>", "<EMAIL>"],
            "bankName": "AirBank, a.s.",
            "bankAccount": "**********/0300",
            "idNumber": "********",
            "taxIdNumber": "SK2022916731",
            "vatRate": 20,
            "note": "Best note ever"
        }
        """.trimIndent()

        val messagePayload = event.toMessagePayload()

        assertNotNull(messagePayload.id)
        assertNotNull(messagePayload.createdAt)
        assertEquals("DISTRIBUTOR_CREATED_OR_UPDATED", messagePayload.type.name)
        assertEquals(
            MESSAGING_MAPPER.readTree(expectedJson),
            MESSAGING_MAPPER.readTree(messagePayload.data)
        )
    }

    @Test
    fun `test toMessagePayload - required event fields present and all optional empty - should create message payload with correct type and serialization`() {
        val event = AdminDistributorCreatedOrUpdatedEvent(
            code = "1234",
            title = "Best movies ever, s.r.o.",
            disfilmCode = Optional.empty(),
            addressStreet = Optional.empty(),
            addressCity = Optional.empty(),
            addressPostCode = Optional.empty(),
            contactName1 = Optional.empty(),
            contactName2 = Optional.empty(),
            contactName3 = Optional.empty(),
            contactPhone1 = Optional.empty(),
            contactPhone2 = Optional.empty(),
            contactPhone3 = Optional.empty(),
            contactEmails = Optional.empty(),
            bankName = Optional.empty(),
            bankAccount = Optional.empty(),
            idNumber = Optional.empty(),
            taxIdNumber = Optional.empty(),
            vatRate = Optional.empty(),
            note = Optional.empty()
        )
        val expectedJson = """
        {
            "code": "1234",
            "title": "Best movies ever, s.r.o.",
            "disfilmCode": null,
            "addressStreet": null,
            "addressCity": null,
            "addressPostCode": null,
            "contactName1": null,
            "contactName2": null,
            "contactName3": null,
            "contactPhone1": null,
            "contactPhone2": null,
            "contactPhone3": null,
            "contactEmails": null,
            "bankName": null,
            "bankAccount": null,
            "idNumber": null,
            "taxIdNumber": null,
            "vatRate": null,
            "note": null
        }
        """.trimIndent()

        val messagePayload = event.toMessagePayload()

        assertNotNull(messagePayload.id)
        assertNotNull(messagePayload.createdAt)
        assertEquals("DISTRIBUTOR_CREATED_OR_UPDATED", messagePayload.type.name)
        assertEquals(
            MESSAGING_MAPPER.readTree(expectedJson),
            MESSAGING_MAPPER.readTree(messagePayload.data)
        )
    }

    @Test
    fun `test toMessagePayload - required event fields all other are null - should create message payload with correct type and serialization`() {
        val event = AdminDistributorCreatedOrUpdatedEvent(
            code = "1234",
            title = "Best movies ever, s.r.o."
        )
        val expectedJson = """
            {
                "code": "1234",
                "title": "Best movies ever, s.r.o."
            }
        """.trimIndent()

        val messagePayload = event.toMessagePayload()

        assertNotNull(messagePayload.id)
        assertNotNull(messagePayload.createdAt)
        assertEquals("DISTRIBUTOR_CREATED_OR_UPDATED", messagePayload.type.name)
        assertEquals(
            MESSAGING_MAPPER.readTree(expectedJson),
            MESSAGING_MAPPER.readTree(messagePayload.data)
        )
    }
}
