package com.cleevio.cinemax.api.module.jso.service

import com.cleevio.cinemax.api.DatabaseCleanup
import com.cleevio.cinemax.api.IntegrationTest
import com.cleevio.cinemax.api.module.jso.entity.Jso
import com.cleevio.cinemax.api.util.assertDescriptorEquals
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.test.context.jdbc.Sql
import org.springframework.test.context.jdbc.SqlGroup
import kotlin.test.assertEquals

@SqlGroup(
    Sql(
        scripts = [
            "/db/migration/V110__init_jso_table_with_data.sql"
        ]
    )
)
class JsoJooqFinderServiceIT @Autowired constructor(
    private val underTest: JsoJooqFinderService,
) : IntegrationTest(DatabaseCleanup.AFTER_EACH) {

    @Test
    fun `test findAll - should find all JSO values`() {
        val jsos = underTest.findAll().sortedBy { it.createdAt }

        assertEquals(7, jsos.size)
        assertDescriptorEquals(JSO_1, jsos[2])
        assertDescriptorEquals(JSO_2, jsos[3])
        assertDescriptorEquals(JSO_3, jsos[6])
    }
}

private val JSO_1 = Jso(
    originalId = 3,
    code = "S",
    title = "Strach"
)
private val JSO_2 = Jso(
    originalId = 4,
    code = "Z",
    title = "Závislosť"
)
private val JSO_3 = Jso(
    originalId = 7,
    code = "H",
    title = "Nahota"
)
