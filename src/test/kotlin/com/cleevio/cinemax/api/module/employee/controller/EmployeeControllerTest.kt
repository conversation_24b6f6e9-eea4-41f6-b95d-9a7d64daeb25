package com.cleevio.cinemax.api.module.employee.controller

import com.cleevio.cinemax.api.ControllerTest
import com.cleevio.cinemax.api.common.constant.ApiVersion
import com.cleevio.cinemax.api.common.constant.SourceApp
import com.cleevio.cinemax.api.common.util.jsonContent
import com.cleevio.cinemax.api.module.employee.constant.EmployeeRole
import com.cleevio.cinemax.api.module.employee.controller.dto.TokensResponse
import com.cleevio.cinemax.api.module.employee.exception.AuthenticationFailedException
import com.cleevio.cinemax.api.module.employee.service.command.GetSelfCommand
import com.cleevio.cinemax.api.module.employee.service.command.LoginEmployeeCommand
import com.cleevio.cinemax.api.module.employee.service.command.RefreshAccessTokenCommand
import com.cleevio.cinemax.api.module.employee.service.command.ResetPasswordCommand
import com.cleevio.cinemax.api.module.employee.service.model.TokensModel
import com.cleevio.cinemax.api.util.TEST_PRINCIPAL_USERNAME
import com.cleevio.cinemax.api.util.createEmployee
import com.cleevio.cinemax.api.util.mockAccessToken
import com.cleevio.cinemax.api.util.mockRefreshToken
import io.mockk.Called
import io.mockk.Runs
import io.mockk.every
import io.mockk.just
import io.mockk.verifySequence
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest
import org.springframework.http.HttpHeaders
import org.springframework.http.MediaType
import org.springframework.test.web.servlet.MockMvc
import org.springframework.test.web.servlet.get
import org.springframework.test.web.servlet.post
import org.springframework.test.web.servlet.put

@WebMvcTest(EmployeeController::class)
class EmployeeControllerTest @Autowired constructor(
    private val mvc: MockMvc,
) : ControllerTest() {

    @Test
    fun `test loginEmployee, missing accept header, should fall back to latest version content type`() {
        every { employeeService.loginEmployee(any()) } returns TOKENS_MODEL
        every { tokensResponseMapper.mapSingle(any()) } returns TOKENS_RESPONSE

        mvc.post(LOGIN_EMPLOYEE_PATH) {
            header(HttpHeaders.ACCEPT, ApiVersion.VERSION_1_JSON)
            contentType = MediaType.APPLICATION_JSON
            content = """
                {
                    "username": "${EMPLOYEE_1.username}",
                    "password": "$DUMMY_PASSWORD"
                }
            """.trimIndent()
        }.andExpect {
            content { contentType(ApiVersion.VERSION_1_JSON) }
            status { isOk() }
        }

        verifySequence {
            employeeService.loginEmployee(any())
            tokensResponseMapper.mapSingle(any())
        }
    }

    @Test
    fun `test loginEmployee, missing content type, should return 415`() {
        mvc.post(LOGIN_EMPLOYEE_PATH) {
            header(HttpHeaders.ACCEPT, ApiVersion.VERSION_1_JSON)
            content = """{}"""
        }.andExpect {
            status { isUnsupportedMediaType() }
        }
    }

    @Test
    fun `test loginEmployee, invalid content type, should return 415`() {
        mvc.post(LOGIN_EMPLOYEE_PATH) {
            header(HttpHeaders.ACCEPT, ApiVersion.VERSION_1_JSON)
            contentType = MediaType.APPLICATION_PDF
            content = """{}"""
        }.andExpect {
            status { isUnsupportedMediaType() }
        }
    }

    @Test
    fun `test loginEmployee, should serialize and deserialize correctly`() {
        every { employeeService.loginEmployee(any()) } returns TOKENS_MODEL
        every { tokensResponseMapper.mapSingle(any()) } returns TOKENS_RESPONSE

        mvc.post(LOGIN_EMPLOYEE_PATH) {
            header(HttpHeaders.ACCEPT, ApiVersion.VERSION_1_JSON)
            contentType = MediaType.APPLICATION_JSON
            content = """
                {
                    "username": "${EMPLOYEE_1.username}",
                    "password": "$DUMMY_PASSWORD"
                }
            """.trimIndent()
        }.andExpect {
            status { isOk() }
            content { contentType(ApiVersion.VERSION_1_JSON) }
            jsonContent(
                """
                {
                  "accessToken": "$ACCESS_TOKEN",
                  "accessExpiresIn": $ACCESS_EXPIRATION,
                  "refreshToken": "$REFRESH_TOKEN",
                  "refreshExpiresIn": $REFRESH_EXPIRATION
                }
            """
            )
        }

        verifySequence {
            employeeService.loginEmployee(
                LoginEmployeeCommand(
                    username = EMPLOYEE_1.username,
                    password = DUMMY_PASSWORD,
                    source = SourceApp.POS_APP
                )
            )
            tokensResponseMapper.mapSingle(TOKENS_MODEL)
        }
    }

    @Test
    fun `test loginEmployee - service throws exception - should return 401 unauthorized`() {
        every { employeeService.loginEmployee(any()) } throws AuthenticationFailedException()

        mvc.post(LOGIN_EMPLOYEE_PATH) {
            header(HttpHeaders.ACCEPT, ApiVersion.VERSION_1_JSON)
            contentType = MediaType.APPLICATION_JSON
            content = """
                {
                    "username": "${EMPLOYEE_1.username}",
                    "password": "$DUMMY_PASSWORD"
                }
            """.trimIndent()
        }.andExpect {
            status { isUnauthorized() }
            content { contentType(ApiVersion.VERSION_1_JSON) }
        }

        verifySequence {
            employeeService.loginEmployee(
                LoginEmployeeCommand(
                    username = EMPLOYEE_1.username,
                    password = DUMMY_PASSWORD,
                    source = SourceApp.POS_APP
                )
            )
            tokensResponseMapper wasNot Called
        }
    }

    @Test
    fun `test refreshAccessToken, missing accept header, should fall back to latest version content type`() {
        every { employeeService.refreshAccessToken(any()) } returns TOKENS_MODEL
        every { tokensResponseMapper.mapSingle(any()) } returns TOKENS_RESPONSE

        mvc.post(REFRESH_ACCESS_TOKEN_EMPLOYEE_PATH) {
            header(HttpHeaders.ACCEPT, ApiVersion.VERSION_1_JSON)
            contentType = MediaType.APPLICATION_JSON
            content = """
                {
                    "refreshToken": "$DUMMY_TOKEN"
                }
            """.trimIndent()
        }.andExpect {
            content { contentType(ApiVersion.VERSION_1_JSON) }
            status { isOk() }
        }

        verifySequence {
            employeeService.refreshAccessToken(any())
            tokensResponseMapper.mapSingle(any())
        }
    }

    @Test
    fun `test refreshAccessToken, missing content type, should return 415`() {
        mvc.post(REFRESH_ACCESS_TOKEN_EMPLOYEE_PATH) {
            header(HttpHeaders.ACCEPT, ApiVersion.VERSION_1_JSON)
            content = """{}"""
        }.andExpect {
            status { isUnsupportedMediaType() }
        }
    }

    @Test
    fun `test refreshAccessToken, invalid content type, should return 415`() {
        mvc.post(REFRESH_ACCESS_TOKEN_EMPLOYEE_PATH) {
            header(HttpHeaders.ACCEPT, ApiVersion.VERSION_1_JSON)
            contentType = MediaType.APPLICATION_PDF
            content = """{}"""
        }.andExpect {
            status { isUnsupportedMediaType() }
        }
    }

    @Test
    fun `test refreshAccessToken, should serialize and deserialize correctly`() {
        every { employeeService.refreshAccessToken(any()) } returns TOKENS_MODEL
        every { tokensResponseMapper.mapSingle(any()) } returns TOKENS_RESPONSE

        mvc.post(REFRESH_ACCESS_TOKEN_EMPLOYEE_PATH) {
            header(HttpHeaders.ACCEPT, ApiVersion.VERSION_1_JSON)
            contentType = MediaType.APPLICATION_JSON
            content = """
                {
                    "refreshToken": "$DUMMY_TOKEN"
                }
            """.trimIndent()
        }.andExpect {
            status { isOk() }
            content { contentType(ApiVersion.VERSION_1_JSON) }
            jsonContent(
                """
                {
                  "accessToken": "$ACCESS_TOKEN",
                  "accessExpiresIn": $ACCESS_EXPIRATION,
                  "refreshToken": "$REFRESH_TOKEN",
                  "refreshExpiresIn": $REFRESH_EXPIRATION
                }
            """
            )
        }

        verifySequence {
            employeeService.refreshAccessToken(
                RefreshAccessTokenCommand(
                    refreshToken = DUMMY_TOKEN
                )
            )
            tokensResponseMapper.mapSingle(TOKENS_MODEL)
        }
    }

    @Test
    fun `test refreshAccessToken - service throws exception - should return 401 unauthorized`() {
        every { employeeService.refreshAccessToken(any()) } throws AuthenticationFailedException()

        mvc.post(REFRESH_ACCESS_TOKEN_EMPLOYEE_PATH) {
            header(HttpHeaders.ACCEPT, ApiVersion.VERSION_1_JSON)
            contentType = MediaType.APPLICATION_JSON
            content = """
                {
                    "refreshToken": "$DUMMY_TOKEN"
                }
            """.trimIndent()
        }.andExpect {
            status { isUnauthorized() }
            content { contentType(ApiVersion.VERSION_1_JSON) }
            jsonContent("""{}""")
        }

        verifySequence {
            employeeService.refreshAccessToken(
                RefreshAccessTokenCommand(
                    refreshToken = DUMMY_TOKEN
                )
            )
            tokensResponseMapper wasNot Called
        }
    }

    @Test
    fun `test getSelf, missing accept header, should fall back to latest version content type`() {
        every { employeeService.getSelf(any()) } returns EMPLOYEE_1
        every { cinemaxConfigProperties.branchName } returns BRANCH_NAME

        mvc.get(GET_SELF_PATH) {
            header(HttpHeaders.ACCEPT, ApiVersion.VERSION_1_JSON)
            header(HttpHeaders.AUTHORIZATION, mockAccessToken())
        }.andExpect {
            content { contentType(ApiVersion.VERSION_1_JSON) }
            status { isOk() }
        }

        verifySequence {
            employeeService.getSelf(any())
        }
    }

    @Test
    fun `test getSelf - service throws exception - should return 401 unauthorized`() {
        every { employeeService.getSelf(any()) } throws AuthenticationFailedException()

        mvc.get(GET_SELF_PATH) {
            header(HttpHeaders.ACCEPT, ApiVersion.VERSION_1_JSON)
            header(HttpHeaders.AUTHORIZATION, mockAccessToken())
        }.andExpect {
            status { isUnauthorized() }
            content { contentType(ApiVersion.VERSION_1_JSON) }
        }

        verifySequence {
            employeeService.getSelf(
                GetSelfCommand(TEST_PRINCIPAL_USERNAME)
            )
        }
    }

    @Test
    fun `test getSelf, authorization header with valid token, should return 200 ok`() {
        every { employeeService.getSelf(any()) } returns EMPLOYEE_1
        every { cinemaxConfigProperties.branchName } returns BRANCH_NAME

        mvc.get(GET_SELF_PATH) {
            header(HttpHeaders.ACCEPT, ApiVersion.VERSION_1_JSON)
            header(HttpHeaders.AUTHORIZATION, mockAccessToken())
        }.andExpect {
            status { isOk() }
            content { contentType(ApiVersion.VERSION_1_JSON) }
            jsonContent(
                """
                {
                  "id": "${EMPLOYEE_1.id}",
                  "username": ${EMPLOYEE_1.username},
                  "fullName": "${EMPLOYEE_1.fullName}",
                  "passwordReset": ${EMPLOYEE_1.passwordReset},
                  "role": ${EMPLOYEE_1.role},
                  "lastLoginAt": ${EMPLOYEE_1.lastLoginAt},
                  "branchName": "$BRANCH_NAME"
                }
            """
            )
        }

        verifySequence {
            employeeService.getSelf(
                GetSelfCommand(TEST_PRINCIPAL_USERNAME)
            )
        }
    }

    @Test
    fun `test resetPassword with authorization, should serialize and deserialize correctly`() {
        every { employeeService.resetPassword(any()) } just Runs

        mvc.put(RESET_PASSWORD_PATH) {
            header(HttpHeaders.ACCEPT, ApiVersion.VERSION_1_JSON)
            header(HttpHeaders.AUTHORIZATION, mockAccessToken(EMPLOYEE_1.role))
            contentType = MediaType.APPLICATION_JSON
            content = """
                {
                    "newPassword": "$NEW_PASSWORD"
                }
            """.trimIndent()
        }.andExpect {
            status { isNoContent() }
        }

        verifySequence {
            employeeService.resetPassword(
                ResetPasswordCommand(
                    employeeUsername = TEST_PRINCIPAL_USERNAME,
                    newPassword = NEW_PASSWORD
                )
            )
        }
    }
}

private const val LOGIN_EMPLOYEE_PATH = "/pos-app/employees/login"
private const val REFRESH_ACCESS_TOKEN_EMPLOYEE_PATH = "/pos-app/employees/refresh"
private const val GET_SELF_PATH = "/pos-app/employees/self"
private const val RESET_PASSWORD_PATH = "/pos-app/employees/self/reset-password"
private const val DUMMY_PASSWORD = "dummyPassword"
private const val DUMMY_TOKEN = "dummyToken"
private const val NEW_PASSWORD = "newPassword"
private const val ACCESS_EXPIRATION = 900000
private const val REFRESH_EXPIRATION = 21600000
private const val BRANCH_NAME = "Bratislava Bory"

private val EMPLOYEE_1 = createEmployee(
    originalId = 1,
    username = "manager",
    fullName = "Johnny Manager",
    role = EmployeeRole.MANAGER
)
private val ACCESS_TOKEN = mockAccessToken()
private val REFRESH_TOKEN = mockRefreshToken()
private val TOKENS_MODEL = TokensModel(
    accessToken = ACCESS_TOKEN,
    accessExpiresIn = ACCESS_EXPIRATION,
    refreshToken = REFRESH_TOKEN,
    refreshExpiresIn = REFRESH_EXPIRATION
)
private val TOKENS_RESPONSE = TokensResponse(
    accessToken = ACCESS_TOKEN,
    accessExpiresIn = ACCESS_EXPIRATION,
    refreshToken = REFRESH_TOKEN,
    refreshExpiresIn = REFRESH_EXPIRATION
)
