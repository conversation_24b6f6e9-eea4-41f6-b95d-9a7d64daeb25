package com.cleevio.cinemax.api.module.genre.controller

import com.cleevio.cinemax.api.ControllerTest
import com.cleevio.cinemax.api.common.constant.ApiVersion
import com.cleevio.cinemax.api.common.descriptor.mssql.DescriptorMssqlResponse
import com.cleevio.cinemax.api.common.util.jsonContent
import com.cleevio.cinemax.api.module.employee.constant.EmployeeRole
import com.cleevio.cinemax.api.module.genre.entity.Genre
import com.cleevio.cinemax.api.util.mockAccessToken
import io.mockk.every
import io.mockk.verify
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest
import org.springframework.http.HttpHeaders
import org.springframework.test.web.servlet.MockMvc
import org.springframework.test.web.servlet.get

@WebMvcTest(AdminGenreController::class)
class AdminGenreControllerTest @Autowired constructor(
    private val mvc: MockMvc,
) : ControllerTest() {

    @Test
    fun `test getGenres, should serialize and deserialize correctly`() {
        every { genreFinderService.findAll() } returns listOf(GENRE_1, GENRE_2, GENRE_3)
        every { descriptorMssqlResponseMapper.mapList(any()) } returns listOf(
            GENRE_1_RESPONSE,
            GENRE_2_RESPONSE,
            GENRE_3_RESPONSE
        )

        mvc.get(GET_GENRES_PATH) {
            header(HttpHeaders.ACCEPT, ApiVersion.VERSION_1_JSON)
            header(HttpHeaders.AUTHORIZATION, mockAccessToken(role = EmployeeRole.MANAGER))
        }.andExpect {
            status { isOk() }
            content { contentType(ApiVersion.VERSION_1_JSON) }
            jsonContent(
                """
                [{
                  "id": "${GENRE_1.id}",
                  "title": "${GENRE_1.title}"
                },
                {
                  "id": "${GENRE_2.id}",
                  "title": "${GENRE_2.title}"
                },
                {
                  "id": "${GENRE_3.id}",
                  "title": "${GENRE_3.title}"
                }]
            """
            )
        }

        verify {
            descriptorMssqlResponseMapper.mapList(listOf(GENRE_1, GENRE_2, GENRE_3))
        }
    }
}

private const val GET_GENRES_PATH = "/manager-app/genres"
private val GENRE_1 = Genre(
    originalId = 1,
    code = "0",
    title = "Animovaný"
)
private val GENRE_2 = Genre(
    originalId = 2,
    code = "1",
    title = "Dráma"
)
private val GENRE_3 = Genre(
    originalId = 40,
    code = "-",
    title = "Hudobny, muzikal"
)
private val GENRE_1_RESPONSE = DescriptorMssqlResponse(
    id = GENRE_1.id,
    title = GENRE_1.title
)
private val GENRE_2_RESPONSE = DescriptorMssqlResponse(
    id = GENRE_2.id,
    title = GENRE_2.title
)
private val GENRE_3_RESPONSE = DescriptorMssqlResponse(
    id = GENRE_3.id,
    title = GENRE_3.title
)
