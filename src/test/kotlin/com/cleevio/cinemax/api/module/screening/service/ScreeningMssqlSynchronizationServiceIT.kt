package com.cleevio.cinemax.api.module.screening.service

import com.cleevio.cinemax.api.MssqlIntegrationTest
import com.cleevio.cinemax.api.common.util.isEqualTo
import com.cleevio.cinemax.api.module.language.entity.Language
import com.cleevio.cinemax.api.module.movie.service.command.UpdateMovieLanguageAndTechnologyCommand
import com.cleevio.cinemax.api.module.screening.constant.ScreeningState
import com.cleevio.cinemax.api.module.screening.service.command.CreateOrUpdateScreeningCommand
import com.cleevio.cinemax.api.module.screeningfee.service.command.CreateOrUpdateScreeningFeeCommand
import com.cleevio.cinemax.api.module.screeningtypes.service.command.DeleteAndCreateScreeningTypesCommand
import com.cleevio.cinemax.api.module.synchronizationfrommssql.constant.SynchronizationFromMssqlType
import com.cleevio.cinemax.api.module.synchronizationfrommssql.entity.SynchronizationFromMssql
import com.cleevio.cinemax.api.module.synchronizationfrommssql.service.command.CreateOrUpdateSynchronizationFromMssqlCommand
import com.cleevio.cinemax.api.module.technology.entity.Technology
import com.cleevio.cinemax.api.util.createAuditorium
import com.cleevio.cinemax.api.util.createAuditoriumLayout
import com.cleevio.cinemax.api.util.createMovie
import com.cleevio.cinemax.api.util.createPriceCategory
import com.cleevio.cinemax.api.util.createScreening
import com.cleevio.cinemax.api.util.createScreeningType
import io.mockk.Called
import io.mockk.Runs
import io.mockk.every
import io.mockk.just
import io.mockk.runs
import io.mockk.verify
import io.mockk.verifyOrder
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.test.context.jdbc.Sql
import org.springframework.test.context.jdbc.SqlConfig
import org.springframework.test.context.jdbc.SqlGroup
import java.math.BigDecimal
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.LocalTime
import java.util.UUID
import kotlin.test.assertEquals
import kotlin.test.assertTrue

@SqlGroup(
    Sql(
        scripts = [
            "/db/mssql-cinemax/test/init_mssql_screening.sql"
        ],
        config = SqlConfig(
            dataSource = "mssqlCinemaxDataSource",
            transactionManager = "mssqlCinemaxTransactionManager"
        )
    ),
    Sql(
        scripts = [
            "/db/mssql-cinemax/test/drop_mssql_screening.sql"
        ],
        config = SqlConfig(
            dataSource = "mssqlCinemaxDataSource",
            transactionManager = "mssqlCinemaxTransactionManager"
        ),
        executionPhase = Sql.ExecutionPhase.AFTER_TEST_METHOD
    )
)
class ScreeningMssqlSynchronizationServiceIT @Autowired constructor(
    private val underTest: ScreeningMssqlSynchronizationService,
) : MssqlIntegrationTest() {

    @Test
    fun `test synchronize all - no PSQL screening, 3 MSSQL screenings - should create 3 screenings`() {
        every { synchronizationFromMssqlFinderServiceMock.findByType(any()) } returns null
        every { screeningServiceMock.syncCreateOrUpdateScreening(any()) } just Runs
        every { screeningJooqFinderServiceMock.findByOriginalId(any()) } returns DUMMY_SCREENING
        every { screeningFeeServiceMock.createOrUpdateScreeningFee(any()) } just Runs
        every { auditoriumJooqFinderServiceMock.findByOriginalCode(AUDITORIUM_1.originalCode) } returns AUDITORIUM_1
        every { auditoriumJooqFinderServiceMock.findByOriginalCode(AUDITORIUM_2.originalCode) } returns AUDITORIUM_2
        every { movieJooqFinderServiceMock.findNonDeletedByOriginalId(MOVIE_1.originalId!!) } returns MOVIE_1
        every { movieJooqFinderServiceMock.findNonDeletedByOriginalId(MOVIE_2.originalId!!) } returns MOVIE_2
        every { priceCategoryJooqFinderServiceMock.findByOriginalId(PRICE_CATEGORY_1.originalId!!) } returns PRICE_CATEGORY_1
        every { priceCategoryJooqFinderServiceMock.findByOriginalId(PRICE_CATEGORY_2.originalId!!) } returns PRICE_CATEGORY_2
        every { synchronizationFromMssqlServiceMock.createOrUpdate(any()) } returns SYNCHRONIZATION_FROM_MSSQL
        every { screeningTypesServiceMock.deleteAndCreateScreeningTypes(any()) } just runs
        every { screeningTypeJpaFinderServiceMock.findAllByCodeIn(any()) } returns SCREENING_TYPE_LIST
        every { languageJpaFinderServiceMock.findByCode(LANGUAGE_1.code) } returns LANGUAGE_1
        every { languageJpaFinderServiceMock.findByCode(LANGUAGE_2.code) } returns LANGUAGE_2
        every { technologyJpaFinderServiceMock.findByCode(TECHNOLOGY_1.code) } returns TECHNOLOGY_1
        every { movieServiceMock.updateMovieLanguageAndTechnology(any()) } just Runs
        every {
            auditoriumLayoutJpaFinderServiceMock.findNonDeletedByOriginalId(any())
        } returnsMany listOf(AUDITORIUM_LAYOUT_1, AUDITORIUM_LAYOUT_2, AUDITORIUM_LAYOUT_3)

        underTest.synchronizeAll()

        val originalAuditoriumCodeCaptor = mutableListOf<Int>()
        val originalMovieIdCaptor = mutableListOf<Int>()
        val originalPriceCategoryIdCaptor = mutableListOf<Int>()
        val originalScreeningIdCaptor = mutableListOf<Int>()
        val screeningCommandCaptor = mutableListOf<CreateOrUpdateScreeningCommand>()
        val screeningFeeCommandCaptor = mutableListOf<CreateOrUpdateScreeningFeeCommand>()
        val languageCodeCaptor = mutableListOf<String>()
        val technologyCodeCaptor = mutableListOf<String>()
        val updateMovieCommandCaptor = mutableListOf<UpdateMovieLanguageAndTechnologyCommand>()

        verify { synchronizationFromMssqlFinderServiceMock.findByType(SynchronizationFromMssqlType.SCREENING) }
        verify { auditoriumJooqFinderServiceMock.findByOriginalCode(capture(originalAuditoriumCodeCaptor)) }
        verify { movieJooqFinderServiceMock.findNonDeletedByOriginalId(capture(originalMovieIdCaptor)) }
        verify { priceCategoryJooqFinderServiceMock.findByOriginalId(capture(originalPriceCategoryIdCaptor)) }
        verify { screeningJooqFinderServiceMock.findByOriginalId(capture(originalScreeningIdCaptor)) }
        verify { screeningServiceMock.syncCreateOrUpdateScreening(capture(screeningCommandCaptor)) }
        verify { screeningFeeServiceMock.createOrUpdateScreeningFee(capture(screeningFeeCommandCaptor)) }
        verify { languageJpaFinderServiceMock.findByCode(capture(languageCodeCaptor)) }
        verify { technologyJpaFinderServiceMock.findByCode(capture(technologyCodeCaptor)) }
        verify { movieServiceMock.updateMovieLanguageAndTechnology(capture(updateMovieCommandCaptor)) }
        verify {
            synchronizationFromMssqlServiceMock.createOrUpdate(
                CreateOrUpdateSynchronizationFromMssqlCommand(
                    type = SynchronizationFromMssqlType.SCREENING,
                    lastSynchronization = SCREENING_3_UPDATED_AT
                )
            )
        }
        verifyOrder {
            screeningTypeJpaFinderServiceMock.findAllByCodeIn(SCREENING_TYPE_LIST.map { it.code }.toSet())
            screeningTypesServiceMock.deleteAndCreateScreeningTypes(
                DeleteAndCreateScreeningTypesCommand(
                    screeningId = DUMMY_SCREENING.id,
                    screeningTypeIds = SCREENING_TYPE_LIST.map { it.id }.toSet()
                )
            )
        }
        verifyOrder {
            auditoriumLayoutJpaFinderServiceMock.findNonDeletedByOriginalId(AUDITORIUM_LAYOUT_1.originalId!!)
            auditoriumLayoutJpaFinderServiceMock.findNonDeletedByOriginalId(AUDITORIUM_LAYOUT_2.originalId!!)
            auditoriumLayoutJpaFinderServiceMock.findNonDeletedByOriginalId(AUDITORIUM_LAYOUT_3.originalId!!)
        }

        val auditoriumIds = setOf(AUDITORIUM_1.originalCode, AUDITORIUM_2.originalCode)
        assertTrue(originalAuditoriumCodeCaptor.size == 3)
        assertTrue(originalAuditoriumCodeCaptor.containsAll(auditoriumIds))
        assertTrue(originalMovieIdCaptor.size == 3)
        assertTrue(originalMovieIdCaptor.containsAll(setOf(MOVIE_1.originalId, MOVIE_2.originalId)))
        assertTrue(originalPriceCategoryIdCaptor.size == 3)
        assertTrue(originalPriceCategoryIdCaptor.containsAll(setOf(PRICE_CATEGORY_1.originalId, PRICE_CATEGORY_2.originalId)))
        assertTrue(
            originalScreeningIdCaptor.containsAll(
                setOf(
                    EXPECTED_SCREENING_COMMAND_1.originalId,
                    EXPECTED_SCREENING_COMMAND_2.originalId,
                    EXPECTED_SCREENING_COMMAND_3.originalId
                )
            )
        )
        assertTrue(screeningCommandCaptor.size == 3)
        assertEquals(EXPECTED_SCREENING_COMMAND_1, screeningCommandCaptor[0])
        assertEquals(EXPECTED_SCREENING_COMMAND_2, screeningCommandCaptor[1])
        assertEquals(EXPECTED_SCREENING_COMMAND_3, screeningCommandCaptor[2])
        assertTrue(screeningFeeCommandCaptor.size == 3)
        assertScreeningFeeCommandEquals(EXPECTED_SCREENING_FEE_COMMAND_1, screeningFeeCommandCaptor[0])
        assertScreeningFeeCommandEquals(EXPECTED_SCREENING_FEE_COMMAND_2, screeningFeeCommandCaptor[1])
        assertScreeningFeeCommandEquals(EXPECTED_SCREENING_FEE_COMMAND_3, screeningFeeCommandCaptor[2])
        assertTrue(languageCodeCaptor.size == 2)
        assertTrue(languageCodeCaptor.containsAll(setOf(LANGUAGE_1.code, LANGUAGE_2.code)))
        assertTrue(technologyCodeCaptor.size == 1)
        assertTrue(technologyCodeCaptor.contains(TECHNOLOGY_1.code))
        assertTrue(updateMovieCommandCaptor.size == 3)
        assertUpdateMovieCommandEquals(UPDATED_MOVIE_COMMAND_1, updateMovieCommandCaptor[0])
        assertUpdateMovieCommandEquals(UPDATED_MOVIE_COMMAND_2, updateMovieCommandCaptor[1])
        assertUpdateMovieCommandEquals(UPDATED_MOVIE_COMMAND_3, updateMovieCommandCaptor[2])
    }

    @Test
    fun `test synchronize all - 2 PSQL screenings, 3 MSSQL screenings - should create 1 screening`() {
        every { synchronizationFromMssqlFinderServiceMock.findByType(any()) } returns SCREENING_2_UPDATED_AT
        every { screeningServiceMock.syncCreateOrUpdateScreening(any()) } just Runs
        every { screeningJooqFinderServiceMock.findByOriginalId(any()) } returns DUMMY_SCREENING
        every { screeningFeeServiceMock.createOrUpdateScreeningFee(any()) } just Runs
        every { auditoriumJooqFinderServiceMock.findByOriginalCode(any()) } returns AUDITORIUM_2
        every { movieJooqFinderServiceMock.findNonDeletedByOriginalId(any()) } returns MOVIE_2
        every { priceCategoryJooqFinderServiceMock.findByOriginalId(PRICE_CATEGORY_2.originalId!!) } returns PRICE_CATEGORY_2
        every { synchronizationFromMssqlServiceMock.createOrUpdate(any()) } returns SYNCHRONIZATION_FROM_MSSQL
        every { auditoriumLayoutJpaFinderServiceMock.findNonDeletedByOriginalId(any()) } returns AUDITORIUM_LAYOUT_3
        every { languageJpaFinderServiceMock.findByCode(LANGUAGE_2.code) } returns LANGUAGE_2
        every { technologyJpaFinderServiceMock.findByCode(TECHNOLOGY_1.code) } returns TECHNOLOGY_1
        every { movieServiceMock.updateMovieLanguageAndTechnology(any()) } just Runs

        underTest.synchronizeAll()

        val originalAuditoriumCodeCaptor = mutableListOf<Int>()
        val originalMovieIdCaptor = mutableListOf<Int>()
        val originalPriceCategoryIdCaptor = mutableListOf<Int>()
        val originalScreeningIdCaptor = mutableListOf<Int>()
        val screeningFeeCommandCaptor = mutableListOf<CreateOrUpdateScreeningFeeCommand>()
        val screeningCommandCaptor = mutableListOf<CreateOrUpdateScreeningCommand>()
        val languageCodeCaptor = mutableListOf<String>()
        val technologyCodeCaptor = mutableListOf<String>()
        val updateMovieCommandCaptor = mutableListOf<UpdateMovieLanguageAndTechnologyCommand>()

        verify { synchronizationFromMssqlFinderServiceMock.findByType(SynchronizationFromMssqlType.SCREENING) }
        verify { auditoriumJooqFinderServiceMock.findByOriginalCode(capture(originalAuditoriumCodeCaptor)) }
        verify { movieJooqFinderServiceMock.findNonDeletedByOriginalId(capture(originalMovieIdCaptor)) }
        verify { priceCategoryJooqFinderServiceMock.findByOriginalId(capture(originalPriceCategoryIdCaptor)) }
        verify { screeningJooqFinderServiceMock.findByOriginalId(capture(originalScreeningIdCaptor)) }
        verify { screeningServiceMock.syncCreateOrUpdateScreening(capture(screeningCommandCaptor)) }
        verify { screeningFeeServiceMock.createOrUpdateScreeningFee(capture(screeningFeeCommandCaptor)) }
        verify { languageJpaFinderServiceMock.findByCode(capture(languageCodeCaptor)) }
        verify { technologyJpaFinderServiceMock.findByCode(capture(technologyCodeCaptor)) }
        verify { movieServiceMock.updateMovieLanguageAndTechnology(capture(updateMovieCommandCaptor)) }
        verify {
            synchronizationFromMssqlServiceMock.createOrUpdate(
                CreateOrUpdateSynchronizationFromMssqlCommand(
                    type = SynchronizationFromMssqlType.SCREENING,
                    lastSynchronization = SCREENING_3_UPDATED_AT
                )
            )
        }
        verify(exactly = 1) { auditoriumLayoutJpaFinderServiceMock.findNonDeletedByOriginalId(AUDITORIUM_LAYOUT_3.originalId!!) }

        assertTrue(originalAuditoriumCodeCaptor.size == 1)
        assertTrue(originalAuditoriumCodeCaptor.containsAll(setOf(AUDITORIUM_2.originalCode)))
        assertTrue(originalMovieIdCaptor.size == 1)
        assertTrue(originalMovieIdCaptor.containsAll(setOf(MOVIE_2.originalId)))
        assertTrue(originalPriceCategoryIdCaptor.size == 1)
        assertTrue(originalPriceCategoryIdCaptor.containsAll(setOf(PRICE_CATEGORY_2.originalId)))
        assertTrue(originalScreeningIdCaptor.containsAll(setOf(EXPECTED_SCREENING_COMMAND_3.originalId)))
        assertTrue(screeningCommandCaptor.size == 1)
        assertEquals(EXPECTED_SCREENING_COMMAND_3, screeningCommandCaptor[0])
        assertTrue(screeningFeeCommandCaptor.size == 1)
        assertScreeningFeeCommandEquals(EXPECTED_SCREENING_FEE_COMMAND_3, screeningFeeCommandCaptor[0])
        assertTrue(languageCodeCaptor.size == 1)
        assertTrue(languageCodeCaptor.containsAll(setOf(LANGUAGE_2.code)))
        assertTrue(technologyCodeCaptor.size == 1)
        assertTrue(technologyCodeCaptor.contains(TECHNOLOGY_1.code))
        assertTrue(updateMovieCommandCaptor.size == 1)
        assertUpdateMovieCommandEquals(UPDATED_MOVIE_COMMAND_3, updateMovieCommandCaptor[0])
    }

    @Test
    fun `test synchronize all - language and technology not found - should create 1 screening`() {
        every { synchronizationFromMssqlFinderServiceMock.findByType(any()) } returns SCREENING_2_UPDATED_AT
        every { screeningServiceMock.syncCreateOrUpdateScreening(any()) } just Runs
        every { screeningJooqFinderServiceMock.findByOriginalId(any()) } returns DUMMY_SCREENING
        every { screeningFeeServiceMock.createOrUpdateScreeningFee(any()) } just Runs
        every { auditoriumJooqFinderServiceMock.findByOriginalCode(any()) } returns AUDITORIUM_2
        every { movieJooqFinderServiceMock.findNonDeletedByOriginalId(any()) } returns MOVIE_2
        every { priceCategoryJooqFinderServiceMock.findByOriginalId(PRICE_CATEGORY_2.originalId!!) } returns PRICE_CATEGORY_2
        every { synchronizationFromMssqlServiceMock.createOrUpdate(any()) } returns SYNCHRONIZATION_FROM_MSSQL
        every { auditoriumLayoutJpaFinderServiceMock.findNonDeletedByOriginalId(any()) } returns AUDITORIUM_LAYOUT_3
        every { languageJpaFinderServiceMock.findByCode(LANGUAGE_2.code) } returns null
        every { technologyJpaFinderServiceMock.findByCode(TECHNOLOGY_1.code) } returns null
        every { movieServiceMock.updateMovieLanguageAndTechnology(any()) } just Runs

        underTest.synchronizeAll()

        val originalAuditoriumCodeCaptor = mutableListOf<Int>()
        val originalMovieIdCaptor = mutableListOf<Int>()
        val originalPriceCategoryIdCaptor = mutableListOf<Int>()
        val originalScreeningIdCaptor = mutableListOf<Int>()
        val screeningFeeCommandCaptor = mutableListOf<CreateOrUpdateScreeningFeeCommand>()
        val screeningCommandCaptor = mutableListOf<CreateOrUpdateScreeningCommand>()
        val languageCodeCaptor = mutableListOf<String>()
        val technologyCodeCaptor = mutableListOf<String>()
        val updateMovieCommandCaptor = mutableListOf<UpdateMovieLanguageAndTechnologyCommand>()

        verify { synchronizationFromMssqlFinderServiceMock.findByType(SynchronizationFromMssqlType.SCREENING) }
        verify { auditoriumJooqFinderServiceMock.findByOriginalCode(capture(originalAuditoriumCodeCaptor)) }
        verify { movieJooqFinderServiceMock.findNonDeletedByOriginalId(capture(originalMovieIdCaptor)) }
        verify { priceCategoryJooqFinderServiceMock.findByOriginalId(capture(originalPriceCategoryIdCaptor)) }
        verify { screeningJooqFinderServiceMock.findByOriginalId(capture(originalScreeningIdCaptor)) }
        verify { screeningServiceMock.syncCreateOrUpdateScreening(capture(screeningCommandCaptor)) }
        verify { screeningFeeServiceMock.createOrUpdateScreeningFee(capture(screeningFeeCommandCaptor)) }
        verify { languageJpaFinderServiceMock.findByCode(capture(languageCodeCaptor)) }
        verify { technologyJpaFinderServiceMock.findByCode(capture(technologyCodeCaptor)) }
        verify { movieServiceMock.updateMovieLanguageAndTechnology(capture(updateMovieCommandCaptor)) }
        verify {
            synchronizationFromMssqlServiceMock.createOrUpdate(
                CreateOrUpdateSynchronizationFromMssqlCommand(
                    type = SynchronizationFromMssqlType.SCREENING,
                    lastSynchronization = SCREENING_3_UPDATED_AT
                )
            )
        }
        verify(exactly = 1) { auditoriumLayoutJpaFinderServiceMock.findNonDeletedByOriginalId(AUDITORIUM_LAYOUT_3.originalId!!) }

        assertTrue(originalAuditoriumCodeCaptor.size == 1)
        assertTrue(originalAuditoriumCodeCaptor.containsAll(setOf(AUDITORIUM_2.originalCode)))
        assertTrue(originalMovieIdCaptor.size == 1)
        assertTrue(originalMovieIdCaptor.containsAll(setOf(MOVIE_2.originalId)))
        assertTrue(originalPriceCategoryIdCaptor.size == 1)
        assertTrue(originalPriceCategoryIdCaptor.containsAll(setOf(PRICE_CATEGORY_2.originalId)))
        assertTrue(originalScreeningIdCaptor.containsAll(setOf(EXPECTED_SCREENING_COMMAND_3.originalId)))
        assertTrue(screeningCommandCaptor.size == 1)
        assertEquals(EXPECTED_SCREENING_COMMAND_3, screeningCommandCaptor[0])
        assertTrue(screeningFeeCommandCaptor.size == 1)
        assertScreeningFeeCommandEquals(EXPECTED_SCREENING_FEE_COMMAND_3, screeningFeeCommandCaptor[0])
        assertTrue(languageCodeCaptor.size == 1)
        assertTrue(languageCodeCaptor.containsAll(setOf(LANGUAGE_2.code)))
        assertTrue(technologyCodeCaptor.size == 1)
        assertTrue(technologyCodeCaptor.contains(TECHNOLOGY_1.code))
        assertTrue(updateMovieCommandCaptor.size == 1)
        assertUpdateMovieCommandEquals(
            UPDATED_MOVIE_COMMAND_3.copy(languageId = null, technologyId = null),
            updateMovieCommandCaptor[0]
        )
    }

    @Test
    fun `test synchronize all - no PSQL movie - should create no screenings`() {
        every { synchronizationFromMssqlFinderServiceMock.findByType(any()) } returns null
        every { screeningServiceMock.syncCreateOrUpdateScreening(any()) } just Runs
        every { screeningFeeServiceMock.createOrUpdateScreeningFee(any()) } just Runs
        every { auditoriumJooqFinderServiceMock.findByOriginalCode(any()) } returns AUDITORIUM_2
        every { movieJooqFinderServiceMock.findNonDeletedByOriginalId(any()) } returns null
        every { synchronizationFromMssqlServiceMock.createOrUpdate(any()) } returns SYNCHRONIZATION_FROM_MSSQL

        underTest.synchronizeAll()

        val originalAuditoriumCodeCaptor = mutableListOf<Int>()
        val originalMovieIdCaptor = mutableListOf<Int>()

        verify { synchronizationFromMssqlFinderServiceMock.findByType(SynchronizationFromMssqlType.SCREENING) }
        verify { auditoriumJooqFinderServiceMock.findByOriginalCode(capture(originalAuditoriumCodeCaptor)) }
        verify { movieJooqFinderServiceMock.findNonDeletedByOriginalId(capture(originalMovieIdCaptor)) }
        verify { screeningServiceMock wasNot Called }
        verify { screeningFeeServiceMock wasNot Called }
        verify { languageJpaFinderServiceMock wasNot Called }
        verify { technologyJpaFinderServiceMock wasNot Called }
        verify { movieServiceMock wasNot Called }
        verify { priceCategoryJooqFinderServiceMock wasNot Called }
        verify {
            synchronizationFromMssqlServiceMock.createOrUpdate(
                CreateOrUpdateSynchronizationFromMssqlCommand(
                    type = SynchronizationFromMssqlType.SCREENING,
                    lastSynchronization = SCREENING_3_UPDATED_AT
                )
            )
        }

        val auditoriumIds = setOf(AUDITORIUM_1.originalCode, AUDITORIUM_2.originalCode)
        assertTrue(originalAuditoriumCodeCaptor.size == 3)
        assertTrue(originalAuditoriumCodeCaptor.containsAll(auditoriumIds))
        assertTrue(originalMovieIdCaptor.size == 3)
        assertTrue(originalMovieIdCaptor.containsAll(setOf(MOVIE_1.originalId, MOVIE_2.originalId)))
    }

    @Test
    fun `test synchronize all - no PSQL auditorium - should create no screenings`() {
        every { synchronizationFromMssqlFinderServiceMock.findByType(any()) } returns null
        every { screeningServiceMock.syncCreateOrUpdateScreening(any()) } just Runs
        every { screeningFeeServiceMock.createOrUpdateScreeningFee(any()) } just Runs
        every { auditoriumJooqFinderServiceMock.findByOriginalCode(any()) } returns null
        every { synchronizationFromMssqlServiceMock.createOrUpdate(any()) } returns SYNCHRONIZATION_FROM_MSSQL

        underTest.synchronizeAll()

        val originalAuditoriumCodeCaptor = mutableListOf<Int>()

        verify { synchronizationFromMssqlFinderServiceMock.findByType(SynchronizationFromMssqlType.SCREENING) }
        verify { auditoriumJooqFinderServiceMock.findByOriginalCode(capture(originalAuditoriumCodeCaptor)) }
        verify { movieJooqFinderServiceMock wasNot Called }
        verify { screeningServiceMock wasNot Called }
        verify { screeningFeeServiceMock wasNot Called }
        verify { languageJpaFinderServiceMock wasNot Called }
        verify { technologyJpaFinderServiceMock wasNot Called }
        verify { movieServiceMock wasNot Called }
        verify { priceCategoryJooqFinderServiceMock wasNot Called }
        verify {
            synchronizationFromMssqlServiceMock.createOrUpdate(
                CreateOrUpdateSynchronizationFromMssqlCommand(
                    type = SynchronizationFromMssqlType.SCREENING,
                    lastSynchronization = SCREENING_3_UPDATED_AT
                )
            )
        }

        val auditoriumIds = setOf(AUDITORIUM_1.originalCode, AUDITORIUM_2.originalCode)
        assertTrue(originalAuditoriumCodeCaptor.size == 3)
        assertTrue(originalAuditoriumCodeCaptor.containsAll(auditoriumIds))
    }

    private fun assertScreeningFeeCommandEquals(
        expected: CreateOrUpdateScreeningFeeCommand,
        actual: CreateOrUpdateScreeningFeeCommand,
    ) {
        assertEquals(expected.originalScreeningId, actual.originalScreeningId)
        assertTrue(expected.surchargeVip isEqualTo actual.surchargeVip)
        assertTrue(expected.surchargePremium isEqualTo actual.surchargePremium)
        assertTrue(expected.surchargeImax isEqualTo actual.surchargeImax)
        assertTrue(expected.surchargeUltraX isEqualTo actual.surchargeUltraX)
        assertTrue(expected.serviceFeeVip isEqualTo actual.serviceFeeVip)
        assertTrue(expected.serviceFeeVip isEqualTo actual.serviceFeeVip)
        assertTrue(expected.serviceFeePremium isEqualTo actual.serviceFeePremium)
        assertTrue(expected.serviceFeeImax isEqualTo actual.serviceFeeImax)
        assertTrue(expected.serviceFeeUltraX isEqualTo actual.serviceFeeUltraX)
        assertTrue(expected.surchargeDBox isEqualTo actual.surchargeDBox)
        assertTrue(expected.serviceFeeGeneral isEqualTo actual.serviceFeeGeneral)
    }

    private fun assertUpdateMovieCommandEquals(
        expected: UpdateMovieLanguageAndTechnologyCommand,
        actual: UpdateMovieLanguageAndTechnologyCommand,
    ) {
        assertEquals(expected.movieId, actual.movieId)
        assertEquals(expected.languageId, actual.languageId)
        assertEquals(expected.technologyId, actual.technologyId)
    }
}

private val SCREENING_2_UPDATED_AT = LocalDateTime.of(2023, 7, 19, 16, 16, 0)
private val SCREENING_3_UPDATED_AT = LocalDateTime.of(2023, 7, 19, 16, 17, 0)
private val AUDITORIUM_1 = createAuditorium(
    originalCode = 522630
)
private val AUDITORIUM_2 = createAuditorium(
    originalCode = 522632
)
private val DISTRIBUTOR_1_ID = UUID.randomUUID()
private val MOVIE_1 = createMovie(
    originalId = 4387,
    distributorId = DISTRIBUTOR_1_ID
)
private val MOVIE_2 = createMovie(
    originalId = 4628,
    title = "Barbie",
    distributorId = DISTRIBUTOR_1_ID
)
private val PRICE_CATEGORY_1 = createPriceCategory(
    originalId = 749,
    title = "ARTMAX PO 17",
    active = true
)
private val PRICE_CATEGORY_2 = createPriceCategory(
    originalId = 761,
    title = "Extra - zacatek letnich prazdnin",
    active = true
)
private val AUDITORIUM_LAYOUT_1 = createAuditoriumLayout(originalId = 1, auditoriumId = AUDITORIUM_1.id)
private val AUDITORIUM_LAYOUT_2 = createAuditoriumLayout(originalId = 3, auditoriumId = AUDITORIUM_2.id)
private val AUDITORIUM_LAYOUT_3 = createAuditoriumLayout(originalId = 5, auditoriumId = AUDITORIUM_2.id)
private val LANGUAGE_1 = Language(
    title = "anglická verze",
    originalId = 3,
    code = "ENG"
)
private val LANGUAGE_2 = Language(
    title = "česká verze",
    originalId = 1,
    code = "CZ"
)
private val TECHNOLOGY_1 = Technology(
    title = "Analog",
    originalId = 8,
    code = "01"
)
private val EXPECTED_SCREENING_COMMAND_1 = CreateOrUpdateScreeningCommand(
    originalId = 1,
    auditoriumId = AUDITORIUM_1.id,
    auditoriumLayoutId = AUDITORIUM_LAYOUT_1.id,
    movieId = MOVIE_1.id,
    priceCategoryId = PRICE_CATEGORY_1.id,
    date = LocalDate.of(2023, 7, 21),
    time = LocalTime.of(16, 0),
    saleTimeLimit = 30,
    stopped = false,
    cancelled = false,
    proCommission = 1,
    filmFondCommission = 4,
    distributorCommission = 50,
    publishOnline = true,
    state = ScreeningState.PUBLISHED,
    surchargeVip = BigDecimal.ZERO,
    surchargePremium = BigDecimal.ZERO,
    surchargeImax = BigDecimal.ZERO,
    surchargeUltraX = BigDecimal.ZERO,
    serviceFeeVip = BigDecimal.ZERO,
    serviceFeePremium = BigDecimal.ZERO,
    serviceFeeImax = BigDecimal.ZERO,
    serviceFeeUltraX = BigDecimal.ZERO,
    surchargeDBox = BigDecimal.ZERO,
    serviceFeeGeneral = BigDecimal.ZERO
)
private val EXPECTED_SCREENING_COMMAND_2 = CreateOrUpdateScreeningCommand(
    originalId = 2,
    auditoriumId = AUDITORIUM_2.id,
    auditoriumLayoutId = AUDITORIUM_LAYOUT_2.id,
    movieId = MOVIE_2.id,
    priceCategoryId = PRICE_CATEGORY_2.id,
    date = LocalDate.of(2023, 7, 19),
    time = LocalTime.of(16, 0),
    saleTimeLimit = 30,
    stopped = false,
    cancelled = false,
    proCommission = 2,
    filmFondCommission = 5,
    distributorCommission = 60,
    publishOnline = false,
    state = ScreeningState.PUBLISHED,
    surchargeVip = BigDecimal.ZERO,
    surchargePremium = BigDecimal.ZERO,
    surchargeImax = BigDecimal.ZERO,
    surchargeUltraX = BigDecimal.ZERO,
    serviceFeeVip = BigDecimal.ZERO,
    serviceFeePremium = BigDecimal.ZERO,
    serviceFeeImax = BigDecimal.ZERO,
    serviceFeeUltraX = BigDecimal.ZERO,
    surchargeDBox = BigDecimal.ZERO,
    serviceFeeGeneral = BigDecimal.ZERO
)
private val EXPECTED_SCREENING_COMMAND_3 = CreateOrUpdateScreeningCommand(
    originalId = 3,
    auditoriumId = AUDITORIUM_2.id,
    auditoriumLayoutId = AUDITORIUM_LAYOUT_3.id,
    movieId = MOVIE_2.id,
    priceCategoryId = PRICE_CATEGORY_2.id,
    date = LocalDate.of(2023, 7, 20),
    time = LocalTime.of(16, 0),
    saleTimeLimit = 1000,
    stopped = false,
    cancelled = false,
    proCommission = 3,
    filmFondCommission = 6,
    distributorCommission = 70,
    publishOnline = true,
    state = ScreeningState.PUBLISHED,
    surchargeVip = BigDecimal.ZERO,
    surchargePremium = BigDecimal.ZERO,
    surchargeImax = BigDecimal.ZERO,
    surchargeUltraX = BigDecimal.ZERO,
    serviceFeeVip = BigDecimal.ZERO,
    serviceFeePremium = BigDecimal.ZERO,
    serviceFeeImax = BigDecimal.ZERO,
    serviceFeeUltraX = BigDecimal.ZERO,
    surchargeDBox = BigDecimal.ZERO,
    serviceFeeGeneral = BigDecimal.ZERO
)
private val DUMMY_SCREENING = createScreening(
    originalId = 1,
    auditoriumId = AUDITORIUM_1.id,
    auditoriumLayoutId = AUDITORIUM_LAYOUT_1.id,
    movieId = MOVIE_1.id,
    priceCategoryId = PRICE_CATEGORY_1.id,
    date = LocalDate.of(2023, 7, 21),
    time = LocalTime.of(16, 0)
)
private val EXPECTED_SCREENING_FEE_COMMAND_1 = CreateOrUpdateScreeningFeeCommand(
    originalScreeningId = 1,
    screeningId = UUID.randomUUID(),
    surchargeVip = BigDecimal.ZERO,
    surchargePremium = BigDecimal.ZERO,
    surchargeImax = BigDecimal.valueOf(3),
    surchargeUltraX = BigDecimal.ZERO,
    serviceFeeVip = BigDecimal.ZERO,
    serviceFeePremium = BigDecimal.ZERO,
    serviceFeeImax = BigDecimal.valueOf(2),
    serviceFeeUltraX = BigDecimal.ZERO,
    surchargeDBox = BigDecimal.ONE,
    serviceFeeGeneral = BigDecimal.ZERO
)
private val EXPECTED_SCREENING_FEE_COMMAND_2 = CreateOrUpdateScreeningFeeCommand(
    originalScreeningId = 2,
    screeningId = UUID.randomUUID(),
    surchargeVip = BigDecimal.valueOf(4),
    surchargePremium = BigDecimal.ZERO,
    surchargeImax = BigDecimal.valueOf(2),
    surchargeUltraX = BigDecimal.ZERO,
    serviceFeeVip = BigDecimal.ZERO,
    serviceFeePremium = BigDecimal.ZERO,
    serviceFeeImax = BigDecimal.ZERO,
    serviceFeeUltraX = BigDecimal.ZERO,
    surchargeDBox = BigDecimal.ZERO,
    serviceFeeGeneral = BigDecimal.valueOf(4)
)
private val EXPECTED_SCREENING_FEE_COMMAND_3 = CreateOrUpdateScreeningFeeCommand(
    originalScreeningId = 3,
    screeningId = UUID.randomUUID(),
    surchargeVip = BigDecimal.valueOf(4),
    surchargePremium = BigDecimal.ZERO,
    surchargeImax = BigDecimal.valueOf(2),
    surchargeUltraX = BigDecimal.ZERO,
    serviceFeeVip = BigDecimal.ZERO,
    serviceFeePremium = BigDecimal.ZERO,
    serviceFeeImax = BigDecimal.ZERO,
    serviceFeeUltraX = BigDecimal.ZERO,
    surchargeDBox = BigDecimal.ZERO,
    serviceFeeGeneral = BigDecimal.valueOf(4)
)
private val UPDATED_MOVIE_COMMAND_1 = UpdateMovieLanguageAndTechnologyCommand(
    movieId = MOVIE_1.id,
    languageId = null,
    technologyId = null
)
private val UPDATED_MOVIE_COMMAND_2 = UpdateMovieLanguageAndTechnologyCommand(
    movieId = MOVIE_2.id,
    languageId = LANGUAGE_1.id,
    technologyId = null
)
private val UPDATED_MOVIE_COMMAND_3 = UpdateMovieLanguageAndTechnologyCommand(
    movieId = MOVIE_2.id,
    languageId = LANGUAGE_2.id,
    technologyId = TECHNOLOGY_1.id
)
private val SYNCHRONIZATION_FROM_MSSQL = SynchronizationFromMssql(
    type = SynchronizationFromMssqlType.SCREENING,
    lastSynchronization = LocalDateTime.MIN,
    lastHeartbeat = null
)

private val SCREENING_TYPE_LIST = listOf(
    createScreeningType(code = "05"),
    createScreeningType(code = "06"),
    createScreeningType(code = "16"),
    createScreeningType(code = "23")
)
