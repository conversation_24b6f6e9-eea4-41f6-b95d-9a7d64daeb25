package com.cleevio.cinemax.api.module.basket.service

import com.cleevio.cinemax.api.DatabaseCleanup
import com.cleevio.cinemax.api.IntegrationDataTestHelper
import com.cleevio.cinemax.api.IntegrationTest
import com.cleevio.cinemax.api.module.basket.constant.BasketState
import com.cleevio.cinemax.api.module.basket.exception.BasketNotFoundException
import com.cleevio.cinemax.api.module.basket.service.query.GetWebBasketTicketsQuery
import com.cleevio.cinemax.api.module.basketitem.constant.BasketItemType
import com.cleevio.cinemax.api.module.productcomponent.constant.ProductComponentUnit
import com.cleevio.cinemax.api.module.seat.constant.SeatType
import io.kotest.assertions.throwables.shouldThrow
import io.kotest.matchers.comparables.shouldBeEqualComparingTo
import io.kotest.matchers.nulls.shouldNotBeNull
import io.kotest.matchers.shouldBe
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import java.util.UUID

class GetWebBasketTicketsQueryServiceIT @Autowired constructor(
    private val underTest: GetWebBasketTicketsQueryService,
    private val integrationDataTestHelper: IntegrationDataTestHelper,
) : IntegrationTest(DatabaseCleanup.AFTER_EACH) {

    @Test
    fun `test GetWebBasketTicketsQuery - should return correct response`() {
        val auditoriumLayout = integrationDataTestHelper.getAuditoriumLayout(
            auditoriumId = integrationDataTestHelper.getAuditorium(
                id = UUID.randomUUID()
            )
        )

        val seat1 = integrationDataTestHelper.getSeat(
            originalId = 1,
            row = "1",
            number = "1",
            auditoriumId = auditoriumLayout.auditoriumId,
            auditoriumLayoutId = auditoriumLayout.id,
            type = SeatType.PREMIUM_PLUS
        )

        val seat2 = integrationDataTestHelper.getSeat(
            originalId = 2,
            row = "1",
            number = "2",
            auditoriumId = auditoriumLayout.auditoriumId,
            auditoriumLayoutId = auditoriumLayout.id,
            type = SeatType.REGULAR
        )

        val screening = integrationDataTestHelper.getScreening(
            originalId = 100,
            auditoriumId = auditoriumLayout.auditoriumId,
            auditoriumLayoutId = auditoriumLayout.id
        )

        val reservation1 = integrationDataTestHelper.getReservation(
            seatId = seat1.id,
            screeningId = screening.id
        )

        val reservation2 = integrationDataTestHelper.getReservation(
            seatId = seat2.id,
            screeningId = screening.id
        )

        val ticketPrice1 = integrationDataTestHelper.getTicketPrice(
            seatId = seat1.id,
            screeningId = screening.id,
            totalPrice = 10.toBigDecimal()
        )

        val ticketPrice2 = integrationDataTestHelper.getTicketPrice(
            seatId = seat2.id,
            screeningId = screening.id,
            totalPrice = 8.toBigDecimal()
        )

        val discountCard = integrationDataTestHelper.getDiscountCard(
            id = UUID.randomUUID(),
            code = "999888777",
            title = "FILM karta"
        )

        val ticket1 = integrationDataTestHelper.getTicket(
            originalId = 1000,
            screeningId = screening.id,
            reservationId = reservation1.id,
            ticketPriceId = ticketPrice1.id,
            receiptNumber = "000000001",
            isUsed = true
        )

        val ticket2 = integrationDataTestHelper.getTicket(
            originalId = 1001,
            screeningId = screening.id,
            reservationId = reservation2.id,
            ticketPriceId = ticketPrice2.id,
            receiptNumber = "000000002",
            isUsed = false
        )

        // unrelated ticket
        val ticket3 = integrationDataTestHelper.getTicket(
            originalId = 1002,
            screeningId = screening.id,
            reservationId = reservation2.id,
            ticketPriceId = ticketPrice2.id,
            receiptNumber = "000000002",
            isUsed = false
        )

        val productComponentCategory = integrationDataTestHelper.getProductComponentCategory(
            originalId = 1,
            title = "Okuliare"
        )
        val productComponent = integrationDataTestHelper.getProductComponent(
            originalId = 1,
            code = "05",
            title = "3D okuliare",
            unit = ProductComponentUnit.KS,
            stockQuantity = 100.toBigDecimal(),
            productComponentCategoryId = productComponentCategory.id
        )
        val productCategory = integrationDataTestHelper.getProductCategory(
            title = "Okuliare",
            code = "36"
        )
        val product = integrationDataTestHelper.getProduct(
            title = "3D Okuliare",
            price = 2.20.toBigDecimal(),
            productCategoryId = productCategory.id
        )
        integrationDataTestHelper.getProductComposition(
            productId = product.id,
            productComponentId = productComponent.id,
            amount = 1.toBigDecimal()
        )

        val basket1 = integrationDataTestHelper.getBasket(
            state = BasketState.PAID_ONLINE,
            variableSymbol = "1234567890",
            totalPrice = 22.4.toBigDecimal()
        )

        // unrelated basket
        val basket2 = integrationDataTestHelper.getBasket(
            state = BasketState.PAID,
            variableSymbol = null
        )

        val basketItem1 = integrationDataTestHelper.getBasketItem(
            basketId = basket1.id,
            ticketId = ticket1.id,
            type = BasketItemType.TICKET,
            price = 10.toBigDecimal(),
            isCancelled = false
        )

        val basketItem2 = integrationDataTestHelper.getBasketItem(
            basketId = basket1.id,
            ticketId = ticket2.id,
            type = BasketItemType.TICKET,
            price = 8.toBigDecimal(),
            isCancelled = true
        )

        val basketItem3 = integrationDataTestHelper.getBasketItem(
            basketId = basket1.id,
            productId = product.id,
            type = BasketItemType.PRODUCT,
            price = 4.4.toBigDecimal(),
            quantity = 2
        )

        val basketItem4 = integrationDataTestHelper.getBasketItem(
            basketId = basket2.id,
            ticketId = ticket3.id,
            type = BasketItemType.TICKET,
            price = 9.toBigDecimal(),
            isCancelled = false
        )

        integrationDataTestHelper.getDiscountCardUsage(
            discountCardId = discountCard.id,
            ticketBasketItemId = basketItem2.id
        )

        val response = underTest(
            GetWebBasketTicketsQuery(basket1.id)
        )

        response.shouldNotBeNull()
        response.id shouldBe basket1.id
        response.state shouldBe basket1.state
        response.items.size shouldBe 3
        response.variableSymbol shouldBe basket1.variableSymbol
        response.paidAt shouldBe basket1.paidAt
        response.totalPrice shouldBeEqualComparingTo basket1.totalPrice

        val item1 = response.items[0]
        item1.type shouldBe BasketItemType.TICKET
        item1.price shouldBe 10.toBigDecimal()
        item1.quantity shouldBe 1
        item1.isCancelled shouldBe false
        item1.product shouldBe null

        val ticketResponse1 = item1.ticket
        ticketResponse1.shouldNotBeNull()
        ticketResponse1.receiptNumber shouldBe ticket1.receiptNumber
        ticketResponse1.isUsed shouldBe ticket1.isUsed
        ticketResponse1.includes3dGlasses shouldBe ticket1.includes3dGlasses
        ticketResponse1.originalScreeningId shouldBe screening.originalId

        val ticketPriceResponse1 = ticketResponse1.ticketPrice
        ticketPriceResponse1.basePrice shouldBe ticketPrice1.basePrice
        ticketPriceResponse1.totalPrice shouldBe ticketPrice1.totalPrice

        val ticketDiscountCardResponse1 = ticketResponse1.discountCard
        ticketDiscountCardResponse1 shouldBe null

        val reservationResponse1 = ticketResponse1.reservation
        reservationResponse1.state shouldBe reservation1.state

        val seatResponse1 = reservationResponse1.seat
        seatResponse1.row shouldBe seat1.row
        seatResponse1.number shouldBe seat1.number
        seatResponse1.type shouldBe seat1.type

        val item2 = response.items[1]
        item2.type shouldBe BasketItemType.TICKET
        item2.price shouldBe 8.toBigDecimal()
        item2.quantity shouldBe 1
        item2.isCancelled shouldBe true
        item2.product shouldBe null

        val ticketResponse2 = item2.ticket
        ticketResponse2.shouldNotBeNull()
        ticketResponse2.receiptNumber shouldBe ticket2.receiptNumber
        ticketResponse2.isUsed shouldBe ticket2.isUsed
        ticketResponse2.includes3dGlasses shouldBe ticket2.includes3dGlasses
        ticketResponse2.originalScreeningId shouldBe screening.originalId

        val ticketPriceResponse2 = ticketResponse2.ticketPrice
        ticketPriceResponse2.basePrice shouldBe ticketPrice2.basePrice
        ticketPriceResponse2.totalPrice shouldBe ticketPrice2.totalPrice

        val ticketDiscountCardResponse2 = ticketResponse2.discountCard
        ticketDiscountCardResponse2.shouldNotBeNull()
        ticketDiscountCardResponse2.code shouldBe discountCard.code
        ticketDiscountCardResponse2.title shouldBe discountCard.title

        val reservationResponse2 = ticketResponse2.reservation
        reservationResponse2.state shouldBe reservation2.state

        val seatResponse2 = reservationResponse2.seat
        seatResponse2.row shouldBe seat2.row
        seatResponse2.number shouldBe seat2.number
        seatResponse2.type shouldBe seat2.type

        val item3 = response.items[2]
        item3.type shouldBe BasketItemType.PRODUCT
        item3.price shouldBe 4.4.toBigDecimal()
        item3.quantity shouldBe 2
        item3.isCancelled shouldBe false
        item3.ticket shouldBe null

        val productResponse1 = item3.product
        productResponse1.shouldNotBeNull()
        productResponse1.title shouldBe product.title
        productResponse1.price shouldBe product.price
    }

    @Test
    fun `test GetWebBasketTicketsQuery - basket not found - should throw exception`() {
        shouldThrow<BasketNotFoundException> {
            underTest(
                GetWebBasketTicketsQuery(UUID.randomUUID())
            )
        }
    }

    @Test
    fun `test GetWebBasketTicketsQuery - basket is not a online basket - should throw exception`() {
        val basket = integrationDataTestHelper.getBasket(
            state = BasketState.PAID,
            variableSymbol = null
        )

        shouldThrow<BasketNotFoundException> {
            underTest(
                GetWebBasketTicketsQuery(basket.id)
            )
        }
    }
}
