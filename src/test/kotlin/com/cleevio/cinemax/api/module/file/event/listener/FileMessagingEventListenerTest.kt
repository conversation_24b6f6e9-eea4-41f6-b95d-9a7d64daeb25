package com.cleevio.cinemax.api.module.file.event.listener

import com.cleevio.cinemax.api.common.integration.pubsub.constant.MessageType
import com.cleevio.cinemax.api.common.integration.pubsub.dto.MessagePayload
import com.cleevio.cinemax.api.common.service.PublisherService
import com.cleevio.cinemax.api.common.util.toJsonString
import com.cleevio.cinemax.api.module.file.constant.FileType
import com.cleevio.cinemax.api.module.file.event.FileImageUploadedEvent
import io.kotest.matchers.shouldBe
import io.mockk.every
import io.mockk.just
import io.mockk.mockk
import io.mockk.runs
import io.mockk.verify
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import java.util.UUID

class FileMessagingEventListenerTest {

    private val publisherService = mockk<PublisherService>()
    private val underTest = FileMessagingEventListener(publisherService)

    @BeforeEach
    fun setUp() {
        every { publisherService.publish(any()) } just runs
    }

    @Test
    fun `listenToFileCreatedEvent - should publish message`() {
        val fileId = UUID.randomUUID()
        val event = FileImageUploadedEvent(
            fileId = fileId,
            type = FileType.PRODUCT_IMAGE,
            originalName = "test-image.jpg",
            extension = "jpg",
            base64Content = "base64EncodedContent"
        )

        underTest.listenToFileImageUploadedEvent(event)

        verify(exactly = 1) {
            publisherService.publish(
                withArg<MessagePayload> {
                    it.type shouldBe MessageType.IMAGE_UPLOADED
                    it.data shouldBe event.toJsonString()
                }
            )
        }
    }
}
