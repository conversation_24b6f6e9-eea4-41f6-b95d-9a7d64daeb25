package com.cleevio.cinemax.api.module.basketitem.service

import com.cleevio.cinemax.api.DatabaseCleanup
import com.cleevio.cinemax.api.INTEGRATION_TEST_DATE_TIME
import com.cleevio.cinemax.api.IntegrationTest
import com.cleevio.cinemax.api.common.constant.PaymentType
import com.cleevio.cinemax.api.common.constant.REDUCED_TAX_RATE
import com.cleevio.cinemax.api.common.constant.SUPER_REDUCED_TAX_RATE
import com.cleevio.cinemax.api.common.util.associateNotNull
import com.cleevio.cinemax.api.common.util.toPercentage
import com.cleevio.cinemax.api.common.util.truncatedToSeconds
import com.cleevio.cinemax.api.module.auditorium.service.AuditoriumService
import com.cleevio.cinemax.api.module.auditoriumlayout.service.AuditoriumLayoutService
import com.cleevio.cinemax.api.module.basket.constant.BasketState
import com.cleevio.cinemax.api.module.basket.entity.Basket
import com.cleevio.cinemax.api.module.basket.event.ProductBasketItemAddedToBasketEvent
import com.cleevio.cinemax.api.module.basket.service.BasketReceiptNumbersService
import com.cleevio.cinemax.api.module.basket.service.BasketRepository
import com.cleevio.cinemax.api.module.basket.service.BasketService
import com.cleevio.cinemax.api.module.basket.service.command.CompleteBasketPaymentCommand
import com.cleevio.cinemax.api.module.basket.service.command.GenerateBasketReceiptNumbersCommand
import com.cleevio.cinemax.api.module.basket.service.command.InitiateBasketPaymentCommand
import com.cleevio.cinemax.api.module.basket.service.command.RecalculateBasketProductDiscountPricesCommand
import com.cleevio.cinemax.api.module.basketitem.constant.BasketItemType
import com.cleevio.cinemax.api.module.basketitem.controller.dto.AdminSearchProductBasketItemsFilter
import com.cleevio.cinemax.api.module.basketitem.controller.dto.AdminSearchProductBasketItemsResponse
import com.cleevio.cinemax.api.module.basketitem.entity.BasketItem
import com.cleevio.cinemax.api.module.basketitem.service.command.CancelBasketItemsCommand
import com.cleevio.cinemax.api.module.basketitem.service.command.CreateBasketItemCommand
import com.cleevio.cinemax.api.module.basketitem.service.command.CreateProductSalesBasketItemCommand
import com.cleevio.cinemax.api.module.basketitem.service.command.DeleteBasketItemCommand
import com.cleevio.cinemax.api.module.basketitem.service.query.AdminSearchProductBasketItemsQuery
import com.cleevio.cinemax.api.module.branch.service.BranchRepository
import com.cleevio.cinemax.api.module.card.service.CardRepository
import com.cleevio.cinemax.api.module.cardusage.entity.CardUsage
import com.cleevio.cinemax.api.module.cardusage.service.CardUsageRepository
import com.cleevio.cinemax.api.module.discountcard.constant.DiscountCardType
import com.cleevio.cinemax.api.module.discountcard.service.DiscountCardMssqlService
import com.cleevio.cinemax.api.module.discountcardusage.service.DiscountCardUsageJpaFinderService
import com.cleevio.cinemax.api.module.discountcardusage.service.DiscountCardUsageService
import com.cleevio.cinemax.api.module.discountcardusage.service.command.CreateDiscountCardUsageCommand
import com.cleevio.cinemax.api.module.discountcardusage.service.command.UpdateDiscountCardUsageCommand
import com.cleevio.cinemax.api.module.distributor.service.DistributorService
import com.cleevio.cinemax.api.module.movie.service.MovieService
import com.cleevio.cinemax.api.module.posconfiguration.service.PosConfigurationService
import com.cleevio.cinemax.api.module.pricecategory.service.PriceCategoryService
import com.cleevio.cinemax.api.module.pricecategoryitem.constant.PriceCategoryItemNumber
import com.cleevio.cinemax.api.module.pricecategoryitem.service.PriceCategoryItemService
import com.cleevio.cinemax.api.module.product.constant.ProductType
import com.cleevio.cinemax.api.module.product.service.ProductRepository
import com.cleevio.cinemax.api.module.product.service.ProductService
import com.cleevio.cinemax.api.module.productcategory.constant.ProductCategoryType
import com.cleevio.cinemax.api.module.productcategory.service.ProductCategoryRepository
import com.cleevio.cinemax.api.module.productcomponent.constant.ProductComponentUnit
import com.cleevio.cinemax.api.module.productcomponent.service.ProductComponentRepository
import com.cleevio.cinemax.api.module.productcomponentcategory.service.ProductComponentCategoryRepository
import com.cleevio.cinemax.api.module.productcomposition.service.ProductCompositionService
import com.cleevio.cinemax.api.module.reservation.event.ReservationCreatedEvent
import com.cleevio.cinemax.api.module.screening.service.ScreeningService
import com.cleevio.cinemax.api.module.screeningfee.service.ScreeningFeeService
import com.cleevio.cinemax.api.module.seat.constant.SeatType
import com.cleevio.cinemax.api.module.seat.service.SeatService
import com.cleevio.cinemax.api.util.createAuditorium
import com.cleevio.cinemax.api.util.createAuditoriumLayout
import com.cleevio.cinemax.api.util.createBasket
import com.cleevio.cinemax.api.util.createBranch
import com.cleevio.cinemax.api.util.createCard
import com.cleevio.cinemax.api.util.createDiscountCard
import com.cleevio.cinemax.api.util.createDistributor
import com.cleevio.cinemax.api.util.createMovie
import com.cleevio.cinemax.api.util.createPosConfiguration
import com.cleevio.cinemax.api.util.createPriceCategory
import com.cleevio.cinemax.api.util.createPriceCategoryItem
import com.cleevio.cinemax.api.util.createProduct
import com.cleevio.cinemax.api.util.createProductCategory
import com.cleevio.cinemax.api.util.createProductComponent
import com.cleevio.cinemax.api.util.createProductComponentCategory
import com.cleevio.cinemax.api.util.createProductComposition
import com.cleevio.cinemax.api.util.createScreening
import com.cleevio.cinemax.api.util.createScreeningFee
import com.cleevio.cinemax.api.util.createSeat
import com.cleevio.cinemax.api.util.createTicketBasketItemRequest
import com.cleevio.cinemax.api.util.mapToCreateBasketItemCommand
import com.cleevio.cinemax.api.util.mapToCreateOrUpdateAuditoriumCommand
import com.cleevio.cinemax.api.util.mapToCreateOrUpdateAuditoriumLayoutCommand
import com.cleevio.cinemax.api.util.mapToCreateOrUpdateDiscountCardCommand
import com.cleevio.cinemax.api.util.mapToCreateOrUpdateDistributorCommand
import com.cleevio.cinemax.api.util.mapToCreateOrUpdateMovieCommand
import com.cleevio.cinemax.api.util.mapToCreateOrUpdatePosConfigurationCommand
import com.cleevio.cinemax.api.util.mapToCreateOrUpdatePriceCategoryItemCommand
import com.cleevio.cinemax.api.util.mapToCreateOrUpdateProductCompositionCommand
import com.cleevio.cinemax.api.util.mapToCreateOrUpdateScreeningCommand
import com.cleevio.cinemax.api.util.mapToCreateOrUpdateScreeningFeeCommand
import com.cleevio.cinemax.api.util.mapToCreateOrUpdateSeatCommand
import com.cleevio.cinemax.api.util.mapToSyncCreateOrUpdatePriceCategoryCommand
import com.cleevio.cinemax.api.util.mapToSyncCreateOrUpdateProductCommand
import io.mockk.Runs
import io.mockk.every
import io.mockk.just
import org.junit.jupiter.api.BeforeAll
import org.junit.jupiter.api.Test
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.Arguments
import org.junit.jupiter.params.provider.MethodSource
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.data.domain.PageRequest
import org.springframework.data.domain.Pageable
import org.springframework.data.domain.Sort
import java.math.BigDecimal
import java.time.LocalTime
import java.util.UUID
import java.util.stream.Stream
import kotlin.test.assertEquals
import kotlin.test.assertNull

class AdminSearchProductBasketItemQueryServiceIT @Autowired constructor(
    private val underTest: AdminSearchProductBasketItemQueryService,
    private val basketItemJooqFinderService: BasketItemJooqFinderService,
    private val posConfigurationService: PosConfigurationService,
    private val auditoriumService: AuditoriumService,
    private val auditoriumLayoutService: AuditoriumLayoutService,
    private val seatService: SeatService,
    private val distributorService: DistributorService,
    private val movieService: MovieService,
    private val screeningService: ScreeningService,
    private val priceCategoryService: PriceCategoryService,
    private val priceCategoryItemService: PriceCategoryItemService,
    private val screeningFeeService: ScreeningFeeService,
    private val discountCardMssqlService: DiscountCardMssqlService,
    private val discountCardUsageService: DiscountCardUsageService,
    private val discountCardUsageJpaFinderService: DiscountCardUsageJpaFinderService,
    private val basketRepository: BasketRepository,
    private val basketService: BasketService,
    private val basketReceiptNumbersService: BasketReceiptNumbersService,
    private val basketItemService: BasketItemService,
    private val basketItemRepository: BasketItemRepository,
    private val productService: ProductService,
    private val productCategoryRepository: ProductCategoryRepository,
    private val productCompositionService: ProductCompositionService,
    private val productComponentCategoryRepository: ProductComponentCategoryRepository,
    private val productComponentRepository: ProductComponentRepository,
    private val branchRepository: BranchRepository,
    private val productRepository: ProductRepository,
    private val cardRepository: CardRepository,
    private val cardUsageRepository: CardUsageRepository,
) : IntegrationTest(DatabaseCleanup.AFTER_ALL) {

    @BeforeAll
    fun setUp() {
        every { productReceiptNumberGeneratorMock.generateProductReceiptNumber() } returns "0000000001"
        every { applicationEventPublisherMock.publishEvent(any<ProductBasketItemAddedToBasketEvent>()) } just Runs
        every {
            reservationMssqlFinderServiceMock.findByOriginalScreeningIdAndOriginalSeatId(
                any(),
                any()
            )
        } returns null
        every { applicationEventPublisherMock.publishEvent(any<ReservationCreatedEvent>()) } just Runs

        branchRepository.save(BRANCH_1)
        setOf(POS_CONFIGURATION_1, POS_CONFIGURATION_2).forEach {
            posConfigurationService.createOrUpdatePosConfiguration(mapToCreateOrUpdatePosConfigurationCommand(it))
        }
        auditoriumService.syncCreateOrUpdateAuditorium(mapToCreateOrUpdateAuditoriumCommand(AUDITORIUM_1))
        auditoriumLayoutService.syncCreateOrUpdateAuditoriumLayout(mapToCreateOrUpdateAuditoriumLayoutCommand(AUDITORIUM_LAYOUT_1))
        setOf(SEAT_1, SEAT_2).forEach {
            seatService.createOrUpdateSeat(mapToCreateOrUpdateSeatCommand(it))
        }
        distributorService.syncCreateOrUpdateDistributor(mapToCreateOrUpdateDistributorCommand(DISTRIBUTOR_1))
        movieService.syncCreateOrUpdateMovie(mapToCreateOrUpdateMovieCommand(MOVIE_1))
        priceCategoryService.syncCreateOrUpdatePriceCategory(mapToSyncCreateOrUpdatePriceCategoryCommand(PRICE_CATEGORY_1))
        priceCategoryItemService.createOrUpdatePriceCategoryItem(
            mapToCreateOrUpdatePriceCategoryItemCommand(PRICE_CATEGORY_ITEM_1, PRICE_CATEGORY_ITEM_1.priceCategoryId)
        )
        screeningService.syncCreateOrUpdateScreening(mapToCreateOrUpdateScreeningCommand(SCREENING_1))
        screeningFeeService.createOrUpdateScreeningFee(mapToCreateOrUpdateScreeningFeeCommand(SCREENING_FEE_1))

        productCategoryRepository.saveAll(setOf(PRODUCT_CATEGORY_1, PRODUCT_CATEGORY_2, PRODUCT_CATEGORY_3, PRODUCT_CATEGORY_4))
        productComponentCategoryRepository.saveAll(setOf(PRODUCT_COMPONENT_CATEGORY_1, PRODUCT_COMPONENT_CATEGORY_2))
        productComponentRepository.saveAll(setOf(PRODUCT_COMPONENT_1, PRODUCT_COMPONENT_2, PRODUCT_COMPONENT_3, PRODUCT_COMPONENT_4))

        productService.syncCreateOrUpdateProduct(mapToSyncCreateOrUpdateProductCommand(PRODUCT_1, PRODUCT_CATEGORY_1.id))
        setOf(PRODUCT_2, PRODUCT_3, PRODUCT_4).forEach {
            productService.syncCreateOrUpdateProduct(mapToSyncCreateOrUpdateProductCommand(it, PRODUCT_CATEGORY_2.id))
        }.also { productRepository.save(PRODUCT_4.apply { taxRate = PRODUCT_4.taxRate }) } // taxRate is not synced from MSSQL, necessary to add it explicitly

        setOf(PRODUCT_DISCOUNT_1, PRODUCT_DISCOUNT_2).forEach {
            productService.syncCreateOrUpdateProduct(mapToSyncCreateOrUpdateProductCommand(it, PRODUCT_CATEGORY_3.id))
        }
        productService.syncCreateOrUpdateProduct(mapToSyncCreateOrUpdateProductCommand(PRODUCT_DISCOUNT_3, PRODUCT_CATEGORY_4.id))
        setOf(PRODUCT_COMPOSITION_1, PRODUCT_COMPOSITION_2, PRODUCT_COMPOSITION_3, PRODUCT_COMPOSITION_4, PRODUCT_COMPOSITION_5, PRODUCT_COMPOSITION_6).forEach {
            productCompositionService.createOrUpdateProductComposition(
                mapToCreateOrUpdateProductCompositionCommand(it)
            )
        }
        setOf(DISCOUNT_CARD_1, DISCOUNT_CARD_2, DISCOUNT_CARD_3).forEach {
            discountCardMssqlService.createOrUpdateDiscountCard(mapToCreateOrUpdateDiscountCardCommand(it))
        }
        cardRepository.save(CARD_1)

        // two products, no discounts
        BASKET_1.createBasket()
        BASKET_1.addProductToBasket(1, PRODUCT_1.id, BasketItemType.PRODUCT)
        BASKET_1.addProductToBasket(2, PRODUCT_2.id, BasketItemType.PRODUCT)
        BASKET_1.payBasket(PaymentType.CASH, POS_CONFIGURATION_1.id)

        // one product, no discounts, cashless, another POS
        BASKET_2.createBasket()
        BASKET_2.addProductToBasket(3, PRODUCT_1.id, BasketItemType.PRODUCT, 2)
        BASKET_2.payBasket(PaymentType.CASHLESS, POS_CONFIGURATION_2.id)

        // one product, basket contains ticket
        BASKET_3.createBasket()
        BASKET_3.addProductToBasket(4, PRODUCT_1.id, BasketItemType.PRODUCT, 3)
        BASKET_3.addTicketToBasket(1, SEAT_1.id, SCREENING_1.id, PriceCategoryItemNumber.PRICE_1)
        BASKET_3.payBasket(PaymentType.CASH, POS_CONFIGURATION_1.id)

        // one product, one non-card product discount, one isolated group from voucher
        BASKET_4.createBasket()
        BASKET_4.addProductToBasket(5, PRODUCT_2.id, BasketItemType.PRODUCT)
        BASKET_4.addProductToBasket(6, PRODUCT_DISCOUNT_3.id, BasketItemType.PRODUCT_DISCOUNT)
        BASKET_4.addProductToBasket(7, PRODUCT_1.id, BasketItemType.PRODUCT, discountCardId = DISCOUNT_CARD_3.id, isolatedWith = PRODUCT_DISCOUNT_2.id, posConfigurationId = POS_CONFIGURATION_1.id)
        BASKET_4.addProductToBasket(8, PRODUCT_DISCOUNT_2.id, BasketItemType.PRODUCT_DISCOUNT, discountCardId = DISCOUNT_CARD_3.id, isolatedWith = PRODUCT_1.id, posConfigurationId = POS_CONFIGURATION_1.id)
        basketItemService.recalculateBasketProductDiscountPrices(
            RecalculateBasketProductDiscountPricesCommand(BASKET_4.id)
        )
        BASKET_4.payBasket(PaymentType.CASHLESS, POS_CONFIGURATION_1.id)

        // one product, two product discounts - one from discount card, one from voucher
        BASKET_5.createBasket()
        BASKET_5.addProductToBasket(
            originalId = 9,
            productId = PRODUCT_1.id,
            type = BasketItemType.PRODUCT
        )
        BASKET_5.addProductToBasket(
            originalId = 10,
            productId = PRODUCT_DISCOUNT_1.id,
            type = BasketItemType.PRODUCT_DISCOUNT,
            posConfigurationId = POS_CONFIGURATION_2.id,
            discountCardId = DISCOUNT_CARD_1.id
        )
        // active (highest) discount in a basket
        BASKET_5.addProductToBasket(
            originalId = 11,
            productId = PRODUCT_DISCOUNT_2.id,
            type = BasketItemType.PRODUCT_DISCOUNT,
            posConfigurationId = POS_CONFIGURATION_2.id,
            discountCardId = DISCOUNT_CARD_2.id,
            cardId = CARD_1.id
        )
        basketItemService.recalculateBasketProductDiscountPrices(
            RecalculateBasketProductDiscountPricesCommand(BASKET_5.id)
        )
        BASKET_5.payBasket(PaymentType.CASH, POS_CONFIGURATION_2.id)

        // cancelled and deleted product
        BASKET_6.createBasket()
        val basket6Item1 = BASKET_6.addProductToBasket(12, PRODUCT_1.id, BasketItemType.PRODUCT)
        BASKET_6.cancelBasketItem(13, basket6Item1.id)
        val basket6Item2 = BASKET_6.addProductToBasket(14, PRODUCT_2.id, BasketItemType.PRODUCT)
        BASKET_6.deleteBasketItem(basket6Item2.id)
        BASKET_6.payBasket(PaymentType.CASHLESS, POS_CONFIGURATION_2.id)

        // non-paid basket - shouldn't appear anywhere
        BASKET_7.createBasket()
        BASKET_7.addProductToBasket(15, PRODUCT_2.id, BasketItemType.PRODUCT, quantity = 5)

        // basket item has branchId = BRANCH_1.id
        BASKET_8.createBasket()
        BASKET_8.addProductSaleProductToBasket(16, PRODUCT_1.id, price = PRODUCT_1.price, branchId = BRANCH_1.id)
        BASKET_8.payBasket(PaymentType.CASH, POS_CONFIGURATION_1.id)

        // product in product
        BASKET_9.createBasket()
        BASKET_9.addProductToBasket(17, PRODUCT_4.id, BasketItemType.PRODUCT, quantity = 2)
        BASKET_9.payBasket(PaymentType.CASH, POS_CONFIGURATION_1.id)
    }

    @Test
    fun `test searchProductBasketItems - item with all attributes - should be mapped correctly`() {
        val result = underTest(
            AdminSearchProductBasketItemsQuery(
                pageable = Pageable.unpaged(),
                filter = AdminSearchProductBasketItemsFilter(
                    discountCardCode = CARD_1.code
                )
            )
        ).content.first()

        val expectedTotalPrice = PRODUCT_1.price.multiply(PRODUCT_DISCOUNT_2.discountPercentage!!.toPercentage()).negate()

        assertEquals(BasketItemType.PRODUCT_DISCOUNT, result.type)
        assertEquals("0000000001", result.productReceiptNumber)
        assertEquals(1, result.quantity)
        assertEquals(expectedTotalPrice, result.price)
        assertEquals(false, result.isCancelled)
        assertEquals("Bratislava Bory", result.branchName)
        assertEquals(BASKET_5.id, result.basket.id)
        assertEquals(BASKET_5.paidAt?.truncatedToSeconds(), result.basket.paidAt.truncatedToSeconds())
        assertEquals(PaymentType.CASH, result.basket.paymentType)
        assertEquals(POS_CONFIGURATION_2.id, result.basket.paymentPosConfiguration?.id)
        assertEquals(POS_CONFIGURATION_2.title, result.basket.paymentPosConfiguration?.title)
        assertEquals(BASKET_5.updatedBy, result.basket.updatedBy)
        assertEquals(PRODUCT_DISCOUNT_2.id, result.product.id)
        assertEquals(PRODUCT_DISCOUNT_2.title, result.product.title)
        assertEquals(PRODUCT_DISCOUNT_2.code, result.product.code)
        assertEquals(PRODUCT_CATEGORY_3.id, result.product.productCategory.id)
        assertEquals(PRODUCT_CATEGORY_3.type, result.product.productCategory.type)
        assertEquals(PRODUCT_CATEGORY_3.title, result.product.productCategory.title)
        assertEquals(CARD_1.id, result.discountCard?.id)
        assertEquals(CARD_1.type, result.discountCard?.type)
        assertEquals(CARD_1.code, result.discountCard?.code)
        assertEquals(CARD_1.title, result.discountCard?.title)
        assertEquals(CARD_1.id, result.card?.id)
        assertEquals(CARD_1.code, result.card?.code)
        assertEquals(CARD_1.title, result.card?.title)
    }

    @Test
    fun `test searchProductBasketItems - branch is not null - should be mapped correctly`() {
        val result = underTest(
            AdminSearchProductBasketItemsQuery(
                pageable = Pageable.unpaged(),
                filter = AdminSearchProductBasketItemsFilter(
                    branchIds = setOf(BRANCH_1.id),
                    productReceiptNumber = "55446678"
                )
            )
        ).content.first()

        val expectedTotalPrice = PRODUCT_1.price

        assertEquals(BasketItemType.PRODUCT, result.type)
        assertEquals("55446678", result.productReceiptNumber)
        assertEquals(1, result.quantity)
        assertEquals(expectedTotalPrice, result.price)
        assertEquals(false, result.isCancelled)
        assertEquals("Bratislava Bory", result.branchName)
        assertEquals(BASKET_8.id, result.basket.id)
        assertEquals(BASKET_8.paidAt?.truncatedToSeconds(), result.basket.paidAt.truncatedToSeconds())
        assertEquals(PaymentType.CASH, result.basket.paymentType)
        assertEquals(POS_CONFIGURATION_1.id, result.basket.paymentPosConfiguration?.id)
        assertEquals(POS_CONFIGURATION_1.title, result.basket.paymentPosConfiguration?.title)
        assertEquals(BASKET_8.updatedBy, result.basket.updatedBy)
        assertEquals(PRODUCT_1.id, result.product.id)
        assertEquals(PRODUCT_1.title, result.product.title)
        assertEquals(PRODUCT_1.code, result.product.code)
        assertEquals(PRODUCT_CATEGORY_1.id, result.product.productCategory.id)
        assertEquals(PRODUCT_CATEGORY_1.type, result.product.productCategory.type)
        assertEquals(PRODUCT_CATEGORY_1.title, result.product.productCategory.title)
        assertNull(result.discountCard?.id)
        assertNull(result.discountCard?.type)
        assertNull(result.discountCard?.code)
        assertNull(result.discountCard?.title)
    }

    @Test
    fun `test searchProductBasketItems - PRODUCT_IN_PRODUCT item - should be mapped correctly`() {
        val result = underTest(
            AdminSearchProductBasketItemsQuery(
                pageable = Pageable.unpaged(),
                filter = AdminSearchProductBasketItemsFilter(
                    productIds = setOf(PRODUCT_4.id)
                )
            )
        ).content.first()

        val expectedTotalPrice = PRODUCT_4.price.multiply(2.toBigDecimal())

        assertEquals(BasketItemType.PRODUCT, result.type)
        assertEquals("0000000001", result.productReceiptNumber)
        assertEquals(2, result.quantity)
        assertEquals(expectedTotalPrice, result.price)
        assertEquals(false, result.isCancelled)
        assertEquals("Bratislava Bory", result.branchName)
        assertEquals(BASKET_9.id, result.basket.id)
        assertEquals(BASKET_9.paidAt?.truncatedToSeconds(), result.basket.paidAt.truncatedToSeconds())
        assertEquals(PaymentType.CASH, result.basket.paymentType)
        assertEquals(POS_CONFIGURATION_1.id, result.basket.paymentPosConfiguration?.id)
        assertEquals(POS_CONFIGURATION_1.title, result.basket.paymentPosConfiguration?.title)
        assertEquals(BASKET_9.updatedBy, result.basket.updatedBy)
        assertEquals(PRODUCT_4.id, result.product.id)
        assertEquals(PRODUCT_4.title, result.product.title)
        assertEquals(PRODUCT_4.code, result.product.code)
        assertEquals(PRODUCT_CATEGORY_2.id, result.product.productCategory.id)
        assertEquals(PRODUCT_CATEGORY_2.type, result.product.productCategory.type)
        assertEquals(PRODUCT_CATEGORY_2.title, result.product.productCategory.title)
        assertNull(result.discountCard)
    }

    @ParameterizedTest
    @MethodSource("filtersAndExpectedOriginalIdsProvider")
    fun `test searchProductBasketItems - single filter - should search correct items`(
        filter: AdminSearchProductBasketItemsFilter,
        expectedResult: Set<Int>,
    ) {
        val response = underTest(
            AdminSearchProductBasketItemsQuery(
                pageable = Pageable.unpaged(),
                filter = filter
            )
        ).content

        // assert equality of sets here, because ordering is not the point of this test
        assertEquals(expectedResult, determineOriginalIdsFromIds(response).toSet())
    }

    @ParameterizedTest
    @MethodSource("sortsAndExpectedOriginalIdsProvider")
    fun `test searchProductBasketItems - pagination and sorting - should search correct items`(
        pageable: Pageable,
        expectedResult: List<Int>,
    ) {
        val response = underTest(
            AdminSearchProductBasketItemsQuery(
                pageable = pageable,
                filter = AdminSearchProductBasketItemsFilter()
            )
        ).content

        assertEquals(expectedResult, determineOriginalIdsFromIds(response))
    }

    private fun determineOriginalIdsFromIds(response: List<AdminSearchProductBasketItemsResponse>): List<Int> {
        val idToOriginalIdMap = basketItemJooqFinderService.findAll().associateNotNull { it.id to it.originalId }
        return response.map { it.id }.mapNotNull { idToOriginalIdMap[it] }
    }

    companion object {
        @JvmStatic
        fun filtersAndExpectedOriginalIdsProvider(): Stream<Arguments> {
            return Stream.of(
                Arguments.of(
                    AdminSearchProductBasketItemsFilter(),
                    setOf(1, 2, 3, 4, 5, 6, 7, 8, 9, 11, 12, 16, 17)
                ),
                Arguments.of(
                    AdminSearchProductBasketItemsFilter(
                        basketPaidAtFrom = BASKET_3.paidAt
                    ),
                    setOf(4, 5, 6, 7, 8, 9, 11, 12, 16, 17)
                ),
                Arguments.of(
                    AdminSearchProductBasketItemsFilter(
                        basketPaidAtTo = BASKET_4.paidAt
                    ),
                    setOf(1, 2, 3, 4, 5, 6, 7, 8)
                ),
                Arguments.of(
                    AdminSearchProductBasketItemsFilter(
                        basketPaidAtFrom = BASKET_3.paidAt,
                        basketPaidAtTo = BASKET_4.paidAt
                    ),
                    setOf(4, 5, 6, 7, 8)
                ),
                Arguments.of(
                    AdminSearchProductBasketItemsFilter(
                        posConfigurationIds = setOf(POS_CONFIGURATION_1.id)
                    ),
                    setOf(1, 2, 4, 5, 6, 7, 8, 16, 17)
                ),
                Arguments.of(
                    AdminSearchProductBasketItemsFilter(
                        productIds = setOf(PRODUCT_1.id, PRODUCT_2.id)
                    ),
                    setOf(1, 2, 3, 4, 5, 7, 9, 12, 16)
                ),
                Arguments.of(
                    AdminSearchProductBasketItemsFilter(
                        productCategoryIds = setOf(PRODUCT_CATEGORY_3.id)
                    ),
                    setOf(8, 11)
                ),
                Arguments.of(
                    AdminSearchProductBasketItemsFilter(
                        productCode = "00004"
                    ),
                    setOf(17)
                ),
                Arguments.of(
                    AdminSearchProductBasketItemsFilter(
                        paymentTypes = setOf(PaymentType.CASHLESS)
                    ),
                    setOf(3, 5, 6, 7, 8, 12)
                ),
                Arguments.of(
                    AdminSearchProductBasketItemsFilter(
                        discountCardCode = DISCOUNT_CARD_2.code
                    ),
                    setOf(11)
                ),
                Arguments.of(
                    AdminSearchProductBasketItemsFilter(
                        discountCardTypes = setOf(DiscountCardType.VOUCHER)
                    ),
                    setOf(7, 8, 11)
                ),
                Arguments.of(
                    AdminSearchProductBasketItemsFilter(
                        discountCardTitle = DISCOUNT_CARD_2.title
                    ),
                    setOf(11)
                ),
                Arguments.of(
                    AdminSearchProductBasketItemsFilter(
                        discountCardCode = CARD_1.code
                    ),
                    setOf(11)
                ),
                Arguments.of(
                    AdminSearchProductBasketItemsFilter(
                        discountCardTitle = CARD_1.title
                    ),
                    setOf(11)
                ),
                Arguments.of(
                    AdminSearchProductBasketItemsFilter(
                        isDiscount = true
                    ),
                    setOf(6, 8, 11)
                ),
                Arguments.of(
                    AdminSearchProductBasketItemsFilter(
                        isDiscount = false
                    ),
                    setOf(1, 2, 3, 4, 5, 7, 9, 12, 16, 17)
                ),
                Arguments.of(
                    AdminSearchProductBasketItemsFilter(
                        isCancelled = true
                    ),
                    setOf(12)
                ),
                Arguments.of(
                    AdminSearchProductBasketItemsFilter(
                        isCancelled = false
                    ),
                    setOf(1, 2, 3, 4, 5, 6, 7, 8, 9, 11, 16, 17)
                ),
                Arguments.of(
                    AdminSearchProductBasketItemsFilter(
                        branchIds = setOf(BRANCH_1.id)
                    ),
                    setOf(1, 2, 3, 9, 11, 12, 17, 16, 4, 5, 6, 7, 8)
                ),
                Arguments.of(
                    AdminSearchProductBasketItemsFilter(
                        basketUpdatedBy = setOf("anonymous")
                    ),
                    setOf(1, 2, 3, 4, 5, 6, 7, 8, 9, 11, 12, 16, 17)
                ),
                Arguments.of(
                    AdminSearchProductBasketItemsFilter(
                        basketUpdatedBy = setOf("monika")
                    ),
                    emptySet<Int>()
                )
            )
        }

        @JvmStatic
        fun sortsAndExpectedOriginalIdsProvider(): Stream<Arguments> {
            // when the items are sorted by common values, e.g. Basket.paidAt or Basket.paymentType, there has to be a default
            // sort to have deterministic results - this one's order is different from the default, so it was easier writing it
            val defaultSort = Sort.Order.asc("basketItem.createdAt")

            return Stream.of(
                Arguments.of(
                    PageRequest.of(0, 5, Sort.by(defaultSort)),
                    listOf(1, 2, 3, 4, 5)
                ),
                Arguments.of(
                    PageRequest.of(1, 5, Sort.by(defaultSort)),
                    listOf(6, 7, 8, 9, 11)
                ),
                Arguments.of(
                    PageRequest.of(0, 15, Sort.by(Sort.Order.desc("basketItem.type"), defaultSort)),
                    listOf(6, 8, 11, 1, 2, 3, 4, 5, 7, 9, 12, 16, 17)
                ),
                Arguments.of(
                    PageRequest.of(0, 15, Sort.by(Sort.Order.asc("basketItem.type"), defaultSort)),
                    listOf(1, 2, 3, 4, 5, 7, 9, 12, 16, 17, 6, 8, 11)
                ),
                Arguments.of(
                    PageRequest.of(0, 15, Sort.by(Sort.Order.asc("basketItem.quantity"), defaultSort)),
                    listOf(1, 2, 5, 6, 7, 8, 9, 11, 12, 16, 3, 17, 4)
                ),
                Arguments.of(
                    // 1: 10, 2: 1, 3: 20, 4: 30, 5: 0.9, 6: -0.1, 7: 10, 8: -10, 9: 10, 11: -10, 12: 10, 16: 10, 17: 14
                    PageRequest.of(0, 15, Sort.by(Sort.Order.desc("basketItem.price"), defaultSort)),
                    listOf(4, 3, 17, 1, 7, 9, 12, 16, 2, 5, 6, 8, 11)
                ),
                Arguments.of(
                    PageRequest.of(0, 15, Sort.by(Sort.Order.asc("branch.name"), defaultSort)),
                    listOf(1, 2, 3, 4, 5, 6, 7, 8, 9, 11, 12, 16, 17)
                ),
                Arguments.of(
                    PageRequest.of(0, 15, Sort.by(Sort.Order.desc("branch.name"), defaultSort)),
                    listOf(1, 2, 3, 4, 5, 6, 7, 8, 9, 11, 12, 16, 17)
                ),
                Arguments.of(
                    PageRequest.of(0, 15, Sort.by(Sort.Order.asc("basket.paidAt"), defaultSort)),
                    listOf(1, 2, 3, 4, 5, 6, 7, 8, 9, 11, 12, 16, 17)
                ),
                Arguments.of(
                    PageRequest.of(0, 15, Sort.by(Sort.Order.desc("basket.paidAt"), defaultSort)),
                    listOf(17, 16, 12, 9, 11, 5, 6, 7, 8, 4, 3, 1, 2)
                ),
                Arguments.of(
                    PageRequest.of(0, 15, Sort.by(Sort.Order.asc("basket.paymentType"), defaultSort)),
                    listOf(1, 2, 4, 9, 11, 16, 17, 3, 5, 6, 7, 8, 12)
                ),
                Arguments.of(
                    PageRequest.of(0, 15, Sort.by(Sort.Order.desc("basket.paymentType"), defaultSort)),
                    listOf(3, 5, 6, 7, 8, 12, 1, 2, 4, 9, 11, 16, 17)
                ),
                Arguments.of(
                    PageRequest.of(0, 15, Sort.by(Sort.Order.asc("basket.updatedBy"), defaultSort)),
                    listOf(1, 2, 3, 4, 5, 6, 7, 8, 9, 11, 12, 16, 17)
                ),
                Arguments.of(
                    PageRequest.of(0, 15, Sort.by(Sort.Order.asc("posConfiguration.title"), defaultSort)),
                    listOf(1, 2, 4, 5, 6, 7, 8, 16, 17, 3, 9, 11, 12)
                ),
                Arguments.of(
                    PageRequest.of(0, 15, Sort.by(Sort.Order.desc("posConfiguration.title"), defaultSort)),
                    listOf(3, 9, 11, 12, 1, 2, 4, 5, 6, 7, 8, 16, 17)
                ),

                Arguments.of(
                    PageRequest.of(0, 15, Sort.by(Sort.Order.asc("basketItem.isCancelled"), defaultSort)),
                    listOf(1, 2, 3, 4, 5, 6, 7, 8, 9, 11, 16, 17, 12)
                ),
                Arguments.of(
                    PageRequest.of(0, 15, Sort.by(Sort.Order.desc("basketItem.isCancelled"), defaultSort)),
                    listOf(12, 1, 2, 3, 4, 5, 6, 7, 8, 9, 11, 16, 17)
                ),
                Arguments.of(
                    // Popcorn XXL: 1, 3, 4, 7, 9, 12, 16
                    // Coca Cola: 2, 5
                    // COMBO 2Cola+2Cappy: 17
                    // VIP karta -20%: 10
                    // Voucher 100%: 8, 11
                    // Zlava 10%: 6
                    PageRequest.of(0, 15, Sort.by(Sort.Order.desc("product.title"), defaultSort)),
                    listOf(6, 8, 11, 1, 3, 4, 7, 9, 12, 16, 17, 2, 5)
                ),
                Arguments.of(
                    PageRequest.of(0, 15, Sort.by(Sort.Order.asc("product.title"), defaultSort)),
                    listOf(2, 5, 17, 1, 3, 4, 7, 9, 12, 16, 8, 11, 6)
                ),
                Arguments.of(
                    // Popcorn: 1, 3, 4, 7, 9, 12, 16
                    // Napoje: 2, 5, 17
                    // Zlava karta: 8, 10, 11
                    // Obecna sleva: 6
                    PageRequest.of(0, 15, Sort.by(Sort.Order.asc("productCategory.title"), defaultSort)),
                    listOf(2, 5, 17, 6, 1, 3, 4, 7, 9, 12, 16, 8, 11)
                ),
                Arguments.of(
                    // card 1 (VIP karta): 10
                    // card 2 (Wonka voucher): 11
                    // card 3 (Akce1 voucher): 7, 8
                    PageRequest.of(0, 15, Sort.by(Sort.Order.asc("discountCard.title"), defaultSort)),
                    listOf(7, 8, 11, 1, 2, 3, 4, 5, 6, 9, 12, 16, 17)
                ),
                Arguments.of(
                    PageRequest.of(0, 15, Sort.by(Sort.Order.desc("discountCard.type"), defaultSort)),
                    listOf(7, 8, 11, 1, 2, 3, 4, 5, 6, 9, 12, 16, 17)
                ),
                Arguments.of(
                    PageRequest.of(0, 15, Sort.by(Sort.Order.asc("card.title"), defaultSort)),
                    listOf(2, 5, 17, 7, 1, 4, 16, 8, 6, 3, 9, 12, 11)
                ),
                Arguments.of(
                    PageRequest.of(0, 15, Sort.by(Sort.Order.desc("card.type"), defaultSort)),
                    listOf(11, 8, 6, 17, 7, 1, 2, 3, 4, 5, 9, 12, 16)
                )
            )
        }
    }

    private fun Basket.addTicketToBasket(
        originalId: Int,
        seatId: UUID,
        screeningId: UUID,
        priceCategoryItemNumber: PriceCategoryItemNumber,
        primaryTicketDiscountId: UUID? = null,
        secondaryTicketDiscountId: UUID? = null,
        primaryDiscountCardId: UUID? = null,
        secondaryDiscountCardId: UUID? = null,
    ): BasketItem {
        return basketItemService.createBasketItem(
            mapToCreateBasketItemCommand(
                request = createTicketBasketItemRequest(
                    seatId = seatId,
                    screeningId = screeningId,
                    priceCategoryItem = priceCategoryItemNumber,
                    primaryTicketDiscountId = primaryTicketDiscountId,
                    secondaryTicketDiscountId = secondaryTicketDiscountId,
                    primaryDiscountCardId = primaryDiscountCardId,
                    secondaryDiscountCardId = secondaryDiscountCardId
                ),
                basketId = this.id,
                originalId = originalId
            )
        )
    }

    private fun Basket.addProductToBasket(
        originalId: Int,
        productId: UUID,
        type: BasketItemType,
        quantity: Int = 1,
        posConfigurationId: UUID? = null,
        discountCardId: UUID? = null,
        cardId: UUID? = null,
        isolatedWith: UUID? = null,
    ): BasketItem {
        val basketItem = basketItemService.createBasketItem(
            CreateBasketItemCommand(
                basketId = this.id,
                type = type,
                quantity = quantity,
                productId = productId,
                productIsolatedWithId = isolatedWith,
                originalId = originalId
            )
        )

        discountCardId?.let {
            val discountCardUsage = discountCardUsageJpaFinderService.findNonDeletedByDiscountCardIdAndBasketIsInModifiableState(discountCardId) ?: run {
                return@run discountCardUsageService.createDiscountCardUsage(
                    CreateDiscountCardUsageCommand(
                        discountCardId = it,
                        basketId = basketItem.basketId,
                        posConfigurationId = posConfigurationId!!
                    )
                )
            }

            discountCardUsageService.updateDiscountCardUsage(
                UpdateDiscountCardUsageCommand(
                    discountCardUsageId = discountCardUsage.id,
                    basketItemId = basketItem.id
                )
            )
        }

        cardId?.let {
            cardUsageRepository.save(
                CardUsage(
                    cardId = it,
                    basketId = basketItem.basketId,
                    basketItemId = basketItem.id,
                    originalId = 100,
                    discountPercentage = null
                )
            )
        }

        return basketItem
    }

    private fun Basket.addProductSaleProductToBasket(
        itemOriginalId: Int,
        productId: UUID,
        type: BasketItemType = BasketItemType.PRODUCT,
        price: BigDecimal,
        quantity: Int = 1,
        receiptNumber: String = "55446678",
        isCancelled: Boolean = false,
        branchId: UUID,
    ): BasketItem {
        val basketItem = basketItemService.createProductSalesBasketItem(
            CreateProductSalesBasketItemCommand(
                basketId = this.id,
                productId = productId,
                type = type,
                price = price,
                quantity = quantity,
                receiptNumber = receiptNumber,
                isCancelled = isCancelled,
                branchId = branchId
            )
        )

        return basketItemRepository.save(basketItem.apply { originalId = itemOriginalId })
    }

    private fun Basket.payBasket(
        paymentType: PaymentType,
        posConfigurationId: UUID,
    ) {
        basketService.initiateBasketPayment(
            InitiateBasketPaymentCommand(
                basketId = this.id,
                posConfigurationId = posConfigurationId,
                paymentType = paymentType
            )
        )
        basketReceiptNumbersService.generateBasketReceiptNumbers(
            GenerateBasketReceiptNumbersCommand(
                basketId = this.id
            )
        )
        this.paidAt = basketService.completeBasketPayment(
            CompleteBasketPaymentCommand(
                basketId = this.id
            )
        ).paidAt
    }

    private fun Basket.cancelBasketItem(originalId: Int, basketItemId: UUID) {
        val cancelledBasketItemIds = basketItemService.cancelBasketItems(
            command = CancelBasketItemsCommand(
                basketItemIds = setOf(basketItemId),
                printReceipt = false
            )
        )
        val cancelledBasketItem =
            basketItemRepository.findAllByBasketIdAndDeletedAtIsNullAndCancelledBasketItemIdIsNotNull(
                basketId = cancelledBasketItemIds.first()
            ).first()

        basketItemRepository.save(
            cancelledBasketItem.apply { this.originalId = originalId }
        )
    }

    private fun Basket.deleteBasketItem(basketItemId: UUID) {
        basketItemService.deleteBasketItem(
            command = DeleteBasketItemCommand(
                basketId = this.id,
                basketItemId = basketItemId
            )
        )
    }

    private fun Basket.createBasket() = basketRepository.save(this)
}

// baskets
private val BASKET_1 = createBasket(state = BasketState.OPEN, totalPrice = 0.toBigDecimal())
private val BASKET_2 = createBasket(state = BasketState.OPEN, totalPrice = 0.toBigDecimal())
private val BASKET_3 = createBasket(state = BasketState.OPEN, totalPrice = 0.toBigDecimal())
private val BASKET_4 = createBasket(state = BasketState.OPEN, totalPrice = 0.toBigDecimal())
private val BASKET_5 = createBasket(state = BasketState.OPEN, totalPrice = 0.toBigDecimal())
private val BASKET_6 = createBasket(state = BasketState.OPEN, totalPrice = 0.toBigDecimal())
private val BASKET_7 = createBasket(state = BasketState.OPEN, totalPrice = 0.toBigDecimal())
private val BASKET_8 = createBasket(state = BasketState.OPEN, totalPrice = 0.toBigDecimal())
private val BASKET_9 = createBasket(state = BasketState.OPEN, totalPrice = 0.toBigDecimal())

private val BRANCH_1 = createBranch()
private val POS_CONFIGURATION_1 = createPosConfiguration(title = "pokl1")
private val POS_CONFIGURATION_2 = createPosConfiguration(title = "pokl2", macAddress = "EE:DD:CC:BB:AA")

private val PRODUCT_CATEGORY_1 = createProductCategory(
    title = "Popcorn",
    type = ProductCategoryType.PRODUCT
)
private val PRODUCT_CATEGORY_2 = createProductCategory(
    originalId = 2,
    title = "Napoje",
    type = ProductCategoryType.PRODUCT
)
private val PRODUCT_CATEGORY_3 = createProductCategory(
    originalId = 3,
    title = "Zlava karta",
    type = ProductCategoryType.DISCOUNT
)
private val PRODUCT_CATEGORY_4 = createProductCategory(
    originalId = 4,
    title = "Obecna sleva",
    type = ProductCategoryType.DISCOUNT
)
private val PRODUCT_COMPONENT_CATEGORY_1 = createProductComponentCategory()
private val PRODUCT_COMPONENT_CATEGORY_2 = createProductComponentCategory(originalId = 2, code = "02", taxRate = REDUCED_TAX_RATE)
private val PRODUCT_COMPONENT_1 = createProductComponent(
    originalId = 1,
    code = "1",
    title = "Kukurica",
    stockQuantity = 100.toBigDecimal(),
    productComponentCategoryId = PRODUCT_COMPONENT_CATEGORY_1.id,
    unit = ProductComponentUnit.KG
)
private val PRODUCT_COMPONENT_2 = createProductComponent(
    originalId = 2,
    code = "2",
    title = "Kelimek 0.3l",
    stockQuantity = 100.toBigDecimal(),
    productComponentCategoryId = PRODUCT_COMPONENT_CATEGORY_1.id,
    unit = ProductComponentUnit.KS
)
private val PRODUCT_COMPONENT_3 = createProductComponent(
    originalId = 3,
    code = "3",
    title = "Sirup/stava",
    stockQuantity = 100.toBigDecimal(),
    productComponentCategoryId = PRODUCT_COMPONENT_CATEGORY_1.id,
    unit = ProductComponentUnit.L
)
private val PRODUCT_COMPONENT_4 = createProductComponent(
    title = "Cappy male",
    stockQuantity = 100.toBigDecimal(),
    productComponentCategoryId = PRODUCT_COMPONENT_CATEGORY_2.id,
    unit = ProductComponentUnit.KS,
    purchasePrice = 1.1.toBigDecimal()
)
private val PRODUCT_1 = createProduct(
    originalId = 1,
    productCategoryId = PRODUCT_CATEGORY_1.id,
    title = "Popcorn XXL",
    soldInBuffet = true,
    soldInCafe = false,
    soldInVip = false
)
private val PRODUCT_2 = createProduct(
    originalId = 2,
    productCategoryId = PRODUCT_CATEGORY_2.id,
    title = "Coca Cola 0.3l",
    price = 1.toBigDecimal(),
    soldInBuffet = false,
    soldInCafe = true,
    soldInVip = false
)
private val PRODUCT_3 = createProduct(
    originalId = 3,
    productCategoryId = PRODUCT_CATEGORY_2.id,
    title = "Cappy",
    price = 2.2.toBigDecimal(),
    soldInBuffet = false,
    soldInCafe = false,
    soldInVip = true
)
private val PRODUCT_4 = createProduct(
    originalId = 4,
    type = ProductType.PRODUCT_IN_PRODUCT,
    productCategoryId = PRODUCT_CATEGORY_2.id,
    title = "COMBO 2Cola+2Cappy",
    price = 7.5.toBigDecimal(),
    taxRate = SUPER_REDUCED_TAX_RATE,
    soldInBuffet = true,
    soldInCafe = true,
    soldInVip = true
)
private val PRODUCT_DISCOUNT_1 = createProduct(
    originalId = 5,
    productCategoryId = PRODUCT_CATEGORY_3.id,
    title = "VIP karta 20%",
    price = BigDecimal.ZERO,
    discountPercentage = 20,
    soldInBuffet = false,
    soldInCafe = false,
    soldInVip = true
)
private val PRODUCT_DISCOUNT_2 = createProduct(
    originalId = 6,
    productCategoryId = PRODUCT_CATEGORY_3.id,
    title = "Voucher 100%",
    type = ProductType.ADDITIONAL_SALE,
    price = BigDecimal.ZERO,
    discountPercentage = 100,
    soldInBuffet = true,
    soldInCafe = true,
    soldInVip = false
)
private val PRODUCT_DISCOUNT_3 = createProduct(
    originalId = 7,
    productCategoryId = PRODUCT_CATEGORY_4.id,
    title = "Zlava 10%",
    type = ProductType.ADDITIONAL_SALE,
    price = BigDecimal.ZERO,
    discountPercentage = 10,
    soldInBuffet = true,
    soldInCafe = false,
    soldInVip = true
)
private val PRODUCT_COMPOSITION_1 = createProductComposition(
    originalId = 1,
    productId = PRODUCT_1.id,
    productComponentId = PRODUCT_COMPONENT_1.id,
    amount = 0.2.toBigDecimal()
)
private val PRODUCT_COMPOSITION_2 = createProductComposition(
    originalId = 2,
    productId = PRODUCT_2.id,
    productComponentId = PRODUCT_COMPONENT_2.id,
    amount = BigDecimal.ONE
)
private val PRODUCT_COMPOSITION_3 = createProductComposition(
    originalId = 3,
    productId = PRODUCT_2.id,
    productComponentId = PRODUCT_COMPONENT_3.id,
    amount = 0.0078.toBigDecimal()
)

// Cappy
private val PRODUCT_COMPOSITION_4 = createProductComposition(
    originalId = 4,
    productId = PRODUCT_3.id,
    productComponentId = PRODUCT_COMPONENT_4.id,
    amount = 1.toBigDecimal()
)

// COMBO compositions
private val PRODUCT_COMPOSITION_5 = createProductComposition(
    originalId = 5,
    productId = PRODUCT_4.id,
    productInProductId = PRODUCT_2.id,
    amount = 2.toBigDecimal(),
    productInProductPrice = PRODUCT_2.price.minus(1.toBigDecimal()),
    productInProductFlagshipPrice = PRODUCT_2.price.minus(1.toBigDecimal())
)
private val PRODUCT_COMPOSITION_6 = createProductComposition(
    originalId = 6,
    productId = PRODUCT_4.id,
    productInProductId = PRODUCT_3.id,
    amount = 2.toBigDecimal(),
    productInProductPrice = PRODUCT_3.price.minus(1.toBigDecimal()),
    productInProductFlagshipPrice = PRODUCT_2.price.minus(1.toBigDecimal())
)
private val DISCOUNT_CARD_1 = createDiscountCard(
    originalId = 1,
    ticketDiscountId = null,
    productDiscountId = PRODUCT_DISCOUNT_1.id,
    title = "VIP karta",
    code = "44444"
)
private val DISCOUNT_CARD_2 = createDiscountCard(
    originalId = 2,
    ticketDiscountId = null,
    productDiscountId = PRODUCT_DISCOUNT_2.id,
    title = "Wonka voucher",
    code = "55555",
    type = DiscountCardType.VOUCHER
)
private val DISCOUNT_CARD_3 = createDiscountCard( // provides isolated group
    originalId = 3,
    ticketDiscountId = null,
    productDiscountId = PRODUCT_DISCOUNT_2.id,
    productId = PRODUCT_1.id,
    title = "Akce1",
    code = "11111",
    type = DiscountCardType.VOUCHER
)
private val CARD_1 = createCard()

// necessary data for ticket basket item - just to have it covered
private val AUDITORIUM_1 = createAuditorium(originalId = 1, title = "Sála IMAX", code = "IMAX")
private val AUDITORIUM_LAYOUT_1 = createAuditoriumLayout(originalId = 1, auditoriumId = AUDITORIUM_1.id)
private val SEAT_1 = createSeat(
    originalId = 1,
    auditoriumId = AUDITORIUM_1.id,
    auditoriumLayoutId = AUDITORIUM_LAYOUT_1.id,
    type = SeatType.REGULAR
)
private val SEAT_2 = createSeat(
    originalId = 2,
    auditoriumId = AUDITORIUM_1.id,
    auditoriumLayoutId = AUDITORIUM_LAYOUT_1.id,
    type = SeatType.REGULAR
)
private val DISTRIBUTOR_1 = createDistributor(originalId = 1, title = "Best movies ever, s.r.o.")
private val MOVIE_1 = createMovie(
    originalId = 1,
    rawTitle = "Star Wars: Episode I – The Phantom Menace 2D (CT)",
    title = "Star Wars: Episode I – The Phantom Menace",
    distributorId = DISTRIBUTOR_1.id
)
private val PRICE_CATEGORY_1 = createPriceCategory(originalId = 1, title = "Do 17h")
private val PRICE_CATEGORY_ITEM_1 = createPriceCategoryItem(
    priceCategoryId = PRICE_CATEGORY_1.id,
    number = PriceCategoryItemNumber.PRICE_1,
    price = 5.toBigDecimal()
)
private val SCREENING_1 = createScreening(
    originalId = 1,
    date = INTEGRATION_TEST_DATE_TIME.toLocalDate().plusDays(1),
    time = LocalTime.of(20, 0),
    auditoriumId = AUDITORIUM_1.id,
    auditoriumLayoutId = AUDITORIUM_LAYOUT_1.id,
    movieId = MOVIE_1.id,
    priceCategoryId = PRICE_CATEGORY_1.id
)
private val SCREENING_FEE_1 = createScreeningFee(
    originalScreeningId = 1,
    screeningId = SCREENING_1.id,
    surchargeVip = 0.toBigDecimal(),
    surchargePremium = 0.toBigDecimal(),
    surchargeImax = 1.toBigDecimal(),
    surchargeUltraX = 0.toBigDecimal(),
    surchargeDBox = 0.toBigDecimal(),
    serviceFeeVip = 0.toBigDecimal(),
    serviceFeePremium = 1.toBigDecimal(),
    serviceFeeImax = 0.toBigDecimal(),
    serviceFeeUltraX = 0.toBigDecimal(),
    serviceFeeGeneral = 0.toBigDecimal()
)
