package com.cleevio.cinemax.api.module.employee.service

import com.cleevio.cinemax.api.MssqlIntegrationTest
import com.cleevio.cinemax.api.module.employee.constant.EmployeeRole
import com.cleevio.cinemax.api.module.employee.service.command.CreateOrUpdateEmployeeCommand
import com.cleevio.cinemax.api.module.synchronizationfrommssql.constant.SynchronizationFromMssqlType
import com.cleevio.cinemax.api.module.synchronizationfrommssql.entity.SynchronizationFromMssql
import com.cleevio.cinemax.api.module.synchronizationfrommssql.service.command.CreateOrUpdateSynchronizationFromMssqlCommand
import com.cleevio.cinemax.api.util.createEmployee
import io.mockk.Runs
import io.mockk.every
import io.mockk.just
import io.mockk.verify
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.test.context.jdbc.Sql
import org.springframework.test.context.jdbc.SqlConfig
import org.springframework.test.context.jdbc.SqlGroup
import java.time.LocalDateTime
import kotlin.test.assertEquals
import kotlin.test.assertTrue

@SqlGroup(
    Sql(
        scripts = [
            "/db/mssql-cinemax/test/init_mssql_employee_with_data.sql"
        ],
        config = SqlConfig(
            dataSource = "mssqlCinemaxDataSource",
            transactionManager = "mssqlCinemaxTransactionManager"
        )
    ),
    Sql(
        scripts = [
            "/db/mssql-cinemax/test/drop_mssql_employee_and_role.sql"
        ],
        config = SqlConfig(
            dataSource = "mssqlCinemaxDataSource",
            transactionManager = "mssqlCinemaxTransactionManager"
        ),
        executionPhase = Sql.ExecutionPhase.AFTER_TEST_METHOD
    )
)
class EmployeeMssqlSynchronizationServiceIT @Autowired constructor(
    private val underTest: EmployeeMssqlSynchronizationService,
) : MssqlIntegrationTest() {

    @Test
    fun `test synchronize all - no PSQL employees, 4 MSSQL employees - should create 4 employees`() {
        every { synchronizationFromMssqlFinderServiceMock.findByType(any()) } returns null
        every { employeeMssqlBuffetFinderRepositoryMock.findAllByUpdatedAtGt() } returns listOf()
        every { employeeFinderServiceMock.findByUsername(any()) } returns null
        every { employeeServiceMock.syncCreateOrUpdateEmployee(any()) } just Runs
        every { synchronizationFromMssqlServiceMock.createOrUpdate(any()) } returns SYNCHRONIZATION_FROM_MSSQL

        underTest.synchronizeAll()

        val commandCaptor = mutableListOf<CreateOrUpdateEmployeeCommand>()
        val usernameCaptor = mutableListOf<String>()

        verify { synchronizationFromMssqlFinderServiceMock.findByType(SynchronizationFromMssqlType.EMPLOYEE) }
        verify { employeeMssqlBuffetFinderRepositoryMock.findAllByUpdatedAtGt() }
        verify { employeeFinderServiceMock.findByUsername(capture(usernameCaptor)) }
        verify { employeeServiceMock.syncCreateOrUpdateEmployee(capture(commandCaptor)) }
        verify {
            synchronizationFromMssqlServiceMock.createOrUpdate(
                CreateOrUpdateSynchronizationFromMssqlCommand(
                    type = SynchronizationFromMssqlType.EMPLOYEE,
                    lastSynchronization = EMPLOYEE_4_UPDATED_AT
                )
            )
        }

        assertTrue(commandCaptor.size == 4)
        assertEmployeeEquals(EXPECTED_COMMAND_1, commandCaptor[0])
        assertEmployeeEquals(EXPECTED_COMMAND_2, commandCaptor[1])
        assertEmployeeEquals(EXPECTED_COMMAND_3, commandCaptor[2])
        assertEmployeeEquals(EXPECTED_COMMAND_4, commandCaptor[3])

        assertTrue(usernameCaptor.size == 4)
        assertEquals(EXPECTED_COMMAND_1.username, usernameCaptor[0])
        assertEquals(EXPECTED_COMMAND_2.username, usernameCaptor[1])
        assertEquals(EXPECTED_COMMAND_3.username, usernameCaptor[2])
        assertEquals(EXPECTED_COMMAND_4.username, usernameCaptor[3])
    }

    @Test
    fun `test synchronize all - 3 PSQL employees, 4 MSSQL employees - should create 1 employee`() {
        every { synchronizationFromMssqlFinderServiceMock.findByType(any()) } returns EMPLOYEE_3_UPDATED_AT
        every { employeeMssqlBuffetFinderRepositoryMock.findAllByUpdatedAtGt() } returns listOf()
        every {
            employeeFinderServiceMock.findByUsername(EXPECTED_COMMAND_1.username)
        } returns createEmployee(username = EXPECTED_COMMAND_1.username, originalId = EXPECTED_COMMAND_1.originalId)
        every {
            employeeFinderServiceMock.findByUsername(EXPECTED_COMMAND_2.username)
        } returns createEmployee(username = EXPECTED_COMMAND_2.username, originalId = EXPECTED_COMMAND_2.originalId)
        every {
            employeeFinderServiceMock.findByUsername(EXPECTED_COMMAND_3.username)
        } returns createEmployee(username = EXPECTED_COMMAND_3.username, originalId = EXPECTED_COMMAND_3.originalId)
        every {
            employeeFinderServiceMock.findByUsername(EXPECTED_COMMAND_4.username)
        } returns createEmployee(username = EXPECTED_COMMAND_4.username, originalId = EXPECTED_COMMAND_4.originalId)
        every { employeeServiceMock.syncCreateOrUpdateEmployee(any()) } just Runs
        every { synchronizationFromMssqlServiceMock.createOrUpdate(any()) } returns SYNCHRONIZATION_FROM_MSSQL

        underTest.synchronizeAll()

        val commandCaptor = mutableListOf<CreateOrUpdateEmployeeCommand>()
        val usernameCaptor = mutableListOf<String>()

        verify { synchronizationFromMssqlFinderServiceMock.findByType(SynchronizationFromMssqlType.EMPLOYEE) }
        verify { employeeMssqlBuffetFinderRepositoryMock.findAllByUpdatedAtGt() }
        verify { employeeFinderServiceMock.findByUsername(capture(usernameCaptor)) }
        verify { employeeServiceMock.syncCreateOrUpdateEmployee(capture(commandCaptor)) }
        verify {
            synchronizationFromMssqlServiceMock.createOrUpdate(
                CreateOrUpdateSynchronizationFromMssqlCommand(
                    type = SynchronizationFromMssqlType.EMPLOYEE,
                    lastSynchronization = EMPLOYEE_4_UPDATED_AT
                )
            )
        }

        assertTrue(commandCaptor.size == 1)
        assertEmployeeEquals(EXPECTED_COMMAND_4, commandCaptor[0])

        assertTrue(usernameCaptor.size == 1)
        assertEquals(EXPECTED_COMMAND_4.username, usernameCaptor[0])
    }

    private fun assertEmployeeEquals(expected: CreateOrUpdateEmployeeCommand, actual: CreateOrUpdateEmployeeCommand) {
        assertEquals(expected.originalId, actual.originalId)
        assertEquals(expected.username, actual.username)
        assertEquals(expected.fullName, actual.fullName)
        assertEquals(expected.posName, actual.posName)
        assertEquals(expected.password, actual.password)
        assertEquals(expected.passwordReset, actual.passwordReset)
        assertEquals(expected.role, actual.role)
        assertEquals(expected.accessibleAt, actual.accessibleAt)
    }
}

private val EMPLOYEE_3_UPDATED_AT = LocalDateTime.of(2022, 6, 25, 12, 59, 0)
private val EMPLOYEE_4_UPDATED_AT = LocalDateTime.of(2023, 7, 27, 22, 1, 0)
private val EXPECTED_COMMAND_1 = CreateOrUpdateEmployeeCommand(
    originalId = 1,
    originalBuffetId = 1,
    username = "pokl1",
    fullName = null,
    posName = "pokl1",
    password = "a",
    passwordReset = false,
    role = EmployeeRole.CASHIER,
    accessibleAt = LocalDateTime.of(2021, 11, 24, 1, 21, 0, 750000000)
)
private val EXPECTED_COMMAND_2 = CreateOrUpdateEmployeeCommand(
    originalId = 2,
    originalBuffetId = 2,
    username = "sisa",
    fullName = "Silvia Kadášová",
    posName = null,
    password = "sisa",
    passwordReset = false,
    role = EmployeeRole.MANAGER,
    accessibleAt = LocalDateTime.of(2021, 11, 24, 1, 21, 0, 750000000)
)
private val EXPECTED_COMMAND_3 = CreateOrUpdateEmployeeCommand(
    originalId = 3,
    originalBuffetId = 3,
    username = "lauren",
    fullName = "Laura Furdíková",
    posName = "lauren",
    password = "lauren",
    passwordReset = false,
    role = EmployeeRole.MANAGER,
    accessibleAt = null
)
private val EXPECTED_COMMAND_4 = CreateOrUpdateEmployeeCommand(
    originalId = 4,
    originalBuffetId = 4,
    username = "janko",
    fullName = "Ján Brutenic",
    posName = "janko",
    password = "janko",
    passwordReset = false,
    role = EmployeeRole.CASHIER,
    accessibleAt = null
)
private val SYNCHRONIZATION_FROM_MSSQL = SynchronizationFromMssql(
    type = SynchronizationFromMssqlType.EMPLOYEE,
    lastSynchronization = LocalDateTime.MIN,
    lastHeartbeat = null
)
