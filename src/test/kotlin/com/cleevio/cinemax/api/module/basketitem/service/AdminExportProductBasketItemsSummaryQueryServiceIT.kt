package com.cleevio.cinemax.api.module.basketitem.service

import com.cleevio.cinemax.api.DatabaseCleanup
import com.cleevio.cinemax.api.INTEGRATION_TEST_DATE_TIME
import com.cleevio.cinemax.api.IntegrationTest
import com.cleevio.cinemax.api.common.constant.ExportFormat
import com.cleevio.cinemax.api.common.constant.NO_TAX_RATE
import com.cleevio.cinemax.api.common.constant.PaymentType
import com.cleevio.cinemax.api.common.constant.REDUCED_TAX_RATE
import com.cleevio.cinemax.api.common.constant.SUPER_REDUCED_TAX_RATE
import com.cleevio.cinemax.api.common.util.isEqualTo
import com.cleevio.cinemax.api.module.auditorium.service.AuditoriumService
import com.cleevio.cinemax.api.module.auditoriumlayout.service.AuditoriumLayoutService
import com.cleevio.cinemax.api.module.basket.constant.BasketState
import com.cleevio.cinemax.api.module.basket.entity.Basket
import com.cleevio.cinemax.api.module.basket.event.ProductBasketItemAddedToBasketEvent
import com.cleevio.cinemax.api.module.basket.service.BasketReceiptNumbersService
import com.cleevio.cinemax.api.module.basket.service.BasketRepository
import com.cleevio.cinemax.api.module.basket.service.BasketService
import com.cleevio.cinemax.api.module.basket.service.command.CompleteBasketPaymentCommand
import com.cleevio.cinemax.api.module.basket.service.command.GenerateBasketReceiptNumbersCommand
import com.cleevio.cinemax.api.module.basket.service.command.InitiateBasketPaymentCommand
import com.cleevio.cinemax.api.module.basket.service.command.RecalculateBasketProductDiscountPricesCommand
import com.cleevio.cinemax.api.module.basketitem.constant.BasketItemType
import com.cleevio.cinemax.api.module.basketitem.controller.dto.AdminSearchProductBasketItemsFilter
import com.cleevio.cinemax.api.module.basketitem.entity.BasketItem
import com.cleevio.cinemax.api.module.basketitem.event.ProductWithPackagingDepositFoundEvent
import com.cleevio.cinemax.api.module.basketitem.model.ProductBasketItemExportSummaryRecordModel
import com.cleevio.cinemax.api.module.basketitem.service.command.CancelBasketItemsCommand
import com.cleevio.cinemax.api.module.basketitem.service.command.CreateBasketItemCommand
import com.cleevio.cinemax.api.module.basketitem.service.command.CreatePackagingDepositBasketItemCommand
import com.cleevio.cinemax.api.module.basketitem.service.command.DeleteBasketItemCommand
import com.cleevio.cinemax.api.module.basketitem.service.query.AdminExportProductBasketItemsQuery
import com.cleevio.cinemax.api.module.branch.service.BranchRepository
import com.cleevio.cinemax.api.module.discountcard.constant.DiscountCardType
import com.cleevio.cinemax.api.module.discountcard.service.DiscountCardMssqlService
import com.cleevio.cinemax.api.module.discountcardusage.service.DiscountCardUsageJpaFinderService
import com.cleevio.cinemax.api.module.discountcardusage.service.DiscountCardUsageService
import com.cleevio.cinemax.api.module.discountcardusage.service.command.CreateDiscountCardUsageCommand
import com.cleevio.cinemax.api.module.discountcardusage.service.command.UpdateDiscountCardUsageCommand
import com.cleevio.cinemax.api.module.distributor.service.DistributorService
import com.cleevio.cinemax.api.module.movie.service.MovieService
import com.cleevio.cinemax.api.module.posconfiguration.service.PosConfigurationService
import com.cleevio.cinemax.api.module.pricecategory.service.PriceCategoryService
import com.cleevio.cinemax.api.module.pricecategoryitem.constant.PriceCategoryItemNumber
import com.cleevio.cinemax.api.module.pricecategoryitem.service.PriceCategoryItemService
import com.cleevio.cinemax.api.module.product.constant.ProductType
import com.cleevio.cinemax.api.module.product.service.ProductRepository
import com.cleevio.cinemax.api.module.product.service.ProductService
import com.cleevio.cinemax.api.module.productcategory.constant.ProductCategoryType
import com.cleevio.cinemax.api.module.productcategory.service.ProductCategoryRepository
import com.cleevio.cinemax.api.module.productcomponent.constant.ProductComponentUnit
import com.cleevio.cinemax.api.module.productcomponent.service.ProductComponentRepository
import com.cleevio.cinemax.api.module.productcomponentcategory.service.ProductComponentCategoryRepository
import com.cleevio.cinemax.api.module.productcomposition.service.ProductCompositionService
import com.cleevio.cinemax.api.module.reservation.event.ReservationCreatedEvent
import com.cleevio.cinemax.api.module.screening.service.ScreeningService
import com.cleevio.cinemax.api.module.screeningfee.service.ScreeningFeeService
import com.cleevio.cinemax.api.module.seat.constant.SeatType
import com.cleevio.cinemax.api.module.seat.service.SeatService
import com.cleevio.cinemax.api.util.createAuditorium
import com.cleevio.cinemax.api.util.createAuditoriumLayout
import com.cleevio.cinemax.api.util.createBasket
import com.cleevio.cinemax.api.util.createBranch
import com.cleevio.cinemax.api.util.createDiscountCard
import com.cleevio.cinemax.api.util.createDistributor
import com.cleevio.cinemax.api.util.createMovie
import com.cleevio.cinemax.api.util.createPosConfiguration
import com.cleevio.cinemax.api.util.createPriceCategory
import com.cleevio.cinemax.api.util.createPriceCategoryItem
import com.cleevio.cinemax.api.util.createProduct
import com.cleevio.cinemax.api.util.createProductCategory
import com.cleevio.cinemax.api.util.createProductComponent
import com.cleevio.cinemax.api.util.createProductComponentCategory
import com.cleevio.cinemax.api.util.createProductComposition
import com.cleevio.cinemax.api.util.createScreening
import com.cleevio.cinemax.api.util.createScreeningFee
import com.cleevio.cinemax.api.util.createSeat
import com.cleevio.cinemax.api.util.createTicketBasketItemRequest
import com.cleevio.cinemax.api.util.mapToCreateBasketItemCommand
import com.cleevio.cinemax.api.util.mapToCreateOrUpdateAuditoriumCommand
import com.cleevio.cinemax.api.util.mapToCreateOrUpdateAuditoriumLayoutCommand
import com.cleevio.cinemax.api.util.mapToCreateOrUpdateDiscountCardCommand
import com.cleevio.cinemax.api.util.mapToCreateOrUpdateDistributorCommand
import com.cleevio.cinemax.api.util.mapToCreateOrUpdateMovieCommand
import com.cleevio.cinemax.api.util.mapToCreateOrUpdatePosConfigurationCommand
import com.cleevio.cinemax.api.util.mapToCreateOrUpdatePriceCategoryItemCommand
import com.cleevio.cinemax.api.util.mapToCreateOrUpdateProductCompositionCommand
import com.cleevio.cinemax.api.util.mapToCreateOrUpdateScreeningCommand
import com.cleevio.cinemax.api.util.mapToCreateOrUpdateScreeningFeeCommand
import com.cleevio.cinemax.api.util.mapToCreateOrUpdateSeatCommand
import com.cleevio.cinemax.api.util.mapToSyncCreateOrUpdatePriceCategoryCommand
import com.cleevio.cinemax.api.util.mapToSyncCreateOrUpdateProductCommand
import io.mockk.Runs
import io.mockk.every
import io.mockk.just
import org.junit.jupiter.api.BeforeAll
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.Arguments
import org.junit.jupiter.params.provider.MethodSource
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.data.domain.Pageable
import java.math.BigDecimal
import java.time.LocalTime
import java.util.UUID
import java.util.stream.Stream
import kotlin.test.assertEquals
import kotlin.test.assertTrue

class AdminExportProductBasketItemsSummaryQueryServiceIT @Autowired constructor(
    private val underTest: AdminExportProductBasketItemsSummaryQueryService,
    private val posConfigurationService: PosConfigurationService,
    private val auditoriumService: AuditoriumService,
    private val auditoriumLayoutService: AuditoriumLayoutService,
    private val seatService: SeatService,
    private val distributorService: DistributorService,
    private val movieService: MovieService,
    private val screeningService: ScreeningService,
    private val priceCategoryService: PriceCategoryService,
    private val priceCategoryItemService: PriceCategoryItemService,
    private val screeningFeeService: ScreeningFeeService,
    private val discountCardMssqlService: DiscountCardMssqlService,
    private val discountCardUsageService: DiscountCardUsageService,
    private val discountCardUsageJpaFinderService: DiscountCardUsageJpaFinderService,
    private val basketRepository: BasketRepository,
    private val basketService: BasketService,
    private val basketReceiptNumbersService: BasketReceiptNumbersService,
    private val basketItemService: BasketItemService,
    private val basketItemRepository: BasketItemRepository,
    private val productService: ProductService,
    private val productCategoryRepository: ProductCategoryRepository,
    private val productCompositionService: ProductCompositionService,
    private val productComponentCategoryRepository: ProductComponentCategoryRepository,
    private val productComponentRepository: ProductComponentRepository,
    private val productRepository: ProductRepository,
    private val branchRepository: BranchRepository,
) : IntegrationTest(DatabaseCleanup.AFTER_ALL) {

    @BeforeAll
    fun setUp() {
        branchRepository.save(createBranch())

        every { productReceiptNumberGeneratorMock.generateProductReceiptNumber() } returns "0000000001"
        every { applicationEventPublisherMock.publishEvent(any<ProductBasketItemAddedToBasketEvent>()) } just Runs
        every { applicationEventPublisherMock.publishEvent(any<ProductWithPackagingDepositFoundEvent>()) } just Runs
        every {
            reservationMssqlFinderServiceMock.findByOriginalScreeningIdAndOriginalSeatId(
                any(),
                any()
            )
        } returns null
        every { applicationEventPublisherMock.publishEvent(any<ReservationCreatedEvent>()) } just Runs

        setOf(POS_CONFIGURATION_1, POS_CONFIGURATION_2).forEach {
            posConfigurationService.createOrUpdatePosConfiguration(mapToCreateOrUpdatePosConfigurationCommand(it))
        }
        auditoriumService.syncCreateOrUpdateAuditorium(mapToCreateOrUpdateAuditoriumCommand(AUDITORIUM_1))
        auditoriumLayoutService.syncCreateOrUpdateAuditoriumLayout(
            mapToCreateOrUpdateAuditoriumLayoutCommand(
                AUDITORIUM_LAYOUT_1
            )
        )
        setOf(SEAT_1, SEAT_2).forEach {
            seatService.createOrUpdateSeat(mapToCreateOrUpdateSeatCommand(it))
        }
        distributorService.syncCreateOrUpdateDistributor(mapToCreateOrUpdateDistributorCommand(DISTRIBUTOR_1))
        movieService.syncCreateOrUpdateMovie(mapToCreateOrUpdateMovieCommand(MOVIE_1))
        priceCategoryService.syncCreateOrUpdatePriceCategory(
            mapToSyncCreateOrUpdatePriceCategoryCommand(
                PRICE_CATEGORY_1
            )
        )
        priceCategoryItemService.createOrUpdatePriceCategoryItem(
            mapToCreateOrUpdatePriceCategoryItemCommand(PRICE_CATEGORY_ITEM_1, PRICE_CATEGORY_ITEM_1.priceCategoryId)
        )
        screeningService.syncCreateOrUpdateScreening(mapToCreateOrUpdateScreeningCommand(SCREENING_1))
        screeningFeeService.createOrUpdateScreeningFee(mapToCreateOrUpdateScreeningFeeCommand(SCREENING_FEE_1))

        productCategoryRepository.saveAll(
            setOf(
                PRODUCT_CATEGORY_1,
                PRODUCT_CATEGORY_2,
                PRODUCT_CATEGORY_3,
                PRODUCT_CATEGORY_4,
                PRODUCT_CATEGORY_5
            )
        )
        productComponentCategoryRepository.saveAll(setOf(PRODUCT_COMPONENT_CATEGORY_1, PRODUCT_COMPONENT_CATEGORY_2))
        productComponentRepository.saveAll(setOf(PRODUCT_COMPONENT_1, PRODUCT_COMPONENT_2, PRODUCT_COMPONENT_3, PRODUCT_COMPONENT_4))

        productService.syncCreateOrUpdateProduct(
            mapToSyncCreateOrUpdateProductCommand(
                PRODUCT_1,
                PRODUCT_CATEGORY_1.id
            )
        )
        productService.syncCreateOrUpdateProduct(
            mapToSyncCreateOrUpdateProductCommand(
                PRODUCT_2,
                PRODUCT_CATEGORY_2.id
            )
        )
        setOf(PRODUCT_3, PRODUCT_4, PRODUCT_6).forEach {
            productService.syncCreateOrUpdateProduct(mapToSyncCreateOrUpdateProductCommand(it, PRODUCT_CATEGORY_2.id))
        }.also { productRepository.save(PRODUCT_4.apply { taxRate = PRODUCT_4.taxRate }) } // taxRate is not synced from MSSQL, necessary to add it explicitly
        productService.syncCreateOrUpdateProduct(
            mapToSyncCreateOrUpdateProductCommand(
                PRODUCT_5,
                PRODUCT_CATEGORY_5.id
            )
        )
        setOf(PRODUCT_DISCOUNT_1, PRODUCT_DISCOUNT_2).forEach {
            productService.syncCreateOrUpdateProduct(mapToSyncCreateOrUpdateProductCommand(it, PRODUCT_CATEGORY_3.id))
        }
        productService.syncCreateOrUpdateProduct(
            mapToSyncCreateOrUpdateProductCommand(
                PRODUCT_DISCOUNT_3,
                PRODUCT_CATEGORY_4.id
            )
        )
        setOf(
            PRODUCT_COMPOSITION_1,
            PRODUCT_COMPOSITION_2,
            PRODUCT_COMPOSITION_3,
            PRODUCT_COMPOSITION_4,
            PRODUCT_COMPOSITION_5,
            PRODUCT_COMPOSITION_6,
            PRODUCT_COMPOSITION_7,
            PRODUCT_COMPOSITION_8,
            PRODUCT_COMPOSITION_9
        ).forEach {
            productCompositionService.createOrUpdateProductComposition(
                mapToCreateOrUpdateProductCompositionCommand(it)
            )
        }

        setOf(DISCOUNT_CARD_1, DISCOUNT_CARD_2, DISCOUNT_CARD_3).forEach {
            discountCardMssqlService.createOrUpdateDiscountCard(mapToCreateOrUpdateDiscountCardCommand(it))
        }

        // two products, no discounts
        BASKET_1.createBasket()
        BASKET_1.addProductToBasket(1, PRODUCT_1.id, BasketItemType.PRODUCT)
        BASKET_1.addProductToBasket(2, PRODUCT_2.id, BasketItemType.PRODUCT)
        BASKET_1.payBasket(PaymentType.CASH, POS_CONFIGURATION_1.id)

        // one product, no discounts, cashless, another POS
        BASKET_2.createBasket()
        BASKET_2.addProductToBasket(3, PRODUCT_1.id, BasketItemType.PRODUCT, 2)
        BASKET_2.payBasket(PaymentType.CASHLESS, POS_CONFIGURATION_2.id)

        // one product, basket contains ticket
        BASKET_3.createBasket()
        BASKET_3.addProductToBasket(4, PRODUCT_1.id, BasketItemType.PRODUCT, 3)
        BASKET_3.addTicketToBasket(1, SEAT_1.id, SCREENING_1.id, PriceCategoryItemNumber.PRICE_1)
        BASKET_3.payBasket(PaymentType.CASH, POS_CONFIGURATION_1.id)

        // one product, one non-card product discount, one isolated group from voucher
        BASKET_4.createBasket()
        BASKET_4.addProductToBasket(5, PRODUCT_2.id, BasketItemType.PRODUCT)
        BASKET_4.addProductToBasket(6, PRODUCT_DISCOUNT_3.id, BasketItemType.PRODUCT_DISCOUNT)
        BASKET_4.addProductToBasket(
            originalId = 7,
            productId = PRODUCT_1.id,
            type = BasketItemType.PRODUCT,
            discountCardId = DISCOUNT_CARD_3.id,
            isolatedWith = PRODUCT_DISCOUNT_2.id,
            posConfigurationId = POS_CONFIGURATION_1.id
        )
        BASKET_4.addProductToBasket(
            originalId = 8,
            productId = PRODUCT_DISCOUNT_2.id,
            type = BasketItemType.PRODUCT_DISCOUNT,
            discountCardId = DISCOUNT_CARD_3.id,
            isolatedWith = PRODUCT_1.id,
            posConfigurationId = POS_CONFIGURATION_1.id
        )
        basketItemService.recalculateBasketProductDiscountPrices(
            RecalculateBasketProductDiscountPricesCommand(BASKET_4.id)
        )
        BASKET_4.payBasket(PaymentType.CASHLESS, POS_CONFIGURATION_1.id)

        // one product, two product discounts - one from discount card, one from voucher
        BASKET_5.createBasket()
        BASKET_5.addProductToBasket(
            originalId = 9,
            productId = PRODUCT_1.id,
            type = BasketItemType.PRODUCT
        )
        BASKET_5.addProductToBasket(
            originalId = 10,
            productId = PRODUCT_DISCOUNT_1.id,
            type = BasketItemType.PRODUCT_DISCOUNT,
            posConfigurationId = POS_CONFIGURATION_2.id,
            discountCardId = DISCOUNT_CARD_1.id
        )
        // active (highest) discount in a basket
        BASKET_5.addProductToBasket(
            originalId = 11,
            productId = PRODUCT_DISCOUNT_2.id,
            type = BasketItemType.PRODUCT_DISCOUNT,
            posConfigurationId = POS_CONFIGURATION_2.id,
            discountCardId = DISCOUNT_CARD_2.id
        )
        basketItemService.recalculateBasketProductDiscountPrices(
            RecalculateBasketProductDiscountPricesCommand(BASKET_5.id)
        )
        BASKET_5.payBasket(PaymentType.CASH, POS_CONFIGURATION_2.id)

        // cancelled and deleted product
        BASKET_6.createBasket()
        val basket6Item1 = BASKET_6.addProductToBasket(12, PRODUCT_1.id, BasketItemType.PRODUCT)
        BASKET_6.cancelBasketItem(13, basket6Item1.id)
        val basket6Item2 = BASKET_6.addProductToBasket(14, PRODUCT_2.id, BasketItemType.PRODUCT)
        BASKET_6.deleteBasketItem(basket6Item2.id)
        BASKET_6.payBasket(PaymentType.CASHLESS, POS_CONFIGURATION_2.id)

        // non-paid basket - shouldn't appear anywhere
        BASKET_7.createBasket()
        BASKET_7.addProductToBasket(15, PRODUCT_2.id, BasketItemType.PRODUCT, quantity = 5)

        // contains 1 product of 1 component and 2xCOMBO
        BASKET_8.createBasket()
        BASKET_8.addProductToBasket(
            originalId = 16,
            productId = PRODUCT_3.id,
            type = BasketItemType.PRODUCT,
            quantity = 1
        )
        BASKET_8.addProductToBasket(
            originalId = 17,
            productId = PRODUCT_4.id,
            type = BasketItemType.PRODUCT,
            quantity = 2
        )
        BASKET_8.payBasket(PaymentType.CASHLESS, POS_CONFIGURATION_2.id)

        // contains product consisting of two components and one packaging deposit
        BASKET_9.createBasket()
        BASKET_9.addProductToBasket(
            originalId = 18,
            productId = PRODUCT_6.id,
            type = BasketItemType.PRODUCT,
            quantity = 3
        )
        BASKET_9.addPackagingDepositToBasket(
            packagingDepositProductId = PRODUCT_5.id,
            quantity = 3
        )
        BASKET_9.payBasket(PaymentType.CASH, POS_CONFIGURATION_2.id)
    }

    @ParameterizedTest
    @MethodSource("productBasketItemSummaryProvider")
    fun `test AdminExportProductBasketItemsQuery - with filter - should be mapped correctly`(
        filter: AdminSearchProductBasketItemsFilter,
        expectedResponse: ProductBasketItemExportSummaryRecordModel,
    ) {
        val response = underTest(
            AdminExportProductBasketItemsQuery(
                pageable = Pageable.unpaged(),
                filter = filter,
                exportFormat = ExportFormat.XLSX,
                username = "test_user"
            )
        )

        assertEquals(expectedResponse.productsCount, response.productsCount)
        assertEquals(expectedResponse.basketsCount, response.basketsCount)
        assertTrue(expectedResponse.purchasePriceSum isEqualTo response.purchasePriceSum)
        assertTrue(expectedResponse.salesCash isEqualTo response.salesCash)
        assertTrue(expectedResponse.salesCashless isEqualTo response.salesCashless)
        assertTrue(expectedResponse.grossSales isEqualTo response.grossSales)
        assertTrue(expectedResponse.netSales isEqualTo response.netSales)
        assertTrue(expectedResponse.taxAmount isEqualTo response.taxAmount)
        assertTrue(expectedResponse.grossRevenue isEqualTo response.grossRevenue)
        assertTrue(expectedResponse.netRevenue isEqualTo response.netRevenue)
        assertEquals(expectedResponse.cancelledProductsCount, response.cancelledProductsCount)
        assertTrue(expectedResponse.cancelledProductsExpense isEqualTo response.cancelledProductsExpense)
    }

    companion object {
        @JvmStatic
        fun productBasketItemSummaryProvider(): Stream<Arguments> {
            return Stream.of(
                Arguments.of(
                    AdminSearchProductBasketItemsFilter(
                        discountCardCode = DISCOUNT_CARD_2.code // product discount
                    ),
                    ProductBasketItemExportSummaryRecordModel(
                        productsCount = 1,
                        basketsCount = 1,
                        purchasePriceSum = 0.toBigDecimal(),
                        salesCash = (-10).toBigDecimal(),
                        salesCashless = 0.toBigDecimal(),
                        grossSales = (-10).toBigDecimal(),
                        netSales = (-10).toBigDecimal(),
                        taxAmount = 0.toBigDecimal(),
                        grossRevenue = (-10).toBigDecimal(),
                        netRevenue = (-10).toBigDecimal(),
                        cancelledProductsCount = 0,
                        cancelledProductsExpense = 0.toBigDecimal()
                    )
                ),
                Arguments.of(
                    AdminSearchProductBasketItemsFilter(
                        isCancelled = true // cancelled item
                    ),
                    ProductBasketItemExportSummaryRecordModel(
                        productsCount = 0,
                        basketsCount = 1,
                        purchasePriceSum = 1.25.toBigDecimal(),
                        salesCash = 0.toBigDecimal(),
                        salesCashless = 0.toBigDecimal(),
                        grossSales = 0.toBigDecimal(),
                        netSales = (-1.87).toBigDecimal(),
                        taxAmount = 1.87.toBigDecimal(),
                        grossRevenue = (-1.25).toBigDecimal(),
                        netRevenue = (-3.12).toBigDecimal(),
                        cancelledProductsCount = 1,
                        cancelledProductsExpense = 10.toBigDecimal()
                    )
                ),
                Arguments.of(
                    AdminSearchProductBasketItemsFilter(
                        productIds = setOf(PRODUCT_3.id) // product with one component
                    ),
                    ProductBasketItemExportSummaryRecordModel(
                        productsCount = 1,
                        basketsCount = 1,
                        purchasePriceSum = 1.1.toBigDecimal(),
                        salesCash = 0.toBigDecimal(),
                        salesCashless = 2.2.toBigDecimal(),
                        grossSales = 2.2.toBigDecimal(),
                        netSales = 1.79.toBigDecimal(),
                        taxAmount = 0.41.toBigDecimal(),
                        grossRevenue = 1.1.toBigDecimal(),
                        netRevenue = 0.69.toBigDecimal(),
                        cancelledProductsCount = 0,
                        cancelledProductsExpense = 0.toBigDecimal()
                    )
                ),
                Arguments.of(
                    AdminSearchProductBasketItemsFilter(
                        productIds = setOf(PRODUCT_4.id) // product in product
                    ),
                    ProductBasketItemExportSummaryRecordModel(
                        productsCount = 1,
                        basketsCount = 1,
                        purchasePriceSum = 5.26.toBigDecimal(),
                        salesCash = 0.toBigDecimal(),
                        salesCashless = 15.toBigDecimal(),
                        grossSales = 15.toBigDecimal(),
                        netSales = 14.26.toBigDecimal(),
                        taxAmount = 0.74.toBigDecimal(),
                        grossRevenue = 9.74.toBigDecimal(),
                        netRevenue = 9.00.toBigDecimal(),
                        cancelledProductsCount = 0,
                        cancelledProductsExpense = 0.toBigDecimal()
                    )
                ),
                Arguments.of(
                    AdminSearchProductBasketItemsFilter(
                        basketPaidAtFrom = BASKET_4.paidAt, // sum two baskets
                        basketPaidAtTo = BASKET_5.paidAt
                    ),
                    ProductBasketItemExportSummaryRecordModel(
                        productsCount = 6,
                        basketsCount = 2,
                        purchasePriceSum = 2.715.toBigDecimal(),
                        salesCash = 0.toBigDecimal(),
                        salesCashless = 1.8.toBigDecimal(),
                        grossSales = 1.8.toBigDecimal(),
                        netSales = (-2.31).toBigDecimal(),
                        taxAmount = 4.11.toBigDecimal(),
                        grossRevenue = (-0.915).toBigDecimal(),
                        netRevenue = (-5.025).toBigDecimal(),
                        cancelledProductsCount = 0,
                        cancelledProductsExpense = 0.toBigDecimal()
                    )
                ),
                Arguments.of(
                    AdminSearchProductBasketItemsFilter(
                        basketPaidAtFrom = BASKET_9.paidAt // product with automatic packaging deposit
                    ),
                    ProductBasketItemExportSummaryRecordModel(
                        productsCount = 2,
                        basketsCount = 1,
                        purchasePriceSum = 1.095.toBigDecimal(),
                        salesCash = 6.45.toBigDecimal(),
                        salesCashless = 0.toBigDecimal(),
                        grossSales = 6.45.toBigDecimal(),
                        netSales = 3.08.toBigDecimal(),
                        taxAmount = 3.37.toBigDecimal(),
                        grossRevenue = 5.355.toBigDecimal(),
                        netRevenue = 1.985.toBigDecimal(),
                        cancelledProductsCount = 0,
                        cancelledProductsExpense = 0.toBigDecimal()
                    )
                )
            )
        }
    }

    private fun Basket.addTicketToBasket(
        originalId: Int,
        seatId: UUID,
        screeningId: UUID,
        priceCategoryItemNumber: PriceCategoryItemNumber,
        primaryTicketDiscountId: UUID? = null,
        secondaryTicketDiscountId: UUID? = null,
        primaryDiscountCardId: UUID? = null,
        secondaryDiscountCardId: UUID? = null,
    ): BasketItem {
        return basketItemService.createBasketItem(
            mapToCreateBasketItemCommand(
                request = createTicketBasketItemRequest(
                    seatId = seatId,
                    screeningId = screeningId,
                    priceCategoryItem = priceCategoryItemNumber,
                    primaryTicketDiscountId = primaryTicketDiscountId,
                    secondaryTicketDiscountId = secondaryTicketDiscountId,
                    primaryDiscountCardId = primaryDiscountCardId,
                    secondaryDiscountCardId = secondaryDiscountCardId
                ),
                basketId = this.id,
                originalId = originalId
            )
        )
    }

    private fun Basket.addPackagingDepositToBasket(
        packagingDepositProductId: UUID,
        quantity: Int = 1,
    ) {
        basketItemService.createPackagingDepositBasketItem(
            CreatePackagingDepositBasketItemCommand(
                basketId = this.id,
                quantity = quantity,
                productId = packagingDepositProductId
            )
        )
    }

    private fun Basket.addProductToBasket(
        originalId: Int,
        productId: UUID,
        type: BasketItemType,
        quantity: Int = 1,
        posConfigurationId: UUID? = null,
        discountCardId: UUID? = null,
        isolatedWith: UUID? = null,
    ): BasketItem {
        val basketItem = basketItemService.createBasketItem(
            CreateBasketItemCommand(
                basketId = this.id,
                type = type,
                productId = productId,
                productIsolatedWithId = isolatedWith,
                quantity = quantity,
                originalId = originalId
            )
        )

        discountCardId?.let {
            val discountCardUsage = discountCardUsageJpaFinderService.findNonDeletedByDiscountCardIdAndBasketIsInModifiableState(discountCardId) ?: run {
                return@run discountCardUsageService.createDiscountCardUsage(
                    CreateDiscountCardUsageCommand(
                        discountCardId = it,
                        basketId = basketItem.basketId,
                        posConfigurationId = posConfigurationId!!
                    )
                )
            }

            discountCardUsageService.updateDiscountCardUsage(
                UpdateDiscountCardUsageCommand(
                    discountCardUsageId = discountCardUsage.id,
                    basketItemId = basketItem.id
                )
            )
        }
        return basketItem
    }

    private fun Basket.payBasket(
        paymentType: PaymentType,
        posConfigurationId: UUID,
    ) {
        basketService.initiateBasketPayment(
            InitiateBasketPaymentCommand(
                basketId = this.id,
                posConfigurationId = posConfigurationId,
                paymentType = paymentType
            )
        )
        basketReceiptNumbersService.generateBasketReceiptNumbers(
            GenerateBasketReceiptNumbersCommand(
                basketId = this.id
            )
        )
        this.paidAt = basketService.completeBasketPayment(
            CompleteBasketPaymentCommand(
                basketId = this.id
            )
        ).paidAt
    }

    private fun Basket.cancelBasketItem(originalId: Int, basketItemId: UUID) {
        val cancelledBasketItemIds = basketItemService.cancelBasketItems(
            command = CancelBasketItemsCommand(
                basketItemIds = setOf(basketItemId),
                printReceipt = false
            )
        )
        val cancelledBasketItem =
            basketItemRepository.findAllByBasketIdAndDeletedAtIsNullAndCancelledBasketItemIdIsNotNull(
                basketId = cancelledBasketItemIds.first()
            ).first()

        basketItemRepository.save(
            cancelledBasketItem.apply { this.originalId = originalId }
        )
    }

    private fun Basket.deleteBasketItem(basketItemId: UUID) {
        basketItemService.deleteBasketItem(
            command = DeleteBasketItemCommand(
                basketId = this.id,
                basketItemId = basketItemId
            )
        )
    }

    private fun Basket.createBasket() = basketRepository.save(this)
}

// baskets
private val BASKET_1 = createBasket(state = BasketState.OPEN, totalPrice = 0.toBigDecimal())
private val BASKET_2 = createBasket(state = BasketState.OPEN, totalPrice = 0.toBigDecimal())
private val BASKET_3 = createBasket(state = BasketState.OPEN, totalPrice = 0.toBigDecimal())
private val BASKET_4 = createBasket(state = BasketState.OPEN, totalPrice = 0.toBigDecimal())
private val BASKET_5 = createBasket(state = BasketState.OPEN, totalPrice = 0.toBigDecimal())
private val BASKET_6 = createBasket(state = BasketState.OPEN, totalPrice = 0.toBigDecimal())
private val BASKET_7 = createBasket(state = BasketState.OPEN, totalPrice = 0.toBigDecimal())
private val BASKET_8 = createBasket(state = BasketState.OPEN, totalPrice = 0.toBigDecimal())
private val BASKET_9 = createBasket(state = BasketState.OPEN, totalPrice = 0.toBigDecimal())

private val POS_CONFIGURATION_1 = createPosConfiguration(title = "pokl1")
private val POS_CONFIGURATION_2 = createPosConfiguration(title = "pokl2", macAddress = "EE:DD:CC:BB:AA")

private val PRODUCT_CATEGORY_1 = createProductCategory(
    title = "Popcorn",
    type = ProductCategoryType.PRODUCT
)
private val PRODUCT_CATEGORY_2 = createProductCategory(
    originalId = 2,
    title = "Napoje",
    type = ProductCategoryType.PRODUCT
)
private val PRODUCT_CATEGORY_3 = createProductCategory(
    originalId = 3,
    title = "Zlava karta",
    type = ProductCategoryType.DISCOUNT
)
private val PRODUCT_CATEGORY_4 = createProductCategory(
    originalId = 4,
    title = "Obecna sleva",
    type = ProductCategoryType.DISCOUNT
)
private val PRODUCT_CATEGORY_5 = createProductCategory(
    originalId = 5,
    title = "Zaloha",
    type = ProductCategoryType.PRODUCT,
    taxRate = NO_TAX_RATE
)
private val PRODUCT_COMPONENT_CATEGORY_1 = createProductComponentCategory()
private val PRODUCT_COMPONENT_CATEGORY_2 = createProductComponentCategory(originalId = 2, code = "02", taxRate = REDUCED_TAX_RATE)
private val PRODUCT_COMPONENT_1 = createProductComponent(
    title = "Kukurica",
    stockQuantity = 100.toBigDecimal(),
    productComponentCategoryId = PRODUCT_COMPONENT_CATEGORY_1.id,
    unit = ProductComponentUnit.KG,
    purchasePrice = 5.toBigDecimal()
)
private val PRODUCT_COMPONENT_2 = createProductComponent(
    title = "Kelimek 0.3l",
    stockQuantity = 100.toBigDecimal(),
    productComponentCategoryId = PRODUCT_COMPONENT_CATEGORY_1.id,
    unit = ProductComponentUnit.KS,
    purchasePrice = 0.2.toBigDecimal()
)
private val PRODUCT_COMPONENT_3 = createProductComponent(
    title = "Sirup/stava",
    stockQuantity = 100.toBigDecimal(),
    productComponentCategoryId = PRODUCT_COMPONENT_CATEGORY_1.id,
    unit = ProductComponentUnit.L,
    purchasePrice = 1.5.toBigDecimal()
)
private val PRODUCT_COMPONENT_4 = createProductComponent(
    title = "Cappy male",
    stockQuantity = 100.toBigDecimal(),
    productComponentCategoryId = PRODUCT_COMPONENT_CATEGORY_2.id,
    unit = ProductComponentUnit.KS,
    purchasePrice = 1.1.toBigDecimal()
)
private val PRODUCT_1 = createProduct(originalId = 1, productCategoryId = PRODUCT_CATEGORY_1.id, title = "Popcorn XXL")
private val PRODUCT_2 = createProduct(originalId = 2, productCategoryId = PRODUCT_CATEGORY_2.id, title = "Coca Cola 0.3l", price = 2.toBigDecimal())
private val PRODUCT_3 = createProduct(
    originalId = 3,
    productCategoryId = PRODUCT_CATEGORY_2.id,
    title = "Cappy",
    price = 2.2.toBigDecimal()
)
private val PRODUCT_4 = createProduct(
    originalId = 4,
    type = ProductType.PRODUCT_IN_PRODUCT,
    productCategoryId = PRODUCT_CATEGORY_2.id,
    title = "COMBO 2Cola+2Cappy",
    price = 7.5.toBigDecimal(),
    taxRate = SUPER_REDUCED_TAX_RATE
)
private val PRODUCT_5 = createProduct(
    originalId = 5,
    productCategoryId = PRODUCT_CATEGORY_5.id,
    title = "Záloha za obal",
    price = 0.15.toBigDecimal(),
    type = ProductType.ADDITIONAL_SALE,
    isPackagingDeposit = true
)
private val PRODUCT_6 = createProduct(
    originalId = 6,
    productCategoryId = PRODUCT_CATEGORY_2.id,
    title = "Cola+zaloha",
    price = 2.toBigDecimal(),
    type = ProductType.PRODUCT
)
private val PRODUCT_DISCOUNT_1 = createProduct(
    originalId = 7,
    productCategoryId = PRODUCT_CATEGORY_3.id,
    title = "VIP karta 20%",
    price = 0.toBigDecimal(),
    discountPercentage = 20
)
private val PRODUCT_DISCOUNT_2 = createProduct(
    originalId = 8,
    productCategoryId = PRODUCT_CATEGORY_3.id,
    title = "Voucher 100%",
    type = ProductType.ADDITIONAL_SALE,
    price = 0.toBigDecimal(),
    discountPercentage = 100
)
private val PRODUCT_DISCOUNT_3 = createProduct(
    originalId = 9,
    productCategoryId = PRODUCT_CATEGORY_4.id,
    title = "Zlava 10%",
    type = ProductType.ADDITIONAL_SALE,
    price = 0.toBigDecimal(),
    discountPercentage = 10
)
private val PRODUCT_COMPOSITION_1 = createProductComposition(
    originalId = 1,
    productId = PRODUCT_1.id,
    productComponentId = PRODUCT_COMPONENT_1.id,
    amount = 0.25.toBigDecimal()
)
private val PRODUCT_COMPOSITION_2 = createProductComposition(
    originalId = 2,
    productId = PRODUCT_2.id,
    productComponentId = PRODUCT_COMPONENT_2.id,
    amount = BigDecimal.ONE
)
private val PRODUCT_COMPOSITION_3 = createProductComposition(
    originalId = 3,
    productId = PRODUCT_2.id,
    productComponentId = PRODUCT_COMPONENT_3.id,
    amount = 0.01.toBigDecimal()
)

// Cappy
private val PRODUCT_COMPOSITION_4 = createProductComposition(
    originalId = 4,
    productId = PRODUCT_3.id,
    productComponentId = PRODUCT_COMPONENT_4.id,
    amount = 1.toBigDecimal()
)

// COMBO compositions
private val PRODUCT_COMPOSITION_5 = createProductComposition(
    originalId = 5,
    productId = PRODUCT_4.id,
    productInProductId = PRODUCT_2.id,
    amount = 2.toBigDecimal(),
    productInProductPrice = PRODUCT_2.price.minus(1.toBigDecimal()),
    productInProductFlagshipPrice = PRODUCT_2.price.minus(1.toBigDecimal())
)
private val PRODUCT_COMPOSITION_6 = createProductComposition(
    originalId = 6,
    productId = PRODUCT_4.id,
    productInProductId = PRODUCT_3.id,
    amount = 2.toBigDecimal(),
    productInProductPrice = PRODUCT_3.price.minus(1.toBigDecimal()),
    productInProductFlagshipPrice = PRODUCT_2.price.minus(1.toBigDecimal())
)

// automatic packaging deposit for product Cola se zalohou
private val PRODUCT_COMPOSITION_7 = createProductComposition(
    originalId = 7,
    productId = PRODUCT_6.id,
    productInProductId = PRODUCT_5.id,
    amount = 1.toBigDecimal()
)
private val PRODUCT_COMPOSITION_8 = createProductComposition(
    originalId = 8,
    productId = PRODUCT_6.id,
    productComponentId = PRODUCT_COMPONENT_2.id,
    amount = 1.toBigDecimal()
)
private val PRODUCT_COMPOSITION_9 = createProductComposition(
    originalId = 9,
    productId = PRODUCT_6.id,
    productComponentId = PRODUCT_COMPONENT_3.id,
    amount = 0.01.toBigDecimal()
)
private val DISCOUNT_CARD_1 = createDiscountCard(
    originalId = 1,
    ticketDiscountId = null,
    productDiscountId = PRODUCT_DISCOUNT_1.id,
    title = "VIP karta",
    code = "44444"
)
private val DISCOUNT_CARD_2 = createDiscountCard(
    originalId = 2,
    ticketDiscountId = null,
    productDiscountId = PRODUCT_DISCOUNT_2.id,
    title = "Wonka voucher",
    code = "55555",
    type = DiscountCardType.VOUCHER
)
private val DISCOUNT_CARD_3 = createDiscountCard( // provides isolated group
    originalId = 3,
    ticketDiscountId = null,
    productDiscountId = PRODUCT_DISCOUNT_2.id,
    productId = PRODUCT_1.id,
    title = "Akce1",
    code = "11111",
    type = DiscountCardType.VOUCHER
)

// necessary data for ticket basket item - just to have it covered
private val AUDITORIUM_1 = createAuditorium(originalId = 1, title = "Sála IMAX", code = "IMAX")
private val AUDITORIUM_LAYOUT_1 = createAuditoriumLayout(originalId = 1, auditoriumId = AUDITORIUM_1.id)
private val SEAT_1 = createSeat(
    originalId = 1,
    auditoriumId = AUDITORIUM_1.id,
    auditoriumLayoutId = AUDITORIUM_LAYOUT_1.id,
    type = SeatType.REGULAR
)
private val SEAT_2 = createSeat(
    originalId = 2,
    auditoriumId = AUDITORIUM_1.id,
    auditoriumLayoutId = AUDITORIUM_LAYOUT_1.id,
    type = SeatType.REGULAR
)
private val DISTRIBUTOR_1 = createDistributor(originalId = 1, title = "Best movies ever, s.r.o.")
private val MOVIE_1 = createMovie(
    originalId = 1,
    rawTitle = "Star Wars: Episode I – The Phantom Menace 2D (CT)",
    title = "Star Wars: Episode I – The Phantom Menace",
    distributorId = DISTRIBUTOR_1.id
)
private val PRICE_CATEGORY_1 = createPriceCategory(originalId = 1, title = "Do 17h")
private val PRICE_CATEGORY_ITEM_1 = createPriceCategoryItem(
    priceCategoryId = PRICE_CATEGORY_1.id,
    number = PriceCategoryItemNumber.PRICE_1,
    price = 5.toBigDecimal()
)
private val SCREENING_1 = createScreening(
    originalId = 1,
    date = INTEGRATION_TEST_DATE_TIME.toLocalDate().plusDays(1),
    time = LocalTime.of(20, 0),
    auditoriumId = AUDITORIUM_1.id,
    auditoriumLayoutId = AUDITORIUM_LAYOUT_1.id,
    movieId = MOVIE_1.id,
    priceCategoryId = PRICE_CATEGORY_1.id
)
private val SCREENING_FEE_1 = createScreeningFee(
    originalScreeningId = 1,
    screeningId = SCREENING_1.id,
    surchargeVip = 0.toBigDecimal(),
    surchargePremium = 0.toBigDecimal(),
    surchargeImax = 1.toBigDecimal(),
    surchargeUltraX = 0.toBigDecimal(),
    surchargeDBox = 0.toBigDecimal(),
    serviceFeeVip = 0.toBigDecimal(),
    serviceFeePremium = 1.toBigDecimal(),
    serviceFeeImax = 0.toBigDecimal(),
    serviceFeeUltraX = 0.toBigDecimal(),
    serviceFeeGeneral = 0.toBigDecimal()
)
