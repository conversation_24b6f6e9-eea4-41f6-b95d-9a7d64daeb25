package com.cleevio.cinemax.api.module.distributor.controller.mapper

import com.cleevio.cinemax.api.module.distributor.controller.dto.AdminGetDistributorResponse
import com.cleevio.cinemax.api.util.createDistributor
import org.junit.jupiter.api.Test
import kotlin.test.assertEquals

class AdminGetDistributorResponseMapperTest {

    @Test
    fun `test map - should correctly map entity to response`() {
        val distributor = createDistributor(
            originalId = 1,
            code = "DISTR_1",
            disfilmCode = "01",
            title = "Best movies ever, s.r.o.",
            addressStreet = "Filmová 58",
            addressCity = "Praha",
            addressPostCode = "150 00",
            contactName1 = "<PERSON>",
            contactName2 = "<PERSON>",
            contactName3 = "Tom",
            contactPhone1 = "+************",
            contactPhone2 = "+************",
            contactPhone3 = "*********",
            contactEmails = setOf("<EMAIL>", "<EMAIL>"),
            bankName = "AirBank, a.s.",
            bankAccount = "***********/0300",
            idNumber = "********",
            taxIdNumber = "SK2022916731",
            vatRate = 20,
            note = "Best note ever"
        )

        val expectedResponse = AdminGetDistributorResponse(
            id = distributor.id,
            code = "DISTR_1",
            disfilmCode = "01",
            title = "Best movies ever, s.r.o.",
            addressStreet = "Filmová 58",
            addressCity = "Praha",
            addressPostCode = "150 00",
            contactName1 = "John",
            contactName2 = "Jerry",
            contactName3 = "Tom",
            contactPhone1 = "+************",
            contactPhone2 = "+************",
            contactPhone3 = "*********",
            contactEmails = setOf("<EMAIL>", "<EMAIL>"),
            bankName = "AirBank, a.s.",
            bankAccount = "***********/0300",
            idNumber = "********",
            taxIdNumber = "SK2022916731",
            vatRate = 20,
            note = "Best note ever"
        )

        assertEquals(expectedResponse, GetDistributorResponseMapper.map(distributor))
    }
}
