package com.cleevio.cinemax.api.module.branch.service

import com.cleevio.cinemax.api.DatabaseCleanup
import com.cleevio.cinemax.api.IntegrationTest
import com.cleevio.cinemax.api.module.branch.service.query.AdminGetBranchesQuery
import com.cleevio.cinemax.api.util.createBranch
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import kotlin.test.assertEquals

class AdminGetBranchesQueryServiceIT @Autowired constructor(
    private val underTest: AdminGetBranchesQueryService,
    private val branchRepository: BranchRepository,
) : IntegrationTest(DatabaseCleanup.AFTER_ALL) {

    @Test
    fun `test AdminGetBranchesQuery - should correctly return branches`() {
        branchRepository.saveAll(setOf(BRANCH_1, BRANCH_2, BRANCH_3))

        val response = underTest(AdminGetBranchesQuery())

        assertEquals(3, response.size)
        assertEquals(BRANCH_1.id, response[0].id)
        assertEquals(BRANCH_1.name, response[0].name)
        assertEquals(BRANCH_2.id, response[1].id)
        assertEquals(BRANCH_2.name, response[1].name)
        assertEquals(BRANCH_3.id, response[2].id)
        assertEquals(BRANCH_3.name, response[2].name)
    }
}

private val BRANCH_1 = createBranch()
private val BRANCH_2 = createBranch(
    originalId = 2,
    productSalesCode = 2,
    auditoriumOriginalCodePrefix = "6000",
    code = "510001",
    name = "Kosice"
)
private val BRANCH_3 = createBranch(
    originalId = 3,
    productSalesCode = 3,
    auditoriumOriginalCodePrefix = "7000",
    code = "510002",
    name = "Martin"
)
