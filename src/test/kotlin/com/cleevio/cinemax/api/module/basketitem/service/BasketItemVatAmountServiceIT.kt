package com.cleevio.cinemax.api.module.basketitem.service

import com.cleevio.cinemax.api.DatabaseCleanup
import com.cleevio.cinemax.api.IntegrationTest
import com.cleevio.cinemax.api.common.constant.NO_TAX_RATE
import com.cleevio.cinemax.api.common.constant.REDUCED_TAX_RATE
import com.cleevio.cinemax.api.common.constant.STANDARD_TAX_RATE
import com.cleevio.cinemax.api.common.util.isEqualTo
import com.cleevio.cinemax.api.module.basketitem.constant.BasketItemType
import com.cleevio.cinemax.api.module.basketitem.entity.BasketItem
import com.cleevio.cinemax.api.module.branch.service.BranchRepository
import com.cleevio.cinemax.api.module.product.constant.ProductType
import com.cleevio.cinemax.api.module.product.service.ProductService
import com.cleevio.cinemax.api.module.productcategory.constant.ProductCategoryType
import com.cleevio.cinemax.api.module.productcategory.service.ProductCategoryService
import com.cleevio.cinemax.api.module.productcomposition.service.ProductCompositionService
import com.cleevio.cinemax.api.util.createBranch
import com.cleevio.cinemax.api.util.createProduct
import com.cleevio.cinemax.api.util.createProductCategory
import com.cleevio.cinemax.api.util.createProductComposition
import com.cleevio.cinemax.api.util.mapToCreateOrUpdateProductCategoryCommand
import com.cleevio.cinemax.api.util.mapToCreateOrUpdateProductCompositionCommand
import com.cleevio.cinemax.api.util.mapToSyncCreateOrUpdateProductCommand
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import org.springframework.beans.factory.annotation.Autowired
import java.util.UUID
import kotlin.test.assertTrue

class BasketItemVatAmountServiceIT @Autowired constructor(
    private val underTest: BasketItemVatAmountService,
    private val productService: ProductService,
    private val productCategoryService: ProductCategoryService,
    private val productCompositionService: ProductCompositionService,
    private val branchRepository: BranchRepository,
) : IntegrationTest(DatabaseCleanup.AFTER_EACH) {

    @BeforeEach
    fun setUp() {
        branchRepository.save(createBranch())

        setOf(PRODUCT_CATEGORY_STANDARD_TAX, PRODUCT_CATEGORY_REDUCED_TAX, PRODUCT_CATEGORY_NO_TAX).forEach {
            productCategoryService.syncCreateOrUpdateProductCategory(mapToCreateOrUpdateProductCategoryCommand(it))
        }

        setOf(
            PRODUCT_STANDARD_TAX,
            PRODUCT_REDUCED_TAX,
            PRODUCT_NO_TAX,
            PRODUCT_DISCOUNT,
            PRODUCT_IN_PRODUCT
        ).forEach { product ->
            val productCategoryId = when (product.id) {
                PRODUCT_STANDARD_TAX.id -> PRODUCT_CATEGORY_STANDARD_TAX.id
                PRODUCT_REDUCED_TAX.id -> PRODUCT_CATEGORY_REDUCED_TAX.id
                PRODUCT_NO_TAX.id -> PRODUCT_CATEGORY_NO_TAX.id
                PRODUCT_DISCOUNT.id -> PRODUCT_CATEGORY_STANDARD_TAX.id
                PRODUCT_IN_PRODUCT.id -> PRODUCT_CATEGORY_STANDARD_TAX.id
                else -> error("Unknown product.")
            }
            productService.syncCreateOrUpdateProduct(mapToSyncCreateOrUpdateProductCommand(product, productCategoryId))
        }

        setOf(PRODUCT_IN_PRODUCT_COMPOSITION_1, PRODUCT_IN_PRODUCT_COMPOSITION_2).forEach {
            productCompositionService.createOrUpdateProductComposition(
                mapToCreateOrUpdateProductCompositionCommand(it)
            )
        }
    }

    @Test
    fun `test determineVatAmount - ticket basket item - should use standard tax rate`() {
        val basketItem = BasketItem(
            basketId = UUID.randomUUID(),
            type = BasketItemType.TICKET,
            price = 100.00.toBigDecimal(),
            quantity = 1,
            isCancelled = false,
            vatAmount = 0.toBigDecimal()
        )

        val vatAmount = underTest.determineVatAmount(
            basketItemType = basketItem.type,
            price = basketItem.price,
            quantity = basketItem.quantity,
            productId = basketItem.productId
        )

        // standard tax rate is 23%, so VAT amount should be 100 - (100 / 1.23) = 18.70
        assertTrue(18.70.toBigDecimal() isEqualTo vatAmount)
    }

    @Test
    fun `test determineVatAmount - product basket item with standard tax rate - should calculate correctly`() {
        val basketItem = BasketItem(
            basketId = UUID.randomUUID(),
            productId = PRODUCT_STANDARD_TAX.id,
            type = BasketItemType.PRODUCT,
            price = 50.00.toBigDecimal(),
            quantity = 1,
            isCancelled = false,
            vatAmount = 0.toBigDecimal()
        )

        val vatAmount = underTest.determineVatAmount(
            basketItemType = basketItem.type,
            price = basketItem.price,
            quantity = basketItem.quantity,
            productId = basketItem.productId
        )

        // standard tax rate is 23%, so VAT amount should be 50 - (50 / 1.23) = 9.35
        assertTrue(9.35.toBigDecimal() isEqualTo vatAmount)
    }

    @Test
    fun `test determineVatAmount - product basket item with quantity greater than 1 - should calculate correctly`() {
        val basketItem = BasketItem(
            basketId = UUID.randomUUID(),
            productId = PRODUCT_STANDARD_TAX.id,
            type = BasketItemType.PRODUCT,
            price = 50.00.toBigDecimal(),
            quantity = 2,
            isCancelled = false,
            vatAmount = 0.toBigDecimal()
        )

        val vatAmount = underTest.determineVatAmount(
            basketItemType = basketItem.type,
            price = basketItem.price,
            quantity = basketItem.quantity,
            productId = basketItem.productId
        )

        // standard tax rate is 23%, so VAT amount should be 50 - (50 / 1.23) = 9.35 * quantity (2)
        assertTrue(18.7.toBigDecimal() isEqualTo vatAmount)
    }

    @Test
    fun `test determineVatAmount - product basket item with non-rounded price - should calculate to 2 decimal places`() {
        val basketItem = BasketItem(
            basketId = UUID.randomUUID(),
            productId = PRODUCT_STANDARD_TAX.id,
            type = BasketItemType.PRODUCT,
            price = 45.67.toBigDecimal(),
            quantity = 1,
            isCancelled = false,
            vatAmount = 0.toBigDecimal()
        )

        val vatAmount = underTest.determineVatAmount(
            basketItemType = basketItem.type,
            price = basketItem.price,
            quantity = basketItem.quantity,
            productId = basketItem.productId
        )

        // standard tax rate is 23%, so VAT amount should be 45.67 - (45.67 / 1.23) = 8,********* = 8,54 (rounded)
        assertTrue(8.54.toBigDecimal() isEqualTo vatAmount)
    }

    @Test
    fun `test determineVatAmount - product basket item with reduced tax rate - should calculate correctly`() {
        val basketItem = BasketItem(
            basketId = UUID.randomUUID(),
            productId = PRODUCT_REDUCED_TAX.id,
            type = BasketItemType.PRODUCT,
            price = 30.00.toBigDecimal(),
            quantity = 1,
            isCancelled = false,
            vatAmount = 0.toBigDecimal()
        )

        val vatAmount = underTest.determineVatAmount(
            basketItemType = basketItem.type,
            price = basketItem.price,
            quantity = basketItem.quantity,
            productId = basketItem.productId
        )

        // reduced tax rate is 19%, so VAT amount should be 30 - (30 / 1.19) = 4.79
        assertTrue(4.79.toBigDecimal() isEqualTo vatAmount)
    }

    @Test
    fun `test determineVatAmount - product basket item with no tax - should return zero`() {
        val basketItem = BasketItem(
            basketId = UUID.randomUUID(),
            productId = PRODUCT_NO_TAX.id,
            type = BasketItemType.PRODUCT,
            price = 25.00.toBigDecimal(),
            quantity = 1,
            isCancelled = false,
            vatAmount = 0.toBigDecimal()
        )

        val vatAmount = underTest.determineVatAmount(
            basketItemType = basketItem.type,
            price = basketItem.price,
            quantity = basketItem.quantity,
            productId = basketItem.productId
        )

        // no tax rate is 0%, so VAT amount should be 0
        assertTrue(0.toBigDecimal() isEqualTo vatAmount)
    }

    @Test
    fun `test determineVatAmount - product discount basket item - should use product category tax rate`() {
        val basketItem = BasketItem(
            basketId = UUID.randomUUID(),
            productId = PRODUCT_DISCOUNT.id,
            type = BasketItemType.PRODUCT_DISCOUNT,
            price = (-10.00).toBigDecimal(),
            quantity = 1,
            isCancelled = false,
            vatAmount = 0.toBigDecimal()
        )

        val vatAmount = underTest.determineVatAmount(
            basketItemType = basketItem.type,
            price = basketItem.price,
            quantity = basketItem.quantity,
            productId = basketItem.productId
        )

        // standard tax rate is 23%, so VAT amount should be -10 - (-10 / 1.23) = -1.87
        assertTrue((-1.87).toBigDecimal() isEqualTo vatAmount)
    }

    @Test
    fun `test determineVatAmount - product in product basket item - should calculate complex tax rate`() {
        val basketItem = BasketItem(
            basketId = UUID.randomUUID(),
            productId = PRODUCT_IN_PRODUCT.id,
            type = BasketItemType.PRODUCT,
            price = 60.00.toBigDecimal(),
            quantity = 1,
            isCancelled = false,
            vatAmount = 0.toBigDecimal()
        )

        val vatAmount = underTest.determineVatAmount(
            basketItemType = basketItem.type,
            price = basketItem.price,
            quantity = basketItem.quantity,
            productId = basketItem.productId
        )

        // standard tax rate is 23%, so VAT amount for first product should be 45 - (45 / 1.23) = 8.41
        // reduced tax rate is 19%, so VAT amount for second product should be 25 - (25 / 1.19) = 3.99
        assertTrue(12.40.toBigDecimal() isEqualTo vatAmount)
    }

    @Test
    fun `test determineVatAmount - product basket item without productId - should throw error`() {
        val basketItem = BasketItem(
            basketId = UUID.randomUUID(),
            productId = null,
            type = BasketItemType.PRODUCT,
            price = 50.00.toBigDecimal(),
            quantity = 1,
            isCancelled = false,
            vatAmount = 0.toBigDecimal()
        )

        assertThrows<IllegalStateException> {
            underTest.determineVatAmount(
                basketItemType = basketItem.type,
                price = basketItem.price,
                quantity = basketItem.quantity,
                productId = basketItem.productId
            )
        }
    }

    @Test
    fun `test determineVatAmount - product discount basket item without productId - should throw error`() {
        val basketItem = BasketItem(
            basketId = UUID.randomUUID(),
            productId = null,
            type = BasketItemType.PRODUCT_DISCOUNT,
            price = (-5.00).toBigDecimal(),
            quantity = 1,
            isCancelled = false,
            vatAmount = 0.toBigDecimal()
        )

        assertThrows<IllegalStateException> {
            underTest.determineVatAmount(
                basketItemType = basketItem.type,
                price = basketItem.price,
                quantity = basketItem.quantity,
                productId = basketItem.productId
            )
        }
    }
}

private val PRODUCT_CATEGORY_STANDARD_TAX = createProductCategory(
    originalId = 1,
    code = "01",
    title = "Standard Tax Category",
    type = ProductCategoryType.PRODUCT,
    taxRate = STANDARD_TAX_RATE
)

private val PRODUCT_CATEGORY_REDUCED_TAX = createProductCategory(
    originalId = 2,
    code = "02",
    title = "Reduced Tax Category",
    type = ProductCategoryType.PRODUCT,
    taxRate = REDUCED_TAX_RATE
)

private val PRODUCT_CATEGORY_NO_TAX = createProductCategory(
    originalId = 3,
    code = "03",
    title = "No Tax Category",
    type = ProductCategoryType.PRODUCT,
    taxRate = NO_TAX_RATE
)

private val PRODUCT_STANDARD_TAX = createProduct(
    originalId = 1,
    code = "00001",
    productCategoryId = PRODUCT_CATEGORY_STANDARD_TAX.id,
    title = "Standard Tax Product",
    type = ProductType.PRODUCT,
    price = 50.00.toBigDecimal()
)

private val PRODUCT_REDUCED_TAX = createProduct(
    originalId = 2,
    code = "00002",
    productCategoryId = PRODUCT_CATEGORY_REDUCED_TAX.id,
    title = "Reduced Tax Product",
    type = ProductType.PRODUCT,
    price = 30.00.toBigDecimal()
)

private val PRODUCT_NO_TAX = createProduct(
    originalId = 3,
    code = "00003",
    productCategoryId = PRODUCT_CATEGORY_NO_TAX.id,
    title = "No Tax Product",
    type = ProductType.PRODUCT,
    price = 25.00.toBigDecimal()
)

private val PRODUCT_DISCOUNT = createProduct(
    originalId = 4,
    code = "00004",
    productCategoryId = PRODUCT_CATEGORY_STANDARD_TAX.id,
    title = "Standard Tax Discount",
    type = ProductType.PRODUCT,
    price = (-10.00).toBigDecimal(),
    discountPercentage = 20
)

private val PRODUCT_IN_PRODUCT = createProduct(
    originalId = 5,
    code = "00005",
    productCategoryId = PRODUCT_CATEGORY_STANDARD_TAX.id,
    title = "Product In Product",
    type = ProductType.PRODUCT_IN_PRODUCT,
    price = 60.00.toBigDecimal()
)

private val PRODUCT_IN_PRODUCT_COMPOSITION_1 = createProductComposition(
    originalId = 1,
    productId = PRODUCT_IN_PRODUCT.id,
    productComponentId = null,
    amount = 1.toBigDecimal(),
    productInProductId = PRODUCT_STANDARD_TAX.id,
    productInProductPrice = PRODUCT_STANDARD_TAX.price.minus(10.toBigDecimal()),
    productInProductFlagshipPrice = PRODUCT_STANDARD_TAX.price.minus(5.toBigDecimal())
)
private val PRODUCT_IN_PRODUCT_COMPOSITION_2 = createProductComposition(
    originalId = 2,
    productId = PRODUCT_IN_PRODUCT.id,
    productComponentId = null,
    amount = 1.toBigDecimal(),
    productInProductId = PRODUCT_REDUCED_TAX.id,
    productInProductPrice = PRODUCT_REDUCED_TAX.price.minus(10.toBigDecimal()),
    productInProductFlagshipPrice = PRODUCT_REDUCED_TAX.price.minus(5.toBigDecimal())
)
