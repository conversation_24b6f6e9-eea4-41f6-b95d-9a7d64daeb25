package com.cleevio.cinemax.api.module.employee.service

import com.cleevio.cinemax.api.common.constant.SourceApp
import com.cleevio.cinemax.api.common.constant.TokenType
import com.cleevio.cinemax.api.common.service.JwtService
import com.cleevio.cinemax.api.module.employee.constant.EmployeeRole
import com.cleevio.cinemax.api.module.employee.entity.Employee
import com.cleevio.cinemax.api.module.employee.event.EmployeePasswordResetEvent
import com.cleevio.cinemax.api.module.employee.exception.AuthenticationFailedException
import com.cleevio.cinemax.api.module.employee.exception.EmployeeNotFoundException
import com.cleevio.cinemax.api.module.employee.exception.PasswordResetUnavailableException
import com.cleevio.cinemax.api.module.employee.service.command.DeleteEmployeeCommand
import com.cleevio.cinemax.api.module.employee.service.command.EnforcePasswordResetCommand
import com.cleevio.cinemax.api.module.employee.service.command.LoginEmployeeCommand
import com.cleevio.cinemax.api.module.employee.service.command.RefreshAccessTokenCommand
import com.cleevio.cinemax.api.module.employee.service.command.ResetPasswordCommand
import com.cleevio.cinemax.api.module.employee.util.encodePassword
import com.cleevio.cinemax.api.util.createEmployee
import io.mockk.Called
import io.mockk.Runs
import io.mockk.every
import io.mockk.just
import io.mockk.mockk
import io.mockk.verify
import io.mockk.verifySequence
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import org.springframework.context.ApplicationEventPublisher
import org.springframework.security.crypto.password.PasswordEncoder
import java.time.LocalDateTime
import kotlin.test.assertEquals
import kotlin.test.assertNotNull
import kotlin.test.assertNull

class EmployeeServiceTest {

    private val employeeRepository = mockk<EmployeeRepository>()
    private val employeeFinderRepository = mockk<EmployeeFinderRepository>()
    private val employeeFinderService = mockk<EmployeeFinderService>()
    private val jwtService = mockk<JwtService>()
    private val passwordEncoder = mockk<PasswordEncoder>()
    private val applicationEventPublisher = mockk<ApplicationEventPublisher>()

    private val underTest = EmployeeService(
        employeeRepository,
        employeeFinderRepository,
        employeeFinderService,
        jwtService,
        passwordEncoder,
        applicationEventPublisher,
        ACCESS_EXPIRATION,
        REFRESH_EXPIRATION
    )

    @Test
    fun `test login employee - command with not existing employee - should throw exception`() {
        every { employeeFinderRepository.findNonDeletedByUsername(any()) } returns null

        assertThrows<AuthenticationFailedException> {
            underTest.loginEmployee(LOGIN_EMPLOYEE_COMMAND)
        }

        verifySequence {
            employeeFinderRepository.findNonDeletedByUsername(LOGIN_EMPLOYEE_COMMAND.username)
            passwordEncoder wasNot Called
            jwtService wasNot Called
            employeeRepository wasNot Called
        }
    }

    @Test
    fun `test login employee - command with incorrect password - should throw exception`() {
        every { employeeFinderRepository.findNonDeletedByUsername(any()) } returns EMPLOYEE_1
        every { passwordEncoder.matches(any(), any()) } returns false

        assertThrows<AuthenticationFailedException> {
            underTest.loginEmployee(LOGIN_EMPLOYEE_COMMAND)
        }

        verifySequence {
            employeeFinderRepository.findNonDeletedByUsername(LOGIN_EMPLOYEE_COMMAND.username)
            passwordEncoder.matches(LOGIN_EMPLOYEE_COMMAND.password, EMPLOYEE_1.passwordHash)
            jwtService wasNot Called
            employeeRepository wasNot Called
        }
    }

    @Test
    fun `test login employee - correct username and password - should call everything accordingly`() {
        every { employeeFinderRepository.findNonDeletedByUsername(any()) } returns EMPLOYEE_1
        every { passwordEncoder.matches(any(), any()) } returns true
        every { jwtService.generateToken(any(), any(), any()) } returns DUMMY_TOKEN
        every { employeeRepository.save(any()) } returns EMPLOYEE_1

        val lastLoginAtBefore = EMPLOYEE_1.lastLoginAt
        val employeeCaptor = mutableListOf<Employee>()
        underTest.loginEmployee(LOGIN_EMPLOYEE_COMMAND)

        verifySequence {
            employeeFinderRepository.findNonDeletedByUsername(LOGIN_EMPLOYEE_COMMAND.username)
            passwordEncoder.matches(LOGIN_EMPLOYEE_COMMAND.password, EMPLOYEE_1.passwordHash)
            jwtService.generateToken(LOGIN_EMPLOYEE_COMMAND.username, TokenType.ACCESS, setOf(EmployeeRole.MANAGER))
            jwtService.generateToken(LOGIN_EMPLOYEE_COMMAND.username, TokenType.REFRESH, setOf(EmployeeRole.MANAGER))
            employeeRepository.save(capture(employeeCaptor))
        }

        assertEmployeeEquals(EMPLOYEE_1, employeeCaptor[0])
        val lastLoginAtAfter = employeeCaptor[0].lastLoginAt
        assertNull(lastLoginAtBefore)
        assertNotNull(lastLoginAtAfter)
    }

    @Test
    fun `test login employee - correct username and password, cashier logs to manager app - should throw exception`() {
        every { employeeFinderRepository.findNonDeletedByUsername(any()) } returns EMPLOYEE_2
        every { passwordEncoder.matches(any(), any()) } returns true

        assertThrows<AuthenticationFailedException> {
            underTest.loginEmployee(LOGIN_EMPLOYEE_COMMAND_FROM_MANAGER_APP)
        }

        verifySequence {
            employeeFinderRepository.findNonDeletedByUsername(LOGIN_EMPLOYEE_COMMAND_FROM_MANAGER_APP.username)
            passwordEncoder.matches(LOGIN_EMPLOYEE_COMMAND_FROM_MANAGER_APP.password, EMPLOYEE_2.passwordHash)
            jwtService wasNot Called
            employeeRepository wasNot Called
        }
    }

    @Test
    fun `test login employee - correct username and password, user accessible - should call everything accordingly`() {
        every { employeeFinderRepository.findNonDeletedByUsername(any()) } returns EMPLOYEE_1_ACCESSIBLE
        every { passwordEncoder.matches(any(), any()) } returns true
        every { jwtService.generateToken(any(), any(), any()) } returns DUMMY_TOKEN
        every { employeeRepository.save(any()) } returns EMPLOYEE_1_ACCESSIBLE

        val lastLoginAtBefore = EMPLOYEE_1_ACCESSIBLE.lastLoginAt
        val employeeCaptor = mutableListOf<Employee>()
        underTest.loginEmployee(LOGIN_EMPLOYEE_COMMAND)

        verifySequence {
            employeeFinderRepository.findNonDeletedByUsername(LOGIN_EMPLOYEE_COMMAND.username)
            passwordEncoder.matches(LOGIN_EMPLOYEE_COMMAND.password, EMPLOYEE_1_ACCESSIBLE.passwordHash)
            jwtService.generateToken(LOGIN_EMPLOYEE_COMMAND.username, TokenType.ACCESS, setOf(EmployeeRole.MANAGER))
            jwtService.generateToken(LOGIN_EMPLOYEE_COMMAND.username, TokenType.REFRESH, setOf(EmployeeRole.MANAGER))
            employeeRepository.save(capture(employeeCaptor))
        }

        assertEmployeeEquals(EMPLOYEE_1_ACCESSIBLE, employeeCaptor[0])
        val lastLoginAtAfter = employeeCaptor[0].lastLoginAt
        assertNull(lastLoginAtBefore)
        assertNotNull(lastLoginAtAfter)
    }

    @Test
    fun `test login employee - employee not accessible - should throw exception`() {
        every { employeeFinderRepository.findNonDeletedByUsername(any()) } returns EMPLOYEE_1_NOT_ACCESSIBLE
        every { passwordEncoder.matches(any(), any()) } returns true

        assertThrows<AuthenticationFailedException> {
            underTest.loginEmployee(LOGIN_EMPLOYEE_COMMAND)
        }

        verifySequence {
            employeeFinderRepository.findNonDeletedByUsername(LOGIN_EMPLOYEE_COMMAND.username)
            passwordEncoder.matches(LOGIN_EMPLOYEE_COMMAND.password, EMPLOYEE_1_NOT_ACCESSIBLE.passwordHash)
            jwtService wasNot Called
            employeeRepository wasNot Called
        }
    }

    @Test
    fun `test refresh access token - command with invalid token - should throw exception`() {
        every { jwtService.extractUsername(any()) } returns DUMMY_USERNAME
        every { jwtService.isTokenValid(any(), any(), any()) } returns false

        assertThrows<AuthenticationFailedException> {
            underTest.refreshAccessToken(REFRESH_ACCESS_TOKEN_COMMAND)
        }

        verifySequence {
            jwtService.extractUsername(REFRESH_ACCESS_TOKEN_COMMAND.refreshToken)
            jwtService.isTokenValid(REFRESH_ACCESS_TOKEN_COMMAND.refreshToken, DUMMY_USERNAME, TokenType.REFRESH)
            employeeFinderRepository wasNot Called
        }
        verify(exactly = 0) { jwtService.generateToken(any(), any(), any()) }
    }

    @Test
    fun `test refresh access token - token with not existing employee - should throw exception`() {
        every { jwtService.extractUsername(any()) } returns DUMMY_USERNAME
        every { jwtService.isTokenValid(any(), any(), any()) } returns true
        every { employeeFinderRepository.findNonDeletedByUsername(any()) } returns null

        assertThrows<AuthenticationFailedException> {
            underTest.refreshAccessToken(REFRESH_ACCESS_TOKEN_COMMAND)
        }

        verifySequence {
            jwtService.extractUsername(REFRESH_ACCESS_TOKEN_COMMAND.refreshToken)
            jwtService.isTokenValid(REFRESH_ACCESS_TOKEN_COMMAND.refreshToken, DUMMY_USERNAME, TokenType.REFRESH)
            employeeFinderRepository.findNonDeletedByUsername(DUMMY_USERNAME)
        }
        verify(exactly = 0) { jwtService.generateToken(any(), any(), any()) }
    }

    @Test
    fun `test refresh access token - valid token and existing employee - should call everything accordingly`() {
        every { jwtService.extractUsername(any()) } returns EMPLOYEE_1.username
        every { jwtService.isTokenValid(any(), any(), any()) } returns true
        every { employeeFinderRepository.findNonDeletedByUsername(any()) } returns EMPLOYEE_1
        every { jwtService.generateToken(any(), any(), any()) } returns DUMMY_TOKEN

        underTest.refreshAccessToken(REFRESH_ACCESS_TOKEN_COMMAND)

        verifySequence {
            jwtService.extractUsername(REFRESH_ACCESS_TOKEN_COMMAND.refreshToken)
            jwtService.isTokenValid(REFRESH_ACCESS_TOKEN_COMMAND.refreshToken, EMPLOYEE_1.username, TokenType.REFRESH)
            employeeFinderRepository.findNonDeletedByUsername(EMPLOYEE_1.username)
            jwtService.generateToken(EMPLOYEE_1.username, TokenType.ACCESS, setOf(EmployeeRole.MANAGER))
            jwtService.generateToken(EMPLOYEE_1.username, TokenType.REFRESH, setOf(EmployeeRole.MANAGER))
        }
    }

    @Test
    fun `test reset password - reset password is unavailable for employee - should throw exception`() {
        every { employeeFinderService.getNonDeletedByUsername(any()) } returns EMPLOYEE_1_PASSWORD_RESET_UNAVAILABLE

        assertThrows<PasswordResetUnavailableException> {
            underTest.resetPassword(RESET_PASSWORD_COMMAND)
        }

        verifySequence {
            employeeFinderService.getNonDeletedByUsername(RESET_PASSWORD_COMMAND.employeeUsername)
            passwordEncoder wasNot Called
            employeeRepository wasNot Called
            applicationEventPublisher wasNot Called
        }
    }

    @Test
    fun `test reset password - employee can reset password - should call everything accordingly`() {
        every { employeeFinderService.getNonDeletedByUsername(any()) } returns EMPLOYEE_2
        every { passwordEncoder.encode(any()) } returns DUMMY_PASSWORD_HASH
        every { employeeRepository.save(any()) } returns EMPLOYEE_2_AFTER_PASSWORD_RESET
        every { applicationEventPublisher.publishEvent(any<EmployeePasswordResetEvent>()) } just Runs

        underTest.resetPassword(RESET_PASSWORD_COMMAND)

        verifySequence {
            employeeFinderService.getNonDeletedByUsername(RESET_PASSWORD_COMMAND.employeeUsername)
            passwordEncoder.encode(RESET_PASSWORD_COMMAND.newPassword)
            employeeRepository.save(EMPLOYEE_2.apply { passwordReset = false })
            applicationEventPublisher.publishEvent(
                EmployeePasswordResetEvent(
                    employeeId = EMPLOYEE_2.id,
                    encodedPassword = encodePassword(RESET_PASSWORD_COMMAND.newPassword)
                )
            )
        }
    }

    @Test
    fun `test deleteEmployee - employee not found - should throw exception`() {
        every { employeeFinderService.getNonDeletedById(any()) } throws EmployeeNotFoundException()
        assertThrows<EmployeeNotFoundException> {
            underTest.deleteEmployee(
                DeleteEmployeeCommand(
                    employeeId = EMPLOYEE_2.id
                )
            )
        }

        verifySequence {
            employeeFinderService.getNonDeletedById(EMPLOYEE_2.id)
        }
    }

    @Test
    fun `test enforce reset password - employee not found - should throw exception`() {
        every { employeeFinderService.getNonDeletedById(any()) } throws EmployeeNotFoundException()

        assertThrows<EmployeeNotFoundException> {
            underTest.enforcePasswordReset(ENFORCE_PASSWORD_RESET_COMMAND)
        }

        verifySequence {
            employeeFinderService.getNonDeletedById(EMPLOYEE_1.id)
            passwordEncoder wasNot Called
            employeeRepository wasNot Called
            applicationEventPublisher wasNot Called
        }
    }

    private fun assertEmployeeEquals(expected: Employee, actual: Employee) {
        assertEquals(expected.originalId, actual.originalId)
        assertEquals(expected.username, actual.username)
        assertEquals(expected.fullName, actual.fullName)
        assertEquals(expected.passwordHash, actual.passwordHash)
        assertEquals(expected.passwordReset, actual.passwordReset)
        assertEquals(expected.role, actual.role)
        assertNotNull(actual.createdAt)
        assertNotNull(actual.updatedAt)
    }
}

private const val ACCESS_EXPIRATION = 900000
private const val REFRESH_EXPIRATION = 21600000
private const val DUMMY_TOKEN = "dummyToken"
private const val DUMMY_USERNAME = "dummyUsername"
private const val DUMMY_PASSWORD_HASH = "dummyHash"
private val LOGIN_EMPLOYEE_COMMAND = LoginEmployeeCommand(
    username = DUMMY_USERNAME,
    password = "dummyPassword",
    source = SourceApp.POS_APP
)
private val LOGIN_EMPLOYEE_COMMAND_FROM_MANAGER_APP = LoginEmployeeCommand(
    username = DUMMY_USERNAME,
    password = "dummyPassword",
    source = SourceApp.MANAGER_APP
)
private val REFRESH_ACCESS_TOKEN_COMMAND = RefreshAccessTokenCommand(
    refreshToken = "dummyRefreshToken"
)
private val EMPLOYEE_1 = createEmployee(
    originalId = 1,
    username = "manager",
    fullName = "Johnny Manager",
    role = EmployeeRole.MANAGER,
    passwordReset = true
)
private val EMPLOYEE_1_PASSWORD_RESET_UNAVAILABLE = createEmployee(
    originalId = 1,
    username = "manager",
    fullName = "Johnny Manager",
    role = EmployeeRole.MANAGER,
    passwordReset = false
)
private val EMPLOYEE_1_AFTER_PASSWORD_RESET = createEmployee(
    originalId = 1,
    username = "manager",
    fullName = "Johnny Manager",
    role = EmployeeRole.MANAGER,
    passwordReset = false,
    passwordHash = DUMMY_PASSWORD_HASH
)
private val EMPLOYEE_1_NOT_ACCESSIBLE = createEmployee(
    originalId = 1,
    username = "manager",
    fullName = "Johnny Manager",
    role = EmployeeRole.MANAGER,
    passwordReset = false,
    passwordHash = DUMMY_PASSWORD_HASH,
    accessibleAt = LocalDateTime.now().plusDays(3)
)
private val EMPLOYEE_1_ACCESSIBLE = createEmployee(
    originalId = 1,
    username = "manager",
    fullName = "Johnny Manager",
    role = EmployeeRole.MANAGER,
    passwordReset = false,
    passwordHash = DUMMY_PASSWORD_HASH,
    accessibleAt = LocalDateTime.now().minusDays(3)
)
private val EMPLOYEE_2 = createEmployee(
    originalId = 2,
    username = "johnny",
    fullName = "Johnny Cashier",
    role = EmployeeRole.CASHIER,
    passwordReset = true
)
private val EMPLOYEE_2_AFTER_PASSWORD_RESET = createEmployee(
    originalId = 2,
    username = "johnny",
    fullName = "Johnny Cashier",
    role = EmployeeRole.CASHIER,
    passwordReset = false,
    passwordHash = DUMMY_PASSWORD_HASH
)
private val RESET_PASSWORD_COMMAND = ResetPasswordCommand(
    employeeUsername = EMPLOYEE_2.username,
    newPassword = "newDummyPassword"
)
private val ENFORCE_PASSWORD_RESET_COMMAND = EnforcePasswordResetCommand(
    employeeId = EMPLOYEE_1.id
)
