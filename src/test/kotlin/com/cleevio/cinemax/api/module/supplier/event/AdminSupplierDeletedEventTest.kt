package com.cleevio.cinemax.api.module.supplier.event

import com.cleevio.cinemax.api.common.util.MESSAGING_MAPPER
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertNotNull
import org.junit.jupiter.api.Test

class AdminSupplierDeletedEventTest {

    @Test
    fun `test toMessagePayload - should create message payload with correct type and serialization`() {
        val event = AdminSupplierDeletedEvent(code = "1234")
        val expectedJson = """
            {
                "code": "1234"
            }
        """.trimIndent()

        val messagePayload = event.toMessagePayload()

        assertNotNull(messagePayload.id)
        assertNotNull(messagePayload.createdAt)
        assertEquals("SUPPLIER_DELETED", messagePayload.type.name)
        assertEquals(
            MESSAGING_MAPPER.readTree(expectedJson),
            MESSAGING_MAPPER.readTree(messagePayload.data)
        )
    }
}
