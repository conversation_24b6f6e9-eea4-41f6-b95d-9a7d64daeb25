package com.cleevio.cinemax.api.module.distributor.event.listener

import com.cleevio.cinemax.api.common.integration.pubsub.dto.MessagePayload
import com.cleevio.cinemax.api.common.service.PublisherService
import com.cleevio.cinemax.api.module.distributor.event.AdminDistributorCreatedOrUpdatedEvent
import com.cleevio.cinemax.api.module.distributor.event.AdminDistributorDeletedEvent
import io.mockk.every
import io.mockk.just
import io.mockk.mockk
import io.mockk.runs
import io.mockk.verify
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test

class DistributorMessagingEventListenerTest {

    private val publisherService: PublisherService = mockk<PublisherService>()
    private val underTest = DistributorMessagingEventListener(publisherService)

    @BeforeEach
    fun setUp() {
        every { publisherService.publish(any()) } just runs
    }

    @Test
    fun `listenToAdminDistributorCreatedOrUpdatedEvent - should publish message`() {
        val event = AdminDistributorCreatedOrUpdatedEvent(
            code = "1234",
            title = "Distributor Co."
        )

        underTest.listenToAdminDistributorCreatedOrUpdatedEvent(event)
        verify(exactly = 1) { publisherService.publish(any<MessagePayload>()) }
    }

    @Test
    fun `listenToAdminDistributorDeletedEvent - should publish message`() {
        val event = AdminDistributorDeletedEvent(code = "1234")

        underTest.listenToAdminDistributorDeletedEvent(event)

        verify(exactly = 1) { publisherService.publish(any<MessagePayload>()) }
    }
}
