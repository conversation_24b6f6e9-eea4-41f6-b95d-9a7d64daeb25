package com.cleevio.cinemax.api.module.product.service

import com.cleevio.cinemax.api.DatabaseCleanup
import com.cleevio.cinemax.api.IntegrationTest
import com.cleevio.cinemax.api.common.constant.REDUCED_TAX_RATE
import com.cleevio.cinemax.api.common.constant.STANDARD_TAX_RATE
import com.cleevio.cinemax.api.common.util.isEqualTo
import com.cleevio.cinemax.api.module.branch.constant.BranchType
import com.cleevio.cinemax.api.module.branch.service.BranchRepository
import com.cleevio.cinemax.api.module.file.exception.FileNotFoundException
import com.cleevio.cinemax.api.module.file.service.FileRepository
import com.cleevio.cinemax.api.module.product.constant.ProductType
import com.cleevio.cinemax.api.module.product.entity.Product
import com.cleevio.cinemax.api.module.product.event.AdminProductDeletedEvent
import com.cleevio.cinemax.api.module.product.event.ProductCreatedOrUpdatedEvent
import com.cleevio.cinemax.api.module.product.event.ProductDeletedEvent
import com.cleevio.cinemax.api.module.product.event.toMessagingEvent
import com.cleevio.cinemax.api.module.product.exception.InvalidProductCategoryTypeForProductTypeException
import com.cleevio.cinemax.api.module.product.exception.InvalidProductCompositionForProductTypeException
import com.cleevio.cinemax.api.module.product.exception.InvalidTaxRateForProductInProductException
import com.cleevio.cinemax.api.module.product.exception.PackagingDepositProductAlreadyExistsException
import com.cleevio.cinemax.api.module.product.exception.ProductNotFoundException
import com.cleevio.cinemax.api.module.product.service.command.AdminCreateProductCommand
import com.cleevio.cinemax.api.module.product.service.command.AdminCreateProductCommand.AdminCreateProductProductCompositionCommand
import com.cleevio.cinemax.api.module.product.service.command.AdminUpdateProductCommand
import com.cleevio.cinemax.api.module.product.service.command.AdminUpdateProductCommand.AdminUpdateProductProductCompositionCommand
import com.cleevio.cinemax.api.module.product.service.command.DeleteProductCommand
import com.cleevio.cinemax.api.module.product.service.command.MessagingCreateOrUpdateProductCommand
import com.cleevio.cinemax.api.module.product.service.command.MessagingDeleteProductCommand
import com.cleevio.cinemax.api.module.product.service.command.UpdateProductOriginalIdCommand
import com.cleevio.cinemax.api.module.product.service.command.UpdateProductStockQuantityCommand
import com.cleevio.cinemax.api.module.productcategory.constant.ProductCategoryType
import com.cleevio.cinemax.api.module.productcategory.exception.ProductCategoryNotFoundException
import com.cleevio.cinemax.api.module.productcategory.service.ProductCategoryJooqFinderService
import com.cleevio.cinemax.api.module.productcategory.service.ProductCategoryRepository
import com.cleevio.cinemax.api.module.productcategory.service.ProductCategoryService
import com.cleevio.cinemax.api.module.productcomponent.constant.ProductComponentUnit
import com.cleevio.cinemax.api.module.productcomponent.entity.ProductComponent
import com.cleevio.cinemax.api.module.productcomponent.service.ProductComponentJpaFinderService
import com.cleevio.cinemax.api.module.productcomponent.service.ProductComponentRepository
import com.cleevio.cinemax.api.module.productcomponent.service.ProductComponentService
import com.cleevio.cinemax.api.module.productcomponentcategory.service.ProductComponentCategoryRepository
import com.cleevio.cinemax.api.module.productcomposition.service.ProductCompositionRepository
import com.cleevio.cinemax.api.module.productcomposition.service.ProductCompositionService
import com.cleevio.cinemax.api.util.assertProductAndProductCompositionsToAdminCreateProductCommand
import com.cleevio.cinemax.api.util.assertProductAndProductCompositionsToAdminUpdateProductCommand
import com.cleevio.cinemax.api.util.assertProductAndProductCompositionsToMessagingCreateOrUpdateProductCommand
import com.cleevio.cinemax.api.util.createBranch
import com.cleevio.cinemax.api.util.createFile
import com.cleevio.cinemax.api.util.createProduct
import com.cleevio.cinemax.api.util.createProductCategory
import com.cleevio.cinemax.api.util.createProductComponent
import com.cleevio.cinemax.api.util.createProductComponentCategory
import com.cleevio.cinemax.api.util.createProductComposition
import com.cleevio.cinemax.api.util.createProductFromAdminCreateCommand
import com.cleevio.cinemax.api.util.mapToCreateOrUpdateProductCategoryCommand
import com.cleevio.cinemax.api.util.mapToCreateOrUpdateProductComponentCommand
import com.cleevio.cinemax.api.util.mapToCreateOrUpdateProductCompositionCommand
import com.cleevio.cinemax.api.util.mapToSyncCreateOrUpdateProductCommand
import com.cleevio.cinemax.api.util.toUUID
import io.mockk.Called
import io.mockk.Runs
import io.mockk.every
import io.mockk.just
import io.mockk.verify
import io.mockk.verifySequence
import jakarta.validation.ConstraintViolationException
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertDoesNotThrow
import org.junit.jupiter.api.assertThrows
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.Arguments
import org.junit.jupiter.params.provider.CsvSource
import org.junit.jupiter.params.provider.MethodSource
import org.springframework.beans.factory.annotation.Autowired
import java.math.BigDecimal
import java.util.UUID
import java.util.stream.Stream
import kotlin.jvm.optionals.getOrNull
import kotlin.test.assertEquals
import kotlin.test.assertFalse
import kotlin.test.assertNotNull
import kotlin.test.assertNull
import kotlin.test.assertTrue

class ProductServiceIT @Autowired constructor(
    private val underTest: ProductService,
    private val productJooqFinderService: ProductJooqFinderService,
    private val productCategoryJooqFinderService: ProductCategoryJooqFinderService,
    private val productCategoryService: ProductCategoryService,
    private val productCompositionService: ProductCompositionService,
    private val productComponentService: ProductComponentService,
    private val productComponentCategoryRepository: ProductComponentCategoryRepository,
    private val fileRepository: FileRepository,
    private val productRepository: ProductRepository,
    private val productCompositionRepository: ProductCompositionRepository,
    private val productComponentRepository: ProductComponentRepository,
    private val productCategoryRepository: ProductCategoryRepository,
    private val productComponentJpaFinderService: ProductComponentJpaFinderService,
    private val branchRepository: BranchRepository,
) : IntegrationTest(DatabaseCleanup.AFTER_EACH) {

    @BeforeEach
    fun setUp() {
        branchRepository.save(BRANCH_1)
        fileRepository.saveAll(setOf(FILE_1, FILE_2))
        setOf(PRODUCT_CATEGORY_1, PRODUCT_CATEGORY_2).forEach {
            productCategoryService.syncCreateOrUpdateProductCategory(mapToCreateOrUpdateProductCategoryCommand(it))
        }
        setOf(PRODUCT_1 to PRODUCT_CATEGORY_1.id, PRODUCT_2 to PRODUCT_CATEGORY_2.id).forEach {
            underTest.syncCreateOrUpdateProduct(mapToSyncCreateOrUpdateProductCommand(it.first, it.second))
        }
        productComponentCategoryRepository.save(PRODUCT_COMPONENT_CATEGORY_1)
        setOf(
            PRODUCT_COMPONENT_1,
            PRODUCT_COMPONENT_2,
            PRODUCT_COMPONENT_3,
            PRODUCT_COMPONENT_4,
            PRODUCT_COMPONENT_5
        ).forEach {
            productComponentService.syncCreateOrUpdateProductComponent(
                mapToCreateOrUpdateProductComponentCommand(it)
            )
        }
        setOf(
            PRODUCT_COMPOSITION_1,
            PRODUCT_COMPOSITION_2,
            PRODUCT_COMPOSITION_3,
            PRODUCT_COMPOSITION_4,
            PRODUCT_COMPOSITION_5
        ).forEach {
            productCompositionService.createOrUpdateProductComposition(
                mapToCreateOrUpdateProductCompositionCommand(it)
            )
        }

        every { applicationEventPublisherMock.publishEvent(any<ProductCreatedOrUpdatedEvent>()) } just Runs
        every { applicationEventPublisherMock.publishEvent(any<ProductDeletedEvent>()) } just Runs
    }

    @Test
    fun `test syncCreateOrUpdateProduct - should create product`() {
        val category = productCategoryJooqFinderService.findNonDeletedByOriginalId(PRODUCT_CATEGORY_1.originalId!!)
        val command = mapToSyncCreateOrUpdateProductCommand(PRODUCT_1, category!!.id)
        underTest.syncCreateOrUpdateProduct(command)

        val createdProduct = productJooqFinderService.findNonDeletedByOriginalId(PRODUCT_1.originalId!!)
        assertNotNull(createdProduct)
        assertProductEquals(PRODUCT_1, createdProduct)
    }

    @Test
    fun `test syncCreateOrUpdateProduct - one product exists - insert equal product so it should update`() {
        val category = productCategoryJooqFinderService.findNonDeletedByOriginalId(PRODUCT_CATEGORY_1.originalId!!)
        val command = mapToSyncCreateOrUpdateProductCommand(PRODUCT_1, category!!.id)
        underTest.syncCreateOrUpdateProduct(command)
        underTest.syncCreateOrUpdateProduct(command)

        val updatedProduct = productJooqFinderService.findNonDeletedByOriginalId(PRODUCT_1.originalId!!)
        assertNotNull(updatedProduct)
        assertProductEquals(PRODUCT_1, updatedProduct)

        val products = productJooqFinderService.findAllNonDeleted()
        assertEquals(products.size, 2)
        assertTrue { products.first { it.id == PRODUCT_1.id }.updatedAt.isAfter(PRODUCT_1.updatedAt) }
    }

    @Test
    fun `test syncCreateOrUpdateProduct - two products - should create two products`() {
        val category = productCategoryJooqFinderService.findNonDeletedByOriginalId(PRODUCT_CATEGORY_1.originalId!!)
        val command1 = mapToSyncCreateOrUpdateProductCommand(PRODUCT_1, category!!.id)
        underTest.syncCreateOrUpdateProduct(command1)
        val command2 = mapToSyncCreateOrUpdateProductCommand(PRODUCT_2, category.id)
        underTest.syncCreateOrUpdateProduct(command2)

        val categories = productJooqFinderService.findAllNonDeleted()
        assertEquals(categories.size, 2)
        assertProductEquals(PRODUCT_1, categories.first { it.originalId == PRODUCT_1.originalId })
        assertProductEquals(PRODUCT_2, categories.first { it.originalId == PRODUCT_2.originalId })
    }

    @Test
    fun `test syncCreateOrUpdateProduct - command with null attribute - entity attr is null`() {
        val category = productCategoryJooqFinderService.findNonDeletedByOriginalId(PRODUCT_CATEGORY_1.originalId!!)
        val command = mapToSyncCreateOrUpdateProductCommand(PRODUCT_1, category!!.id)
        underTest.syncCreateOrUpdateProduct(command)

        val createdProduct = productJooqFinderService.findNonDeletedByOriginalId(PRODUCT_1.originalId!!)
        assertNotNull(createdProduct)
        assertProductEquals(PRODUCT_1, createdProduct)

        val commandWithNullOrder = command.copy(
            order = null
        )

        underTest.syncCreateOrUpdateProduct(commandWithNullOrder)

        val updatedProduct = productJooqFinderService.findNonDeletedByOriginalId(PRODUCT_1.originalId!!)
        assertNotNull(updatedProduct)
        assertEquals(PRODUCT_1.originalId, updatedProduct.originalId)
        assertEquals(PRODUCT_1.title, updatedProduct.title)
        assertEquals(PRODUCT_1.type, updatedProduct.type)
        assertTrue(PRODUCT_1.price isEqualTo updatedProduct.price)
        assertEquals(PRODUCT_1.active, updatedProduct.active)
        assertNull(updatedProduct.order)
        assertEquals(PRODUCT_1.soldInBuffet, updatedProduct.soldInBuffet)
        assertEquals(PRODUCT_1.soldInCafe, updatedProduct.soldInCafe)
        assertEquals(PRODUCT_1.soldInVip, updatedProduct.soldInVip)
        assertNotNull(updatedProduct.createdAt)
        assertNotNull(updatedProduct.updatedAt)
        assertTrue { updatedProduct.updatedAt.isAfter(PRODUCT_1.updatedAt) }
    }

    @Test
    fun `test syncCreateOrUpdateProduct - command with blank string - should throw exception`() {
        val command = mapToSyncCreateOrUpdateProductCommand(PRODUCT_2, PRODUCT_CATEGORY_1.id)
        val commandWithBlankString = command.copy(
            title = ""
        )
        assertThrows<ConstraintViolationException> {
            underTest.syncCreateOrUpdateProduct(commandWithBlankString)
        }
    }

    @Test
    fun `test deleteProduct - product does not exist - should throw`() {
        assertThrows<ProductNotFoundException> {
            underTest.deleteProduct(DeleteProductCommand(1.toUUID()))
        }
    }

    @Test
    fun `test deleteProduct - product exists - should soft delete product`() {
        every { applicationEventPublisherMock.publishEvent(any<AdminProductDeletedEvent>()) } just Runs
        val productToDelete = productRepository.save(createProductFromAdminCreateCommand(ADMIN_CREATE_PRODUCT_COMMAND))

        underTest.deleteProduct(DeleteProductCommand(productToDelete.id))

        val deletedProduct = productRepository.findById(productToDelete.id).getOrNull()
        assertNotNull(deletedProduct)
        assertTrue(deletedProduct.deletedAt != null)
        assertTrue(deletedProduct.isDeleted())

        verify { applicationEventPublisherMock.publishEvent(AdminProductDeletedEvent(productToDelete.code)) }
        verify { applicationEventPublisherMock.publishEvent(ProductDeletedEvent(productToDelete.id)) }
    }

    @ParameterizedTest
    @MethodSource("updateProductStockQuantityProvider")
    fun `test updateProductStockQuantity - should update product stock quantity correctly`(
        productId: UUID,
        originalStockQuantity: BigDecimal,
        quantityDifference: Int,
        expectedStockQuantity: BigDecimal,
    ) {
        assertStockQuantity(productId, originalStockQuantity)

        underTest.updateProductStockQuantity(
            UpdateProductStockQuantityCommand(
                productId = productId,
                quantityDifference = quantityDifference
            )
        )

        assertStockQuantity(productId, expectedStockQuantity)
    }

    @Test
    fun `test updateProductStockQuantity - product is PRODUCT_IN_PRODUCT type - should update stock quantity of all product components`() {
        val command = ADMIN_CREATE_PRODUCT_COMMAND.copy(
            type = ProductType.PRODUCT_IN_PRODUCT,
            taxRate = null,
            productCategoryId = PRODUCT_CATEGORY_1.id,
            productComposition = listOf(
                AdminCreateProductProductCompositionCommand(
                    productComponentId = null,
                    productInProductId = PRODUCT_2.id,
                    quantity = 1.toBigDecimal(),
                    productInProductPrice = 100.toBigDecimal()
                ),
                AdminCreateProductProductCompositionCommand(
                    productComponentId = null,
                    productInProductId = PRODUCT_1.id,
                    quantity = 1.toBigDecimal(),
                    productInProductPrice = 11.toBigDecimal()
                )
            )
        )

        val productId = underTest.adminCreateProduct(command)

        assertStockQuantity(productId, 125.toBigDecimal())

        underTest.updateProductStockQuantity(
            UpdateProductStockQuantityCommand(
                productId = productId,
                quantityDifference = 5
            )
        )
        assertStockQuantity(productId, 120.toBigDecimal())
        val productComponentDataMap =
            productComponentJpaFinderService.findAllProductComponentDataModelsByProductIdIn(setOf(productId))
                .associateBy { it.productComponentId }

        assertEquals(5, productComponentDataMap.size)

        val expectedStockQuantity1 = PRODUCT_COMPONENT_1.expectedStockQuantity(5, PRODUCT_COMPOSITION_1.amount)
        val expectedStockQuantity2 = PRODUCT_COMPONENT_2.expectedStockQuantity(5, PRODUCT_COMPOSITION_2.amount)
        val expectedStockQuantity3 = PRODUCT_COMPONENT_3.expectedStockQuantity(5, PRODUCT_COMPOSITION_3.amount)
        val expectedStockQuantity4 = PRODUCT_COMPONENT_4.expectedStockQuantity(5, PRODUCT_COMPOSITION_4.amount)
        val expectedStockQuantity5 = PRODUCT_COMPONENT_5.expectedStockQuantity(5, PRODUCT_COMPOSITION_5.amount)

        assertTrue(expectedStockQuantity1 isEqualTo productComponentDataMap[PRODUCT_COMPONENT_1.id]!!.stockQuantity)
        assertTrue(expectedStockQuantity2 isEqualTo productComponentDataMap[PRODUCT_COMPONENT_2.id]!!.stockQuantity)
        assertTrue(expectedStockQuantity3 isEqualTo productComponentDataMap[PRODUCT_COMPONENT_3.id]!!.stockQuantity)
        assertTrue(expectedStockQuantity4 isEqualTo productComponentDataMap[PRODUCT_COMPONENT_4.id]!!.stockQuantity)
        assertTrue(expectedStockQuantity5 isEqualTo productComponentDataMap[PRODUCT_COMPONENT_5.id]!!.stockQuantity)
    }

    @ParameterizedTest
    @CsvSource(
        value = [
            "23, true",
            "19, true",
            "5, true",
            "0, true",
            "-5, false",
            "15, false",
            "1, false",
            "null, true"
        ],
        nullValues = ["null"]
    )
    fun `test adminCreateProduct and adminUpdateProduct - commands correctly validate value of taxRate`(
        taxRate: Int?,
        valid: Boolean,
    ) {
        if (!valid) {
            assertThrows<ConstraintViolationException> {
                underTest.adminCreateProduct(ADMIN_CREATE_PRODUCT_COMMAND.copy(taxRate = taxRate))
            }
            assertThrows<ConstraintViolationException> {
                underTest.adminUpdateProduct(ADMIN_UPDATE_PRODUCT_COMMAND.copy(taxRate = taxRate))
            }
        } else {
            assertDoesNotThrow {
                val productId = underTest.adminCreateProduct(ADMIN_CREATE_PRODUCT_COMMAND.copy(taxRate = taxRate))
                underTest.adminUpdateProduct(ADMIN_UPDATE_PRODUCT_COMMAND.copy(id = productId, taxRate = taxRate))
            }
        }
    }

    @Test
    fun `test adminCreateProduct - image file id does not exists - should throw`() {
        assertThrows<FileNotFoundException> {
            underTest.adminCreateProduct(
                ADMIN_CREATE_PRODUCT_COMMAND.copy(imageFileId = 1.toUUID())
            )
        }
        verify { applicationEventPublisherMock wasNot Called }
    }

    @Test
    fun `test adminCreateProduct - product category does not exist - should throw`() {
        assertThrows<ProductCategoryNotFoundException> {
            underTest.adminCreateProduct(
                ADMIN_CREATE_PRODUCT_COMMAND.copy(productCategoryId = 1.toUUID())
            )
        }
        verify { applicationEventPublisherMock wasNot Called }
    }

    @Test
    fun `test adminCreateProduct - PRODUCT product type - empty product composition - should throw`() {
        assertThrows<InvalidProductCompositionForProductTypeException> {
            underTest.adminCreateProduct(
                ADMIN_CREATE_PRODUCT_COMMAND.copy(type = ProductType.PRODUCT, productComposition = emptyList())
            )
        }
        verify { applicationEventPublisherMock wasNot Called }
    }

    @Test
    fun `test adminCreateProduct - PRODUCT product type - productInProductId contains products other than packaging deposit - should throw`() {
        assertThrows<InvalidProductCompositionForProductTypeException> {
            underTest.adminCreateProduct(
                ADMIN_CREATE_PRODUCT_COMMAND.copy(
                    type = ProductType.PRODUCT,
                    productComposition = listOf(
                        AdminCreateProductProductCompositionCommand(
                            productComponentId = PRODUCT_COMPONENT_1.id,
                            productInProductId = PRODUCT_3.id,
                            quantity = 1.toBigDecimal()
                        )
                    )
                )
            )
        }
        verify { applicationEventPublisherMock wasNot Called }
    }

    @Test
    fun `test adminCreateProduct - PRODUCT product type with package deposit product - packaging deposit productInProductPrice is not null - should throw`() {
        productCategoryRepository.save(PRODUCT_CATEGORY_1)
        productRepository.save(PRODUCT_3)

        assertThrows<InvalidProductCompositionForProductTypeException> {
            underTest.adminCreateProduct(
                ADMIN_CREATE_PRODUCT_COMMAND.copy(
                    isPackagingDeposit = true,
                    productComposition = listOf(
                        AdminCreateProductProductCompositionCommand(
                            productComponentId = PRODUCT_COMPONENT_1.id,
                            productInProductId = null,
                            quantity = 1.toBigDecimal()
                        ),
                        AdminCreateProductProductCompositionCommand(
                            productComponentId = null,
                            productInProductId = PRODUCT_4.id,
                            quantity = 2.toBigDecimal(),
                            productInProductPrice = 0.3.toBigDecimal()
                        )
                    )
                )
            )
        }
    }

    @Test
    fun `test adminCreateProduct - ADDITIONAL_SALE product type - product composition is not empty - should throw`() {
        assertThrows<InvalidProductCompositionForProductTypeException> {
            underTest.adminCreateProduct(
                ADMIN_CREATE_PRODUCT_COMMAND.copy(
                    type = ProductType.ADDITIONAL_SALE
                )
            )
        }
        verify { applicationEventPublisherMock wasNot Called }
    }

    @ParameterizedTest
    @CsvSource(value = ["null, null", "100, 20"], nullValues = ["null"])
    fun `test adminCreateProduct - ADDITIONAL_SALE product type and DISCOUNT product category type - discount amount and percentage are both null or both not null - should throw`(
        discountAmount: BigDecimal?,
        discountPercentage: Int?,
    ) {
        productCategoryRepository.save(PRODUCT_CATEGORY_4_DISCOUNT)

        assertThrows<InvalidProductCategoryTypeForProductTypeException> {
            underTest.adminCreateProduct(
                ADMIN_CREATE_PRODUCT_COMMAND.copy(
                    type = ProductType.ADDITIONAL_SALE,
                    productCategoryId = PRODUCT_CATEGORY_4_DISCOUNT.id,
                    discountAmount = discountAmount,
                    discountPercentage = discountPercentage,
                    productComposition = emptyList()
                )
            )
        }
        verify { applicationEventPublisherMock wasNot Called }
    }

    @Test
    fun `test adminCreateProduct - PRODUCT_IN_PRODUCT product type - product composition is empty - should throw`() {
        assertThrows<InvalidProductCompositionForProductTypeException> {
            underTest.adminCreateProduct(
                ADMIN_CREATE_PRODUCT_COMMAND.copy(
                    type = ProductType.PRODUCT_IN_PRODUCT,
                    productComposition = emptyList()
                )
            )
        }
        verify { applicationEventPublisherMock wasNot Called }
    }

    @Test
    fun `test adminCreateProduct - PRODUCT_IN_PRODUCT product type - productComponent in composition is not null - should throw`() {
        productRepository.save(PRODUCT_3)

        assertThrows<InvalidProductCompositionForProductTypeException> {
            underTest.adminCreateProduct(
                ADMIN_CREATE_PRODUCT_COMMAND.copy(
                    type = ProductType.PRODUCT_IN_PRODUCT,
                    productComposition = listOf(
                        AdminCreateProductProductCompositionCommand(
                            productComponentId = PRODUCT_COMPONENT_1.id,
                            productInProductId = PRODUCT_3.id,
                            quantity = 1.toBigDecimal()
                        )
                    )
                )
            )
        }
        verify { applicationEventPublisherMock wasNot Called }
    }

    @Test
    fun `test adminCreateProduct - PRODUCT_IN_PRODUCT product type - product composition quantity is not whole number - should throw`() {
        productRepository.save(PRODUCT_3)

        assertThrows<InvalidProductCompositionForProductTypeException> {
            underTest.adminCreateProduct(
                ADMIN_CREATE_PRODUCT_COMMAND.copy(
                    type = ProductType.PRODUCT_IN_PRODUCT,
                    productComposition = listOf(
                        AdminCreateProductProductCompositionCommand(
                            productComponentId = null,
                            productInProductId = PRODUCT_3.id,
                            quantity = 1.1.toBigDecimal()
                        )
                    )
                )
            )
        }
        verify { applicationEventPublisherMock wasNot Called }
    }

    @Test
    fun `test adminCreateProduct - PRODUCT_IN_PRODUCT product type - productInProductPrice is null - should throw`() {
        productRepository.save(PRODUCT_3)

        assertThrows<InvalidProductCompositionForProductTypeException> {
            underTest.adminCreateProduct(
                ADMIN_CREATE_PRODUCT_COMMAND.copy(
                    type = ProductType.PRODUCT_IN_PRODUCT,
                    productComposition = listOf(
                        AdminCreateProductProductCompositionCommand(
                            productComponentId = null,
                            productInProductId = PRODUCT_3.id,
                            quantity = 1.toBigDecimal(),
                            productInProductPrice = null
                        )
                    )
                )
            )
        }
        verify { applicationEventPublisherMock wasNot Called }
    }

    @Test
    fun `test adminCreateProduct - PRODUCT_IN_PRODUCT product type - product category type is DISCOUNT - should throw`() {
        productCategoryRepository.save(PRODUCT_CATEGORY_4_DISCOUNT)
        productRepository.save(PRODUCT_3)

        assertThrows<InvalidProductCategoryTypeForProductTypeException> {
            underTest.adminCreateProduct(
                ADMIN_CREATE_PRODUCT_COMMAND.copy(
                    type = ProductType.PRODUCT_IN_PRODUCT,
                    productCategoryId = PRODUCT_CATEGORY_4_DISCOUNT.id,
                    productComposition = listOf(
                        AdminCreateProductProductCompositionCommand(
                            productComponentId = null,
                            productInProductId = PRODUCT_3.id,
                            quantity = 1.toBigDecimal(),
                            productInProductPrice = 2.5.toBigDecimal()
                        )
                    )
                )
            )
        }
        verify { applicationEventPublisherMock wasNot Called }
    }

    @Test
    fun `test adminCreateProduct - valid command for PRODUCT product type - should correctly create product and product composition`() {
        val productId = underTest.adminCreateProduct(ADMIN_CREATE_PRODUCT_COMMAND)
        val product = productRepository.findById(productId).getOrNull()
        val productCompositions = productCompositionRepository.findAllByProductId(productId)

        assertNotNull(product)
        assertProductAndProductCompositionsToAdminCreateProductCommand(
            expected = ADMIN_CREATE_PRODUCT_COMMAND,
            actualProduct = product,
            actualProductCompositions = productCompositions
        )
        verifySequence {
            applicationEventPublisherMock.publishEvent(ProductCreatedOrUpdatedEvent(productId))
            applicationEventPublisherMock.publishEvent(
                product.toMessagingEvent(
                    productCategoryCode = PRODUCT_CATEGORY_1.code,
                    imageFileId = FILE_1.id,
                    productComposition = ADMIN_CREATE_PRODUCT_COMMAND.productComposition,
                    productComponentIdToCode = mapOf(
                        PRODUCT_COMPONENT_1.id to PRODUCT_COMPONENT_1.code,
                        PRODUCT_COMPONENT_2.id to PRODUCT_COMPONENT_2.code
                    ),
                    productInProductIdToCode = emptyMap()
                )
            )
        }
    }

    @Test
    fun `test adminCreateProduct - valid command for PRODUCT product type with package deposit product - should not create another packaging deposit product`() {
        productCategoryRepository.save(PRODUCT_CATEGORY_5)
        productRepository.save(PRODUCT_4)

        val command = ADMIN_CREATE_PRODUCT_COMMAND.copy(
            isPackagingDeposit = true,
            productComposition = listOf(
                AdminCreateProductProductCompositionCommand(
                    productComponentId = PRODUCT_COMPONENT_1.id,
                    productInProductId = null,
                    quantity = 1.toBigDecimal()
                ),
                AdminCreateProductProductCompositionCommand(
                    productComponentId = null,
                    productInProductId = PRODUCT_4.id,
                    quantity = 2.toBigDecimal()
                )
            )
        )

        assertThrows<PackagingDepositProductAlreadyExistsException> {
            underTest.adminCreateProduct(command)
        }
    }

    @Test
    fun `test adminUpdateProduct - valid command for PRODUCT product type and packaging deposit already exists - should throw`() {
        productCategoryRepository.save(PRODUCT_CATEGORY_5)
        productRepository.save(PRODUCT_4)

        val createCommand = ADMIN_CREATE_PRODUCT_COMMAND.copy(
            isPackagingDeposit = false,
            productComposition = listOf(
                AdminCreateProductProductCompositionCommand(
                    productComponentId = PRODUCT_COMPONENT_1.id,
                    productInProductId = null,
                    quantity = 1.toBigDecimal()
                ),
                AdminCreateProductProductCompositionCommand(
                    productComponentId = null,
                    productInProductId = PRODUCT_4.id,
                    quantity = 2.toBigDecimal()
                )
            )
        )

        underTest.adminCreateProduct(createCommand)
        val createdProduct = productRepository.findAll().sortedByDescending { it.createdAt }[0]
        assertFalse(createdProduct.isPackagingDeposit)

        val updateCommand = ADMIN_UPDATE_PRODUCT_COMMAND.copy(id = createdProduct.id, isPackagingDeposit = true)
        assertThrows<PackagingDepositProductAlreadyExistsException> {
            underTest.adminUpdateProduct(updateCommand)
        }
    }

    @Test
    fun `test adminCreateProduct - valid command for ADDITIONAL_SALE product type - should correctly create product but no product composition`() {
        productCategoryRepository.save(PRODUCT_CATEGORY_4_DISCOUNT)

        val command = ADMIN_CREATE_PRODUCT_COMMAND.copy(
            type = ProductType.ADDITIONAL_SALE,
            productCategoryId = PRODUCT_CATEGORY_4_DISCOUNT.id,
            discountAmount = 20.toBigDecimal(),
            discountPercentage = null,
            productComposition = emptyList()
        )
        val productId = underTest.adminCreateProduct(command)
        val product = productRepository.findById(productId).getOrNull()
        val productCompositions = productCompositionRepository.findAllByProductId(productId)

        assertNotNull(product)
        assertProductAndProductCompositionsToAdminCreateProductCommand(
            expected = command,
            actualProduct = product,
            actualProductCompositions = productCompositions
        )
        verifySequence {
            applicationEventPublisherMock.publishEvent(ProductCreatedOrUpdatedEvent(productId))
            applicationEventPublisherMock.publishEvent(
                product.toMessagingEvent(
                    productCategoryCode = PRODUCT_CATEGORY_4_DISCOUNT.code,
                    imageFileId = FILE_1.id,
                    productComposition = emptyList(),
                    productComponentIdToCode = emptyMap(),
                    productInProductIdToCode = emptyMap()
                )
            )
        }
    }

    @Test
    fun `test adminCreateProduct - valid command for PRODUCT_IN_PRODUCT product type - should correctly create product and product composition`() {
        productCategoryRepository.save(PRODUCT_CATEGORY_3)
        productRepository.save(PRODUCT_3)

        val command = ADMIN_CREATE_PRODUCT_COMMAND.copy(
            type = ProductType.PRODUCT_IN_PRODUCT,
            taxRate = null,
            productCategoryId = PRODUCT_CATEGORY_3.id,
            productComposition = listOf(
                AdminCreateProductProductCompositionCommand(
                    productComponentId = null,
                    productInProductId = PRODUCT_3.id,
                    quantity = 1.toBigDecimal(),
                    productInProductPrice = 2.5.toBigDecimal()
                ),
                AdminCreateProductProductCompositionCommand(
                    productComponentId = null,
                    productInProductId = PRODUCT_2.id,
                    quantity = 2.toBigDecimal(),
                    productInProductPrice = 3.toBigDecimal()
                )
            )
        )
        val productId = underTest.adminCreateProduct(command)
        val product = productRepository.findById(productId).getOrNull()
        val productCompositions = productCompositionRepository.findAllByProductId(productId)

        assertNotNull(product)
        assertProductAndProductCompositionsToAdminCreateProductCommand(
            expected = command,
            actualProduct = product,
            actualProductCompositions = productCompositions
        )
        verifySequence {
            applicationEventPublisherMock.publishEvent(ProductCreatedOrUpdatedEvent(productId))
            applicationEventPublisherMock.publishEvent(
                product.toMessagingEvent(
                    productCategoryCode = PRODUCT_CATEGORY_3.code,
                    imageFileId = FILE_1.id,
                    productComposition = command.productComposition,
                    productComponentIdToCode = emptyMap(),
                    productInProductIdToCode = mapOf(
                        PRODUCT_3.id to PRODUCT_3.code,
                        PRODUCT_2.id to PRODUCT_2.code
                    )
                )
            )
        }
    }

    @Test
    fun `test adminUpdateProduct - product does not exist - should throw`() {
        assertThrows<ProductNotFoundException> {
            underTest.adminUpdateProduct(
                ADMIN_UPDATE_PRODUCT_COMMAND.copy(id = 1.toUUID())
            )
        }
        verify { applicationEventPublisherMock wasNot Called }
    }

    @Test
    fun `test adminUpdateProduct - PRODUCT product type - empty product composition - should throw`() {
        val originalProduct = productRepository.save(
            createProductFromAdminCreateCommand(ADMIN_CREATE_PRODUCT_COMMAND.copy(type = ProductType.PRODUCT))
        )

        assertThrows<InvalidProductCompositionForProductTypeException> {
            underTest.adminUpdateProduct(
                ADMIN_UPDATE_PRODUCT_COMMAND.copy(
                    id = originalProduct.id,
                    productComposition = emptyList()
                )
            )
        }
        verify { applicationEventPublisherMock wasNot Called }
    }

    @Test
    fun `test adminUpdateProduct - PRODUCT product type - productInProductId in composition is not null - should throw`() {
        val originalProduct = productRepository.save(
            createProductFromAdminCreateCommand(ADMIN_CREATE_PRODUCT_COMMAND.copy(type = ProductType.PRODUCT))
        )

        assertThrows<InvalidProductCompositionForProductTypeException> {
            underTest.adminUpdateProduct(
                ADMIN_UPDATE_PRODUCT_COMMAND.copy(
                    id = originalProduct.id,
                    productComposition = listOf(
                        AdminUpdateProductProductCompositionCommand(
                            productComponentId = PRODUCT_COMPONENT_3.id,
                            productInProductId = PRODUCT_3.id,
                            quantity = 1.toBigDecimal()
                        )
                    )
                )
            )
        }
        verify { applicationEventPublisherMock wasNot Called }
    }

    @Test
    fun `test adminUpdateProduct - PRODUCT product type with package deposit product - packaging deposit productInProductPrice is not null - should throw`() {
        val originalProduct = productRepository.save(
            createProductFromAdminCreateCommand(ADMIN_CREATE_PRODUCT_COMMAND.copy(type = ProductType.PRODUCT))
        )

        assertThrows<InvalidProductCompositionForProductTypeException> {
            underTest.adminUpdateProduct(
                ADMIN_UPDATE_PRODUCT_COMMAND.copy(
                    id = originalProduct.id,
                    productComposition = listOf(
                        AdminUpdateProductProductCompositionCommand(
                            productComponentId = PRODUCT_COMPONENT_1.id,
                            productInProductId = null,
                            quantity = 1.toBigDecimal()
                        ),
                        AdminUpdateProductProductCompositionCommand(
                            productComponentId = null,
                            productInProductId = PRODUCT_4.id,
                            quantity = 2.toBigDecimal(),
                            productInProductPrice = 0.3.toBigDecimal()
                        )
                    )
                )
            )
        }
    }

    @Test
    fun `test adminUpdateProduct - PRODUCT_IN_PRODUCT product type - product composition is empty - should throw`() {
        val originalProduct = productRepository.save(
            createProductFromAdminCreateCommand(ADMIN_CREATE_PRODUCT_COMMAND.copy(type = ProductType.PRODUCT_IN_PRODUCT))
        )

        assertThrows<InvalidProductCompositionForProductTypeException> {
            underTest.adminUpdateProduct(
                ADMIN_UPDATE_PRODUCT_COMMAND.copy(
                    id = originalProduct.id,
                    productComposition = emptyList()
                )
            )
        }
        verify { applicationEventPublisherMock wasNot Called }
    }

    @Test
    fun `test adminUpdateProduct - PRODUCT_IN_PRODUCT product type - product component in composition is not null - should throw`() {
        val originalProduct = productRepository.save(
            createProductFromAdminCreateCommand(ADMIN_CREATE_PRODUCT_COMMAND.copy(type = ProductType.PRODUCT_IN_PRODUCT))
        )

        assertThrows<InvalidProductCompositionForProductTypeException> {
            underTest.adminUpdateProduct(
                ADMIN_UPDATE_PRODUCT_COMMAND.copy(
                    id = originalProduct.id,
                    productComposition = listOf(
                        AdminUpdateProductProductCompositionCommand(
                            productComponentId = PRODUCT_COMPONENT_3.id,
                            productInProductId = null,
                            quantity = 1.toBigDecimal()
                        )
                    )
                )
            )
        }
        verify { applicationEventPublisherMock wasNot Called }
    }

    @Test
    fun `test adminUpdateProduct - PRODUCT_IN_PRODUCT product type - product composition quantity is not whole number - should throw`() {
        val originalProduct = productRepository.save(
            createProductFromAdminCreateCommand(ADMIN_CREATE_PRODUCT_COMMAND.copy(type = ProductType.PRODUCT_IN_PRODUCT))
        )

        assertThrows<InvalidProductCompositionForProductTypeException> {
            underTest.adminUpdateProduct(
                ADMIN_UPDATE_PRODUCT_COMMAND.copy(
                    id = originalProduct.id,
                    productComposition = listOf(
                        AdminUpdateProductProductCompositionCommand(
                            productComponentId = null,
                            productInProductId = PRODUCT_3.id,
                            quantity = 1.7.toBigDecimal()
                        )
                    )
                )
            )
        }
        verify { applicationEventPublisherMock wasNot Called }
    }

    @Test
    fun `test adminUpdateProduct - PRODUCT_IN_PRODUCT product type - command taxRate is not null - should throw`() {
        val originalProduct = productRepository.save(
            createProductFromAdminCreateCommand(ADMIN_CREATE_PRODUCT_COMMAND.copy(type = ProductType.PRODUCT_IN_PRODUCT))
        )

        assertThrows<InvalidTaxRateForProductInProductException> {
            underTest.adminUpdateProduct(
                ADMIN_UPDATE_PRODUCT_COMMAND.copy(
                    id = originalProduct.id,
                    productComposition = listOf(
                        AdminUpdateProductProductCompositionCommand(
                            productComponentId = null,
                            productInProductId = PRODUCT_3.id,
                            quantity = 1.toBigDecimal(),
                            productInProductPrice = 2.toBigDecimal()
                        )
                    ),
                    taxRate = 23
                )
            )
        }
        verify { applicationEventPublisherMock wasNot Called }
    }

    @Test
    fun `test adminUpdateProduct - PRODUCT_IN_PRODUCT product type - productInProductPrice is null - should throw`() {
        val originalProduct = productRepository.save(
            createProductFromAdminCreateCommand(ADMIN_CREATE_PRODUCT_COMMAND.copy(type = ProductType.PRODUCT_IN_PRODUCT))
        )

        assertThrows<InvalidProductCompositionForProductTypeException> {
            underTest.adminUpdateProduct(
                ADMIN_UPDATE_PRODUCT_COMMAND.copy(
                    id = originalProduct.id,
                    productComposition = listOf(
                        AdminUpdateProductProductCompositionCommand(
                            productComponentId = null,
                            productInProductId = PRODUCT_3.id,
                            quantity = 1.toBigDecimal(),
                            productInProductPrice = null
                        )
                    )
                )
            )
        }
        verify { applicationEventPublisherMock wasNot Called }
    }

    @Test
    fun `test adminUpdateProduct - ADDITIONAL_SALE product type with DISCOUNT category type - discount amount and percentage both null - should throw`() {
        productCategoryRepository.save(PRODUCT_CATEGORY_4_DISCOUNT)
        val originalProduct = productRepository.save(
            createProductFromAdminCreateCommand(
                ADMIN_CREATE_PRODUCT_COMMAND.copy(
                    type = ProductType.ADDITIONAL_SALE
                )
            )
        )

        assertThrows<InvalidProductCategoryTypeForProductTypeException> {
            underTest.adminUpdateProduct(
                ADMIN_UPDATE_PRODUCT_COMMAND.copy(
                    id = originalProduct.id,
                    productCategoryId = PRODUCT_CATEGORY_4_DISCOUNT.id,
                    discountAmount = null,
                    discountPercentage = null,
                    productComposition = emptyList()
                )
            )
        }
        verify { applicationEventPublisherMock wasNot Called }
    }

    @Test
    fun `test adminUpdateProduct - PRODUCT_IN_PRODUCT product type with DISCOUNT product category type - should throw`() {
        productCategoryRepository.save(PRODUCT_CATEGORY_4_DISCOUNT)
        val originalProduct = productRepository.save(
            createProductFromAdminCreateCommand(
                ADMIN_CREATE_PRODUCT_COMMAND.copy(
                    type = ProductType.PRODUCT_IN_PRODUCT,
                    productCategoryId = PRODUCT_CATEGORY_4_DISCOUNT.id
                )
            )
        )

        assertThrows<InvalidProductCategoryTypeForProductTypeException> {
            underTest.adminUpdateProduct(
                ADMIN_UPDATE_PRODUCT_COMMAND.copy(
                    id = originalProduct.id,
                    productCategoryId = PRODUCT_CATEGORY_4_DISCOUNT.id,
                    productComposition = listOf(
                        AdminUpdateProductProductCompositionCommand(
                            productComponentId = null,
                            productInProductId = PRODUCT_3.id,
                            quantity = 1.toBigDecimal(),
                            productInProductPrice = 2.5.toBigDecimal()
                        )
                    )
                )
            )
        }
        verify { applicationEventPublisherMock wasNot Called }
    }

    @Test
    fun `test adminUpdateProduct - valid command for PRODUCT product type - should correctly update product and product composition`() {
        val originalProduct = productRepository.save(
            createProductFromAdminCreateCommand(ADMIN_CREATE_PRODUCT_COMMAND)
        )
        val command = ADMIN_UPDATE_PRODUCT_COMMAND.copy(
            id = originalProduct.id,
            title = originalProduct.title
        )
        val updatedProductId = underTest.adminUpdateProduct(command)
        val updatedProduct = productRepository.findById(updatedProductId).getOrNull()
        val updatedProductCompositions = productCompositionRepository.findAllByProductId(updatedProductId)

        assertNotNull(updatedProduct)
        assertProductAndProductCompositionsToAdminUpdateProductCommand(
            expected = command,
            originalProductType = originalProduct.type,
            actualProduct = updatedProduct,
            actualProductCompositions = updatedProductCompositions
        )
        verifySequence {
            applicationEventPublisherMock.publishEvent(ProductCreatedOrUpdatedEvent(updatedProductId))
            applicationEventPublisherMock.publishEvent(
                updatedProduct.toMessagingEvent(
                    productCategoryCode = PRODUCT_CATEGORY_1.code,
                    imageFileId = FILE_2.id,
                    productComposition = ADMIN_UPDATE_PRODUCT_COMMAND.productComposition,
                    productComponentIdToCode = mapOf(
                        PRODUCT_COMPONENT_3.id to PRODUCT_COMPONENT_3.code,
                        PRODUCT_COMPONENT_4.id to PRODUCT_COMPONENT_4.code
                    ),
                    productInProductIdToCode = emptyMap()
                )
            )
        }
    }

    @Test
    fun `test adminUpdateProduct - valid command for PRODUCT_IN_PRODUCT product type - should correctly update product and product composition`() {
        productRepository.save(PRODUCT_3)
        val originalProduct = productRepository.save(
            createProductFromAdminCreateCommand(
                ADMIN_CREATE_PRODUCT_COMMAND.copy(
                    type = ProductType.PRODUCT_IN_PRODUCT,
                    taxRate = null
                )
            )
        )

        val updateCommand = ADMIN_UPDATE_PRODUCT_COMMAND.copy(
            id = originalProduct.id,
            taxRate = null,
            productComposition = listOf(
                AdminUpdateProductProductCompositionCommand(
                    productComponentId = null,
                    productInProductId = PRODUCT_3.id,
                    quantity = 1.toBigDecimal(),
                    productInProductPrice = 2.5.toBigDecimal()
                )
            )
        )

        val updatedProductId = underTest.adminUpdateProduct(updateCommand)
        val updatedProduct = productRepository.findById(updatedProductId).getOrNull()
        val updatedProductCompositions = productCompositionRepository.findAllByProductId(updatedProductId)

        assertNotNull(updatedProduct)
        assertProductAndProductCompositionsToAdminUpdateProductCommand(
            expected = updateCommand,
            originalProductType = originalProduct.type,
            actualProduct = updatedProduct,
            actualProductCompositions = updatedProductCompositions
        )
        verifySequence {
            applicationEventPublisherMock.publishEvent(ProductCreatedOrUpdatedEvent(updatedProductId))
            applicationEventPublisherMock.publishEvent(
                updatedProduct.toMessagingEvent(
                    productCategoryCode = PRODUCT_CATEGORY_1.code,
                    imageFileId = FILE_2.id,
                    productComposition = updateCommand.productComposition,
                    productComponentIdToCode = emptyMap(),
                    productInProductIdToCode = mapOf(
                        PRODUCT_3.id to PRODUCT_3.code
                    )
                )
            )
        }
    }

    @Test
    fun `test adminUpdateProduct - valid command for ADDITIONAL_SALE product type - should update product without product composition`() {
        productCategoryRepository.save(PRODUCT_CATEGORY_4_DISCOUNT)
        val originalProduct = productRepository.save(
            createProductFromAdminCreateCommand(
                ADMIN_CREATE_PRODUCT_COMMAND.copy(
                    type = ProductType.ADDITIONAL_SALE,
                    productCategoryId = PRODUCT_CATEGORY_4_DISCOUNT.id
                )
            )
        )

        val command = ADMIN_UPDATE_PRODUCT_COMMAND.copy(
            id = originalProduct.id,
            productCategoryId = PRODUCT_CATEGORY_4_DISCOUNT.id,
            discountAmount = 20.toBigDecimal(),
            discountPercentage = null,
            productComposition = emptyList()
        )
        val updatedProductId = underTest.adminUpdateProduct(command)
        val updatedProduct = productRepository.findById(updatedProductId).getOrNull()
        val updatedProductCompositions = productCompositionRepository.findAllByProductId(updatedProductId)

        assertNotNull(updatedProduct)
        assertTrue(updatedProductCompositions.isEmpty())
        assertProductAndProductCompositionsToAdminUpdateProductCommand(
            expected = command,
            originalProductType = ProductType.ADDITIONAL_SALE,
            actualProduct = updatedProduct,
            actualProductCompositions = updatedProductCompositions
        )
        verifySequence {
            applicationEventPublisherMock.publishEvent(ProductCreatedOrUpdatedEvent(updatedProductId))
            applicationEventPublisherMock.publishEvent(
                updatedProduct.toMessagingEvent(
                    productCategoryCode = PRODUCT_CATEGORY_4_DISCOUNT.code,
                    imageFileId = FILE_2.id,
                    productComposition = emptyList(),
                    productComponentIdToCode = emptyMap(),
                    productInProductIdToCode = emptyMap()
                )
            )
        }
    }

    @Test
    fun `test updateProductOriginalId - should correctly update in db`() {
        val product1 = productRepository.save(
            createProductFromAdminCreateCommand(ADMIN_CREATE_PRODUCT_COMMAND)
        )
        assertNull(productRepository.findById(product1.id).get().originalId)

        underTest.updateProductOriginalId(
            UpdateProductOriginalIdCommand(
                code = product1.code,
                originalId = 5
            )
        )

        assertEquals(5, productRepository.findById(product1.id).get().originalId)
    }

    @Test
    fun `test messagingCreateOrUpdateProduct - does not exist by code - should create product`() {
        every { applicationEventPublisherMock.publishEvent(any<ProductCreatedOrUpdatedEvent>()) } just Runs
        val command = MESSAGING_CREATE_OR_UPDATE_PRODUCT_COMMAND

        underTest.messagingCreateOrUpdateProduct(command)

        assertEquals(3, productRepository.count())

        val createdProduct = productRepository.findAll()[2]
        val productCompositions = productCompositionRepository.findAllByProductId(createdProduct.id)
        val productComponentIdToCode = productComponentRepository.findAllByIdInAndDeletedAtIsNull(
            productCompositions.mapNotNull { it.productComponentId }.toSet()
        ).associate { it.id to it.code }

        createdProduct.let {
            assertProductAndProductCompositionsToMessagingCreateOrUpdateProductCommand(
                expected = command,
                actualProduct = it,
                expectedProductCategoryId = PRODUCT_CATEGORY_1.id,
                expectedImageFileId = FILE_1.id,
                actualProductCompositions = productCompositions,
                productComponentIdToCode = productComponentIdToCode,
                productInProductIdToCode = emptyMap()
            )
            assertNotNull(it.createdAt)
            assertNull(it.deletedAt)
        }
        verify {
            applicationEventPublisherMock.publishEvent(
                ProductCreatedOrUpdatedEvent(
                    createdProduct.id
                )
            )
        }
    }

    @Test
    fun `test messagingCreateOrUpdateProduct - exists by code - should update product`() {
        every { applicationEventPublisherMock.publishEvent(any<ProductCreatedOrUpdatedEvent>()) } just Runs
        val createCommand = MESSAGING_CREATE_OR_UPDATE_PRODUCT_COMMAND

        underTest.messagingCreateOrUpdateProduct(createCommand)

        assertEquals(3, productRepository.count())
        val createdProduct = productRepository.findAll()[2]
        val productCompositions = productCompositionRepository.findAllByProductId(createdProduct.id)
        val productComponentIdToCode = productComponentRepository.findAllByIdInAndDeletedAtIsNull(
            productCompositions.mapNotNull { it.productComponentId }.toSet()
        ).associate { it.id to it.code }

        createdProduct.let {
            assertProductAndProductCompositionsToMessagingCreateOrUpdateProductCommand(
                expected = createCommand,
                actualProduct = it,
                expectedProductCategoryId = PRODUCT_CATEGORY_1.id,
                expectedImageFileId = FILE_1.id,
                actualProductCompositions = productCompositions,
                productComponentIdToCode = productComponentIdToCode,
                productInProductIdToCode = emptyMap()
            )
            assertNotNull(it.createdAt)
            assertNull(it.deletedAt)
        }

        val updateCommand = MESSAGING_CREATE_OR_UPDATE_PRODUCT_COMMAND.copy(
            productCategoryCode = PRODUCT_CATEGORY_2.code,
            imageFileId = null,
            taxRate = REDUCED_TAX_RATE,
            productComposition = listOf(
                MessagingCreateOrUpdateProductCommand.MessagingCreateOrUpdateProductCompositionCommand(
                    productComponentCode = PRODUCT_COMPONENT_1.code,
                    productInProductCode = null,
                    quantity = 15.toBigDecimal(),
                    productInProductPrice = 5.toBigDecimal(),
                    productInProductFlagshipPrice = 6.toBigDecimal()
                )
            )
        )

        underTest.messagingCreateOrUpdateProduct(updateCommand)
        assertEquals(3, productRepository.count())

        val updatedProductCompositions = productCompositionRepository.findAllByProductId(createdProduct.id)
        val updatedProductComponentIdToCode = productComponentRepository.findAllByIdInAndDeletedAtIsNull(
            updatedProductCompositions.mapNotNull { it.productComponentId }.toSet()
        ).associate { it.id to it.code }

        productRepository.findAll()[2].let {
            assertProductAndProductCompositionsToMessagingCreateOrUpdateProductCommand(
                expected = updateCommand,
                actualProduct = it,
                expectedProductCategoryId = PRODUCT_CATEGORY_2.id,
                expectedImageFileId = null,
                actualProductCompositions = updatedProductCompositions,
                productComponentIdToCode = updatedProductComponentIdToCode,
                productInProductIdToCode = emptyMap()
            )
            assertTrue(it.updatedAt.isAfter(it.createdAt))
            assertNull(it.deletedAt)
        }

        verify(exactly = 2) {
            applicationEventPublisherMock.publishEvent(
                ProductCreatedOrUpdatedEvent(
                    createdProduct.id
                )
            )
        }
    }

    @Test
    fun `test messagingDeleteProduct - should soft delete product`() {
        val product = createProduct(
            originalId = 100,
            code = "1234",
            productCategoryId = PRODUCT_CATEGORY_1.id,
            title = "Produkt 1"
        ).also { productRepository.save(it) }

        assertNull(product.deletedAt)
        assertEquals(3, productRepository.count())

        underTest.messagingDeleteProduct(MessagingDeleteProductCommand(product.code))

        val deletedProduct = productRepository.findById(product.id).get()
        assertNotNull(deletedProduct.deletedAt)

        verify { applicationEventPublisherMock.publishEvent(ProductDeletedEvent(deletedProduct.id)) }
    }

    private fun assertStockQuantity(productId: UUID, expected: BigDecimal) {
        val stockQuantity = productJooqFinderService.findAllNonDeletedActiveProductStockQuantitiesByIdIn(
            ids = setOf(productId)
        ).firstNotNullOf { it.value }
        assertTrue(expected isEqualTo stockQuantity)
    }

    companion object {
        @JvmStatic
        fun updateProductStockQuantityProvider(): Stream<Arguments> {
            return Stream.of(
                Arguments.of(PRODUCT_1.id, 125.toBigDecimal(), -5, 130.toBigDecimal()),
                Arguments.of(PRODUCT_1.id, 125.toBigDecimal(), -2, 127.toBigDecimal()),
                Arguments.of(PRODUCT_1.id, 125.toBigDecimal(), -1, 126.toBigDecimal()),
                Arguments.of(PRODUCT_1.id, 125.toBigDecimal(), 0, 125.toBigDecimal()),
                Arguments.of(PRODUCT_1.id, 125.toBigDecimal(), 1, 124.toBigDecimal()),
                Arguments.of(PRODUCT_1.id, 125.toBigDecimal(), 2, 123.toBigDecimal()),
                Arguments.of(PRODUCT_1.id, 125.toBigDecimal(), 5, 120.toBigDecimal()),
                Arguments.of(PRODUCT_2.id, 720.toBigDecimal(), -50, 770.toBigDecimal()),
                Arguments.of(PRODUCT_2.id, 720.toBigDecimal(), -25, 745.toBigDecimal()),
                Arguments.of(PRODUCT_2.id, 720.toBigDecimal(), -10, 730.toBigDecimal()),
                Arguments.of(PRODUCT_2.id, 720.toBigDecimal(), 0, 720.toBigDecimal()),
                Arguments.of(PRODUCT_2.id, 720.toBigDecimal(), 10, 710.toBigDecimal()),
                Arguments.of(PRODUCT_2.id, 720.toBigDecimal(), 25, 695.toBigDecimal()),
                Arguments.of(PRODUCT_2.id, 720.toBigDecimal(), 50, 670.toBigDecimal())
            )
        }
    }

    private fun assertProductEquals(expected: Product, actual: Product) {
        assertEquals(expected.originalId, actual.originalId)
        assertEquals(expected.title, actual.title)
        assertEquals(expected.type, actual.type)
        assertTrue(expected.price isEqualTo actual.price)
        assertEquals(expected.active, actual.active)
        assertEquals(expected.order, actual.order)
        assertEquals(expected.soldInBuffet, actual.soldInBuffet)
        assertEquals(expected.soldInCafe, actual.soldInCafe)
        assertEquals(expected.soldInVip, actual.soldInVip)
        assertEquals(expected.discountPercentage, actual.discountPercentage)
        assertEquals(expected.discountAmount, actual.discountAmount)
        assertEquals(expected.stockQuantityThreshold, actual.stockQuantityThreshold)
        assertEquals(expected.imageFileId, actual.imageFileId)
        assertNotNull(actual.createdAt)
        assertNotNull(actual.updatedAt)
    }
}

private val BRANCH_1 = createBranch(
    code = "510640",
    name = "Bratislava Bory",
    auditoriumOriginalCodePrefix = "510",
    type = BranchType.REGULAR
)
private val FILE_1 = createFile(
    originalId = 1,
    originalName = "KITKAT.PNG",
    extension = "PNG"
)
private val FILE_2 = createFile(
    originalId = 2,
    originalName = "SNICKERS.PNG",
    extension = "PNG"
)
private val PRODUCT_CATEGORY_1 = createProductCategory(
    originalId = 1,
    code = "01",
    title = "Nápoje",
    type = ProductCategoryType.PRODUCT,
    order = 10,
    taxRate = STANDARD_TAX_RATE
)
private val PRODUCT_CATEGORY_2 = createProductCategory(
    originalId = 2,
    code = "02",
    title = "Popcorn",
    type = ProductCategoryType.PRODUCT,
    order = 1,
    taxRate = STANDARD_TAX_RATE
)
private val PRODUCT_CATEGORY_4_DISCOUNT = createProductCategory(
    originalId = 4,
    code = "04",
    title = "Sleva",
    type = ProductCategoryType.DISCOUNT,
    order = null,
    taxRate = REDUCED_TAX_RATE
)
private val PRODUCT_CATEGORY_3 = createProductCategory(
    originalId = 3,
    code = "03",
    title = "Doplnky",
    type = ProductCategoryType.PRODUCT,
    order = 15,
    taxRate = STANDARD_TAX_RATE
)
private val PRODUCT_CATEGORY_5 = createProductCategory(
    originalId = 5,
    code = "05",
    title = "Záloha",
    type = ProductCategoryType.PRODUCT,
    taxRate = STANDARD_TAX_RATE
)
private val PRODUCT_1 = createProduct(
    originalId = 1,
    code = "01",
    productCategoryId = PRODUCT_CATEGORY_1.id,
    title = "Coca Cola 0.33l",
    order = 23,
    type = ProductType.PRODUCT,
    price = 3.5.toBigDecimal(),
    stockQuantityThreshold = 10,
    imageFileId = FILE_1.id
)
private val PRODUCT_2 = createProduct(
    originalId = 2,
    code = "02",
    productCategoryId = PRODUCT_CATEGORY_2.id,
    title = "Popcorn XXL",
    order = 25,
    type = ProductType.PRODUCT,
    price = 6.5.toBigDecimal(),
    soldInBuffet = false
)
private val PRODUCT_3 = createProduct(
    originalId = 3,
    code = "03",
    productCategoryId = PRODUCT_CATEGORY_1.id,
    title = "CO2",
    order = null,
    type = ProductType.PRODUCT,
    price = 3.toBigDecimal()
)
private val PRODUCT_4 = createProduct(
    originalId = 4,
    code = "04",
    productCategoryId = PRODUCT_CATEGORY_5.id,
    title = "Záloha na obal",
    type = ProductType.ADDITIONAL_SALE,
    price = 0.15.toBigDecimal(),
    isPackagingDeposit = true
)

private val PRODUCT_COMPONENT_CATEGORY_1 = createProductComponentCategory()
private val PRODUCT_COMPONENT_1 = createProductComponent(
    originalId = 1,
    code = "01",
    title = "Coca Cola 0.33l",
    unit = ProductComponentUnit.KS,
    stockQuantity = 125.toBigDecimal(),
    productComponentCategoryId = PRODUCT_COMPONENT_CATEGORY_1.id
)
private val PRODUCT_COMPONENT_2 = createProductComponent(
    originalId = 2,
    code = "02",
    title = "Brčko",
    unit = ProductComponentUnit.KS,
    stockQuantity = 2500.toBigDecimal(),
    productComponentCategoryId = PRODUCT_COMPONENT_CATEGORY_1.id
)
private val PRODUCT_COMPONENT_3 = createProductComponent(
    originalId = 3,
    code = "03",
    title = "Kukurica",
    unit = ProductComponentUnit.KG,
    stockQuantity = 180.0.toBigDecimal(),
    productComponentCategoryId = PRODUCT_COMPONENT_CATEGORY_1.id
)
private val PRODUCT_COMPONENT_4 = createProductComponent(
    originalId = 4,
    code = "04",
    title = "Soľ",
    unit = ProductComponentUnit.KG,
    stockQuantity = 230.8.toBigDecimal(),
    productComponentCategoryId = PRODUCT_COMPONENT_CATEGORY_1.id
)
private val PRODUCT_COMPONENT_5 = createProductComponent(
    originalId = 5,
    code = "05",
    title = "Tuk",
    unit = ProductComponentUnit.KG,
    stockQuantity = 750.55.toBigDecimal(),
    productComponentCategoryId = PRODUCT_COMPONENT_CATEGORY_1.id
)
private val PRODUCT_COMPOSITION_1 = createProductComposition(
    originalId = 1,
    productId = PRODUCT_1.id,
    productComponentId = PRODUCT_COMPONENT_1.id,
    amount = 1.toBigDecimal()
)
private val PRODUCT_COMPOSITION_2 = createProductComposition(
    originalId = 2,
    productId = PRODUCT_1.id,
    productComponentId = PRODUCT_COMPONENT_2.id,
    amount = 1.toBigDecimal()
)
private val PRODUCT_COMPOSITION_3 = createProductComposition(
    originalId = 3,
    productId = PRODUCT_2.id,
    productComponentId = PRODUCT_COMPONENT_3.id,
    amount = 0.25.toBigDecimal()
)
private val PRODUCT_COMPOSITION_4 = createProductComposition(
    originalId = 4,
    productId = PRODUCT_2.id,
    productComponentId = PRODUCT_COMPONENT_4.id,
    amount = 0.00838.toBigDecimal()
)
private val PRODUCT_COMPOSITION_5 = createProductComposition(
    originalId = 5,
    productId = PRODUCT_2.id,
    productComponentId = PRODUCT_COMPONENT_5.id,
    amount = 0.06798.toBigDecimal()
)
private val ADMIN_CREATE_PRODUCT_COMMAND = AdminCreateProductCommand(
    title = "New product",
    active = true,
    type = ProductType.PRODUCT,
    productCategoryId = PRODUCT_CATEGORY_1.id,
    imageFileId = FILE_1.id,
    price = 111.toBigDecimal(),
    flagshipPrice = 120.toBigDecimal(),
    soldInBuffet = true,
    soldInCafe = true,
    soldInVip = true,
    order = 1,
    tabletOrder = 1,
    isPackagingDeposit = false,
    stockQuantityThreshold = 2,
    taxRate = REDUCED_TAX_RATE,
    discountAmount = null,
    discountPercentage = null,
    productComposition = listOf(
        AdminCreateProductProductCompositionCommand(
            productComponentId = PRODUCT_COMPONENT_1.id,
            productInProductId = null,
            quantity = 1.toBigDecimal()
        ),
        AdminCreateProductProductCompositionCommand(
            productComponentId = PRODUCT_COMPONENT_2.id,
            productInProductId = null,
            quantity = 2.toBigDecimal()
        )
    )
)
private val ADMIN_UPDATE_PRODUCT_COMMAND = AdminUpdateProductCommand(
    id = 123.toUUID(),
    title = "Updated Product Title",
    active = false,
    productCategoryId = PRODUCT_CATEGORY_1.id,
    imageFileId = FILE_2.id,
    price = 150.toBigDecimal(),
    flagshipPrice = 250.toBigDecimal(),
    soldInBuffet = false,
    soldInCafe = true,
    soldInVip = false,
    order = 2,
    tabletOrder = 3,
    isPackagingDeposit = true,
    stockQuantityThreshold = 5,
    taxRate = STANDARD_TAX_RATE,
    discountAmount = null,
    discountPercentage = null,
    productComposition = listOf(
        AdminUpdateProductProductCompositionCommand(
            productComponentId = PRODUCT_COMPONENT_3.id,
            productInProductId = null,
            quantity = 1.5.toBigDecimal()
        ),
        AdminUpdateProductProductCompositionCommand(
            productComponentId = PRODUCT_COMPONENT_4.id,
            productInProductId = null,
            quantity = 2.0.toBigDecimal()
        )
    )
)
private val MESSAGING_CREATE_OR_UPDATE_PRODUCT_COMMAND = MessagingCreateOrUpdateProductCommand(
    title = "Product 2",
    code = "54321",
    active = true,
    type = ProductType.PRODUCT,
    productCategoryCode = PRODUCT_CATEGORY_1.code,
    imageFileId = FILE_1.id,
    price = 111.toBigDecimal(),
    soldInBuffet = true,
    soldInCafe = true,
    soldInVip = true,
    order = 1,
    tabletOrder = 1,
    isPackagingDeposit = false,
    stockQuantityThreshold = 2,
    discountAmount = null,
    discountPercentage = null,
    productComposition = listOf(
        MessagingCreateOrUpdateProductCommand.MessagingCreateOrUpdateProductCompositionCommand(
            productComponentCode = PRODUCT_COMPONENT_1.code,
            productInProductCode = null,
            quantity = 1.toBigDecimal(),
            productInProductPrice = 2.toBigDecimal(),
            productInProductFlagshipPrice = 3.toBigDecimal()
        ),
        MessagingCreateOrUpdateProductCommand.MessagingCreateOrUpdateProductCompositionCommand(
            productComponentCode = PRODUCT_COMPONENT_2.code,
            productInProductCode = null,
            quantity = 2.toBigDecimal(),
            productInProductPrice = 3.toBigDecimal(),
            productInProductFlagshipPrice = 4.toBigDecimal()
        )
    )
)

private fun ProductComponent.expectedStockQuantity(quantityDifference: Int, compositionAmount: BigDecimal) =
    this.stockQuantity - (quantityDifference.toBigDecimal() * compositionAmount)
