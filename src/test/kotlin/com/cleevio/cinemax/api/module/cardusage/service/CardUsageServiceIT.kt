package com.cleevio.cinemax.api.module.cardusage.service

import com.cleevio.cinemax.api.DatabaseCleanup
import com.cleevio.cinemax.api.IntegrationDataTestHelper
import com.cleevio.cinemax.api.IntegrationTest
import com.cleevio.cinemax.api.module.basket.constant.BasketState
import com.cleevio.cinemax.api.module.basketitem.constant.BasketItemType
import com.cleevio.cinemax.api.module.card.entity.Card
import com.cleevio.cinemax.api.module.card.service.CardRepository
import com.cleevio.cinemax.api.module.cardusage.exception.CardUsageNotFoundException
import com.cleevio.cinemax.api.module.cardusage.service.command.CreateCardUsageIfNotExistsCommand
import com.cleevio.cinemax.api.module.cardusage.service.command.DeleteCardUsagesCommand
import com.cleevio.cinemax.api.module.cardusage.service.command.UpdateCardUsageOriginalIdCommand
import com.cleevio.cinemax.api.module.discountcard.constant.DiscountCardType
import com.cleevio.cinemax.api.module.seat.constant.SeatType
import io.kotest.assertions.throwables.shouldThrow
import io.kotest.matchers.nulls.shouldNotBeNull
import io.kotest.matchers.shouldBe
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import java.util.UUID

class CardUsageServiceIT @Autowired constructor(
    private val underTest: CardUsageService,
    private val integrationDataTestHelper: IntegrationDataTestHelper,
    private val cardRepository: CardRepository,
    private val cardUsageRepository: CardUsageRepository,
) : IntegrationTest(DatabaseCleanup.AFTER_EACH) {

    @Test
    fun `createCardUsageIfNotExists - usage doesn't exist - should successfully create card usage`() {
        val testData = prepareTestData()

        underTest.createCardUsageIfNotExists(
            CreateCardUsageIfNotExistsCommand(
                cardId = testData.cardId,
                basketId = testData.basketId,
                basketItemId = testData.basketItemId,
                discountPercentage = 50
            )
        )

        val createdCardUsage = cardUsageRepository.findAll().first()

        createdCardUsage.shouldNotBeNull()
        createdCardUsage.cardId shouldBe testData.cardId
        createdCardUsage.basketId shouldBe testData.basketId
        createdCardUsage.basketItemId shouldBe testData.basketItemId
        createdCardUsage.originalId shouldBe null
        createdCardUsage.discountPercentage shouldBe 50
    }

    @Test
    fun `createCardUsageIfNotExists - usage already exists - should quit execution`() {
        val testData = prepareTestData()

        underTest.createCardUsageIfNotExists(
            CreateCardUsageIfNotExistsCommand(
                cardId = testData.cardId,
                basketId = testData.basketId,
                basketItemId = testData.basketItemId,
                discountPercentage = null
            )
        )

        val updatedAtBefore = cardUsageRepository.findAll().first().updatedAt

        underTest.createCardUsageIfNotExists(
            CreateCardUsageIfNotExistsCommand(
                cardId = testData.cardId,
                basketId = testData.basketId,
                basketItemId = testData.basketItemId,
                discountPercentage = null
            )
        )

        val updatedAtAfter = cardUsageRepository.findAll().first().updatedAt
        updatedAtBefore shouldBe updatedAtAfter
    }

    @Test
    fun `test deleteCardUsages - usage exists - should successfully delete card usage`() {
        val testData = prepareTestData()

        underTest.createCardUsageIfNotExists(
            CreateCardUsageIfNotExistsCommand(
                cardId = testData.cardId,
                basketId = testData.basketId,
                basketItemId = testData.basketItemId,
                discountPercentage = null
            )
        )

        val createdCardUsage = cardUsageRepository.findAll().first()

        underTest.deleteCardUsages(
            DeleteCardUsagesCommand(
                cardUsageIds = setOf(createdCardUsage.id)
            )
        )

        cardUsageRepository.findAll() shouldBe emptyList()
    }

    @Test
    fun `test deleteCardUsages - usage doesn't exist - should throw exception`() {
        shouldThrow<CardUsageNotFoundException> {
            underTest.deleteCardUsages(
                DeleteCardUsagesCommand(
                    cardUsageIds = setOf(UUID.randomUUID())
                )
            )
        }
    }

    @Test
    fun `test updateCrdUsageOriginalId - usage exists - should successfully update original id`() {
        val testData = prepareTestData()

        underTest.createCardUsageIfNotExists(
            CreateCardUsageIfNotExistsCommand(
                cardId = testData.cardId,
                basketId = testData.basketId,
                basketItemId = testData.basketItemId,
                discountPercentage = null
            )
        )

        val createdCardUsage = cardUsageRepository.findAll().first()
        val originalId = 12345L

        underTest.updateCardUsageOriginalId(
            UpdateCardUsageOriginalIdCommand(
                cardUsageId = createdCardUsage.id,
                originalId = originalId
            )
        )

        val updatedCardUsage = cardUsageRepository.findById(createdCardUsage.id).get()
        updatedCardUsage.originalId shouldBe originalId
    }

    @Test
    fun `test updateCrdUsageOriginalId - usage doesn't exist - should throw exception`() {
        shouldThrow<CardUsageNotFoundException> {
            underTest.updateCardUsageOriginalId(
                UpdateCardUsageOriginalIdCommand(
                    cardUsageId = UUID.randomUUID(),
                    originalId = 12345L
                )
            )
        }
    }

    private fun prepareTestData(): TestData {
        val card = cardRepository.save(
            Card(
                type = DiscountCardType.CARD,
                title = "Test Card",
                code = "TEST123",
                validFrom = null,
                validUntil = null
            )
        )

        val auditoriumLayout = integrationDataTestHelper.getAuditoriumLayout(
            auditoriumId = integrationDataTestHelper.getAuditorium()
        )

        val seat = integrationDataTestHelper.getSeat(
            originalId = 1,
            row = "1",
            number = "1",
            auditoriumId = auditoriumLayout.auditoriumId,
            auditoriumLayoutId = auditoriumLayout.id,
            type = SeatType.REGULAR
        )

        val screening = integrationDataTestHelper.getScreening(
            auditoriumId = auditoriumLayout.auditoriumId,
            auditoriumLayoutId = auditoriumLayout.id
        )

        val reservation = integrationDataTestHelper.getReservation(
            seatId = seat.id,
            screeningId = screening.id
        )

        val ticketPrice = integrationDataTestHelper.getTicketPrice(
            seatId = seat.id,
            screeningId = screening.id,
            totalPrice = 10.toBigDecimal()
        )

        val ticket = integrationDataTestHelper.getTicket(
            screeningId = screening.id,
            reservationId = reservation.id,
            ticketPriceId = ticketPrice.id
        )

        val basket = integrationDataTestHelper.getBasket(
            totalPrice = 10.toBigDecimal(),
            state = BasketState.OPEN
        )

        val basketItem = integrationDataTestHelper.getBasketItem(
            basketId = basket.id,
            ticketId = ticket.id,
            type = BasketItemType.TICKET,
            price = 10.toBigDecimal()
        )

        return TestData(
            cardId = card.id,
            basketId = basket.id,
            basketItemId = basketItem.id
        )
    }

    data class TestData(
        val cardId: UUID,
        val basketId: UUID,
        val basketItemId: UUID,
    )
}
