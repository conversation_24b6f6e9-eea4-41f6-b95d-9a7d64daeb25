package com.cleevio.cinemax.api.module.basketitem.service

import com.cleevio.cinemax.api.common.constant.ExportFormat
import com.cleevio.cinemax.api.common.constant.ExportableBoolean
import com.cleevio.cinemax.api.common.constant.PaymentType
import com.cleevio.cinemax.api.common.service.model.ExportResultModel
import com.cleevio.cinemax.api.module.basketitem.controller.dto.AdminSearchTicketBasketItemsFilter
import com.cleevio.cinemax.api.module.basketitem.model.TicketBasketItemExportRecordModel
import com.cleevio.cinemax.api.module.basketitem.model.TicketBasketItemsSummaryExportRecordModel
import com.cleevio.cinemax.api.module.basketitem.service.query.AdminExportTicketBasketItemsQuery
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import io.mockk.verifySequence
import org.junit.jupiter.api.assertThrows
import org.springframework.data.domain.PageRequest
import org.springframework.data.domain.Sort
import java.io.ByteArrayInputStream
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.LocalTime
import java.util.UUID
import kotlin.test.Test
import kotlin.test.assertEquals

class TicketBasketItemExportServiceTest {

    private val adminExportTicketBasketItemsQueryService = mockk<AdminExportTicketBasketItemsQueryService>()
    private val ticketBasketItemXlsxExportResultMapper = mockk<TicketBasketItemXlsxExportResultMapper>()
    private val adminExportTicketBasketItemSummaryQueryService = mockk<AdminExportTicketBasketItemSummaryQueryService>()
    private val underTest = TicketBasketItemExportService(
        adminExportTicketBasketItemsQueryService = adminExportTicketBasketItemsQueryService,
        adminExportTicketBasketItemSummaryQueryService = adminExportTicketBasketItemSummaryQueryService,
        ticketBasketItemXlsxExportResultMapper = ticketBasketItemXlsxExportResultMapper
    )

    @Test
    fun `test exportTicketBasketItems - valid query with XLSX format - should call related service and mapper`() {
        val basketPaidAtDateTimeFrom = LocalDateTime.of(2024, 1, 1, 18, 0)
        val basketPaidAtDateTimeTo = LocalDateTime.of(2024, 2, 1, 18, 0)
        val username = "monika"

        val filter = AdminSearchTicketBasketItemsFilter(
            basketPaidAtFrom = basketPaidAtDateTimeFrom,
            basketPaidAtTo = basketPaidAtDateTimeTo,
            auditoriumIds = setOf(UUID.fromString("41fa16ea-c3b9-4a2b-88b0-************")),
            movieIds = setOf(UUID.fromString("41fa16ea-c3b9-4a2b-88b0-************")),
            distributorIds = setOf(UUID.fromString("41fa16ea-c3b9-4a2b-88b0-************")),
            basketUpdatedBy = setOf("jano")
        )
        val query = AdminExportTicketBasketItemsQuery(
            pageable = PageRequest.of(0, 10, Sort.by("ticket.createdAt")),
            filter = filter,
            exportFormat = ExportFormat.XLSX,
            username = username
        )

        val exportListData = listOf(
            TicketBasketItemExportRecordModel(
                basketPaidAtDate = LocalDate.of(2024, 1, 2),
                basketPaidAtTime = LocalTime.of(17, 30),
                receiptNumber = "569754659",
                paymentPosConfigurationTitle = "pokl11",
                basketUpdatedBy = "jano",
                auditoriumCode = "IMAX",
                branchName = "Bratislava Bory",
                distributorTitle = "Hello movies, s.r.o.",
                screeningDate = LocalDate.of(2024, 1, 2),
                screeningTime = LocalTime.of(18, 0),
                movieRawTitle = "Interstellar",
                isUsed = ExportableBoolean.TRUE,
                includes3dGlasses = ExportableBoolean.FALSE,
                paymentType = PaymentType.CASH,
                seatRow = "2",
                seatNumber = "12",
                basePrice = 10.toBigDecimal(),
                surchargeVip = 1.toBigDecimal(),
                surchargePremium = 0.toBigDecimal(),
                surchargeImax = 0.toBigDecimal(),
                surchargeUltraX = 0.toBigDecimal(),
                surchargeDBox = 0.toBigDecimal(),
                serviceFeeVip = 1.toBigDecimal(),
                serviceFeePremium = 0.toBigDecimal(),
                serviceFeeImax = 0.toBigDecimal(),
                serviceFeeUltraX = 0.toBigDecimal(),
                serviceFeeGeneral = 1.toBigDecimal(),
                isCancelled = ExportableBoolean.FALSE,
                isPriceCategoryItemDiscounted = ExportableBoolean.FALSE,
                proDeduction = 0.1.toBigDecimal(),
                filmFondDeduction = 0.1.toBigDecimal(),
                distributorDeduction = 0.1.toBigDecimal(),
                taxAmount = 1.6.toBigDecimal()
            )
        )

        val exportSummaryData = TicketBasketItemsSummaryExportRecordModel(
            sales = TicketBasketItemsSummaryExportRecordModel.SalesSummary(
                screeningsCount = 1,
                ticketsCount = 20,
                ticketsUsedCount = 15,
                salesCash = 1.toBigDecimal(),
                salesCashless = 2.toBigDecimal(),
                grossSales = 3.toBigDecimal(),
                netSales = 4.toBigDecimal(),
                proDeduction = 5.toBigDecimal(),
                filmFondDeduction = 6.toBigDecimal(),
                distributorDeduction = 7.toBigDecimal(),
                taxAmount = 8.toBigDecimal(),
                cancelledTicketsCount = 8,
                cancelledTicketsExpense = 9.toBigDecimal()
            ),
            serviceFees = TicketBasketItemsSummaryExportRecordModel.ServiceFeesSummary(
                general = 10.toBigDecimal(),
                generalCount = 1,
                vip = 11.toBigDecimal(),
                vipCount = 1,
                premiumPlus = 12.toBigDecimal(),
                premiumPlusCount = 1,
                imax = 13.toBigDecimal(),
                imaxCount = 1,
                ultraX = 1.toBigDecimal(),
                ultraXCount = 1,
                dolbyAtmos = 14.toBigDecimal()
            ),
            surcharges = TicketBasketItemsSummaryExportRecordModel.SurchargesSummary(
                vip = 15.toBigDecimal(),
                vipCount = 1,
                dBox = 16.toBigDecimal(),
                dBoxCount = 1,
                premiumPlus = 17.toBigDecimal(),
                premiumPlusCount = 1,
                imax = 18.toBigDecimal(),
                imaxCount = 1,
                ultraX = 1.toBigDecimal(),
                ultraXCount = 1,
                dolbyAtmos = 19.toBigDecimal()
            ),
            technologyCount = TicketBasketItemsSummaryExportRecordModel.TechnologyCountsSummary(
                general = 20,
                vip = 21,
                dBox = 22,
                premiumPlus = 23,
                imax = 24,
                dolbyAtmos = 25,
                total = 26
            ),
            discounts = listOf(
                TicketBasketItemsSummaryExportRecordModel.DiscountSummary(
                    title = "title",
                    count = 27
                )
            )
        )
        val byteArray = ByteArray(1024)
        val inputStream = ByteArrayInputStream(byteArray)
        val exportResult = ExportResultModel(inputStream, byteArray.size.toLong())

        every { adminExportTicketBasketItemsQueryService(query) } returns exportListData
        every { adminExportTicketBasketItemSummaryQueryService(query) } returns exportSummaryData
        every {
            ticketBasketItemXlsxExportResultMapper.mapToExportResultModel(
                any(),
                any(),
                any(),
                any()
            )
        } returns exportResult

        assertEquals(exportResult, underTest.exportTicketBasketItems(query))

        verifySequence {
            adminExportTicketBasketItemsQueryService(query)
            adminExportTicketBasketItemSummaryQueryService(query)
            ticketBasketItemXlsxExportResultMapper.mapToExportResultModel(
                data = exportListData to exportSummaryData,
                username = username,
                basketPaidAtDateFrom = basketPaidAtDateTimeFrom.toLocalDate(),
                basketPaidAtDateTo = basketPaidAtDateTimeTo.toLocalDate()
            )
        }
    }

    @Test
    fun `test exportScreenings - valid query with XML format - should throw`() {
        val basketPaidAtDateTimeFrom = LocalDateTime.of(2024, 1, 1, 18, 0)
        val basketPaidAtDateTimeTo = LocalDateTime.of(2024, 2, 1, 18, 0)
        val username = "monika"

        val filter = AdminSearchTicketBasketItemsFilter(
            basketPaidAtFrom = basketPaidAtDateTimeFrom,
            basketPaidAtTo = basketPaidAtDateTimeTo,
            auditoriumIds = setOf(UUID.fromString("41fa16ea-c3b9-4a2b-88b0-************")),
            movieIds = setOf(UUID.fromString("41fa16ea-c3b9-4a2b-88b0-************")),
            distributorIds = setOf(UUID.fromString("41fa16ea-c3b9-4a2b-88b0-************"))
        )
        val query = AdminExportTicketBasketItemsQuery(
            pageable = PageRequest.of(0, 10),
            filter = filter,
            exportFormat = ExportFormat.XML,
            username = username
        )

        assertThrows<UnsupportedOperationException> { underTest.exportTicketBasketItems(query) }
        verify(exactly = 0) { adminExportTicketBasketItemsQueryService(any()) }
        verify(exactly = 0) { adminExportTicketBasketItemSummaryQueryService(any()) }
        verify(exactly = 0) { ticketBasketItemXlsxExportResultMapper.mapToExportResultModel(any(), any(), any(), any()) }
    }
}
