package com.cleevio.cinemax.api.module.employee.service

import com.cleevio.cinemax.api.DatabaseCleanup
import com.cleevio.cinemax.api.IntegrationTest
import com.cleevio.cinemax.api.module.employee.constant.EmployeeRole
import com.cleevio.cinemax.api.module.employee.event.EmployeeCreatedOrUpdatedEvent
import com.cleevio.cinemax.api.module.employee.exception.EmployeeNotFoundException
import com.cleevio.cinemax.api.module.employee.exception.EmployeeUsernameAlreadyExistsException
import com.cleevio.cinemax.api.module.employee.exception.InvalidPasswordSizeException
import com.cleevio.cinemax.api.module.employee.service.command.CreateOrUpdateEmployeeCommand
import com.cleevio.cinemax.api.module.employee.service.command.DeleteEmployeeCommand
import com.cleevio.cinemax.api.module.employee.service.command.EnforcePasswordResetCommand
import com.cleevio.cinemax.api.module.employee.service.command.UpdateEmployeeOriginalIdsCommand
import com.cleevio.cinemax.api.module.employee.util.encodePassword
import com.cleevio.cinemax.api.truncatedToSeconds
import com.cleevio.cinemax.api.util.DUMMY_PASSWORD
import com.cleevio.cinemax.api.util.assertEmployeeEquals
import com.cleevio.cinemax.api.util.createEmployee
import com.cleevio.cinemax.api.util.mapToCreateOrUpdateEmployeeCommand
import io.mockk.Called
import io.mockk.Runs
import io.mockk.every
import io.mockk.just
import io.mockk.verify
import jakarta.validation.ConstraintViolationException
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.Arguments
import org.junit.jupiter.params.provider.MethodSource
import org.springframework.beans.factory.annotation.Autowired
import java.time.LocalDateTime
import java.util.UUID
import java.util.stream.Stream
import kotlin.test.assertEquals
import kotlin.test.assertFalse
import kotlin.test.assertNotNull
import kotlin.test.assertNull
import kotlin.test.assertTrue

class EmployeeServiceIT @Autowired constructor(
    private val underTest: EmployeeService,
    private val employeeRepository: EmployeeRepository,
    private val employeeFinderService: EmployeeFinderService,
) : IntegrationTest(DatabaseCleanup.AFTER_EACH) {

    @BeforeEach
    fun beforeEach() {
        every { applicationEventPublisherMock.publishEvent(any<EmployeeCreatedOrUpdatedEvent>()) } just Runs
    }

    @Test
    fun `test syncCreateOrUpdateEmployee - synchronized from MSSQL - should create employee`() {
        val command = mapToCreateOrUpdateEmployeeCommand(EMPLOYEE_1)
        underTest.syncCreateOrUpdateEmployee(command)

        val createdEmployee = employeeFinderService.findNonDeletedByOriginalId(EMPLOYEE_1.originalId!!)
        assertNotNull(createdEmployee)
        assertEmployeeEquals(EMPLOYEE_1, createdEmployee)
    }

    @Test
    fun `test syncCreateOrUpdateEmployee - one employee synchronized from MSSQL - insert equal employee so it should update`() {
        val command = mapToCreateOrUpdateEmployeeCommand(EMPLOYEE_2).copy(id = null)
        underTest.syncCreateOrUpdateEmployee(command)
        underTest.syncCreateOrUpdateEmployee(
            command.copy(
                role = EmployeeRole.MANAGER,
                accessibleAt = LocalDateTime.of(1970, 1, 1, 0, 0)
            )
        )

        val updatedEmployee = employeeFinderService.findNonDeletedByOriginalId(EMPLOYEE_2.originalId!!)
        assertNotNull(updatedEmployee)

        assertEquals(EMPLOYEE_2.originalId, updatedEmployee.originalId)
        assertEquals(EMPLOYEE_2.originalBuffetId, updatedEmployee.originalBuffetId)
        assertEquals(EMPLOYEE_2.username, updatedEmployee.username)
        assertEquals(EMPLOYEE_2.fullName, updatedEmployee.fullName)
        assertEquals(EMPLOYEE_2.posName, updatedEmployee.posName)
        assertEquals(EMPLOYEE_2.passwordReset, updatedEmployee.passwordReset)
        assertEquals(EmployeeRole.MANAGER, updatedEmployee.role)
        assertEquals(EMPLOYEE_2.lastLoginAt, updatedEmployee.lastLoginAt)
        assertEquals(LocalDateTime.of(1970, 1, 1, 0, 0), updatedEmployee.accessibleAt)
        assertEquals(EMPLOYEE_2.deletedAt, updatedEmployee.deletedAt)
        assertNotNull(updatedEmployee.createdAt)
        assertNotNull(updatedEmployee.updatedAt)

        assertTrue { updatedEmployee.updatedAt.isAfter(EMPLOYEE_2.updatedAt) }
    }

    @Test
    fun `test syncCreateOrUpdateEmployee - two employees, synchronized from MSSQL - should create two employees`() {
        employeeRepository.save(EMPLOYEE_1)

        val command = mapToCreateOrUpdateEmployeeCommand(EMPLOYEE_2)
        underTest.syncCreateOrUpdateEmployee(command)

        val employees = employeeFinderService.findAllNonDeleted()
        assertEquals(employees.size, 2)
        assertEmployeeEquals(
            EMPLOYEE_1,
            employees.first { it.originalId == EMPLOYEE_1.originalId }
        )
        assertEmployeeEquals(
            EMPLOYEE_2,
            employees.first { it.originalId == EMPLOYEE_2.originalId }
        )
    }

    @Test
    fun `test syncCreateOrUpdateEmployee - command with blank string - should throw exception`() {
        val command = mapToCreateOrUpdateEmployeeCommand(EMPLOYEE_2)
        val commandWithBlankString = command.copy(
            username = ""
        )
        assertThrows<ConstraintViolationException> {
            underTest.syncCreateOrUpdateEmployee(commandWithBlankString)
        }
    }

    @Test
    fun `test adminCreateOrUpdateEmployee - called from manager app, original(Buffet)Id is null - should create employee`() {
        val command = mapToCreateOrUpdateEmployeeCommand(EMPLOYEE_3).copy(id = null)
        underTest.adminCreateOrUpdateEmployee(command)

        val createdEmployee = employeeFinderService.findNonDeletedByUsername(EMPLOYEE_3.username)
        assertNotNull(createdEmployee)
        assertNotNull(createdEmployee.id)
        assertEmployeeEquals(EMPLOYEE_3, createdEmployee)

        verify {
            applicationEventPublisherMock.publishEvent(
                EmployeeCreatedOrUpdatedEvent(
                    employeeId = createdEmployee.id,
                    encodedPassword = encodePassword(command.password!!)
                )
            )
        }
    }

    @Test
    fun `test adminCreateOrUpdateEmployee - called from manager app, update existing employee with null password - should keep original password`() {
        val command = mapToCreateOrUpdateEmployeeCommand(EMPLOYEE_1)
        underTest.syncCreateOrUpdateEmployee(command)

        val createdEmployee = employeeFinderService.findNonDeletedByUsername(EMPLOYEE_1.username)
        assertNotNull(createdEmployee)
        assertEmployeeEquals(EMPLOYEE_1, createdEmployee)

        val updateCommand = command.copy(password = null)
        underTest.adminCreateOrUpdateEmployee(updateCommand)

        val updatedEmployee = employeeFinderService.findNonDeletedByUsername(EMPLOYEE_1.username)
        assertNotNull(updatedEmployee)
        assertEmployeeEquals(EMPLOYEE_1, updatedEmployee)
        assertEquals(createdEmployee.passwordHash, updatedEmployee.passwordHash)

        verify {
            applicationEventPublisherMock.publishEvent(
                EmployeeCreatedOrUpdatedEvent(
                    employeeId = createdEmployee.id,
                    encodedPassword = null
                )
            )
        }
    }

    @Test
    fun `test syncCreateOrUpdateEmployee - create employee command with null password - should throw exception`() {
        val command = mapToCreateOrUpdateEmployeeCommand(EMPLOYEE_2)
        val commandWithNullPassword = command.copy(
            password = null
        )
        assertThrows<IllegalArgumentException> {
            underTest.syncCreateOrUpdateEmployee(commandWithNullPassword)
        }

        verify {
            applicationEventPublisherMock wasNot Called
        }
    }

    @Test
    fun `test adminCreateOrUpdateEmployee - called from manager app, employee with username is already synced from MSSQL - should throw exception`() {
        val syncCommand = mapToCreateOrUpdateEmployeeCommand(EMPLOYEE_3)
        underTest.syncCreateOrUpdateEmployee(syncCommand)

        val createdEmployee = employeeFinderService.findNonDeletedByUsername(EMPLOYEE_3.username)
        assertNotNull(createdEmployee)
        assertEmployeeEquals(EMPLOYEE_3, createdEmployee)

        val adminCommand = CreateOrUpdateEmployeeCommand(
            username = EMPLOYEE_3.username,
            fullName = EMPLOYEE_3.fullName,
            posName = EMPLOYEE_3.posName,
            password = DUMMY_PASSWORD,
            passwordReset = true,
            role = EMPLOYEE_3.role,
            accessibleAt = EMPLOYEE_3.accessibleAt
        )

        assertThrows<EmployeeUsernameAlreadyExistsException> {
            underTest.adminCreateOrUpdateEmployee(adminCommand)
        }

        verify {
            applicationEventPublisherMock wasNot Called
        }
    }

    @Test
    fun `test syncCreateOrUpdateEmployee - called from MSSQL sync, employee with username is already created in manager app - should throw exception`() {
        val adminCommand = mapToCreateOrUpdateEmployeeCommand(EMPLOYEE_3).copy(id = null)
        underTest.adminCreateOrUpdateEmployee(adminCommand)

        val createdEmployee = employeeFinderService.findNonDeletedByUsername(EMPLOYEE_3.username)
        assertNotNull(createdEmployee)
        assertEmployeeEquals(EMPLOYEE_3, createdEmployee)

        val syncCommand = CreateOrUpdateEmployeeCommand(
            originalId = 3,
            username = EMPLOYEE_3.username,
            fullName = EMPLOYEE_3.fullName,
            posName = EMPLOYEE_3.posName,
            password = DUMMY_PASSWORD,
            passwordReset = true,
            role = EMPLOYEE_3.role,
            accessibleAt = EMPLOYEE_3.accessibleAt
        )

        assertThrows<EmployeeUsernameAlreadyExistsException> {
            underTest.syncCreateOrUpdateEmployee(syncCommand)
        }
    }

    @Test
    fun `test syncCreateOrUpdateEmployee - sync from MSSQL, create another in manager app, update synced from MSSQL with existing username - should throw exception`() {
        val command1 = mapToCreateOrUpdateEmployeeCommand(EMPLOYEE_1).copy(id = null)
        underTest.syncCreateOrUpdateEmployee(command1)

        val createdEmployee1 = employeeFinderService.findNonDeletedByUsername(EMPLOYEE_1.username)
        assertNotNull(createdEmployee1)
        assertEmployeeEquals(EMPLOYEE_1, createdEmployee1)

        val command2 = mapToCreateOrUpdateEmployeeCommand(EMPLOYEE_2).copy(id = null)
        underTest.adminCreateOrUpdateEmployee(command2)

        val createdEmployee2 = employeeFinderService.findNonDeletedByUsername(EMPLOYEE_2.username)
        assertNotNull(createdEmployee2)
        assertEmployeeEquals(EMPLOYEE_2, createdEmployee2)

        val command3 = mapToCreateOrUpdateEmployeeCommand(EMPLOYEE_1).copy(
            id = null,
            username = EMPLOYEE_2.username
        )

        assertThrows<EmployeeUsernameAlreadyExistsException> {
            underTest.syncCreateOrUpdateEmployee(command3)
        }
        verify {
            applicationEventPublisherMock.publishEvent(
                EmployeeCreatedOrUpdatedEvent(
                    employeeId = createdEmployee2.id,
                    encodedPassword = encodePassword(command2.password!!)
                )
            )
        }
    }

    @Test
    fun `test deleteEmployee and create identical one once again - should delete and create employee`() {
        val command1 = mapToCreateOrUpdateEmployeeCommand(EMPLOYEE_1)
        underTest.syncCreateOrUpdateEmployee(command1)

        val createdEmployee1 = employeeFinderService.findNonDeletedByOriginalId(EMPLOYEE_1.originalId!!)
        assertNotNull(createdEmployee1)
        assertEmployeeEquals(EMPLOYEE_1, createdEmployee1)

        underTest.deleteEmployee(
            DeleteEmployeeCommand(
                employeeId = createdEmployee1.id
            )
        )

        assertNull(employeeFinderService.findNonDeletedById(createdEmployee1.id))
        val deletedEmployee = employeeFinderService.getById(createdEmployee1.id)
        assertNotNull(deletedEmployee.deletedAt)

        val command2 = mapToCreateOrUpdateEmployeeCommand(EMPLOYEE_1)
        underTest.syncCreateOrUpdateEmployee(command2)

        val createdEmployee2 = employeeFinderService.findNonDeletedByOriginalId(EMPLOYEE_1.originalId!!)
        assertNotNull(createdEmployee2)
        assertEmployeeEquals(EMPLOYEE_1, createdEmployee2)
    }

    @Test
    fun `test createOrUpdateEmployee - existing employee, update from manager app - should update employee`() {
        val command1 = mapToCreateOrUpdateEmployeeCommand(EMPLOYEE_1).copy(id = null)
        underTest.adminCreateOrUpdateEmployee(command1)

        val createdEmployee = employeeFinderService.findNonDeletedByOriginalId(EMPLOYEE_1.originalId!!)
        assertNotNull(createdEmployee)
        assertNotNull(createdEmployee.id)
        assertEmployeeEquals(EMPLOYEE_1, createdEmployee)

        underTest.adminCreateOrUpdateEmployee(
            CreateOrUpdateEmployeeCommand(
                id = createdEmployee.id,
                username = EMPLOYEE_1_UPDATED.username,
                fullName = EMPLOYEE_1_UPDATED.fullName,
                posName = EMPLOYEE_1_UPDATED.posName,
                password = DUMMY_PASSWORD,
                role = EMPLOYEE_1_UPDATED.role,
                accessibleAt = EMPLOYEE_1_UPDATED.accessibleAt
            )
        )

        val updatedEmployee = employeeFinderService.getNonDeletedById(createdEmployee.id)
        assertNull(updatedEmployee.deletedAt)
        assertTrue(updatedEmployee.updatedAt.isAfter(createdEmployee.updatedAt))
        assertEmployeeEquals(EMPLOYEE_1_UPDATED, updatedEmployee)
    }

    @Test
    fun `test createOrUpdateEmployee - existing employee, update from manager app, existing username - should throw exception`() {
        val command1 = mapToCreateOrUpdateEmployeeCommand(EMPLOYEE_1).copy(id = null)
        val command2 = mapToCreateOrUpdateEmployeeCommand(EMPLOYEE_2).copy(id = null)
        underTest.adminCreateOrUpdateEmployee(command1)
        underTest.adminCreateOrUpdateEmployee(command2)

        val createdEmployees = employeeFinderService.findAllNonDeleted().sortedBy { it.originalId }
        assertEmployeeEquals(EMPLOYEE_1, createdEmployees[0])
        assertEmployeeEquals(EMPLOYEE_2, createdEmployees[1])

        assertThrows<EmployeeUsernameAlreadyExistsException> {
            underTest.adminCreateOrUpdateEmployee(
                CreateOrUpdateEmployeeCommand(
                    id = createdEmployees[0].id,
                    username = EMPLOYEE_2.username,
                    fullName = EMPLOYEE_1_UPDATED.fullName,
                    posName = EMPLOYEE_1_UPDATED.posName,
                    password = DUMMY_PASSWORD,
                    role = EMPLOYEE_1_UPDATED.role,
                    accessibleAt = EMPLOYEE_1_UPDATED.accessibleAt
                )
            )
        }
    }

    @Test
    fun `test createOrUpdateEmployee - employee with given id does not exist - should throw exception`() {
        assertThrows<EmployeeNotFoundException> {
            underTest.adminCreateOrUpdateEmployee(
                CreateOrUpdateEmployeeCommand(
                    id = UUID.fromString("dcd84a85-15fc-407f-b7ed-150913db1f0c"),
                    username = EMPLOYEE_2.username,
                    fullName = EMPLOYEE_1_UPDATED.fullName,
                    posName = EMPLOYEE_1_UPDATED.posName,
                    password = DUMMY_PASSWORD,
                    role = EMPLOYEE_1_UPDATED.role,
                    accessibleAt = EMPLOYEE_1_UPDATED.accessibleAt
                )
            )
        }
    }

    @ParameterizedTest
    @MethodSource("passwordProvider")
    fun `test createOrUpdateEmployee - command with unsuitable password - should throw exception`(password: String) {
        val command = mapToCreateOrUpdateEmployeeCommand(EMPLOYEE_3)
        val commandWithPassword = command.copy(
            password = password
        )
        assertThrows<InvalidPasswordSizeException> {
            underTest.adminCreateOrUpdateEmployee(commandWithPassword)
        }
    }

    companion object {
        @JvmStatic
        fun passwordProvider(): Stream<Arguments> {
            return Stream.of(
                Arguments.of("short"),
                Arguments.of("tooLongPassword")
            )
        }
    }

    @Test
    fun `test enforcePasswordReset - should update employee resetPassword attribute`() {
        val command = mapToCreateOrUpdateEmployeeCommand(EMPLOYEE_1).copy(id = null)
        underTest.adminCreateOrUpdateEmployee(command)

        val createdEmployee = employeeFinderService.findNonDeletedByOriginalId(EMPLOYEE_1.originalId!!)
        assertNotNull(createdEmployee)
        assertNotNull(createdEmployee.id)
        assertEmployeeEquals(EMPLOYEE_1, createdEmployee)
        assertFalse(createdEmployee.passwordReset)

        underTest.enforcePasswordReset(EnforcePasswordResetCommand(employeeId = createdEmployee.id))

        val updatedEmployee = employeeFinderService.getNonDeletedById(createdEmployee.id)
        assertTrue(updatedEmployee.passwordReset)
        assertTrue(updatedEmployee.updatedAt.isAfter(createdEmployee.updatedAt))
    }

    @Test
    fun `test updateEmployeeOriginalIds - created employee - should update both employee's originalIds`() {
        val command = mapToCreateOrUpdateEmployeeCommand(EMPLOYEE_1).copy(id = null)
        underTest.adminCreateOrUpdateEmployee(command)
        val employeeId = employeeRepository.findAll().first().id

        underTest.updateEmployeeOriginalIds(
            UpdateEmployeeOriginalIdsCommand(
                employeeId = employeeId,
                originalId = 123,
                originalBuffetId = 456
            )
        )

        val updatedEmployee = employeeFinderService.getNonDeletedById(employeeId)
        assertEquals(123, updatedEmployee.originalId!!)
        assertEquals(456, updatedEmployee.originalBuffetId!!)
    }
}

private val EMPLOYEE_1 = createEmployee(
    originalId = 1,
    username = "manager",
    fullName = "Johnny Manager",
    role = EmployeeRole.MANAGER
)
private val EMPLOYEE_1_UPDATED = createEmployee(
    originalId = 1,
    username = "Johnny",
    fullName = "Johnny Cash(ier)",
    role = EmployeeRole.CASHIER,
    accessibleAt = LocalDateTime.now().plusDays(2).truncatedToSeconds()
)
private val EMPLOYEE_2 = createEmployee(
    originalId = 2,
    username = "cashier",
    fullName = "Johnny Cashier",
    role = EmployeeRole.CASHIER
)
private val EMPLOYEE_3 = createEmployee(
    originalId = null,
    username = "orbison",
    fullName = "Roy Orbison",
    role = EmployeeRole.CASHIER,
    accessibleAt = LocalDateTime.of(2024, 2, 10, 10, 0)
)
