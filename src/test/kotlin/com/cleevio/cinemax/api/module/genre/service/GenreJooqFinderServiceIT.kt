package com.cleevio.cinemax.api.module.genre.service

import com.cleevio.cinemax.api.DatabaseCleanup
import com.cleevio.cinemax.api.IntegrationTest
import com.cleevio.cinemax.api.module.genre.entity.Genre
import com.cleevio.cinemax.api.util.assertDescriptorEquals
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.test.context.jdbc.Sql
import org.springframework.test.context.jdbc.SqlGroup
import kotlin.test.assertEquals

@SqlGroup(
    Sql(
        scripts = [
            "/db/migration/V105__init_genre_table_with_data.sql"
        ]
    )
)
class GenreJooqFinderServiceIT @Autowired constructor(
    private val underTest: GenreJooqFinderService,
) : IntegrationTest(DatabaseCleanup.AFTER_EACH) {

    @Test
    fun `test findAll - should find all genre values`() {
        val genres = underTest.findAll().sortedBy { it.createdAt }

        assertEquals(43, genres.size)
        assertDescriptorEquals(GENRE_1, genres[0])
        assertDescriptorEquals(GENRE_2, genres[1])
        assertDescriptorEquals(GENRE_3, genres[39])
    }
}

private val GENRE_1 = Genre(
    originalId = 1,
    code = "0",
    title = "Animovaný"
)
private val GENRE_2 = Genre(
    originalId = 2,
    code = "1",
    title = "Dráma"
)
private val GENRE_3 = Genre(
    originalId = 40,
    code = "-",
    title = "Hudobny, muzikal"
)
