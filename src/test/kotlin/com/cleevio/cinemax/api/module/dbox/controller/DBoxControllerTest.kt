package com.cleevio.cinemax.api.module.dbox.controller

import com.cleevio.cinemax.api.ControllerTest
import com.cleevio.cinemax.api.common.constant.ApiVersion
import com.cleevio.cinemax.api.common.service.model.ExportResultModel
import io.mockk.every
import io.mockk.verify
import org.hamcrest.Matchers.containsString
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest
import org.springframework.http.HttpHeaders
import org.springframework.test.web.servlet.MockMvc
import org.springframework.test.web.servlet.get
import java.io.ByteArrayInputStream

@WebMvcTest(DBoxController::class)
class DBoxControllerTest @Autowired constructor(
    private val mvc: MockMvc,
) : ControllerTest() {

    @Test
    fun `test getDBoxScheduleData, should return XML response`() {
        val byteArray = """
            <?xml version="1.0" encoding="utf-8"?>
            <D-BOX>
              <Result Code="1" Message="Success" />
              <Movies>
                <Movie Id="12914" Name="Captain America: Prekrasny novy svet 2D (SD)" DurationS="7080" />
              </Movies>
              <Presentations>
                <Presentation Id="159764" BeginDT="2025-02-23T16:50:00" MovieId="12914" SiteId="1" AuditoriumId="510030" DurationS="7080" />
              </Presentations>
            </D-BOX>
        """.trimIndent().toByteArray()

        every { dBoxService.getDBoxScheduleData() } returns ExportResultModel(
            inputStream = ByteArrayInputStream(byteArray),
            size = byteArray.size.toLong()
        )

        mvc.get(GET_D_BOX_SCHEDULE_PATH) {
            header(HttpHeaders.ACCEPT, ApiVersion.VERSION_1_XML)
        }.andExpect {
            status { isOk() }
            header {
                string(HttpHeaders.CONTENT_TYPE, ApiVersion.VERSION_1_XML)
                string(HttpHeaders.CONTENT_DISPOSITION, containsString("attachment"))
                string(HttpHeaders.CONTENT_DISPOSITION, containsString("DBoxScheduleData"))
                longValue(HttpHeaders.CONTENT_LENGTH, byteArray.size.toLong())
            }
        }

        verify {
            dBoxService.getDBoxScheduleData()
        }
    }

    @Test
    fun `test getDBoxReservationData, should return XML response`() {
        val byteArray = """
            <?xml version="1.0" encoding="utf-8"?>
            <D-BOX>
              <Result Code="1" Message="Success" />
              <Reservations>
                <Reservation PresentationId="20160202" SeatId="A1" SeatRow="A" SeatCol="1" State="0" />
                <Reservation PresentationId="20160202" SeatId="A2" SeatRow="A" SeatCol="2" State="1" />
              </Reservations>
            </D-BOX>
        """.trimIndent().toByteArray()

        every { dBoxService.getDBoxReservationData() } returns ExportResultModel(
            inputStream = ByteArrayInputStream(byteArray),
            size = byteArray.size.toLong()
        )

        mvc.get(GET_D_BOX_RESERVATIONS_PATH) {
            header(HttpHeaders.ACCEPT, ApiVersion.VERSION_1_XML)
        }.andExpect {
            status { isOk() }
            header {
                string(HttpHeaders.CONTENT_TYPE, ApiVersion.VERSION_1_XML)
                string(HttpHeaders.CONTENT_DISPOSITION, containsString("attachment"))
                string(HttpHeaders.CONTENT_DISPOSITION, containsString("DBoxReservationData"))
                longValue(HttpHeaders.CONTENT_LENGTH, byteArray.size.toLong())
            }
        }

        verify {
            dBoxService.getDBoxReservationData()
        }
    }
}

private const val GET_D_BOX_SCHEDULE_PATH = "/dbox-app/schedule"
private const val GET_D_BOX_RESERVATIONS_PATH = "/dbox-app/reservations"
