package com.cleevio.cinemax.api.module.dbox.service

import com.cleevio.cinemax.api.module.dbox.service.model.DBoxReservationDataXmlModel
import com.cleevio.cinemax.api.module.dbox.service.model.DBoxScheduleDataXmlModel
import com.cleevio.cinemax.api.module.dbox.service.model.Movie
import com.cleevio.cinemax.api.module.dbox.service.model.Movies
import com.cleevio.cinemax.api.module.dbox.service.model.Presentation
import com.cleevio.cinemax.api.module.dbox.service.model.Presentations
import com.cleevio.cinemax.api.module.dbox.service.model.Reservation
import com.cleevio.cinemax.api.module.dbox.service.model.Reservations
import com.cleevio.cinemax.api.module.dbox.service.model.Result
import com.cleevio.cinemax.api.module.dbox.service.query.AdminGetDBoxReservationDataQueryService
import com.cleevio.cinemax.api.module.dbox.service.query.AdminGetDBoxScheduleDataQueryService
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test

class DBoxServiceTest {

    private val adminGetDBoxScheduleDataQueryService = mockk<AdminGetDBoxScheduleDataQueryService>()
    private val adminGetDBoxReservationDataQueryService = mockk<AdminGetDBoxReservationDataQueryService>()
    private val underTest = DBoxService(adminGetDBoxScheduleDataQueryService, adminGetDBoxReservationDataQueryService)

    @Test
    fun `test getDBoxScheduleData, should return export result model with XML data`() {
        val xmlModel = DBoxScheduleDataXmlModel(
            result = Result(),
            movies = Movies(
                movieList = listOf(
                    Movie(
                        id = "12914",
                        name = "Captain America: Prekrasny novy svet 2D (SD)",
                        durationS = 7080
                    )
                )
            ),
            presentations = Presentations(
                presentationList = listOf(
                    Presentation(
                        id = "159764",
                        beginDT = "2025-02-23T16:50:00",
                        movieId = "12914",
                        auditoriumId = "510030",
                        durationS = 7080
                    )
                )
            )
        )
        every { adminGetDBoxScheduleDataQueryService() } returns xmlModel

        val result = underTest.getDBoxScheduleData()

        verify { adminGetDBoxScheduleDataQueryService() }

        val resultBytes = result.inputStream.readAllBytes()
        assert(resultBytes.isNotEmpty())
        assertEquals(resultBytes.size.toLong(), result.size)
    }

    @Test
    fun `test getDBoxReservationData, should return export result model with XML data`() {
        val xmlModel = DBoxReservationDataXmlModel(
            result = Result(),
            reservations = Reservations(
                reservationList = listOf(
                    Reservation(
                        presentationId = "20160202",
                        seatId = "A1",
                        seatRow = "A",
                        seatCol = "1",
                        state = "0"
                    ),
                    Reservation(
                        presentationId = "20160202",
                        seatId = "A2",
                        seatRow = "A",
                        seatCol = "2",
                        state = "1"
                    )
                )
            )
        )
        every { adminGetDBoxReservationDataQueryService() } returns xmlModel

        val result = underTest.getDBoxReservationData()

        verify { adminGetDBoxReservationDataQueryService() }

        val resultBytes = result.inputStream.readAllBytes()
        assert(resultBytes.isNotEmpty())
        assertEquals(resultBytes.size.toLong(), result.size)
    }
}
