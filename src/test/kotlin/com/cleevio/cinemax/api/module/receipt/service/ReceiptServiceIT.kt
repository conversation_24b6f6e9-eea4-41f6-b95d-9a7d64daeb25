package com.cleevio.cinemax.api.module.receipt.service

import com.cleevio.cinemax.api.DatabaseCleanup
import com.cleevio.cinemax.api.IntegrationTest
import com.cleevio.cinemax.api.module.basket.service.BasketService
import com.cleevio.cinemax.api.module.receipt.constant.ReceiptPrintState
import com.cleevio.cinemax.api.module.receipt.constant.ReceiptType
import com.cleevio.cinemax.api.module.receipt.entity.Receipt
import com.cleevio.cinemax.api.module.receipt.exception.ReceiptNotFoundException
import com.cleevio.cinemax.api.module.receipt.service.command.CreateReceiptCommand
import com.cleevio.cinemax.api.module.receipt.service.command.UpdateLastPrintStateCommand
import com.cleevio.cinemax.api.util.mapInputItemsToInitBasketCommand
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import org.springframework.beans.factory.annotation.Autowired
import java.util.UUID
import kotlin.jvm.optionals.getOrNull
import kotlin.test.assertEquals
import kotlin.test.assertNotNull
import kotlin.test.assertNull

class ReceiptServiceIT @Autowired constructor(
    private val underTest: ReceiptService,
    private val receiptRepository: ReceiptRepository,
    private val basketService: BasketService,
) : IntegrationTest(DatabaseCleanup.AFTER_EACH) {

    @Test
    fun `test createReceipt - single receipt - should create receipt`() {
        val basketId = basketService.initBasket(mapInputItemsToInitBasketCommand()).id

        val createdReceipt = underTest.createReceipt(
            CreateReceiptCommand(
                basketId = basketId,
                receiptsDirectory = RECEIPTS_DIRECTORY,
                content = RECEIPT_XML_CONTENT,
                type = ReceiptType.RECEIPT
            )
        )

        assertCreatedReceipt(createdReceipt, basketId)
    }

    @Test
    fun `test createReceipt - multiple receipts - should create multiple receipts`() {
        val basketId = basketService.initBasket(mapInputItemsToInitBasketCommand()).id

        val createdReceipt1 = underTest.createReceipt(
            CreateReceiptCommand(
                basketId = basketId,
                receiptsDirectory = RECEIPTS_DIRECTORY,
                content = RECEIPT_XML_CONTENT,
                type = ReceiptType.RECEIPT
            )
        )
        val createdReceipt2 = underTest.createReceipt(
            CreateReceiptCommand(
                basketId = basketId,
                receiptsDirectory = RECEIPTS_DIRECTORY,
                content = RECEIPT_XML_CONTENT,
                type = ReceiptType.RECEIPT
            )
        )
        val createdReceipt3 = underTest.createReceipt(
            CreateReceiptCommand(
                basketId = basketId,
                receiptsDirectory = RECEIPTS_DIRECTORY,
                content = RECEIPT_XML_CONTENT,
                type = ReceiptType.CANCELLATION
            )
        )

        setOf(createdReceipt1, createdReceipt2, createdReceipt3).forEach { assertCreatedReceipt(it, basketId) }

        val receipts = receiptRepository.findAll().sortedBy { it.createdAt }
        assertReceiptEquals(createdReceipt1, receipts[0])
        assertReceiptEquals(createdReceipt2, receipts[1])
        assertReceiptEquals(createdReceipt3, receipts[2])
    }

    @Test
    fun `test updateLastPrintState - receipt exists - should update last print state`() {
        val basketId = basketService.initBasket(mapInputItemsToInitBasketCommand()).id

        val createdReceipt = underTest.createReceipt(
            CreateReceiptCommand(
                basketId = basketId,
                receiptsDirectory = RECEIPTS_DIRECTORY,
                content = RECEIPT_XML_CONTENT,
                type = ReceiptType.RECEIPT
            )
        )

        assertCreatedReceipt(createdReceipt, basketId)

        underTest.updateLastPrintState(
            UpdateLastPrintStateCommand(
                receiptId = createdReceipt.id,
                lastPrintState = ReceiptPrintState.SUCCESS
            )
        )

        val updatedReceipt = receiptRepository.findById(createdReceipt.id).getOrNull()!!
        assertReceiptEquals(createdReceipt, updatedReceipt, expectedLastPrintState = ReceiptPrintState.SUCCESS)
    }

    @Test
    fun `test updateLastPrintState - receipt doesn't exist - should throw exception`() {
        assertThrows<ReceiptNotFoundException> {
            underTest.updateLastPrintState(
                UpdateLastPrintStateCommand(
                    receiptId = UUID.randomUUID(),
                    lastPrintState = ReceiptPrintState.PROCESSING
                )
            )
        }
    }

    private fun assertCreatedReceipt(receipt: Receipt, expectedBasketId: UUID) {
        assertNotNull(receipt.id)
        assertEquals(expectedBasketId, receipt.basketId)
        assertEquals(RECEIPTS_DIRECTORY, receipt.directory)
        assertEquals(RECEIPT_XML_CONTENT, receipt.content)
        assertNull(receipt.lastPrintState)
        assertNotNull(receipt.createdAt)
        assertNotNull(receipt.updatedAt)
    }

    private fun assertReceiptEquals(expected: Receipt, actual: Receipt, expectedLastPrintState: ReceiptPrintState? = null) {
        assertEquals(expected.id, actual.id)
        assertEquals(expected.basketId, actual.basketId)
        assertEquals(expected.directory, actual.directory)
        assertEquals(expected.content, actual.content)
        expectedLastPrintState?.let {
            assertEquals(expectedLastPrintState, actual.lastPrintState)
        } ?: assertEquals(expected.lastPrintState, actual.lastPrintState)
        assertEquals(expected.type, actual.type)
    }
}

private const val RECEIPTS_DIRECTORY = "/dummy/directory"
private const val RECEIPT_XML_CONTENT = """
    <ekasa:RegisterReceiptRequest xmlns:ekasa="ekasa_cnmx.xsd">
      <ekasa:Header Version="1.0" Uuid="V-0002450980" ExpectResponse="true" SwId="12200.82 (11.3.2019)"/>
      <ekasa:ReceiptData ReceiptType="PD" ReceiptNumber="0002450980" Amount="0.00" TaxBaseBasic="0.00" TaxBaseReduced="0.00" BasicVatAmount="0.00" ReducedVatAmount="0.00" TaxFreeAmount="0.00">
        <ekasa:Items predal="jano test" text1="Kupou vstupenky zakaznik potvrdzuje" text2="vnutorny poriadok kina.">
          <ekasa:Item NameF="Transformers: Monštrá sa prebúdzajú 3D (SD)" QR="0002450980" Name="SALA K, 27.11.2023 15:10 rad 1 miesto 7" ItemType="K" Quantity="1.00" VatRate="20" UnitPrice="0.00" Price="0.00"/>
        </ekasa:Items>
        <ekasa:Payments>
          <ekasa:Payment Amount="0.00" PaymentType="HO"/>
        </ekasa:Payments>
      </ekasa:ReceiptData>
    </ekasa:RegisterReceiptRequest>
"""
