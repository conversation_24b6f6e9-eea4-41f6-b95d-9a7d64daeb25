package com.cleevio.cinemax.api.module.movie.service

import com.cleevio.cinemax.api.DatabaseCleanup
import com.cleevio.cinemax.api.IntegrationTest
import com.cleevio.cinemax.api.common.integration.disfilm.xml.DISFilmMovie
import com.cleevio.cinemax.api.module.distributor.service.DistributorJpaFinderService
import com.cleevio.cinemax.api.module.distributor.service.DistributorService
import com.cleevio.cinemax.api.module.genre.service.GenreJpaFinderService
import com.cleevio.cinemax.api.module.language.service.LanguageJpaFinderService
import com.cleevio.cinemax.api.module.movie.event.DISFilmMoviesCreatedOrUpdatedEvent
import com.cleevio.cinemax.api.module.production.service.ProductionJpaFinderService
import com.cleevio.cinemax.api.module.rating.service.RatingJpaFinderService
import com.cleevio.cinemax.api.module.screening.constant.MovieTechnology
import com.cleevio.cinemax.api.module.technology.service.TechnologyJpaFinderService
import com.cleevio.cinemax.api.util.assertDISFilmMovieToMovieMapping
import com.cleevio.cinemax.api.util.createDistributor
import com.cleevio.cinemax.api.util.createMovie
import com.cleevio.cinemax.api.util.mapToCreateOrUpdateDistributorCommand
import io.mockk.Runs
import io.mockk.every
import io.mockk.just
import io.mockk.verifySequence
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.test.context.jdbc.Sql
import org.springframework.test.context.jdbc.SqlGroup
import kotlin.test.assertEquals
import kotlin.test.assertNull
import kotlin.test.assertTrue

@SqlGroup(
    Sql(
        scripts = [
            "/db/migration/V104__init_production_table_with_data.sql",
            "/db/migration/V105__init_genre_table_with_data.sql",
            "/db/migration/V106__init_rating_table_with_data.sql",
            "/db/migration/V107__init_technology_table_with_data.sql",
            "/db/migration/V108__init_language_table_with_data.sql"
        ]
    )
)
class MovieDISFilmSynchronizationServiceIT @Autowired constructor(
    private val underTest: MovieDISFilmSynchronizationService,
    private val movieRepository: MovieRepository,
    private val movieJooqFinderService: MovieJooqFinderService,
    private val distributorService: DistributorService,
    private val distributorJpaFinderService: DistributorJpaFinderService,
    private val productionJpaFinderService: ProductionJpaFinderService,
    private val genreJpaFinderService: GenreJpaFinderService,
    private val ratingJpaFinderService: RatingJpaFinderService,
    private val technologyJpaFinderService: TechnologyJpaFinderService,
    private val languageJpaFinderService: LanguageJpaFinderService,
) : IntegrationTest(DatabaseCleanup.AFTER_EACH) {

    @Test
    fun `test syncMoviesFromDISFilm - should correctly create new movie records`() {
        setOf(DISTRIBUTOR_1, DISTRIBUTOR_2).forEach {
            distributorService.syncCreateOrUpdateDistributor(mapToCreateOrUpdateDistributorCommand(it))
        }
        every { disfilmConnectorMock.getMovies() } returns listOf(
            DISFILM_MOVIE_1,
            DISFILM_MOVIE_2,
            DISFILM_MOVIE_3,
            DISFILM_MOVIE_5
        )
        every { movieMssqlFinderRepositoryMock.findDisfilmCodeToOriginalId() } returns mapOf(
            DISFILM_MOVIE_2.disfilmCode to MOVIE_ORIGINAL_ID_2,
            DISFILM_MOVIE_1.disfilmCode to MOVIE_ORIGINAL_ID_1
        )
        every { applicationEventPublisherMock.publishEvent(any<DISFilmMoviesCreatedOrUpdatedEvent>()) } just Runs

        assertEquals(0, movieJooqFinderService.findAll().size)
        val syncedCount = underTest.syncMoviesFromDisfilm()
        assertEquals(3, syncedCount)

        val movies = movieJooqFinderService.findAll()
        assertEquals(3, movieJooqFinderService.findAll().size)

        val languageIdToLanguage = languageJpaFinderService.findAll().associateBy { it.id }

        setOf(DISFILM_MOVIE_1, DISFILM_MOVIE_2, DISFILM_MOVIE_3).forEachIndexed { i, disfilmMovie ->
            assertDISFilmMovieToMovieMapping(
                expected = disfilmMovie,
                actual = movies[i],
                actualDistributorCode = distributorJpaFinderService.getNonDeletedById(movies[i].distributorId).disfilmCode!!,
                actualGenreTitle = movies[i].primaryGenreId?.let { genreJpaFinderService.findById(it)?.title },
                actualRatingCode = movies[i].ratingId?.let { ratingJpaFinderService.findById(it)?.code },
                actualTechnologyTitle = movies[i].technologyId?.let { technologyJpaFinderService.findById(it)?.title },
                actualLanguage = languageIdToLanguage[movies[i].languageId],
                actualProductionTitle = movies[i].productionId?.let { productionJpaFinderService.findById(it)?.title }
            )
        }

        assertEquals(MOVIE_ORIGINAL_ID_1, movies[0].originalId!!)
        assertEquals(MOVIE_ORIGINAL_ID_2, movies[1].originalId!!)
        assertNull(movies[2].originalId)

        verifySequence {
            disfilmConnectorMock.getMovies()
            movieMssqlFinderRepositoryMock.findDisfilmCodeToOriginalId()
            applicationEventPublisherMock.publishEvent(
                DISFilmMoviesCreatedOrUpdatedEvent(
                    setOf(movies[0].id, movies[1].id, movies[2].id)
                )
            )
        }
    }

    @Test
    fun `test syncMoviesFromDISFilm - distributor not found - should sync only valid record`() {
        setOf(DISTRIBUTOR_1, DISTRIBUTOR_2).forEach {
            distributorService.syncCreateOrUpdateDistributor(mapToCreateOrUpdateDistributorCommand(it))
        }
        every { disfilmConnectorMock.getMovies() } returns listOf(
            DISFILM_MOVIE_1,
            DISFILM_MOVIE_2.copy(distributorCode = "AB")
        )
        every { movieMssqlFinderRepositoryMock.findDisfilmCodeToOriginalId() } returns mapOf()
        every { applicationEventPublisherMock.publishEvent(any<DISFilmMoviesCreatedOrUpdatedEvent>()) } just Runs

        assertEquals(0, movieJooqFinderService.findAll().size)
        val syncedCount = underTest.syncMoviesFromDisfilm()
        assertEquals(1, syncedCount)

        val movies = movieJooqFinderService.findAll()
        assertEquals(1, movieJooqFinderService.findAll().size)

        val languageIdToLanguage = languageJpaFinderService.findAll().associateBy { it.id }

        assertDISFilmMovieToMovieMapping(
            expected = DISFILM_MOVIE_1,
            actual = movies[0],
            actualDistributorCode = distributorJpaFinderService.getNonDeletedById(movies[0].distributorId).disfilmCode!!,
            actualGenreTitle = movies[0].primaryGenreId?.let { genreJpaFinderService.findById(it)?.title },
            actualRatingCode = movies[0].ratingId?.let { ratingJpaFinderService.findById(it)?.code },
            actualTechnologyTitle = movies[0].technologyId?.let { technologyJpaFinderService.findById(it)?.title },
            actualLanguage = languageIdToLanguage[movies[0].languageId],
            actualProductionTitle = movies[0].productionId?.let { productionJpaFinderService.findById(it)?.title }
        )
        assertNull(movies[0].originalId)

        verifySequence {
            disfilmConnectorMock.getMovies()
            movieMssqlFinderRepositoryMock.findDisfilmCodeToOriginalId()
            applicationEventPublisherMock.publishEvent(
                DISFilmMoviesCreatedOrUpdatedEvent(
                    movieIds = setOf(movies[0].id)
                )
            )
        }
    }

    @Test
    fun `test syncMoviesFromDISFilm - movie with code already exists - should generate code and create new movie records`() {
        setOf(DISTRIBUTOR_1, DISTRIBUTOR_2).forEach {
            distributorService.syncCreateOrUpdateDistributor(mapToCreateOrUpdateDistributorCommand(it))
        }
        movieRepository.save(
            createMovie(
                originalId = 1,
                title = "Star Wars: Episode I – The Phantom Menace",
                code = "123456",
                releaseYear = 2001,
                parsedTechnology = MovieTechnology.IMAX,
                distributorId = DISTRIBUTOR_1.id
            ).apply { code = "800001" } // first movie_code_seq value
        )

        every { disfilmConnectorMock.getMovies() } returns listOf(DISFILM_MOVIE_1, DISFILM_MOVIE_2, DISFILM_MOVIE_3)
        every { movieMssqlFinderRepositoryMock.findDisfilmCodeToOriginalId() } returns mapOf(
            DISFILM_MOVIE_1.disfilmCode to MOVIE_ORIGINAL_ID_1,
            DISFILM_MOVIE_2.disfilmCode to MOVIE_ORIGINAL_ID_2,
            DISFILM_MOVIE_3.disfilmCode to MOVIE_ORIGINAL_ID_3
        )
        every { applicationEventPublisherMock.publishEvent(any<DISFilmMoviesCreatedOrUpdatedEvent>()) } just Runs

        assertEquals(1, movieJooqFinderService.findAll().size)

        val syncedCount = underTest.syncMoviesFromDisfilm()
        assertEquals(3, syncedCount)

        val movies = movieJooqFinderService.findAll()
        assertEquals(4, movieJooqFinderService.findAll().size)

        val movieRegex = "8[0-9A-Z][0-9]{4}"
        assertTrue(movies[1].code.matches(Regex(movieRegex)))
        assertTrue(movies[2].code.matches(Regex(movieRegex)))
        assertTrue(movies[3].code.matches(Regex(movieRegex)))

        assertEquals(MOVIE_ORIGINAL_ID_1, movies[1].originalId!!)
        assertEquals(MOVIE_ORIGINAL_ID_2, movies[2].originalId!!)
        assertEquals(MOVIE_ORIGINAL_ID_3, movies[3].originalId!!)

        verifySequence {
            disfilmConnectorMock.getMovies()
            movieMssqlFinderRepositoryMock.findDisfilmCodeToOriginalId()
            applicationEventPublisherMock.publishEvent(
                DISFilmMoviesCreatedOrUpdatedEvent(
                    setOf(movies[1].id, movies[2].id, movies[3].id)
                )
            )
        }
    }
}

private val DISTRIBUTOR_1 = createDistributor(originalId = 1, disfilmCode = "01", code = "XY")
private val DISTRIBUTOR_2 = createDistributor(originalId = 2, disfilmCode = "02", code = "YZ")
private val DISFILM_MOVIE_1 = DISFilmMovie(
    disfilmCode = "XXX19003F3",
    rawTitle = "Trhlina (DVD) DVD (OV)",
    originalTitle = "Trhlina DVD (OV)",
    distributorCode = "01",
    premiereDate = "2019-01-01",
    ratingCode = "15",
    duration = 111,
    technologyTitle = "DVD",
    languageCode = "ORIG"
)
private val DISFILM_MOVIE_2 = DISFilmMovie(
    disfilmCode = "BON19001A5",
    rawTitle = "Hovory s TGM 2D (ČD)",
    originalTitle = "Hovory s TGM 2D (ČD)",
    distributorCode = "01",
    premiereDate = "2019-01-03",
    genreTitle = "Dráma",
    ratingCode = "12",
    duration = 80,
    technologyTitle = "DCI2D",
    languageCode = "CZ",
    productionTitle = "Česká Republika"
)
private val DISFILM_MOVIE_3 = DISFilmMovie(
    disfilmCode = "SAT19001S4",
    rawTitle = "Ralph búra internet 2DS (SD)",
    originalTitle = "RALPF BREAKS THE INTERNET 2DS (SD)",
    distributorCode = "02"
)
private val DISFILM_MOVIE_5 = DISFILM_MOVIE_2.copy()

private const val MOVIE_ORIGINAL_ID_1 = 10001
private const val MOVIE_ORIGINAL_ID_2 = 10002
private const val MOVIE_ORIGINAL_ID_3 = 10003
