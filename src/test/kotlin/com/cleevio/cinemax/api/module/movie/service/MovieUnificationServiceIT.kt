package com.cleevio.cinemax.api.module.movie.service

import com.cleevio.cinemax.api.DatabaseCleanup
import com.cleevio.cinemax.api.IntegrationTest
import com.cleevio.cinemax.api.common.util.mapToSet
import com.cleevio.cinemax.api.common.util.toUUID
import com.cleevio.cinemax.api.module.auditorium.service.AuditoriumRepository
import com.cleevio.cinemax.api.module.auditoriumlayout.service.AuditoriumLayoutRepository
import com.cleevio.cinemax.api.module.distributor.service.DistributorRepository
import com.cleevio.cinemax.api.module.genre.entity.Genre
import com.cleevio.cinemax.api.module.genre.service.GenreRepository
import com.cleevio.cinemax.api.module.jso.entity.Jso
import com.cleevio.cinemax.api.module.jso.service.JsoRepository
import com.cleevio.cinemax.api.module.language.entity.Language
import com.cleevio.cinemax.api.module.language.service.LanguageRepository
import com.cleevio.cinemax.api.module.movie.constant.MovieRating
import com.cleevio.cinemax.api.module.movie.entity.Movie
import com.cleevio.cinemax.api.module.moviejso.entity.MovieJso
import com.cleevio.cinemax.api.module.moviejso.service.MovieJsoRepository
import com.cleevio.cinemax.api.module.pricecategory.service.PriceCategoryRepository
import com.cleevio.cinemax.api.module.production.entity.Production
import com.cleevio.cinemax.api.module.production.service.ProductionRepository
import com.cleevio.cinemax.api.module.rating.entity.Rating
import com.cleevio.cinemax.api.module.rating.service.RatingRepository
import com.cleevio.cinemax.api.module.screening.constant.MovieFormat
import com.cleevio.cinemax.api.module.screening.constant.MovieLanguage
import com.cleevio.cinemax.api.module.screening.constant.MovieOrigin
import com.cleevio.cinemax.api.module.screening.service.ScreeningRepository
import com.cleevio.cinemax.api.module.technology.entity.Technology
import com.cleevio.cinemax.api.module.technology.service.TechnologyRepository
import com.cleevio.cinemax.api.module.tmslanguage.entity.TmsLanguage
import com.cleevio.cinemax.api.module.tmslanguage.service.TmsLanguageRepository
import com.cleevio.cinemax.api.util.createAuditorium
import com.cleevio.cinemax.api.util.createAuditoriumLayout
import com.cleevio.cinemax.api.util.createDistributor
import com.cleevio.cinemax.api.util.createPriceCategory
import com.cleevio.cinemax.api.util.createScreening
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import java.time.LocalDate
import java.util.UUID
import kotlin.test.assertEquals

class MovieUnificationServiceIT @Autowired constructor(
    private val underTest: MovieUnificationService,
    private val auditoriumRepository: AuditoriumRepository,
    private val auditoriumLayoutRepository: AuditoriumLayoutRepository,
    private val priceCategoryRepository: PriceCategoryRepository,
    private val distributorRepository: DistributorRepository,
    private val screeningRepository: ScreeningRepository,
    private val productionRepository: ProductionRepository,
    private val genreRepository: GenreRepository,
    private val ratingRepository: RatingRepository,
    private val technologyRepository: TechnologyRepository,
    private val languageRepository: LanguageRepository,
    private val tmsLanguageRepository: TmsLanguageRepository,
    private val movieRepository: MovieRepository,
    private val jsoRepository: JsoRepository,
    private val movieJsoRepository: MovieJsoRepository,
) : IntegrationTest(DatabaseCleanup.AFTER_EACH) {

    @BeforeEach
    fun setUp() {
        auditoriumRepository.save(AUDITORIUM)
        auditoriumLayoutRepository.save(AUDITORIUM_LAYOUT)
        priceCategoryRepository.save(PRICE_CATEGORY)
        distributorRepository.saveAll(setOf(DISTRIBUTOR_1, DISTRIBUTOR_2, DISTRIBUTOR_3))
        productionRepository.saveAll(setOf(PRODUCTION_1, PRODUCTION_2, PRODUCTION_3))
        genreRepository.saveAll(setOf(GENRE_1, GENRE_2, GENRE_3))
        ratingRepository.saveAll(setOf(RATING_1, RATING_2, RATING_3))
        technologyRepository.save(TECHNOLOGY_1)
        languageRepository.saveAll(setOf(LANGUAGE_1, LANGUAGE_2))
        tmsLanguageRepository.saveAll(setOf(TMS_LANGUAGE_1, TMS_LANGUAGE_2))
        jsoRepository.saveAll(setOf(JSO_1, JSO_2))
    }

    @Test
    fun `test deduplicateMovies - duplicate movie is by Cinemax and has no screenings - should delete it`() {
        movieRepository.saveAll(setOf(MOVIE_1, MOVIE_2))
        (1..5).forEach { _ ->
            createScreeningForMovie(MOVIE_2.id)
        }

        underTest.deduplicateMovies()

        assertEquals(1, movieRepository.count())

        movieRepository.findAll().first().run {
            assertEquals(8579, this.originalId)
            assertEquals("Prezidentka 2D (OV)", this.rawTitle)
            assertEquals("003068", this.code)
            assertEquals("MAG22006A3", this.disfilmCode)
            assertEquals(DISTRIBUTOR_1.id, this.distributorId)
            assertEquals("g48cnr", this.messagingCode)
            assertEquals(MovieOrigin.DISFILM, this.origin)
        }
    }

    @Test
    fun `test deduplicateMovies - duplicate movie is by DISFilm and has no screenings - should adopt its DISFilm code and delete it`() {
        movieRepository.saveAll(setOf(MOVIE_1, MOVIE_2))
        (1..5).forEach { _ ->
            createScreeningForMovie(MOVIE_1.id)
        }

        underTest.deduplicateMovies()

        assertEquals(1, movieRepository.count())

        movieRepository.findAll().first().run {
            assertEquals(6719, this.originalId)
            assertEquals("Prezidentka 2D (OV)", this.rawTitle)
            assertEquals("MB0223", this.code)
            assertEquals("MAG22006A3", this.disfilmCode)
            assertEquals(DISTRIBUTOR_1.id, this.distributorId)
            assertEquals("g48cnr", this.messagingCode)
            assertEquals(MovieOrigin.CINEMAX, this.origin)
        }
    }

    @Test
    fun `test deduplicateMovies - both movies have screenings - should prefer the one with more screenings (Cinemax) and switch screenings under it`() {
        movieRepository.saveAll(setOf(MOVIE_1, MOVIE_2))
        movieJsoRepository.saveAll(
            setOf(
                MovieJso(movieId = MOVIE_2.id, jsoId = JSO_1.id),
                MovieJso(movieId = MOVIE_2.id, jsoId = JSO_2.id)
            )
        )
        (1..5).forEach { _ ->
            createScreeningForMovie(MOVIE_2.id)
        }
        (1..50).forEach { _ ->
            createScreeningForMovie(MOVIE_1.id)
        }

        underTest.deduplicateMovies()

        assertEquals(1, movieRepository.count())

        movieRepository.findAll().first().run {
            assertEquals(6719, this.originalId)
            assertEquals("Prezidentka 2D (OV)", this.rawTitle)
            assertEquals("MB0223", this.code)
            assertEquals("MAG22006A3", this.disfilmCode)
            assertEquals(DISTRIBUTOR_1.id, this.distributorId)
            assertEquals("g48cnr", this.messagingCode)
            assertEquals(MovieOrigin.CINEMAX, this.origin)
        }

        assertEquals(55, screeningRepository.count())

        val screeningMovieIds = screeningRepository.findAll().mapToSet { it.movieId }
        assertEquals(1, screeningMovieIds.size)
        assertEquals(MOVIE_1.id, screeningMovieIds.first())

        val movieJsosMovieIds = movieJsoRepository.findAll().mapToSet { it.movieId }
        assertEquals(1, movieJsosMovieIds.size)
        assertEquals(MOVIE_1.id, movieJsosMovieIds.first())

        movieJsoRepository.deleteAll()
    }

    @Test
    fun `test deduplicateMovies - both movies have the same amount of screenings - should prefer the DISFilm one`() {
        movieRepository.saveAll(setOf(MOVIE_1, MOVIE_2))
        movieJsoRepository.saveAll(
            setOf(
                MovieJso(movieId = MOVIE_1.id, jsoId = JSO_1.id),
                MovieJso(movieId = MOVIE_2.id, jsoId = JSO_2.id)
            )
        )
        (1..5).forEach { _ ->
            createScreeningForMovie(MOVIE_1.id)
            createScreeningForMovie(MOVIE_2.id)
        }

        underTest.deduplicateMovies()

        assertEquals(1, movieRepository.count())

        movieRepository.findAll().first().run {
            assertEquals(8579, this.originalId)
            assertEquals("Prezidentka 2D (OV)", this.rawTitle)
            assertEquals("003068", this.code)
            assertEquals("MAG22006A3", this.disfilmCode)
            assertEquals(DISTRIBUTOR_1.id, this.distributorId)
            assertEquals("g48cnr", this.messagingCode)
            assertEquals(MovieOrigin.DISFILM, this.origin)
        }

        assertEquals(10, screeningRepository.count())

        val screeningMovieIds = screeningRepository.findAll().mapToSet { it.movieId }
        assertEquals(1, screeningMovieIds.size)
        assertEquals(MOVIE_2.id, screeningMovieIds.first())

        val movieJsosMovieIds = movieJsoRepository.findAll().mapToSet { it.movieId }
        assertEquals(1, movieJsosMovieIds.size)
        assertEquals(MOVIE_2.id, movieJsosMovieIds.first())

        movieJsoRepository.deleteAll()
    }

    @Test
    fun `test deduplicateMovies - duplicate movie title but different movies - should delete only correct duplicates`() {
        movieRepository.saveAll(setOf(MOVIE_1, MOVIE_2, MOVIE_3, MOVIE_4))
        (1..5).forEach { _ ->
            createScreeningForMovie(MOVIE_2.id)
            createScreeningForMovie(MOVIE_4.id)
        }

        underTest.deduplicateMovies()

        assertEquals(2, movieRepository.count())

        movieRepository.findAll().sortedBy { it.premiereDate }[0].run {
            assertEquals(8579, this.originalId)
            assertEquals("Prezidentka 2D (OV)", this.rawTitle)
            assertEquals("003068", this.code)
            assertEquals("MAG22006A3", this.disfilmCode)
            assertEquals(DISTRIBUTOR_1.id, this.distributorId)
            assertEquals("g48cnr", this.messagingCode)
            assertEquals(MovieOrigin.DISFILM, this.origin)
        }

        movieRepository.findAll().sortedBy { it.premiereDate }[1].run {
            assertEquals(11814, this.originalId)
            assertEquals("Prezidentka 2D (OV)", this.rawTitle)
            assertEquals("FEX008", this.code)
            assertEquals("FEX24007A3", this.disfilmCode)
            assertEquals(DISTRIBUTOR_2.id, this.distributorId)
            assertEquals("5k4c3o", this.messagingCode)
            assertEquals(MovieOrigin.CINEMAX, this.origin)
        }
    }

    @Test
    fun `test deduplicateMovies - 3 movies, only 1 has screenings - should delete only correct duplicates`() {
        movieRepository.saveAll(setOf(MOVIE_5, MOVIE_6, MOVIE_7))
        (1..5).forEach { _ ->
            createScreeningForMovie(MOVIE_5.id)
        }

        underTest.deduplicateMovies()

        assertEquals(1, movieRepository.count())

        movieRepository.findAll().first().run {
            assertEquals(12729, this.originalId)
            assertEquals("Ježko Sonic 3 2D (SD)", this.rawTitle)
            assertEquals("800675", this.code)
            assertEquals("CIN24052A4", this.disfilmCode)
            assertEquals(DISTRIBUTOR_3.id, this.distributorId)
            assertEquals("98r3jy", this.messagingCode)
            assertEquals(MovieOrigin.DISFILM, this.origin)
        }
    }

    @Test
    fun `test deduplicateMovies - duplicate movies without any screenings - should prefer the DISFilm one`() {
        movieRepository.saveAll(setOf(MOVIE_3, MOVIE_4))

        underTest.deduplicateMovies()

        assertEquals(1, movieRepository.count())

        movieRepository.findAll().first().run {
            assertEquals(11086, this.originalId)
            assertEquals("Prezidentka 2D (OV)", this.rawTitle)
            assertEquals("005193", this.code)
            assertEquals("FEX24007A3", this.disfilmCode)
            assertEquals(DISTRIBUTOR_2.id, this.distributorId)
            assertEquals("5k4c3o", this.messagingCode)
            assertEquals(MovieOrigin.DISFILM, this.origin)
        }
    }

    @Test
    fun `test updateMoviesMessagingCodes - should update correctly`() {
        movieRepository.saveAll(setOf(MOVIE_1, MOVIE_2, MOVIE_3, MOVIE_4, MOVIE_5, MOVIE_6, MOVIE_7))

        // simulate movies existing in DB with default messagingCode (which equals to code)
        movieRepository.findAll().forEach {
            movieRepository.save(
                it.apply { messagingCode = code }
            )
        }

        underTest.updateMoviesMessagingCodes()

        val updatedMovies = movieRepository.findAll()
        updatedMovies.first { it.originalId == MOVIE_1.originalId }.run {
            assertEquals("g48cnr", this.messagingCode)
        }
        updatedMovies.first { it.originalId == MOVIE_2.originalId }.run {
            assertEquals("g48cnr", this.messagingCode)
        }
        updatedMovies.first { it.originalId == MOVIE_3.originalId }.run {
            assertEquals("5k4c3o", this.messagingCode)
        }
        updatedMovies.first { it.originalId == MOVIE_4.originalId }.run {
            assertEquals("5k4c3o", this.messagingCode)
        }
        updatedMovies.first { it.originalId == MOVIE_5.originalId }.run {
            assertEquals("98r3jy", this.messagingCode)
        }
        updatedMovies.first { it.originalId == MOVIE_6.originalId }.run {
            assertEquals("98r3jy", this.messagingCode)
        }
        updatedMovies.first { it.originalId == MOVIE_7.originalId }.run {
            assertEquals("98r3jy", this.messagingCode)
        }
    }

    private fun createScreeningForMovie(movieId: UUID) {
        screeningRepository.save(
            createScreening(
                originalId = (0..Int.MAX_VALUE).random(),
                auditoriumId = AUDITORIUM.id,
                auditoriumLayoutId = AUDITORIUM_LAYOUT.id,
                movieId = movieId,
                priceCategoryId = PRICE_CATEGORY.id
            )
        )
    }
}

private val AUDITORIUM = createAuditorium()
private val AUDITORIUM_LAYOUT = createAuditoriumLayout(auditoriumId = AUDITORIUM.id)
private val PRICE_CATEGORY = createPriceCategory()
private val DISTRIBUTOR_1 = createDistributor(originalId = 1, title = "Magic Box Slovakia, s.r.o.")
private val DISTRIBUTOR_2 = createDistributor(originalId = 2, title = "Film Expanded")
private val DISTRIBUTOR_3 = createDistributor(originalId = 3, title = "CinemArt SK s.r.o.")
private val JSO_1 = Jso(originalId = 1001, code = "SE", title = "Strach")
private val JSO_2 = Jso(originalId = 1002, code = "VU", title = "Vulgarizmy")

private val PRODUCTION_1 = Production(
    originalId = 1001,
    code = "1001",
    title = "CZ"
)
private val PRODUCTION_2 = Production(
    originalId = 1002,
    code = "1002",
    title = "SK"
)
private val PRODUCTION_3 = Production(
    originalId = 1003,
    code = "1003",
    title = "USA"
)
private val GENRE_1 = Genre(
    originalId = 1001,
    code = "1001",
    title = "Komedie"
)
private val GENRE_2 = Genre(
    originalId = 1002,
    code = "1002",
    title = "Dokument"
)
private val GENRE_3 = Genre(
    originalId = 1003,
    code = "1003",
    title = "Dětský"
)
private val RATING_1 = Rating(
    originalId = 1001,
    code = "10012",
    title = "12"
)
private val RATING_2 = Rating(
    originalId = 1002,
    code = "10015",
    title = "15"
)
private val RATING_3 = Rating(
    originalId = 1003,
    code = "1007",
    title = "7"
)
private val TECHNOLOGY_1 = Technology(
    originalId = 1001,
    code = "10001",
    title = "DCI2D"
)
private val LANGUAGE_1 = Language(
    originalId = 1001,
    code = "100OV",
    title = "originálna verzia"
)
private val LANGUAGE_2 = Language(
    originalId = 1002,
    code = "100SD",
    title = "slovenský dabing"
)
private val TMS_LANGUAGE_1 = TmsLanguage(
    originalId = 1001,
    code = "100OV",
    title = "originálna verzia"
)
private val TMS_LANGUAGE_2 = TmsLanguage(
    originalId = 1002,
    code = "100SD",
    title = "slovenský dabing"
)

// Prezidentka - the 2022 shitty comedy, record created by Cinemax, has all descriptors
private val MOVIE_1 = Movie(
    id = "9ee5cacd-80ee-4b3f-8c58-fa63decb75ca".toUUID(),
    originalId = 6719,
    rawTitle = "Prezidentka 2D (OV)",
    originalTitle = null,
    title = "Prezidentka",
    code = "MB0223",
    disfilmCode = null,
    releaseYear = 2022,
    premiereDate = LocalDate.parse("2022-06-30"),
    duration = 98,
    parsedRating = MovieRating.PLUS_12,
    parsedFormat = MovieFormat.FORMAT_2D,
    parsedTechnology = null,
    parsedLanguage = MovieLanguage.OV,
    distributorId = DISTRIBUTOR_1.id,
    productionId = PRODUCTION_1.id,
    primaryGenreId = GENRE_1.id,
    secondaryGenreId = null,
    ratingId = RATING_1.id,
    technologyId = TECHNOLOGY_1.id,
    languageId = LANGUAGE_1.id,
    tmsLanguageId = TMS_LANGUAGE_1.id
)

// Prezidentka - the 2022 shitty comedy, record synced from DISFilm, has only basic descriptors
private val MOVIE_2 = Movie(
    id = "6cd654cd-9a07-47f0-b44d-efe02bdfee8a".toUUID(),
    originalId = 8579,
    rawTitle = "Prezidentka 2D (OV)",
    originalTitle = "Prezidentka 2D (OV)",
    title = "Prezidentka",
    code = "003068",
    disfilmCode = "MAG22006A3",
    releaseYear = 2022,
    premiereDate = LocalDate.parse("2022-06-30"),
    duration = 98,
    parsedRating = MovieRating.PLUS_12,
    parsedFormat = MovieFormat.FORMAT_2D,
    parsedTechnology = null,
    parsedLanguage = null,
    distributorId = DISTRIBUTOR_1.id,
    productionId = PRODUCTION_1.id,
    primaryGenreId = GENRE_1.id,
    secondaryGenreId = null,
    ratingId = RATING_1.id,
    technologyId = null,
    languageId = null,
    tmsLanguageId = null
)

// Prezidentka - the 2024 documentary, record synced from DISFilm, has only basic descriptors
private val MOVIE_3 = Movie(
    id = "642c1163-1317-431d-90f9-a55987489dd2".toUUID(),
    originalId = 11086,
    rawTitle = "Prezidentka 2D (OV)",
    originalTitle = "Prezidentka 2D (OV)",
    title = "Prezidentka",
    code = "005193",
    disfilmCode = "FEX24007A3",
    releaseYear = 2024,
    premiereDate = LocalDate.parse("2024-10-31"),
    duration = 114,
    parsedRating = MovieRating.PLUS_12,
    parsedFormat = MovieFormat.FORMAT_2D,
    parsedTechnology = null,
    parsedLanguage = MovieLanguage.OV,
    distributorId = DISTRIBUTOR_2.id,
    productionId = PRODUCTION_2.id,
    primaryGenreId = GENRE_2.id,
    secondaryGenreId = null,
    ratingId = RATING_1.id,
    technologyId = null,
    languageId = null,
    tmsLanguageId = null
)

// Prezidentka - the 2024 documentary, record created by Cinemax, has all descriptors
private val MOVIE_4 = Movie(
    id = "fd74699a-e16b-4c64-8403-6a2e5e74926b".toUUID(),
    originalId = 11814,
    rawTitle = "Prezidentka 2D (OV)",
    originalTitle = null,
    title = "Prezidentka",
    code = "FEX008",
    disfilmCode = null,
    releaseYear = 2024,
    premiereDate = LocalDate.parse("2024-10-31"),
    duration = 108,
    parsedRating = MovieRating.PLUS_15,
    parsedFormat = MovieFormat.FORMAT_2D,
    parsedTechnology = null,
    parsedLanguage = MovieLanguage.OV,
    distributorId = DISTRIBUTOR_2.id,
    productionId = PRODUCTION_2.id,
    primaryGenreId = GENRE_2.id,
    secondaryGenreId = null,
    ratingId = RATING_2.id,
    technologyId = TECHNOLOGY_1.id,
    languageId = LANGUAGE_1.id,
    tmsLanguageId = TMS_LANGUAGE_1.id
)

// synced from DISFilm
private val MOVIE_5 = Movie(
    id = "2d77d303-4860-4e86-9380-593da9dda71e".toUUID(),
    originalId = 12729,
    rawTitle = "Ježko Sonic 3 2D (SD)",
    originalTitle = "Sonic The Hedgehog 3 2D (SD)",
    title = "Ježko Sonic 3",
    code = "800675",
    disfilmCode = "CIN24052A4",
    releaseYear = 2024,
    premiereDate = LocalDate.parse("2024-12-26"),
    duration = 111,
    parsedRating = MovieRating.PLUS_7,
    parsedFormat = MovieFormat.FORMAT_2D,
    parsedTechnology = null,
    parsedLanguage = MovieLanguage.SD,
    distributorId = DISTRIBUTOR_3.id,
    productionId = PRODUCTION_3.id,
    primaryGenreId = GENRE_3.id,
    secondaryGenreId = null,
    ratingId = RATING_3.id,
    technologyId = TECHNOLOGY_1.id,
    languageId = LANGUAGE_2.id,
    tmsLanguageId = TMS_LANGUAGE_2.id
)

// created by Cinemax
private val MOVIE_6 = Movie(
    id = "30b6fecb-f1fd-46d7-9900-0e796fd9e232".toUUID(),
    originalId = 12858,
    rawTitle = "Ježko Sonic 3 2D (SD)",
    originalTitle = null,
    title = "Ježko Sonic 3",
    code = "CN0765",
    disfilmCode = null,
    releaseYear = 2024,
    premiereDate = LocalDate.parse("2024-12-26"),
    duration = 111,
    parsedRating = MovieRating.PLUS_7,
    parsedFormat = MovieFormat.FORMAT_2D,
    parsedTechnology = null,
    parsedLanguage = MovieLanguage.SD,
    distributorId = DISTRIBUTOR_3.id,
    productionId = PRODUCTION_3.id,
    primaryGenreId = GENRE_3.id,
    secondaryGenreId = null,
    ratingId = RATING_3.id,
    technologyId = TECHNOLOGY_1.id,
    languageId = LANGUAGE_2.id,
    tmsLanguageId = TMS_LANGUAGE_2.id
)

// duplicate of MOVIE_6
private val MOVIE_7 = Movie(
    id = "4a5828fa-4010-46a0-bcd5-255aed17ff94".toUUID(),
    originalId = 13209,
    rawTitle = "Ježko Sonic 3 2D (SD)",
    originalTitle = null,
    title = "Ježko Sonic 3",
    code = "CN0766",
    disfilmCode = null,
    releaseYear = 2024,
    premiereDate = LocalDate.parse("2024-12-26"),
    duration = 111,
    parsedRating = MovieRating.PLUS_7,
    parsedFormat = MovieFormat.FORMAT_2D,
    parsedTechnology = null,
    parsedLanguage = MovieLanguage.SD,
    distributorId = DISTRIBUTOR_3.id,
    productionId = PRODUCTION_3.id,
    primaryGenreId = GENRE_3.id,
    secondaryGenreId = null,
    ratingId = RATING_3.id,
    technologyId = TECHNOLOGY_1.id,
    languageId = LANGUAGE_2.id,
    tmsLanguageId = TMS_LANGUAGE_2.id
)
