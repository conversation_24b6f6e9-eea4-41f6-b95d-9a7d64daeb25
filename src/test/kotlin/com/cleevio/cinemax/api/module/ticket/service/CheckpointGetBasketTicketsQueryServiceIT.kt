package com.cleevio.cinemax.api.module.ticket.service

import com.cleevio.cinemax.api.DatabaseCleanup
import com.cleevio.cinemax.api.IntegrationDataTestHelper
import com.cleevio.cinemax.api.IntegrationTest
import com.cleevio.cinemax.api.module.basket.constant.BasketState
import com.cleevio.cinemax.api.module.basketitem.constant.BasketItemType
import com.cleevio.cinemax.api.module.discountcard.constant.DiscountCardType
import com.cleevio.cinemax.api.module.screening.constant.MovieTechnology
import com.cleevio.cinemax.api.module.seat.constant.SeatType
import com.cleevio.cinemax.api.module.ticket.controller.dto.TicketStatus
import com.cleevio.cinemax.api.module.ticket.exception.TicketNotFoundException
import com.cleevio.cinemax.api.module.ticket.service.query.CheckpointGetBasketTicketsQuery
import io.kotest.assertions.throwables.shouldThrow
import io.kotest.matchers.nulls.shouldNotBeNull
import io.kotest.matchers.shouldBe
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import java.time.LocalDateTime
import java.util.UUID

class CheckpointGetBasketTicketsQueryServiceIT @Autowired constructor(
    private val underTest: CheckpointGetBasketTicketsQueryService,
    private val integrationDataTestHelper: IntegrationDataTestHelper,
) : IntegrationTest(DatabaseCleanup.AFTER_EACH) {

    @Test
    fun `test GetTicketsInBasketByTicketReceiptNumberQuery - ticket receipt number on input - should return correct response`() {
        val timeNow = LocalDateTime.of(2025, 1, 1, 12, 0, 0, 0)
        integrationTestClock.setTo(timeNow)

        val auditoriumLayout = integrationDataTestHelper.getAuditoriumLayout(
            auditoriumId = integrationDataTestHelper.getAuditorium(
                id = UUID.randomUUID()
            )
        )

        val seat1 = integrationDataTestHelper.getSeat(
            originalId = 1,
            row = "1",
            number = "1",
            auditoriumId = auditoriumLayout.auditoriumId,
            auditoriumLayoutId = auditoriumLayout.id,
            type = SeatType.PREMIUM_PLUS
        )

        val seat2 = integrationDataTestHelper.getSeat(
            originalId = 2,
            row = "1",
            number = "2",
            auditoriumId = auditoriumLayout.auditoriumId,
            auditoriumLayoutId = auditoriumLayout.id,
            type = SeatType.REGULAR
        )

        val movie = integrationDataTestHelper.getMovie(
            distributorId = integrationDataTestHelper.getDistributor().id,
            parsedTechnology = MovieTechnology.IMAX
        )

        val priceCategory = integrationDataTestHelper.getPriceCategory()
        val priceCategoryItem = integrationDataTestHelper.getPriceCategoryItem(
            priceCategoryId = priceCategory.id,
            title = "Adult"
        )

        val screening = integrationDataTestHelper.getScreening(
            originalId = 100,
            auditoriumId = auditoriumLayout.auditoriumId,
            auditoriumLayoutId = auditoriumLayout.id,
            movieId = movie.id,
            priceCategoryId = priceCategory.id,
            saleTimeLimit = 10,
            date = timeNow.toLocalDate(),
            time = timeNow.toLocalTime().minusMinutes(10).minusSeconds(1)
        )

        val reservation1 = integrationDataTestHelper.getReservation(
            seatId = seat1.id,
            screeningId = screening.id
        )

        val reservation2 = integrationDataTestHelper.getReservation(
            seatId = seat2.id,
            screeningId = screening.id
        )

        val ticketPrice1 = integrationDataTestHelper.getTicketPrice(
            seatId = seat1.id,
            screeningId = screening.id,
            totalPrice = 10.toBigDecimal()
        )

        val ticketPrice2 = integrationDataTestHelper.getTicketPrice(
            seatId = seat2.id,
            screeningId = screening.id,
            totalPrice = 8.toBigDecimal()
        )

        val discountCard = integrationDataTestHelper.getDiscountCard(
            id = UUID.randomUUID(),
            code = "999888777",
            title = "FILM karta"
        )

        val receiptNumber1 = "000000001"
        val ticket1 = integrationDataTestHelper.getTicket(
            originalId = 1000,
            screeningId = screening.id,
            reservationId = reservation1.id,
            ticketPriceId = ticketPrice1.id,
            receiptNumber = receiptNumber1,
            isUsed = true
        )

        val receiptNumber2 = "000000002"
        val ticket2 = integrationDataTestHelper.getTicket(
            originalId = 1001,
            screeningId = screening.id,
            reservationId = reservation2.id,
            ticketPriceId = ticketPrice2.id,
            receiptNumber = receiptNumber2,
            isUsed = false
        )

        // unrelated ticket with different receipt number
        integrationDataTestHelper.getTicket(
            originalId = 1002,
            screeningId = screening.id,
            reservationId = reservation2.id,
            ticketPriceId = ticketPrice2.id,
            receiptNumber = "000000003",
            isUsed = false
        )

        val basket = integrationDataTestHelper.getBasket(
            state = BasketState.PAID_ONLINE,
            variableSymbol = "1234567890",
            totalPrice = 18.toBigDecimal()
        )

        val basketItem1 = integrationDataTestHelper.getBasketItem(
            basketId = basket.id,
            ticketId = ticket1.id,
            type = BasketItemType.TICKET,
            price = 10.toBigDecimal(),
            isCancelled = false
        )

        val basketItem2 = integrationDataTestHelper.getBasketItem(
            basketId = basket.id,
            ticketId = ticket2.id,
            type = BasketItemType.TICKET,
            price = 8.toBigDecimal(),
            isCancelled = false
        )

        integrationDataTestHelper.getDiscountCardUsage(
            discountCardId = discountCard.id,
            ticketBasketItemId = basketItem2.id
        )

        val response = underTest(
            CheckpointGetBasketTicketsQuery(receiptNumber1)
        )

        response.shouldNotBeNull()
        response.screening.shouldNotBeNull()
        response.screening.date shouldBe screening.date
        response.screening.time shouldBe screening.time

        response.screening.movie.shouldNotBeNull()
        response.screening.movie.id shouldBe screening.movieId
        response.screening.movie.title shouldBe movie.title
        response.screening.movie.duration shouldBe movie.duration
        response.screening.movie.format shouldBe movie.parsedFormat
        response.screening.movie.rating shouldBe movie.parsedRating
        response.screening.movie.language shouldBe movie.parsedLanguage
        response.screening.movie.technology shouldBe movie.parsedTechnology

        response.tickets.size shouldBe 2

        val ticketResponse1 = response.tickets.find { it.id == ticket1.id }
        ticketResponse1.shouldNotBeNull()
        ticketResponse1.receiptNumber shouldBe ticket1.receiptNumber
        ticketResponse1.includes3dGlasses shouldBe ticket1.includes3dGlasses
        ticketResponse1.auditoriumCode shouldBe "A"
        ticketResponse1.seatRow shouldBe seat1.row
        ticketResponse1.seatNumber shouldBe seat1.number
        ticketResponse1.seatType shouldBe seat1.type
        ticketResponse1.discountCardType shouldBe null
        ticketResponse1.priceCategoryItemTitle shouldBe priceCategoryItem.title
        ticketResponse1.status shouldBe TicketStatus.ALREADY_USED

        val ticketResponse2 = response.tickets.find { it.id == ticket2.id }
        ticketResponse2.shouldNotBeNull()
        ticketResponse2.receiptNumber shouldBe ticket2.receiptNumber
        ticketResponse2.includes3dGlasses shouldBe ticket2.includes3dGlasses
        ticketResponse2.auditoriumCode shouldBe "A"
        ticketResponse2.seatRow shouldBe seat2.row
        ticketResponse2.seatNumber shouldBe seat2.number
        ticketResponse2.seatType shouldBe seat2.type
        ticketResponse2.discountCardType shouldBe discountCard.type
        ticketResponse2.priceCategoryItemTitle shouldBe priceCategoryItem.title
        ticketResponse2.status shouldBe TicketStatus.TOO_LATE
    }

    @Test
    fun `test GetTicketsInBasketByTicketReceiptNumberQuery - basket variable symbol on input - should return correct response`() {
        val timeNow = LocalDateTime.of(2025, 1, 1, 12, 0, 0, 0)
        integrationTestClock.setTo(timeNow)

        val auditoriumLayout = integrationDataTestHelper.getAuditoriumLayout(
            auditoriumId = integrationDataTestHelper.getAuditorium(
                id = UUID.randomUUID()
            )
        )

        val seat1 = integrationDataTestHelper.getSeat(
            originalId = 1,
            row = "1",
            number = "1",
            auditoriumId = auditoriumLayout.auditoriumId,
            auditoriumLayoutId = auditoriumLayout.id,
            type = SeatType.PREMIUM_PLUS
        )

        val seat2 = integrationDataTestHelper.getSeat(
            originalId = 2,
            row = "1",
            number = "2",
            auditoriumId = auditoriumLayout.auditoriumId,
            auditoriumLayoutId = auditoriumLayout.id,
            type = SeatType.REGULAR
        )

        val movie = integrationDataTestHelper.getMovie(
            distributorId = integrationDataTestHelper.getDistributor().id,
            parsedTechnology = MovieTechnology.IMAX
        )

        val priceCategory = integrationDataTestHelper.getPriceCategory()
        val priceCategoryItem = integrationDataTestHelper.getPriceCategoryItem(
            priceCategoryId = priceCategory.id,
            title = "Adult"
        )

        val screening = integrationDataTestHelper.getScreening(
            originalId = 100,
            auditoriumId = auditoriumLayout.auditoriumId,
            auditoriumLayoutId = auditoriumLayout.id,
            movieId = movie.id,
            priceCategoryId = priceCategory.id,
            saleTimeLimit = 10,
            date = timeNow.toLocalDate(),
            time = timeNow.toLocalTime().plusMinutes(10)
        )

        val reservation1 = integrationDataTestHelper.getReservation(
            seatId = seat1.id,
            screeningId = screening.id
        )

        val reservation2 = integrationDataTestHelper.getReservation(
            seatId = seat2.id,
            screeningId = screening.id
        )

        val ticketPrice1 = integrationDataTestHelper.getTicketPrice(
            seatId = seat1.id,
            screeningId = screening.id,
            totalPrice = 10.toBigDecimal()
        )

        val ticketPrice2 = integrationDataTestHelper.getTicketPrice(
            seatId = seat2.id,
            screeningId = screening.id,
            totalPrice = 8.toBigDecimal()
        )

        val discountCard = integrationDataTestHelper.getDiscountCard(
            id = UUID.randomUUID(),
            code = "999888777",
            title = "FILM karta"
        )

        val ticket1 = integrationDataTestHelper.getTicket(
            originalId = 1000,
            screeningId = screening.id,
            reservationId = reservation1.id,
            ticketPriceId = ticketPrice1.id,
            receiptNumber = "000000001",
            isUsed = false
        )

        val ticket2 = integrationDataTestHelper.getTicket(
            originalId = 1001,
            screeningId = screening.id,
            reservationId = reservation2.id,
            ticketPriceId = ticketPrice2.id,
            receiptNumber = "000000002",
            isUsed = false
        )

        val variableSymbol1 = "1234567890"
        val basket1 = integrationDataTestHelper.getBasket(
            state = BasketState.PAID,
            variableSymbol = variableSymbol1,
            totalPrice = 18.toBigDecimal()
        )

        val variableSymbol2 = "1234567891"
        val basket2 = integrationDataTestHelper.getBasket(
            state = BasketState.PAID,
            variableSymbol = variableSymbol2,
            totalPrice = 18.toBigDecimal()
        )

        val basketItem1 = integrationDataTestHelper.getBasketItem(
            basketId = basket1.id,
            ticketId = ticket1.id,
            type = BasketItemType.TICKET,
            price = 10.toBigDecimal(),
            isCancelled = false
        )

        val basketItem2 = integrationDataTestHelper.getBasketItem(
            basketId = basket2.id,
            ticketId = ticket2.id,
            type = BasketItemType.TICKET,
            price = 8.toBigDecimal(),
            isCancelled = false
        )

        integrationDataTestHelper.getDiscountCardUsage(
            discountCardId = discountCard.id,
            ticketBasketItemId = basketItem2.id
        )

        val response = underTest(
            CheckpointGetBasketTicketsQuery(variableSymbol1)
        )

        response.shouldNotBeNull()
        response.screening.shouldNotBeNull()
        response.screening.date shouldBe screening.date
        response.screening.time shouldBe screening.time

        response.screening.movie.shouldNotBeNull()
        response.screening.movie.id shouldBe screening.movieId
        response.screening.movie.title shouldBe movie.title
        response.screening.movie.duration shouldBe movie.duration
        response.screening.movie.format shouldBe movie.parsedFormat
        response.screening.movie.rating shouldBe movie.parsedRating
        response.screening.movie.language shouldBe movie.parsedLanguage
        response.screening.movie.technology shouldBe movie.parsedTechnology

        response.tickets.size shouldBe 1

        val ticketResponse1 = response.tickets.find { it.id == ticket1.id }
        ticketResponse1.shouldNotBeNull()
        ticketResponse1.receiptNumber shouldBe ticket1.receiptNumber
        ticketResponse1.includes3dGlasses shouldBe ticket1.includes3dGlasses
        ticketResponse1.auditoriumCode shouldBe "A"
        ticketResponse1.seatRow shouldBe seat1.row
        ticketResponse1.seatNumber shouldBe seat1.number
        ticketResponse1.seatType shouldBe seat1.type
        ticketResponse1.discountCardType shouldBe null
        ticketResponse1.priceCategoryItemTitle shouldBe priceCategoryItem.title
        ticketResponse1.status shouldBe TicketStatus.OK
    }

    @Test
    fun `test GetTicketsInBasketByTicketReceiptNumberQuery - basket variable symbol and ticket receipt number collision - should return latest of the two`() {
        val timeNow = LocalDateTime.of(2025, 1, 1, 12, 0, 0, 0)
        integrationTestClock.setTo(timeNow)

        val auditoriumLayout = integrationDataTestHelper.getAuditoriumLayout(
            auditoriumId = integrationDataTestHelper.getAuditorium(
                id = UUID.randomUUID()
            )
        )

        val seat1 = integrationDataTestHelper.getSeat(
            originalId = 1,
            row = "1",
            number = "1",
            auditoriumId = auditoriumLayout.auditoriumId,
            auditoriumLayoutId = auditoriumLayout.id,
            type = SeatType.PREMIUM_PLUS
        )

        val seat2 = integrationDataTestHelper.getSeat(
            originalId = 2,
            row = "1",
            number = "2",
            auditoriumId = auditoriumLayout.auditoriumId,
            auditoriumLayoutId = auditoriumLayout.id,
            type = SeatType.REGULAR
        )

        val movie = integrationDataTestHelper.getMovie(
            distributorId = integrationDataTestHelper.getDistributor().id,
            parsedTechnology = MovieTechnology.IMAX
        )

        val priceCategory = integrationDataTestHelper.getPriceCategory()
        val priceCategoryItem = integrationDataTestHelper.getPriceCategoryItem(
            priceCategoryId = priceCategory.id,
            title = "Adult"
        )

        val screening = integrationDataTestHelper.getScreening(
            originalId = 100,
            auditoriumId = auditoriumLayout.auditoriumId,
            auditoriumLayoutId = auditoriumLayout.id,
            movieId = movie.id,
            priceCategoryId = priceCategory.id,
            saleTimeLimit = 10,
            date = timeNow.toLocalDate(),
            time = timeNow.toLocalTime().plusMinutes(10)
        )

        val reservation1 = integrationDataTestHelper.getReservation(
            seatId = seat1.id,
            screeningId = screening.id
        )

        val reservation2 = integrationDataTestHelper.getReservation(
            seatId = seat2.id,
            screeningId = screening.id
        )

        val ticketPrice1 = integrationDataTestHelper.getTicketPrice(
            seatId = seat1.id,
            screeningId = screening.id,
            totalPrice = 10.toBigDecimal()
        )

        val ticketPrice2 = integrationDataTestHelper.getTicketPrice(
            seatId = seat2.id,
            screeningId = screening.id,
            totalPrice = 8.toBigDecimal()
        )

        val discountCard = integrationDataTestHelper.getDiscountCard(
            id = UUID.randomUUID(),
            code = "999888777",
            title = "FILM karta"
        )

        val ticket1 = integrationDataTestHelper.getTicket(
            originalId = 1000,
            screeningId = screening.id,
            reservationId = reservation1.id,
            ticketPriceId = ticketPrice1.id,
            receiptNumber = "000000001",
            isUsed = false
        )

        val collidingReceiptNumber = "000000002"
        val ticket2 = integrationDataTestHelper.getTicket(
            originalId = 1001,
            screeningId = screening.id,
            reservationId = reservation2.id,
            ticketPriceId = ticketPrice2.id,
            receiptNumber = collidingReceiptNumber,
            isUsed = false
        )

        val basket1 = integrationDataTestHelper.getBasket(
            state = BasketState.PAID,
            variableSymbol = collidingReceiptNumber,
            totalPrice = 18.toBigDecimal()
        )

        val basket2 = integrationDataTestHelper.getBasket(
            state = BasketState.PAID,
            variableSymbol = "100000002",
            totalPrice = 18.toBigDecimal()
        )

        val basketItem1 = integrationDataTestHelper.getBasketItem(
            basketId = basket1.id,
            ticketId = ticket1.id,
            type = BasketItemType.TICKET,
            price = 10.toBigDecimal(),
            isCancelled = false
        )

        val basketItem2 = integrationDataTestHelper.getBasketItem(
            basketId = basket2.id,
            ticketId = ticket2.id,
            type = BasketItemType.TICKET,
            price = 8.toBigDecimal(),
            isCancelled = false
        )

        integrationDataTestHelper.getDiscountCardUsage(
            discountCardId = discountCard.id,
            ticketBasketItemId = basketItem2.id
        )

        val response = underTest(
            CheckpointGetBasketTicketsQuery(collidingReceiptNumber)
        )

        response.shouldNotBeNull()
        response.screening.shouldNotBeNull()
        response.screening.date shouldBe screening.date
        response.screening.time shouldBe screening.time

        response.screening.movie.shouldNotBeNull()
        response.screening.movie.id shouldBe screening.movieId
        response.screening.movie.title shouldBe movie.title
        response.screening.movie.duration shouldBe movie.duration
        response.screening.movie.format shouldBe movie.parsedFormat
        response.screening.movie.rating shouldBe movie.parsedRating
        response.screening.movie.language shouldBe movie.parsedLanguage
        response.screening.movie.technology shouldBe movie.parsedTechnology

        response.tickets.size shouldBe 1

        val ticketResponse1 = response.tickets.find { it.id == ticket2.id }
        ticketResponse1.shouldNotBeNull()
        ticketResponse1.receiptNumber shouldBe ticket2.receiptNumber
        ticketResponse1.includes3dGlasses shouldBe ticket2.includes3dGlasses
        ticketResponse1.auditoriumCode shouldBe "A"
        ticketResponse1.seatRow shouldBe seat2.row
        ticketResponse1.seatNumber shouldBe seat2.number
        ticketResponse1.seatType shouldBe seat2.type
        ticketResponse1.discountCardType shouldBe DiscountCardType.CARD
        ticketResponse1.priceCategoryItemTitle shouldBe priceCategoryItem.title
        ticketResponse1.status shouldBe TicketStatus.OK
    }

    @Test
    fun `test GetTicketsInBasketByTicketReceiptNumberQuery - ticket not found - should throw exception`() {
        shouldThrow<TicketNotFoundException> {
            underTest(
                CheckpointGetBasketTicketsQuery("non-existent-receipt-number-or-variable-symbol")
            )
        }
    }

    @Test
    fun `test GetTicketsInBasketByTicketReceiptNumberQuery - ticket in non-paid basket - should throw exception`() {
        val auditoriumLayout = integrationDataTestHelper.getAuditoriumLayout(
            auditoriumId = integrationDataTestHelper.getAuditorium(
                id = UUID.randomUUID()
            )
        )

        val seat = integrationDataTestHelper.getSeat(
            originalId = 1,
            row = "1",
            number = "1",
            auditoriumId = auditoriumLayout.auditoriumId,
            auditoriumLayoutId = auditoriumLayout.id,
            type = SeatType.REGULAR
        )

        val priceCategory = integrationDataTestHelper.getPriceCategory()
        val priceCategoryItem = integrationDataTestHelper.getPriceCategoryItem(
            priceCategoryId = priceCategory.id,
            title = "Adult"
        )

        val screening = integrationDataTestHelper.getScreening(
            originalId = 100,
            auditoriumId = auditoriumLayout.auditoriumId,
            auditoriumLayoutId = auditoriumLayout.id,
            priceCategoryId = priceCategory.id
        )

        val reservation = integrationDataTestHelper.getReservation(
            seatId = seat.id,
            screeningId = screening.id
        )

        val ticketPrice = integrationDataTestHelper.getTicketPrice(
            seatId = seat.id,
            screeningId = screening.id,
            totalPrice = 10.toBigDecimal()
        )

        val receiptNumber = "000000001"
        val ticket = integrationDataTestHelper.getTicket(
            originalId = 1000,
            screeningId = screening.id,
            reservationId = reservation.id,
            ticketPriceId = ticketPrice.id,
            receiptNumber = receiptNumber,
            isUsed = false
        )

        val basket = integrationDataTestHelper.getBasket(
            state = BasketState.OPEN, // Not a paid basket state
            variableSymbol = "1234567890",
            totalPrice = 10.toBigDecimal()
        )

        integrationDataTestHelper.getBasketItem(
            basketId = basket.id,
            ticketId = ticket.id,
            type = BasketItemType.TICKET,
            price = 10.toBigDecimal(),
            isCancelled = false
        )

        shouldThrow<TicketNotFoundException> {
            underTest(
                CheckpointGetBasketTicketsQuery(receiptNumber)
            )
        }
    }

    @Test
    fun `test GetTicketsInBasketByTicketReceiptNumberQuery - cancelled ticket - should throw exception`() {
        val auditoriumLayout = integrationDataTestHelper.getAuditoriumLayout(
            auditoriumId = integrationDataTestHelper.getAuditorium(
                id = UUID.randomUUID()
            )
        )

        val seat = integrationDataTestHelper.getSeat(
            originalId = 1,
            row = "1",
            number = "1",
            auditoriumId = auditoriumLayout.auditoriumId,
            auditoriumLayoutId = auditoriumLayout.id,
            type = SeatType.REGULAR
        )

        val priceCategory = integrationDataTestHelper.getPriceCategory()
        val priceCategoryItem = integrationDataTestHelper.getPriceCategoryItem(
            priceCategoryId = priceCategory.id,
            title = "Adult"
        )

        val screening = integrationDataTestHelper.getScreening(
            originalId = 100,
            auditoriumId = auditoriumLayout.auditoriumId,
            auditoriumLayoutId = auditoriumLayout.id,
            priceCategoryId = priceCategory.id
        )

        val reservation = integrationDataTestHelper.getReservation(
            seatId = seat.id,
            screeningId = screening.id
        )

        val ticketPrice = integrationDataTestHelper.getTicketPrice(
            seatId = seat.id,
            screeningId = screening.id,
            totalPrice = 10.toBigDecimal()
        )

        val receiptNumber = "000000001"
        val ticket = integrationDataTestHelper.getTicket(
            originalId = 1000,
            screeningId = screening.id,
            reservationId = reservation.id,
            ticketPriceId = ticketPrice.id,
            receiptNumber = receiptNumber,
            isUsed = false
        )

        val basket = integrationDataTestHelper.getBasket(
            state = BasketState.PAID,
            variableSymbol = "1234567890",
            totalPrice = 10.toBigDecimal()
        )

        integrationDataTestHelper.getBasketItem(
            basketId = basket.id,
            ticketId = ticket.id,
            type = BasketItemType.TICKET,
            price = 10.toBigDecimal(),
            isCancelled = true
        )

        shouldThrow<TicketNotFoundException> {
            underTest(
                CheckpointGetBasketTicketsQuery(receiptNumber)
            )
        }
    }
}
