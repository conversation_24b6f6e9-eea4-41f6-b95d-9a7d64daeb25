package com.cleevio.cinemax.api.module.screening.service

import com.cleevio.cinemax.api.IntegrationTest
import com.cleevio.cinemax.api.common.util.toDateString
import com.cleevio.cinemax.api.common.util.toTimeString
import com.cleevio.cinemax.api.module.screening.service.model.ScreeningExportRecordModel
import org.apache.poi.xssf.usermodel.XSSFWorkbook
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import java.io.ByteArrayInputStream
import java.time.LocalDate
import java.time.LocalTime
import kotlin.test.assertContains

class ScreeningXlsxExportResultMapperIT @Autowired constructor(
    private val underTest: ScreeningXlsxExportResultMapper,
) : IntegrationTest() {

    @Test
    fun `test mapToExportResultModel - should map all details correctly and generate Excel file`() {
        val exportResult = underTest.mapToExportResultModel(
            dateFrom = FIXED_DATE.minusDays(30),
            dateTo = FIXED_DATE.plusDays(30),
            data = EXPORT_DATA,
            username = USERNAME
        )
        val byteArray = exportResult.inputStream.readAllBytes()
        assert(byteArray.isNotEmpty())

        // open the generated Excel file from the byte array
        val workbook = XSSFWorkbook(ByteArrayInputStream(byteArray))
        val sheet = workbook.getSheet("Export predstavenia")

        // verify main header
        val mainHeaderRow1 = sheet.getRow(0).getCell(0).stringCellValue
        val mainHeaderRow2 = sheet.getRow(0).getCell(2).stringCellValue
        val mainHeaderRow3 = sheet.getRow(0).getCell(sheet.getRow(0).lastCellNum - 1).stringCellValue

        assertEquals("Kino: Bratislava Bory\nPoužívateľ: $USERNAME", mainHeaderRow1)
        assertEquals(
            "Export predstavenia\nPredstavenia od: ${FIXED_DATE.minusDays(30).toDateString()} do: ${
                FIXED_DATE.plusDays(30).toDateString()
            } ",
            mainHeaderRow2
        )
        assertContains(mainHeaderRow3, "Dátum: ${LocalDate.now().toDateString()}\nČas:")

        // verify column headers
        val columnHeaders = sheet.getRow(4)
        assertEquals("Názov filmu", columnHeaders.getCell(0).stringCellValue)
        assertEquals("Distribútor", columnHeaders.getCell(1).stringCellValue)
        assertEquals("Jazyk", columnHeaders.getCell(2).stringCellValue)
        assertEquals("2D/3D", columnHeaders.getCell(3).stringCellValue)
        assertEquals("Ďalšie tech.", columnHeaders.getCell(4).stringCellValue)
        assertEquals("Minutáž", columnHeaders.getCell(5).stringCellValue)
        assertEquals("Prístupnosť", columnHeaders.getCell(6).stringCellValue)
        assertEquals("Premietací dátum", columnHeaders.getCell(7).stringCellValue)
        assertEquals("Premietací čas", columnHeaders.getCell(8).stringCellValue)
        assertEquals("Ukončenie", columnHeaders.getCell(9).stringCellValue)
        assertEquals("Sála", columnHeaders.getCell(10).stringCellValue)
        assertEquals("Cenová kateg.", columnHeaders.getCell(11).stringCellValue)
        assertEquals("Provízia distr.[%]", columnHeaders.getCell(12).stringCellValue)

        // verify data rows
        val dataRow = sheet.getRow(5)
        assertEquals("Matrix", dataRow.getCell(0).stringCellValue)
        assertEquals("Hello movies, s.r.o.", dataRow.getCell(1).stringCellValue)
        assertEquals("czech", dataRow.getCell(2).stringCellValue)
        assertEquals("3D", dataRow.getCell(3).stringCellValue)
        assertEquals("3D IMAX", dataRow.getCell(4).stringCellValue)
        assertEquals(150.0, dataRow.getCell(5).numericCellValue)
        assertEquals("Very good", dataRow.getCell(6).stringCellValue)
        assertEquals(FIXED_DATE.plusDays(8).toDateString(), dataRow.getCell(7).stringCellValue)
        assertEquals(LocalTime.of(20, 15, 0).toTimeString(), dataRow.getCell(8).stringCellValue)
        assertEquals(LocalTime.of(22, 45, 0).toTimeString(), dataRow.getCell(9).stringCellValue)
        assertEquals("IMAX - CINEMAX BRATISLAVA", dataRow.getCell(10).stringCellValue)
        assertEquals("Do 17h", dataRow.getCell(11).stringCellValue)
        assertEquals(50.0, dataRow.getCell(12).numericCellValue)
    }
}

private const val USERNAME = "username"

private val FIXED_DATE = LocalDate.of(2024, 6, 26)
private val EXPORT_DATA = listOf(
    ScreeningExportRecordModel(
        movieTitle = "Matrix",
        distributorTitle = "Hello movies, s.r.o.",
        movieLanguage = "czech",
        movieTechnology = "3D IMAX",
        movieFormat = "3D",
        movieDuration = 150,
        movieRating = "Very good",
        screeningDate = FIXED_DATE.plusDays(8),
        screeningTime = LocalTime.of(20, 15, 0),
        screeningEndTime = LocalTime.of(22, 45, 0),
        auditoriumTitle = "IMAX - CINEMAX BRATISLAVA",
        priceCategoryTitle = "Do 17h",
        distributorCommission = 50
    )
)
