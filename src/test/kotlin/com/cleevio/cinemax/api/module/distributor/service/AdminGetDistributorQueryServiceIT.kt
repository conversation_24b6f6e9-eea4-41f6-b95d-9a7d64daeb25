package com.cleevio.cinemax.api.module.distributor.service

import com.cleevio.cinemax.api.DatabaseCleanup
import com.cleevio.cinemax.api.IntegrationTest
import com.cleevio.cinemax.api.module.distributor.exception.DistributorNotFoundException
import com.cleevio.cinemax.api.module.distributor.service.query.AdminGetDistributorQuery
import com.cleevio.cinemax.api.util.createDistributor
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import org.springframework.beans.factory.annotation.Autowired
import java.util.UUID
import kotlin.test.assertEquals

class AdminGetDistributorQueryServiceIT @Autowired constructor(
    private val underTest: AdminGetDistributorQueryService,
    private val distributorRepository: DistributorRepository,
) : IntegrationTest(DatabaseCleanup.AFTER_EACH) {

    @Test
    fun `test AdminGetDistributorQuery - should return correct distributor`() {
        val existingDistributor = createDistributor(
            originalId = 1,
            code = "OC1"
        ).also { distributorRepository.save(it) }

        val response = underTest(AdminGetDistributorQuery(existingDistributor.id))

        assertEquals(existingDistributor.id, response.id)
        assertEquals(existingDistributor.code, response.code)
        assertEquals(existingDistributor.addressStreet, response.addressStreet)
        assertEquals(existingDistributor.addressCity, response.addressCity)
        assertEquals(existingDistributor.addressPostCode, response.addressPostCode)
        assertEquals(existingDistributor.contactName1, response.contactName1)
        assertEquals(existingDistributor.contactName2, response.contactName2)
        assertEquals(existingDistributor.contactName3, response.contactName3)
        assertEquals(existingDistributor.contactPhone1, response.contactPhone1)
        assertEquals(existingDistributor.contactPhone2, response.contactPhone2)
        assertEquals(existingDistributor.contactPhone3, response.contactPhone3)
        assertEquals(existingDistributor.contactEmails, response.contactEmails)
        assertEquals(existingDistributor.bankName, response.bankName)
        assertEquals(existingDistributor.bankAccount, response.bankAccount)
        assertEquals(existingDistributor.idNumber, response.idNumber)
        assertEquals(existingDistributor.taxIdNumber, response.taxIdNumber)
        assertEquals(existingDistributor.vatRate, response.vatRate)
        assertEquals(existingDistributor.note, response.note)
    }

    @Test
    fun `test AdminGetDistributorQuery - should throw if distributor is deleted or does not exist`() {
        val deletedDistributor = createDistributor(
            originalId = 1,
            code = "OC1"
        ).also {
            distributorRepository.save(it.apply { markDeleted() })
        }

        assertThrows<DistributorNotFoundException> {
            underTest(AdminGetDistributorQuery(deletedDistributor.id))
        }
        assertThrows<DistributorNotFoundException> {
            underTest(AdminGetDistributorQuery(UUID.fromString("5a6dd6f5-9d0d-4af3-ac82-847546b0a38b")))
        }
    }
}
