package com.cleevio.cinemax.api.module.distributor.entity

import com.cleevio.cinemax.api.module.distributor.event.toMessagingEvent
import com.cleevio.cinemax.api.util.createDistributor
import org.junit.jupiter.api.Assertions.assertEquals
import java.util.Optional
import kotlin.test.Test

class DistributorExtensionsTest {

    @Test
    fun `test toMessagingEvent - should map Distributor to AdminDistributorCreatedOrUpdatedEvent correctly`() {
        val distributor = createDistributor()
        val event = distributor.toMessagingEvent()

        assertEquals(distributor.code, event.code)
        assertEquals(Optional.ofNullable(distributor.disfilmCode), event.disfilmCode)
        assertEquals(distributor.title, event.title)
        assertEquals(Optional.ofNullable(distributor.addressStreet), event.addressStreet)
        assertEquals(Optional.ofNullable(distributor.addressCity), event.addressCity)
        assertEquals(Optional.ofNullable(distributor.addressPostCode), event.addressPostCode)
        assertEquals(Optional.ofNullable(distributor.contactName1), event.contactName1)
        assertEquals(Optional.ofNullable(distributor.contactName2), event.contactName2)
        assertEquals(Optional.ofNullable(distributor.contactName3), event.contactName3)
        assertEquals(Optional.ofNullable(distributor.contactPhone1), event.contactPhone1)
        assertEquals(Optional.ofNullable(distributor.contactPhone2), event.contactPhone2)
        assertEquals(Optional.ofNullable(distributor.contactPhone3), event.contactPhone3)
        assertEquals(Optional.ofNullable(distributor.contactEmails), event.contactEmails)
        assertEquals(Optional.ofNullable(distributor.bankName), event.bankName)
        assertEquals(Optional.ofNullable(distributor.bankAccount), event.bankAccount)
        assertEquals(Optional.ofNullable(distributor.idNumber), event.idNumber)
        assertEquals(Optional.ofNullable(distributor.taxIdNumber), event.taxIdNumber)
        assertEquals(Optional.ofNullable(distributor.vatRate), event.vatRate)
        assertEquals(Optional.ofNullable(distributor.note), event.note)
    }
}
