package com.cleevio.cinemax.api.module.distributor.controller.mapper

import com.cleevio.cinemax.api.module.distributor.controller.dto.AdminSearchDistributorsResponse
import com.cleevio.cinemax.api.util.createDistributor
import org.junit.jupiter.api.Test
import kotlin.test.assertEquals

class AdminSearchDistributorsResponseMapperTest {

    @Test
    fun `test map list - should correctly map to response object`() {
        val distributor1 = createDistributor(title = "Distributor title 1")
        val distributor2 = createDistributor(title = "Distributor title 2")
        val expectedResponse = listOf(
            AdminSearchDistributorsResponse(
                id = distributor1.id,
                title = distributor1.title,
                createdAt = distributor1.createdAt,
                updatedAt = distributor1.updatedAt
            ),
            AdminSearchDistributorsResponse(
                id = distributor2.id,
                title = distributor2.title,
                createdAt = distributor2.createdAt,
                updatedAt = distributor2.updatedAt
            )
        )

        assertEquals(expectedResponse, DistributorSearchResponseMapper.mapList(listOf(distributor1, distributor2)))
    }
}
