package com.cleevio.cinemax.api.module.supplier.service

import com.cleevio.cinemax.api.DatabaseCleanup
import com.cleevio.cinemax.api.IntegrationTest
import com.cleevio.cinemax.api.module.supplier.exception.SupplierNotFoundException
import com.cleevio.cinemax.api.module.supplier.service.query.AdminGetSupplierQuery
import com.cleevio.cinemax.api.util.createSupplier
import com.cleevio.cinemax.api.util.toUUID
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import org.springframework.beans.factory.annotation.Autowired
import kotlin.test.assertEquals

class AdminGetSupplierQueryServiceIT @Autowired constructor(
    private val underTest: AdminGetSupplierQueryService,
    private val supplierRepository: SupplierRepository,
) : IntegrationTest(DatabaseCleanup.AFTER_EACH) {

    @Test
    fun `test AdminGetSupplierQuery - should return correct supplier`() {
        val existingSupplier = createSupplier().also { supplierRepository.save(it) }
        val response = underTest(AdminGetSupplierQuery(existingSupplier.id))

        assertEquals(existingSupplier.id, response.id)
        assertEquals(existingSupplier.code, response.code)
        assertEquals(existingSupplier.title, response.title)
        assertEquals(existingSupplier.addressStreet, response.addressStreet)
        assertEquals(existingSupplier.addressCity, response.addressCity)
        assertEquals(existingSupplier.addressPostCode, response.addressPostCode)
        assertEquals(existingSupplier.contactName, response.contactName)
        assertEquals(existingSupplier.contactPhone, response.contactPhone)
        assertEquals(existingSupplier.contactEmails, response.contactEmails)
        assertEquals(existingSupplier.bankName, response.bankName)
        assertEquals(existingSupplier.bankAccount, response.bankAccount)
        assertEquals(existingSupplier.idNumber, response.idNumber)
        assertEquals(existingSupplier.taxIdNumber, response.taxIdNumber)
    }

    @Test
    fun `test AdminGetSupplierQuery - should throw if supplier is deleted or does not exist`() {
        val deletedSupplier = createSupplier().also {
            supplierRepository.save(it.apply { markDeleted() })
        }

        assertThrows<SupplierNotFoundException> {
            underTest(AdminGetSupplierQuery(deletedSupplier.id))
        }
        assertThrows<SupplierNotFoundException> {
            underTest(AdminGetSupplierQuery(1.toUUID()))
        }
    }
}
