package com.cleevio.cinemax.api.module.screening.service

import com.cleevio.cinemax.api.DatabaseCleanup
import com.cleevio.cinemax.api.IntegrationTest
import com.cleevio.cinemax.api.common.util.truncatedToSeconds
import com.cleevio.cinemax.api.module.auditorium.service.AuditoriumRepository
import com.cleevio.cinemax.api.module.auditoriumlayout.service.AuditoriumLayoutRepository
import com.cleevio.cinemax.api.module.distributor.service.DistributorRepository
import com.cleevio.cinemax.api.module.groupreservation.service.GroupReservationRepository
import com.cleevio.cinemax.api.module.language.entity.Language
import com.cleevio.cinemax.api.module.language.service.LanguageRepository
import com.cleevio.cinemax.api.module.movie.service.MovieRepository
import com.cleevio.cinemax.api.module.pricecategory.service.PriceCategoryRepository
import com.cleevio.cinemax.api.module.rating.entity.Rating
import com.cleevio.cinemax.api.module.rating.service.RatingRepository
import com.cleevio.cinemax.api.module.reservation.constant.ReservationState
import com.cleevio.cinemax.api.module.reservation.service.ReservationRepository
import com.cleevio.cinemax.api.module.screening.exception.ScreeningNotFoundException
import com.cleevio.cinemax.api.module.screening.service.query.AdminGetScreeningQuery
import com.cleevio.cinemax.api.module.seat.constant.DoubleSeatType
import com.cleevio.cinemax.api.module.seat.constant.SeatType
import com.cleevio.cinemax.api.module.seat.service.SeatRepository
import com.cleevio.cinemax.api.module.technology.entity.Technology
import com.cleevio.cinemax.api.module.technology.service.TechnologyRepository
import com.cleevio.cinemax.api.module.tmslanguage.entity.TmsLanguage
import com.cleevio.cinemax.api.module.tmslanguage.service.TmsLanguageRepository
import com.cleevio.cinemax.api.util.createAuditorium
import com.cleevio.cinemax.api.util.createAuditoriumLayout
import com.cleevio.cinemax.api.util.createDistributor
import com.cleevio.cinemax.api.util.createGroupReservation
import com.cleevio.cinemax.api.util.createMovie
import com.cleevio.cinemax.api.util.createPriceCategory
import com.cleevio.cinemax.api.util.createReservation
import com.cleevio.cinemax.api.util.createScreening
import com.cleevio.cinemax.api.util.createSeat
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import org.springframework.beans.factory.annotation.Autowired
import java.time.LocalTime
import kotlin.test.assertEquals
import kotlin.test.assertNotNull
import kotlin.test.assertNull

class AdminGetScreeningQueryServiceIT @Autowired constructor(
    private val underTest: AdminGetScreeningQueryService,
    private val auditoriumLayoutRepository: AuditoriumLayoutRepository,
    private val ratingRepository: RatingRepository,
    private val technologyRepository: TechnologyRepository,
    private val languageRepository: LanguageRepository,
    private val tmsLanguageRepository: TmsLanguageRepository,
    private val seatRepository: SeatRepository,
    private val reservationRepository: ReservationRepository,
    private val screeningRepository: ScreeningRepository,
    private val auditoriumRepository: AuditoriumRepository,
    private val distributorRepository: DistributorRepository,
    private val movieRepository: MovieRepository,
    private val priceCategoryRepository: PriceCategoryRepository,
    private val groupReservationRepository: GroupReservationRepository,
) : IntegrationTest(DatabaseCleanup.AFTER_EACH) {

    @BeforeEach
    fun setUp() {
        auditoriumRepository.saveAll(setOf(AUDITORIUM_1, AUDITORIUM_2))
        auditoriumLayoutRepository.saveAll(setOf(AUDITORIUM_LAYOUT_1, AUDITORIUM_LAYOUT_2))
        distributorRepository.save(DISTRIBUTOR_1)
        ratingRepository.save(RATING_1)
        technologyRepository.save(TECHNOLOGY_1)
        languageRepository.save(LANGUAGE_1)
        tmsLanguageRepository.save(TMS_LANGUAGE_1)
        movieRepository.saveAll(setOf(MOVIE_1, MOVIE_2))
        priceCategoryRepository.save(PRICE_CATEGORY_1)
        screeningRepository.saveAll(setOf(SCREENING_1, SCREENING_2, SCREENING_3_DELETED))
        seatRepository.saveAll(setOf(SEAT_1, SEAT_2, SEAT_3, SEAT_4, SEAT_5, SEAT_6, SEAT_7, SEAT_8))
        groupReservationRepository.save(GROUP_RESERVATION_1)
        reservationRepository.saveAll(setOf(RESERVATION_1, RESERVATION_2, RESERVATION_3_DELETED, RESERVATION_4, RESERVATION_5))
    }

    @Test
    fun `test AdminGetScreeningsQuery - valid screeningId - should return correct response`() {
        val response = underTest(AdminGetScreeningQuery(screeningId = SCREENING_1.id))

        assertNotNull(response)
        assertEquals(SCREENING_1.id, response.id)
        assertEquals(SCREENING_1.date, response.date)
        assertEquals(SCREENING_1.time, response.time)
        assertEquals(SCREENING_1.saleTimeLimit, response.saleTimeLimit)
        assertEquals(SCREENING_1.createdAt.truncatedToSeconds(), response.createdAt.truncatedToSeconds())
        assertEquals(SCREENING_1.updatedAt.truncatedToSeconds(), response.updatedAt.truncatedToSeconds())

        assertEquals(MOVIE_1.id, response.movie.id)
        assertEquals(MOVIE_1.title, response.movie.title)
        assertEquals(MOVIE_1.rawTitle, response.movie.rawTitle)

        assertEquals(AUDITORIUM_1.id, response.auditorium.id)
        assertEquals(AUDITORIUM_1.title, response.auditorium.title)
        assertEquals(AUDITORIUM_1.code, response.auditorium.code)

        assertEquals(4, response.seats.size)

        response.seats.first { it.id == SEAT_1.id }.let { seat ->
            assertEquals(SEAT_1.type, seat.type)
            assertEquals(SEAT_1.doubleSeatType, seat.doubleSeatType)
            assertEquals(SEAT_1.row, seat.row)
            assertEquals(SEAT_1.number, seat.number)
            assertEquals(SEAT_1.positionLeft, seat.positionLeft)
            assertEquals(SEAT_1.positionTop, seat.positionTop)
            assertEquals(RESERVATION_5.state, seat.reservation?.state)
            assertNull(seat.reservation?.groupReservationId)
        }

        response.seats.first { it.id == SEAT_2.id }.let { seat ->
            assertEquals(SEAT_2.type, seat.type)
            assertEquals(SEAT_2.doubleSeatType, seat.doubleSeatType)
            assertEquals(SEAT_2.row, seat.row)
            assertEquals(SEAT_2.number, seat.number)
            assertEquals(SEAT_2.positionLeft, seat.positionLeft)
            assertEquals(SEAT_2.positionTop, seat.positionTop)
            assertEquals(RESERVATION_2.state, seat.reservation?.state)
            assertEquals(GROUP_RESERVATION_1.id, seat.reservation?.groupReservationId)
        }

        response.seats.first { it.id == SEAT_3.id }.let { seat ->
            assertEquals(SEAT_3.type, seat.type)
            assertEquals(SEAT_3.doubleSeatType, seat.doubleSeatType)
            assertEquals(SEAT_3.row, seat.row)
            assertEquals(SEAT_3.number, seat.number)
            assertEquals(SEAT_3.positionLeft, seat.positionLeft)
            assertEquals(SEAT_3.positionTop, seat.positionTop)
            assertNull(seat.reservation)
        }

        response.seats.first { it.id == SEAT_4.id }.let { seat ->
            assertEquals(SEAT_4.type, seat.type)
            assertEquals(SEAT_4.doubleSeatType, seat.doubleSeatType)
            assertEquals(SEAT_4.row, seat.row)
            assertEquals(SEAT_4.number, seat.number)
            assertEquals(SEAT_4.positionLeft, seat.positionLeft)
            assertEquals(SEAT_4.positionTop, seat.positionTop)
            assertNull(seat.reservation)
        }
    }

    @Test
    fun `test AdminGetScreeningsQuery - screening not found - should throw`() {
        val query = AdminGetScreeningQuery(screeningId = SCREENING_3_DELETED.id)

        assertThrows<ScreeningNotFoundException> {
            underTest(query)
        }
    }
}

private val LOCAL_TIME = LocalTime.of(20, 0, 0)
private val AUDITORIUM_1 = createAuditorium(
    originalId = 1,
    code = "A",
    title = "SÁLA A - CINEMAX BRATISLAVA",
    originalCode = 22
)
private val AUDITORIUM_2 = createAuditorium(
    originalId = 2,
    code = "B",
    title = "SÁLA B - CINEMAX BRATISLAVA",
    originalCode = 33
)
private val DISTRIBUTOR_1 = createDistributor(originalId = 1)
private val AUDITORIUM_LAYOUT_1 = createAuditoriumLayout(originalId = 1, auditoriumId = AUDITORIUM_1.id)
private val AUDITORIUM_LAYOUT_2 = createAuditoriumLayout(originalId = 2, auditoriumId = AUDITORIUM_1.id)
private val RATING_1 = Rating(
    originalId = 1,
    code = "13",
    title = "13"
)
private val TECHNOLOGY_1 = Technology(
    originalId = 1,
    title = "2D",
    code = "2DA"
)
private val LANGUAGE_1 = Language(
    originalId = 10,
    title = "slovak",
    code = "SVK"
)
private val TMS_LANGUAGE_1 = TmsLanguage(
    originalId = 1,
    title = "Slovenske zneni",
    code = "TMS2"
)
private val MOVIE_1 = createMovie(
    originalId = 1,
    title = "Star Wars: Episode I – The Phantom Menace",
    releaseYear = 2001,
    distributorId = DISTRIBUTOR_1.id,
    ratingId = RATING_1.id,
    technologyId = TECHNOLOGY_1.id,
    languageId = LANGUAGE_1.id,
    tmsLanguageId = TMS_LANGUAGE_1.id
)
private val MOVIE_2 = createMovie(
    originalId = 2,
    title = "Star Wars: Episode II",
    releaseYear = 2001,
    distributorId = DISTRIBUTOR_1.id,
    ratingId = RATING_1.id,
    technologyId = TECHNOLOGY_1.id,
    languageId = LANGUAGE_1.id,
    tmsLanguageId = TMS_LANGUAGE_1.id
)
private val PRICE_CATEGORY_1 = createPriceCategory(
    originalId = 1,
    title = "ARTMAX PO 17",
    active = true
)
private val SCREENING_1 = createScreening(
    originalId = 1,
    auditoriumId = AUDITORIUM_1.id,
    auditoriumLayoutId = AUDITORIUM_LAYOUT_1.id,
    movieId = MOVIE_1.id,
    priceCategoryId = PRICE_CATEGORY_1.id,
    time = LOCAL_TIME.plusHours(2).plusMinutes(1)
)
private val SCREENING_2 = createScreening(
    originalId = 2,
    auditoriumId = AUDITORIUM_2.id,
    auditoriumLayoutId = AUDITORIUM_LAYOUT_1.id,
    movieId = MOVIE_2.id,
    priceCategoryId = PRICE_CATEGORY_1.id,
    time = LOCAL_TIME.plusHours(2).plusMinutes(1)
)
private val SCREENING_3_DELETED = createScreening(
    originalId = 3,
    auditoriumId = AUDITORIUM_1.id,
    auditoriumLayoutId = AUDITORIUM_LAYOUT_1.id,
    movieId = MOVIE_1.id,
    priceCategoryId = PRICE_CATEGORY_1.id,
    time = LOCAL_TIME.plusHours(2).plusMinutes(1)
).apply { markDeleted() }
private val SEAT_1 = createSeat(
    originalId = 1,
    auditoriumId = AUDITORIUM_1.id,
    auditoriumLayoutId = AUDITORIUM_LAYOUT_1.id,
    type = SeatType.VIP,
    doubleSeatType = DoubleSeatType.DOUBLE_SEAT_LEFT
)
private val SEAT_2 = createSeat(
    originalId = 2,
    auditoriumId = AUDITORIUM_1.id,
    auditoriumLayoutId = AUDITORIUM_LAYOUT_1.id,
    type = SeatType.REGULAR,
    doubleSeatType = DoubleSeatType.DOUBLE_SEAT_RIGHT
)
private val SEAT_3 = createSeat(
    originalId = 3,
    auditoriumId = AUDITORIUM_1.id,
    auditoriumLayoutId = AUDITORIUM_LAYOUT_1.id,
    type = SeatType.REGULAR,
    doubleSeatType = DoubleSeatType.DOUBLE_SEAT_RIGHT
)
private val SEAT_4 = createSeat(
    originalId = 4,
    auditoriumId = AUDITORIUM_1.id,
    auditoriumLayoutId = AUDITORIUM_LAYOUT_1.id,
    type = SeatType.REGULAR,
    doubleSeatType = DoubleSeatType.DOUBLE_SEAT_RIGHT
)
private val SEAT_5 = createSeat(
    originalId = 5,
    auditoriumId = AUDITORIUM_2.id,
    auditoriumLayoutId = AUDITORIUM_LAYOUT_1.id,
    type = SeatType.REGULAR,
    doubleSeatType = DoubleSeatType.DOUBLE_SEAT_RIGHT
)
private val SEAT_6 = createSeat(
    originalId = 6,
    auditoriumId = AUDITORIUM_1.id,
    auditoriumLayoutId = AUDITORIUM_LAYOUT_2.id,
    type = SeatType.VIP,
    doubleSeatType = DoubleSeatType.DOUBLE_SEAT_LEFT
)
private val SEAT_7 = createSeat(
    originalId = 7,
    auditoriumId = AUDITORIUM_1.id,
    auditoriumLayoutId = AUDITORIUM_LAYOUT_2.id,
    type = SeatType.REGULAR
)
private val SEAT_8 = createSeat(
    originalId = 8,
    auditoriumId = AUDITORIUM_1.id,
    auditoriumLayoutId = AUDITORIUM_LAYOUT_2.id,
    type = SeatType.REGULAR
)
private val GROUP_RESERVATION_1 = createGroupReservation()
private val RESERVATION_1 = createReservation(
    screeningId = SCREENING_1.id,
    seatId = SEAT_1.id,
    state = ReservationState.UNAVAILABLE
)
private val RESERVATION_2 = createReservation(
    screeningId = SCREENING_1.id,
    seatId = SEAT_2.id,
    state = ReservationState.UNAVAILABLE,
    groupReservationId = GROUP_RESERVATION_1.id
)
private val RESERVATION_3_DELETED = createReservation(
    screeningId = SCREENING_1.id,
    seatId = SEAT_3.id,
    state = ReservationState.FREE
).apply { markDeleted() }
private val RESERVATION_4 = createReservation(
    screeningId = SCREENING_2.id,
    seatId = SEAT_5.id,
    state = ReservationState.FREE
)
private val RESERVATION_5 = createReservation(
    screeningId = SCREENING_1.id,
    seatId = SEAT_1.id,
    state = ReservationState.UNAVAILABLE
)
