package com.cleevio.cinemax.api.module.tms.controller

import Session
import SessionAttributes
import Sessions
import TmsScheduleDataXmlModel
import com.cleevio.cinemax.api.ControllerTest
import com.cleevio.cinemax.api.common.constant.ApiVersion
import com.cleevio.cinemax.api.common.service.model.ExportResultModel
import com.cleevio.cinemax.api.common.util.serializeToXml
import io.mockk.every
import io.mockk.verify
import org.hamcrest.CoreMatchers.containsString
import org.junitpioneer.jupiter.RetryingTest
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest
import org.springframework.http.HttpHeaders
import org.springframework.http.MediaType
import org.springframework.test.web.servlet.MockMvc
import org.springframework.test.web.servlet.get
import java.io.ByteArrayInputStream

@WebMvcTest(TmsController::class)
class TmsControllerTest @Autowired constructor(
    private val mvc: MockMvc,
) : ControllerTest() {

    @RetryingTest(5)
    fun `test getTmsData, should call service and return xml file`() {
        val tmsScheduleData = TmsScheduleDataXmlModel(
            sessions = Sessions(
                sessionList = listOf(
                    Session(
                        id = "142976",
                        screenIdentifier = "X",
                        start = "202502241900",
                        end = "202502242141",
                        featureDuration = 161,
                        title = "Čierny Panther: Navždy Wakanda IMAX 3D (SD)",
                        rating = "12",
                        seatsAvailable = 264,
                        seatsSold = 1,
                        sessionAttributes = SessionAttributes(
                            attributes = listOf(
                                "CinemArt SK s.r.o.",
                                "12",
                                "3D"
                            )
                        ),
                        complexIdentifier = "CINEMAX Bory"
                    )
                )
            )
        )

        val xmlData = tmsScheduleData.serializeToXml()
        val byteArray = xmlData.toByteArray()
        val inputStream = ByteArrayInputStream(byteArray)
        val exportResult = ExportResultModel(inputStream, byteArray.size.toLong())

        every { tmsService.getTmsScheduleData() } returns exportResult

        mvc.get(GET_TMS_SCHEDULE_PATH) {
            contentType = MediaType.APPLICATION_JSON
            header(HttpHeaders.ACCEPT, ApiVersion.VERSION_1_XML)
        }.andExpect {
            status { isOk() }
            header {
                string(HttpHeaders.CONTENT_TYPE, ApiVersion.VERSION_1_XML)
                string(HttpHeaders.CONTENT_DISPOSITION, containsString("attachment"))
                string(HttpHeaders.CONTENT_DISPOSITION, containsString("TmsSystemScheduleData"))
                longValue(HttpHeaders.CONTENT_LENGTH, byteArray.size.toLong())
            }
        }

        verify {
            tmsService.getTmsScheduleData()
        }
    }
}

private const val GET_TMS_SCHEDULE_PATH = "/tms-app/schedule"
