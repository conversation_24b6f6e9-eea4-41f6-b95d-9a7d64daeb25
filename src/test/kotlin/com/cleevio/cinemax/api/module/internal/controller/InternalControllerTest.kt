package com.cleevio.cinemax.api.module.internal.controller

import com.cleevio.cinemax.api.ControllerTest
import com.cleevio.cinemax.api.common.constant.ApiVersion
import io.mockk.Runs
import io.mockk.every
import io.mockk.just
import io.mockk.verify
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest
import org.springframework.http.HttpHeaders
import org.springframework.http.MediaType
import org.springframework.test.web.servlet.MockMvc
import org.springframework.test.web.servlet.post
import java.time.LocalDate

@WebMvcTest(InternalController::class)
class InternalControllerTest @Autowired constructor(
    private val mvc: MockMvc,
) : ControllerTest() {

    @Test
    fun `test synchronizeMssqlProductSales, should serialize and deserialize correctly`() {
        every { productSaleMssqlSynchronizationServiceMock.synchronizeAll(any(), any(), any()) } just Runs

        mvc.post("$BASE_PATH/synchronize-mssql-product-sales") {
            header(HttpHeaders.ACCEPT, ApiVersion.VERSION_1_JSON)
            contentType = MediaType.APPLICATION_JSON
            content = """
                {
                    "syncStartDate": "2022-07-09",
                    "syncEndDate": "2023-11-02",
                    "batchDaySpan": "7"
                }
            """.trimIndent()
        }.andExpect {
            status { isNoContent() }
        }

        verify(exactly = 1) {
            productSaleMssqlSynchronizationServiceMock.synchronizeAll(
                syncStartDate = LocalDate.parse("2022-07-09"),
                syncEndDate = LocalDate.parse("2023-11-02"),
                batchDaySpan = 7
            )
        }
    }

    @Test
    fun `test synchronizeMssqlTicketSales, should serialize and deserialize correctly`() {
        every { ticketSaleMssqlSynchronizationServiceMock.synchronizeAll(any(), any(), any()) } just Runs

        mvc.post("$BASE_PATH/synchronize-mssql-ticket-sales") {
            header(HttpHeaders.ACCEPT, ApiVersion.VERSION_1_JSON)
            contentType = MediaType.APPLICATION_JSON
            content = """
                {
                    "syncStartDate": "2022-07-09",
                    "syncEndDate": "2023-11-02",
                    "batchDaySpan": "7"
                }
            """.trimIndent()
        }.andExpect {
            status { isNoContent() }
        }

        verify(exactly = 1) {
            ticketSaleMssqlSynchronizationServiceMock.synchronizeAll(
                syncStartDate = LocalDate.parse("2022-07-09"),
                syncEndDate = LocalDate.parse("2023-11-02"),
                batchDaySpan = 7
            )
        }
    }
}

private const val BASE_PATH = "/internal"
