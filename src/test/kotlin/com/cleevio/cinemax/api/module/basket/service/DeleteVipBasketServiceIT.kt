package com.cleevio.cinemax.api.module.basket.service

import com.cleevio.cinemax.api.DatabaseCleanup
import com.cleevio.cinemax.api.IntegrationTest
import com.cleevio.cinemax.api.common.constant.PaymentType
import com.cleevio.cinemax.api.module.basket.constant.BasketState
import com.cleevio.cinemax.api.module.basket.event.ProductBasketItemDeletedFromBasketEvent
import com.cleevio.cinemax.api.module.basket.service.command.DeleteLatestVipBasketCommand
import com.cleevio.cinemax.api.module.basketitem.constant.BasketItemType
import com.cleevio.cinemax.api.module.basketitem.service.BasketItemRepository
import com.cleevio.cinemax.api.module.posconfiguration.constant.ProductMode
import com.cleevio.cinemax.api.module.product.constant.ProductType
import com.cleevio.cinemax.api.module.product.service.ProductRepository
import com.cleevio.cinemax.api.module.productcategory.constant.ProductCategoryType
import com.cleevio.cinemax.api.module.productcategory.service.ProductCategoryRepository
import com.cleevio.cinemax.api.module.table.constant.TableType
import com.cleevio.cinemax.api.module.table.service.TableRepository
import com.cleevio.cinemax.api.util.createBasket
import com.cleevio.cinemax.api.util.createBasketItem
import com.cleevio.cinemax.api.util.createProduct
import com.cleevio.cinemax.api.util.createProductCategory
import com.cleevio.cinemax.api.util.createTable
import com.cleevio.cinemax.api.util.toUUID
import io.kotest.matchers.shouldBe
import io.kotest.matchers.shouldNotBe
import io.mockk.Runs
import io.mockk.every
import io.mockk.just
import io.mockk.verify
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import java.math.BigDecimal
import java.util.UUID

class DeleteVipBasketServiceIT @Autowired constructor(
    private val underTest: DeleteVipBasketService,
    private val basketRepository: BasketRepository,
    private val basketItemRepository: BasketItemRepository,
    private val tableRepository: TableRepository,
    private val productCategoryRepository: ProductCategoryRepository,
    private val productRepository: ProductRepository,
) : IntegrationTest(DatabaseCleanup.AFTER_EACH) {

    @BeforeEach
    fun setUp() {
        // Create a VIP table
        val vipTable = tableRepository.save(
            createTable(
                id = 1.toUUID(),
                originalId = 1,
                title = "VIP Table 1",
                label = "VIP1",
                type = TableType.SEAT,
                productMode = ProductMode.VIP,
                paymentType = PaymentType.CASHLESS,
                ipAddress = "*************"
            )
        )

        // Create a product category
        val productCategory = productCategoryRepository.save(
            createProductCategory(
                originalId = 1,
                code = "01",
                title = "Drinks",
                type = ProductCategoryType.PRODUCT,
                order = 1,
                taxRate = 21,
                hexColorCode = "#FF0000"
            )
        )

        // Create a VIP product
        val vipProduct = productRepository.save(
            createProduct(
                id = 10.toUUID(),
                originalId = 10,
                code = "P10",
                productCategoryId = productCategory.id,
                title = "VIP Cola",
                type = ProductType.PRODUCT,
                price = BigDecimal.valueOf(5.99),
                active = true,
                soldInVip = true,
                tabletOrder = 1
            )
        )

        basketRepository.save(
            createBasket(
                id = 1.toUUID(),
                tableId = vipTable.id,
                totalPrice = BigDecimal.valueOf(5.99),
                state = BasketState.PAID,
                paymentType = PaymentType.CASHLESS
            )
        )

        val latestBasket = basketRepository.save(
            createBasket(
                id = 2.toUUID(),
                tableId = vipTable.id,
                totalPrice = BigDecimal.valueOf(11.98),
                state = BasketState.OPEN,
                paymentType = PaymentType.CASHLESS
            )
        )

        basketItemRepository.save(
            createBasketItem(
                basketId = latestBasket.id,
                productId = vipProduct.id,
                type = BasketItemType.PRODUCT,
                price = vipProduct.price
            )
        )

        every { applicationEventPublisherMock.publishEvent(any()) } just Runs
        every { applicationEventPublisherMock.publishEvent(any<ProductBasketItemDeletedFromBasketEvent>()) } just Runs
    }

    @Test
    fun `test deleteVipBasket - should delete the latest basket for the table`() {
        // When
        underTest.invoke(DeleteLatestVipBasketCommand(tableId = 1.toUUID()))

        // Then
        val deletedBasket = basketRepository.findById(2.toUUID()).get()
        deletedBasket.deletedAt shouldNotBe null
        deletedBasket.state shouldBe BasketState.DELETED

        val deletedBasketItem = basketItemRepository.findAll().first()
        deletedBasketItem.deletedAt shouldNotBe null

        val oldBasketAfterDelete = basketRepository.findByIdAndDeletedAtIsNull(1.toUUID())
        oldBasketAfterDelete!!.deletedAt shouldBe null
        oldBasketAfterDelete.state shouldBe BasketState.PAID
    }

    @Test
    fun `test deleteVipBasket - with non-existent table should do nothing`() {
        // When
        underTest.invoke(DeleteLatestVipBasketCommand(tableId = UUID.randomUUID()))

        // Then
        val latestBasketAfterDelete = basketRepository.findByIdAndDeletedAtIsNull(2.toUUID())
        latestBasketAfterDelete!!.deletedAt shouldBe null
        latestBasketAfterDelete.state shouldBe BasketState.OPEN

        val oldBasketAfterDelete = basketRepository.findByIdAndDeletedAtIsNull(1.toUUID())
        oldBasketAfterDelete!!.deletedAt shouldBe null
        oldBasketAfterDelete.state shouldBe BasketState.PAID

        // Verify no events were published
        verify(exactly = 0) { applicationEventPublisherMock.publishEvent(any()) }
    }
}
