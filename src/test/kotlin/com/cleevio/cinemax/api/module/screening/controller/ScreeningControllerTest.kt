package com.cleevio.cinemax.api.module.screening.controller

import com.cleevio.cinemax.api.ControllerTest
import com.cleevio.cinemax.api.common.constant.ApiVersion
import com.cleevio.cinemax.api.common.service.query.SearchQueryDeprecated
import com.cleevio.cinemax.api.common.util.jsonContent
import com.cleevio.cinemax.api.module.pricecategoryitem.constant.PriceCategoryItemNumber
import com.cleevio.cinemax.api.module.reservation.constant.ReservationState
import com.cleevio.cinemax.api.module.screening.constant.MovieTechnology
import com.cleevio.cinemax.api.module.screening.service.query.ScreeningFilter
import com.cleevio.cinemax.api.module.seat.constant.DoubleSeatType
import com.cleevio.cinemax.api.module.seat.constant.SeatType
import com.cleevio.cinemax.api.truncatedAndFormatted
import com.cleevio.cinemax.api.util.createAuditorium
import com.cleevio.cinemax.api.util.createAuditoriumLayout
import com.cleevio.cinemax.api.util.createGroupReservation
import com.cleevio.cinemax.api.util.createMovie
import com.cleevio.cinemax.api.util.createPriceCategory
import com.cleevio.cinemax.api.util.createPriceCategoryItem
import com.cleevio.cinemax.api.util.createReservation
import com.cleevio.cinemax.api.util.createScreening
import com.cleevio.cinemax.api.util.createSeat
import com.cleevio.cinemax.api.util.mapToAuditoriumSearchResponse
import com.cleevio.cinemax.api.util.mapToMovieSearchResponse
import com.cleevio.cinemax.api.util.mapToPriceCategoryItemResponse
import com.cleevio.cinemax.api.util.mapToPriceCategorySearchResponse
import com.cleevio.cinemax.api.util.mapToReservationSearchResponse
import com.cleevio.cinemax.api.util.mapToScreeningResponse
import com.cleevio.cinemax.api.util.mapToSeatSearchResponse
import com.cleevio.cinemax.api.util.minutesToLocalTime
import com.cleevio.cinemax.api.util.mockAccessToken
import com.cleevio.cinemax.psql.tables.ScreeningColumnNames
import io.mockk.every
import io.mockk.verifySequence
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest
import org.springframework.data.domain.PageImpl
import org.springframework.data.domain.PageRequest
import org.springframework.data.domain.Sort
import org.springframework.http.HttpHeaders
import org.springframework.http.MediaType
import org.springframework.test.web.servlet.MockMvc
import org.springframework.test.web.servlet.post
import java.math.BigDecimal
import java.time.LocalDate
import java.time.format.DateTimeFormatter
import java.time.temporal.ChronoUnit
import java.util.UUID

@WebMvcTest(ScreeningController::class)
class ScreeningControllerTest @Autowired constructor(
    private val mvc: MockMvc,
) : ControllerTest() {

    @Test
    fun `searchScreenings, missing accept header, should fall back to latest version content type`() {
        every { screeningJooqFinderService.search(any()) } returns PageImpl(listOf(SCREENING))
        every { screeningSearchResponseMapper.mapList(any()) } returns listOf(SCREENING_RESPONSE)

        mvc.post(SEARCH_SCREENINGS_PATH) {
            header(HttpHeaders.ACCEPT, ApiVersion.VERSION_1_JSON)
            header(HttpHeaders.AUTHORIZATION, mockAccessToken())
            contentType = MediaType.APPLICATION_JSON
            content = """
                {
                    "date": "$SEARCH_DATE"
                }
            """.trimIndent()
        }.andExpect {
            content { contentType(ApiVersion.VERSION_1_JSON) }
            status { isOk() }
        }

        verifySequence {
            screeningJooqFinderService.search(any())
            screeningSearchResponseMapper.mapList(any())
        }
    }

    @Test
    fun `searchScreenings, missing content type, should return 415`() {
        mvc.post(SEARCH_SCREENINGS_PATH) {
            header(HttpHeaders.ACCEPT, ApiVersion.VERSION_1_JSON)
            header(HttpHeaders.AUTHORIZATION, mockAccessToken())
            content = """{}"""
        }.andExpect {
            status { isUnsupportedMediaType() }
        }
    }

    @Test
    fun `searchScreenings, invalid content type, should return 415`() {
        mvc.post(SEARCH_SCREENINGS_PATH) {
            header(HttpHeaders.ACCEPT, ApiVersion.VERSION_1_JSON)
            header(HttpHeaders.AUTHORIZATION, mockAccessToken())
            contentType = MediaType.APPLICATION_PDF
            content = """{}"""
        }.andExpect {
            status { isUnsupportedMediaType() }
        }
    }

    @Test
    fun `searchScreenings, should serialize and deserialize correctly`() {
        every { screeningJooqFinderService.search(any()) } returns PageImpl(listOf(SCREENING))
        every { screeningSearchResponseMapper.mapList(any()) } returns listOf(SCREENING_RESPONSE)

        mvc.post(SEARCH_SCREENINGS_PATH) {
            contentType = MediaType.APPLICATION_JSON
            header(HttpHeaders.ACCEPT, ApiVersion.VERSION_1_JSON)
            header(HttpHeaders.AUTHORIZATION, mockAccessToken())
            content = """
                {
                    "date": "$SEARCH_DATE"
                }
            """.trimIndent()
        }.andExpect {
            status { isOk() }
            content { contentType(ApiVersion.VERSION_1_JSON) }
            jsonContent(
                """
                {
                    "content": [
                        {
                            "id": "${SCREENING.id}",
                            "date": "${SCREENING.date}",
                            "time": "${SCREENING.time.format(DateTimeFormatter.ISO_TIME)}",
                            "saleTimeLimit": "${minutesToLocalTime(SCREENING.saleTimeLimit).format(DateTimeFormatter.ISO_TIME)}",
                            "saleTimeLimitWithDate": "${SCREENING.getScreeningTime().plus(SCREENING.saleTimeLimit.toLong(), ChronoUnit.MINUTES).truncatedAndFormatted()}",
                            "movie": {
                                "id": "${MOVIE.id}",
                                "title": "${MOVIE.title}",
                                "releaseYear": ${MOVIE.releaseYear},
                                "duration": ${MOVIE.duration},
                                "rating": "${MOVIE.parsedRating}",
                                "format": "${MOVIE.parsedFormat}",
                                "technology": "${MOVIE.parsedTechnology}",
                                "language": "${MOVIE.parsedLanguage}"
                            },
                            "priceCategory": {
                              "id": "${PRICE_CATEGORY.id}",
                              "title": "${PRICE_CATEGORY.title}",
                              "items": [
                                {
                                  "id": "${PRICE_CATEGORY_ITEM.id}",
                                  "number": "${PRICE_CATEGORY_ITEM.number}",
                                  "title": "${PRICE_CATEGORY_ITEM.title}",
                                  "price": ${PRICE_CATEGORY_ITEM.price}
                                }
                              ]
                            },
                            "auditorium": {
                                "id": "${AUDITORIUM.id}",
                                "title": "${AUDITORIUM.title}",
                                "code": "${AUDITORIUM.code}",
                                "seats": [
                                    {
                                        "id": "${SEAT_1.id}",
                                        "type": "${SEAT_1.type}",
                                        "doubleSeatType": "${SEAT_1.doubleSeatType}",
                                        "row": "${SEAT_1.row}",
                                        "number": "${SEAT_1.number}",
                                        "positionLeft": ${SEAT_1.positionLeft},
                                        "positionTop": ${SEAT_1.positionTop},
                                        "reservation": {
                                            "state": "${RESERVATION_1.state}",
                                            "groupReservationId": null
                                        }
                                    },
                                    {
                                        "id": "${SEAT_2.id}",
                                        "type": "${SEAT_2.type}",
                                        "doubleSeatType": null,
                                        "row": "${SEAT_2.row}",
                                        "number": "${SEAT_2.number}",
                                        "positionLeft": ${SEAT_2.positionLeft},
                                        "positionTop": ${SEAT_2.positionTop},
                                        "reservation": {
                                            "state": "${RESERVATION_2.state}",
                                            "groupReservationId": "${RESERVATION_2.groupReservationId}"
                                        }
                                    }
                                ]
                            }
                        }
                    ],
                    "totalElements": 1,
                    "totalPages": 1
                }
              """
            )
        }

        verifySequence {
            screeningJooqFinderService.search(
                SearchQueryDeprecated(
                    pageable = PageRequest.of(0, 2000, Sort.by(ScreeningColumnNames.TIME).ascending()),
                    filter = ScreeningFilter(SEARCH_DATE)
                )
            )
            screeningSearchResponseMapper.mapList(listOf(SCREENING))
        }
    }
}

private const val SEARCH_SCREENINGS_PATH = "/pos-app/screenings/search"

private val SEARCH_DATE = LocalDate.of(2023, 8, 1)
private val DISTRIBUTOR_1_ID = UUID.randomUUID()
private val MOVIE = createMovie(parsedTechnology = MovieTechnology.IMAX, distributorId = DISTRIBUTOR_1_ID)
private val AUDITORIUM = createAuditorium()
private val AUDITORIUM_LAYOUT = createAuditoriumLayout(originalId = 1, auditoriumId = AUDITORIUM.id, code = "01")
private val SEAT_1 = createSeat(
    originalId = 1,
    auditoriumId = AUDITORIUM.id,
    auditoriumLayoutId = AUDITORIUM_LAYOUT.id,
    doubleSeatType = DoubleSeatType.DOUBLE_SEAT_RIGHT,
    number = "1"
)
private val SEAT_2 = createSeat(
    originalId = 2,
    auditoriumId = AUDITORIUM.id,
    auditoriumLayoutId = AUDITORIUM_LAYOUT.id,
    type = SeatType.REGULAR,
    number = "2"
)
private val SCREENING = createScreening(
    auditoriumId = AUDITORIUM.id,
    movieId = MOVIE.id
)
private val GROUP_RESERVATION_1 = createGroupReservation()
private val RESERVATION_1 = createReservation(
    screeningId = SCREENING.id,
    seatId = SEAT_1.id
)
private val RESERVATION_2 = createReservation(
    screeningId = SCREENING.id,
    seatId = SEAT_2.id,
    state = ReservationState.GROUP_RESERVED,
    groupReservationId = GROUP_RESERVATION_1.id
)
private val SEAT_RESPONSE_1 = mapToSeatSearchResponse(
    seat = SEAT_1,
    reservationResponse = mapToReservationSearchResponse(RESERVATION_1)
)
private val SEAT_RESPONSE_2 = mapToSeatSearchResponse(
    seat = SEAT_2,
    reservationResponse = mapToReservationSearchResponse(RESERVATION_2)
)
private val PRICE_CATEGORY = createPriceCategory(
    originalId = 1,
    title = "ARTMAX PO 17",
    active = true
)
private val PRICE_CATEGORY_ITEM = createPriceCategoryItem(
    priceCategoryId = PRICE_CATEGORY.id,
    number = PriceCategoryItemNumber.PRICE_1,
    title = "Dospely",
    price = BigDecimal.valueOf(9.5)
)
private val PRICE_CATEGORY_ITEM_RESPONSE = mapToPriceCategoryItemResponse(PRICE_CATEGORY_ITEM)
private val SCREENING_RESPONSE = mapToScreeningResponse(
    screening = SCREENING,
    movieResponse = mapToMovieSearchResponse(MOVIE),
    priceCategoryResponse = mapToPriceCategorySearchResponse(
        priceCategory = PRICE_CATEGORY,
        items = listOf(PRICE_CATEGORY_ITEM_RESPONSE)
    ),
    auditoriumResponse = mapToAuditoriumSearchResponse(
        auditorium = AUDITORIUM,
        seatResponses = listOf(SEAT_RESPONSE_1, SEAT_RESPONSE_2)
    )
)
