package com.cleevio.cinemax.api.module.product.event.listener

import com.cleevio.cinemax.api.common.integration.pubsub.dto.MessagePayload
import com.cleevio.cinemax.api.common.service.PublisherService
import com.cleevio.cinemax.api.module.product.constant.ProductType
import com.cleevio.cinemax.api.module.product.event.AdminProductCreatedOrUpdatedEvent
import com.cleevio.cinemax.api.module.product.event.AdminProductDeletedEvent
import io.mockk.every
import io.mockk.just
import io.mockk.mockk
import io.mockk.runs
import io.mockk.verify
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test

class ProductMessagingEventListenerTest {

    private val publisherService: PublisherService = mockk<PublisherService>()
    private val underTest = ProductMessagingEventListener(publisherService)

    @BeforeEach
    fun setUp() {
        every { publisherService.publish(any()) } just runs
    }

    @Test
    fun `test listenToAdminProductCreatedOrUpdatedEvent - should publish message`() {
        val event = AdminProductCreatedOrUpdatedEvent(
            title = "Product 1",
            code = "1234",
            active = true,
            type = ProductType.PRODUCT,
            productCategoryCode = "02",
            price = 2.toBigDecimal(),
            soldInBuffet = true,
            soldInCafe = true,
            soldInVip = true,
            productComposition = emptyList()
        )

        underTest.listenToAdminProductCreatedOrUpdatedEvent(event)
        verify(exactly = 1) { publisherService.publish(any<MessagePayload>()) }
    }

    @Test
    fun `test listenToAdminProductDeletedEvent - should publish message`() {
        val event = AdminProductDeletedEvent(code = "1234")

        underTest.listenToAdminProductDeletedEvent(event)

        verify(exactly = 1) { publisherService.publish(any<MessagePayload>()) }
    }
}
