package com.cleevio.cinemax.api.module.basketitem.service

import com.cleevio.cinemax.api.DatabaseCleanup
import com.cleevio.cinemax.api.IntegrationTest
import com.cleevio.cinemax.api.common.constant.PaymentType
import com.cleevio.cinemax.api.common.util.isEqualTo
import com.cleevio.cinemax.api.module.auditorium.entity.Auditorium
import com.cleevio.cinemax.api.module.auditorium.service.AuditoriumRepository
import com.cleevio.cinemax.api.module.auditoriumlayout.entity.AuditoriumLayout
import com.cleevio.cinemax.api.module.auditoriumlayout.service.AuditoriumLayoutRepository
import com.cleevio.cinemax.api.module.basket.constant.BasketState
import com.cleevio.cinemax.api.module.basket.service.BasketRepository
import com.cleevio.cinemax.api.module.basketitem.constant.BasketItemType
import com.cleevio.cinemax.api.module.basketitem.entity.BasketItem
import com.cleevio.cinemax.api.module.basketitem.service.query.AdminBranchSalesOverviewQuery
import com.cleevio.cinemax.api.module.branch.entity.Branch
import com.cleevio.cinemax.api.module.branch.service.BranchRepository
import com.cleevio.cinemax.api.module.distributor.service.DistributorRepository
import com.cleevio.cinemax.api.module.movie.service.MovieRepository
import com.cleevio.cinemax.api.module.pricecategory.service.PriceCategoryRepository
import com.cleevio.cinemax.api.module.product.service.ProductRepository
import com.cleevio.cinemax.api.module.productcategory.service.ProductCategoryRepository
import com.cleevio.cinemax.api.module.reservation.constant.ReservationState
import com.cleevio.cinemax.api.module.reservation.service.ReservationRepository
import com.cleevio.cinemax.api.module.screening.constant.ScreeningState
import com.cleevio.cinemax.api.module.screening.entity.Screening
import com.cleevio.cinemax.api.module.screening.service.ScreeningRepository
import com.cleevio.cinemax.api.module.seat.constant.SeatType
import com.cleevio.cinemax.api.module.seat.service.SeatRepository
import com.cleevio.cinemax.api.module.ticket.service.TicketRepository
import com.cleevio.cinemax.api.module.ticketprice.service.TicketPriceRepository
import com.cleevio.cinemax.api.util.createAuditorium
import com.cleevio.cinemax.api.util.createAuditoriumLayout
import com.cleevio.cinemax.api.util.createBasket
import com.cleevio.cinemax.api.util.createBasketItem
import com.cleevio.cinemax.api.util.createBranch
import com.cleevio.cinemax.api.util.createDistributor
import com.cleevio.cinemax.api.util.createMovie
import com.cleevio.cinemax.api.util.createPriceCategory
import com.cleevio.cinemax.api.util.createProduct
import com.cleevio.cinemax.api.util.createProductCategory
import com.cleevio.cinemax.api.util.createReservation
import com.cleevio.cinemax.api.util.createScreening
import com.cleevio.cinemax.api.util.createSeat
import com.cleevio.cinemax.api.util.createTicket
import com.cleevio.cinemax.api.util.createTicketPrice
import com.cleevio.cinemax.api.util.toUUID
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.LocalTime
import java.util.UUID
import kotlin.test.assertEquals
import kotlin.test.assertNull
import kotlin.test.assertTrue

class AdminBranchSalesOverviewQueryServiceIT @Autowired constructor(
    private val underTest: AdminBranchSalesOverviewQueryService,
    private val branchRepository: BranchRepository,
    private val basketRepository: BasketRepository,
    private val basketItemRepository: BasketItemRepository,
    private val screeningRepository: ScreeningRepository,
    private val auditoriumRepository: AuditoriumRepository,
    private val auditoriumLayoutRepository: AuditoriumLayoutRepository,
    private val seatRepository: SeatRepository,
    private val distributorRepository: DistributorRepository,
    private val movieRepository: MovieRepository,
    private val reservationRepository: ReservationRepository,
    private val ticketRepository: TicketRepository,
    private val ticketPriceRepository: TicketPriceRepository,
    private val priceCategoryRepository: PriceCategoryRepository,
    private val productCategoryRepository: ProductCategoryRepository,
    private val productRepository: ProductRepository,
    private val basketItemService: BasketItemService,
) : IntegrationTest(DatabaseCleanup.AFTER_EACH) {

    @BeforeEach
    fun setUp() {
        integrationTestClock.setTo(LocalDateTime.of(2025, 1, 1, 0, 0, 0))
        branchRepository.saveAll(setOf(BRANCH_BRATISLAVA, BRANCH_KOSICE))
        auditoriumRepository.saveAll(setOf(AUDITORIUM_BRATISLAVA, AUDITORIUM_KOSICE))
        auditoriumLayoutRepository.saveAll(setOf(AUDITORIUM_LAYOUT_BRATISLAVA, AUDITORIUM_LAYOUT_KOSICE))
        distributorRepository.save(DISTRIBUTOR)
        movieRepository.save(MOVIE_1)
        seatRepository.saveAll(SEATS)
        priceCategoryRepository.save(PRICE_CATEGORY)
        productCategoryRepository.save(PRODUCT_CATEGORY)
        productRepository.save(PRODUCT)

        // create test data for each month/year combination
        createMonthlyData(BRATISLAVA_SETUP, 10000, 2024, 1, 11, 2, 3, 1)
        createMonthlyData(BRATISLAVA_SETUP, 10000, 2024, 6, 10, 3, 5, 2)
        createMonthlyData(BRATISLAVA_SETUP, 10000, 2024, 12, 8, 4, 6, 3)
        createMonthlyData(BRATISLAVA_SETUP, 10000, 2024, 8, 4, 0, 0, 0)

        createMonthlyData(BRATISLAVA_SETUP, 10000, 2023, 1, 8, 4, 5, 5)
        createMonthlyData(BRATISLAVA_SETUP, 10000, 2023, 6, 9, 3, 2, 1)
        createMonthlyData(BRATISLAVA_SETUP, 10000, 2023, 12, 10, 1, 3, 2)
        createMonthlyData(BRATISLAVA_SETUP, 10000, 2023, 3, 5, 0, 0, 0)

        createMonthlyData(KOSICE_SETUP, 20000, 2024, 3, 5, 2, 7, 2)
        createMonthlyData(KOSICE_SETUP, 20000, 2024, 7, 3, 3, 9, 3)
        createMonthlyData(KOSICE_SETUP, 20000, 2024, 11, 2, 4, 10, 4)
        createMonthlyData(KOSICE_SETUP, 20000, 2024, 1, 6, 0, 0, 0)

        createMonthlyData(KOSICE_SETUP, 20000, 2023, 3, 6, 3, 1, 1)
        createMonthlyData(KOSICE_SETUP, 20000, 2023, 7, 4, 2, 5, 2)
        createMonthlyData(KOSICE_SETUP, 20000, 2023, 11, 8, 1, 7, 3)
        createMonthlyData(KOSICE_SETUP, 20000, 2023, 5, 3, 0, 0, 0)
    }

    @Test
    fun `test invoke - data present for branch - should return correct data`() {
        basketItemService.refreshBranchSalesOverview()

        val query = AdminBranchSalesOverviewQuery(
            branchIds = null,
            selectedYear = 2024
        )

        val result = underTest(query)
        assertEquals(3, result.branches.size)

        // Verify summary branch data
        val summaryBranch = result.branches.first()
        with(summaryBranch) {
            assertNull(code)
            assertEquals("Celkom", title) // Slovak translation

            with(screenings!!) {
                assertTrue { 49.toBigDecimal() isEqualTo selectedYearTotal } // 33 + 16
                assertTrue { 53.toBigDecimal() isEqualTo previousYearTotal } // 32 + 21
                assertTrue { (-4).toBigDecimal() isEqualTo change }
                assertTrue { (-7.55).toBigDecimal() isEqualTo changePercentage }

                assertEquals(7, months.size)
                assertTrue(months.any { it.monthStartDate == LocalDate.of(2024, 1, 1) && it.value isEqualTo 17.toBigDecimal() }) // 11 + 6
                assertTrue(months.any { it.monthStartDate == LocalDate.of(2024, 3, 1) && it.value isEqualTo 5.toBigDecimal() }) // 0 + 5
                assertTrue(months.any { it.monthStartDate == LocalDate.of(2024, 6, 1) && it.value isEqualTo 10.toBigDecimal() }) // 10 + 0
                assertTrue(months.any { it.monthStartDate == LocalDate.of(2024, 7, 1) && it.value isEqualTo 3.toBigDecimal() }) // 0 + 3
                assertTrue(months.any { it.monthStartDate == LocalDate.of(2024, 8, 1) && it.value isEqualTo 4.toBigDecimal() }) // 4 + 0
                assertTrue(months.any { it.monthStartDate == LocalDate.of(2024, 11, 1) && it.value isEqualTo 2.toBigDecimal() }) // 0 + 2
                assertTrue(months.any { it.monthStartDate == LocalDate.of(2024, 12, 1) && it.value isEqualTo 8.toBigDecimal() }) // 8 + 0
            }

            with(tickets!!) {
                assertTrue { 639.toBigDecimal() isEqualTo selectedYearTotal } // 408 + 231
                assertTrue { 358.toBigDecimal() isEqualTo previousYearTotal } // 244 + 114
                assertTrue { 281.toBigDecimal() isEqualTo change }
                assertTrue { 78.49.toBigDecimal() isEqualTo changePercentage }

                assertEquals(7, months.size)
                assertTrue(months.any { it.monthStartDate == LocalDate.of(2024, 1, 1) && it.value isEqualTo 66.toBigDecimal() }) // 66 + 0
                assertTrue(months.any { it.monthStartDate == LocalDate.of(2024, 3, 1) && it.value isEqualTo 70.toBigDecimal() }) // 0 + 70
                assertTrue(months.any { it.monthStartDate == LocalDate.of(2024, 6, 1) && it.value isEqualTo 150.toBigDecimal() }) // 150 + 0
                assertTrue(months.any { it.monthStartDate == LocalDate.of(2024, 7, 1) && it.value isEqualTo 81.toBigDecimal() }) // 0 + 81
                assertTrue(months.any { it.monthStartDate == LocalDate.of(2024, 8, 1) && it.value isEqualTo 0.toBigDecimal() }) // 0 + 0
                assertTrue(months.any { it.monthStartDate == LocalDate.of(2024, 11, 1) && it.value isEqualTo 80.toBigDecimal() }) // 0 + 80
                assertTrue(months.any { it.monthStartDate == LocalDate.of(2024, 12, 1) && it.value isEqualTo 192.toBigDecimal() }) // 192 + 0
            }

            with(ticketsGrossSales!!) {
                assertTrue { 6390.toBigDecimal() isEqualTo selectedYearTotal } // 4080 + 2310
                assertTrue { 3580.toBigDecimal() isEqualTo previousYearTotal } // 2440 + 1140
                assertTrue { 2810.toBigDecimal() isEqualTo change }
                assertTrue { 78.49.toBigDecimal() isEqualTo changePercentage }

                assertEquals(7, months.size)
                assertTrue(months.any { it.monthStartDate == LocalDate.of(2024, 1, 1) && it.value isEqualTo 660.toBigDecimal() }) // 660 + 0
                assertTrue(months.any { it.monthStartDate == LocalDate.of(2024, 3, 1) && it.value isEqualTo 700.toBigDecimal() }) // 0 + 700
                assertTrue(months.any { it.monthStartDate == LocalDate.of(2024, 6, 1) && it.value isEqualTo 1500.toBigDecimal() }) // 1500 + 0
                assertTrue(months.any { it.monthStartDate == LocalDate.of(2024, 7, 1) && it.value isEqualTo 810.toBigDecimal() }) // 0 + 810
                assertTrue(months.any { it.monthStartDate == LocalDate.of(2024, 8, 1) && it.value isEqualTo 0.toBigDecimal() }) // 0 + 0
                assertTrue(months.any { it.monthStartDate == LocalDate.of(2024, 11, 1) && it.value isEqualTo 800.toBigDecimal() }) // 0 + 800
                assertTrue(months.any { it.monthStartDate == LocalDate.of(2024, 12, 1) && it.value isEqualTo 1920.toBigDecimal() }) // 1920 + 0
            }

            with(productsGrossSales!!) {
                assertTrue { 730.toBigDecimal() isEqualTo selectedYearTotal } // 470 + 260
                assertTrue { 810.toBigDecimal() isEqualTo previousYearTotal } // 690 + 120
                assertTrue { (-80).toBigDecimal() isEqualTo change }
                assertTrue { (-9.88).toBigDecimal() isEqualTo changePercentage }

                assertEquals(7, months.size)
                assertTrue(months.any { it.monthStartDate == LocalDate.of(2024, 1, 1) && it.value isEqualTo 0.toBigDecimal() }) // 0 + 0
                assertTrue(months.any { it.monthStartDate == LocalDate.of(2024, 3, 1) && it.value isEqualTo 50.toBigDecimal() }) // 0 + 50
                assertTrue(months.any { it.monthStartDate == LocalDate.of(2024, 6, 1) && it.value isEqualTo 150.toBigDecimal() }) // 150 + 0
                assertTrue(months.any { it.monthStartDate == LocalDate.of(2024, 7, 1) && it.value isEqualTo 90.toBigDecimal() }) // 0 + 90
                assertTrue(months.any { it.monthStartDate == LocalDate.of(2024, 8, 1) && it.value isEqualTo 0.toBigDecimal() }) // 0 + 0
                assertTrue(months.any { it.monthStartDate == LocalDate.of(2024, 11, 1) && it.value isEqualTo 120.toBigDecimal() }) // 0 + 120
                assertTrue(months.any { it.monthStartDate == LocalDate.of(2024, 12, 1) && it.value isEqualTo 320.toBigDecimal() }) // 320 + 0
            }

            with(productsVipGrossSales!!) {
                assertTrue { 555.toBigDecimal() isEqualTo selectedYearTotal } // 420 + 135
                assertTrue { 515.toBigDecimal() isEqualTo previousYearTotal } // 345 + 170
                assertTrue { 40.toBigDecimal() isEqualTo change }
                assertTrue { 7.77.toBigDecimal() isEqualTo changePercentage }

                assertEquals(7, months.size)
                assertTrue(months.any { it.monthStartDate == LocalDate.of(2024, 1, 1) && it.value isEqualTo 110.toBigDecimal() }) // 110 + 0
                assertTrue(months.any { it.monthStartDate == LocalDate.of(2024, 3, 1) && it.value isEqualTo 50.toBigDecimal() }) // 0 + 50
                assertTrue(months.any { it.monthStartDate == LocalDate.of(2024, 6, 1) && it.value isEqualTo 150.toBigDecimal() }) // 150 + 0
                assertTrue(months.any { it.monthStartDate == LocalDate.of(2024, 7, 1) && it.value isEqualTo 45.toBigDecimal() }) // 0 + 45
                assertTrue(months.any { it.monthStartDate == LocalDate.of(2024, 8, 1) && it.value isEqualTo 0.toBigDecimal() }) // 0 + 0
                assertTrue(months.any { it.monthStartDate == LocalDate.of(2024, 11, 1) && it.value isEqualTo 40.toBigDecimal() }) // 0 + 40
                assertTrue(months.any { it.monthStartDate == LocalDate.of(2024, 12, 1) && it.value isEqualTo 160.toBigDecimal() }) // 160 + 0
            }

            with(totalGrossSales!!) {
                assertTrue { 7675.toBigDecimal() isEqualTo selectedYearTotal } // 4970 + 2705
                assertTrue { 4905.toBigDecimal() isEqualTo previousYearTotal } // 3475 + 1430
                assertTrue { 2770.toBigDecimal() isEqualTo change }
                assertTrue { 56.47.toBigDecimal() isEqualTo changePercentage }

                assertEquals(7, months.size)
                assertTrue(months.any { it.monthStartDate == LocalDate.of(2024, 1, 1) && it.value isEqualTo 770.toBigDecimal() }) // 770 + 0
                assertTrue(months.any { it.monthStartDate == LocalDate.of(2024, 3, 1) && it.value isEqualTo 800.toBigDecimal() }) // 0 + 800
                assertTrue(months.any { it.monthStartDate == LocalDate.of(2024, 6, 1) && it.value isEqualTo 1800.toBigDecimal() }) // 1800 + 0
                assertTrue(months.any { it.monthStartDate == LocalDate.of(2024, 7, 1) && it.value isEqualTo 945.toBigDecimal() }) // 0 + 945
                assertTrue(months.any { it.monthStartDate == LocalDate.of(2024, 8, 1) && it.value isEqualTo 0.toBigDecimal() }) // 0 + 0
                assertTrue(months.any { it.monthStartDate == LocalDate.of(2024, 11, 1) && it.value isEqualTo 960.toBigDecimal() }) // 0 + 960
                assertTrue(months.any { it.monthStartDate == LocalDate.of(2024, 12, 1) && it.value isEqualTo 2400.toBigDecimal() }) // 2400 + 0
            }
        }

        // Verify Bratislava branch data
        with(result.branches[1]) {
            assertEquals("BB01", code)
            assertEquals("Bratislava Bory Mall", title)

            with(screenings!!) {
                assertTrue { 33.toBigDecimal() isEqualTo selectedYearTotal } // 11 + 10 + 4 + 8
                assertTrue { 32.toBigDecimal() isEqualTo previousYearTotal } // 8 + 9 + 10 + 5
                assertTrue { 1.toBigDecimal() isEqualTo change }
                assertTrue { 3.13.toBigDecimal() isEqualTo changePercentage }
                assertEquals(4, months.size)
                assertTrue(months.any { it.monthStartDate == LocalDate.of(2024, 1, 1) && it.value isEqualTo 11.toBigDecimal() })
                assertTrue(months.any { it.monthStartDate == LocalDate.of(2024, 6, 1) && it.value isEqualTo 10.toBigDecimal() })
                assertTrue(months.any { it.monthStartDate == LocalDate.of(2024, 8, 1) && it.value isEqualTo 4.toBigDecimal() })
                assertTrue(months.any { it.monthStartDate == LocalDate.of(2024, 12, 1) && it.value isEqualTo 8.toBigDecimal() })
            }

            with(tickets!!) {
                assertTrue { 408.toBigDecimal() isEqualTo selectedYearTotal } // (11*2*3) + (10*3*5) + (4*0*0) + (8*4*6)
                assertTrue { 244.toBigDecimal() isEqualTo previousYearTotal } // (8*4*5) + (9*3*2) + (10*1*3) + (5*0*0)
                assertTrue { 164.toBigDecimal() isEqualTo change }
                assertTrue { 67.21.toBigDecimal() isEqualTo changePercentage }
                assertEquals(4, months.size)
                assertTrue(months.any { it.monthStartDate == LocalDate.of(2024, 1, 1) && it.value isEqualTo 66.toBigDecimal() }) // 11*2*3
                assertTrue(months.any { it.monthStartDate == LocalDate.of(2024, 6, 1) && it.value isEqualTo 150.toBigDecimal() }) // 10*3*5
                assertTrue(months.any { it.monthStartDate == LocalDate.of(2024, 8, 1) && it.value isEqualTo 0.toBigDecimal() }) // 4*0*0
                assertTrue(months.any { it.monthStartDate == LocalDate.of(2024, 12, 1) && it.value isEqualTo 192.toBigDecimal() }) // 8*4*6
            }

            with(ticketsGrossSales!!) {
                assertTrue { 4080.toBigDecimal() isEqualTo selectedYearTotal } // 408 * 10
                assertTrue { 2440.toBigDecimal() isEqualTo previousYearTotal } // 244 * 10
                assertTrue { 1640.toBigDecimal() isEqualTo change }
                assertTrue { 67.21.toBigDecimal() isEqualTo changePercentage }
                assertEquals(4, months.size)
                assertTrue(months.any { it.monthStartDate == LocalDate.of(2024, 1, 1) && it.value isEqualTo 660.toBigDecimal() }) // 66 * 10
                assertTrue(months.any { it.monthStartDate == LocalDate.of(2024, 6, 1) && it.value isEqualTo 1500.toBigDecimal() }) // 150 * 10
                assertTrue(months.any { it.monthStartDate == LocalDate.of(2024, 8, 1) && it.value isEqualTo 0.toBigDecimal() }) // 0 * 10
                assertTrue(months.any { it.monthStartDate == LocalDate.of(2024, 12, 1) && it.value isEqualTo 1920.toBigDecimal() }) // 192 * 10
            }

            with(productsGrossSales!!) {
                assertTrue { 470.toBigDecimal() isEqualTo selectedYearTotal } // (0 + 150 + 0 + 320)
                assertTrue { 690.toBigDecimal() isEqualTo previousYearTotal }
                assertTrue { (-220).toBigDecimal() isEqualTo change }
                assertTrue { (-31.88).toBigDecimal() isEqualTo changePercentage }
                assertEquals(4, months.size)
                assertTrue(months.any { it.monthStartDate == LocalDate.of(2024, 1, 1) && it.value isEqualTo 0.toBigDecimal() }) // no regular products
                assertTrue(months.any { it.monthStartDate == LocalDate.of(2024, 6, 1) && it.value isEqualTo 150.toBigDecimal() }) // 10*3*1*5
                assertTrue(months.any { it.monthStartDate == LocalDate.of(2024, 8, 1) && it.value isEqualTo 0.toBigDecimal() }) // no baskets
                assertTrue(months.any { it.monthStartDate == LocalDate.of(2024, 12, 1) && it.value isEqualTo 320.toBigDecimal() }) // 8*4*2*5
            }

            with(productsVipGrossSales!!) {
                assertTrue { 420.toBigDecimal() isEqualTo selectedYearTotal } // (110 + 150 + 0 + 160)
                assertTrue { 345.toBigDecimal() isEqualTo previousYearTotal } // (160 + 135 + 50 + 0)
                assertTrue { 75.toBigDecimal() isEqualTo change }
                assertTrue { 21.74.toBigDecimal() isEqualTo changePercentage }
                assertEquals(4, months.size)
                assertTrue(months.any { it.monthStartDate == LocalDate.of(2024, 1, 1) && it.value isEqualTo 110.toBigDecimal() }) // 11*2*1*5
                assertTrue(months.any { it.monthStartDate == LocalDate.of(2024, 6, 1) && it.value isEqualTo 150.toBigDecimal() }) // 10*3*1*5
                assertTrue(months.any { it.monthStartDate == LocalDate.of(2024, 8, 1) && it.value isEqualTo 0.toBigDecimal() }) // no baskets
                assertTrue(months.any { it.monthStartDate == LocalDate.of(2024, 12, 1) && it.value isEqualTo 160.toBigDecimal() }) // 8*4*1*5
            }

            with(totalGrossSales!!) {
                assertTrue { 4970.toBigDecimal() isEqualTo selectedYearTotal } // 4080 + 470 + 420
                assertTrue { 3475.toBigDecimal() isEqualTo previousYearTotal } // 2440 + 790 + 245
                assertTrue { 1495.toBigDecimal() isEqualTo change }
                assertTrue { 43.02.toBigDecimal() isEqualTo changePercentage }
                assertEquals(4, months.size)
                assertTrue(months.any { it.monthStartDate == LocalDate.of(2024, 1, 1) && it.value isEqualTo 770.toBigDecimal() }) // 660 + 0 + 110
                assertTrue(months.any { it.monthStartDate == LocalDate.of(2024, 6, 1) && it.value isEqualTo 1800.toBigDecimal() }) // 1500 + 150 + 150
                assertTrue(months.any { it.monthStartDate == LocalDate.of(2024, 8, 1) && it.value isEqualTo 0.toBigDecimal() }) // 0 + 0 + 0
                assertTrue(months.any { it.monthStartDate == LocalDate.of(2024, 12, 1) && it.value isEqualTo 2400.toBigDecimal() }) // 1920 + 320 + 160
            }
        }

        // Verify Košice branch data
        with(result.branches[2]) {
            assertEquals("KE01", code)
            assertEquals("Košice Optima", title)

            with(screenings!!) {
                assertTrue { 16.toBigDecimal() isEqualTo selectedYearTotal } // 5 + 3 + 2 + 6
                assertTrue { 21.toBigDecimal() isEqualTo previousYearTotal } // 6 + 4 + 8
                assertTrue { (-5).toBigDecimal() isEqualTo change }
                assertTrue { (-23.81).toBigDecimal() isEqualTo changePercentage }
                assertEquals(4, months.size)
                assertTrue(months.any { it.monthStartDate == LocalDate.of(2024, 1, 1) && it.value isEqualTo 6.toBigDecimal() })
                assertTrue(months.any { it.monthStartDate == LocalDate.of(2024, 3, 1) && it.value isEqualTo 5.toBigDecimal() })
                assertTrue(months.any { it.monthStartDate == LocalDate.of(2024, 7, 1) && it.value isEqualTo 3.toBigDecimal() })
                assertTrue(months.any { it.monthStartDate == LocalDate.of(2024, 11, 1) && it.value isEqualTo 2.toBigDecimal() })
            }

            with(tickets!!) {
                assertTrue { 231.toBigDecimal() isEqualTo selectedYearTotal } // (5*2*7) + (3*3*9) + (2*4*10) + (6*0*0)
                assertTrue { 114.toBigDecimal() isEqualTo previousYearTotal } // (6*3*1) + (4*2*5) + (8*1*7)
                assertTrue { 117.toBigDecimal() isEqualTo change }
                assertTrue { 102.63.toBigDecimal() isEqualTo changePercentage }
                assertEquals(4, months.size)
                assertTrue(months.any { it.monthStartDate == LocalDate.of(2024, 1, 1) && it.value isEqualTo 0.toBigDecimal() }) // 6*0*0
                assertTrue(months.any { it.monthStartDate == LocalDate.of(2024, 3, 1) && it.value isEqualTo 70.toBigDecimal() }) // 5*2*7
                assertTrue(months.any { it.monthStartDate == LocalDate.of(2024, 7, 1) && it.value isEqualTo 81.toBigDecimal() }) // 3*3*9
                assertTrue(months.any { it.monthStartDate == LocalDate.of(2024, 11, 1) && it.value isEqualTo 80.toBigDecimal() }) // 2*4*10
            }

            with(ticketsGrossSales!!) {
                assertTrue { 2310.toBigDecimal() isEqualTo selectedYearTotal } // 231 * 10
                assertTrue { 1140.toBigDecimal() isEqualTo previousYearTotal } // 114 * 10
                assertTrue { 1170.toBigDecimal() isEqualTo change }
                assertTrue { 102.63.toBigDecimal() isEqualTo changePercentage }
                assertEquals(4, months.size)
                assertTrue(months.any { it.monthStartDate == LocalDate.of(2024, 1, 1) && it.value isEqualTo 0.toBigDecimal() }) // 0 * 10
                assertTrue(months.any { it.monthStartDate == LocalDate.of(2024, 3, 1) && it.value isEqualTo 700.toBigDecimal() }) // 70 * 10
                assertTrue(months.any { it.monthStartDate == LocalDate.of(2024, 7, 1) && it.value isEqualTo 810.toBigDecimal() }) // 81 * 10
                assertTrue(months.any { it.monthStartDate == LocalDate.of(2024, 11, 1) && it.value isEqualTo 800.toBigDecimal() }) // 80 * 10
            }

            with(productsGrossSales!!) {
                assertTrue { 260.toBigDecimal() isEqualTo selectedYearTotal } // (0 + 50 + 90 + 120)
                assertTrue { 120.toBigDecimal() isEqualTo previousYearTotal } // (0 + 40 + 80 + 0)
                assertTrue { 140.toBigDecimal() isEqualTo change }
                assertTrue { 116.67.toBigDecimal() isEqualTo changePercentage }
                assertEquals(4, months.size)
                assertTrue(months.any { it.monthStartDate == LocalDate.of(2024, 1, 1) && it.value isEqualTo 0.toBigDecimal() }) // no baskets
                assertTrue(months.any { it.monthStartDate == LocalDate.of(2024, 3, 1) && it.value isEqualTo 50.toBigDecimal() }) // 5*2*1*5
                assertTrue(months.any { it.monthStartDate == LocalDate.of(2024, 7, 1) && it.value isEqualTo 90.toBigDecimal() }) // 3*3*2*5
                assertTrue(months.any { it.monthStartDate == LocalDate.of(2024, 11, 1) && it.value isEqualTo 120.toBigDecimal() }) // 2*4*3*5
            }

            with(productsVipGrossSales!!) {
                assertTrue { 135.toBigDecimal() isEqualTo selectedYearTotal } // (0 + 50 + 45 + 40)
                assertTrue { 170.toBigDecimal() isEqualTo previousYearTotal } // (90 + 40 + 40 + 0)
                assertTrue { (-35).toBigDecimal() isEqualTo change }
                assertTrue { (-20.59).toBigDecimal() isEqualTo changePercentage }
                assertEquals(4, months.size)
                assertTrue(months.any { it.monthStartDate == LocalDate.of(2024, 1, 1) && it.value isEqualTo 0.toBigDecimal() }) // no baskets
                assertTrue(months.any { it.monthStartDate == LocalDate.of(2024, 3, 1) && it.value isEqualTo 50.toBigDecimal() }) // 5*2*1*5
                assertTrue(months.any { it.monthStartDate == LocalDate.of(2024, 7, 1) && it.value isEqualTo 45.toBigDecimal() }) // 3*3*1*5
                assertTrue(months.any { it.monthStartDate == LocalDate.of(2024, 11, 1) && it.value isEqualTo 40.toBigDecimal() }) // 2*4*1*5
            }

            with(totalGrossSales!!) {
                assertTrue { 2705.toBigDecimal() isEqualTo selectedYearTotal } // 2310 + 260 + 135
                assertTrue { 1430.toBigDecimal() isEqualTo previousYearTotal } // 1140 + 260 + 30
                assertTrue { 1275.toBigDecimal() isEqualTo change }
                assertTrue { 89.16.toBigDecimal() isEqualTo changePercentage }
                assertEquals(4, months.size)
                assertTrue(months.any { it.monthStartDate == LocalDate.of(2024, 1, 1) && it.value isEqualTo 0.toBigDecimal() }) // 0 + 0 + 0
                assertTrue(months.any { it.monthStartDate == LocalDate.of(2024, 3, 1) && it.value isEqualTo 800.toBigDecimal() }) // 700 + 50 + 50
                assertTrue(months.any { it.monthStartDate == LocalDate.of(2024, 7, 1) && it.value isEqualTo 945.toBigDecimal() }) // 810 + 90 + 45
                assertTrue(months.any { it.monthStartDate == LocalDate.of(2024, 11, 1) && it.value isEqualTo 960.toBigDecimal() }) // 800 + 120 + 40
            }
        }
    }

    @Test
    fun `test invoke - no data for given branch - should return empty list`() {
        basketItemService.refreshBranchSalesOverview()
        val query = AdminBranchSalesOverviewQuery(
            branchIds = setOf(1.toUUID()),
            selectedYear = 2024
        )

        val result = underTest(query)

        assertTrue(result.branches.isEmpty())
    }

    @Test
    fun `test invoke - materialized view is not refreshed - should return empty list`() {
        val query = AdminBranchSalesOverviewQuery(
            branchIds = setOf(1.toUUID()),
            selectedYear = 2024
        )

        val result = underTest(query)

        assertTrue(result.branches.isEmpty())
    }

    private fun createMonthlyData(
        branchSetup: Triple<Branch, Auditorium, AuditoriumLayout>,
        branchIntBase: Int,
        year: Int,
        month: Int,
        screeningsCount: Int,
        basketsPerScreening: Int,
        ticketsPerBasket: Int,
        productsPerBasket: Int,
    ) {
        val (branch, auditorium, auditoriumLayout) = branchSetup
        val date = LocalDate.of(year, month, 1)
        val uniqueId = branchIntBase + year + month + branch.originalId + auditoriumLayout.originalId!! + auditorium.originalId!!

        // create screenings
        (1..screeningsCount).forEach { screeningIndex ->
            val screening = createScreening(
                originalId = "$uniqueId$screeningIndex".toInt(),
                auditoriumId = auditorium.id,
                auditoriumLayoutId = auditoriumLayout.id,
                priceCategoryId = PRICE_CATEGORY.id,
                movieId = MOVIE_1.id,
                date = date,
                time = LocalTime.NOON,
                state = ScreeningState.PUBLISHED
            ).also { screeningRepository.save(it) }

            // create baskets
            (0..<basketsPerScreening).forEach { basketIndex ->

                // create reservations and tickets
                val tickets = (0..<ticketsPerBasket).map { ticketIndex ->
                    val seat = SEATS.filter { it.auditoriumId == auditorium.id }[ticketIndex]
                    val reservation = createReservation(
                        originalId = "${screening.originalId!!}$basketIndex$ticketIndex".toInt(),
                        screeningId = screening.id,
                        seatId = seat.id,
                        state = ReservationState.UNAVAILABLE
                    ).also { reservationRepository.save(it) }

                    val ticketPrice = createTicketPrice(
                        screeningId = screening.id,
                        seatId = seat.id,
                        basePrice = 10.toBigDecimal(),
                        basePriceBeforeDiscount = 10.toBigDecimal(),
                        totalPrice = 10.toBigDecimal()
                    ).also { ticketPriceRepository.save(it) }

                    createTicket(
                        originalId = "${screening.originalId!!}$basketIndex$ticketIndex".toInt(),
                        screeningId = screening.id,
                        reservationId = reservation.id,
                        ticketPriceId = ticketPrice.id,
                        isGroupTicket = false,
                        isUsed = false,
                        includes3dGlasses = false
                    )
                }.also { ticketRepository.saveAll(it) }

                // create products
                val products = (0..<productsPerBasket).map { productIndex ->
                    createProduct(
                        originalId = "${screening.originalId!!}$basketIndex$productIndex".toInt(),
                        productCategoryId = PRODUCT_CATEGORY.id,
                        title = "Product_$productIndex",
                        price = (productIndex * 10).toBigDecimal(),
                        soldInBuffet = productIndex != productsPerBasket - 1,
                        soldInCafe = productIndex != productsPerBasket - 1,
                        soldInVip = productIndex == productsPerBasket - 1 // only last product in basket will be sold in VIP
                    )
                }.also { productRepository.saveAll(it) }

                // create basket and basket items
                val basket = createBasket(
                    state = BasketState.PAID,
                    paymentType = PaymentType.CASH,
                    paidAt = LocalDateTime.of(date, LocalTime.NOON)
                ).also { basketRepository.save(it) }

                tickets.map {
                    createBasketItem(
                        basketId = basket.id,
                        type = BasketItemType.TICKET,
                        ticketId = it.id,
                        price = 10.toBigDecimal(),
                        quantity = 1,
                        branchId = branch.id
                    )
                }.also { basketItemRepository.saveAll(it) }

                products.map {
                    createBasketItem(
                        basketId = basket.id,
                        type = BasketItemType.PRODUCT,
                        price = 5.toBigDecimal(),
                        productId = it.id,
                        quantity = 1,
                        branchId = branch.id
                    )
                }.also { basketItemRepository.saveAll(it) }

                // create cancelled and deleted basket items - should not count
                basketItemRepository.saveAll(
                    listOf(
                        createProductBasketItem(basket.id) { it.isCancelled = true },
                        createProductBasketItem(basket.id) { it.markDeleted() }
                    )
                )

                // create non-paid basket - should not count
                createBasket(
                    state = BasketState.PAYMENT_IN_PROGRESS,
                    paymentType = PaymentType.CASH,
                    paidAt = null
                )
                    .also { basketRepository.save(it) }
                    .let { basketItemRepository.save(createProductBasketItem(basket.id)) }
            }
        }
        // create unpublished, stopped, cancelled, in future screenings - should not count
        setOf<(Screening) -> Unit>(
            { it.state = ScreeningState.DRAFT },
            { it.cancelled = true },
            { it.stopped = true },
            { it.date = integrationTestClock.currentTime().toLocalDate().plusDays(1) }
        ).mapIndexed { index, entityModifier ->
            createScreening(
                originalId = 999999 + index,
                auditoriumId = auditorium.id,
                auditoriumLayoutId = auditoriumLayout.id,
                priceCategoryId = PRICE_CATEGORY.id,
                movieId = MOVIE_1.id,
                date = date,
                time = LocalTime.NOON,
                entityModifier = entityModifier
            )
        }
    }
}

private val BRANCH_BRATISLAVA = createBranch(
    originalId = 1,
    productSalesCode = 1,
    code = "BB01",
    name = "Bratislava Bory Mall",
    auditoriumOriginalCodePrefix = "5000"
)
private val BRANCH_KOSICE = createBranch(
    originalId = 2,
    productSalesCode = 2,
    code = "KE01",
    name = "Košice Optima",
    auditoriumOriginalCodePrefix = "6000"
)
private val AUDITORIUM_BRATISLAVA = createAuditorium(
    originalId = 1,
    branchId = BRANCH_BRATISLAVA.id
)
private val AUDITORIUM_KOSICE = createAuditorium(
    originalId = 2,
    branchId = BRANCH_KOSICE.id
)
private val AUDITORIUM_LAYOUT_BRATISLAVA = createAuditoriumLayout(
    originalId = 1,
    auditoriumId = AUDITORIUM_BRATISLAVA.id,
    code = "01"
)
private val AUDITORIUM_LAYOUT_KOSICE = createAuditoriumLayout(
    originalId = 2,
    auditoriumId = AUDITORIUM_KOSICE.id,
    code = "02"
)
private val DISTRIBUTOR = createDistributor(originalId = 1, title = "Distributor s.r.o.")
private val MOVIE_1 = createMovie(distributorId = DISTRIBUTOR.id)
private val BRATISLAVA_SETUP: Triple<Branch, Auditorium, AuditoriumLayout> = Triple(
    BRANCH_BRATISLAVA,
    AUDITORIUM_BRATISLAVA,
    AUDITORIUM_LAYOUT_BRATISLAVA
)
private val KOSICE_SETUP: Triple<Branch, Auditorium, AuditoriumLayout> = Triple(
    BRANCH_KOSICE,
    AUDITORIUM_KOSICE,
    AUDITORIUM_LAYOUT_KOSICE
)
private val SEATS = mapOf(
    AUDITORIUM_BRATISLAVA to AUDITORIUM_LAYOUT_BRATISLAVA,
    AUDITORIUM_KOSICE to AUDITORIUM_LAYOUT_KOSICE
).flatMap { (auditorium, layout) ->
    (1..50).map { seatNum ->
        createSeat(
            originalId = "${auditorium.originalId!!}$seatNum".toInt(),
            auditoriumId = auditorium.id,
            auditoriumLayoutId = layout.id,
            type = SeatType.REGULAR
        )
    }
}
private val PRICE_CATEGORY = createPriceCategory(originalId = 1)
private val PRODUCT_CATEGORY = createProductCategory(originalId = 1)
private val PRODUCT = createProduct(
    originalId = 999,
    productCategoryId = PRODUCT_CATEGORY.id,
    title = "Product_999",
    price = 1000.toBigDecimal()
)

private fun createProductBasketItem(
    basketId: UUID,
    entityModifier: (BasketItem) -> Unit = {},
): BasketItem = createBasketItem(
    basketId = basketId,
    type = BasketItemType.PRODUCT,
    price = 1000.toBigDecimal(),
    productId = PRODUCT.id,
    quantity = 1,
    entityModifier = entityModifier
)
