package com.cleevio.cinemax.api.module.ticket.service

import com.cleevio.cinemax.api.DatabaseCleanup
import com.cleevio.cinemax.api.INTEGRATION_TEST_DATE_TIME
import com.cleevio.cinemax.api.IntegrationTest
import com.cleevio.cinemax.api.common.constant.PaymentType
import com.cleevio.cinemax.api.common.util.mapToSet
import com.cleevio.cinemax.api.module.auditorium.service.AuditoriumService
import com.cleevio.cinemax.api.module.auditoriumlayout.service.AuditoriumLayoutRepository
import com.cleevio.cinemax.api.module.basket.constant.BasketState
import com.cleevio.cinemax.api.module.basket.service.BasketService
import com.cleevio.cinemax.api.module.basket.service.command.InitBasketCommand
import com.cleevio.cinemax.api.module.basket.service.command.MessagingCreateBasketCommand
import com.cleevio.cinemax.api.module.basketitem.constant.BasketItemType
import com.cleevio.cinemax.api.module.basketitem.service.BasketItemService
import com.cleevio.cinemax.api.module.basketitem.service.command.CreateBasketItemsFromGroupReservationCommand
import com.cleevio.cinemax.api.module.branch.service.BranchService
import com.cleevio.cinemax.api.module.distributor.service.DistributorService
import com.cleevio.cinemax.api.module.groupreservation.service.GroupReservationRepository
import com.cleevio.cinemax.api.module.groupreservation.service.GroupReservationService
import com.cleevio.cinemax.api.module.groupreservation.service.command.AdminCreateGroupReservationCommand
import com.cleevio.cinemax.api.module.movie.service.MovieService
import com.cleevio.cinemax.api.module.pricecategory.service.PriceCategoryService
import com.cleevio.cinemax.api.module.pricecategoryitem.constant.PriceCategoryItemNumber
import com.cleevio.cinemax.api.module.pricecategoryitem.service.PriceCategoryItemService
import com.cleevio.cinemax.api.module.reservation.constant.ReservationState
import com.cleevio.cinemax.api.module.reservation.event.ReservationCreatedEvent
import com.cleevio.cinemax.api.module.reservation.service.ReservationJooqFinderService
import com.cleevio.cinemax.api.module.reservation.service.ReservationRepository
import com.cleevio.cinemax.api.module.reservation.service.ReservationService
import com.cleevio.cinemax.api.module.reservation.service.command.CreateReservationCommand
import com.cleevio.cinemax.api.module.screening.service.ScreeningService
import com.cleevio.cinemax.api.module.screeningfee.service.ScreeningFeeService
import com.cleevio.cinemax.api.module.seat.constant.SeatType
import com.cleevio.cinemax.api.module.seat.service.SeatService
import com.cleevio.cinemax.api.module.ticket.controller.dto.AdminMoveTicketRequest
import com.cleevio.cinemax.api.module.ticket.entity.Ticket
import com.cleevio.cinemax.api.module.ticket.event.AdminTicketUsedUpdatedEvent
import com.cleevio.cinemax.api.module.ticket.event.TicketsMovedEvent
import com.cleevio.cinemax.api.module.ticket.exception.TicketNotFoundException
import com.cleevio.cinemax.api.module.ticket.service.command.AdminMoveTicketsCommand
import com.cleevio.cinemax.api.module.ticket.service.command.CreateSimpleTicketCommand
import com.cleevio.cinemax.api.module.ticket.service.command.DeleteTicketCommand
import com.cleevio.cinemax.api.module.ticket.service.command.DeleteTicketsCommand
import com.cleevio.cinemax.api.module.ticket.service.command.MarkTicketsAsUsedCommand
import com.cleevio.cinemax.api.module.ticket.service.command.MessagingCreateTicketCommand
import com.cleevio.cinemax.api.module.ticket.service.command.MessagingUpdateTicketUsedCommand
import com.cleevio.cinemax.api.module.ticket.service.command.UpdateTicketCommand
import com.cleevio.cinemax.api.module.ticket.service.command.UpdateTicketOriginalIdCommand
import com.cleevio.cinemax.api.module.ticket.service.command.UpdateTicketReceiptNumberCommand
import com.cleevio.cinemax.api.module.ticket.service.command.UpdateTicketUsedCommand
import com.cleevio.cinemax.api.module.ticketdiscount.constant.TicketDiscountType
import com.cleevio.cinemax.api.module.ticketdiscount.constant.TicketDiscountUsageType
import com.cleevio.cinemax.api.module.ticketdiscount.service.TicketDiscountService
import com.cleevio.cinemax.api.module.ticketprice.constant.AuditoriumServiceFeeType
import com.cleevio.cinemax.api.module.ticketprice.constant.AuditoriumSurchargeType
import com.cleevio.cinemax.api.module.ticketprice.constant.SeatServiceFeeType
import com.cleevio.cinemax.api.module.ticketprice.constant.SeatSurchargeType
import com.cleevio.cinemax.api.module.ticketprice.service.TicketPriceJpaFinderService
import com.cleevio.cinemax.api.module.ticketprice.service.command.MessagingCreateTicketPriceCommand
import com.cleevio.cinemax.api.util.TEST_PRINCIPAL_USERNAME
import com.cleevio.cinemax.api.util.assertReservationEquals
import com.cleevio.cinemax.api.util.createAuditorium
import com.cleevio.cinemax.api.util.createAuditoriumLayout
import com.cleevio.cinemax.api.util.createBranch
import com.cleevio.cinemax.api.util.createDistributor
import com.cleevio.cinemax.api.util.createMovie
import com.cleevio.cinemax.api.util.createPriceCategory
import com.cleevio.cinemax.api.util.createPriceCategoryItem
import com.cleevio.cinemax.api.util.createReservation
import com.cleevio.cinemax.api.util.createScreening
import com.cleevio.cinemax.api.util.createScreeningFee
import com.cleevio.cinemax.api.util.createScreeningWithCurrentDateTime
import com.cleevio.cinemax.api.util.createSeat
import com.cleevio.cinemax.api.util.createTicket
import com.cleevio.cinemax.api.util.createTicketDiscount
import com.cleevio.cinemax.api.util.mapToCreateOrUpdateAuditoriumCommand
import com.cleevio.cinemax.api.util.mapToCreateOrUpdateBranchCommand
import com.cleevio.cinemax.api.util.mapToCreateOrUpdateDistributorCommand
import com.cleevio.cinemax.api.util.mapToCreateOrUpdateMovieCommand
import com.cleevio.cinemax.api.util.mapToCreateOrUpdatePriceCategoryItemCommand
import com.cleevio.cinemax.api.util.mapToCreateOrUpdateScreeningCommand
import com.cleevio.cinemax.api.util.mapToCreateOrUpdateScreeningFeeCommand
import com.cleevio.cinemax.api.util.mapToCreateOrUpdateSeatCommand
import com.cleevio.cinemax.api.util.mapToCreateOrUpdateTicketDiscountCommand
import com.cleevio.cinemax.api.util.mapToCreateTicketCommand
import com.cleevio.cinemax.api.util.mapToSyncCreateOrUpdatePriceCategoryCommand
import com.cleevio.cinemax.api.util.toUUID
import io.mockk.Runs
import io.mockk.every
import io.mockk.just
import io.mockk.verify
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import org.springframework.beans.factory.annotation.Autowired
import java.math.BigDecimal
import java.time.LocalDateTime
import java.util.Optional
import java.util.UUID
import kotlin.test.assertEquals
import kotlin.test.assertFalse
import kotlin.test.assertNotNull
import kotlin.test.assertNull
import kotlin.test.assertTrue

class TicketServiceIT @Autowired constructor(
    private val underTest: TicketService,
    private val auditoriumService: AuditoriumService,
    private val movieService: MovieService,
    private val screeningService: ScreeningService,
    private val seatService: SeatService,
    private val reservationJooqFinderService: ReservationJooqFinderService,
    private val reservationRepository: ReservationRepository,
    private val screeningFeeService: ScreeningFeeService,
    private val priceCategoryService: PriceCategoryService,
    private val priceCategoryItemService: PriceCategoryItemService,
    private val ticketJooqFinderService: TicketJooqFinderService,
    private val ticketDiscountService: TicketDiscountService,
    private val distributorService: DistributorService,
    private val auditoriumLayoutRepository: AuditoriumLayoutRepository,
    private val ticketPriceJpaFinderService: TicketPriceJpaFinderService,
    private val groupReservationService: GroupReservationService,
    private val groupReservationRepository: GroupReservationRepository,
    private val basketItemService: BasketItemService,
    private val basketService: BasketService,
    private val reservationService: ReservationService,
    private val branchService: BranchService,
) : IntegrationTest(DatabaseCleanup.AFTER_EACH) {

    @BeforeEach
    fun setUp() {
        auditoriumService.syncCreateOrUpdateAuditorium(mapToCreateOrUpdateAuditoriumCommand(AUDITORIUM_1))
        auditoriumLayoutRepository.save(AUDITORIUM_LAYOUT_1)
        distributorService.syncCreateOrUpdateDistributor(mapToCreateOrUpdateDistributorCommand(DISTRIBUTOR_1))
        movieService.syncCreateOrUpdateMovie(mapToCreateOrUpdateMovieCommand(MOVIE_1))
        priceCategoryService.syncCreateOrUpdatePriceCategory(
            mapToSyncCreateOrUpdatePriceCategoryCommand(
                PRICE_CATEGORY_1
            )
        )
        screeningService.syncCreateOrUpdateScreening(mapToCreateOrUpdateScreeningCommand(SCREENING_1))
        setOf(SEAT_1, SEAT_2, SEAT_3).forEach {
            seatService.createOrUpdateSeat(mapToCreateOrUpdateSeatCommand(it))
        }
        priceCategoryItemService.createOrUpdatePriceCategoryItem(
            mapToCreateOrUpdatePriceCategoryItemCommand(PRICE_CATEGORY_ITEM_1, PRICE_CATEGORY_1.id)
        )
        priceCategoryItemService.createOrUpdatePriceCategoryItem(
            mapToCreateOrUpdatePriceCategoryItemCommand(PRICE_CATEGORY_ITEM_2, PRICE_CATEGORY_1.id)
        )
        screeningFeeService.createOrUpdateScreeningFee(mapToCreateOrUpdateScreeningFeeCommand(SCREENING_FEE_1))
        setOf(TICKET_DISCOUNT_1, TICKET_DISCOUNT_2, TICKET_DISCOUNT_3).forEach {
            ticketDiscountService.syncCreateOrUpdateTicketDiscount(mapToCreateOrUpdateTicketDiscountCommand(it))
        }
        branchService.syncCreateOrUpdateBranch(mapToCreateOrUpdateBranchCommand(BRANCH_1))

        every {
            reservationMssqlFinderServiceMock.findByOriginalScreeningIdAndOriginalSeatId(
                any(),
                any()
            )
        } returns null
        every { applicationEventPublisherMock.publishEvent(any<ReservationCreatedEvent>()) } just Runs
        every { applicationEventPublisherMock.publishEvent(any<AdminTicketUsedUpdatedEvent>()) } just Runs
        every { applicationEventPublisherMock.publishEvent(any<TicketsMovedEvent>()) } just Runs
    }

    @Test
    fun `test createTicket - should create ticket`() {
        val command = mapToCreateTicketCommand(
            ticket = TICKET_1,
            seatId = SEAT_1.id,
            priceCategoryItemNumber = PRICE_CATEGORY_ITEM_1.number,
            isGroupTicket = true
        )

        underTest.createTicket(command)

        val reservations = reservationJooqFinderService.findAll()
        assertEquals(1, reservations.size)
        assertReservationEquals(RESERVATION_1, reservations[0])

        val tickets = ticketJooqFinderService.findAll()
        assertEquals(1, tickets.size)
        val createdTicket = tickets[0]
        assertNotNull(createdTicket)
        assertTicketEquals(TICKET_1, createdTicket)
        assertEquals(reservations[0].id, createdTicket.reservationId)
    }

    @Test
    fun `test updateTicket - should update ticket`() {
        val command = mapToCreateTicketCommand(
            ticket = TICKET_1,
            seatId = SEAT_1.id,
            priceCategoryItemNumber = PRICE_CATEGORY_ITEM_1.number,
            isGroupTicket = true
        )
        underTest.createTicket(command)

        val createdTicket = ticketJooqFinderService.findAll()[0]

        underTest.updateTicket(
            UpdateTicketCommand(
                ticketId = createdTicket.id,
                secondaryTicketDiscountId = Optional.of(TICKET_DISCOUNT_3.id),
                freeTicket = false
            )
        )

        assertEquals(1, ticketJooqFinderService.findAll().size)
        val updatedTicket = ticketJooqFinderService.findAll()[0]
        assertEquals(createdTicket.originalId, updatedTicket.originalId)
        assertEquals(createdTicket.screeningId, updatedTicket.screeningId)
        assertEquals(createdTicket.reservationId, updatedTicket.reservationId)
        assertEquals(createdTicket.ticketDiscountPrimaryId, updatedTicket.ticketDiscountPrimaryId)
        assertEquals(TICKET_DISCOUNT_3.id, updatedTicket.ticketDiscountSecondaryId)
        assertEquals(createdTicket.isGroupTicket, updatedTicket.isGroupTicket)
        assertTrue(updatedTicket.updatedAt.isAfter(createdTicket.updatedAt))
        assertEquals(TEST_PRINCIPAL_USERNAME, updatedTicket.updatedBy)
    }

    @Test
    fun `test deleteTicket - should delete ticket and reservation`() {
        val command = mapToCreateTicketCommand(TICKET_1, SEAT_1.id, PRICE_CATEGORY_ITEM_1.number)
        underTest.createTicket(command)

        val createdTicket = ticketJooqFinderService.findAll()[0]
        assertNull(createdTicket.deletedAt)
        val createdReservation = reservationJooqFinderService.findAll()[0]
        assertNull(createdReservation.deletedAt)

        underTest.deleteTicket(DeleteTicketCommand(createdTicket.id))

        assertNull(ticketJooqFinderService.findNonDeletedById(createdTicket.id))
        val deletedTicket = ticketJooqFinderService.findById(createdTicket.id)
        assertNotNull(deletedTicket)
        assertNotNull(deletedTicket.deletedAt)
        assertEquals(TEST_PRINCIPAL_USERNAME, deletedTicket.updatedBy)

        assertNull(reservationJooqFinderService.findNonDeletedById(createdReservation.id))
        val deletedReservation = reservationJooqFinderService.findById(createdReservation.id)
        assertNotNull(deletedReservation)
        assertNotNull(deletedReservation.deletedAt)
        assertEquals(ReservationState.DELETED, deletedReservation.state)
        assertEquals(TEST_PRINCIPAL_USERNAME, deletedReservation.updatedBy)
    }

    @Test
    fun `test delete ticket - group reservation exists for ticket - should delete only ticket and keep reservation`() {
        groupReservationService.adminCreateGroupReservation(
            command = AdminCreateGroupReservationCommand(
                name = "Test",
                screeningId = SCREENING_1.id,
                seatIds = setOf(SEAT_1.id)
            )
        )

        val groupReservation = groupReservationRepository.findAll().also {
            assertEquals(1, it.size)
        }.first()

        reservationService.createReservation(
            CreateReservationCommand(
                screeningId = SCREENING_1.id,
                seatId = SEAT_1.id,
                state = ReservationState.GROUP_RESERVED,
                groupReservationId = groupReservation.id
            )
        )

        basketItemService.createBasketItemsFromGroupReservation(
            CreateBasketItemsFromGroupReservationCommand(
                basketId = basketService.initBasket(InitBasketCommand(items = listOf())).id,
                groupReservationId = groupReservation.id,
                priceCategoryItemNumber = PriceCategoryItemNumber.PRICE_1
            )
        )

        val createdTicket = ticketJooqFinderService.findAll().also {
            assertEquals(1, it.size)
            assertNull(it[0].deletedAt)
        }.first()

        val createdReservation = reservationJooqFinderService.findAll().also {
            assertEquals(1, it.size)
            assertNull(it[0].deletedAt)
        }.first()

        underTest.deleteTicket(DeleteTicketCommand(createdTicket.id))

        ticketJooqFinderService.findById(createdTicket.id).let {
            assertNotNull(it)
            assertNotNull(it.deletedAt)
        }

        reservationJooqFinderService.findById(createdReservation.id).let {
            assertEquals(createdReservation, it)
            assertNotNull(it)
            assertNull(it.deletedAt)
        }
    }

    @Test
    fun `test deleteTickets - should delete tickets and reservations`() {
        val command1 = mapToCreateTicketCommand(TICKET_1, SEAT_1.id, PRICE_CATEGORY_ITEM_1.number)
        val command2 = mapToCreateTicketCommand(TICKET_2, SEAT_2.id, PRICE_CATEGORY_ITEM_1.number)
        underTest.createTicket(command1)
        underTest.createTicket(command2)

        val createdTickets = ticketJooqFinderService.findAll()
        val createdTicket1 = createdTickets[0]
        val createdTicket2 = createdTickets[1]
        assertEquals(2, createdTickets.size)
        assertNull(createdTicket1.deletedAt)
        assertNull(createdTicket2.deletedAt)
        val createdReservations = reservationJooqFinderService.findAll()
        assertNull(createdReservations[0].deletedAt)
        assertNull(createdReservations[1].deletedAt)

        underTest.deleteTickets(
            DeleteTicketsCommand(
                basketId = BASKET_ID,
                ticketIds = setOf(createdTicket1.id, createdTicket2.id)
            )
        )

        assertNull(ticketJooqFinderService.findNonDeletedById(createdTicket1.id))
        assertNull(ticketJooqFinderService.findNonDeletedById(createdTicket2.id))
        val deletedTickets = ticketJooqFinderService.findAllByIdIn(setOf(createdTicket1.id, createdTicket2.id))
            .sortedBy { it.createdAt }
        assertEquals(2, deletedTickets.size)
        assertNotNull(deletedTickets[0].deletedAt)
        assertNotNull(deletedTickets[1].deletedAt)
        assertEquals(TEST_PRINCIPAL_USERNAME, deletedTickets[0].updatedBy)
        assertEquals(TEST_PRINCIPAL_USERNAME, deletedTickets[1].updatedBy)

        assertNull(reservationJooqFinderService.findNonDeletedById(createdReservations[0].id))
        assertNull(reservationJooqFinderService.findNonDeletedById(createdReservations[1].id))
        val deletedReservation1 = reservationJooqFinderService.findById(createdReservations[0].id)
        val deletedReservation2 = reservationJooqFinderService.findById(createdReservations[1].id)
        assertNotNull(deletedReservation1)
        assertNotNull(deletedReservation2)
        assertNotNull(deletedReservation1.deletedAt)
        assertNotNull(deletedReservation2.deletedAt)
        assertEquals(ReservationState.DELETED, deletedReservation1.state)
        assertEquals(ReservationState.DELETED, deletedReservation2.state)
        assertEquals(TEST_PRINCIPAL_USERNAME, deletedReservation1.updatedBy)
        assertEquals(TEST_PRINCIPAL_USERNAME, deletedReservation2.updatedBy)
    }

    @Test
    fun `test delete tickets - some of tickets have group reservations - should delete all tickets and reservations only not group ones`() {
        val command = mapToCreateTicketCommand(TICKET_2, SEAT_2.id, PRICE_CATEGORY_ITEM_1.number)
        underTest.createTicket(command)

        groupReservationService.adminCreateGroupReservation(
            command = AdminCreateGroupReservationCommand(
                name = "Test",
                screeningId = SCREENING_1.id,
                seatIds = setOf(SEAT_1.id)
            )
        )

        val groupReservation = groupReservationRepository.findAll().also {
            assertEquals(1, it.size)
        }.first()

        reservationService.createReservation(
            CreateReservationCommand(
                screeningId = SCREENING_1.id,
                seatId = SEAT_1.id,
                state = ReservationState.GROUP_RESERVED,
                groupReservationId = groupReservation.id
            )
        )

        val basket = basketService.initBasket(InitBasketCommand(items = listOf()))
        basketItemService.createBasketItemsFromGroupReservation(
            CreateBasketItemsFromGroupReservationCommand(
                basketId = basket.id,
                groupReservationId = groupReservation.id,
                priceCategoryItemNumber = PriceCategoryItemNumber.PRICE_1
            )
        )

        val createdTickets = ticketJooqFinderService.findAll().also {
            assertEquals(2, it.size)
            assertTrue(it.all { ticket -> ticket.deletedAt == null })
        }

        reservationJooqFinderService.findAll().also {
            assertEquals(2, it.size)
            assertTrue(it.all { ticket -> ticket.deletedAt == null })
        }

        underTest.deleteTickets(
            DeleteTicketsCommand(
                basketId = basket.id,
                ticketIds = createdTickets.map { it.id }.toSet()
            )
        )

        val ticketsAfterDelete = ticketJooqFinderService.findAll()
        assertEquals(2, ticketsAfterDelete.size)
        assertTrue(ticketsAfterDelete.all { ticket -> ticket.deletedAt != null })

        val reservationsAfterDelete = reservationJooqFinderService.findAll()
        assertEquals(2, reservationsAfterDelete.size)

        reservationsAfterDelete.filter { it.groupReservationId != null }.let {
            assertEquals(1, it.size)
            assertEquals(SEAT_1.id, it[0].seatId)
            assertNull(it[0].deletedAt)
        }
        reservationsAfterDelete.filter { it.groupReservationId == null }.let {
            assertEquals(1, it.size)
            assertEquals(SEAT_2.id, it[0].seatId)
            assertNotNull(it[0].deletedAt)
        }
    }

    @Test
    fun `test updateTicketReceiptNumber - should update receipt number`() {
        val command = mapToCreateTicketCommand(TICKET_1, SEAT_1.id, PRICE_CATEGORY_ITEM_1.number)
        underTest.createTicket(command)

        val createdTicket = ticketJooqFinderService.findAll()[0]
        assertNull(createdTicket.receiptNumber)

        val receiptNumber = "000123456789"
        underTest.updateTicketReceiptNumber(
            UpdateTicketReceiptNumberCommand(
                ticketId = createdTicket.id,
                receiptNumber = receiptNumber
            )
        )

        val updatedTicket = ticketJooqFinderService.getNonDeletedById(createdTicket.id)
        assertTrue(updatedTicket.updatedAt.isAfter(createdTicket.updatedAt))
        assertEquals(receiptNumber, updatedTicket.receiptNumber)
    }

    @Test
    fun `test updateTicketOriginalId - should update original id`() {
        val command = mapToCreateTicketCommand(TICKET_1, SEAT_1.id, PRICE_CATEGORY_ITEM_1.number)
        underTest.createTicket(command)

        val createdTicket = ticketJooqFinderService.findAll()[0]
        assertNull(createdTicket.receiptNumber)

        val originalId = 123456
        underTest.updateTicketOriginalId(
            UpdateTicketOriginalIdCommand(
                ticketId = createdTicket.id,
                originalId = originalId
            )
        )

        val updatedTicket = ticketJooqFinderService.getNonDeletedById(createdTicket.id)
        assertTrue(updatedTicket.updatedAt.isAfter(createdTicket.updatedAt))
        assertEquals(originalId, updatedTicket.originalId)
    }

    @Test
    fun `test moveTickets - should update tickets and reservations`() {
        screeningService.syncCreateOrUpdateScreening(mapToCreateOrUpdateScreeningCommand(SCREENING_2))
        screeningFeeService.createOrUpdateScreeningFee(mapToCreateOrUpdateScreeningFeeCommand(SCREENING_FEE_2))

        val command1 = mapToCreateTicketCommand(
            ticket = TICKET_3,
            seatId = SEAT_1.id,
            priceCategoryItemNumber = PRICE_CATEGORY_ITEM_1.number
        )
        val command2 = mapToCreateTicketCommand(
            ticket = TICKET_4,
            seatId = SEAT_2.id,
            priceCategoryItemNumber = PRICE_CATEGORY_ITEM_1.number
        )
        val command3 = mapToCreateTicketCommand(
            ticket = TICKET_5,
            seatId = SEAT_3.id,
            priceCategoryItemNumber = PRICE_CATEGORY_ITEM_1.number
        )
        setOf(command1, command2, command3).forEach { underTest.createTicket(it) }

        val createdTickets = ticketJooqFinderService.findAll()
        val createdReservations = reservationJooqFinderService.findAll()
        assertEquals(3, createdTickets.size)
        assertEquals(3, createdReservations.size)

        val ticketPriceIds = createdTickets.map { it.ticketPriceId }.toSet()
        val totalPricesAfterCreate = ticketPriceJpaFinderService.findAllByIdIn(ticketPriceIds).map { it.totalPrice }

        assertEquals(
            createdTickets.map { it.reservationId },
            createdReservations.map { it.id }
        )
        setOf(RESERVATION_1, RESERVATION_2, RESERVATION_3).forEachIndexed { index, reservation ->
            assertReservationEquals(reservation, createdReservations[index])
        }

        underTest.moveTickets(
            AdminMoveTicketsCommand(
                ticketRequests = listOf(
                    AdminMoveTicketRequest(
                        ticketId = createdTickets[0].id,
                        screeningId = SCREENING_2.id,
                        seatId = SEAT_3.id
                    ),
                    AdminMoveTicketRequest(
                        ticketId = createdTickets[1].id,
                        screeningId = SCREENING_2.id,
                        seatId = SEAT_2.id
                    ),
                    AdminMoveTicketRequest(
                        ticketId = createdTickets[2].id,
                        screeningId = SCREENING_2.id,
                        seatId = SEAT_1.id
                    )
                )
            )
        )

        val allReservations = reservationJooqFinderService.findAll().sortedBy { it.createdAt }
        assertEquals(6, allReservations.size)

        val oldReservations = allReservations.subList(0, 3)
        val newReservations = allReservations.subList(3, 6)

        oldReservations.forEach {
            assertEquals(ReservationState.DELETED, it.state)
            assertTrue(it.isDeleted())
        }
        setOf(RESERVATION_4, RESERVATION_5, RESERVATION_6).forEachIndexed { index, reservation ->
            assertReservationEquals(reservation, newReservations[index])
        }

        val updatedTickets = ticketJooqFinderService.findAllByIdIn(createdTickets.map { it.id }.toSet())
        updatedTickets.forEachIndexed { index, updatedTicket ->
            assertEquals(SCREENING_2.id, updatedTicket.screeningId)
            assertEquals(createdTickets[index].ticketPriceId, updatedTickets[index].ticketPriceId)
            assertEquals(newReservations[index].id, updatedTicket.reservationId)
            assertTrue(updatedTicket.updatedAt.isAfter(createdTickets[index].updatedAt))
        }
        val totalPricesAfterUpdate = ticketPriceJpaFinderService.findAllByIdIn(ticketPriceIds).map { it.totalPrice }
        assertEquals(totalPricesAfterCreate, totalPricesAfterUpdate)

        verify {
            applicationEventPublisherMock.publishEvent(
                TicketsMovedEvent(
                    ticketIds = createdTickets.mapToSet { it.id }
                )
            )
        }
    }

    @Test
    fun `test syncUpdateTicketUsed - should update isUsed attribute`() {
        val command = mapToCreateTicketCommand(TICKET_1, SEAT_1.id, PRICE_CATEGORY_ITEM_1.number)
        val ticketModel = underTest.createTicket(command)

        underTest.updateTicketOriginalId(
            UpdateTicketOriginalIdCommand(
                ticketId = ticketModel.ticket.id,
                originalId = 1
            )
        )

        val createdTicket = ticketJooqFinderService.findAll()[0]
        assertFalse(createdTicket.isUsed)

        underTest.updateTicketUsed(
            UpdateTicketUsedCommand(
                originalId = createdTicket.originalId!!,
                isUsed = true
            )
        )

        val updatedTicket = ticketJooqFinderService.getNonDeletedById(createdTicket.id)
        assertTrue(updatedTicket.updatedAt.isAfter(createdTicket.updatedAt))
        assertTrue(updatedTicket.isUsed)

        verify {
            applicationEventPublisherMock.publishEvent(
                AdminTicketUsedUpdatedEvent(
                    ticketId = updatedTicket.id,
                    isUsed = updatedTicket.isUsed
                )
            )
        }
    }

    @Test
    fun `test markTicketsAsUsed - should update isUsed attribute`() {
        val ticketModel1 = underTest.createTicket(
            mapToCreateTicketCommand(TICKET_1, SEAT_1.id, PRICE_CATEGORY_ITEM_1.number)
        )
        val ticketModel2 = underTest.createTicket(
            mapToCreateTicketCommand(TICKET_2, SEAT_2.id, PRICE_CATEGORY_ITEM_2.number)
        )
        val ticketModel3 = underTest.createTicket(
            mapToCreateTicketCommand(TICKET_3, SEAT_3.id, PRICE_CATEGORY_ITEM_1.number)
        )

        underTest.markTicketsAsUsed(
            MarkTicketsAsUsedCommand(
                ticketIds = setOf(
                    ticketModel1.ticket.id,
                    ticketModel3.ticket.id
                )
            )
        )

        val tickets = ticketJooqFinderService.findAll().sortedBy { it.createdAt }
        assertEquals(3, tickets.size)
        tickets[0].let {
            assertEquals(ticketModel1.ticket.id, it.id)
            assertTrue(it.isUsed)
        }
        tickets[1].let {
            assertEquals(ticketModel2.ticket.id, it.id)
            assertFalse(it.isUsed)
        }
        tickets[2].let {
            assertEquals(ticketModel3.ticket.id, it.id)
            assertTrue(it.isUsed)
        }
    }

    @Test
    fun `test markTicketsAsUsed - should throw is non-existent ID is provided`() {
        val ticketModel1 = underTest.createTicket(
            mapToCreateTicketCommand(TICKET_1, SEAT_1.id, PRICE_CATEGORY_ITEM_1.number)
        )

        assertThrows<TicketNotFoundException> {
            underTest.markTicketsAsUsed(
                MarkTicketsAsUsedCommand(
                    ticketIds = setOf(
                        ticketModel1.ticket.id,
                        UUID.randomUUID()
                    )
                )
            )
        }
    }

    @Test
    fun `test createMssqlTicket - should create ticket`() {
        // in case of online tickets, prior reservation always exists
        reservationRepository.save(RESERVATION_1)

        underTest.createSimpleTicket(
            CreateSimpleTicketCommand(
                originalId = 1,
                screeningId = SCREENING_1.id,
                seatId = SEAT_1.id,
                reservationId = RESERVATION_1.id,
                priceCategoryItemNumber = PRICE_CATEGORY_ITEM_1.number,
                ticketDiscountPrimaryId = TICKET_DISCOUNT_1.id,
                ticketDiscountSecondaryId = null,
                receiptNumber = "123456789",
                isUsed = true,
                isGroupTicket = false,
                includes3dGlasses = false,
                basePrice = 5.toBigDecimal(),
                basePriceBeforeDiscount = 6.toBigDecimal(),
                seatSurcharge = 0.1.toBigDecimal(),
                seatSurchargeType = SeatSurchargeType.PREMIUM_PLUS,
                auditoriumSurcharge = 0.2.toBigDecimal(),
                auditoriumSurchargeType = AuditoriumSurchargeType.IMAX,
                seatServiceFee = 0.3.toBigDecimal(),
                seatServiceFeeType = SeatServiceFeeType.VIP,
                auditoriumServiceFee = 0.4.toBigDecimal(),
                auditoriumServiceFeeType = AuditoriumServiceFeeType.ULTRA_X,
                serviceFeeGeneral = 0.5.toBigDecimal(),
                freeTicket = true,
                totalPrice = 7.toBigDecimal()
            )
        )

        val tickets = ticketJooqFinderService.findAll()
        assertEquals(1, tickets.size)

        val createdTicket = tickets[0]
        assertNotNull(createdTicket)
        assertEquals(1, createdTicket.originalId)
        assertEquals(SCREENING_1.id, createdTicket.screeningId)
        assertEquals(RESERVATION_1.id, createdTicket.reservationId)
        assertNotNull(createdTicket.ticketPriceId)
        assertEquals(TICKET_DISCOUNT_1.id, createdTicket.ticketDiscountPrimaryId)
        assertNull(createdTicket.ticketDiscountSecondaryId)
        assertEquals("123456789", createdTicket.receiptNumber)
        assertFalse(createdTicket.isGroupTicket)
        assertTrue(createdTicket.isUsed)
        assertFalse(createdTicket.includes3dGlasses)
    }

    @Test
    fun `test createMessagingTicket - ticket doesn't exist - should create ticket`() {
        underTest.createMessagingTicket(
            MessagingCreateTicketCommand(
                ticketId = 1.toUUID(),
                screeningId = SCREENING_1.id,
                seatId = SEAT_1.id,
                ticketDiscountPrimaryCode = TICKET_DISCOUNT_1.code,
                ticketDiscountSecondaryCode = null,
                receiptNumber = "123456789",
                isGroupTicket = false,
                isUsed = true,
                includes3dGlasses = false,
                ticketPrice = MessagingCreateTicketPriceCommand(
                    ticketId = UUID.randomUUID(),
                    basePrice = 10.toBigDecimal(),
                    basePriceItemNumber = PriceCategoryItemNumber.PRICE_1,
                    basePriceBeforeDiscount = 12.toBigDecimal(),
                    seatSurcharge = 2.toBigDecimal(),
                    seatSurchargeType = SeatSurchargeType.VIP,
                    auditoriumSurcharge = 3.toBigDecimal(),
                    auditoriumSurchargeType = AuditoriumSurchargeType.IMAX,
                    seatServiceFee = 1.toBigDecimal(),
                    seatServiceFeeType = SeatServiceFeeType.VIP,
                    auditoriumServiceFee = 1.5.toBigDecimal(),
                    auditoriumServiceFeeType = AuditoriumServiceFeeType.IMAX,
                    serviceFeeGeneral = 0.5.toBigDecimal(),
                    primaryDiscountPercentage = 10,
                    primaryDiscountAmount = 1.2.toBigDecimal(),
                    secondaryDiscountPercentage = 5,
                    secondaryDiscountAmount = 0.6.toBigDecimal(),
                    freeTicket = false,
                    zeroFees = false,
                    totalPrice = 15.2.toBigDecimal()
                )
            )
        )

        val reservations = reservationJooqFinderService.findAll()
        assertEquals(1, reservations.size)

        val createdReservation = reservations[0]
        assertEquals(SCREENING_1.id, createdReservation.screeningId)
        assertEquals(SEAT_1.id, createdReservation.seatId)
        assertEquals(ReservationState.UNAVAILABLE, createdReservation.state)

        val tickets = ticketJooqFinderService.findAll()
        assertEquals(1, tickets.size)

        val createdTicket = tickets[0]
        assertNotNull(createdTicket)
        assertEquals(1.toUUID(), createdTicket.id)
        assertNull(createdTicket.originalId)
        assertEquals(SCREENING_1.id, createdTicket.screeningId)
        assertNotNull(createdTicket.ticketPriceId)
        assertEquals(TICKET_DISCOUNT_1.id, createdTicket.ticketDiscountPrimaryId)
        assertNull(createdTicket.ticketDiscountSecondaryId)
        assertEquals("123456789", createdTicket.receiptNumber)
        assertFalse(createdTicket.isGroupTicket)
        assertTrue(createdTicket.isUsed)
        assertFalse(createdTicket.includes3dGlasses)
    }

    @Test
    fun `test createMessagingTicket - screening date time is in the past - should create ticket`() {
        screeningService.syncCreateOrUpdateScreening(mapToCreateOrUpdateScreeningCommand(SCREENING_3))
        screeningFeeService.createOrUpdateScreeningFee(mapToCreateOrUpdateScreeningFeeCommand(SCREENING_FEE_3))

        underTest.createMessagingTicket(
            MessagingCreateTicketCommand(
                ticketId = 1.toUUID(),
                screeningId = SCREENING_3.id,
                seatId = SEAT_1.id,
                ticketDiscountPrimaryCode = TICKET_DISCOUNT_1.code,
                ticketDiscountSecondaryCode = null,
                receiptNumber = "123456789",
                isGroupTicket = false,
                isUsed = true,
                includes3dGlasses = false,
                ticketPrice = MessagingCreateTicketPriceCommand(
                    ticketId = UUID.randomUUID(),
                    basePrice = 10.toBigDecimal(),
                    basePriceItemNumber = PriceCategoryItemNumber.PRICE_1,
                    basePriceBeforeDiscount = 12.toBigDecimal(),
                    seatSurcharge = 2.toBigDecimal(),
                    seatSurchargeType = SeatSurchargeType.VIP,
                    auditoriumSurcharge = 3.toBigDecimal(),
                    auditoriumSurchargeType = AuditoriumSurchargeType.IMAX,
                    seatServiceFee = 1.toBigDecimal(),
                    seatServiceFeeType = SeatServiceFeeType.VIP,
                    auditoriumServiceFee = 1.5.toBigDecimal(),
                    auditoriumServiceFeeType = AuditoriumServiceFeeType.IMAX,
                    serviceFeeGeneral = 0.5.toBigDecimal(),
                    primaryDiscountPercentage = 10,
                    primaryDiscountAmount = 1.2.toBigDecimal(),
                    secondaryDiscountPercentage = 5,
                    secondaryDiscountAmount = 0.6.toBigDecimal(),
                    freeTicket = false,
                    zeroFees = false,
                    totalPrice = 15.2.toBigDecimal()
                )
            )
        )

        val reservations = reservationJooqFinderService.findAll()
        assertEquals(1, reservations.size)

        val createdReservation = reservations[0]
        assertEquals(SCREENING_3.id, createdReservation.screeningId)
        assertEquals(SEAT_1.id, createdReservation.seatId)
        assertEquals(ReservationState.UNAVAILABLE, createdReservation.state)

        val tickets = ticketJooqFinderService.findAll()
        assertEquals(1, tickets.size)

        val createdTicket = tickets[0]
        assertNotNull(createdTicket)
        assertEquals(1.toUUID(), createdTicket.id)
        assertNull(createdTicket.originalId)
        assertEquals(SCREENING_3.id, createdTicket.screeningId)
        assertNotNull(createdTicket.ticketPriceId)
        assertEquals(TICKET_DISCOUNT_1.id, createdTicket.ticketDiscountPrimaryId)
        assertNull(createdTicket.ticketDiscountSecondaryId)
        assertEquals("123456789", createdTicket.receiptNumber)
        assertFalse(createdTicket.isGroupTicket)
        assertTrue(createdTicket.isUsed)
        assertFalse(createdTicket.includes3dGlasses)
    }

    @Test
    fun `test messagingUpdateTicketUsed - should update isUsed`() {
        val command = MessagingCreateBasketCommand(
            id = UUID.randomUUID(),
            totalPrice = 7.125.toBigDecimal(),
            state = BasketState.PAID,
            paidAt = LocalDateTime.now(),
            paymentType = PaymentType.CASHLESS,
            variableSymbol = null,
            branchCode = BRANCH_1.code,
            items = listOf(
                MessagingCreateBasketCommand.BasketItem(
                    id = 1.toUUID(),
                    type = BasketItemType.TICKET,
                    price = 7.125.toBigDecimal(),
                    vatAmount = 1.33.toBigDecimal(),
                    quantity = 1,
                    isCancelled = false,
                    discountCardCode = null,
                    ticket = MessagingCreateBasketCommand.BasketTicketItemInput(
                        id = 11.toUUID(),
                        screeningId = SCREENING_1.id,
                        auditoriumOriginalCode = AUDITORIUM_1.originalCode,
                        seatId = SEAT_1.id,
                        ticketReceiptNumber = "100000001",
                        ticketDiscountPrimaryCode = null,
                        ticketDiscountSecondaryCode = TICKET_DISCOUNT_2.code,
                        isGroupTicket = false,
                        isUsed = false,
                        includes3dGlasses = false,
                        ticketPrice = MessagingCreateBasketCommand.BasketTicketItemTicketPriceInput(
                            basePrice = 10.toBigDecimal(),
                            basePriceItemNumber = PriceCategoryItemNumber.PRICE_1,
                            basePriceBeforeDiscount = 12.toBigDecimal(),
                            seatSurcharge = 2.toBigDecimal(),
                            seatSurchargeType = SeatSurchargeType.VIP,
                            auditoriumSurcharge = 3.toBigDecimal(),
                            auditoriumSurchargeType = AuditoriumSurchargeType.IMAX,
                            seatServiceFee = 1.toBigDecimal(),
                            seatServiceFeeType = SeatServiceFeeType.VIP,
                            auditoriumServiceFee = 1.5.toBigDecimal(),
                            auditoriumServiceFeeType = AuditoriumServiceFeeType.IMAX,
                            serviceFeeGeneral = 0.5.toBigDecimal(),
                            primaryDiscountPercentage = 10,
                            primaryDiscountAmount = 1.2.toBigDecimal(),
                            secondaryDiscountPercentage = 5,
                            secondaryDiscountAmount = 0.6.toBigDecimal(),
                            freeTicket = false,
                            zeroFees = false,
                            totalPrice = 15.2.toBigDecimal()
                        )
                    ),
                    product = null
                )
            )
        )
        basketService.messagingCreateBasket(command)

        val createdTicket = ticketJooqFinderService.findNonDeletedById(11.toUUID())
        assertNotNull(createdTicket)
        assertFalse(createdTicket.isUsed)

        underTest.messagingUpdateTicketUsed(
            MessagingUpdateTicketUsedCommand(
                ticketId = createdTicket.id,
                isUsed = true
            )
        )

        val updatedTicket = ticketJooqFinderService.getNonDeletedById(createdTicket.id)
        assertTrue(updatedTicket.isUsed)
        assertTrue(updatedTicket.updatedAt.isAfter(createdTicket.updatedAt))
    }

    private fun assertTicketEquals(expected: Ticket, actual: Ticket) {
        assertEquals(expected.originalId, actual.originalId)
        assertEquals(expected.screeningId, actual.screeningId)
        assertEquals(expected.ticketDiscountPrimaryId, actual.ticketDiscountPrimaryId)
        assertEquals(expected.ticketDiscountSecondaryId, actual.ticketDiscountSecondaryId)
        assertEquals(expected.isGroupTicket, actual.isGroupTicket)
        assertNotNull(actual.createdAt)
        assertNotNull(actual.updatedAt)
    }
}

private val BASKET_ID = UUID.randomUUID()
private val DISTRIBUTOR_1 = createDistributor()
private val AUDITORIUM_1 = createAuditorium(
    originalId = 1,
    code = "A",
    title = "SÁLA A - CINEMAX BRATISLAVA"
)
private val AUDITORIUM_LAYOUT_1 = createAuditoriumLayout(auditoriumId = AUDITORIUM_1.id)
private val MOVIE_1 = createMovie(
    originalId = 1,
    title = "Star Wars: Episode I – The Phantom Menace",
    releaseYear = 2001,
    distributorId = DISTRIBUTOR_1.id
)
private val PRICE_CATEGORY_1 = createPriceCategory(
    originalId = 1,
    title = "ARTMAX PO 17",
    active = true
)
private val PRICE_CATEGORY_ITEM_1 = createPriceCategoryItem(
    priceCategoryId = PRICE_CATEGORY_1.id,
    number = PriceCategoryItemNumber.PRICE_1,
    title = "Dospely",
    price = BigDecimal.valueOf(9.5)
)
private val PRICE_CATEGORY_ITEM_2 = createPriceCategoryItem(
    priceCategoryId = PRICE_CATEGORY_1.id,
    number = PriceCategoryItemNumber.PRICE_2,
    title = "Student",
    price = BigDecimal.valueOf(4.5)
)
private val SCREENING_1 = createScreeningWithCurrentDateTime(
    originalId = 1,
    auditoriumId = AUDITORIUM_1.id,
    auditoriumLayoutId = AUDITORIUM_LAYOUT_1.id,
    movieId = MOVIE_1.id,
    priceCategoryId = PRICE_CATEGORY_1.id
)
private val SCREENING_2 = createScreening(
    originalId = 2,
    auditoriumId = AUDITORIUM_1.id,
    auditoriumLayoutId = AUDITORIUM_LAYOUT_1.id,
    movieId = MOVIE_1.id,
    priceCategoryId = PRICE_CATEGORY_1.id,
    date = INTEGRATION_TEST_DATE_TIME.toLocalDate().plusDays(2)
)
private val SCREENING_3 = createScreening(
    originalId = 3,
    auditoriumId = AUDITORIUM_1.id,
    auditoriumLayoutId = AUDITORIUM_LAYOUT_1.id,
    movieId = MOVIE_1.id,
    priceCategoryId = PRICE_CATEGORY_1.id,
    date = INTEGRATION_TEST_DATE_TIME.toLocalDate().minusDays(2)
)
private val SCREENING_FEE_1 = createScreeningFee(
    originalScreeningId = 1,
    screeningId = SCREENING_1.id,
    surchargeVip = BigDecimal.ZERO,
    surchargePremium = BigDecimal.ZERO,
    surchargeImax = BigDecimal.ZERO,
    surchargeUltraX = BigDecimal.ZERO,
    serviceFeeVip = BigDecimal.ZERO,
    serviceFeePremium = BigDecimal.ZERO,
    serviceFeeImax = BigDecimal.ZERO,
    serviceFeeUltraX = BigDecimal.ZERO,
    surchargeDBox = BigDecimal.ZERO,
    serviceFeeGeneral = BigDecimal.ZERO
)
private val SCREENING_FEE_2 = createScreeningFee(
    originalScreeningId = 2,
    screeningId = SCREENING_2.id,
    surchargeVip = BigDecimal.ONE,
    surchargePremium = BigDecimal.ZERO,
    surchargeImax = BigDecimal.ZERO,
    surchargeUltraX = BigDecimal.ZERO,
    serviceFeeVip = BigDecimal.ONE,
    serviceFeePremium = BigDecimal.ZERO,
    serviceFeeImax = BigDecimal.ZERO,
    serviceFeeUltraX = BigDecimal.ZERO,
    surchargeDBox = BigDecimal.ZERO,
    serviceFeeGeneral = BigDecimal.ZERO
)
private val SCREENING_FEE_3 = createScreeningFee(
    originalScreeningId = 3,
    screeningId = SCREENING_3.id,
    surchargeVip = BigDecimal.ONE,
    surchargePremium = BigDecimal.ZERO,
    surchargeImax = BigDecimal.ZERO,
    surchargeUltraX = BigDecimal.ZERO,
    serviceFeeVip = BigDecimal.ONE,
    serviceFeePremium = BigDecimal.ZERO,
    serviceFeeImax = BigDecimal.ZERO,
    serviceFeeUltraX = BigDecimal.ZERO,
    surchargeDBox = BigDecimal.ZERO,
    serviceFeeGeneral = BigDecimal.ZERO
)
private val SEAT_1 = createSeat(
    originalId = 1,
    auditoriumId = AUDITORIUM_1.id,
    auditoriumLayoutId = AUDITORIUM_LAYOUT_1.id,
    row = "A",
    number = "6",
    positionTop = 62
)
private val SEAT_2 = createSeat(
    originalId = 2,
    auditoriumId = AUDITORIUM_1.id,
    auditoriumLayoutId = AUDITORIUM_LAYOUT_1.id,
    row = "A",
    number = "7",
    positionTop = 62
)
private val SEAT_3 = createSeat(
    originalId = 3,
    auditoriumId = AUDITORIUM_1.id,
    auditoriumLayoutId = AUDITORIUM_LAYOUT_1.id,
    row = "A",
    number = "8",
    positionTop = 62,
    type = SeatType.VIP
)
private val TICKET_DISCOUNT_1 = createTicketDiscount(
    originalId = 1,
    code = "01X",
    title = "Artmax FILM karta",
    order = 11
)
private val TICKET_DISCOUNT_2 = createTicketDiscount(
    originalId = 2,
    code = "02X",
    title = "Internet VIP",
    type = TicketDiscountType.PERCENTAGE,
    usageType = TicketDiscountUsageType.SECONDARY,
    amount = BigDecimal.valueOf(15),
    order = 12
)
private val TICKET_DISCOUNT_3 = createTicketDiscount(
    originalId = 3,
    usageType = TicketDiscountUsageType.SECONDARY
)
private val TICKET_1 = createTicket(
    screeningId = SCREENING_1.id,
    reservationId = UUID.randomUUID(),
    ticketPriceId = UUID.randomUUID(), // both random UUIDs are replaced with real data in TicketService#createTicket
    ticketDiscountPrimaryId = TICKET_DISCOUNT_1.id,
    ticketDiscountSecondaryId = TICKET_DISCOUNT_2.id,
    isGroupTicket = true
)
private val TICKET_2 = createTicket(
    screeningId = SCREENING_1.id,
    reservationId = UUID.randomUUID(),
    ticketPriceId = UUID.randomUUID(), // both random UUIDs are replaced with real data in TicketService#createTicket
    ticketDiscountPrimaryId = TICKET_DISCOUNT_1.id,
    ticketDiscountSecondaryId = TICKET_DISCOUNT_2.id,
    isGroupTicket = true
)
private val TICKET_3 = createTicket(
    screeningId = SCREENING_1.id,
    reservationId = UUID.randomUUID(),
    ticketPriceId = UUID.randomUUID()
)
private val TICKET_4 = createTicket(
    screeningId = SCREENING_1.id,
    reservationId = UUID.randomUUID(),
    ticketPriceId = UUID.randomUUID()
)
private val TICKET_5 = createTicket(
    screeningId = SCREENING_1.id,
    reservationId = UUID.randomUUID(),
    ticketPriceId = UUID.randomUUID()
)
private val RESERVATION_1 = createReservation(
    screeningId = SCREENING_1.id,
    seatId = SEAT_1.id
)
private val RESERVATION_2 = createReservation(
    screeningId = SCREENING_1.id,
    seatId = SEAT_2.id
)
private val RESERVATION_3 = createReservation(
    screeningId = SCREENING_1.id,
    seatId = SEAT_3.id
)
private val RESERVATION_4 = createReservation(
    screeningId = SCREENING_2.id,
    seatId = SEAT_3.id,
    state = RESERVATION_1.state
)
private val RESERVATION_5 = createReservation(
    screeningId = SCREENING_2.id,
    seatId = SEAT_2.id,
    state = RESERVATION_2.state
)
private val RESERVATION_6 = createReservation(
    screeningId = SCREENING_2.id,
    seatId = SEAT_1.id,
    state = RESERVATION_3.state
)
private val BRANCH_1 = createBranch(code = "510640")
