package com.cleevio.cinemax.api.module.auditorium.service

import com.cleevio.cinemax.api.DatabaseCleanup
import com.cleevio.cinemax.api.IntegrationTest
import com.cleevio.cinemax.api.module.auditorium.exception.AuditoriumNotFoundException
import com.cleevio.cinemax.api.module.auditorium.service.query.AdminGetAuditoriumQuery
import com.cleevio.cinemax.api.module.auditoriumdefault.service.AuditoriumDefaultRepository
import com.cleevio.cinemax.api.module.auditoriumlayout.service.AuditoriumLayoutRepository
import com.cleevio.cinemax.api.truncatedToSeconds
import com.cleevio.cinemax.api.util.createAuditorium
import com.cleevio.cinemax.api.util.createAuditoriumDefault
import com.cleevio.cinemax.api.util.createAuditoriumLayout
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import org.springframework.beans.factory.annotation.Autowired
import java.util.UUID
import kotlin.test.assertEquals

class AdminGetPosConfigurationsQueryServiceIT @Autowired constructor(
    private val underTest: AdminGetAuditoriumQueryService,
    private val auditoriumRepository: AuditoriumRepository,
    private val auditoriumDefaultRepository: AuditoriumDefaultRepository,
    private val auditoriumLayoutRepository: AuditoriumLayoutRepository,
) : IntegrationTest(DatabaseCleanup.AFTER_EACH) {

    @Test
    fun `test AdminGetAuditoriumQuery - valid auditoriumId - should return correct auditorium detail`() {
        val auditorium1 = createAuditorium(
            originalId = 1,
            code = "IMAX",
            title = "IMAX",
            capacity = 100,
            city = "Bratislava"
        )
        val auditoriumDefault1 = createAuditoriumDefault(
            originalId = 1,
            auditoriumId = auditorium1.id,
            surchargeVip = 1.00.toBigDecimal(),
            surchargePremium = 2.00.toBigDecimal(),
            surchargeImax = 3.00.toBigDecimal(),
            surchargeUltraX = 4.00.toBigDecimal(),
            serviceFeeVip = 5.00.toBigDecimal(),
            serviceFeePremium = 6.00.toBigDecimal(),
            serviceFeeImax = 7.00.toBigDecimal(),
            serviceFeeUltraX = 8.00.toBigDecimal(),
            surchargeDBox = 9.00.toBigDecimal(),
            proCommission = 2,
            filmFondCommission = 3,
            distributorCommission = 50,
            saleTimeLimit = 25,
            publishOnline = true
        )
        val auditoriumLayout1 = createAuditoriumLayout(
            originalId = 1,
            code = "01",
            auditoriumId = auditorium1.id,
            title = "IMAX 3D"
        )
        val auditoriumLayout2 = createAuditoriumLayout(
            originalId = 2,
            code = "02",
            auditoriumId = auditorium1.id,
            title = "Dolby Atmos"
        )

        auditoriumRepository.save(auditorium1)
        auditoriumDefaultRepository.save(auditoriumDefault1)
        auditoriumLayoutRepository.saveAll(listOf(auditoriumLayout1, auditoriumLayout2))

        val result = underTest(AdminGetAuditoriumQuery(auditorium1.id))

        assertEquals(auditorium1.id, result.id)
        assertEquals(auditorium1.title, result.title)
        assertEquals(auditorium1.originalCode, result.originalCode)
        assertEquals(auditorium1.capacity, result.capacity)
        assertEquals(auditorium1.city, result.city)
        assertEquals(auditorium1.createdAt.truncatedToSeconds(), result.createdAt.truncatedToSeconds())
        assertEquals(auditorium1.updatedAt.truncatedToSeconds(), result.updatedAt.truncatedToSeconds())
        assertEquals(auditoriumDefault1.id, result.default.id)
        assertEquals(auditoriumDefault1.surchargeVip, result.default.surchargeVip)
        assertEquals(auditoriumDefault1.surchargePremium, result.default.surchargePremium)
        assertEquals(auditoriumDefault1.surchargeImax, result.default.surchargeImax)
        assertEquals(auditoriumDefault1.surchargeUltraX, result.default.surchargeUltraX)
        assertEquals(auditoriumDefault1.serviceFeeVip, result.default.serviceFeeVip)
        assertEquals(auditoriumDefault1.serviceFeePremium, result.default.serviceFeePremium)
        assertEquals(auditoriumDefault1.serviceFeeImax, result.default.serviceFeeImax)
        assertEquals(auditoriumDefault1.serviceFeeUltraX, result.default.serviceFeeUltraX)
        assertEquals(auditoriumDefault1.surchargeDBox, result.default.surchargeDBox)
        assertEquals(auditoriumDefault1.proCommission, result.default.proCommission)
        assertEquals(auditoriumDefault1.filmFondCommission, result.default.filmFondCommission)
        assertEquals(auditoriumDefault1.distributorCommission, result.default.distributorCommission)
        assertEquals(auditoriumDefault1.saleTimeLimit, result.default.saleTimeLimit)
        assertEquals(auditoriumDefault1.publishOnline, result.default.publishOnline)
        assertEquals(2, result.layouts.size)
        result.layouts[0].let { layout ->
            assertEquals(auditoriumLayout1.id, layout.id)
            assertEquals(auditoriumLayout1.code, layout.code)
            assertEquals(auditoriumLayout1.title, layout.title)
        }
        result.layouts[1].let { layout ->
            assertEquals(auditoriumLayout2.id, layout.id)
            assertEquals(auditoriumLayout2.code, layout.code)
            assertEquals(auditoriumLayout2.title, layout.title)
        }
    }

    @Test
    fun `test AdminGetAuditoriumQuery - record by auditoriumId not exists - should throw`() {
        assertThrows<AuditoriumNotFoundException> {
            underTest(AdminGetAuditoriumQuery(UUID.fromString("737f0de0-34d5-485f-998d-597424a5644b")))
        }
    }
}
