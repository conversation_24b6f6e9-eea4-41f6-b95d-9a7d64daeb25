package com.cleevio.cinemax.api.module.product.event

import com.cleevio.cinemax.api.common.util.MESSAGING_MAPPER
import org.junit.jupiter.api.Test
import kotlin.test.assertEquals
import kotlin.test.assertNotNull

class AdminProductDeletedEventTest {

    @Test
    fun `test toMessagePayload - should create message payload with correct type and serialization`() {
        val event = AdminProductDeletedEvent(
            code = "1234"
        )

        val expectedJson = """
            {
              "code": "1234"
            }
        """.trimIndent()

        val messagePayload = event.toMessagePayload()

        assertNotNull(messagePayload.id)
        assertNotNull(messagePayload.createdAt)
        assertEquals("PRODUCT_DELETED", messagePayload.type.name)
        assertEquals(MESSAGING_MAPPER.readTree(expectedJson), MESSAGING_MAPPER.readTree(messagePayload.data))
    }
}
