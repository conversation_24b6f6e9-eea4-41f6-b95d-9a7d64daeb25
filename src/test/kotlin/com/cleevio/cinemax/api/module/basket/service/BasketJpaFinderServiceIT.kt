package com.cleevio.cinemax.api.module.basket.service

import com.cleevio.cinemax.api.DatabaseCleanup
import com.cleevio.cinemax.api.IntegrationTest
import com.cleevio.cinemax.api.common.constant.PaymentType
import com.cleevio.cinemax.api.module.basket.constant.BasketState
import com.cleevio.cinemax.api.module.posconfiguration.service.PosConfigurationRepository
import com.cleevio.cinemax.api.util.createBasket
import com.cleevio.cinemax.api.util.createPosConfiguration
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import kotlin.test.assertEquals

class BasketJpaFinderServiceIT @Autowired constructor(
    private val underTest: BasketJpaFinderService,
    private val basketRepository: BasketRepository,
    private val posConfigurationRepository: PosConfigurationRepository,
) : IntegrationTest(DatabaseCleanup.AFTER_EACH) {

    @Test
    fun `test findAllNonDeletedPaidByPosConfigurationIdIn - should return correct baskets`() {
        posConfigurationRepository.saveAll(
            listOf(
                POS_CONFIGURATION_1,
                POS_CONFIGURATION_2
            )
        )
        basketRepository.saveAll(
            listOf(
                BASKET_1_POS_1,
                BASKET_2_POS_1,
                BASKET_3_POS_1,
                BASKET_4_POS_1,
                BASKET_5_POS_1,
                BASKET_6_POS_2,
                BASKET_7_POS_2
            )
        )

        val result = underTest.findAllNonDeletedPaidByPosConfigurationIdIn(
            setOf(POS_CONFIGURATION_1.id, POS_CONFIGURATION_2.id)
        )

        assertEquals(5, result.size)
        assertEquals(
            expected = setOf(
                BASKET_1_POS_1.id,
                BASKET_2_POS_1.id,
                BASKET_3_POS_1.id,
                BASKET_6_POS_2.id,
                BASKET_7_POS_2.id
            ),
            actual = result.map { it.id }.toSet()
        )
    }
}

private val POS_CONFIGURATION_1 = createPosConfiguration(macAddress = "AA:BB:CC:DD:EE", title = "POS 1 config")
private val POS_CONFIGURATION_2 = createPosConfiguration(macAddress = "AA:BB:CC:DD:FF", title = "POS 2 config")

private val BASKET_1_POS_1 = createBasket(
    state = BasketState.PAID,
    paymentType = PaymentType.CASH,
    paymentPosConfigurationId = POS_CONFIGURATION_1.id
)
private val BASKET_2_POS_1 = createBasket(
    state = BasketState.PAID,
    paymentType = PaymentType.CASHLESS,
    paymentPosConfigurationId = POS_CONFIGURATION_1.id
)
private val BASKET_3_POS_1 = createBasket(
    state = BasketState.PAID,
    paymentType = PaymentType.CASH,
    paymentPosConfigurationId = POS_CONFIGURATION_1.id
)
private val BASKET_4_POS_1 = createBasket(
    state = BasketState.PAID,
    paymentType = PaymentType.CASH,
    paymentPosConfigurationId = POS_CONFIGURATION_1.id
) {
    it.markDeleted()
}
private val BASKET_5_POS_1 = createBasket(
    state = BasketState.OPEN,
    paymentType = PaymentType.CASH,
    paymentPosConfigurationId = POS_CONFIGURATION_1.id
)
private val BASKET_6_POS_2 = createBasket(
    state = BasketState.PAID,
    paymentType = PaymentType.CASH,
    paymentPosConfigurationId = POS_CONFIGURATION_2.id
)
private val BASKET_7_POS_2 = createBasket(
    state = BasketState.PAID,
    paymentType = PaymentType.CASHLESS,
    paymentPosConfigurationId = POS_CONFIGURATION_2.id
)
