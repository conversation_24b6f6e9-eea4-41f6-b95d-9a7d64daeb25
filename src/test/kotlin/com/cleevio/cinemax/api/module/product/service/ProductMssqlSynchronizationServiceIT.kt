package com.cleevio.cinemax.api.module.product.service

import com.cleevio.cinemax.api.MssqlIntegrationTest
import com.cleevio.cinemax.api.common.constant.STANDARD_TAX_RATE
import com.cleevio.cinemax.api.common.util.isEqualTo
import com.cleevio.cinemax.api.module.file.constant.FileType
import com.cleevio.cinemax.api.module.file.entity.File
import com.cleevio.cinemax.api.module.file.service.BLANK_IMAGE_FILE_NAME
import com.cleevio.cinemax.api.module.product.constant.ProductType
import com.cleevio.cinemax.api.module.product.service.command.SyncCreateOrUpdateProductCommand
import com.cleevio.cinemax.api.module.productcategory.constant.ProductCategoryType
import com.cleevio.cinemax.api.module.synchronizationfrommssql.constant.SynchronizationFromMssqlType
import com.cleevio.cinemax.api.module.synchronizationfrommssql.entity.SynchronizationFromMssql
import com.cleevio.cinemax.api.module.synchronizationfrommssql.service.command.CreateOrUpdateSynchronizationFromMssqlCommand
import com.cleevio.cinemax.api.util.createProduct
import com.cleevio.cinemax.api.util.createProductCategory
import io.mockk.Called
import io.mockk.every
import io.mockk.verify
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.test.context.jdbc.Sql
import org.springframework.test.context.jdbc.SqlConfig
import org.springframework.test.context.jdbc.SqlGroup
import java.math.BigDecimal
import java.time.LocalDateTime
import kotlin.test.assertEquals
import kotlin.test.assertTrue

@SqlGroup(
    Sql(
        scripts = [
            "/db/mssql-buffet/test/init_mssql_product.sql"
        ],
        config = SqlConfig(
            dataSource = "mssqlBuffetDataSource",
            transactionManager = "mssqlBuffetTransactionManager"
        )
    ),
    Sql(
        scripts = [
            "/db/mssql-buffet/test/drop_mssql_product.sql"
        ],
        config = SqlConfig(
            dataSource = "mssqlBuffetDataSource",
            transactionManager = "mssqlBuffetTransactionManager"
        ),
        executionPhase = Sql.ExecutionPhase.AFTER_TEST_METHOD
    )
)
class ProductMssqlSynchronizationServiceIT @Autowired constructor(
    private val underTest: ProductMssqlSynchronizationService,
) : MssqlIntegrationTest() {

    @Test
    fun `test synchronize all - no PSQL product, 4 MSSQL products - should create 4 products`() {
        every { synchronizationFromMssqlFinderServiceMock.findByType(any()) } returns null
        every { fileJpaFinderServiceMock.findByOriginalName(BLANK_IMAGE_FILE_NAME) } returns null
        every { fileJpaFinderServiceMock.findByOriginalName(IMAGE_ORIGINAL_NAME_FANTA) } returns DUMMY_FILE
        every { fileJpaFinderServiceMock.findByOriginalName(IMAGE_ORIGINAL_NAME_CAPPY) } returns DUMMY_FILE
        every { productServiceMock.syncCreateOrUpdateProduct(any()) } returns DUMMY_PRODUCT
        every {
            productCategoryJooqFinderServiceMock.findNonDeletedByOriginalCode(PRODUCT_CATEGORY_1.code)
        } returns PRODUCT_CATEGORY_1
        every {
            productCategoryJooqFinderServiceMock.findNonDeletedByOriginalCode(PRODUCT_CATEGORY_2.code)
        } returns PRODUCT_CATEGORY_2
        every { synchronizationFromMssqlServiceMock.createOrUpdate(any()) } returns SYNCHRONIZATION_FROM_MSSQL

        underTest.synchronizeAll()

        val originalProductCategoryCodeCaptor = mutableListOf<String>()
        val originalFileNameCaptor = mutableListOf<String>()
        val commandCaptor = mutableListOf<SyncCreateOrUpdateProductCommand>()

        verify { synchronizationFromMssqlFinderServiceMock.findByType(SynchronizationFromMssqlType.PRODUCT) }
        verify { fileJpaFinderServiceMock.findByOriginalName(capture(originalFileNameCaptor)) }
        verify { productCategoryJooqFinderServiceMock.findNonDeletedByOriginalCode(capture(originalProductCategoryCodeCaptor)) }
        verify { productServiceMock.syncCreateOrUpdateProduct(capture(commandCaptor)) }
        verify {
            synchronizationFromMssqlServiceMock.createOrUpdate(
                CreateOrUpdateSynchronizationFromMssqlCommand(
                    type = SynchronizationFromMssqlType.PRODUCT,
                    lastSynchronization = PRODUCT_3_UPDATED_AT
                )
            )
        }

        val productCategoryCodes = setOf(PRODUCT_CATEGORY_1.code, PRODUCT_CATEGORY_2.code)
        val originalFileNames = setOf(IMAGE_ORIGINAL_NAME_NENI, IMAGE_ORIGINAL_NAME_FANTA, IMAGE_ORIGINAL_NAME_CAPPY)
        assertTrue(originalProductCategoryCodeCaptor.size == 4)
        assertTrue(originalProductCategoryCodeCaptor.containsAll(productCategoryCodes))
        assertTrue(originalFileNameCaptor.size == 4)
        assertTrue(originalFileNameCaptor.containsAll(originalFileNames))
        assertTrue(commandCaptor.size == 4)
        assertCommandEquals(EXPECTED_COMMAND_1, commandCaptor[0])
        assertCommandEquals(EXPECTED_COMMAND_2, commandCaptor[1])
        assertCommandEquals(EXPECTED_COMMAND_3, commandCaptor[2])
        assertCommandEquals(EXPECTED_COMMAND_4, commandCaptor[3])
    }

    @Test
    fun `test synchronize all - 3 PSQL products, 4 MSSQL products - should create 1 product`() {
        every { synchronizationFromMssqlFinderServiceMock.findByType(any()) } returns PRODUCT_2_UPDATED_AT
        every { fileJpaFinderServiceMock.findByOriginalName(any()) } returns DUMMY_FILE
        every { productServiceMock.syncCreateOrUpdateProduct(any()) } returns DUMMY_PRODUCT
        every { productCategoryJooqFinderServiceMock.findNonDeletedByOriginalCode(any()) } returns PRODUCT_CATEGORY_2
        every { synchronizationFromMssqlServiceMock.createOrUpdate(any()) } returns SYNCHRONIZATION_FROM_MSSQL

        underTest.synchronizeAll()

        val originalProductCategoryCodeCaptor = mutableListOf<String>()
        val originalFileNameCaptor = mutableListOf<String>()
        val commandCaptor = mutableListOf<SyncCreateOrUpdateProductCommand>()

        verify { synchronizationFromMssqlFinderServiceMock.findByType(SynchronizationFromMssqlType.PRODUCT) }
        verify { fileJpaFinderServiceMock.findByOriginalName(capture(originalFileNameCaptor)) }
        verify { productCategoryJooqFinderServiceMock.findNonDeletedByOriginalCode(capture(originalProductCategoryCodeCaptor)) }
        verify { productServiceMock.syncCreateOrUpdateProduct(capture(commandCaptor)) }
        verify {
            synchronizationFromMssqlServiceMock.createOrUpdate(
                CreateOrUpdateSynchronizationFromMssqlCommand(
                    type = SynchronizationFromMssqlType.PRODUCT,
                    lastSynchronization = PRODUCT_3_UPDATED_AT
                )
            )
        }

        assertTrue(originalProductCategoryCodeCaptor.size == 1)
        assertTrue(originalProductCategoryCodeCaptor.containsAll(setOf(PRODUCT_CATEGORY_2.code)))
        assertTrue(originalFileNameCaptor.size == 1)
        assertTrue(originalFileNameCaptor.containsAll(setOf(IMAGE_ORIGINAL_NAME_CAPPY)))
        assertTrue(commandCaptor.size == 1)
        assertCommandEquals(EXPECTED_COMMAND_2, commandCaptor[0])
    }

    @Test
    fun `test synchronize all - no PSQL product category - should create no product`() {
        every { synchronizationFromMssqlFinderServiceMock.findByType(any()) } returns null
        every { fileJpaFinderServiceMock.findByOriginalName(any()) } returns null
        every { productServiceMock.syncCreateOrUpdateProduct(any()) } returns DUMMY_PRODUCT
        every { productCategoryJooqFinderServiceMock.findNonDeletedByOriginalCode(any()) } returns null
        every { synchronizationFromMssqlServiceMock.createOrUpdate(any()) } returns SYNCHRONIZATION_FROM_MSSQL

        underTest.synchronizeAll()

        val originalProductCategoryCodeCaptor = mutableListOf<String>()

        verify { synchronizationFromMssqlFinderServiceMock.findByType(SynchronizationFromMssqlType.PRODUCT) }
        verify { fileJpaFinderServiceMock wasNot Called }
        verify { productCategoryJooqFinderServiceMock.findNonDeletedByOriginalCode(capture(originalProductCategoryCodeCaptor)) }
        verify { productServiceMock wasNot Called }
        verify {
            synchronizationFromMssqlServiceMock.createOrUpdate(
                CreateOrUpdateSynchronizationFromMssqlCommand(
                    type = SynchronizationFromMssqlType.PRODUCT,
                    lastSynchronization = PRODUCT_3_UPDATED_AT
                )
            )
        }

        val productCategoryCodes = setOf(PRODUCT_CATEGORY_1.code, PRODUCT_CATEGORY_2.code)
        assertTrue(originalProductCategoryCodeCaptor.size == 4)
        assertTrue(originalProductCategoryCodeCaptor.containsAll(productCategoryCodes))
    }

    private fun assertCommandEquals(expected: SyncCreateOrUpdateProductCommand, actual: SyncCreateOrUpdateProductCommand) {
        assertEquals(expected.originalId, actual.originalId)
        assertEquals(expected.originalCode, actual.originalCode)
        assertEquals(expected.productCategoryId, actual.productCategoryId)
        assertEquals(expected.title, actual.title)
        assertEquals(expected.title, actual.title)
        assertEquals(expected.type, actual.type)
        assertTrue(expected.price isEqualTo actual.price)
        assertEquals(expected.order, actual.order)
        assertEquals(expected.soldInBuffet, actual.soldInBuffet)
        assertEquals(expected.soldInCafe, actual.soldInCafe)
        assertEquals(expected.soldInVip, actual.soldInVip)
        assertEquals(expected.imageFileId, actual.imageFileId)
        assertEquals(expected.isPackagingDeposit, actual.isPackagingDeposit)
        assertTrue(expected.discountAmount isEqualTo actual.discountAmount)
    }
}

private const val IMAGE_ORIGINAL_NAME_NENI = "NENI.PNG"
private const val IMAGE_ORIGINAL_NAME_FANTA = "FANTA POHAR.PNG"
private const val IMAGE_ORIGINAL_NAME_CAPPY = "CAPPY-POMARANC-PET.JPG"
private val PRODUCT_2_UPDATED_AT = LocalDateTime.of(2023, 1, 19, 15, 8, 8)
private val PRODUCT_3_UPDATED_AT = LocalDateTime.of(2023, 6, 7, 11, 16, 9)
private val PRODUCT_CATEGORY_1 = createProductCategory(
    originalId = 1,
    code = "35",
    title = "Zlavove karty",
    type = ProductCategoryType.DISCOUNT,
    order = 10,
    taxRate = STANDARD_TAX_RATE
)
private val PRODUCT_CATEGORY_2 = createProductCategory(
    originalId = 2,
    code = "05",
    title = "Napoje",
    type = ProductCategoryType.PRODUCT,
    order = 11,
    taxRate = STANDARD_TAX_RATE
)
private val DUMMY_PRODUCT = createProduct(
    productCategoryId = PRODUCT_CATEGORY_1.id,
    title = "Dummy Product",
    price = BigDecimal.ONE
)
private val DUMMY_FILE = File(
    type = FileType.PRODUCT_IMAGE,
    extension = "jpg"
)
private val EXPECTED_COMMAND_1 = SyncCreateOrUpdateProductCommand(
    originalId = 1,
    originalCode = "01027",
    productCategoryId = PRODUCT_CATEGORY_2.id,
    title = "Fanta+Cola combo",
    type = ProductType.PRODUCT_IN_PRODUCT,
    price = BigDecimal.valueOf(3.2),
    active = true,
    order = null,
    soldInBuffet = false,
    soldInCafe = false,
    soldInVip = true,
    priceNoVat = BigDecimal.valueOf(2.67),
    discountPercentage = null,
    stockQuantityThreshold = 25,
    imageFileId = DUMMY_FILE.id,
    isPackagingDeposit = false,
    tabletOrder = 0
)
private val EXPECTED_COMMAND_2 = SyncCreateOrUpdateProductCommand(
    originalId = 2,
    originalCode = "01031",
    productCategoryId = PRODUCT_CATEGORY_2.id,
    title = "Záloha na obal",
    type = ProductType.PRODUCT,
    price = BigDecimal.valueOf(2.5),
    active = true,
    order = 6,
    soldInBuffet = false,
    soldInCafe = false,
    soldInVip = true,
    priceNoVat = BigDecimal.valueOf(2.08),
    discountPercentage = null,
    stockQuantityThreshold = null,
    imageFileId = DUMMY_FILE.id,
    isPackagingDeposit = true,
    tabletOrder = 6
)
private val EXPECTED_COMMAND_3 = SyncCreateOrUpdateProductCommand(
    originalId = 3,
    originalCode = "VP",
    productCategoryId = PRODUCT_CATEGORY_1.id,
    title = "VIP karta -20%",
    type = ProductType.ADDITIONAL_SALE,
    price = BigDecimal.ZERO,
    active = true,
    order = null,
    soldInBuffet = true,
    soldInCafe = true,
    soldInVip = true,
    priceNoVat = BigDecimal.valueOf(0.00),
    discountPercentage = 20,
    stockQuantityThreshold = 10,
    imageFileId = null,
    isPackagingDeposit = false,
    tabletOrder = 16
)
private val EXPECTED_COMMAND_4 = SyncCreateOrUpdateProductCommand(
    originalId = 4,
    originalCode = "02564",
    productCategoryId = PRODUCT_CATEGORY_1.id,
    title = "Produktova sleva",
    type = ProductType.PRODUCT,
    price = BigDecimal.valueOf(5),
    active = true,
    order = null,
    soldInBuffet = false,
    soldInCafe = false,
    soldInVip = true,
    priceNoVat = BigDecimal.valueOf(2.5),
    discountPercentage = null,
    discountAmount = BigDecimal.valueOf(5),
    stockQuantityThreshold = 25,
    imageFileId = null,
    isPackagingDeposit = false,
    tabletOrder = 0
)

private val SYNCHRONIZATION_FROM_MSSQL = SynchronizationFromMssql(
    type = SynchronizationFromMssqlType.PRODUCT,
    lastSynchronization = LocalDateTime.MIN,
    lastHeartbeat = null
)
