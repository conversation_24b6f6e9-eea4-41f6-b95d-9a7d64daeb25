package com.cleevio.cinemax.api.module.screening.service

import com.cleevio.cinemax.api.DatabaseCleanup
import com.cleevio.cinemax.api.INTEGRATION_TEST_DATE_TIME
import com.cleevio.cinemax.api.IntegrationTest
import com.cleevio.cinemax.api.common.holiday.HolidayResolver
import com.cleevio.cinemax.api.common.util.isEqualTo
import com.cleevio.cinemax.api.module.auditorium.service.AuditoriumRepository
import com.cleevio.cinemax.api.module.auditoriumdefault.service.AuditoriumDefaultRepository
import com.cleevio.cinemax.api.module.auditoriumlayout.exception.AuditoriumLayoutNotFoundException
import com.cleevio.cinemax.api.module.auditoriumlayout.service.AuditoriumLayoutRepository
import com.cleevio.cinemax.api.module.distributor.service.DistributorRepository
import com.cleevio.cinemax.api.module.movie.service.MovieRepository
import com.cleevio.cinemax.api.module.pricecategory.service.PriceCategoryRepository
import com.cleevio.cinemax.api.module.reservation.event.ScreeningWithNonDefaultLayoutSyncedFromMssqlEvent
import com.cleevio.cinemax.api.module.reservation.service.ReservationRepository
import com.cleevio.cinemax.api.module.screening.constant.ScreeningState
import com.cleevio.cinemax.api.module.screening.event.AdminScreeningCreatedEvent
import com.cleevio.cinemax.api.module.screening.event.PublishedScreeningCreatedOrUpdatedEvent
import com.cleevio.cinemax.api.module.screening.event.ScreeningsPublishedEvent
import com.cleevio.cinemax.api.module.screening.event.ScreeningsSwitchedToDraftOrDeletedEvent
import com.cleevio.cinemax.api.module.screening.exception.AuditoriumForScreeningNotFoundException
import com.cleevio.cinemax.api.module.screening.exception.DestinationDateInThePastException
import com.cleevio.cinemax.api.module.screening.exception.MovieForScreeningNotFoundException
import com.cleevio.cinemax.api.module.screening.exception.PriceCategoryForScreeningNotFoundException
import com.cleevio.cinemax.api.module.screening.exception.ScreeningDateTimeConflictException
import com.cleevio.cinemax.api.module.screening.exception.ScreeningDateTimeInThePastException
import com.cleevio.cinemax.api.module.screening.exception.ScreeningInPublishedStateException
import com.cleevio.cinemax.api.module.screening.exception.ScreeningNotFoundException
import com.cleevio.cinemax.api.module.screening.exception.ScreeningsDateTimeConflictException
import com.cleevio.cinemax.api.module.screening.exception.ScreeningsWithTicketsSoldException
import com.cleevio.cinemax.api.module.screening.service.command.CreateOrUpdateScreeningCommand
import com.cleevio.cinemax.api.module.screening.service.command.DeleteScreeningCommand
import com.cleevio.cinemax.api.module.screening.service.command.DuplicateScreeningCommand
import com.cleevio.cinemax.api.module.screening.service.command.DuplicateScreeningDayCommand
import com.cleevio.cinemax.api.module.screening.service.command.MessagingCreateOrUpdateScreeningCommand
import com.cleevio.cinemax.api.module.screening.service.command.UpdateScreeningOriginalIdCommand
import com.cleevio.cinemax.api.module.screening.service.command.UpdateScreeningStatesCommand
import com.cleevio.cinemax.api.module.screeningfee.service.ScreeningFeeJpaFinderService
import com.cleevio.cinemax.api.module.screeningfee.service.ScreeningFeeRepository
import com.cleevio.cinemax.api.module.screeningtype.exception.ScreeningTypeNotFoundException
import com.cleevio.cinemax.api.module.screeningtype.service.D_BOX_SCREENING_TYPE_CODE
import com.cleevio.cinemax.api.module.screeningtype.service.IMAX_SCREENING_TYPE_CODE
import com.cleevio.cinemax.api.module.screeningtype.service.ScreeningTypeRepository
import com.cleevio.cinemax.api.module.screeningtype.service.ULTRA_X_SCREENING_TYPE_CODE
import com.cleevio.cinemax.api.module.screeningtype.service.VIP_SCREENING_TYPE_CODE
import com.cleevio.cinemax.api.module.screeningtypes.service.ScreeningTypesRepository
import com.cleevio.cinemax.api.module.seat.service.SeatRepository
import com.cleevio.cinemax.api.module.ticket.service.TicketRepository
import com.cleevio.cinemax.api.module.ticketprice.service.TicketPriceRepository
import com.cleevio.cinemax.api.util.assertScreeningEquals
import com.cleevio.cinemax.api.util.assertScreeningFeeEquals
import com.cleevio.cinemax.api.util.createAuditorium
import com.cleevio.cinemax.api.util.createAuditoriumDefault
import com.cleevio.cinemax.api.util.createAuditoriumLayout
import com.cleevio.cinemax.api.util.createDistributor
import com.cleevio.cinemax.api.util.createMovie
import com.cleevio.cinemax.api.util.createPriceCategory
import com.cleevio.cinemax.api.util.createReservation
import com.cleevio.cinemax.api.util.createScreening
import com.cleevio.cinemax.api.util.createScreeningFee
import com.cleevio.cinemax.api.util.createScreeningType
import com.cleevio.cinemax.api.util.createScreeningTypes
import com.cleevio.cinemax.api.util.createSeat
import com.cleevio.cinemax.api.util.createTicket
import com.cleevio.cinemax.api.util.createTicketPrice
import com.cleevio.cinemax.api.util.getNextDateForDay
import com.cleevio.cinemax.api.util.mapToCreateOrUpdateScreeningCommand
import com.cleevio.cinemax.api.util.toUUID
import io.mockk.Called
import io.mockk.Runs
import io.mockk.every
import io.mockk.just
import io.mockk.verify
import io.mockk.verifyOrder
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Disabled
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertDoesNotThrow
import org.junit.jupiter.api.assertThrows
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.Arguments
import org.junit.jupiter.params.provider.MethodSource
import org.springframework.beans.factory.annotation.Autowired
import java.math.BigDecimal
import java.time.DayOfWeek
import java.time.LocalDate
import java.time.LocalTime
import java.time.temporal.TemporalAdjusters
import java.util.UUID
import java.util.stream.Stream
import kotlin.test.assertContains
import kotlin.test.assertEquals
import kotlin.test.assertFalse
import kotlin.test.assertNotNull
import kotlin.test.assertNull
import kotlin.test.assertTrue

class ScreeningServiceIT @Autowired constructor(
    private val underTest: ScreeningService,
    private val screeningRepository: ScreeningRepository,
    private val movieRepository: MovieRepository,
    private val priceCategoryRepository: PriceCategoryRepository,
    private val auditoriumLayoutRepository: AuditoriumLayoutRepository,
    private val auditoriumDefaultRepository: AuditoriumDefaultRepository,
    private val screeningFeeRepository: ScreeningFeeRepository,
    private val screeningFeeJpaFinderService: ScreeningFeeJpaFinderService,
    private val screeningTypeRepository: ScreeningTypeRepository,
    private val screeningTypesRepository: ScreeningTypesRepository,
    private val seatRepository: SeatRepository,
    private val reservationsRepository: ReservationRepository,
    private val ticketPriceRepository: TicketPriceRepository,
    private val ticketRepository: TicketRepository,
    private val auditoriumRepository: AuditoriumRepository,
    private val distributorRepository: DistributorRepository,
    private val holidayResolver: HolidayResolver,
) : IntegrationTest(DatabaseCleanup.AFTER_EACH) {

    @BeforeEach
    fun setUp() {
        auditoriumRepository.save(AUDITORIUM_1)
        auditoriumLayoutRepository.save(AUDITORIUM_LAYOUT_1)
        distributorRepository.save(DISTRIBUTOR_1)
        movieRepository.save(MOVIE_1)
        auditoriumDefaultRepository.save(AUDITORIUM_DEFAULT_1)
        priceCategoryRepository.saveAll(
            setOf(
                PRICE_CATEGORY_1,
                PRICE_CATEGORY_2,
                PRICE_CATEGORY_3,
                PRICE_CATEGORY_4,
                PRICE_CATEGORY_5
            )
        )

        every { applicationEventPublisherMock.publishEvent(any<PublishedScreeningCreatedOrUpdatedEvent>()) } just Runs
    }

    @Test
    fun `test syncCreateOrUpdateScreening - create screening with default layout - should create screening`() {
        val command = mapToCreateOrUpdateScreeningCommand(SCREENING_1).copy(id = null)
        underTest.syncCreateOrUpdateScreening(command)

        val createdScreening = screeningRepository.findByOriginalId(SCREENING_1.originalId!!)
        assertNotNull(createdScreening)
        assertEquals(SCREENING_1.originalId, createdScreening.originalId)
        assertScreeningEquals(SCREENING_1, createdScreening)

        verify { applicationEventPublisherMock wasNot Called }
    }

    @Test
    fun `test syncCreateOrUpdateScreening - create screening with non-default layout - should create screening and publish`() {
        auditoriumRepository.save(AUDITORIUM_2)
        auditoriumLayoutRepository.save(AUDITORIUM_LAYOUT_3)

        val command = mapToCreateOrUpdateScreeningCommand(SCREENING_4).copy(id = null)
        underTest.syncCreateOrUpdateScreening(command)

        val createdScreening = screeningRepository.findByOriginalId(SCREENING_4.originalId!!)
        assertNotNull(createdScreening)
        assertEquals(SCREENING_4.originalId, createdScreening.originalId)
        assertScreeningEquals(SCREENING_4, createdScreening)

        verify {
            applicationEventPublisherMock.publishEvent(
                ScreeningWithNonDefaultLayoutSyncedFromMssqlEvent(SCREENING_4.originalId!!)
            )
        }
    }

    @Test
    fun `test syncCreateOrUpdateScreening - one screening exists - insert equal screening so it should update`() {
        val createCommand = mapToCreateOrUpdateScreeningCommand(SCREENING_1).copy(id = null)
        underTest.syncCreateOrUpdateScreening(createCommand)
        val createdScreening = screeningRepository.findAll()[0]

        val updateCommand = mapToCreateOrUpdateScreeningCommand(SCREENING_1).copy(id = createdScreening.id)
        underTest.syncCreateOrUpdateScreening(updateCommand)

        val updatedScreening = screeningRepository.findByOriginalId(SCREENING_1.originalId!!)
        assertNotNull(updatedScreening)
        assertEquals(SCREENING_1.originalId, updatedScreening.originalId)
        assertScreeningEquals(SCREENING_1, updatedScreening)

        val screenings = screeningRepository.findAll()
        assertEquals(screenings.size, 1)
        assertTrue(updatedScreening.updatedAt.isAfter(SCREENING_1.updatedAt))
    }

    @Test
    fun `test syncCreateOrUpdateScreening - two screenings - should create two screenings`() {
        screeningRepository.save(SCREENING_1)

        val command = mapToCreateOrUpdateScreeningCommand(SCREENING_2).copy(id = null)
        underTest.syncCreateOrUpdateScreening(command)

        val screenings = screeningRepository.findAll()
        assertEquals(screenings.size, 2)
        assertScreeningEquals(SCREENING_1, screenings.first { it.originalId == SCREENING_1.originalId })
        assertScreeningEquals(SCREENING_2, screenings.first { it.originalId == SCREENING_2.originalId })
    }

    @Test
    fun `test syncCreateOrUpdateScreening - command with null attribute - entity attr is null`() {
        screeningRepository.save(SCREENING_2)

        val createdScreening = screeningRepository.findByOriginalId(SCREENING_2.originalId!!)
        assertNotNull(createdScreening)
        assertEquals(SCREENING_2.originalId, createdScreening.originalId)
        assertScreeningEquals(SCREENING_2, createdScreening)

        val command = mapToCreateOrUpdateScreeningCommand(SCREENING_2).copy(adTimeSlot = 5)
        underTest.syncCreateOrUpdateScreening(command)

        val updatedScreening = screeningRepository.findByOriginalId(SCREENING_2.originalId!!)
        assertNotNull(updatedScreening)
        assertEquals(SCREENING_2.originalId, updatedScreening.originalId)
        assertEquals(SCREENING_2.auditoriumId, updatedScreening.auditoriumId)
        assertEquals(SCREENING_2.movieId, updatedScreening.movieId)
        assertEquals(SCREENING_2.date, updatedScreening.date)
        assertEquals(SCREENING_2.time.withNano(0), updatedScreening.time.withNano(0))
        assertEquals(SCREENING_2.saleTimeLimit, updatedScreening.saleTimeLimit)
        assertNotNull(updatedScreening.createdAt)
        assertNotNull(updatedScreening.updatedAt)
        assertTrue(updatedScreening.updatedAt.isAfter(SCREENING_2.updatedAt))
    }

    @Test
    fun `test syncCreateOrUpdateScreening - exists deleted by originalId - should not update screening`() {
        val command = mapToCreateOrUpdateScreeningCommand(SCREENING_1).copy(id = null)
        underTest.syncCreateOrUpdateScreening(command)

        val createdScreening = screeningRepository.findByOriginalId(SCREENING_1.originalId!!)
        assertNotNull(createdScreening)
        assertEquals(SCREENING_1.originalId, createdScreening.originalId)
        assertScreeningEquals(SCREENING_1, createdScreening)

        underTest.updateScreeningStates(UpdateScreeningStatesCommand(ScreeningState.DRAFT, setOf(createdScreening.id)))
        underTest.deleteScreening(DeleteScreeningCommand(createdScreening.id))

        val deletedScreening = screeningRepository.findByOriginalId(SCREENING_1.originalId!!)
        assertNotNull(deletedScreening)
        assertTrue(deletedScreening.isDeleted())

        val updateCommand = mapToCreateOrUpdateScreeningCommand(SCREENING_2)
            .copy(originalId = SCREENING_1.originalId!!)
        underTest.syncCreateOrUpdateScreening(updateCommand)

        val notUpdatedScreening = screeningRepository.findByOriginalId(SCREENING_1.originalId!!)
        assertNotNull(notUpdatedScreening)
        assertScreeningEquals(SCREENING_1, notUpdatedScreening)
        assertTrue(notUpdatedScreening.isDeleted())
    }

    @Test
    fun `test syncCreateOrUpdateScreening - command with not existing auditorium - should throw exception`() {
        val command = mapToCreateOrUpdateScreeningCommand(SCREENING_1)
        val commandWithNotExistingAuditorium = command.copy(
            auditoriumId = UUID.randomUUID()
        )
        assertThrows<AuditoriumForScreeningNotFoundException> {
            underTest.syncCreateOrUpdateScreening(commandWithNotExistingAuditorium)
        }
    }

    @Test
    fun `test syncCreateOrUpdateScreening - command with not existing movie - should throw exception`() {
        val command = mapToCreateOrUpdateScreeningCommand(SCREENING_1)
        val commandWithNotExistingMovie = command.copy(
            movieId = UUID.randomUUID()
        )
        assertThrows<MovieForScreeningNotFoundException> {
            underTest.syncCreateOrUpdateScreening(commandWithNotExistingMovie)
        }
    }

    @Test
    fun `test syncCreateOrUpdateScreening - command with not existing price category - should throw exception`() {
        val command = mapToCreateOrUpdateScreeningCommand(SCREENING_1)
        val commandWithNotExistingPriceCategory = command.copy(
            priceCategoryId = UUID.randomUUID()
        )
        assertThrows<PriceCategoryForScreeningNotFoundException> {
            underTest.syncCreateOrUpdateScreening(commandWithNotExistingPriceCategory)
        }
    }

    @Test
    fun `test adminCreateOrUpdateScreening - published screening datetime is in past - should throw`() {
        val commandWithDateTimeInPast = mapToCreateOrUpdateScreeningCommand(SCREENING_1).copy(
            id = null,
            date = INTEGRATION_TEST_DATE_TIME.toLocalDate(),
            time = INTEGRATION_TEST_DATE_TIME.toLocalTime().minusMinutes(1)
        )

        assertThrows<ScreeningDateTimeInThePastException> {
            underTest.adminCreateOrUpdateScreening(commandWithDateTimeInPast)
        }
    }

    @Test
    fun `test adminCreateOrUpdateScreening - updating modifiable attribute of past published screening - shouldn't throw`() {
        screeningRepository.save(SCREENING_5)
        val command = mapToCreateOrUpdateScreeningCommand(SCREENING_5).copy(
            adTimeSlot = 30
        )

        assertDoesNotThrow {
            underTest.adminCreateOrUpdateScreening(command)
        }
    }

    @Test
    fun `test adminCreateOrUpdateScreening - updating non-modifiable attribute of past screening - should throw`() {
        screeningRepository.save(SCREENING_5)
        val command = mapToCreateOrUpdateScreeningCommand(SCREENING_5).copy(
            time = LocalTime.of(22, 0)
        )

        assertThrows<ScreeningInPublishedStateException> {
            underTest.adminCreateOrUpdateScreening(command)
        }
    }

    @Test
    fun `test adminCreateOrUpdateScreening - create published screening with datetime conflict - should throw`() {
        screeningRepository.save(SCREENING_1)
        val conflictingCommand = mapToCreateOrUpdateScreeningCommand(SCREENING_1).copy(
            id = null,
            originalId = null
        )
        assertThrows<ScreeningDateTimeConflictException> {
            underTest.adminCreateOrUpdateScreening(conflictingCommand)
        }
    }

    @Test
    fun `test adminCreateOrUpdateScreening - update published screening with datetime conflict - should throw`() {
        screeningRepository.saveAll(setOf(SCREENING_1, SCREENING_2))
        val conflictingCommand = mapToCreateOrUpdateScreeningCommand(SCREENING_2).copy(
            date = SCREENING_1.date,
            time = SCREENING_1.time,
            state = null
        )
        assertThrows<ScreeningDateTimeConflictException> {
            underTest.adminCreateOrUpdateScreening(conflictingCommand)
        }
    }

    @Test
    fun `test adminCreateOrUpdateScreening - move draft screening to conflict with published screening - shouldn't throw`() {
        screeningRepository.saveAll(setOf(SCREENING_1, SCREENING_3))
        val conflictingCommand = mapToCreateOrUpdateScreeningCommand(SCREENING_3).copy(
            date = SCREENING_1.date,
            time = SCREENING_1.time,
            state = null
        )
        assertDoesNotThrow {
            underTest.adminCreateOrUpdateScreening(conflictingCommand)
        }
    }

    @Test
    fun `test adminCreateOrUpdateScreening - draft screening datetime validation - shouldn't throw`() {
        screeningRepository.save(SCREENING_1)

        val commandWithDateTimeInPast = mapToCreateOrUpdateScreeningCommand(SCREENING_2).copy(
            id = null,
            originalId = null,
            time = INTEGRATION_TEST_DATE_TIME.toLocalTime().minusMinutes(1),
            state = ScreeningState.DRAFT
        )
        assertDoesNotThrow { underTest.adminCreateOrUpdateScreening(commandWithDateTimeInPast) }

        val conflictingCommand = mapToCreateOrUpdateScreeningCommand(SCREENING_2).copy(
            id = null,
            originalId = null,
            date = SCREENING_1.date,
            time = SCREENING_1.time,
            state = ScreeningState.DRAFT
        )
        assertDoesNotThrow {
            underTest.adminCreateOrUpdateScreening(conflictingCommand)
        }
        verify(exactly = 0) { applicationEventPublisherMock.publishEvent(any()) }
    }

    @Test
    fun `test adminCreateOrUpdateScreening - screening is moved to another auditorium - should correctly resolve new auditorium's supported screening types`() {
        auditoriumRepository.save(AUDITORIUM_2)
        auditoriumLayoutRepository.save(AUDITORIUM_LAYOUT_2)
        auditoriumDefaultRepository.save(AUDITORIUM_DEFAULT_3)
        screeningTypeRepository.save(SCREENING_TYPE_1)
        screeningTypeRepository.saveAll(
            setOf(
                SCREENING_TYPE_CUSTOM,
                SCREENING_TYPE_D_BOX,
                SCREENING_TYPE_VIP,
                SCREENING_TYPE_IMAX,
                SCREENING_TYPE_ULTRA_X
            )
        )

        val command = mapToCreateOrUpdateScreeningCommand(
            screening = SCREENING_1,
            screeningTypeIds = setOf(SCREENING_TYPE_CUSTOM.id, SCREENING_TYPE_VIP.id)
        ).copy(id = null, originalId = null, state = ScreeningState.DRAFT)

        underTest.adminCreateOrUpdateScreening(command)

        val createdScreening = screeningRepository.findAll()[0]
        val screeningTypes = screeningTypesRepository.findAllByScreeningId(screeningId = createdScreening.id)
        assertEquals(
            screeningTypes.map { it.screeningTypeId }.toSet(),
            setOf(SCREENING_TYPE_CUSTOM.id, SCREENING_TYPE_VIP.id)
        )

        underTest.adminCreateOrUpdateScreening(
            command.copy(
                id = createdScreening.id,
                auditoriumId = AUDITORIUM_2.id
            )
        )

        assertEquals(1, screeningRepository.findAll().size)

        val updatedScreening = screeningRepository.findAll()[0]
        val updatedScreeningTypes = screeningTypesRepository.findAllByScreeningId(screeningId = updatedScreening.id)
        assertEquals(
            setOf(
                SCREENING_TYPE_CUSTOM.id,
                SCREENING_TYPE_IMAX.id,
                SCREENING_TYPE_ULTRA_X.id
            ),
            updatedScreeningTypes.map { it.screeningTypeId }.toSet()
        )
    }

    @Test
    fun `test updateScreeningStates - cancel published screening, publish draft screening to conflict with it - shouldn't throw`() {
        priceCategoryRepository.save(PRICE_CATEGORY_6)
        screeningRepository.saveAll(setOf(SCREENING_7, SCREENING_2))

        val cancelCommand = mapToCreateOrUpdateScreeningCommand(SCREENING_2).copy(
            cancelled = true
        )
        underTest.adminCreateOrUpdateScreening(cancelCommand)

        val dateTimeCommand = mapToCreateOrUpdateScreeningCommand(SCREENING_7).copy(
            date = SCREENING_2.date,
            time = SCREENING_2.time
        )
        underTest.adminCreateOrUpdateScreening(dateTimeCommand)

        assertDoesNotThrow {
            underTest.updateScreeningStates(
                UpdateScreeningStatesCommand(
                    screeningState = ScreeningState.PUBLISHED,
                    screeningIds = setOf(SCREENING_7.id)
                )
            )
        }
    }

    @Test
    fun `test updateScreeningStates - stop published screening, move other DRAFT to conflict with it an publish it - shouldn't throw`() {
        screeningRepository.saveAll(setOf(SCREENING_1, SCREENING_2))

        val stopCommand = mapToCreateOrUpdateScreeningCommand(SCREENING_2).copy(
            stopped = true
        )
        underTest.adminCreateOrUpdateScreening(stopCommand)

        underTest.updateScreeningStates(UpdateScreeningStatesCommand(ScreeningState.DRAFT, setOf(SCREENING_1.id)))

        val dateTimeCommand = mapToCreateOrUpdateScreeningCommand(SCREENING_1).copy(
            date = SCREENING_2.date,
            time = SCREENING_2.time,
            state = null
        )
        underTest.adminCreateOrUpdateScreening(dateTimeCommand)

        assertDoesNotThrow {
            underTest.updateScreeningStates(
                UpdateScreeningStatesCommand(
                    ScreeningState.PUBLISHED,
                    setOf(SCREENING_1.id)
                )
            )
        }
    }

    @Test
    fun `test updateScreeningStates - cancel screening, movie it to conflict with published screening - shouldn't throw`() {
        screeningRepository.saveAll(setOf(SCREENING_1, SCREENING_2))

        val cancelCommand = mapToCreateOrUpdateScreeningCommand(SCREENING_2).copy(
            cancelled = true
        )
        underTest.adminCreateOrUpdateScreening(cancelCommand)

        underTest.updateScreeningStates(UpdateScreeningStatesCommand(ScreeningState.DRAFT, setOf(SCREENING_2.id)))

        val dateTimeCommand = cancelCommand.copy(
            date = SCREENING_1.date,
            time = SCREENING_1.time,
            state = null
        )
        underTest.adminCreateOrUpdateScreening(dateTimeCommand)

        assertDoesNotThrow {
            underTest.updateScreeningStates(
                UpdateScreeningStatesCommand(
                    ScreeningState.PUBLISHED,
                    setOf(SCREENING_2.id)
                )
            )
        }
    }

    @Test
    fun `test updateScreeningStates - stop published screening, draft it and move it to conflict with stopped, publish again - shouldn't throw`() {
        screeningRepository.saveAll(setOf(SCREENING_1, SCREENING_2))

        val stopCommand = mapToCreateOrUpdateScreeningCommand(SCREENING_2).copy(
            stopped = true
        )
        underTest.adminCreateOrUpdateScreening(stopCommand)

        underTest.updateScreeningStates(UpdateScreeningStatesCommand(ScreeningState.DRAFT, setOf(SCREENING_2.id)))

        val dateTimeCommand = stopCommand.copy(
            date = SCREENING_1.date,
            time = SCREENING_1.time,
            state = null
        )
        underTest.adminCreateOrUpdateScreening(dateTimeCommand)

        assertDoesNotThrow {
            underTest.updateScreeningStates(
                UpdateScreeningStatesCommand(
                    ScreeningState.PUBLISHED,
                    setOf(SCREENING_2.id)
                )
            )
        }
    }

    @Test
    fun `test updateScreeningStates - un-stop stopped conflicting DRAFT screening, publish it - should throw`() {
        screeningRepository.saveAll(setOf(SCREENING_1, SCREENING_3))

        val stopCommand = mapToCreateOrUpdateScreeningCommand(SCREENING_3).copy(
            stopped = true,
            date = SCREENING_1.date,
            time = SCREENING_1.time
        )
        underTest.adminCreateOrUpdateScreening(stopCommand)

        val unstopCommand = stopCommand.copy(stopped = false)
        underTest.adminCreateOrUpdateScreening(unstopCommand)

        assertThrows<ScreeningsDateTimeConflictException> {
            underTest.updateScreeningStates(
                UpdateScreeningStatesCommand(
                    ScreeningState.PUBLISHED,
                    setOf(SCREENING_3.id)
                )
            )
        }
    }

    @Test
    fun `test adminCreateOrUpdateScreening - un-cancel cancelled conflicting screening - should throw`() {
        screeningRepository.saveAll(setOf(SCREENING_1, SCREENING_3))

        val cancelCommand = mapToCreateOrUpdateScreeningCommand(SCREENING_3).copy(
            cancelled = true,
            date = SCREENING_1.date,
            time = SCREENING_1.time
        )
        underTest.adminCreateOrUpdateScreening(cancelCommand)

        underTest.updateScreeningStates(UpdateScreeningStatesCommand(ScreeningState.PUBLISHED, setOf(SCREENING_3.id)))

        val uncancelCommand = cancelCommand.copy(
            cancelled = false,
            state = null
        )
        assertThrows<ScreeningDateTimeConflictException> {
            underTest.adminCreateOrUpdateScreening(uncancelCommand)
        }
    }

    @Test
    fun `test adminCreateOrUpdateScreening - auditorium does not exist - should throw`() {
        val command = mapToCreateOrUpdateScreeningCommand(SCREENING_1).copy(
            id = null,
            originalId = null,
            auditoriumId = UUID.fromString("36e3fa95-4bb7-4260-9bfd-0b68c06a2848")
        )
        assertThrows<AuditoriumForScreeningNotFoundException> {
            underTest.adminCreateOrUpdateScreening(command)
        }
    }

    @Test
    fun `test adminCreateOrUpdateScreening - auditorium layout does not exist - should throw`() {
        val command = mapToCreateOrUpdateScreeningCommand(SCREENING_1).copy(
            id = null,
            originalId = null,
            auditoriumLayoutId = UUID.fromString("36e3fa95-4bb7-4260-9bfd-0b68c06a2848")
        )
        assertThrows<AuditoriumLayoutNotFoundException> {
            underTest.adminCreateOrUpdateScreening(command)
        }
    }

    @Test
    fun `test adminCreateOrUpdateScreening - movie does not exist - should throw`() {
        val command = mapToCreateOrUpdateScreeningCommand(SCREENING_1).copy(
            id = null,
            originalId = null,
            movieId = UUID.fromString("36e3fa95-4bb7-4260-9bfd-0b68c06a2848")
        )
        assertThrows<MovieForScreeningNotFoundException> {
            underTest.adminCreateOrUpdateScreening(command)
        }
    }

    @Test
    fun `test adminCreateOrUpdateScreening - screening types do not exist - should throw`() {
        val command = mapToCreateOrUpdateScreeningCommand(SCREENING_1).copy(
            id = null,
            originalId = null,
            screeningTypeIds = setOf(UUID.fromString("36e3fa95-4bb7-4260-9bfd-0b68c06a2848"))
        )
        assertThrows<ScreeningTypeNotFoundException> {
            underTest.adminCreateOrUpdateScreening(command)
        }
    }

    @Test
    fun `test adminCreateOrUpdateScreening - screening does not exist - should create new one with new fee and types`() {
        screeningTypeRepository.saveAll(setOf(SCREENING_TYPE_1, SCREENING_TYPE_2))

        val command = mapToCreateOrUpdateScreeningCommand(
            SCREENING_2,
            SCREENING_FEE_2,
            setOf(SCREENING_TYPE_1.id)
        ).copy(id = null, originalId = null)

        underTest.adminCreateOrUpdateScreening(command)

        val createdScreening = screeningRepository.findAll()[0]
        assertNotNull(createdScreening)
        assertScreeningEquals(SCREENING_2, createdScreening)

        val createdScreeningFee = screeningFeeRepository.findAll()[0]
        assertEquals(createdScreening.id, createdScreeningFee.screeningId)
        assertScreeningFeeEquals(SCREENING_FEE_2, createdScreeningFee)

        val createdScreeningTypes = screeningTypesRepository.findAll()
        assertEquals(1, createdScreeningTypes.size)
        assertTrue(
            createdScreeningTypes[0].let {
                it.screeningId == createdScreening.id && it.screeningTypeId == SCREENING_TYPE_1.id
            }
        )

        verify {
            applicationEventPublisherMock.publishEvent(
                PublishedScreeningCreatedOrUpdatedEvent(
                    screeningId = createdScreening.id
                )
            )
        }
        verify { applicationEventPublisherMock.publishEvent(any(AdminScreeningCreatedEvent::class)) }
    }

    @Test
    fun `test adminCreateOrUpdateScreening - screening does exist - should update existing one with updated fee and types`() {
        screeningTypeRepository.saveAll(setOf(SCREENING_TYPE_1, SCREENING_TYPE_2))
        screeningRepository.save(SCREENING_1)
        screeningFeeRepository.save(SCREENING_FEE_1)
        screeningTypesRepository.save(SCREENING_TYPES_1)

        assertEquals(SCREENING_1.id, screeningRepository.findAll()[0].id)
        assertEquals(SCREENING_FEE_1.id, screeningFeeRepository.findAll()[0].id)
        assertEquals(SCREENING_1.id, screeningFeeRepository.findAll()[0].screeningId)
        assertEquals(SCREENING_TYPE_1.id, screeningTypesRepository.findAll()[0].screeningTypeId)
        assertEquals(SCREENING_1.id, screeningTypesRepository.findAll()[0].screeningId)

        val command = mapToCreateOrUpdateScreeningCommand(SCREENING_1, SCREENING_FEE_2, setOf(SCREENING_TYPE_2.id))
            .copy(distributorCommission = 75)
        underTest.adminCreateOrUpdateScreening(command)

        val updatedScreening = screeningRepository.findAll()[0]
        assertNotNull(updatedScreening)
        assertEquals(75, updatedScreening.distributorCommission)

        assertEquals(SCREENING_1.auditoriumId, updatedScreening.auditoriumId)

        val updatedScreeningFee = screeningFeeRepository.findAll()[0]
        assertEquals(updatedScreening.id, updatedScreeningFee.screeningId)
        assertScreeningFeeEquals(SCREENING_FEE_2, updatedScreeningFee)

        val updatedScreeningTypes = screeningTypesRepository.findAll()
        assertEquals(1, updatedScreeningTypes.size)
        assertTrue(
            updatedScreeningTypes[0].let {
                it.screeningId == updatedScreening.id && it.screeningTypeId == SCREENING_TYPE_2.id
            }
        )

        verify {
            applicationEventPublisherMock.publishEvent(
                PublishedScreeningCreatedOrUpdatedEvent(
                    screeningId = updatedScreening.id
                )
            )
        }
        verify(exactly = 0) {
            applicationEventPublisherMock.publishEvent(
                AdminScreeningCreatedEvent(
                    screeningId = updatedScreening.id,
                    auditoriumLayoutId = command.auditoriumLayoutId
                )
            )
        }
    }

    @ParameterizedTest
    @MethodSource("validUpdateDraftScreeningCommandsProvider")
    fun `test adminCreateOrUpdateScreening - update screening with sold tickets - should not throw exception`(
        command: CreateOrUpdateScreeningCommand,
    ) {
        prepareAdminCreateOrUpdateScreeningTestData()

        assertDoesNotThrow {
            underTest.adminCreateOrUpdateScreening(command)
        }
    }

    @ParameterizedTest
    @MethodSource("invalidUpdateScreeningCommandsProvider")
    fun `test adminCreateOrUpdateScreening - update screening - should throw exception`(
        command: CreateOrUpdateScreeningCommand,
    ) {
        prepareAdminCreateOrUpdateScreeningTestData()

        assertThrows<ScreeningInPublishedStateException> {
            underTest.adminCreateOrUpdateScreening(command)
        }
    }

    @Test
    fun `test updateScreeningOriginalId - should correctly update in db`() {
        underTest.adminCreateOrUpdateScreening(
            mapToCreateOrUpdateScreeningCommand(SCREENING_1).copy(id = null, originalId = null)
        )
        val screening1 = screeningRepository.findAll()[0]
        assertNull(screeningRepository.findById(screening1.id).get().originalId)

        underTest.updateScreeningOriginalId(
            UpdateScreeningOriginalIdCommand(
                screeningId = screening1.id,
                originalId = 5
            )
        )

        assertEquals(5, screeningRepository.findById(screening1.id).get().originalId)
    }

    @ParameterizedTest
    @MethodSource("validUpdatePublishedScreeningCommandsProvider")
    fun `test adminCreateOrUpdateScreening - update of modifiable attribute in PUBLISHED screening - should not throw`(
        command: CreateOrUpdateScreeningCommand,
    ) {
        prepareAdminCreateOrUpdateScreeningTestData()

        assertDoesNotThrow { underTest.adminCreateOrUpdateScreening(command) }
    }

    @Test
    fun `test adminCreateOrUpdateScreening - screening is updated to another auditorium - should inherit auditorium's default fees`() {
        auditoriumRepository.save(AUDITORIUM_2)
        auditoriumLayoutRepository.save(AUDITORIUM_LAYOUT_2)
        auditoriumDefaultRepository.save(AUDITORIUM_DEFAULT_2)
        screeningTypeRepository.save(SCREENING_TYPE_1)
        priceCategoryRepository.save(PRICE_CATEGORY_6)

        val command = mapToCreateOrUpdateScreeningCommand(
            SCREENING_7,
            SCREENING_FEE_4,
            setOf(SCREENING_TYPE_1.id)
        ).copy(id = null, originalId = null)

        underTest.adminCreateOrUpdateScreening(command)

        val createdScreening = screeningRepository.findAll()[0]
        val createdScreeningFee = screeningFeeRepository.findByScreeningId(createdScreening.id)!!
        assertScreeningFeeEquals(SCREENING_FEE_4, createdScreeningFee)

        underTest.adminCreateOrUpdateScreening(
            mapToCreateOrUpdateScreeningCommand(
                createdScreening,
                SCREENING_FEE_4,
                setOf(SCREENING_TYPE_1.id)
            ).copy(auditoriumId = AUDITORIUM_2.id)
        )

        assertEquals(1, screeningRepository.findAll().size)
        val updatedScreening = screeningRepository.findAll()[0]

        val updatedScreeningFee = screeningFeeRepository.findByScreeningId(updatedScreening.id)!!
        assertScreeningFeeEquals(
            AUDITORIUM_DEFAULT_2.toScreeningFee(updatedScreening.id, SCREENING_FEE_4.serviceFeeGeneral),
            updatedScreeningFee
        )
    }

    @Test
    fun `test adminCreateOrUpdateScreening - screening is updated over the 17h mark - should auto select correct price category`() {
        priceCategoryRepository.save(PRICE_CATEGORY_6)
        priceCategoryRepository.saveAll(setOf(PRICE_CATEGORY_7, PRICE_CATEGORY_8)) // these added just to check autoselect logic does not count with non-active categories

        val command = mapToCreateOrUpdateScreeningCommand(
            SCREENING_7,
            SCREENING_FEE_1
        ).copy(id = null, originalId = null)

        underTest.adminCreateOrUpdateScreening(command)

        val createdScreening = screeningRepository.findAll()[0]
        assertEquals(PRICE_CATEGORY_6.id, createdScreening.priceCategoryId)

        underTest.adminCreateOrUpdateScreening(
            mapToCreateOrUpdateScreeningCommand(
                createdScreening,
                SCREENING_FEE_1
            ).copy(time = LocalTime.of(16, 0))
        )
        assertEquals(1, screeningRepository.findAll().size)

        val updatedScreening = screeningRepository.findAll()[0]
        assertEquals(
            if (holidayResolver.isNationalHoliday(updatedScreening.date)) PRICE_CATEGORY_4.id else PRICE_CATEGORY_5.id,
            updatedScreening.priceCategoryId
        )

        underTest.adminCreateOrUpdateScreening(
            mapToCreateOrUpdateScreeningCommand(
                createdScreening,
                SCREENING_FEE_1
            ).copy(time = LocalTime.of(19, 0))
        )
        assertEquals(1, screeningRepository.findAll().size)

        val twiceUpdatedScreening = screeningRepository.findAll()[0]
        assertEquals(PRICE_CATEGORY_4.id, twiceUpdatedScreening.priceCategoryId)
    }

    @Test
    fun `test adminCreateOrUpdateScreening - screening time is exactly 17h but has special price category - should not auto select price category`() {
        priceCategoryRepository.save(PRICE_CATEGORY_6)

        val command = mapToCreateOrUpdateScreeningCommand(
            SCREENING_7,
            SCREENING_FEE_1
        ).copy(id = null, originalId = null, time = LocalTime.of(17, 0))

        underTest.adminCreateOrUpdateScreening(command)

        val createdScreening = screeningRepository.findAll()[0]
        assertEquals(PRICE_CATEGORY_6.id, createdScreening.priceCategoryId)

        underTest.adminCreateOrUpdateScreening(
            command.copy(id = createdScreening.id, saleTimeLimit = 60)
        )
        assertEquals(1, screeningRepository.findAll().size)

        val updatedScreening = screeningRepository.findAll()[0]
        assertEquals(PRICE_CATEGORY_6.id, updatedScreening.priceCategoryId)
    }

    @Test
    fun `test adminCreateOrUpdateScreening - screening time is exactly 17h but doesn't have special price category - should auto select price category`() {
        val command = mapToCreateOrUpdateScreeningCommand(
            SCREENING_7,
            SCREENING_FEE_1
        ).copy(
            id = null,
            originalId = null,
            time = LocalTime.of(17, 0),
            // incorrect "do 17h" category, but not "special"
            priceCategoryId = PRICE_CATEGORY_5.id
        )

        underTest.adminCreateOrUpdateScreening(command)

        val createdScreening = screeningRepository.findAll()[0]
        assertEquals(PRICE_CATEGORY_5.id, createdScreening.priceCategoryId)

        underTest.adminCreateOrUpdateScreening(
            command.copy(id = createdScreening.id, saleTimeLimit = 60)
        )
        assertEquals(1, screeningRepository.findAll().size)

        val updatedScreening = screeningRepository.findAll()[0]
        assertEquals(PRICE_CATEGORY_4.id, updatedScreening.priceCategoryId)
    }

    @Test
    fun `test duplicateScreening - related entities exist - should duplicate screening and create screening fee and types`() {
        auditoriumRepository.save(AUDITORIUM_2)
        auditoriumLayoutRepository.save(AUDITORIUM_LAYOUT_2)
        screeningTypeRepository.save(SCREENING_TYPE_1)

        val command = mapToCreateOrUpdateScreeningCommand(
            SCREENING_1,
            SCREENING_FEE_1,
            setOf(SCREENING_TYPE_1.id)
        ).copy(id = null, originalId = null)

        underTest.adminCreateOrUpdateScreening(command)

        val createdScreening = screeningRepository.findAll()[0]
        assertNotNull(createdScreening)
        assertScreeningEquals(SCREENING_1, createdScreening)

        val createdScreeningFee = screeningFeeJpaFinderService.getByScreeningId(createdScreening.id)
        assertScreeningFeeEquals(SCREENING_FEE_1, createdScreeningFee)

        val createdScreeningTypes = screeningTypesRepository.findAllByScreeningId(createdScreening.id)
        assertEquals(1, createdScreeningTypes.size)
        assertTrue(
            createdScreeningTypes[0].let {
                it.screeningId == createdScreening.id && it.screeningTypeId == SCREENING_TYPE_1.id
            }
        )

        // FIRST DUPLICATION
        val duplicateTime = LocalTime.of(22, 0)
        underTest.duplicateScreening(
            DuplicateScreeningCommand(
                screeningId = createdScreening.id,
                auditoriumId = AUDITORIUM_1.id,
                time = duplicateTime
            )
        )
        assertEquals(2, screeningRepository.findAll().size)

        // assert that duplicated screening has correct attributes
        val duplicatedScreening = screeningRepository.findAll().sortedBy { it.createdAt }[1]
        assertScreeningEquals(
            createdScreening.apply {
                auditoriumId = AUDITORIUM_1.id
                time = duplicateTime
                state = ScreeningState.DRAFT
                auditoriumLayoutId = AUDITORIUM_LAYOUT_1.id
            },
            duplicatedScreening
        )

        // check that source screening (createdScreening) is not modified by screening duplication
        val createdScreeningCheck = screeningRepository.findById(createdScreening.id).get()
        assertEquals(AUDITORIUM_1.id, createdScreeningCheck.auditoriumId)
        assertEquals(SCREENING_1.time, createdScreeningCheck.time)
        assertEquals(SCREENING_1.state, createdScreeningCheck.state)
        assertEquals(AUDITORIUM_LAYOUT_1.id, createdScreeningCheck.auditoriumLayoutId)

        // SECOND DUPLICATION
        underTest.duplicateScreening(
            DuplicateScreeningCommand(
                screeningId = duplicatedScreening.id,
                auditoriumId = SCREENING_1.auditoriumId,
                time = SCREENING_1.time
            )
        )
        assertEquals(3, screeningRepository.findAll().size)

        // assert that duplicated screening has correct attributes
        val duplicatedDuplicatedScreening = screeningRepository.findAll().sortedBy { it.createdAt }[2]
        assertScreeningEquals(
            createScreening(
                originalId = SCREENING_1.originalId!!,
                auditoriumId = SCREENING_1.auditoriumId,
                auditoriumLayoutId = SCREENING_1.auditoriumLayoutId,
                movieId = SCREENING_1.movieId,
                priceCategoryId = SCREENING_1.priceCategoryId,
                proCommission = SCREENING_1.proCommission,
                filmFondCommission = SCREENING_1.filmFondCommission,
                distributorCommission = SCREENING_1.distributorCommission,
                publishOnline = SCREENING_1.publishOnline,
                date = SCREENING_1.date,
                time = SCREENING_1.time,
                saleTimeLimit = SCREENING_1.saleTimeLimit,
                state = ScreeningState.DRAFT
            ),
            duplicatedDuplicatedScreening
        )

        // check that source screening (duplicateScreening) is not modified by screening duplication
        val duplicatedScreeningCheck = screeningRepository.findByIdAndDeletedAtIsNull(duplicatedScreening.id)!!
        assertEquals(AUDITORIUM_1.id, duplicatedScreeningCheck.auditoriumId)
        assertEquals(duplicateTime, duplicatedScreeningCheck.time)
        assertEquals(ScreeningState.DRAFT, duplicatedScreeningCheck.state)
        assertEquals(AUDITORIUM_LAYOUT_1.id, duplicatedScreeningCheck.auditoriumLayoutId)

        setOf(duplicatedScreening, duplicatedDuplicatedScreening).forEach { screening ->
            val duplicatedScreeningFee = screeningFeeJpaFinderService.getByScreeningId(screening.id)
            assertScreeningFeeEquals(SCREENING_FEE_1, duplicatedScreeningFee)

            val duplicatedScreeningTypes = screeningTypesRepository.findAllByScreeningId(screening.id)
            assertEquals(1, duplicatedScreeningTypes.size)
            assertTrue(
                duplicatedScreeningTypes[0].let {
                    it.screeningId == screening.id && it.screeningTypeId == SCREENING_TYPE_1.id
                }
            )
        }
    }

    @Test
    fun `test duplicateScreening - screening is duplicated over the 17h mark - should auto select correct price category`() {
        screeningTypeRepository.save(SCREENING_TYPE_1)
        priceCategoryRepository.saveAll(setOf(PRICE_CATEGORY_7, PRICE_CATEGORY_8)) // these added just to check autoselect logic does not count with non-active categories

        val command = mapToCreateOrUpdateScreeningCommand(
            SCREENING_1,
            SCREENING_FEE_1,
            setOf(SCREENING_TYPE_1.id)
        ).copy(id = null, originalId = null)

        underTest.adminCreateOrUpdateScreening(command)

        val createdScreening = screeningRepository.findAll()[0]
        assertEquals(PRICE_CATEGORY_4.id, createdScreening.priceCategoryId)

        underTest.duplicateScreening(
            DuplicateScreeningCommand(
                screeningId = createdScreening.id,
                auditoriumId = AUDITORIUM_1.id,
                time = LocalTime.of(16, 0)
            )
        )
        assertEquals(2, screeningRepository.findAll().size)

        val duplicatedScreening = screeningRepository.findAll().sortedBy { it.createdAt }[1]
        assertEquals(
            if (holidayResolver.isNationalHoliday(duplicatedScreening.date)) PRICE_CATEGORY_4.id else PRICE_CATEGORY_5.id,
            duplicatedScreening.priceCategoryId
        )

        underTest.duplicateScreening(
            DuplicateScreeningCommand(
                screeningId = duplicatedScreening.id,
                auditoriumId = AUDITORIUM_1.id,
                time = LocalTime.of(19, 0)
            )
        )
        assertEquals(3, screeningRepository.findAll().size)

        val twiceDuplicatedScreening = screeningRepository.findAll().sortedBy { it.createdAt }[2]
        assertEquals(PRICE_CATEGORY_4.id, twiceDuplicatedScreening.priceCategoryId)
    }

    @Test
    fun `test duplicateScreening - screening is duplicated to another auditorium - should inherit auditorium's default fees`() {
        auditoriumRepository.save(AUDITORIUM_2)
        auditoriumLayoutRepository.save(AUDITORIUM_LAYOUT_2)
        auditoriumDefaultRepository.save(AUDITORIUM_DEFAULT_2)
        screeningTypeRepository.save(SCREENING_TYPE_1)

        val command = mapToCreateOrUpdateScreeningCommand(
            SCREENING_1,
            SCREENING_FEE_1,
            setOf(SCREENING_TYPE_1.id)
        ).copy(id = null, originalId = null)

        underTest.adminCreateOrUpdateScreening(command)

        val createdScreening = screeningRepository.findAll()[0]
        val createdScreeningFee = screeningFeeRepository.findByScreeningId(createdScreening.id)!!
        assertScreeningFeeEquals(SCREENING_FEE_1, createdScreeningFee)

        underTest.duplicateScreening(
            DuplicateScreeningCommand(
                screeningId = createdScreening.id,
                auditoriumId = AUDITORIUM_2.id,
                time = LocalTime.of(20, 0)
            )
        )
        assertEquals(2, screeningRepository.findAll().size)

        val duplicatedScreening = screeningRepository.findAll().sortedBy { it.createdAt }[1]
        val duplicatedScreeningFee = screeningFeeRepository.findByScreeningId(duplicatedScreening.id)!!
        assertScreeningFeeEquals(
            AUDITORIUM_DEFAULT_2.toScreeningFee(duplicatedScreening.id, SCREENING_FEE_1.serviceFeeGeneral),
            duplicatedScreeningFee
        )
    }

    @Test
    fun `test duplicateScreening - duplicated to the same auditorium with non-default layout - should keep auditorium layout`() {
        auditoriumRepository.save(AUDITORIUM_2)
        auditoriumLayoutRepository.save(AUDITORIUM_LAYOUT_3)

        val command = mapToCreateOrUpdateScreeningCommand(
            SCREENING_4,
            SCREENING_FEE_2,
            null
        ).copy(id = null, originalId = null)

        underTest.adminCreateOrUpdateScreening(command)

        val createdScreening = screeningRepository.findAll()[0]
        assertNotNull(createdScreening)
        assertScreeningEquals(SCREENING_4, createdScreening)

        val createdScreeningFee = screeningFeeJpaFinderService.getByScreeningId(createdScreening.id)
        assertScreeningFeeEquals(SCREENING_FEE_2, createdScreeningFee)

        val duplicateTime = LocalTime.of(18, 0)
        underTest.duplicateScreening(
            DuplicateScreeningCommand(
                screeningId = createdScreening.id,
                auditoriumId = AUDITORIUM_2.id,
                time = duplicateTime
            )
        )
        assertEquals(2, screeningRepository.findAll().size)

        // assert that duplicated screening has correct attributes
        val duplicatedScreening = screeningRepository.findAll().sortedBy { it.createdAt }[1]
        assertScreeningEquals(
            createdScreening.apply {
                auditoriumId = AUDITORIUM_2.id
                time = duplicateTime
                state = ScreeningState.DRAFT
                auditoriumLayoutId = AUDITORIUM_LAYOUT_3.id
            },
            duplicatedScreening
        )

        // check that source screening (createdScreening) is not modified by screening duplication
        val createdScreeningCheck = screeningRepository.findById(createdScreening.id).get()
        assertEquals(AUDITORIUM_2.id, createdScreeningCheck.auditoriumId)
        assertEquals(SCREENING_4.time, createdScreeningCheck.time)
        assertEquals(SCREENING_4.state, createdScreeningCheck.state)
        assertEquals(AUDITORIUM_LAYOUT_3.id, createdScreeningCheck.auditoriumLayoutId)
    }

    @Test
    fun `test duplicateScreening - screening is duplicated to another auditorium - should correctly resolve new auditorium's supported screening types`() {
        auditoriumRepository.save(AUDITORIUM_2)
        auditoriumLayoutRepository.save(AUDITORIUM_LAYOUT_2)
        auditoriumDefaultRepository.save(AUDITORIUM_DEFAULT_3)
        screeningTypeRepository.save(SCREENING_TYPE_1)
        screeningTypeRepository.saveAll(
            setOf(
                SCREENING_TYPE_CUSTOM,
                SCREENING_TYPE_D_BOX,
                SCREENING_TYPE_VIP,
                SCREENING_TYPE_IMAX,
                SCREENING_TYPE_ULTRA_X
            )
        )

        val command = mapToCreateOrUpdateScreeningCommand(
            screening = SCREENING_1,
            screeningTypeIds = setOf(SCREENING_TYPE_CUSTOM.id, SCREENING_TYPE_VIP.id)
        ).copy(id = null, originalId = null)

        underTest.adminCreateOrUpdateScreening(command)

        val createdScreening = screeningRepository.findAll()[0]
        val screeningTypes = screeningTypesRepository.findAllByScreeningId(screeningId = createdScreening.id)
        assertEquals(
            screeningTypes.map { it.screeningTypeId }.toSet(),
            setOf(SCREENING_TYPE_CUSTOM.id, SCREENING_TYPE_VIP.id)
        )

        underTest.duplicateScreening(
            DuplicateScreeningCommand(
                screeningId = createdScreening.id,
                auditoriumId = AUDITORIUM_2.id,
                time = LocalTime.of(20, 0)
            )
        )
        assertEquals(2, screeningRepository.findAll().size)

        val duplicatedScreening = screeningRepository.findAll().sortedBy { it.createdAt }[1]
        val duplicatedScreeningTypes =
            screeningTypesRepository.findAllByScreeningId(screeningId = duplicatedScreening.id)
        assertEquals(
            setOf(
                SCREENING_TYPE_CUSTOM.id,
                SCREENING_TYPE_IMAX.id,
                SCREENING_TYPE_ULTRA_X.id
            ),
            duplicatedScreeningTypes.map { it.screeningTypeId }.toSet()
        )
    }

    @Test
    fun `test duplicateScreening - screening does not exist - should throw exception`() {
        screeningTypeRepository.save(SCREENING_TYPE_1)

        val command = mapToCreateOrUpdateScreeningCommand(
            SCREENING_1,
            SCREENING_FEE_1,
            setOf(SCREENING_TYPE_1.id)
        ).copy(id = null, originalId = null)

        underTest.adminCreateOrUpdateScreening(command)

        val createdScreening = screeningRepository.findAll()[0]
        assertNotNull(createdScreening)
        assertScreeningEquals(SCREENING_1, createdScreening)

        assertThrows<ScreeningNotFoundException> {
            underTest.duplicateScreening(
                DuplicateScreeningCommand(
                    screeningId = UUID.randomUUID(),
                    auditoriumId = AUDITORIUM_2.id,
                    time = LocalTime.of(14, 0)
                )
            )
        }
    }

    @Test
    fun `test duplicateScreening - default auditorium layout does not exist - should throw exception`() {
        auditoriumRepository.save(AUDITORIUM_2)
        auditoriumLayoutRepository.save(
            createAuditoriumLayout(
                originalId = 2,
                auditoriumId = AUDITORIUM_2.id,
                code = "02"
            )
        )

        val command = mapToCreateOrUpdateScreeningCommand(
            SCREENING_1,
            SCREENING_FEE_1,
            null
        ).copy(id = null, originalId = null)

        underTest.adminCreateOrUpdateScreening(command)

        val createdScreening = screeningRepository.findAll()[0]
        assertNotNull(createdScreening)
        assertScreeningEquals(SCREENING_1, createdScreening)

        assertThrows<AuditoriumLayoutNotFoundException> {
            underTest.duplicateScreening(
                DuplicateScreeningCommand(
                    screeningId = createdScreening.id,
                    auditoriumId = AUDITORIUM_2.id,
                    time = LocalTime.of(14, 0)
                )
            )
        }
    }

    @Test
    fun `test duplicateScreening - auditorium does not exist - should throw exception`() {
        screeningTypeRepository.save(SCREENING_TYPE_1)

        val command = mapToCreateOrUpdateScreeningCommand(
            SCREENING_1,
            SCREENING_FEE_1,
            setOf(SCREENING_TYPE_1.id)
        ).copy(id = null, originalId = null)

        underTest.adminCreateOrUpdateScreening(command)

        val createdScreening = screeningRepository.findAll()[0]
        assertNotNull(createdScreening)
        assertScreeningEquals(SCREENING_1, createdScreening)

        assertThrows<AuditoriumForScreeningNotFoundException> {
            underTest.duplicateScreening(
                DuplicateScreeningCommand(
                    screeningId = createdScreening.id,
                    auditoriumId = UUID.randomUUID(),
                    time = LocalTime.of(14, 0)
                )
            )
        }
    }

    @Test
    fun `test deleteScreening - when screening does not exist - should throw`() {
        assertThrows<ScreeningNotFoundException> {
            underTest.deleteScreening(
                DeleteScreeningCommand(UUID.fromString("29bdc064-6428-45b1-bba2-d01f98a2e135"))
            )
        }
    }

    @Test
    fun `test deleteScreening - screening in PUBLISHED state - should throw`() {
        screeningRepository.save(SCREENING_1)

        assertThrows<ScreeningInPublishedStateException> {
            underTest.deleteScreening(DeleteScreeningCommand(SCREENING_1.id))
        }
    }

    @Test
    fun `test deleteScreening - DRAFT screening does exist - should softly delete and publish event`() {
        screeningRepository.save(SCREENING_3)
        val screenings = screeningRepository.findAll()
        assertEquals(1, screenings.size)
        assertEquals(SCREENING_3.id, screenings[0].id)
        assertNull(screenings[0].deletedAt)

        underTest.deleteScreening(DeleteScreeningCommand(SCREENING_3.id))

        val screeningsAfterDeletion = screeningRepository.findAll()
        assertEquals(1, screeningsAfterDeletion.size)
        assertEquals(SCREENING_3.id, screeningsAfterDeletion[0].id)
        assertNotNull(screeningsAfterDeletion[0].deletedAt)

        verify {
            applicationEventPublisherMock.publishEvent(
                ScreeningsSwitchedToDraftOrDeletedEvent(
                    setOf(
                        SCREENING_3.id
                    )
                )
            )
        }
    }

    @Test
    fun `test deleteScreening - screening does exist but has null originalId - should softly delete, event is not published`() {
        auditoriumRepository.save(AUDITORIUM_2)
        auditoriumLayoutRepository.save(AUDITORIUM_LAYOUT_3)

        screeningRepository.save(SCREENING_6)
        val screenings = screeningRepository.findAll()
        assertEquals(1, screenings.size)
        assertEquals(SCREENING_6.id, screenings[0].id)
        assertNull(screenings[0].deletedAt)

        underTest.deleteScreening(DeleteScreeningCommand(screenings[0].id))

        val screeningsAfterDeletion = screeningRepository.findAll()
        assertEquals(1, screeningsAfterDeletion.size)
        assertEquals(SCREENING_6.id, screeningsAfterDeletion[0].id)
        assertNotNull(screeningsAfterDeletion[0].deletedAt)

        verify { applicationEventPublisherMock wasNot Called }
    }

    @Disabled
    @Test
    fun `test duplicateScreeningDay - should duplicate screening day and create screening fees and types`() {
        auditoriumRepository.save(AUDITORIUM_2)
        val screeningTypes = listOf(SCREENING_TYPE_1, SCREENING_TYPE_2)
        screeningTypes.forEach { screeningTypeRepository.save(it) }

        val screening1DuplicateSource = createScreening(
            originalId = SCREENING_1.originalId!!,
            auditoriumId = SCREENING_1.auditoriumId,
            auditoriumLayoutId = SCREENING_1.auditoriumLayoutId,
            movieId = SCREENING_1.movieId,
            priceCategoryId = SCREENING_1.priceCategoryId,
            proCommission = SCREENING_1.proCommission,
            filmFondCommission = SCREENING_1.filmFondCommission,
            distributorCommission = SCREENING_1.distributorCommission,
            publishOnline = SCREENING_1.publishOnline,
            date = LOCAL_DATE_PLUS_DAYS_1,
            time = SCREENING_1.time,
            saleTimeLimit = SCREENING_1.saleTimeLimit,
            state = ScreeningState.DRAFT
        )
        val screening2DuplicateSource = createScreening(
            originalId = SCREENING_2.originalId!!,
            auditoriumId = SCREENING_2.auditoriumId,
            auditoriumLayoutId = SCREENING_2.auditoriumLayoutId,
            movieId = SCREENING_2.movieId,
            priceCategoryId = SCREENING_2.priceCategoryId,
            proCommission = SCREENING_2.proCommission,
            filmFondCommission = SCREENING_2.filmFondCommission,
            distributorCommission = SCREENING_2.distributorCommission,
            publishOnline = SCREENING_2.publishOnline,
            date = LOCAL_DATE_PLUS_DAYS_1,
            time = SCREENING_2.time,
            saleTimeLimit = SCREENING_2.saleTimeLimit,
            state = SCREENING_2.state
        )

        val command1 = mapToCreateOrUpdateScreeningCommand(
            screening1DuplicateSource,
            SCREENING_FEE_1,
            setOf(SCREENING_TYPE_1.id, SCREENING_TYPE_2.id)
        ).copy(id = null, originalId = null)

        val command2 = mapToCreateOrUpdateScreeningCommand(
            screening2DuplicateSource,
            SCREENING_FEE_2,
            null
        ).copy(id = null, originalId = null)

        setOf(command1, command2).forEach { underTest.adminCreateOrUpdateScreening(it) }

        val createdScreenings = screeningRepository.findAll().sortedBy { it.createdAt }
        assertEquals(2, createdScreenings.size)
        assertScreeningEquals(
            screening1DuplicateSource,
            createdScreenings[0]
        )
        assertScreeningEquals(screening2DuplicateSource, createdScreenings[1])

        val createdScreeningTypes = screeningTypesRepository.findAllByScreeningId(createdScreenings[0].id)
            .sortedBy { it.createdAt }
        assertEquals(2, createdScreeningTypes.size)

        for (i in 0..1) {
            assertEquals(createdScreeningTypes[i].screeningTypeId, screeningTypes[i].id)
        }
        assertEquals(0, screeningTypesRepository.findAllByScreeningId(createdScreenings[1].id).size)

        val destinationDates = listOf(LOCAL_DATE_PLUS_DAYS_2, LOCAL_DATE_PLUS_DAYS_3)
        underTest.duplicateScreeningDay(
            DuplicateScreeningDayCommand(
                sourceDate = LOCAL_DATE_PLUS_DAYS_1,
                destinationDates = destinationDates.toSet()
            )
        )

        val screenings = screeningRepository.findAll().sortedBy { it.createdAt }
        assertEquals(6, screenings.size)

        val screening1Duplicates = screenings.slice(2..3)
        val screening2Duplicates = screenings.slice(4..5)

        // check whether screening duplicates have correctly assigned attrs
        screening1Duplicates.forEachIndexed { index, duplicateScreening ->
            assertScreeningEquals(
                createdScreenings[0].apply {
                    date = destinationDates[index]
                    state = ScreeningState.DRAFT
                },
                duplicateScreening
            )
        }
        screening2Duplicates.forEachIndexed { index, duplicateScreening ->
            assertScreeningEquals(
                createdScreenings[1].apply {
                    date = destinationDates[index]
                    state = ScreeningState.DRAFT
                },
                duplicateScreening
            )
        }

        // check screening fee duplicates
        val screening1DuplicateFees = screeningFeeJpaFinderService.findAllByScreeningIdIn(
            screening1Duplicates.map { it.id }.toSet()
        )
        val screening2DuplicateFees = screeningFeeJpaFinderService.findAllByScreeningIdIn(
            screening2Duplicates.map { it.id }.toSet()
        )
        screening1DuplicateFees.forEach { assertScreeningFeeEquals(SCREENING_FEE_1, it) }
        screening2DuplicateFees.forEach { assertScreeningFeeEquals(SCREENING_FEE_2, it) }

        screening1Duplicates.forEach {
            val screening1DuplicateScreeningTypes = screeningTypesRepository.findAllByScreeningId(it.id)
            assertEquals(2, screening1DuplicateScreeningTypes.size)

            screening1DuplicateScreeningTypes.forEachIndexed { index, duplicatedScreeningTypes ->
                assertEquals(screeningTypes[index].id, duplicatedScreeningTypes.screeningTypeId)
            }
        }
        screening2Duplicates.forEach {
            assertTrue(screeningTypesRepository.findAllByScreeningId(it.id).isEmpty())
        }
    }

    @Test
    fun `test duplicateScreeningDay - destination date is in the past - should throw exception`() {
        val command = mapToCreateOrUpdateScreeningCommand(
            SCREENING_1,
            SCREENING_FEE_1,
            null
        ).copy(id = null, originalId = null)

        underTest.adminCreateOrUpdateScreening(command)

        val createdScreening = screeningRepository.findAll()[0]
        assertNotNull(createdScreening)
        assertScreeningEquals(SCREENING_1, createdScreening)

        assertThrows<DestinationDateInThePastException> {
            underTest.duplicateScreeningDay(
                DuplicateScreeningDayCommand(
                    sourceDate = SCREENING_1.date,
                    destinationDates = setOf(
                        LOCAL_DATE_PLUS_DAYS_2,
                        LOCAL_DATE_PLUS_DAYS_3,
                        LocalDate.now().minusDays(1)
                    )
                )
            )
        }
    }

    @Test
    fun `test duplicateScreeningDay - destination date has different price category - should auto select correct price category`() {
        screeningTypeRepository.save(SCREENING_TYPE_1)
        priceCategoryRepository.saveAll(setOf(PRICE_CATEGORY_7, PRICE_CATEGORY_8)) // these added just to check autoselect logic does not count with non-active categories

        val today = LocalDate.now()
        val sourceDate = today.with(TemporalAdjusters.next(DayOfWeek.SUNDAY)) // weekend price category "po 17"
        val destinationDate = sourceDate.with(TemporalAdjusters.next(DayOfWeek.MONDAY)) // monday price category "do 17"
        val command = mapToCreateOrUpdateScreeningCommand(
            SCREENING_1,
            SCREENING_FEE_1,
            setOf(SCREENING_TYPE_1.id)
        ).copy(id = null, originalId = null, date = sourceDate)

        underTest.adminCreateOrUpdateScreening(command)

        val createdScreening = screeningRepository.findAll()[0]
        assertEquals(PRICE_CATEGORY_4.id, createdScreening.priceCategoryId)

        underTest.duplicateScreeningDay(
            DuplicateScreeningDayCommand(
                sourceDate = sourceDate,
                destinationDates = setOf(destinationDate)
            )
        )
        assertEquals(2, screeningRepository.findAll().size)

        val duplicatedScreening = screeningRepository.findAll().sortedBy { it.createdAt }[1]
        assertEquals(
            if (holidayResolver.isNationalHoliday(destinationDate)) PRICE_CATEGORY_4.id else PRICE_CATEGORY_5.id,
            duplicatedScreening.priceCategoryId
        )
    }

    @Test
    fun `test updateScreeningState - any of screenings not found - should throw not found exception`() {
        screeningRepository.saveAll(setOf(SCREENING_1, SCREENING_2, SCREENING_3))

        assertThrows<ScreeningNotFoundException> {
            underTest.updateScreeningStates(
                UpdateScreeningStatesCommand(
                    screeningIds = setOf(
                        SCREENING_1.id,
                        SCREENING_2.id,
                        SCREENING_3.id,
                        UUID.fromString("*************-46f2-bb24-48920648cefa")
                    ),
                    screeningState = ScreeningState.PUBLISHED
                )
            )
        }

        // no screening state has changed
        val savedScreenings = screeningRepository.findAll()
        assertEquals(SCREENING_1.state, savedScreenings.first { it.id == SCREENING_1.id }.state)
        assertEquals(SCREENING_2.state, savedScreenings.first { it.id == SCREENING_2.id }.state)
        assertEquals(SCREENING_3.state, savedScreenings.first { it.id == SCREENING_3.id }.state)

        verify(exactly = 0) { applicationEventPublisherMock.publishEvent(any()) }
    }

    @Test
    fun `test updateScreeningState - any of screening with datetime conflict - should throw datetime conflict exception`() {
        screeningRepository.saveAll(setOf(SCREENING_1, SCREENING_2, SCREENING_3))

        val exceptionMessage = assertThrows<ScreeningsDateTimeConflictException> {
            underTest.updateScreeningStates(
                UpdateScreeningStatesCommand(
                    screeningIds = setOf(
                        SCREENING_1.id,
                        SCREENING_2.id,
                        SCREENING_3.id
                    ),
                    screeningState = ScreeningState.PUBLISHED
                )
            )
        }.message

        assertContains(exceptionMessage, SCREENING_3.id.toString())
        assertFalse(exceptionMessage.contains(SCREENING_1.id.toString()))
        assertFalse(exceptionMessage.contains(SCREENING_2.id.toString()))

        // no screening state has changed
        val savedScreenings = screeningRepository.findAll()
        assertEquals(SCREENING_1.state, savedScreenings.first { it.id == SCREENING_1.id }.state)
        assertEquals(SCREENING_2.state, savedScreenings.first { it.id == SCREENING_2.id }.state)
        assertEquals(SCREENING_3.state, savedScreenings.first { it.id == SCREENING_3.id }.state)

        verify(exactly = 0) { applicationEventPublisherMock.publishEvent(any()) }
    }

    @Test
    fun `test updateScreeningState - any of screening with ticket sold - should throw ticket sold exception`() {
        screeningRepository.saveAll(setOf(SCREENING_1, SCREENING_2, SCREENING_3))
        seatRepository.save(SEAT_1)
        reservationsRepository.save(RESERVATION_1)
        ticketPriceRepository.save(TICKET_PRICE_1)
        ticketRepository.save(TICKET_1)

        val exceptionMessage = assertThrows<ScreeningsWithTicketsSoldException> {
            underTest.updateScreeningStates(
                UpdateScreeningStatesCommand(
                    screeningIds = setOf(
                        SCREENING_1.id,
                        SCREENING_2.id,
                        SCREENING_3.id
                    ),
                    screeningState = ScreeningState.DRAFT
                )
            )
        }.message

        assertContains(exceptionMessage, SCREENING_1.id.toString())
        assertFalse(exceptionMessage.contains(SCREENING_2.id.toString()))
        assertFalse(exceptionMessage.contains(SCREENING_3.id.toString()))

        // no screening state has changed
        val savedScreenings = screeningRepository.findAll()
        assertEquals(SCREENING_1.state, savedScreenings.first { it.id == SCREENING_1.id }.state)
        assertEquals(SCREENING_2.state, savedScreenings.first { it.id == SCREENING_2.id }.state)
        assertEquals(SCREENING_3.state, savedScreenings.first { it.id == SCREENING_3.id }.state)

        verify(exactly = 0) { applicationEventPublisherMock.publishEvent(any()) }
    }

    @Test
    fun `test updateScreeningState - valid screenings - should correctly update state and publish events`() {
        screeningRepository.saveAll(setOf(SCREENING_1, SCREENING_3))

        val savedScreenings = screeningRepository.findAll()
        assertNotNull(savedScreenings.first { it.id == SCREENING_1.id && it.state == ScreeningState.PUBLISHED })
        assertNotNull(savedScreenings.first { it.id == SCREENING_3.id && it.state == ScreeningState.DRAFT })

        underTest.updateScreeningStates(
            UpdateScreeningStatesCommand(
                screeningIds = setOf(SCREENING_1.id),
                screeningState = ScreeningState.DRAFT
            )
        )
        underTest.updateScreeningStates(
            UpdateScreeningStatesCommand(
                screeningIds = setOf(SCREENING_3.id),
                screeningState = ScreeningState.PUBLISHED
            )
        )

        val updatedScreenings = screeningRepository.findAll()
        assertNotNull(updatedScreenings.first { it.id == SCREENING_1.id && it.state == ScreeningState.DRAFT })
        assertNotNull(updatedScreenings.first { it.id == SCREENING_3.id && it.state == ScreeningState.PUBLISHED })

        verifyOrder {
            applicationEventPublisherMock.publishEvent(
                ScreeningsSwitchedToDraftOrDeletedEvent(screeningIds = setOf(SCREENING_1.id))
            )
            applicationEventPublisherMock.publishEvent(
                ScreeningsPublishedEvent(screeningIds = setOf(SCREENING_3.id))
            )
        }
    }

    @Test
    fun `test updateScreeningState - DRAFT screening without original id updated to DRAFT - should not be synced`() {
        screeningRepository.save(SCREENING_1)

        val command = mapToCreateOrUpdateScreeningCommand(SCREENING_3).copy(id = null, originalId = null)
        underTest.adminCreateOrUpdateScreening(command)

        val savedScreenings = screeningRepository.findAll()
        assertEquals(2, savedScreenings.size)

        val createdScreening3 = savedScreenings.first { it.state == ScreeningState.DRAFT }
        assertNotNull(savedScreenings.first { it.id == SCREENING_1.id && it.state == ScreeningState.PUBLISHED })
        assertNotNull(createdScreening3)

        underTest.updateScreeningStates(
            UpdateScreeningStatesCommand(
                screeningIds = setOf(SCREENING_1.id, createdScreening3.id),
                screeningState = ScreeningState.DRAFT
            )
        )

        val updatedScreenings = screeningRepository.findAll()
        assertNotNull(updatedScreenings.first { it.id == SCREENING_1.id && it.state == ScreeningState.DRAFT })
        assertNotNull(updatedScreenings.first { it.id == createdScreening3.id && it.state == ScreeningState.DRAFT })

        verify {
            applicationEventPublisherMock.publishEvent(
                ScreeningsSwitchedToDraftOrDeletedEvent(screeningIds = setOf(SCREENING_1.id))
            )
        }
    }

    @Test
    fun `test messagingCreateOrUpdateScreening - should create screening with screening types and screening fee`() {
        screeningTypeRepository.saveAll(setOf(SCREENING_TYPE_1, SCREENING_TYPE_2))

        val screeningId = UUID.randomUUID()
        val command = MessagingCreateOrUpdateScreeningCommand(
            id = screeningId,
            auditoriumOriginalCode = AUDITORIUM_1.originalCode,
            auditoriumLayoutId = AUDITORIUM_LAYOUT_1.id,
            movieMessagingCode = MOVIE_1.messagingCode,
            priceCategoryId = PRICE_CATEGORY_1.id,
            date = LocalDate.now().plusDays(1),
            time = LocalTime.of(19, 0),
            saleTimeLimit = 20,
            stopped = false,
            cancelled = false,
            proCommission = 1,
            filmFondCommission = 1,
            distributorCommission = 30,
            publishOnline = true,
            state = ScreeningState.PUBLISHED,
            adTimeSlot = 15,
            deletedAt = null,
            screeningTypeCodes = setOf(SCREENING_TYPE_1.code, SCREENING_TYPE_2.code),
            surchargeVip = 5.00.toBigDecimal(),
            surchargePremium = 3.00.toBigDecimal(),
            surchargeImax = 4.00.toBigDecimal(),
            surchargeUltraX = 3.50.toBigDecimal(),
            surchargeDBox = 2.50.toBigDecimal(),
            serviceFeeVip = 1.50.toBigDecimal(),
            serviceFeePremium = 1.00.toBigDecimal(),
            serviceFeeImax = 1.25.toBigDecimal(),
            serviceFeeUltraX = 1.20.toBigDecimal(),
            serviceFeeGeneral = 0.75.toBigDecimal()
        )

        underTest.messagingCreateOrUpdateScreening(command)

        val screening = screeningRepository.findById(screeningId).get()
        assertEquals(screeningId, screening.id)
        assertEquals(AUDITORIUM_1.id, screening.auditoriumId)
        assertEquals(AUDITORIUM_LAYOUT_1.id, screening.auditoriumLayoutId)
        assertEquals(MOVIE_1.id, screening.movieId)
        assertEquals(PRICE_CATEGORY_1.id, screening.priceCategoryId)
        assertEquals(command.date, screening.date)
        assertEquals(command.time, screening.time)
        assertEquals(command.saleTimeLimit, screening.saleTimeLimit)
        assertEquals(command.stopped, screening.stopped)
        assertEquals(command.cancelled, screening.cancelled)
        assertEquals(command.proCommission, screening.proCommission)
        assertEquals(command.filmFondCommission, screening.filmFondCommission)
        assertEquals(command.distributorCommission, screening.distributorCommission)
        assertEquals(command.publishOnline, screening.publishOnline)
        assertEquals(command.state, screening.state)
        assertEquals(command.adTimeSlot, screening.adTimeSlot)
        assertEquals(command.deletedAt, screening.deletedAt)

        val screeningTypeIds = screeningTypesRepository.findAllByScreeningId(screeningId)
            .map { it.screeningTypeId }
            .toSet()
        assertEquals(screeningTypeIds, setOf(SCREENING_TYPE_1.id, SCREENING_TYPE_2.id))

        val screeningFee = screeningFeeRepository.findAllByScreeningIdIn(setOf(screeningId)).first()
        assertEquals(screeningFee.screeningId, screeningId)
        assertTrue(screeningFee.surchargeVip isEqualTo command.surchargeVip)
        assertTrue(screeningFee.surchargePremium isEqualTo command.surchargePremium)
        assertTrue(screeningFee.surchargeImax isEqualTo command.surchargeImax)
        assertTrue(screeningFee.surchargeUltraX isEqualTo command.surchargeUltraX)
        assertTrue(screeningFee.surchargeDBox isEqualTo command.surchargeDBox)
        assertTrue(screeningFee.serviceFeeVip isEqualTo command.serviceFeeVip)
        assertTrue(screeningFee.serviceFeePremium isEqualTo command.serviceFeePremium)
        assertTrue(screeningFee.serviceFeeImax isEqualTo command.serviceFeeImax)
        assertTrue(screeningFee.serviceFeeUltraX isEqualTo command.serviceFeeUltraX)
        assertTrue(screeningFee.serviceFeeGeneral isEqualTo command.serviceFeeGeneral)
    }

    private fun prepareAdminCreateOrUpdateScreeningTestData() {
        screeningTypeRepository.saveAll(setOf(SCREENING_TYPE_1, SCREENING_TYPE_2))
        screeningRepository.save(SCREENING_1)
        screeningRepository.save(SCREENING_3)
        screeningFeeRepository.saveAll(setOf(SCREENING_FEE_1, SCREENING_FEE_3))
        screeningTypesRepository.save(SCREENING_TYPES_1)
        // one ticket is sold
        seatRepository.save(SEAT_1)
        reservationsRepository.save(RESERVATION_1)
        ticketPriceRepository.save(TICKET_PRICE_1)
        ticketRepository.save(TICKET_1)

        auditoriumRepository.save(AUDITORIUM_2)
        auditoriumLayoutRepository.save(AUDITORIUM_LAYOUT_2)
        auditoriumDefaultRepository.save(AUDITORIUM_DEFAULT_2)
        movieRepository.save(MOVIE_2)
    }

    companion object {
        @JvmStatic
        fun validUpdateDraftScreeningCommandsProvider(): Stream<Arguments> {
            val draftScreeningCommand = mapToCreateOrUpdateScreeningCommand(SCREENING_3)

            return Stream.of(
                Arguments.of(
                    draftScreeningCommand.copy(
                        date = SCREENING_3.date.plusDays(2),
                        time = SCREENING_3.time.plusHours(2)
                    )
                ),
                Arguments.of(draftScreeningCommand.copy(date = INTEGRATION_TEST_DATE_TIME.toLocalDate().minusWeeks(1))),
                // changed one of hasUnmodifiableAttributeChanged, shouldn't throw because DRAFT
                Arguments.of(draftScreeningCommand.copy(date = SCREENING_1.date.plusDays(2))),
                Arguments.of(draftScreeningCommand.copy(time = SCREENING_1.time.plusHours(5))),
                Arguments.of(draftScreeningCommand.copy(auditoriumId = AUDITORIUM_2.id)),
                Arguments.of(draftScreeningCommand.copy(auditoriumLayoutId = AUDITORIUM_LAYOUT_2.id)),
                Arguments.of(draftScreeningCommand.copy(movieId = MOVIE_2.id))
            )
        }

        @JvmStatic
        fun validUpdatePublishedScreeningCommandsProvider(): Stream<Arguments> {
            val publishedScreeningCommand =
                mapToCreateOrUpdateScreeningCommand(SCREENING_1, SCREENING_FEE_2, setOf(SCREENING_TYPE_2.id))

            return Stream.of(
                // changed one of modifiable attributes (not in hasUnmodifiableAttributeChanged), shouldn't throw despite screening is PUBLISHED
                Arguments.of(publishedScreeningCommand.copy(priceCategoryId = PRICE_CATEGORY_5.id)),
                Arguments.of(publishedScreeningCommand.copy(saleTimeLimit = 45)),
                Arguments.of(publishedScreeningCommand.copy(stopped = true)),
                Arguments.of(publishedScreeningCommand.copy(cancelled = true)),
                Arguments.of(publishedScreeningCommand.copy(proCommission = 8)),
                Arguments.of(publishedScreeningCommand.copy(filmFondCommission = 9)),
                Arguments.of(publishedScreeningCommand.copy(distributorCommission = 10)),
                Arguments.of(publishedScreeningCommand.copy(publishOnline = false)),
                Arguments.of(publishedScreeningCommand.copy(adTimeSlot = 25)),
                Arguments.of(publishedScreeningCommand.copy(movieId = MOVIE_2.id))
            )
        }

        @JvmStatic
        fun invalidUpdateScreeningCommandsProvider(): Stream<Arguments> {
            val publishedScreeningCommand =
                mapToCreateOrUpdateScreeningCommand(SCREENING_1, SCREENING_FEE_2, setOf(SCREENING_TYPE_2.id))

            return Stream.of(
                // changed one of hasUnmodifiableAttributeChanged, should throw because screening is PUBLISHED
                Arguments.of(publishedScreeningCommand.copy(date = SCREENING_1.date.plusDays(2))),
                Arguments.of(publishedScreeningCommand.copy(time = SCREENING_1.time.plusHours(5))),
                Arguments.of(publishedScreeningCommand.copy(auditoriumId = AUDITORIUM_2.id)),
                Arguments.of(publishedScreeningCommand.copy(auditoriumLayoutId = AUDITORIUM_LAYOUT_2.id))
            )
        }
    }
}

private val AUDITORIUM_1 = createAuditorium(
    originalId = 1,
    code = "A",
    title = "SÁLA A - CINEMAX BRATISLAVA"
)
private val AUDITORIUM_2 = createAuditorium(
    originalId = 2,
    code = "B",
    title = "SÁLA B - CINEMAX BRATISLAVA"
)
private val DISTRIBUTOR_1 = createDistributor()
private val AUDITORIUM_LAYOUT_1 =
    createAuditoriumLayout(originalId = 1, auditoriumId = AUDITORIUM_1.id, code = "01")
private val AUDITORIUM_LAYOUT_2 =
    createAuditoriumLayout(originalId = 2, auditoriumId = AUDITORIUM_2.id, code = "01")
private val AUDITORIUM_LAYOUT_3 =
    createAuditoriumLayout(originalId = 3, auditoriumId = AUDITORIUM_2.id, code = "02")
private val AUDITORIUM_DEFAULT_1 = createAuditoriumDefault(auditoriumId = AUDITORIUM_1.id)
private val AUDITORIUM_DEFAULT_2 = createAuditoriumDefault(
    originalId = 2,
    auditoriumId = AUDITORIUM_2.id,
    surchargeVip = 9.00.toBigDecimal(),
    surchargePremium = 8.00.toBigDecimal(),
    surchargeImax = 7.00.toBigDecimal(),
    surchargeUltraX = 6.00.toBigDecimal(),
    serviceFeeVip = 5.00.toBigDecimal(),
    serviceFeePremium = 4.00.toBigDecimal(),
    serviceFeeImax = 3.00.toBigDecimal(),
    serviceFeeUltraX = 2.00.toBigDecimal(),
    surchargeDBox = 1.00.toBigDecimal()
)
private val AUDITORIUM_DEFAULT_3 = createAuditoriumDefault(
    originalId = 3,
    auditoriumId = AUDITORIUM_2.id,
    surchargeImax = 1.00.toBigDecimal(),
    surchargeUltraX = 1.00.toBigDecimal(),
    serviceFeeImax = 1.00.toBigDecimal(),
    serviceFeeUltraX = 1.00.toBigDecimal(),
    surchargeDBox = 0.toBigDecimal(),
    surchargeVip = 0.toBigDecimal(),
    surchargePremium = 0.toBigDecimal(),
    serviceFeeVip = 0.toBigDecimal(),
    serviceFeePremium = 0.toBigDecimal()
)
private val MOVIE_1 = createMovie(
    originalId = 1,
    title = "Star Wars: Episode I – The Phantom Menace",
    releaseYear = 2001,
    distributorId = DISTRIBUTOR_1.id,
    duration = 60,
    code = "6464"
)
private val MOVIE_2 = createMovie(
    originalId = 2,
    title = "Star Wars: Episode II",
    releaseYear = 2010,
    distributorId = DISTRIBUTOR_1.id,
    duration = 60,
    code = "6465"
)
private val PRICE_CATEGORY_1 = createPriceCategory(
    id = 1.toUUID(),
    originalId = 1,
    originalCode = null,
    title = "Artmax po 17h"
)
private val PRICE_CATEGORY_2 = createPriceCategory(
    id = 2.toUUID(),
    originalId = 2,
    originalCode = null,
    title = "Artmax do 17h"
)
private val PRICE_CATEGORY_3 = createPriceCategory(
    id = 3.toUUID(),
    originalId = 3,
    originalCode = null,
    title = "Do 17h Artmax"
)
private val PRICE_CATEGORY_4 = createPriceCategory(
    id = 4.toUUID(),
    originalId = 4,
    originalCode = null,
    title = "Po 17h"
)
private val PRICE_CATEGORY_5 = createPriceCategory(
    id = 5.toUUID(),
    originalId = 5,
    originalCode = null,
    title = "Do 17h"
)
private val PRICE_CATEGORY_6 = createPriceCategory(
    id = 6.toUUID(),
    originalId = 6,
    originalCode = null,
    title = "Special"
)
private val PRICE_CATEGORY_7 = createPriceCategory(
    id = 7.toUUID(),
    originalId = 7,
    originalCode = null,
    title = "Po 17",
    active = false
)
private val PRICE_CATEGORY_8 = createPriceCategory(
    id = 8.toUUID(),
    originalId = 8,
    originalCode = null,
    title = "Do 17",
    active = false
)
private val SCREENING_1 = createScreening(
    originalId = 1,
    auditoriumId = AUDITORIUM_1.id,
    auditoriumLayoutId = AUDITORIUM_LAYOUT_1.id,
    movieId = MOVIE_1.id,
    priceCategoryId = PRICE_CATEGORY_4.id,
    proCommission = 3,
    filmFondCommission = 6,
    distributorCommission = 70,
    publishOnline = true,
    date = getNextDateForDay(DayOfWeek.THURSDAY),
    time = LocalTime.of(20, 0),
    state = ScreeningState.PUBLISHED
)
private val SCREENING_2 = createScreening(
    originalId = 2,
    auditoriumId = AUDITORIUM_1.id,
    auditoriumLayoutId = AUDITORIUM_LAYOUT_1.id,
    movieId = MOVIE_1.id,
    priceCategoryId = PRICE_CATEGORY_4.id,
    proCommission = 13,
    filmFondCommission = 16,
    distributorCommission = 170,
    publishOnline = false,
    date = getNextDateForDay(DayOfWeek.FRIDAY),
    time = LocalTime.of(22, 0),
    state = ScreeningState.PUBLISHED
)
private val SCREENING_3 = createScreening(
    originalId = 3,
    auditoriumId = AUDITORIUM_1.id,
    auditoriumLayoutId = AUDITORIUM_LAYOUT_1.id,
    movieId = MOVIE_1.id,
    priceCategoryId = PRICE_CATEGORY_4.id,
    proCommission = 13,
    filmFondCommission = 16,
    distributorCommission = 170,
    publishOnline = false,
    date = getNextDateForDay(DayOfWeek.FRIDAY),
    time = LocalTime.of(21, 0),
    state = ScreeningState.DRAFT
)
private val SCREENING_4 = createScreening(
    originalId = 4,
    auditoriumId = AUDITORIUM_2.id,
    auditoriumLayoutId = AUDITORIUM_LAYOUT_3.id,
    movieId = MOVIE_1.id,
    priceCategoryId = PRICE_CATEGORY_4.id,
    proCommission = 13,
    filmFondCommission = 16,
    distributorCommission = 170,
    publishOnline = false,
    date = getNextDateForDay(DayOfWeek.THURSDAY),
    time = LocalTime.of(14, 0),
    state = ScreeningState.PUBLISHED
)
private val SCREENING_5 = createScreening( // SCREENING_1 in the past
    originalId = 5,
    auditoriumId = AUDITORIUM_1.id,
    auditoriumLayoutId = AUDITORIUM_LAYOUT_1.id,
    movieId = MOVIE_1.id,
    priceCategoryId = PRICE_CATEGORY_4.id,
    proCommission = 3,
    filmFondCommission = 6,
    distributorCommission = 70,
    publishOnline = true,
    date = SCREENING_1.date.minusWeeks(2),
    time = LocalTime.of(20, 0),
    state = ScreeningState.PUBLISHED
)
private val SCREENING_6 = createScreening(
    originalId = null,
    auditoriumId = AUDITORIUM_2.id,
    auditoriumLayoutId = AUDITORIUM_LAYOUT_3.id,
    movieId = MOVIE_1.id,
    priceCategoryId = PRICE_CATEGORY_4.id,
    proCommission = 13,
    filmFondCommission = 16,
    distributorCommission = 170,
    publishOnline = false,
    date = getNextDateForDay(DayOfWeek.SATURDAY),
    time = LocalTime.of(14, 0),
    state = ScreeningState.DRAFT
)
private val SCREENING_7 = createScreening( // SCREENING_1 in DRAFT with PRICE_CATEGORY_5
    originalId = 7,
    auditoriumId = AUDITORIUM_1.id,
    auditoriumLayoutId = AUDITORIUM_LAYOUT_1.id,
    movieId = MOVIE_1.id,
    priceCategoryId = PRICE_CATEGORY_6.id,
    proCommission = 3,
    filmFondCommission = 6,
    distributorCommission = 70,
    publishOnline = true,
    date = getNextDateForDay(DayOfWeek.THURSDAY),
    time = LocalTime.of(20, 0),
    state = ScreeningState.DRAFT
)
private val SCREENING_FEE_1 = createScreeningFee(
    originalScreeningId = null,
    screeningId = SCREENING_1.id,
    surchargeVip = BigDecimal(10),
    surchargePremium = BigDecimal(5),
    surchargeImax = BigDecimal(7),
    surchargeUltraX = BigDecimal(8),
    serviceFeeVip = BigDecimal(2),
    serviceFeePremium = BigDecimal(3),
    serviceFeeImax = BigDecimal(4),
    serviceFeeUltraX = BigDecimal(5),
    surchargeDBox = BigDecimal(6),
    serviceFeeGeneral = BigDecimal(1)
)
private val SCREENING_FEE_2 = createScreeningFee(
    originalScreeningId = null,
    screeningId = SCREENING_2.id,
    surchargeVip = BigDecimal(100),
    surchargePremium = BigDecimal(50),
    surchargeImax = BigDecimal(70),
    surchargeUltraX = BigDecimal(80),
    serviceFeeVip = BigDecimal(20),
    serviceFeePremium = BigDecimal(30),
    serviceFeeImax = BigDecimal(40),
    serviceFeeUltraX = BigDecimal(50),
    surchargeDBox = BigDecimal(60),
    serviceFeeGeneral = BigDecimal(10)
)
private val SCREENING_FEE_3 = createScreeningFee(
    originalScreeningId = null,
    screeningId = SCREENING_3.id,
    surchargeVip = BigDecimal(1),
    surchargePremium = BigDecimal(2),
    surchargeImax = BigDecimal(3),
    surchargeUltraX = BigDecimal(4),
    serviceFeeVip = BigDecimal(5),
    serviceFeePremium = BigDecimal(6),
    serviceFeeImax = BigDecimal(7),
    serviceFeeUltraX = BigDecimal(8),
    surchargeDBox = BigDecimal(9),
    serviceFeeGeneral = BigDecimal(10)
)
private val SCREENING_FEE_4 = createScreeningFee(
    originalScreeningId = null,
    screeningId = SCREENING_7.id,
    surchargeVip = BigDecimal(10),
    surchargePremium = BigDecimal(5),
    surchargeImax = BigDecimal(7),
    surchargeUltraX = BigDecimal(8),
    serviceFeeVip = BigDecimal(2),
    serviceFeePremium = BigDecimal(3),
    serviceFeeImax = BigDecimal(4),
    serviceFeeUltraX = BigDecimal(5),
    surchargeDBox = BigDecimal(6),
    serviceFeeGeneral = BigDecimal(1)
)
private val SCREENING_TYPE_1 = createScreeningType(
    originalId = 1,
    code = "type1",
    title = "Best movies ever, s.r.o."
)
private val SCREENING_TYPE_2 = createScreeningType(
    originalId = 2,
    code = "type2",
    title = "Worst movies ever, s.r.o."
)
private val SCREENING_TYPE_D_BOX =
    createScreeningType(id = 16.toUUID(), originalId = 3, code = D_BOX_SCREENING_TYPE_CODE, title = "D-Box")
private val SCREENING_TYPE_VIP = createScreeningType(id = 19.toUUID(), originalId = 4, code = VIP_SCREENING_TYPE_CODE, title = "VIP")
private val SCREENING_TYPE_IMAX = createScreeningType(id = 15.toUUID(), originalId = 5, code = IMAX_SCREENING_TYPE_CODE, title = "IMAX")
private val SCREENING_TYPE_ULTRA_X =
    createScreeningType(id = 24.toUUID(), originalId = 6, code = ULTRA_X_SCREENING_TYPE_CODE, title = "UltraX")
private val SCREENING_TYPE_CUSTOM = createScreeningType(id = 99.toUUID(), originalId = 7, code = "99", title = "Custom-type")
private val SCREENING_TYPES_1 = createScreeningTypes(
    screeningId = SCREENING_1.id,
    screeningTypeId = SCREENING_TYPE_1.id
)
private val SEAT_1 = createSeat(
    auditoriumId = AUDITORIUM_1.id,
    auditoriumLayoutId = AUDITORIUM_LAYOUT_1.id,
    originalId = 1
)
private val RESERVATION_1 = createReservation(
    screeningId = SCREENING_1.id,
    seatId = SEAT_1.id
)
private val TICKET_PRICE_1 = createTicketPrice(
    screeningId = SCREENING_1.id,
    seatId = SEAT_1.id,
    totalPrice = 100.toBigDecimal()
)
private val TICKET_1 = createTicket(
    screeningId = SCREENING_1.id,
    reservationId = RESERVATION_1.id,
    ticketPriceId = TICKET_PRICE_1.id
)
private val LOCAL_DATE_PLUS_DAYS_1 = LocalDate.now().plusDays(1)
private val LOCAL_DATE_PLUS_DAYS_2 = LocalDate.now().plusDays(2)
private val LOCAL_DATE_PLUS_DAYS_3 = LocalDate.now().plusDays(3)
