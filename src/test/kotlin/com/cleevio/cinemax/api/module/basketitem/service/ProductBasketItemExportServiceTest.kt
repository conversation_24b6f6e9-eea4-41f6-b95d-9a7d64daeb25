package com.cleevio.cinemax.api.module.basketitem.service

import com.cleevio.cinemax.api.common.constant.ExportFormat
import com.cleevio.cinemax.api.common.constant.ExportableBoolean
import com.cleevio.cinemax.api.common.constant.PaymentType
import com.cleevio.cinemax.api.common.service.model.ExportResultModel
import com.cleevio.cinemax.api.module.basketitem.controller.dto.AdminSearchProductBasketItemsFilter
import com.cleevio.cinemax.api.module.basketitem.model.ProductBasketItemExportRecordModel
import com.cleevio.cinemax.api.module.basketitem.model.ProductBasketItemExportSummaryRecordModel
import com.cleevio.cinemax.api.module.basketitem.service.query.AdminExportProductBasketItemsQuery
import com.cleevio.cinemax.api.module.discountcard.constant.DiscountCardType
import com.cleevio.cinemax.api.module.product.constant.ProductType
import com.cleevio.cinemax.api.util.toUUID
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import io.mockk.verifySequence
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.assertThrows
import org.springframework.data.domain.Pageable
import org.springframework.data.domain.Sort
import java.io.ByteArrayInputStream
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.LocalTime
import kotlin.test.Test

class ProductBasketItemExportServiceTest {

    private val productBasketItemXlsxExportResultMapper = mockk<ProductBasketItemXlsxExportResultMapper>()
    private val adminExportProductBasketItemsQueryService = mockk<AdminExportProductBasketItemsQueryService>()
    private val adminExportProductBasketItemsSummaryQueryService =
        mockk<AdminExportProductBasketItemsSummaryQueryService>()

    private val underTest = ProductBasketItemExportService(
        productBasketItemXlsxExportResultMapper = productBasketItemXlsxExportResultMapper,
        adminExportProductBasketItemsQueryService = adminExportProductBasketItemsQueryService,
        adminExportProductBasketItemsSummaryQueryService = adminExportProductBasketItemsSummaryQueryService
    )

    @Test
    fun `test exportProductBasketItems - valid query with XLSX format - should call service and mapper`() {
        val basketPaidAtFrom = LocalDateTime.of(2024, 1, 1, 18, 0)
        val basketPaidAtTo = LocalDateTime.of(2024, 2, 1, 18, 0)
        val username = "test-user"

        val query = AdminExportProductBasketItemsQuery(
            pageable = Pageable.unpaged(Sort.by("product.createdAt")),
            filter = createFilter(basketPaidAtFrom, basketPaidAtTo),
            exportFormat = ExportFormat.XLSX,
            username = username
        )

        val exportListData = listOf(
            ProductBasketItemExportRecordModel(
                paidAtDate = LocalDate.of(2024, 1, 2),
                paidAtTime = LocalTime.of(17, 30),
                productReceiptNumber = "ABC-123-XYZ",
                posTitle = "Pokladna 1",
                basketUpdatedBy = "monika",
                productTitle = "Fanta PET 0.5l",
                productCode = "ABC123",
                productCategoryTitle = "Napoje",
                productType = ProductType.PRODUCT,
                quantity = 2,
                purchasePrice = "2.00".toBigDecimal(),
                sellingPrice = "3.00".toBigDecimal(),
                vatPercentage = 20,
                paymentType = PaymentType.CASH,
                isCancelled = ExportableBoolean.FALSE,
                isDiscounted = ExportableBoolean.TRUE,
                discountCardType = DiscountCardType.CARD,
                discountCardCode = "*********"
            )
        )

        val exportSummaryData = ProductBasketItemExportSummaryRecordModel(
            productsCount = 125,
            basketsCount = 45,
            purchasePriceSum = 1250.50.toBigDecimal(),
            salesCash = 2500.50.toBigDecimal(),
            salesCashless = 3750.75.toBigDecimal(),
            grossSales = 6251.25.toBigDecimal(),
            netSales = 5250.00.toBigDecimal(),
            taxAmount = 1001.25.toBigDecimal(),
            grossRevenue = 5000.75.toBigDecimal(),
            netRevenue = 3999.50.toBigDecimal(),
            cancelledProductsCount = 15,
            cancelledProductsExpense = 225.50.toBigDecimal()
        )

        val byteArray = ByteArray(1024)
        val inputStream = ByteArrayInputStream(byteArray)
        val exportResult = ExportResultModel(inputStream, byteArray.size.toLong())

        every { adminExportProductBasketItemsQueryService(query) } returns exportListData
        every { adminExportProductBasketItemsSummaryQueryService(query) } returns exportSummaryData
        every {
            productBasketItemXlsxExportResultMapper.mapToExportResultModel(any(), any(), any(), any())
        } returns exportResult

        val actualResult = underTest.exportProductBasketItems(query)

        assertEquals(exportResult, actualResult)

        verifySequence {
            adminExportProductBasketItemsQueryService(query)
            adminExportProductBasketItemsSummaryQueryService(query)
            productBasketItemXlsxExportResultMapper.mapToExportResultModel(
                data = exportListData to exportSummaryData,
                username = username,
                basketPaidAtDateFrom = basketPaidAtFrom.toLocalDate(),
                basketPaidAtDateTo = basketPaidAtTo.toLocalDate()
            )
        }
    }

    @Test
    fun `test exportProductBasketItems - query with XML format - should throw UnsupportedOperationException`() {
        val basketPaidAtFrom = LocalDateTime.of(2024, 1, 1, 18, 0)
        val basketPaidAtTo = LocalDateTime.of(2024, 2, 1, 18, 0)
        val username = "test-user"

        val query = AdminExportProductBasketItemsQuery(
            pageable = Pageable.unpaged(),
            filter = createFilter(basketPaidAtFrom, basketPaidAtTo),
            exportFormat = ExportFormat.XML,
            username = username
        )

        assertThrows<UnsupportedOperationException> {
            underTest.exportProductBasketItems(query)
        }

        verify(exactly = 0) { adminExportProductBasketItemsQueryService(any()) }
        verify(exactly = 0) {
            productBasketItemXlsxExportResultMapper.mapToExportResultModel(
                any(),
                any(),
                any(),
                any()
            )
        }
    }

    private fun createFilter(
        paidAtFrom: LocalDateTime,
        paidAtTo: LocalDateTime,
    ) = AdminSearchProductBasketItemsFilter(
        basketPaidAtFrom = paidAtFrom,
        basketPaidAtTo = paidAtTo,
        posConfigurationIds = setOf(3.toUUID()),
        productReceiptNumber = "1000000001",
        productIds = setOf(1.toUUID()),
        productCategoryIds = setOf(2.toUUID()),
        productCode = "Fanta-Code-001",
        paymentTypes = setOf(PaymentType.CASHLESS),
        discountCardCode = "XYZ999",
        discountCardTypes = setOf(DiscountCardType.CARD),
        discountCardTitle = "FILM karta",
        isDiscount = true,
        isCancelled = false
    )
}
