package com.cleevio.cinemax.api.module.supplier.controller

import com.cleevio.cinemax.api.ControllerTest
import com.cleevio.cinemax.api.common.constant.ApiVersion
import com.cleevio.cinemax.api.common.util.jsonContent
import com.cleevio.cinemax.api.module.employee.constant.EmployeeRole
import com.cleevio.cinemax.api.module.supplier.controller.dto.AdminGetSupplierResponse
import com.cleevio.cinemax.api.module.supplier.controller.dto.AdminSearchSuppliersResponse
import com.cleevio.cinemax.api.module.supplier.service.command.CreateOrUpdateSupplierCommand
import com.cleevio.cinemax.api.module.supplier.service.command.DeleteSupplierCommand
import com.cleevio.cinemax.api.module.supplier.service.query.AdminGetSupplierQuery
import com.cleevio.cinemax.api.module.supplier.service.query.AdminSearchSuppliersFilter
import com.cleevio.cinemax.api.module.supplier.service.query.AdminSearchSuppliersQuery
import com.cleevio.cinemax.api.truncatedAndFormatted
import com.cleevio.cinemax.api.util.mockAccessToken
import com.cleevio.cinemax.psql.tables.SupplierColumnNames
import io.mockk.every
import io.mockk.just
import io.mockk.runs
import io.mockk.verify
import io.mockk.verifySequence
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest
import org.springframework.data.domain.PageImpl
import org.springframework.data.domain.PageRequest
import org.springframework.data.domain.Sort
import org.springframework.http.HttpHeaders
import org.springframework.http.MediaType
import org.springframework.test.web.servlet.MockMvc
import org.springframework.test.web.servlet.delete
import org.springframework.test.web.servlet.get
import org.springframework.test.web.servlet.post
import org.springframework.test.web.servlet.put
import java.time.LocalDateTime
import java.util.Optional
import java.util.UUID

@WebMvcTest(AdminSupplierController::class)
class AdminSupplierControllerTest @Autowired constructor(
    private val mvc: MockMvc,
) : ControllerTest() {

    @Test
    fun `test createSupplier, should serialize and deserialize correctly`() {
        val supplierId = UUID.fromString("9e22ebee-f458-4c8b-a8ee-1503ee5099d7")
        every { supplierService.adminCreateOrUpdateSupplier(any()) } returns supplierId

        mvc.post(MANAGER_BASE_SUPPLIER_PATH) {
            header(HttpHeaders.ACCEPT, ApiVersion.VERSION_1_JSON)
            header(HttpHeaders.AUTHORIZATION, mockAccessToken(EmployeeRole.MANAGER))
            contentType = MediaType.APPLICATION_JSON
            content = """
                {
                  "title": "Cornico Popcorn",
                  "addressStreet": "Krížna",
                  "addressCity": "Bratislava",
                  "addressPostCode": "811 07",
                  "contactName": "Roman Milata",
                  "contactPhone": "+*********** 133",
                  "contactEmails": [
                    "<EMAIL>",
                    "<EMAIL>"
                  ],
                  "bankName": "ČSOB",
                  "bankAccount": "**********/7500",
                  "idNumber": "********",
                  "taxIdNumber": "SK21200689390"
                }
            """.trimIndent()
        }.andExpect {
            status { isOk() }
            jsonContent(
                """
                    {
                      "id": "$supplierId"
                    }
                """.trimIndent()
            )
        }

        verifySequence {
            supplierService.adminCreateOrUpdateSupplier(
                CreateOrUpdateSupplierCommand(
                    code = null,
                    title = "Cornico Popcorn",
                    addressStreet = Optional.of("Krížna"),
                    addressCity = Optional.of("Bratislava"),
                    addressPostCode = Optional.of("811 07"),
                    contactName = Optional.of("Roman Milata"),
                    contactPhone = Optional.of("+*********** 133"),
                    contactEmails = Optional.of(setOf("<EMAIL>", "<EMAIL>")),
                    bankName = Optional.of("ČSOB"),
                    bankAccount = Optional.of("**********/7500"),
                    idNumber = Optional.of("********"),
                    taxIdNumber = Optional.of("SK21200689390")
                )
            )
        }
    }

    @Test
    fun `test createSupplier, should serialize and deserialize correctly with mixed optional and required fields`() {
        val supplierId = UUID.fromString("9e22ebee-f458-4c8b-a8ee-1503ee5099d7")
        every { supplierService.adminCreateOrUpdateSupplier(any()) } returns supplierId

        mvc.post(MANAGER_BASE_SUPPLIER_PATH) {
            header(HttpHeaders.ACCEPT, ApiVersion.VERSION_1_JSON)
            header(HttpHeaders.AUTHORIZATION, mockAccessToken(EmployeeRole.MANAGER))
            contentType = MediaType.APPLICATION_JSON
            content = """
            {
              "title": "Cornico Popcorn",
              "contactPhone": null,
              "bankName": null,
              "bankAccount": null
            }
            """.trimIndent()
        }.andExpect {
            status { isOk() }
            jsonContent(
                """
                {
                  "id": "$supplierId"
                }
                """.trimIndent()
            )
        }

        verifySequence {
            supplierService.adminCreateOrUpdateSupplier(
                CreateOrUpdateSupplierCommand(
                    code = null,
                    title = "Cornico Popcorn",
                    addressStreet = null,
                    addressCity = null,
                    addressPostCode = null,
                    contactName = null,
                    contactPhone = Optional.empty(),
                    contactEmails = null,
                    bankName = Optional.empty(),
                    bankAccount = Optional.empty(),
                    idNumber = null,
                    taxIdNumber = null
                )
            )
        }
    }

    @Test
    fun `test searchSuppliers, should serialize and deserialize correctly`() {
        val supplier1 = AdminSearchSuppliersResponse(
            id = UUID.fromString("9ee09b68-153b-4ecb-9dd6-8095c4d53dd9"),
            code = "0002",
            title = "Cornico Popcorn a.s.",
            createdAt = LocalDateTime.parse("2023-09-30T10:00:00"),
            updatedAt = LocalDateTime.parse("2023-10-30T10:00:00")
        )
        val supplier2 = AdminSearchSuppliersResponse(
            id = UUID.fromString("d023c5ba-fc87-4f4b-b68b-98dcb7483496"),
            code = "0003",
            title = "Super nachos s.r.o.",
            createdAt = LocalDateTime.parse("2023-11-30T10:00:00"),
            updatedAt = LocalDateTime.parse("2023-12-30T10:00:00")
        )

        every { adminSearchSuppliersQueryService(any()) } returns PageImpl(
            listOf(supplier1, supplier2)
        )

        mvc.post(SEARCH_SUPPLIERS_PATH) {
            contentType = MediaType.APPLICATION_JSON
            header(HttpHeaders.ACCEPT, ApiVersion.VERSION_1_JSON)
            header(HttpHeaders.AUTHORIZATION, mockAccessToken(role = EmployeeRole.MANAGER))
            content = """
                {
                  "title": "Popcorn"
                }
            """.trimIndent()
        }.andExpect {
            status { isOk() }
            content { contentType(ApiVersion.VERSION_1_JSON) }
            jsonContent(
                """
                    {
                      "content": [
                        {
                          "id": "${supplier1.id}",
                          "code": "${supplier1.code}",
                          "title": "${supplier1.title}",
                          "createdAt": "${supplier1.createdAt.truncatedAndFormatted()}",
                          "updatedAt": "${supplier1.updatedAt.truncatedAndFormatted()}"
                        },
                        {
                          "id": "${supplier2.id}",
                          "code": "${supplier2.code}",
                          "title": "${supplier2.title}",
                          "createdAt": "${supplier2.createdAt.truncatedAndFormatted()}",
                          "updatedAt": "${supplier2.updatedAt.truncatedAndFormatted()}"
                        }
                      ],
                      "totalElements": 2,
                      "totalPages": 1
                    }
                """.trimIndent()
            )
        }

        verifySequence {
            adminSearchSuppliersQueryService(
                AdminSearchSuppliersQuery(
                    filter = AdminSearchSuppliersFilter(title = "Popcorn"),
                    pageable = PageRequest.of(0, 10, Sort.by(SupplierColumnNames.TITLE))
                )
            )
        }
    }

    @Test
    fun `test getSupplier, should serialize and deserialize correctly`() {
        val supplierId = UUID.fromString("9ee09b68-153b-4ecb-9dd6-8095c4d53dd9")
        val expectedResponse = AdminGetSupplierResponse(
            id = supplierId,
            code = "0002",
            title = "Cornico Popcorn",
            addressStreet = "Krížna",
            addressCity = "Bratislava",
            addressPostCode = "811 07",
            contactName = "Roman Milata",
            contactPhone = "+*********** 133",
            contactEmails = setOf("<EMAIL>"),
            bankName = "ČSOB",
            bankAccount = "**********/7500",
            idNumber = "********",
            taxIdNumber = "SK21200689390"
        )

        every { adminGetSupplierQueryService(any()) } returns expectedResponse

        mvc.get(GET_AND_UPDATE_AND_DELETE_SUPPLIER_PATH(supplierId)) {
            contentType = MediaType.APPLICATION_JSON
            header(HttpHeaders.ACCEPT, ApiVersion.VERSION_1_JSON)
            header(HttpHeaders.AUTHORIZATION, mockAccessToken(role = EmployeeRole.MANAGER))
        }.andExpect {
            status { isOk() }
            content { contentType(ApiVersion.VERSION_1_JSON) }
            jsonContent(
                """
                    {
                      "id": "$supplierId",
                      "code": "0002",
                      "title": "Cornico Popcorn",
                      "addressStreet": "Krížna",
                      "addressCity": "Bratislava",
                      "addressPostCode": "811 07",
                      "contactName": "Roman Milata",
                      "contactPhone": "+*********** 133",
                      "contactEmails": ["<EMAIL>"],
                      "bankName": "ČSOB",
                      "bankAccount": "**********/7500",
                      "idNumber": "********",
                      "taxIdNumber": "SK21200689390"
                    }
                """.trimIndent()
            )
        }

        verifySequence { adminGetSupplierQueryService(AdminGetSupplierQuery(supplierId)) }
    }

    @Test
    fun `test updateSupplier, should serialize and deserialize correctly`() {
        val supplierId = UUID.fromString("9e22ebee-f458-4c8b-a8ee-1503ee5099d7")
        every { supplierService.adminCreateOrUpdateSupplier(any()) } returns supplierId

        mvc.put(GET_AND_UPDATE_AND_DELETE_SUPPLIER_PATH(supplierId)) {
            header(HttpHeaders.ACCEPT, ApiVersion.VERSION_1_JSON)
            header(HttpHeaders.AUTHORIZATION, mockAccessToken(EmployeeRole.MANAGER))
            contentType = MediaType.APPLICATION_JSON
            content = """
                {
                  "title": "Cornico Popcorn",
                  "addressStreet": "Krížna",
                  "addressCity": "Bratislava",
                  "addressPostCode": "811 07",
                  "contactName": "Roman Milata",
                  "contactPhone": "+*********** 133",
                  "contactEmails": [
                    "<EMAIL>"
                  ],
                  "bankName": "ČSOB",
                  "bankAccount": "**********/7500",
                  "idNumber": "********",
                  "taxIdNumber": "SK21200689390"
                }
            """.trimIndent()
        }.andExpect {
            status { isOk() }
            jsonContent(
                """
                    {
                      "id": "$supplierId"
                    }
                """.trimIndent()
            )
        }

        verifySequence {
            supplierService.adminCreateOrUpdateSupplier(
                CreateOrUpdateSupplierCommand(
                    id = supplierId,
                    code = null,
                    title = "Cornico Popcorn",
                    addressStreet = Optional.of("Krížna"),
                    addressCity = Optional.of("Bratislava"),
                    addressPostCode = Optional.of("811 07"),
                    contactName = Optional.of("Roman Milata"),
                    contactPhone = Optional.of("+*********** 133"),
                    contactEmails = Optional.of(setOf("<EMAIL>")),
                    bankName = Optional.of("ČSOB"),
                    bankAccount = Optional.of("**********/7500"),
                    idNumber = Optional.of("********"),
                    taxIdNumber = Optional.of("SK21200689390")
                )
            )
        }
    }

    @Test
    fun `test updateSupplier, should serialize and deserialize correctly with missing optional fields`() {
        val supplierId = UUID.fromString("9e22ebee-f458-4c8b-a8ee-1503ee5099d7")
        every { supplierService.adminCreateOrUpdateSupplier(any()) } returns supplierId

        mvc.put(GET_AND_UPDATE_AND_DELETE_SUPPLIER_PATH(supplierId)) {
            header(HttpHeaders.ACCEPT, ApiVersion.VERSION_1_JSON)
            header(HttpHeaders.AUTHORIZATION, mockAccessToken(EmployeeRole.MANAGER))
            contentType = MediaType.APPLICATION_JSON
            content = """
              {
              "title": "Cornico Popcorn",
              "contactPhone": null,
              "bankName": null,
              "bankAccount": null
            }
            """.trimIndent()
        }.andExpect {
            status { isOk() }
            jsonContent(
                """
                    {
                      "id": "$supplierId"
                    }
                """.trimIndent()
            )
        }

        verifySequence {
            supplierService.adminCreateOrUpdateSupplier(
                CreateOrUpdateSupplierCommand(
                    id = supplierId,
                    code = null,
                    title = "Cornico Popcorn",
                    addressStreet = null,
                    addressCity = null,
                    addressPostCode = null,
                    contactName = null,
                    contactPhone = Optional.empty(),
                    contactEmails = null,
                    bankName = Optional.empty(),
                    bankAccount = Optional.empty(),
                    idNumber = null,
                    taxIdNumber = null
                )
            )
        }
    }

    @Test
    fun `test deleteSupplier, should serialize and deserialize correctly and call service`() {
        val supplierId = UUID.fromString("9e22ebee-f458-4c8b-a8ee-1503ee5099d7")

        every { supplierService.deleteSupplier(any()) } just runs

        mvc.delete(GET_AND_UPDATE_AND_DELETE_SUPPLIER_PATH(supplierId)) {
            header(HttpHeaders.ACCEPT, ApiVersion.VERSION_1_JSON)
            header(HttpHeaders.AUTHORIZATION, mockAccessToken(EmployeeRole.MANAGER))
        }.andExpect {
            status { isNoContent() }
        }

        verify {
            supplierService.deleteSupplier(DeleteSupplierCommand(supplierId))
        }
    }
}

private const val MANAGER_BASE_SUPPLIER_PATH = "/manager-app/suppliers"
private const val SEARCH_SUPPLIERS_PATH = "$MANAGER_BASE_SUPPLIER_PATH/search"
private val GET_AND_UPDATE_AND_DELETE_SUPPLIER_PATH: (UUID) -> String =
    { supplierId: UUID -> "$MANAGER_BASE_SUPPLIER_PATH/$supplierId" }
