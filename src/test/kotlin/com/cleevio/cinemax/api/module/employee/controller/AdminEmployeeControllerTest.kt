package com.cleevio.cinemax.api.module.employee.controller

import com.cleevio.cinemax.api.ControllerTest
import com.cleevio.cinemax.api.common.constant.ApiVersion
import com.cleevio.cinemax.api.common.constant.SourceApp
import com.cleevio.cinemax.api.common.service.query.SearchUnfilteredQueryDeprecated
import com.cleevio.cinemax.api.common.util.jsonContent
import com.cleevio.cinemax.api.module.employee.constant.EmployeeRole
import com.cleevio.cinemax.api.module.employee.controller.dto.TokensResponse
import com.cleevio.cinemax.api.module.employee.exception.AuthenticationFailedException
import com.cleevio.cinemax.api.module.employee.service.command.CreateOrUpdateEmployeeCommand
import com.cleevio.cinemax.api.module.employee.service.command.DeleteEmployeeCommand
import com.cleevio.cinemax.api.module.employee.service.command.EnforcePasswordResetCommand
import com.cleevio.cinemax.api.module.employee.service.command.GetSelfCommand
import com.cleevio.cinemax.api.module.employee.service.command.LoginEmployeeCommand
import com.cleevio.cinemax.api.module.employee.service.command.RefreshAccessTokenCommand
import com.cleevio.cinemax.api.module.employee.service.command.ResetPasswordCommand
import com.cleevio.cinemax.api.module.employee.service.model.TokensModel
import com.cleevio.cinemax.api.truncatedAndFormatted
import com.cleevio.cinemax.api.truncatedToSeconds
import com.cleevio.cinemax.api.util.TEST_PRINCIPAL_USERNAME
import com.cleevio.cinemax.api.util.createEmployee
import com.cleevio.cinemax.api.util.mapToEmployeeResponse
import com.cleevio.cinemax.api.util.mockAccessToken
import com.cleevio.cinemax.api.util.mockRefreshToken
import com.cleevio.cinemax.psql.tables.EmployeeColumnNames
import io.mockk.Called
import io.mockk.Runs
import io.mockk.every
import io.mockk.just
import io.mockk.verifySequence
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest
import org.springframework.data.domain.PageImpl
import org.springframework.data.domain.PageRequest
import org.springframework.data.domain.Sort
import org.springframework.http.HttpHeaders
import org.springframework.http.MediaType
import org.springframework.test.web.servlet.MockMvc
import org.springframework.test.web.servlet.delete
import org.springframework.test.web.servlet.get
import org.springframework.test.web.servlet.post
import org.springframework.test.web.servlet.put
import java.time.LocalDateTime
import java.util.UUID

@WebMvcTest(AdminEmployeeController::class)
class AdminEmployeeControllerTest @Autowired constructor(
    private val mvc: MockMvc,
) : ControllerTest() {

    @Test
    fun `test loginEmployee, missing accept header, should fall back to latest version content type`() {
        every { employeeService.loginEmployee(any()) } returns TOKENS_MODEL
        every { tokensResponseMapper.mapSingle(any()) } returns TOKENS_RESPONSE

        mvc.post(LOGIN_EMPLOYEE_PATH) {
            header(HttpHeaders.ACCEPT, ApiVersion.VERSION_1_JSON)
            contentType = MediaType.APPLICATION_JSON
            content = """
                {
                    "username": "${EMPLOYEE_1.username}",
                    "password": "$DUMMY_PASSWORD"
                }
            """.trimIndent()
        }.andExpect {
            content { contentType(ApiVersion.VERSION_1_JSON) }
            status { isOk() }
        }

        verifySequence {
            employeeService.loginEmployee(any())
            tokensResponseMapper.mapSingle(any())
        }
    }

    @Test
    fun `test loginEmployee, missing content type, should return 415`() {
        mvc.post(LOGIN_EMPLOYEE_PATH) {
            header(HttpHeaders.ACCEPT, ApiVersion.VERSION_1_JSON)
            content = """{}"""
        }.andExpect {
            status { isUnsupportedMediaType() }
        }
    }

    @Test
    fun `test loginEmployee, invalid content type, should return 415`() {
        mvc.post(LOGIN_EMPLOYEE_PATH) {
            header(HttpHeaders.ACCEPT, ApiVersion.VERSION_1_JSON)
            contentType = MediaType.APPLICATION_PDF
            content = """{}"""
        }.andExpect {
            status { isUnsupportedMediaType() }
        }
    }

    @Test
    fun `test loginEmployee, should serialize and deserialize correctly`() {
        every { employeeService.loginEmployee(any()) } returns TOKENS_MODEL
        every { tokensResponseMapper.mapSingle(any()) } returns TOKENS_RESPONSE

        mvc.post(LOGIN_EMPLOYEE_PATH) {
            header(HttpHeaders.ACCEPT, ApiVersion.VERSION_1_JSON)
            contentType = MediaType.APPLICATION_JSON
            content = """
                {
                    "username": "${EMPLOYEE_1.username}",
                    "password": "$DUMMY_PASSWORD"
                }
            """.trimIndent()
        }.andExpect {
            status { isOk() }
            content { contentType(ApiVersion.VERSION_1_JSON) }
            jsonContent(
                """
                {
                  "accessToken": "$ACCESS_TOKEN",
                  "accessExpiresIn": $ACCESS_EXPIRATION,
                  "refreshToken": "$REFRESH_TOKEN",
                  "refreshExpiresIn": $REFRESH_EXPIRATION
                }
            """
            )
        }

        verifySequence {
            employeeService.loginEmployee(
                LoginEmployeeCommand(
                    username = EMPLOYEE_1.username,
                    password = DUMMY_PASSWORD,
                    source = SourceApp.MANAGER_APP
                )
            )
            tokensResponseMapper.mapSingle(TOKENS_MODEL)
        }
    }

    @Test
    fun `test loginEmployee - service throws exception - should return 401 unauthorized`() {
        every { employeeService.loginEmployee(any()) } throws AuthenticationFailedException()

        mvc.post(LOGIN_EMPLOYEE_PATH) {
            header(HttpHeaders.ACCEPT, ApiVersion.VERSION_1_JSON)
            contentType = MediaType.APPLICATION_JSON
            content = """
                {
                    "username": "${EMPLOYEE_1.username}",
                    "password": "$DUMMY_PASSWORD"
                }
            """.trimIndent()
        }.andExpect {
            status { isUnauthorized() }
            content { contentType(ApiVersion.VERSION_1_JSON) }
        }

        verifySequence {
            employeeService.loginEmployee(
                LoginEmployeeCommand(
                    username = EMPLOYEE_1.username,
                    password = DUMMY_PASSWORD,
                    source = SourceApp.MANAGER_APP
                )
            )
            tokensResponseMapper wasNot Called
        }
    }

    @Test
    fun `test refreshAccessToken, missing accept header, should fall back to latest version content type`() {
        every { employeeService.refreshAccessToken(any()) } returns TOKENS_MODEL
        every { tokensResponseMapper.mapSingle(any()) } returns TOKENS_RESPONSE

        mvc.post(REFRESH_ACCESS_TOKEN_EMPLOYEE_PATH) {
            header(HttpHeaders.ACCEPT, ApiVersion.VERSION_1_JSON)
            contentType = MediaType.APPLICATION_JSON
            content = """
                {
                    "refreshToken": "$DUMMY_TOKEN"
                }
            """.trimIndent()
        }.andExpect {
            content { contentType(ApiVersion.VERSION_1_JSON) }
            status { isOk() }
        }

        verifySequence {
            employeeService.refreshAccessToken(any())
            tokensResponseMapper.mapSingle(any())
        }
    }

    @Test
    fun `test refreshAccessToken, missing content type, should return 415`() {
        mvc.post(REFRESH_ACCESS_TOKEN_EMPLOYEE_PATH) {
            header(HttpHeaders.ACCEPT, ApiVersion.VERSION_1_JSON)
            content = """{}"""
        }.andExpect {
            status { isUnsupportedMediaType() }
        }
    }

    @Test
    fun `test refreshAccessToken, invalid content type, should return 415`() {
        mvc.post(REFRESH_ACCESS_TOKEN_EMPLOYEE_PATH) {
            header(HttpHeaders.ACCEPT, ApiVersion.VERSION_1_JSON)
            contentType = MediaType.APPLICATION_PDF
            content = """{}"""
        }.andExpect {
            status { isUnsupportedMediaType() }
        }
    }

    @Test
    fun `test refreshAccessToken, should serialize and deserialize correctly`() {
        every { employeeService.refreshAccessToken(any()) } returns TOKENS_MODEL
        every { tokensResponseMapper.mapSingle(any()) } returns TOKENS_RESPONSE

        mvc.post(REFRESH_ACCESS_TOKEN_EMPLOYEE_PATH) {
            header(HttpHeaders.ACCEPT, ApiVersion.VERSION_1_JSON)
            contentType = MediaType.APPLICATION_JSON
            content = """
                {
                    "refreshToken": "$DUMMY_TOKEN"
                }
            """.trimIndent()
        }.andExpect {
            status { isOk() }
            content { contentType(ApiVersion.VERSION_1_JSON) }
            jsonContent(
                """
                {
                  "accessToken": "$ACCESS_TOKEN",
                  "accessExpiresIn": $ACCESS_EXPIRATION,
                  "refreshToken": "$REFRESH_TOKEN",
                  "refreshExpiresIn": $REFRESH_EXPIRATION
                }
            """
            )
        }

        verifySequence {
            employeeService.refreshAccessToken(
                RefreshAccessTokenCommand(
                    refreshToken = DUMMY_TOKEN
                )
            )
            tokensResponseMapper.mapSingle(TOKENS_MODEL)
        }
    }

    @Test
    fun `test refreshAccessToken - service throws exception - should return 401 unauthorized`() {
        every { employeeService.refreshAccessToken(any()) } throws AuthenticationFailedException()

        mvc.post(REFRESH_ACCESS_TOKEN_EMPLOYEE_PATH) {
            header(HttpHeaders.ACCEPT, ApiVersion.VERSION_1_JSON)
            contentType = MediaType.APPLICATION_JSON
            content = """
                {
                    "refreshToken": "$DUMMY_TOKEN"
                }
            """.trimIndent()
        }.andExpect {
            status { isUnauthorized() }
            content { contentType(ApiVersion.VERSION_1_JSON) }
            jsonContent("""{}""")
        }

        verifySequence {
            employeeService.refreshAccessToken(
                RefreshAccessTokenCommand(
                    refreshToken = DUMMY_TOKEN
                )
            )
            tokensResponseMapper wasNot Called
        }
    }

    @Test
    fun `test getSelf, missing accept header, should fall back to latest version content type`() {
        every { employeeService.getSelf(any()) } returns EMPLOYEE_1
        every { cinemaxConfigProperties.branchName } returns BRANCH_NAME

        mvc.get(GET_SELF_PATH) {
            header(HttpHeaders.ACCEPT, ApiVersion.VERSION_1_JSON)
            header(HttpHeaders.AUTHORIZATION, mockAccessToken(EMPLOYEE_1.role))
        }.andExpect {
            content { contentType(ApiVersion.VERSION_1_JSON) }
            status { isOk() }
        }

        verifySequence {
            employeeService.getSelf(any())
        }
    }

    @Test
    fun `test getSelf - service throws exception - should return 401 unauthorized`() {
        every { employeeService.getSelf(any()) } throws AuthenticationFailedException()

        mvc.get(GET_SELF_PATH) {
            header(HttpHeaders.ACCEPT, ApiVersion.VERSION_1_JSON)
            header(HttpHeaders.AUTHORIZATION, mockAccessToken(EMPLOYEE_1.role))
        }.andExpect {
            status { isUnauthorized() }
            content { contentType(ApiVersion.VERSION_1_JSON) }
        }

        verifySequence {
            employeeService.getSelf(
                GetSelfCommand(TEST_PRINCIPAL_USERNAME)
            )
        }
    }

    @Test
    fun `test getSelf, authorization header with valid token, should return 200 ok`() {
        every { employeeService.getSelf(any()) } returns EMPLOYEE_1
        every { cinemaxConfigProperties.branchName } returns BRANCH_NAME

        mvc.get(GET_SELF_PATH) {
            header(HttpHeaders.ACCEPT, ApiVersion.VERSION_1_JSON)
            header(HttpHeaders.AUTHORIZATION, mockAccessToken(EMPLOYEE_1.role))
        }.andExpect {
            status { isOk() }
            content { contentType(ApiVersion.VERSION_1_JSON) }
            jsonContent(
                """
                {
                  "id": "${EMPLOYEE_1.id}",
                  "username": ${EMPLOYEE_1.username},
                  "fullName": "${EMPLOYEE_1.fullName}",
                  "passwordReset": ${EMPLOYEE_1.passwordReset},
                  "role": ${EMPLOYEE_1.role},
                  "lastLoginAt": ${EMPLOYEE_1.lastLoginAt},
                  "branchName": "$BRANCH_NAME"
                }
            """
            )
        }

        verifySequence {
            employeeService.getSelf(
                GetSelfCommand(TEST_PRINCIPAL_USERNAME)
            )
            cinemaxConfigProperties.branchName
        }
    }

    @Test
    fun `test getSelf, authorization header with cashier token, should return 403 access denied`() {
        every { employeeService.getSelf(any()) } returns EMPLOYEE_1

        mvc.get(GET_SELF_PATH) {
            header(HttpHeaders.ACCEPT, ApiVersion.VERSION_1_JSON)
            header(HttpHeaders.AUTHORIZATION, mockAccessToken())
        }.andExpect {
            status { isForbidden() }
            content { contentType(ApiVersion.VERSION_1_JSON) }
        }

        verifySequence {
            employeeService wasNot Called
        }
    }

    @Test
    fun `test resetPassword with authorization, should serialize and deserialize correctly`() {
        every { employeeService.resetPassword(any()) } just Runs

        mvc.put(RESET_PASSWORD_PATH) {
            header(HttpHeaders.ACCEPT, ApiVersion.VERSION_1_JSON)
            header(HttpHeaders.AUTHORIZATION, mockAccessToken(EMPLOYEE_1.role))
            contentType = MediaType.APPLICATION_JSON
            content = """
                {
                    "newPassword": "$NEW_PASSWORD"
                }
            """.trimIndent()
        }.andExpect {
            status { isNoContent() }
        }

        verifySequence {
            employeeService.resetPassword(
                ResetPasswordCommand(
                    employeeUsername = TEST_PRINCIPAL_USERNAME,
                    newPassword = NEW_PASSWORD
                )
            )
        }
    }

    @Test
    fun `test createEmployee, should serialize and deserialize correctly`() {
        every { employeeService.adminCreateOrUpdateEmployee(any()) } just Runs

        mvc.post(MANAGER_BASE_EMPLOYEE_PATH) {
            header(HttpHeaders.ACCEPT, ApiVersion.VERSION_1_JSON)
            header(HttpHeaders.AUTHORIZATION, mockAccessToken(EMPLOYEE_1.role))
            contentType = MediaType.APPLICATION_JSON
            content = """
                {
                    "username": "${EMPLOYEE_2.username}",
                    "fullName": "${EMPLOYEE_2.fullName}",
                    "posName": "${EMPLOYEE_2.posName}",
                    "password": "$DUMMY_PASSWORD",
                    "role": "${EMPLOYEE_2.role}",
                    "accessibleAt": "${EMPLOYEE_2.accessibleAt?.truncatedAndFormatted()}"
                }
            """.trimIndent()
        }.andExpect {
            status { isNoContent() }
        }

        verifySequence {
            employeeService.adminCreateOrUpdateEmployee(
                CreateOrUpdateEmployeeCommand(
                    username = EMPLOYEE_2.username,
                    fullName = EMPLOYEE_2.fullName,
                    posName = EMPLOYEE_2.posName,
                    password = DUMMY_PASSWORD,
                    passwordReset = true,
                    role = EMPLOYEE_2.role,
                    accessibleAt = EMPLOYEE_2.accessibleAt
                )
            )
        }
    }

    @Test
    fun `test searchEmployees, should serialize and deserialize correctly`() {
        every { employeeFinderService.search(any()) } returns PageImpl(listOf(EMPLOYEE_1, EMPLOYEE_3, EMPLOYEE_4))
        every { employeeSearchResponseMapper.mapList(any()) } returns listOf(
            EMPLOYEE_1_RESPONSE,
            EMPLOYEE_3_RESPONSE,
            EMPLOYEE_4_RESPONSE
        )

        mvc.post(SEARCH_EMPLOYEE_PATH) {
            contentType = MediaType.APPLICATION_JSON
            header(HttpHeaders.ACCEPT, ApiVersion.VERSION_1_JSON)
            header(HttpHeaders.AUTHORIZATION, mockAccessToken(role = EmployeeRole.MANAGER))
        }.andExpect {
            status { isOk() }
            content { contentType(ApiVersion.VERSION_1_JSON) }
            jsonContent(
                """
                {
                    "content": [
                        {
                            "id": "${EMPLOYEE_1.id}",
                            "username": "${EMPLOYEE_1.username}",
                            "fullName": "${EMPLOYEE_1.fullName}",
                            "posName": "${EMPLOYEE_1.posName}",
                            "role": "${EMPLOYEE_1.role}",
                            "lastLoginAt": null,
                            "accessibleAt": null,
                            "createdAt": "${EMPLOYEE_1.createdAt.truncatedAndFormatted()}",
                            "updatedAt": "${EMPLOYEE_1.updatedAt.truncatedAndFormatted()}"
                        },
                        {
                            "id": "${EMPLOYEE_3.id}",
                            "username": "${EMPLOYEE_3.username}",
                            "fullName": "${EMPLOYEE_3.fullName}",
                            "posName": "${EMPLOYEE_3.posName}",
                            "role": "${EMPLOYEE_3.role}",
                            "lastLoginAt": null,
                            "accessibleAt": null,
                            "createdAt": "${EMPLOYEE_3.createdAt.truncatedAndFormatted()}",
                            "updatedAt": "${EMPLOYEE_3.updatedAt.truncatedAndFormatted()}"
                        },
                        {
                            "id": "${EMPLOYEE_4.id}",
                            "username": "${EMPLOYEE_4.username}",
                            "fullName": "${EMPLOYEE_4.fullName}",
                            "posName": null,
                            "role": "${EMPLOYEE_4.role}",
                            "lastLoginAt": null,
                            "accessibleAt": null,
                            "createdAt": "${EMPLOYEE_4.createdAt.truncatedAndFormatted()}",
                            "updatedAt": "${EMPLOYEE_4.updatedAt.truncatedAndFormatted()}"
                        }
                    ],
                    "totalElements": 3,
                    "totalPages": 1
                }
              """
            )
        }

        verifySequence {
            employeeFinderService.search(
                SearchUnfilteredQueryDeprecated(
                    pageable = PageRequest.of(0, 10, Sort.by(EmployeeColumnNames.ACCESSIBLE_AT).descending())
                )
            )
            employeeSearchResponseMapper.mapList(listOf(EMPLOYEE_1, EMPLOYEE_3, EMPLOYEE_4))
        }
    }

    @Test
    fun `test deleteEmployee, should serialize and deserialize correctly`() {
        every { employeeService.deleteEmployee(any()) } just Runs

        mvc.delete(DELETE_AND_UPDATE_EMPLOYEE_PATH(EMPLOYEE_2.id)) {
            header(HttpHeaders.ACCEPT, ApiVersion.VERSION_1_JSON)
            header(HttpHeaders.AUTHORIZATION, mockAccessToken(EMPLOYEE_1.role))
            contentType = MediaType.APPLICATION_JSON
        }.andExpect {
            status { isNoContent() }
        }

        verifySequence {
            employeeService.deleteEmployee(
                DeleteEmployeeCommand(
                    employeeId = EMPLOYEE_2.id
                )
            )
        }
    }

    @Test
    fun `test enforcePasswordReset - should serialize and deserialize correctly`() {
        every { employeeService.enforcePasswordReset(any()) } just Runs

        mvc.post(ENFORCE_PASSWORD_RESET_PATH(EMPLOYEE_1.id)) {
            header(HttpHeaders.ACCEPT, ApiVersion.VERSION_1_JSON)
            header(HttpHeaders.AUTHORIZATION, mockAccessToken(EMPLOYEE_1.role))
        }.andExpect {
            status { isNoContent() }
        }

        verifySequence {
            employeeService.enforcePasswordReset(
                EnforcePasswordResetCommand(
                    EMPLOYEE_1.id
                )
            )
        }
    }

    @Test
    fun `test updateEmployee, should serialize and deserialize correctly`() {
        every { employeeService.adminCreateOrUpdateEmployee(any()) } just Runs

        mvc.put(DELETE_AND_UPDATE_EMPLOYEE_PATH(EMPLOYEE_1.id)) {
            header(HttpHeaders.ACCEPT, ApiVersion.VERSION_1_JSON)
            header(HttpHeaders.AUTHORIZATION, mockAccessToken(EMPLOYEE_1.role))
            contentType = MediaType.APPLICATION_JSON
            content = """
                {
                    "username": "${EMPLOYEE_1_UPDATED.username}",
                    "fullName": "${EMPLOYEE_1_UPDATED.fullName}",
                    "posName": "${EMPLOYEE_1_UPDATED.posName}",
                    "password": "$DUMMY_PASSWORD",
                    "role": "${EMPLOYEE_1_UPDATED.role}",
                    "accessibleAt": "${EMPLOYEE_1_UPDATED.accessibleAt!!.truncatedAndFormatted()}"
                }
            """.trimIndent()
        }.andExpect {
            status { isNoContent() }
        }

        verifySequence {
            employeeService.adminCreateOrUpdateEmployee(
                CreateOrUpdateEmployeeCommand(
                    id = EMPLOYEE_1.id,
                    username = EMPLOYEE_1_UPDATED.username,
                    fullName = EMPLOYEE_1_UPDATED.fullName,
                    posName = EMPLOYEE_1_UPDATED.posName,
                    password = DUMMY_PASSWORD,
                    role = EMPLOYEE_1_UPDATED.role,
                    accessibleAt = EMPLOYEE_1_UPDATED.accessibleAt!!.truncatedToSeconds()
                )
            )
        }
    }

    @Test
    fun `test updateEmployee with no password, should serialize and deserialize correctly`() {
        every { employeeService.adminCreateOrUpdateEmployee(any()) } just Runs

        mvc.put(DELETE_AND_UPDATE_EMPLOYEE_PATH(EMPLOYEE_1.id)) {
            header(HttpHeaders.ACCEPT, ApiVersion.VERSION_1_JSON)
            header(HttpHeaders.AUTHORIZATION, mockAccessToken(EMPLOYEE_1.role))
            contentType = MediaType.APPLICATION_JSON
            content = """
                {
                    "username": "${EMPLOYEE_1_UPDATED.username}",
                    "fullName": "${EMPLOYEE_1_UPDATED.fullName}",
                    "posName": "${EMPLOYEE_1_UPDATED.posName}",
                    "role": "${EMPLOYEE_1_UPDATED.role}",
                    "accessibleAt": "${EMPLOYEE_1_UPDATED.accessibleAt!!.truncatedAndFormatted()}"
                }
            """.trimIndent()
        }.andExpect {
            status { isNoContent() }
        }

        verifySequence {
            employeeService.adminCreateOrUpdateEmployee(
                CreateOrUpdateEmployeeCommand(
                    id = EMPLOYEE_1.id,
                    username = EMPLOYEE_1_UPDATED.username,
                    fullName = EMPLOYEE_1_UPDATED.fullName,
                    posName = EMPLOYEE_1_UPDATED.posName,
                    password = null,
                    role = EMPLOYEE_1_UPDATED.role,
                    accessibleAt = EMPLOYEE_1_UPDATED.accessibleAt!!.truncatedToSeconds()
                )
            )
        }
    }
}

private const val LOGIN_EMPLOYEE_PATH = "/manager-app/employees/login"
private const val REFRESH_ACCESS_TOKEN_EMPLOYEE_PATH = "/manager-app/employees/refresh"
private const val GET_SELF_PATH = "/manager-app/employees/self"
private const val RESET_PASSWORD_PATH = "/manager-app/employees/self/reset-password"
private val ENFORCE_PASSWORD_RESET_PATH: (UUID) -> String = { employeeId: UUID ->
    "/manager-app/employees/$employeeId/password-reset"
}
private const val MANAGER_BASE_EMPLOYEE_PATH = "/manager-app/employees"
private const val SEARCH_EMPLOYEE_PATH = "/manager-app/employees/search"
private val DELETE_AND_UPDATE_EMPLOYEE_PATH: (UUID) -> String = { employeeId: UUID -> "/manager-app/employees/$employeeId" }
private const val DUMMY_PASSWORD = "dummyPassword"
private const val NEW_PASSWORD = "newPassword"
private const val DUMMY_PASSWORD_HASH = "dummyPasswordHash"
private const val DUMMY_TOKEN = "dummyToken"
private const val ACCESS_EXPIRATION = 900000
private const val REFRESH_EXPIRATION = 21600000
private const val BRANCH_NAME = "Bratislava Bory"

private val EMPLOYEE_1 = createEmployee(
    originalId = 1,
    username = "manager",
    fullName = "Johnny Manager",
    role = EmployeeRole.MANAGER
)
private val EMPLOYEE_1_UPDATED = createEmployee(
    originalId = 1,
    username = "johnny",
    fullName = "Johnny Cash(ier)",
    role = EmployeeRole.CASHIER,
    accessibleAt = LocalDateTime.now().plusDays(2).truncatedToSeconds()
)
private val EMPLOYEE_2 = createEmployee(
    originalId = null,
    username = "cashier",
    fullName = "Johnny Cashier",
    posName = "pokl1",
    passwordHash = DUMMY_PASSWORD_HASH,
    role = EmployeeRole.CASHIER,
    accessibleAt = LocalDateTime.of(2024, 2, 10, 10, 0)
)
private val EMPLOYEE_3 = createEmployee(
    originalId = null,
    username = "employee3",
    fullName = "Roy Orbison",
    role = EmployeeRole.MANAGER
)
private val EMPLOYEE_4 = createEmployee(
    originalId = null,
    username = "employee4",
    fullName = "Ray Charles",
    posName = null,
    role = EmployeeRole.CASHIER
)
private val EMPLOYEE_1_RESPONSE = mapToEmployeeResponse(EMPLOYEE_1)
private val EMPLOYEE_3_RESPONSE = mapToEmployeeResponse(EMPLOYEE_3)
private val EMPLOYEE_4_RESPONSE = mapToEmployeeResponse(EMPLOYEE_4)
private val ACCESS_TOKEN = mockAccessToken()
private val REFRESH_TOKEN = mockRefreshToken()
private val TOKENS_MODEL = TokensModel(
    accessToken = ACCESS_TOKEN,
    accessExpiresIn = ACCESS_EXPIRATION,
    refreshToken = REFRESH_TOKEN,
    refreshExpiresIn = REFRESH_EXPIRATION
)
private val TOKENS_RESPONSE = TokensResponse(
    accessToken = ACCESS_TOKEN,
    accessExpiresIn = ACCESS_EXPIRATION,
    refreshToken = REFRESH_TOKEN,
    refreshExpiresIn = REFRESH_EXPIRATION
)
