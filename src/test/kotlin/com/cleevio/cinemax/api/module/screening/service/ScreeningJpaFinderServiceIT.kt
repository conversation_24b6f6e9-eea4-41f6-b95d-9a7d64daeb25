package com.cleevio.cinemax.api.module.screening.service

import com.cleevio.cinemax.api.DatabaseCleanup
import com.cleevio.cinemax.api.IntegrationTest
import com.cleevio.cinemax.api.module.auditorium.service.AuditoriumRepository
import com.cleevio.cinemax.api.module.auditoriumlayout.service.AuditoriumLayoutRepository
import com.cleevio.cinemax.api.module.distributor.service.DistributorRepository
import com.cleevio.cinemax.api.module.movie.service.MovieRepository
import com.cleevio.cinemax.api.module.pricecategory.service.PriceCategoryRepository
import com.cleevio.cinemax.api.module.reservation.service.ReservationRepository
import com.cleevio.cinemax.api.module.screening.constant.ScreeningState
import com.cleevio.cinemax.api.module.seat.service.SeatRepository
import com.cleevio.cinemax.api.module.ticket.service.TicketRepository
import com.cleevio.cinemax.api.module.ticketprice.service.TicketPriceRepository
import com.cleevio.cinemax.api.util.createAuditorium
import com.cleevio.cinemax.api.util.createAuditoriumLayout
import com.cleevio.cinemax.api.util.createDistributor
import com.cleevio.cinemax.api.util.createMovie
import com.cleevio.cinemax.api.util.createPriceCategory
import com.cleevio.cinemax.api.util.createReservation
import com.cleevio.cinemax.api.util.createScreening
import com.cleevio.cinemax.api.util.createSeat
import com.cleevio.cinemax.api.util.createTicket
import com.cleevio.cinemax.api.util.createTicketPrice
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.CsvSource
import org.springframework.beans.factory.annotation.Autowired
import java.time.LocalDate
import java.time.LocalTime
import java.util.UUID
import kotlin.test.assertEquals
import kotlin.test.assertFalse

class ScreeningJpaFinderServiceIT @Autowired constructor(
    private val underTest: ScreeningJpaFinderService,
    private val movieRepository: MovieRepository,
    private val distributorRepository: DistributorRepository,
    private val auditoriumRepository: AuditoriumRepository,
    private val screeningRepository: ScreeningRepository,
    private val priceCategoryRepository: PriceCategoryRepository,
    private val auditoriumLayoutRepository: AuditoriumLayoutRepository,
    private val seatRepository: SeatRepository,
    private val reservationsRepository: ReservationRepository,
    private val ticketPriceRepository: TicketPriceRepository,
    private val ticketRepository: TicketRepository,
) : IntegrationTest(DatabaseCleanup.AFTER_EACH) {

    @BeforeEach
    fun setUp() {
        auditoriumRepository.saveAll(setOf(AUDITORIUM_1, AUDITORIUM_2))
        distributorRepository.save(DISTRIBUTOR_1)
        movieRepository.saveAll(setOf(MOVIE_1, MOVIE_2))
        priceCategoryRepository.save(PRICE_CATEGORY_1)
        auditoriumLayoutRepository.save(AUDITORIUM_LAYOUT_1)
        screeningRepository.saveAll(setOf(SCREENING_1, SCREENING_2, SCREENING_3))
        seatRepository.save(SEAT_1)
        reservationsRepository.save(RESERVATION_1)
        ticketPriceRepository.save(TICKET_PRICE_1)
        ticketRepository.save(TICKET_1)
    }

    @Test
    fun `test existsDuplicateInRequestedTimeRange - updating existing screening - should ignore original datetime`() {
        assertFalse(
            underTest.existsPublishedInRequestedTimeRange(
                auditoriumId = SCREENING_3.auditoriumId,
                date = SCREENING_3.date,
                time = SCREENING_3.time,
                movieId = MOVIE_2.id,
                adTimeSlot = SCREENING_3.adTimeSlot,
                screeningId = SCREENING_3.id,
                cancelOrStopScreening = false
            )
        )
    }

    @ParameterizedTest
    @CsvSource(
        "98a8de50-fc0c-45aa-be2e-e68fc38c873a, 2024-08-01, 23:19, false",
        "98a8de50-fc0c-45aa-be2e-e68fc38c873a, 2024-08-01, 23:20, false",
        "98a8de50-fc0c-45aa-be2e-e68fc38c873a, 2024-08-01, 23:21, true",
        "98a8de50-fc0c-45aa-be2e-e68fc38c873a, 2024-08-02, 00:00, true",
        "98a8de50-fc0c-45aa-be2e-e68fc38c873a, 2024-08-02, 01:14, true",
        "98a8de50-fc0c-45aa-be2e-e68fc38c873a, 2024-08-02, 01:15, false",
        "98a8de50-fc0c-45aa-be2e-e68fc38c873a, 2025-08-01, 20:00, false",
        "f632402c-7cee-45f4-9b2a-f0c4dffa7772, 2024-08-01, 20:00, false"
    )
    fun `test existsDuplicateInRequestedTimeRange - should correctly return result`(
        auditoriumId: UUID,
        date: LocalDate,
        time: LocalTime,
        expected: Boolean,
    ) {
        val result = underTest.existsPublishedInRequestedTimeRange(
            auditoriumId = auditoriumId,
            date = date,
            time = time,
            movieId = MOVIE_2.id,
            adTimeSlot = 10,
            screeningId = null,
            cancelOrStopScreening = false
        )

        assertEquals(expected, result)
    }

    @ParameterizedTest
    @CsvSource(
        "df050a61-c592-441d-bb74-9dd11a84aae6, true",
        "0fb5af21-c62a-4874-9c96-5d9a2ddbec29, false"
    )
    fun `test existSoldTicketsForScreening - should correctly return result`(
        screeningId: UUID,
        expected: Boolean,
    ) {
        val result = underTest.existSoldTicketsForScreening(id = screeningId)
        assertEquals(expected, result)
    }

    @Test
    fun `test getAllNonDeletedById - should correctly return existing and non deleted screenings`() {
        screeningRepository.saveAndFlush(SCREENING_4)

        val screenings = underTest.getAllNonDeletedById(
            setOf(
                SCREENING_1.id,
                SCREENING_4.id,
                UUID.fromString("13db7d7f-**************-549e6a88cff7")
            )
        )

        assertEquals(1, screenings.size)
    }
}

private val AUDITORIUM_1 = createAuditorium(
    id = UUID.fromString("98a8de50-fc0c-45aa-be2e-e68fc38c873a"),
    originalId = 1,
    code = "BORY-A13",
    title = "Hlediste 1 (Bory)"
)
private val AUDITORIUM_2 = createAuditorium(
    originalId = 2,
    code = "IMAX"
)
private val AUDITORIUM_LAYOUT_1 = createAuditoriumLayout(originalId = 1, auditoriumId = AUDITORIUM_1.id)
private val PRICE_CATEGORY_1 = createPriceCategory(
    originalId = 1,
    title = "ARTMAX PO 17",
    active = true
)
private val DISTRIBUTOR_1 = createDistributor(originalId = 1)
private val MOVIE_1 = createMovie(
    originalId = 1,
    distributorId = DISTRIBUTOR_1.id,
    duration = 60,
    code = "90786953"
)
private val MOVIE_2 = createMovie(
    originalId = 2,
    distributorId = DISTRIBUTOR_1.id,
    duration = 30,
    code = "90786954"
)
private val SCREENING_1 = createScreening(
    id = UUID.fromString("df050a61-c592-441d-bb74-9dd11a84aae6"),
    originalId = 1,
    auditoriumId = AUDITORIUM_1.id,
    movieId = MOVIE_1.id,
    date = LocalDate.of(2024, 8, 1),
    time = LocalTime.of(20, 0, 0),
    priceCategoryId = PRICE_CATEGORY_1.id,
    auditoriumLayoutId = AUDITORIUM_LAYOUT_1.id,
    adTimeSlot = 15,
    state = ScreeningState.PUBLISHED
)
private val SCREENING_3 = createScreening(
    id = UUID.fromString("0fb5af21-c62a-4874-9c96-5d9a2ddbec29"),
    originalId = 3,
    auditoriumId = AUDITORIUM_1.id,
    movieId = MOVIE_1.id,
    date = LocalDate.of(2024, 8, 2),
    time = LocalTime.of(0, 0, 0),
    priceCategoryId = PRICE_CATEGORY_1.id,
    auditoriumLayoutId = AUDITORIUM_LAYOUT_1.id,
    adTimeSlot = 15,
    state = ScreeningState.PUBLISHED
)
private val SCREENING_2 = createScreening(
    originalId = 2,
    auditoriumId = AUDITORIUM_2.id,
    movieId = MOVIE_1.id,
    date = LocalDate.of(2024, 8, 1),
    time = LocalTime.of(21, 0, 0),
    priceCategoryId = PRICE_CATEGORY_1.id,
    auditoriumLayoutId = AUDITORIUM_LAYOUT_1.id,
    adTimeSlot = 15,
    state = ScreeningState.PUBLISHED
)
private val SCREENING_4 = createScreening(
    originalId = 4,
    auditoriumId = AUDITORIUM_2.id,
    movieId = MOVIE_1.id,
    priceCategoryId = PRICE_CATEGORY_1.id,
    auditoriumLayoutId = AUDITORIUM_LAYOUT_1.id
) { it.markDeleted() }

private val SEAT_1 = createSeat(
    auditoriumId = AUDITORIUM_1.id,
    auditoriumLayoutId = AUDITORIUM_LAYOUT_1.id
)
private val RESERVATION_1 = createReservation(
    screeningId = SCREENING_1.id,
    seatId = SEAT_1.id
)
private val TICKET_PRICE_1 = createTicketPrice(
    screeningId = SCREENING_1.id,
    seatId = SEAT_1.id,
    totalPrice = 100.toBigDecimal()
)
private val TICKET_1 = createTicket(
    screeningId = SCREENING_1.id,
    reservationId = RESERVATION_1.id,
    ticketPriceId = TICKET_PRICE_1.id
)
