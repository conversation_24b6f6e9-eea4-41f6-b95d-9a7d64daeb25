package com.cleevio.cinemax.api.module.distributor.event

import com.cleevio.cinemax.api.common.util.MESSAGING_MAPPER
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertNotNull
import org.junit.jupiter.api.Test

class AdminDistributorDeletedEventTest {

    @Test
    fun `test toMessagePayload - should create message payload with correct type and serialization`() {
        val event = AdminDistributorDeletedEvent(code = "1234")
        val expectedJson = """
            {
                "code": "1234"
            }
        """.trimIndent()

        val messagePayload = event.toMessagePayload()

        assertNotNull(messagePayload.id)
        assertNotNull(messagePayload.createdAt)
        assertEquals("DISTRIBUTOR_DELETED", messagePayload.type.name)
        assertEquals(
            MESSAGING_MAPPER.readTree(expectedJson),
            MESSAGING_MAPPER.readTree(messagePayload.data)
        )
    }
}
