package com.cleevio.cinemax.api.module.movie.service

import com.cleevio.cinemax.api.MssqlIntegrationTest
import com.cleevio.cinemax.api.module.genre.entity.Genre
import com.cleevio.cinemax.api.module.language.entity.Language
import com.cleevio.cinemax.api.module.movie.constant.MovieRating
import com.cleevio.cinemax.api.module.movie.service.command.CreateOrUpdateMovieCommand
import com.cleevio.cinemax.api.module.production.entity.Production
import com.cleevio.cinemax.api.module.rating.entity.Rating
import com.cleevio.cinemax.api.module.screening.constant.MovieFormat
import com.cleevio.cinemax.api.module.screening.constant.MovieLanguage
import com.cleevio.cinemax.api.module.screening.constant.MovieTechnology
import com.cleevio.cinemax.api.module.synchronizationfrommssql.constant.SynchronizationFromMssqlType
import com.cleevio.cinemax.api.module.synchronizationfrommssql.entity.SynchronizationFromMssql
import com.cleevio.cinemax.api.module.synchronizationfrommssql.service.command.CreateOrUpdateSynchronizationFromMssqlCommand
import com.cleevio.cinemax.api.module.technology.entity.Technology
import com.cleevio.cinemax.api.module.tmslanguage.entity.TmsLanguage
import com.cleevio.cinemax.api.util.createDistributor
import com.cleevio.cinemax.mssql.rps_cinemaxe.dbo.tables.pojos.Rprog
import io.mockk.Called
import io.mockk.Runs
import io.mockk.every
import io.mockk.just
import io.mockk.verify
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.test.context.jdbc.Sql
import org.springframework.test.context.jdbc.SqlConfig
import org.springframework.test.context.jdbc.SqlGroup
import java.time.LocalDate
import java.time.LocalDateTime
import java.util.Optional
import kotlin.test.assertEquals
import kotlin.test.assertTrue

@SqlGroup(
    Sql(
        scripts = [
            "/db/mssql-cinemax/test/init_mssql_movie.sql"
        ],
        config = SqlConfig(
            dataSource = "mssqlCinemaxDataSource",
            transactionManager = "mssqlCinemaxTransactionManager"
        )
    ),
    Sql(
        scripts = [
            "/db/mssql-cinemax/test/drop_mssql_movie.sql"
        ],
        config = SqlConfig(
            dataSource = "mssqlCinemaxDataSource",
            transactionManager = "mssqlCinemaxTransactionManager"
        ),
        executionPhase = Sql.ExecutionPhase.AFTER_TEST_METHOD
    )
)
class MovieMssqlSynchronizationServiceIT @Autowired constructor(
    private val underTest: MovieMssqlSynchronizationService,
    private val movieMssqlFinderRepository: MovieMssqlFinderRepository,
) : MssqlIntegrationTest() {

    @Test
    fun `test synchronize all - no PSQL movies, 4 MSSQL movies - should create 3 movies`() {
        every { synchronizationFromMssqlFinderServiceMock.findByType(any()) } returns null
        every { synchronizationFromMssqlServiceMock.createOrUpdate(any()) } returns SYNCHRONIZATION_FROM_MSSQL
        every { distributorJpaFinderServiceMock.findNonDeletedByCode("0") } returns DISTRIBUTOR_1
        every { distributorJpaFinderServiceMock.findNonDeletedByCode("4") } returns DISTRIBUTOR_2
        every { productionJpaFinderServiceMock.findByCode("56") } returns PRODUCTION_1
        every { genreJpaFinderServiceMock.findByCode("i") } returns GENRE_1
        every { genreJpaFinderServiceMock.findByCode("d") } returns GENRE_2
        every { genreJpaFinderServiceMock.findByCode("a") } returns GENRE_3
        every { ratingJpaFinderServiceMock.findByCode("12") } returns RATING_1
        every { screeningMssqlFinderServiceMock.findLatestByMovieOriginalId(1) } returns RPROG_1
        every { screeningMssqlFinderServiceMock.findLatestByMovieOriginalId(2) } returns RPROG_2
        every { screeningMssqlFinderServiceMock.findLatestByMovieOriginalId(3) } returns null
        every { technologyJpaFinderServiceMock.findByCode("05") } returns TECHNOLOGY_1
        every { languageJpaFinderServiceMock.findByCode("CZST") } returns LANGUAGE_1
        every { tmsLanguageJpaFinderServiceMock.findByCode("1") } returns TMS_LANGUAGE_1
        every { tmsLanguageJpaFinderServiceMock.findByCode("2") } returns TMS_LANGUAGE_2
        every { movieServiceMock.syncCreateOrUpdateMovie(any()) } just Runs

        assertTrue(movieMssqlFinderRepository.findAllByUpdatedAtGt().size == 4)

        underTest.synchronizeAll()

        val movieOriginalIdCaptor = mutableListOf<Int>()
        val distributorOriginalCodeCaptor = mutableListOf<String>()
        val productionCodeCaptor = mutableListOf<String>()
        val genreCodeCaptor = mutableListOf<String>()
        val ratingCodeCaptor = mutableListOf<String>()
        val tmsLanguageCodeCaptor = mutableListOf<String>()
        val commandCaptor = mutableListOf<CreateOrUpdateMovieCommand>()

        verify { synchronizationFromMssqlFinderServiceMock.findByType(SynchronizationFromMssqlType.MOVIE) }
        verify { distributorJpaFinderServiceMock.findNonDeletedByCode(capture(distributorOriginalCodeCaptor)) }
        verify { productionJpaFinderServiceMock.findByCode(capture(productionCodeCaptor)) }
        verify { genreJpaFinderServiceMock.findByCode(capture(genreCodeCaptor)) }
        verify { ratingJpaFinderServiceMock.findByCode(capture(ratingCodeCaptor)) }
        verify { screeningMssqlFinderServiceMock.findLatestByMovieOriginalId(capture(movieOriginalIdCaptor)) }
        verify { technologyJpaFinderServiceMock.findByCode(TECHNOLOGY_1.code) }
        verify { languageJpaFinderServiceMock.findByCode(LANGUAGE_1.code) }
        verify { tmsLanguageJpaFinderServiceMock.findByCode(capture(tmsLanguageCodeCaptor)) }
        verify { movieServiceMock.syncCreateOrUpdateMovie(capture(commandCaptor)) }
        verify {
            synchronizationFromMssqlServiceMock.createOrUpdate(
                CreateOrUpdateSynchronizationFromMssqlCommand(
                    type = SynchronizationFromMssqlType.MOVIE,
                    lastSynchronization = MOVIE_3_UPDATED_AT
                )
            )
        }

        assertTrue(movieOriginalIdCaptor.size == 3)
        assertTrue(
            movieOriginalIdCaptor.containsAll(
                setOf(EXPECTED_COMMAND_1.originalId, EXPECTED_COMMAND_2.originalId, EXPECTED_COMMAND_3.originalId)
            )
        )
        assertTrue(distributorOriginalCodeCaptor.size == 3)
        assertTrue(distributorOriginalCodeCaptor.containsAll(setOf(DISTRIBUTOR_1.code, DISTRIBUTOR_2.code)))
        assertTrue(productionCodeCaptor.size == 2)
        assertTrue(productionCodeCaptor.containsAll(setOf(PRODUCTION_1.code)))
        assertTrue(genreCodeCaptor.size == 3)
        assertTrue(genreCodeCaptor.containsAll(setOf(GENRE_1.code, GENRE_2.code, GENRE_3.code)))
        assertTrue(ratingCodeCaptor.size == 1)
        assertTrue(ratingCodeCaptor.containsAll(setOf(RATING_1.code)))
        assertTrue(tmsLanguageCodeCaptor.size == 2)
        assertTrue(tmsLanguageCodeCaptor.containsAll(setOf(TMS_LANGUAGE_1.code, TMS_LANGUAGE_2.code)))

        assertTrue(commandCaptor.size == 3)
        assertEquals(EXPECTED_COMMAND_1, commandCaptor[0])
        assertEquals(EXPECTED_COMMAND_2, commandCaptor[1])
        assertEquals(EXPECTED_COMMAND_3, commandCaptor[2])
    }

    @Test
    fun `test synchronize all - 2 PSQL movies, 4 MSSQL movies - should create 1 movie`() {
        every { synchronizationFromMssqlFinderServiceMock.findByType(any()) } returns MOVIE_2_UPDATED_AT
        every { distributorJpaFinderServiceMock.findNonDeletedByCode(any()) } returns DISTRIBUTOR_1
        every { screeningMssqlFinderServiceMock.findLatestByMovieOriginalId(3) } returns null
        every { movieServiceMock.syncCreateOrUpdateMovie(any()) } just Runs
        every { synchronizationFromMssqlServiceMock.createOrUpdate(any()) } returns SYNCHRONIZATION_FROM_MSSQL

        assertTrue(movieMssqlFinderRepository.findAllByUpdatedAtGt().size == 4)

        underTest.synchronizeAll()

        val commandCaptor = mutableListOf<CreateOrUpdateMovieCommand>()

        verify { synchronizationFromMssqlFinderServiceMock.findByType(SynchronizationFromMssqlType.MOVIE) }
        verify { distributorJpaFinderServiceMock.findNonDeletedByCode("0") }
        verify { productionJpaFinderServiceMock wasNot Called }
        verify { genreJpaFinderServiceMock wasNot Called }
        verify { ratingJpaFinderServiceMock wasNot Called }
        verify { technologyJpaFinderServiceMock wasNot Called }
        verify { languageJpaFinderServiceMock wasNot Called }
        verify { tmsLanguageJpaFinderServiceMock wasNot Called }
        verify { movieServiceMock.syncCreateOrUpdateMovie(capture(commandCaptor)) }
        verify {
            synchronizationFromMssqlServiceMock.createOrUpdate(
                CreateOrUpdateSynchronizationFromMssqlCommand(
                    type = SynchronizationFromMssqlType.MOVIE,
                    lastSynchronization = MOVIE_3_UPDATED_AT
                )
            )
        }

        assertTrue(commandCaptor.size == 1)
        assertEquals(EXPECTED_COMMAND_3, commandCaptor[0])
    }

    @Test
    fun `test synchronize all - movie 3 throws exception - should create 2 movies and set lastSynchronization`() {
        every { synchronizationFromMssqlFinderServiceMock.findByType(any()) } returns null
        every { movieServiceMock.syncCreateOrUpdateMovie(EXPECTED_COMMAND_1) } just Runs
        every { movieServiceMock.syncCreateOrUpdateMovie(EXPECTED_COMMAND_2) } just Runs
        every { movieServiceMock.syncCreateOrUpdateMovie(EXPECTED_COMMAND_3) } throws Exception()
        every { synchronizationFromMssqlServiceMock.createOrUpdate(any()) } returns SYNCHRONIZATION_FROM_MSSQL
        every { distributorJpaFinderServiceMock.findNonDeletedByCode("0") } returns DISTRIBUTOR_1
        every { distributorJpaFinderServiceMock.findNonDeletedByCode("4") } returns DISTRIBUTOR_2
        every { productionJpaFinderServiceMock.findByCode("56") } returns PRODUCTION_1
        every { genreJpaFinderServiceMock.findByCode("i") } returns GENRE_1
        every { genreJpaFinderServiceMock.findByCode("d") } returns GENRE_2
        every { genreJpaFinderServiceMock.findByCode("a") } returns GENRE_3
        every { ratingJpaFinderServiceMock.findByCode("12") } returns RATING_1
        every { screeningMssqlFinderServiceMock.findLatestByMovieOriginalId(1) } returns RPROG_1
        every { screeningMssqlFinderServiceMock.findLatestByMovieOriginalId(2) } returns RPROG_2
        every { screeningMssqlFinderServiceMock.findLatestByMovieOriginalId(3) } returns null
        every { technologyJpaFinderServiceMock.findByCode("05") } returns TECHNOLOGY_1
        every { languageJpaFinderServiceMock.findByCode("CZST") } returns LANGUAGE_1
        every { tmsLanguageJpaFinderServiceMock.findByCode("1") } returns TMS_LANGUAGE_1
        every { tmsLanguageJpaFinderServiceMock.findByCode("2") } returns TMS_LANGUAGE_2

        assertTrue(movieMssqlFinderRepository.findAllByUpdatedAtGt().size == 4)

        underTest.synchronizeAll()

        val movieOriginalIdCaptor = mutableListOf<Int>()
        val distributorOriginalCodeCaptor = mutableListOf<String>()
        val productionCodeCaptor = mutableListOf<String>()
        val genreCodeCaptor = mutableListOf<String>()
        val ratingCodeCaptor = mutableListOf<String>()
        val tmsLanguageCodeCaptor = mutableListOf<String>()
        val commandCaptor = mutableListOf<CreateOrUpdateMovieCommand>()

        verify { synchronizationFromMssqlFinderServiceMock.findByType(SynchronizationFromMssqlType.MOVIE) }
        verify { distributorJpaFinderServiceMock.findNonDeletedByCode(capture(distributorOriginalCodeCaptor)) }
        verify { productionJpaFinderServiceMock.findByCode(capture(productionCodeCaptor)) }
        verify { genreJpaFinderServiceMock.findByCode(capture(genreCodeCaptor)) }
        verify { ratingJpaFinderServiceMock.findByCode(capture(ratingCodeCaptor)) }
        verify { screeningMssqlFinderServiceMock.findLatestByMovieOriginalId(capture(movieOriginalIdCaptor)) }
        verify { technologyJpaFinderServiceMock.findByCode(TECHNOLOGY_1.code) }
        verify { languageJpaFinderServiceMock.findByCode(LANGUAGE_1.code) }
        verify { tmsLanguageJpaFinderServiceMock.findByCode(capture(tmsLanguageCodeCaptor)) }
        verify { movieServiceMock.syncCreateOrUpdateMovie(capture(commandCaptor)) }
        verify {
            synchronizationFromMssqlServiceMock.createOrUpdate(
                CreateOrUpdateSynchronizationFromMssqlCommand(
                    type = SynchronizationFromMssqlType.MOVIE,
                    lastSynchronization = MOVIE_2_UPDATED_AT
                )
            )
        }

        assertTrue(movieOriginalIdCaptor.size == 3)
        assertTrue(
            movieOriginalIdCaptor.containsAll(
                setOf(EXPECTED_COMMAND_1.originalId, EXPECTED_COMMAND_2.originalId, EXPECTED_COMMAND_3.originalId)
            )
        )
        assertTrue(distributorOriginalCodeCaptor.size == 3)
        assertTrue(distributorOriginalCodeCaptor.containsAll(setOf(DISTRIBUTOR_1.code, DISTRIBUTOR_2.code)))
        assertTrue(productionCodeCaptor.size == 2)
        assertTrue(productionCodeCaptor.containsAll(setOf(PRODUCTION_1.code)))
        assertTrue(genreCodeCaptor.size == 3)
        assertTrue(genreCodeCaptor.containsAll(setOf(GENRE_1.code, GENRE_2.code, GENRE_3.code)))
        assertTrue(ratingCodeCaptor.size == 1)
        assertTrue(ratingCodeCaptor.containsAll(setOf(RATING_1.code)))
        assertTrue(tmsLanguageCodeCaptor.size == 2)
        assertTrue(tmsLanguageCodeCaptor.containsAll(setOf(TMS_LANGUAGE_1.code, TMS_LANGUAGE_2.code)))

        assertTrue(commandCaptor.size == 3)
        assertEquals(EXPECTED_COMMAND_1, commandCaptor[0])
        assertEquals(EXPECTED_COMMAND_2, commandCaptor[1])
        assertEquals(EXPECTED_COMMAND_3, commandCaptor[2])
    }

    @Test
    fun `test synchronize all - movie 3 throws exception - should create 2 movies with no related entities and set lastSynchronization`() {
        every { synchronizationFromMssqlFinderServiceMock.findByType(any()) } returns null
        every { movieServiceMock.syncCreateOrUpdateMovie(EXPECTED_COMMAND_1_NULL_ENTITY_IDS) } just Runs
        every { movieServiceMock.syncCreateOrUpdateMovie(EXPECTED_COMMAND_2_NULL_ENTITY_IDS) } just Runs
        every { movieServiceMock.syncCreateOrUpdateMovie(EXPECTED_COMMAND_3) } throws Exception()
        every { synchronizationFromMssqlServiceMock.createOrUpdate(any()) } returns SYNCHRONIZATION_FROM_MSSQL
        every { distributorJpaFinderServiceMock.findNonDeletedByCode("0") } returns DISTRIBUTOR_1
        every { distributorJpaFinderServiceMock.findNonDeletedByCode("4") } returns DISTRIBUTOR_2
        every { productionJpaFinderServiceMock.findByCode("56") } returns null
        every { genreJpaFinderServiceMock.findByCode("i") } returns null
        every { genreJpaFinderServiceMock.findByCode("d") } returns null
        every { genreJpaFinderServiceMock.findByCode("a") } returns null
        every { ratingJpaFinderServiceMock.findByCode("12") } returns null
        every { screeningMssqlFinderServiceMock.findLatestByMovieOriginalId(1) } returns RPROG_1
        every { screeningMssqlFinderServiceMock.findLatestByMovieOriginalId(2) } returns RPROG_2
        every { screeningMssqlFinderServiceMock.findLatestByMovieOriginalId(3) } returns null
        every { technologyJpaFinderServiceMock.findByCode("05") } returns null
        every { languageJpaFinderServiceMock.findByCode("CZST") } returns null
        every { tmsLanguageJpaFinderServiceMock.findByCode("1") } returns null
        every { tmsLanguageJpaFinderServiceMock.findByCode("2") } returns null

        assertTrue(movieMssqlFinderRepository.findAllByUpdatedAtGt().size == 4)

        underTest.synchronizeAll()

        val movieOriginalIdCaptor = mutableListOf<Int>()
        val distributorOriginalCodeCaptor = mutableListOf<String>()
        val productionCodeCaptor = mutableListOf<String>()
        val genreCodeCaptor = mutableListOf<String>()
        val ratingCodeCaptor = mutableListOf<String>()
        val tmsLanguageCodeCaptor = mutableListOf<String>()
        val commandCaptor = mutableListOf<CreateOrUpdateMovieCommand>()

        verify { synchronizationFromMssqlFinderServiceMock.findByType(SynchronizationFromMssqlType.MOVIE) }
        verify { distributorJpaFinderServiceMock.findNonDeletedByCode(capture(distributorOriginalCodeCaptor)) }
        verify { productionJpaFinderServiceMock.findByCode(capture(productionCodeCaptor)) }
        verify { genreJpaFinderServiceMock.findByCode(capture(genreCodeCaptor)) }
        verify { ratingJpaFinderServiceMock.findByCode(capture(ratingCodeCaptor)) }
        verify { screeningMssqlFinderServiceMock.findLatestByMovieOriginalId(capture(movieOriginalIdCaptor)) }
        verify { technologyJpaFinderServiceMock.findByCode(TECHNOLOGY_1.code) }
        verify { languageJpaFinderServiceMock.findByCode(LANGUAGE_1.code) }
        verify { tmsLanguageJpaFinderServiceMock.findByCode(capture(tmsLanguageCodeCaptor)) }
        verify { movieServiceMock.syncCreateOrUpdateMovie(capture(commandCaptor)) }
        verify {
            synchronizationFromMssqlServiceMock.createOrUpdate(
                CreateOrUpdateSynchronizationFromMssqlCommand(
                    type = SynchronizationFromMssqlType.MOVIE,
                    lastSynchronization = MOVIE_2_UPDATED_AT
                )
            )
        }

        assertTrue(movieOriginalIdCaptor.size == 3)
        assertTrue(
            movieOriginalIdCaptor.containsAll(
                setOf(
                    EXPECTED_COMMAND_1_NULL_ENTITY_IDS.originalId,
                    EXPECTED_COMMAND_2_NULL_ENTITY_IDS.originalId,
                    EXPECTED_COMMAND_3.originalId
                )
            )
        )
        assertTrue(distributorOriginalCodeCaptor.size == 3)
        assertTrue(distributorOriginalCodeCaptor.containsAll(setOf(DISTRIBUTOR_1.code, DISTRIBUTOR_2.code)))
        assertTrue(productionCodeCaptor.size == 2)
        assertTrue(productionCodeCaptor.containsAll(setOf(PRODUCTION_1.code)))
        assertTrue(genreCodeCaptor.size == 3)
        assertTrue(genreCodeCaptor.containsAll(setOf(GENRE_1.code, GENRE_2.code, GENRE_3.code)))
        assertTrue(ratingCodeCaptor.size == 1)
        assertTrue(ratingCodeCaptor.containsAll(setOf(RATING_1.code)))
        assertTrue(tmsLanguageCodeCaptor.size == 2)
        assertTrue(tmsLanguageCodeCaptor.containsAll(setOf(TMS_LANGUAGE_1.code, TMS_LANGUAGE_2.code)))

        assertTrue(commandCaptor.size == 3)
        assertEquals(EXPECTED_COMMAND_1_NULL_ENTITY_IDS, commandCaptor[0])
        assertEquals(EXPECTED_COMMAND_2_NULL_ENTITY_IDS, commandCaptor[1])
        assertEquals(EXPECTED_COMMAND_3, commandCaptor[2])
    }
}

private val MOVIE_2_UPDATED_AT = LocalDateTime.of(2020, 7, 27, 19, 38, 0)
private val MOVIE_3_UPDATED_AT = LocalDateTime.of(2023, 4, 1, 13, 53, 0)
private val RPROG_1 = Rprog().apply {
    rfilmid = 1
    jazyk = "CZST"
    cfotrm = "05"
}
private val RPROG_2 = Rprog().apply {
    rfilmid = 2
    jazyk = "  "
    cfotrm = "  "
}
private val DISTRIBUTOR_1 = createDistributor(code = "0")
private val DISTRIBUTOR_2 = createDistributor(
    originalId = 1,
    code = "4"
)
private val PRODUCTION_1 = Production(
    originalId = 98,
    code = "56",
    title = "USA"
)
private val GENRE_1 = Genre(
    originalId = 1,
    code = "i",
    title = "Zivotopisny"
)
private val GENRE_2 = Genre(
    originalId = 2,
    code = "d",
    title = "Fantasy"
)
private val GENRE_3 = Genre(
    originalId = 3,
    code = "a",
    title = "Dobrodruzny"
)
private val RATING_1 = Rating(
    originalId = 1,
    code = "12",
    title = "12"
)
private val TECHNOLOGY_1 = Technology(
    originalId = 12,
    code = "05",
    title = "IMAX"
)
private val LANGUAGE_1 = Language(
    originalId = 5,
    code = "CZST",
    title = "ceske titulky"
)
private val TMS_LANGUAGE_1 = TmsLanguage(
    originalId = 1,
    code = "1",
    title = "ceske titulky"
)
private val TMS_LANGUAGE_2 = TmsLanguage(
    originalId = 2,
    code = "2",
    title = "slovenske titulky"
)
private val EXPECTED_COMMAND_1 = CreateOrUpdateMovieCommand(
    originalId = 1,
    title = "Bohemian Rhapsody",
    rawTitle = "Bohemian Rhapsody 2D (ST)",
    originalTitle = Optional.of("Bohemian Rhapsody 2D (ST)"),
    code = "001080",
    disfilmCode = "CIN16163A2",
    premiereDate = Optional.of(LocalDate.of(2018, 11, 1)),
    duration = 134,
    parsedRating = MovieRating.PLUS_12,
    parsedFormat = MovieFormat.FORMAT_2D,
    parsedLanguage = MovieLanguage.ST,
    distributorId = DISTRIBUTOR_1.id,
    productionId = Optional.of(PRODUCTION_1.id),
    primaryGenreId = Optional.of(GENRE_1.id),
    secondaryGenreId = null,
    ratingId = Optional.of(RATING_1.id),
    technologyId = TECHNOLOGY_1.id,
    languageId = LANGUAGE_1.id,
    tmsLanguageId = TMS_LANGUAGE_2.id
)
private val EXPECTED_COMMAND_1_NULL_ENTITY_IDS = CreateOrUpdateMovieCommand(
    originalId = 1,
    title = "Bohemian Rhapsody",
    rawTitle = "Bohemian Rhapsody 2D (ST)",
    originalTitle = Optional.of("Bohemian Rhapsody 2D (ST)"),
    code = "001080",
    disfilmCode = "CIN16163A2",
    premiereDate = Optional.of(LocalDate.of(2018, 11, 1)),
    duration = 134,
    parsedRating = MovieRating.PLUS_12,
    parsedFormat = MovieFormat.FORMAT_2D,
    parsedLanguage = MovieLanguage.ST,
    distributorId = DISTRIBUTOR_1.id,
    productionId = null,
    primaryGenreId = null,
    secondaryGenreId = null,
    ratingId = null,
    technologyId = null,
    languageId = null,
    tmsLanguageId = null
)
private val EXPECTED_COMMAND_2 = CreateOrUpdateMovieCommand(
    originalId = 2,
    title = "Pán prstenov: Návrat krála",
    rawTitle = "Pán prstenov: Návrat krála",
    originalTitle = Optional.of("LOTR:The Return of the King"),
    code = "200402",
    disfilmCode = null,
    premiereDate = Optional.of(LocalDate.of(2004, 1, 15)),
    duration = 200,
    parsedRating = null,
    parsedFormat = MovieFormat.FORMAT_2D,
    parsedLanguage = null,
    distributorId = DISTRIBUTOR_2.id,
    productionId = Optional.of(PRODUCTION_1.id),
    primaryGenreId = Optional.of(GENRE_2.id),
    secondaryGenreId = Optional.of(GENRE_3.id),
    ratingId = null,
    technologyId = null,
    languageId = null,
    tmsLanguageId = TMS_LANGUAGE_1.id
)
private val EXPECTED_COMMAND_2_NULL_ENTITY_IDS = CreateOrUpdateMovieCommand(
    originalId = 2,
    title = "Pán prstenov: Návrat krála",
    rawTitle = "Pán prstenov: Návrat krála",
    originalTitle = Optional.of("LOTR:The Return of the King"),
    code = "200402",
    disfilmCode = null,
    premiereDate = Optional.of(LocalDate.of(2004, 1, 15)),
    duration = 200,
    parsedRating = null,
    parsedFormat = MovieFormat.FORMAT_2D,
    parsedLanguage = null,
    distributorId = DISTRIBUTOR_2.id,
    productionId = null,
    primaryGenreId = null,
    secondaryGenreId = null,
    ratingId = null,
    technologyId = null,
    languageId = null,
    tmsLanguageId = null
)
private val EXPECTED_COMMAND_3 = CreateOrUpdateMovieCommand(
    originalId = 3,
    title = "Strážcovia galaxie 3",
    rawTitle = "Strážcovia galaxie 3 IMAX 3D (ST)",
    originalTitle = null,
    code = "CN0518",
    disfilmCode = null,
    premiereDate = Optional.of(LocalDate.of(2023, 5, 4)),
    duration = 149,
    parsedRating = MovieRating.UNKNOWN,
    parsedFormat = MovieFormat.FORMAT_3D,
    parsedTechnology = MovieTechnology.IMAX,
    parsedLanguage = MovieLanguage.ST,
    distributorId = DISTRIBUTOR_1.id,
    productionId = null,
    primaryGenreId = null,
    secondaryGenreId = null,
    ratingId = null,
    technologyId = null,
    languageId = null,
    tmsLanguageId = null
)
private val SYNCHRONIZATION_FROM_MSSQL = SynchronizationFromMssql(
    type = SynchronizationFromMssqlType.MOVIE,
    lastSynchronization = LocalDateTime.MIN,
    lastHeartbeat = null
)
