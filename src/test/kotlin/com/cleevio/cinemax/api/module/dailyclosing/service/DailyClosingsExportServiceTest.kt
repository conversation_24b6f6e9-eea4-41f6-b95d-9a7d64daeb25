package com.cleevio.cinemax.api.module.dailyclosing.service

import com.cleevio.cinemax.api.common.constant.ExportFormat
import com.cleevio.cinemax.api.common.constant.PaymentType
import com.cleevio.cinemax.api.common.service.model.ExportResultModel
import com.cleevio.cinemax.api.module.dailyclosing.model.DailyClosingsExportRecordModel
import com.cleevio.cinemax.api.module.dailyclosing.model.DailyClosingsOtherMovementsExportRecordModel
import com.cleevio.cinemax.api.module.dailyclosing.service.query.AdminExportDailyClosingsFilter
import com.cleevio.cinemax.api.module.dailyclosing.service.query.AdminExportDailyClosingsQuery
import com.cleevio.cinemax.api.module.dailyclosingmovement.constant.DailyClosingMovementItemType
import com.cleevio.cinemax.api.module.dailyclosingmovement.constant.DailyClosingMovementType
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import io.mockk.verifySequence
import org.junit.jupiter.api.assertThrows
import java.io.ByteArrayInputStream
import java.math.BigDecimal
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.LocalTime
import java.util.UUID
import kotlin.test.Test
import kotlin.test.assertEquals

class DailyClosingsExportServiceTest {

    private val adminExportDailyClosingsQueryService = mockk<AdminExportDailyClosingsQueryService>()
    private val adminExportDailyClosingsOtherMovementsQueryService = mockk<AdminExportDailyClosingsOtherMovementsQueryService>()
    private val dailyClosingsXlsxExportResultMapper = mockk<DailyClosingsXlsxExportResultMapper>()
    private val underTest = DailyClosingsExportService(
        adminExportDailyClosingsQueryService = adminExportDailyClosingsQueryService,
        adminExportDailyClosingsOtherMovementsQueryService = adminExportDailyClosingsOtherMovementsQueryService,
        dailyClosingsXlsxExportResultMapper = dailyClosingsXlsxExportResultMapper
    )

    @Test
    fun `test exportDailyClosings - valid query with XLSX format - should call related service and mapper`() {
        val now = LocalDateTime.now()
        val username = "username"
        val query = AdminExportDailyClosingsQuery(
            filter = AdminExportDailyClosingsFilter(
                closedAtFrom = now.toLocalDate().minusDays(1),
                closedAtTo = now.toLocalDate(),
                posConfigurationIds = setOf(UUID.fromString("4ce27624-1297-4977-ab91-f562f3a04c48"))
            ),
            exportFormat = ExportFormat.XLSX,
            username = username
        )

        val dailyClosingsData = listOf(
            DailyClosingsExportRecordModel(
                posConfigurationTitle = "POS1",
                closedAtDate = now.toLocalDate(),
                closedAtTime = now.toLocalTime(),
                salesCash = BigDecimal("100.00"),
                salesCashless = BigDecimal("200.00"),
                salesTotal = BigDecimal("300.00"),
                serviceFeesCash = BigDecimal("10.00"),
                serviceFeesCashless = BigDecimal("20.00"),
                cancelledCash = BigDecimal("5.00"),
                cancelledCashless = BigDecimal("10.00"),
                otherMovementsRevenues = BigDecimal("0.00"),
                otherMovementsExpenses = BigDecimal("0.00"),
                fixedPriceTicketsAmount = BigDecimal("0.00"),
                deduction = BigDecimal("5.00"),
                netSales = BigDecimal("285.00")
            )
        )
        val otherMovementsData = listOf(
            DailyClosingsOtherMovementsExportRecordModel(
                posConfigurationTitle = "POS Terminal 1",
                otherMovementTitle = "Cash Deposit",
                createdAtDate = LocalDate.of(2023, 10, 15),
                createdAtTime = LocalTime.of(14, 30, 0),
                itemType = DailyClosingMovementItemType.TICKETS,
                type = DailyClosingMovementType.REVENUE,
                otherReceiptNumber = "RCPT123456",
                variableSymbol = "VS123456",
                paymentType = PaymentType.CASH,
                amount = "Kc 1500.00"
            )
        )
        val byteArray = ByteArray(1024)
        val inputStream = ByteArrayInputStream(byteArray)
        val exportResult = ExportResultModel(inputStream, byteArray.size.toLong())

        every { adminExportDailyClosingsQueryService(query) } returns dailyClosingsData
        every { adminExportDailyClosingsOtherMovementsQueryService(query) } returns otherMovementsData
        every {
            dailyClosingsXlsxExportResultMapper.mapToExportResultModel(
                any(),
                any(),
                any(),
                any()
            )
        } returns exportResult

        assertEquals(exportResult, underTest.exportDailyClosings(query))

        verifySequence {
            adminExportDailyClosingsQueryService(query)
            dailyClosingsXlsxExportResultMapper.mapToExportResultModel(
                closedAtFrom = now.toLocalDate().minusDays(1),
                closedAtTo = now.toLocalDate(),
                data = dailyClosingsData to otherMovementsData,
                username = username
            )
        }
    }

    @Test
    fun `test exportDailyClosings - valid query with XML format - should throw`() {
        val now = LocalDate.now()
        val username = "username"
        val query = AdminExportDailyClosingsQuery(
            filter = AdminExportDailyClosingsFilter(
                closedAtFrom = now.minusDays(1),
                closedAtTo = now,
                posConfigurationIds = setOf(UUID.fromString("4ce27624-1297-4977-ab91-f562f3a04c48"))
            ),
            exportFormat = ExportFormat.XML,
            username = username
        )

        assertThrows<UnsupportedOperationException> { underTest.exportDailyClosings(query) }
        verify(exactly = 0) { adminExportDailyClosingsQueryService(any()) }
        verify(exactly = 0) { dailyClosingsXlsxExportResultMapper.mapToExportResultModel(any(), any(), any(), any()) }
    }
}
