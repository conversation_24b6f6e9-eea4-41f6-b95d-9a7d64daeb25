package com.cleevio.cinemax.api.module.table.service

import com.cleevio.cinemax.api.MssqlIntegrationTest
import com.cleevio.cinemax.api.common.constant.PaymentType
import com.cleevio.cinemax.api.module.posconfiguration.constant.ProductMode
import com.cleevio.cinemax.api.module.synchronizationfrommssql.constant.SynchronizationFromMssqlType
import com.cleevio.cinemax.api.module.synchronizationfrommssql.entity.SynchronizationFromMssql
import com.cleevio.cinemax.api.module.synchronizationfrommssql.service.command.CreateOrUpdateSynchronizationFromMssqlCommand
import com.cleevio.cinemax.api.module.table.constant.TableType
import com.cleevio.cinemax.api.module.table.service.command.CreateOrUpdateTableCommand
import io.mockk.Runs
import io.mockk.every
import io.mockk.just
import io.mockk.verify
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.test.context.jdbc.Sql
import org.springframework.test.context.jdbc.SqlConfig
import org.springframework.test.context.jdbc.SqlGroup
import java.time.LocalDateTime
import kotlin.test.assertEquals
import kotlin.test.assertTrue

@SqlGroup(
    Sql(
        scripts = [
            "/db/mssql-buffet/test/init_mssql_table.sql"
        ],
        config = SqlConfig(
            dataSource = "mssqlBuffetDataSource",
            transactionManager = "mssqlBuffetTransactionManager"
        )
    ),
    Sql(
        scripts = [
            "/db/mssql-buffet/test/drop_mssql_table.sql"
        ],
        config = SqlConfig(
            dataSource = "mssqlBuffetDataSource",
            transactionManager = "mssqlBuffetTransactionManager"
        ),
        executionPhase = Sql.ExecutionPhase.AFTER_TEST_METHOD
    )
)
class TableMssqlSynchronizationServiceIT @Autowired constructor(
    private val underTest: TableMssqlSynchronizationService,
) : MssqlIntegrationTest() {

    @Test
    fun `test synchronizeAll - no PSQL tables, 5 MSSQL tables - should create 3 tables and skip 2`() {
        every { synchronizationFromMssqlFinderServiceMock.findByType(any()) } returns null
        every { tableServiceMock.createOrUpdateTable(any()) } just Runs
        every { synchronizationFromMssqlServiceMock.createOrUpdate(any()) } returns SYNCHRONIZATION_FROM_MSSQL

        underTest.synchronizeAll()

        val commandCaptor = mutableListOf<CreateOrUpdateTableCommand>()

        verify { synchronizationFromMssqlFinderServiceMock.findByType(SynchronizationFromMssqlType.TABLE) }
        verify { tableServiceMock.createOrUpdateTable(capture(commandCaptor)) }
        verify {
            synchronizationFromMssqlServiceMock.createOrUpdate(
                CreateOrUpdateSynchronizationFromMssqlCommand(
                    type = SynchronizationFromMssqlType.TABLE,
                    lastSynchronization = TABLE_3_UPDATED_AT
                )
            )
        }

        assertTrue(commandCaptor.size == 3)
        assertEquals(EXPECTED_COMMAND_1, commandCaptor[0])
        assertEquals(EXPECTED_COMMAND_2, commandCaptor[1])
        assertEquals(EXPECTED_COMMAND_3, commandCaptor[2])
    }
}

private val TABLE_3_UPDATED_AT = LocalDateTime.of(2023, 3, 26, 15, 53, 0)

private val EXPECTED_COMMAND_1 = CreateOrUpdateTableCommand(
    originalId = 1,
    title = "Stol 1",
    label = "1",
    order = 1,
    type = TableType.TABLE,
    productMode = ProductMode.CAFE,
    paymentType = PaymentType.CASH,
    discountCardCode = null,
    ipAddress = null
)
private val EXPECTED_COMMAND_2 = CreateOrUpdateTableCommand(
    originalId = 2,
    title = "E14, 1211",
    label = "E14, 1211",
    order = null,
    type = TableType.SEAT,
    productMode = ProductMode.VIP,
    paymentType = PaymentType.CASHLESS,
    discountCardCode = null,
    ipAddress = "************"
)
private val EXPECTED_COMMAND_3 = CreateOrUpdateTableCommand(
    originalId = 3,
    title = "F13, 1211",
    label = "F13, 1211",
    order = null,
    type = TableType.SEAT,
    productMode = ProductMode.VIP,
    paymentType = PaymentType.CASH,
    discountCardCode = "123456789",
    ipAddress = "************"
)
private val SYNCHRONIZATION_FROM_MSSQL = SynchronizationFromMssql(
    type = SynchronizationFromMssqlType.TABLE,
    lastSynchronization = LocalDateTime.MIN,
    lastHeartbeat = null
)
