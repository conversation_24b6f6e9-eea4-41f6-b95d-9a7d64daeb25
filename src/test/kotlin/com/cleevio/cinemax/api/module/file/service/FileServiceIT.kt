package com.cleevio.cinemax.api.module.file.service

import com.cleevio.cinemax.api.DatabaseCleanup
import com.cleevio.cinemax.api.IntegrationTest
import com.cleevio.cinemax.api.module.file.constant.FileType
import com.cleevio.cinemax.api.module.file.entity.File
import com.cleevio.cinemax.api.module.file.event.FileCreatedEvent
import com.cleevio.cinemax.api.module.file.event.FileImageUploadedEvent
import com.cleevio.cinemax.api.module.file.service.command.UpdateFileOriginalIdCommand
import com.cleevio.cinemax.api.module.file.service.command.UploadFileCommand
import com.cleevio.cinemax.api.util.createFile
import com.cleevio.cinemax.api.util.mapToCreateOrUpdateFileCommand
import io.mockk.Runs
import io.mockk.every
import io.mockk.just
import io.mockk.mockk
import io.mockk.verify
import jakarta.validation.ValidationException
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.CsvSource
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.web.multipart.MultipartFile
import java.util.Base64
import kotlin.jvm.optionals.getOrNull
import kotlin.test.assertEquals
import kotlin.test.assertNotNull
import kotlin.test.assertNull
import kotlin.test.assertTrue

class FileServiceIT @Autowired constructor(
    private val underTest: FileService,
    private val fileRepository: FileRepository,
) : IntegrationTest(DatabaseCleanup.AFTER_EACH) {

    @Test
    fun `test createOrUpdateFile - should create file`() {
        val testInputStream = TEST_IMAGE_BYTE_ARRAY_1.inputStream()
        val command = mapToCreateOrUpdateFileCommand(FILE_1, testInputStream)
        underTest.createOrUpdateFile(command)

        val createdFile = fileRepository.findByOriginalId(FILE_1.originalId!!)
        assertNotNull(createdFile)
        assertFileEquals(FILE_1, createdFile)
    }

    @Test
    fun `test createOrUpdateFile - one file exists - insert equal file so it should update`() {
        val testInputStream = TEST_IMAGE_BYTE_ARRAY_1.inputStream()
        val command = mapToCreateOrUpdateFileCommand(FILE_1, testInputStream)
        underTest.createOrUpdateFile(command)

        val createdFile = fileRepository.findByOriginalId(FILE_1.originalId!!)
        assertNotNull(createdFile)
        assertFileEquals(FILE_1, createdFile)

        underTest.createOrUpdateFile(command)

        val files = fileRepository.findAll()
        assertEquals(files.size, 1)
        assertFileEquals(FILE_1, files[0])
        assertTrue { files[0].updatedAt.isAfter(FILE_1.updatedAt) }
    }

    @Test
    fun `test createOrUpdateFile - two files - should create two files`() {
        val testInputStream = TEST_IMAGE_BYTE_ARRAY_1.inputStream()
        val command1 = mapToCreateOrUpdateFileCommand(FILE_1, testInputStream)
        underTest.createOrUpdateFile(command1)
        val command2 = mapToCreateOrUpdateFileCommand(FILE_2, testInputStream)
        underTest.createOrUpdateFile(command2)

        val files = fileRepository.findAll()
        assertEquals(files.size, 2)
        assertFileEquals(FILE_1, files.first { it.originalId == FILE_1.originalId })
        assertFileEquals(FILE_2, files.first { it.originalId == FILE_2.originalId })
    }

    @Test
    fun `test createOrUpdateFile - update attributes`() {
        val testInputStream1 = TEST_IMAGE_BYTE_ARRAY_1.inputStream()
        val command = mapToCreateOrUpdateFileCommand(FILE_1, testInputStream1)
        underTest.createOrUpdateFile(command)

        val createdFile = fileRepository.findByOriginalId(FILE_1.originalId!!)
        assertNotNull(createdFile)
        assertFileEquals(FILE_1, createdFile)

        val testInputStream2 = TEST_IMAGE_BYTE_ARRAY_2.inputStream()
        val updateCommand = command.copy(
            originalName = "KITKAT_MAXI.JPG",
            extension = "JPG",
            inputStream = testInputStream2
        )

        underTest.createOrUpdateFile(updateCommand)

        val updatedFile = fileRepository.findByOriginalId(FILE_1.originalId!!)
        assertNotNull(updatedFile)
        assertEquals(FILE_1.originalId, updatedFile.originalId)
        assertEquals("KITKAT_MAXI.JPG", updatedFile.originalName)
        assertEquals(FILE_1.type, updatedFile.type)
        assertEquals("JPG", updatedFile.extension)
        assertNotNull(updatedFile.createdAt)
        assertNotNull(updatedFile.updatedAt)
        assertTrue { updatedFile.updatedAt.isAfter(FILE_1.updatedAt) }
    }

    @ParameterizedTest
    @CsvSource(value = ["pilsnerjpg", "pilsner.bmp"])
    fun `test uploadFile - unrecognizable file extension or unsupported file format - should throw`(
        originalFilename: String,
    ) {
        val fileMock = mockk<MultipartFile>()
        val testInputStream = "pilsner.png".byteInputStream()

        every { fileMock.originalFilename } returns originalFilename
        every { fileMock.inputStream } returns testInputStream

        val command = UploadFileCommand(
            multipartFile = fileMock,
            type = FileType.PRODUCT_CATEGORY_IMAGE
        )

        assertThrows<ValidationException> {
            underTest.uploadFile(command)
        }
    }

    @Test
    fun `test uploadFile - should create file`() {
        val fileMock = mockk<MultipartFile>()
        val testInputStream = "pilsner.png".byteInputStream()
        val testInputBytes = testInputStream.readAllBytes()
        val base64Content = Base64.getEncoder().encodeToString(testInputBytes)

        every { fileMock.originalFilename } returns "pilsner.png"
        every { fileMock.inputStream } returns testInputStream
        every { fileMock.bytes } returns testInputBytes
        every { applicationEventPublisherMock.publishEvent(any<FileCreatedEvent>()) } just Runs
        every { applicationEventPublisherMock.publishEvent(any<FileImageUploadedEvent>()) } just Runs

        val command = UploadFileCommand(
            multipartFile = fileMock,
            type = FileType.PRODUCT_CATEGORY_IMAGE
        )
        val fileId = underTest.uploadFile(command)

        val createdFile = fileRepository.findById(fileId).getOrNull()
        assertNotNull(createdFile)
        assertFileEquals(FILE_3, createdFile)

        verify { applicationEventPublisherMock.publishEvent(FileCreatedEvent(fileId = createdFile.id)) }
        verify {
            applicationEventPublisherMock.publishEvent(
                withArg<FileImageUploadedEvent> {
                    assertEquals(createdFile.id, it.fileId)
                    assertEquals("${createdFile.id}.png", it.originalName)
                    assertEquals("png", it.extension)
                    assertEquals(FileType.PRODUCT_CATEGORY_IMAGE, it.type)
                    assertEquals(base64Content, it.base64Content)
                }
            )
        }
    }

    @Test
    fun `test updateFileOriginalId - should correctly update in db`() {
        val file = FILE_3.also { fileRepository.save(it) }
        assertNull(fileRepository.findById(file.id).get().originalId)

        underTest.updateFileOriginalId(
            UpdateFileOriginalIdCommand(
                fileId = file.id,
                originalId = 5
            )
        )

        assertEquals(5, fileRepository.findById(file.id).get().originalId)
    }

    private fun assertFileEquals(expected: File, actual: File) {
        assertEquals(expected.originalId, actual.originalId)
        assertEquals(expected.type, actual.type)
        assertEquals(expected.originalName ?: actual.getFilename(), actual.originalName)
        assertEquals(expected.extension, actual.extension)
        assertNotNull(actual.createdAt)
        assertNotNull(actual.updatedAt)
    }
}

private const val TEST_IMAGE_BYTE_ARRAY_RAW_1 = "89504E470D0A1A0A0000000D49484452000000640000004B0806000000861822B700" +
    "0000097048597300000B1300000B1301009A9C18000000C549444154789CEDD1310D00300CC0B01EE58FB9F7102C878D20527648D9DF01BC" +
    "0C893124C6901843620C893124C6901843620C893124C6901843620C893124C6901843620C893124C6901843620C893124C6901843620C89" +
    "3124C6901843620C893124C6901843620C893124C6901843620C893124C6901843620C893124C6901843620C893124C6901843620C893124" +
    "C6901843620C893124C6901843620C893124C6901843620C893124C6901843620C893124C6901843620C893124C6901843620C89393A9D01" +
    "2D913CA7FA0000000049454E44AE426082"
private const val TEST_IMAGE_BYTE_ARRAY_RAW_2 = "05674E470D0A1A0A0000000D49484452000000640000004B0806000000861822B700" +
    "0000097048597300000B1300000B1301009A9C18000000C549444154789CEDD1310D00300CC0B01EE58FB9F7102C878D20527648D9DF01BC" +
    "0C893124C6901843620C893124C6901843620C893124C6901843620C893124C6901843620C893124C6901843620C893124C6901843620C89" +
    "3124C6901843620C893124C6901843620C893124C6901843620C893124C6901843620C893124C6901843620C893124C6901843620C893124" +
    "C6901843620C893124C6901843620C893124C6901843620C893124C6901843620C893124C6901843620C893124C6901843620C89393A9D01" +
    "2D913CA7FA0000000049454E44AE421234"

private val TEST_IMAGE_BYTE_ARRAY_1 = TEST_IMAGE_BYTE_ARRAY_RAW_1.chunked(2)
    .map { it.toInt(16).toByte() }
    .toByteArray()
private val TEST_IMAGE_BYTE_ARRAY_2 = TEST_IMAGE_BYTE_ARRAY_RAW_2.chunked(2)
    .map { it.toInt(16).toByte() }
    .toByteArray()

private val FILE_1 = createFile(
    originalName = "KITKAT.PNG",
    extension = "PNG"
)
private val FILE_2 = createFile(
    originalId = 2,
    type = FileType.PRODUCT_CATEGORY_IMAGE,
    originalName = "POPCORN2.JPG",
    extension = "JPG"
)
private val FILE_3 = createFile(
    originalId = null,
    type = FileType.PRODUCT_CATEGORY_IMAGE,
    originalName = null,
    extension = "png"
)
