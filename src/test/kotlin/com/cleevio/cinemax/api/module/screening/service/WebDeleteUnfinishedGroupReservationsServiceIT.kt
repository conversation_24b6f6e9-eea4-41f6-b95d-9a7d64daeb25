package com.cleevio.cinemax.api.module.screening.service

import com.cleevio.cinemax.api.DatabaseCleanup
import com.cleevio.cinemax.api.IntegrationDataTestHelper
import com.cleevio.cinemax.api.IntegrationTest
import com.cleevio.cinemax.api.module.basket.constant.BasketState
import com.cleevio.cinemax.api.module.basket.event.BasketItemModifiedEvent
import com.cleevio.cinemax.api.module.basket.service.BasketRepository
import com.cleevio.cinemax.api.module.basketitem.constant.BasketItemType
import com.cleevio.cinemax.api.module.basketitem.service.BasketItemRepository
import com.cleevio.cinemax.api.module.groupreservation.constant.GroupReservationType
import com.cleevio.cinemax.api.module.groupreservation.service.GroupReservationRepository
import com.cleevio.cinemax.api.module.reservation.constant.ReservationState
import com.cleevio.cinemax.api.module.reservation.service.ReservationRepository
import com.cleevio.cinemax.api.module.screening.service.command.DeleteUnfinishedGroupReservationCommand
import com.cleevio.cinemax.api.module.ticket.service.TicketRepository
import com.cleevio.cinemax.api.util.toUUID
import io.kotest.matchers.shouldBe
import io.mockk.Runs
import io.mockk.every
import io.mockk.just
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import java.math.BigDecimal

class WebDeleteUnfinishedGroupReservationsServiceIT @Autowired constructor(
    private val underTest: WebDeleteUnfinishedGroupReservationsService,
    private val groupReservationRepository: GroupReservationRepository,
    private val reservationRepository: ReservationRepository,
    private val basketRepository: BasketRepository,
    private val basketItemRepository: BasketItemRepository,
    private val ticketRepository: TicketRepository,
    private val integrationDataTestHelper: IntegrationDataTestHelper,
) : IntegrationTest(DatabaseCleanup.AFTER_EACH) {

    @BeforeEach
    fun setUp() {
        every { applicationEventPublisherMock.publishEvent(any()) } just Runs
        every { applicationEventPublisherMock.publishEvent(any<BasketItemModifiedEvent>()) } just Runs
    }

    @Test
    fun `test DeleteUnfinishedGroupReservationCommand - should delete single group reservation with all related entities`() {
        val auditoriumLayout = integrationDataTestHelper.getAuditoriumLayout(
            auditoriumId = integrationDataTestHelper.getAuditorium(
                id = 1.toUUID()
            )
        )

        val seat = integrationDataTestHelper.getSeat(
            id = 10.toUUID(),
            originalId = 1,
            row = "1",
            number = "1",
            auditoriumId = auditoriumLayout.auditoriumId,
            auditoriumLayoutId = auditoriumLayout.id
        )

        val screening = integrationDataTestHelper.getScreening(
            id = 100.toUUID(),
            originalId = 100,
            auditoriumId = auditoriumLayout.auditoriumId,
            auditoriumLayoutId = auditoriumLayout.id,
            priceCategoryId = integrationDataTestHelper.getPriceCategory(
                id = 1000.toUUID()
            ).id
        )
        integrationDataTestHelper.getScreeningFee(
            screeningId = screening.id,
            originalScreeningId = screening.originalId
        )
        integrationDataTestHelper.getPriceCategoryItem(
            priceCategoryId = screening.priceCategoryId
        )

        val groupReservation = integrationDataTestHelper.getGroupReservation(
            id = 1000.toUUID(),
            type = GroupReservationType.ONLINE
        )
        val reservation = integrationDataTestHelper.getReservation(
            id = 100.toUUID(),
            groupReservationId = groupReservation.id,
            screeningId = screening.id,
            seatId = seat.id,
            state = ReservationState.ONLINE_RESERVED
        )

        val ticket = integrationDataTestHelper.getTicket(
            id = 2222.toUUID(),
            originalId = 2222,
            screeningId = screening.id,
            reservationId = reservation.id,
            ticketPriceId = integrationDataTestHelper.getTicketPrice(
                seatId = seat.id,
                screeningId = screening.id,
                totalPrice = BigDecimal.TEN
            ).id
        )

        val basket = integrationDataTestHelper.getBasket(state = BasketState.OPEN_ONLINE)
        integrationDataTestHelper.getBasketItem(
            basketId = basket.id,
            ticketId = ticket.id,
            type = BasketItemType.TICKET,
            price = BigDecimal.TEN
        )

        underTest(
            DeleteUnfinishedGroupReservationCommand(
                groupReservationId = groupReservation.id
            )
        )

        val updatedGroupReservations = groupReservationRepository.findAll()
        updatedGroupReservations.size shouldBe 1
        updatedGroupReservations.forEach { it.isDeleted() shouldBe true }

        val updatedReservations = reservationRepository.findAll()
        updatedReservations.size shouldBe 1
        updatedReservations.forEach { it.isDeleted() shouldBe true }

        val updatedBaskets = basketRepository.findAll()
        updatedBaskets.size shouldBe 1
        updatedBaskets.forEach { it.isDeleted() shouldBe true }

        val updatedBasketItems = basketItemRepository.findAll()
        updatedBasketItems.size shouldBe 1
        updatedBasketItems.forEach { it.isDeleted() shouldBe true }

        val updatedTickets = ticketRepository.findAll()
        updatedTickets.size shouldBe 1
        updatedTickets.forEach { it.isDeleted() shouldBe true }
    }

    @Test
    fun `test DeleteUnfinishedGroupReservationCommand - should delete multiple group reservations with all related entities`() {
        val auditoriumLayout = integrationDataTestHelper.getAuditoriumLayout(
            originalId = 2,
            auditoriumId = integrationDataTestHelper.getAuditorium(
                id = 2.toUUID(),
                originalId = 2
            )
        )

        val seat1 = integrationDataTestHelper.getSeat(
            id = 110.toUUID(),
            originalId = 11,
            row = "1",
            number = "1",
            auditoriumId = auditoriumLayout.auditoriumId,
            auditoriumLayoutId = auditoriumLayout.id
        )
        val seat2 = integrationDataTestHelper.getSeat(
            id = 121.toUUID(),
            originalId = 121,
            row = "2",
            number = "2",
            auditoriumId = auditoriumLayout.auditoriumId,
            auditoriumLayoutId = auditoriumLayout.id
        )
        val seat3 = integrationDataTestHelper.getSeat(
            id = 132.toUUID(),
            originalId = 132,
            row = "3",
            number = "3",
            auditoriumId = auditoriumLayout.auditoriumId,
            auditoriumLayoutId = auditoriumLayout.id
        )

        val distributor1 = integrationDataTestHelper.getDistributor(
            originalId = 1,
            code = "D1"
        )
        val distributor2 = integrationDataTestHelper.getDistributor(
            originalId = 2,
            code = "D2"
        )

        val screening1 = integrationDataTestHelper.getScreening(
            id = 100.toUUID(),
            originalId = 100,
            auditoriumId = auditoriumLayout.auditoriumId,
            auditoriumLayoutId = auditoriumLayout.id,
            movieId = integrationDataTestHelper.getMovie(
                originalId = 1,
                distributorId = distributor1.id
            ).id,
            priceCategoryId = integrationDataTestHelper.getPriceCategory(
                id = 1000.toUUID()
            ).id
        )
        integrationDataTestHelper.getScreeningFee(
            screeningId = screening1.id,
            originalScreeningId = screening1.originalId
        )
        integrationDataTestHelper.getPriceCategoryItem(
            priceCategoryId = screening1.priceCategoryId
        )

        val screening2 = integrationDataTestHelper.getScreening(
            id = 200.toUUID(),
            originalId = 200,
            auditoriumId = auditoriumLayout.auditoriumId,
            auditoriumLayoutId = auditoriumLayout.id,
            movieId = integrationDataTestHelper.getMovie(
                originalId = 2,
                distributorId = distributor2.id
            ).id,
            priceCategoryId = integrationDataTestHelper.getPriceCategory(
                id = 2000.toUUID(),
                originalId = 2
            ).id
        )
        integrationDataTestHelper.getScreeningFee(
            screeningId = screening2.id,
            originalScreeningId = screening2.originalId
        )
        integrationDataTestHelper.getPriceCategoryItem(
            priceCategoryId = screening2.priceCategoryId
        )

        // first group reservation with 2 reservations
        val groupReservation1 = integrationDataTestHelper.getGroupReservation(
            id = 11000.toUUID(),
            originalId = 11,
            type = GroupReservationType.ONLINE
        )
        val reservation1 = integrationDataTestHelper.getReservation(
            id = 1100.toUUID(),
            groupReservationId = groupReservation1.id,
            screeningId = screening1.id,
            seatId = seat1.id,
            state = ReservationState.ONLINE_RESERVED
        )
        val reservation2 = integrationDataTestHelper.getReservation(
            id = 1200.toUUID(),
            groupReservationId = groupReservation1.id,
            screeningId = screening1.id,
            seatId = seat2.id,
            state = ReservationState.ONLINE_RESERVED
        )

        val ticket1 = integrationDataTestHelper.getTicket(
            id = 12222.toUUID(),
            originalId = 12222,
            screeningId = screening1.id,
            reservationId = reservation1.id,
            ticketPriceId = integrationDataTestHelper.getTicketPrice(
                seatId = seat1.id,
                screeningId = screening1.id,
                totalPrice = BigDecimal.TEN
            ).id
        )
        val ticket2 = integrationDataTestHelper.getTicket(
            id = 13333.toUUID(),
            originalId = 13333,
            screeningId = screening1.id,
            reservationId = reservation2.id,
            ticketPriceId = integrationDataTestHelper.getTicketPrice(
                seatId = seat2.id,
                screeningId = screening1.id,
                totalPrice = BigDecimal.TEN
            ).id
        )

        val basket1 = integrationDataTestHelper.getBasket(
            id = 14444.toUUID(),
            state = BasketState.OPEN_ONLINE
        )
        integrationDataTestHelper.getBasketItem(
            basketId = basket1.id,
            ticketId = ticket1.id,
            type = BasketItemType.TICKET,
            price = BigDecimal.TEN
        )
        integrationDataTestHelper.getBasketItem(
            basketId = basket1.id,
            ticketId = ticket2.id,
            type = BasketItemType.TICKET,
            price = BigDecimal.TEN
        )

        // second group reservation with 1 reservation
        val groupReservation2 = integrationDataTestHelper.getGroupReservation(
            id = 22000.toUUID(),
            originalId = 22,
            type = GroupReservationType.ONLINE
        )
        val reservation3 = integrationDataTestHelper.getReservation(
            id = 1300.toUUID(),
            groupReservationId = groupReservation2.id,
            screeningId = screening2.id,
            seatId = seat3.id,
            state = ReservationState.ONLINE_RESERVED
        )

        val ticket3 = integrationDataTestHelper.getTicket(
            id = 15555.toUUID(),
            originalId = 15555,
            screeningId = screening2.id,
            reservationId = reservation3.id,
            ticketPriceId = integrationDataTestHelper.getTicketPrice(
                seatId = seat3.id,
                screeningId = screening2.id,
                totalPrice = BigDecimal.TEN
            ).id
        )

        val basket2 = integrationDataTestHelper.getBasket(
            id = 16666.toUUID(),
            state = BasketState.OPEN_ONLINE
        )
        integrationDataTestHelper.getBasketItem(
            basketId = basket2.id,
            ticketId = ticket3.id,
            type = BasketItemType.TICKET,
            price = BigDecimal.TEN
        )

        underTest(
            DeleteUnfinishedGroupReservationCommand(
                groupReservationId = groupReservation1.id
            )
        )

        val updatedGroupReservations = groupReservationRepository.findAll()
        updatedGroupReservations.size shouldBe 2
        val deletedGroupReservation = updatedGroupReservations.find { it.id == groupReservation1.id }
        deletedGroupReservation?.isDeleted() shouldBe true
        val nonDeletedGroupReservation = updatedGroupReservations.find { it.id == groupReservation2.id }
        nonDeletedGroupReservation?.isDeleted() shouldBe false

        val updatedReservations = reservationRepository.findAll()
        updatedReservations.size shouldBe 3
        val deletedReservations = updatedReservations.filter { it.groupReservationId == groupReservation1.id }
        deletedReservations.size shouldBe 2
        deletedReservations.forEach { it.isDeleted() shouldBe true }
        val nonDeletedReservation = updatedReservations.find { it.groupReservationId == groupReservation2.id }
        nonDeletedReservation?.isDeleted() shouldBe false

        val updatedBaskets = basketRepository.findAll()
        updatedBaskets.size shouldBe 2
        val deletedBasket = updatedBaskets.find { it.id == basket1.id }
        deletedBasket?.isDeleted() shouldBe true
        val nonDeletedBasket = updatedBaskets.find { it.id == basket2.id }
        nonDeletedBasket?.isDeleted() shouldBe false

        val updatedBasketItems = basketItemRepository.findAll()
        updatedBasketItems.size shouldBe 3
        val deletedBasketItems = updatedBasketItems.filter { it.basketId == basket1.id }
        deletedBasketItems.size shouldBe 2
        deletedBasketItems.forEach { it.isDeleted() shouldBe true }
        val nonDeletedBasketItem = updatedBasketItems.find { it.basketId == basket2.id }
        nonDeletedBasketItem?.isDeleted() shouldBe false

        val updatedTickets = ticketRepository.findAll()
        updatedTickets.size shouldBe 3
        val deletedTickets = updatedTickets.filter { it.id == ticket1.id || it.id == ticket2.id }
        deletedTickets.size shouldBe 2
        deletedTickets.forEach { it.isDeleted() shouldBe true }
        val nonDeletedTicket = updatedTickets.find { it.id == ticket3.id }
        nonDeletedTicket?.isDeleted() shouldBe false
    }
}
