package com.cleevio.cinemax.api.module.technology.controller

import com.cleevio.cinemax.api.ControllerTest
import com.cleevio.cinemax.api.common.constant.ApiVersion
import com.cleevio.cinemax.api.common.descriptor.mssql.DescriptorMssqlResponse
import com.cleevio.cinemax.api.common.util.jsonContent
import com.cleevio.cinemax.api.module.employee.constant.EmployeeRole
import com.cleevio.cinemax.api.module.technology.entity.Technology
import com.cleevio.cinemax.api.util.mockAccessToken
import io.mockk.every
import io.mockk.verify
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest
import org.springframework.http.HttpHeaders
import org.springframework.test.web.servlet.MockMvc
import org.springframework.test.web.servlet.get

@WebMvcTest(AdminTechnologyController::class)
class AdminTechnologyControllerTest @Autowired constructor(
    private val mvc: MockMvc,
) : ControllerTest() {

    @Test
    fun `test getTechnologies, should serialize and deserialize correctly`() {
        every { technologyJooqFinderService.findAll() } returns listOf(TECHNOLOGY_1, TECHNOLOGY_2, TECHNOLOGY_3)
        every { descriptorMssqlResponseMapper.mapList(any()) } returns listOf(
            TECHNOLOGY_1_RESPONSE,
            TECHNOLOGY_2_RESPONSE,
            TECHNOLOGY_3_RESPONSE
        )

        mvc.get(GET_TECHNOLOGIES_PATH) {
            header(HttpHeaders.ACCEPT, ApiVersion.VERSION_1_JSON)
            header(HttpHeaders.AUTHORIZATION, mockAccessToken(role = EmployeeRole.MANAGER))
        }.andExpect {
            status { isOk() }
            content { contentType(ApiVersion.VERSION_1_JSON) }
            jsonContent(
                """
                [{
                  "id": "${TECHNOLOGY_1.id}",
                  "title": "${TECHNOLOGY_1.title}"
                },
                {
                  "id": "${TECHNOLOGY_2.id}",
                  "title": "${TECHNOLOGY_2.title}"
                },
                {
                  "id": "${TECHNOLOGY_3.id}",
                  "title": "${TECHNOLOGY_3.title}"
                }]
            """
            )
        }

        verify {
            descriptorMssqlResponseMapper.mapList(listOf(TECHNOLOGY_1, TECHNOLOGY_2, TECHNOLOGY_3))
        }
    }
}

private const val GET_TECHNOLOGIES_PATH = "/manager-app/technologies"
private val TECHNOLOGY_1 = Technology(
    originalId = 8,
    code = "01",
    title = "Analog"
)
private val TECHNOLOGY_2 = Technology(
    originalId = 9,
    code = "02",
    title = "DVD"
)
private val TECHNOLOGY_3 = Technology(
    originalId = 12,
    code = "05",
    title = "IMAX"
)
private val TECHNOLOGY_1_RESPONSE = DescriptorMssqlResponse(
    id = TECHNOLOGY_1.id,
    title = TECHNOLOGY_1.title
)
private val TECHNOLOGY_2_RESPONSE = DescriptorMssqlResponse(
    id = TECHNOLOGY_2.id,
    title = TECHNOLOGY_2.title
)
private val TECHNOLOGY_3_RESPONSE = DescriptorMssqlResponse(
    id = TECHNOLOGY_3.id,
    title = TECHNOLOGY_3.title
)
