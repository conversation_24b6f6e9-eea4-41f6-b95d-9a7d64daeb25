package com.cleevio.cinemax.api.module.outboxevent.event.listener

import com.cleevio.cinemax.api.module.outboxevent.constant.OutboxEventType
import com.cleevio.cinemax.api.module.outboxevent.service.OutboxEventService
import com.cleevio.cinemax.api.module.outboxevent.service.command.CreateOutboxEventCommand
import com.cleevio.cinemax.api.module.supplier.event.SupplierCreatedOrUpdatedEvent
import io.mockk.every
import io.mockk.just
import io.mockk.mockk
import io.mockk.runs
import io.mockk.verify
import org.junit.jupiter.api.Test
import java.util.UUID

class SupplierOutboxEventEventListenerTest {
    private val outboxEventService = mockk<OutboxEventService>()
    private val underTest = SupplierOutboxEventEventListener(outboxEventService)

    @Test
    fun `listen to supplier created or updated event - should correctly handle SupplierCreatedOrUpdatedEvent`() {
        every { outboxEventService.createOutboxEvent(any()) } just runs

        val supplierId = UUID.fromString("65aa8e3f-4b86-4eb3-b2e9-5b1482021eda")
        underTest.listenToSupplierCreatedOrUpdatedEvent(
            SupplierCreatedOrUpdatedEvent(supplierId)
        )

        verify {
            outboxEventService.createOutboxEvent(
                CreateOutboxEventCommand(
                    entityId = supplierId,
                    type = OutboxEventType.SUPPLIER_CREATED_OR_UPDATED
                )
            )
        }
    }
}
