package com.cleevio.cinemax.api.module.supplier.service

import com.cleevio.cinemax.api.DatabaseCleanup
import com.cleevio.cinemax.api.IntegrationTest
import com.cleevio.cinemax.api.common.util.truncatedToSeconds
import com.cleevio.cinemax.api.module.supplier.event.AdminSupplierCreatedOrUpdatedEvent
import com.cleevio.cinemax.api.module.supplier.event.SupplierCreatedOrUpdatedEvent
import com.cleevio.cinemax.api.module.supplier.event.toMessagingEvent
import com.cleevio.cinemax.api.module.supplier.exception.SupplierCodeAlreadyExistsException
import com.cleevio.cinemax.api.module.supplier.exception.SupplierNotFoundException
import com.cleevio.cinemax.api.module.supplier.service.command.DeleteSupplierCommand
import com.cleevio.cinemax.api.module.supplier.service.command.MessagingDeleteSupplierCommand
import com.cleevio.cinemax.api.module.supplier.service.command.UpdateSupplierOriginalIdCommand
import com.cleevio.cinemax.api.util.assertCommandToSupplierMapping
import com.cleevio.cinemax.api.util.assertMessagingSupplierCommandToSupplier
import com.cleevio.cinemax.api.util.createSupplier
import com.cleevio.cinemax.api.util.mapToCreateOrUpdateSupplierCommand
import com.cleevio.cinemax.api.util.mapToMessagingCreateOrUpdateSupplierCommand
import io.mockk.confirmVerified
import io.mockk.every
import io.mockk.just
import io.mockk.runs
import io.mockk.verify
import jakarta.validation.ConstraintViolationException
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertDoesNotThrow
import org.junit.jupiter.api.assertThrows
import org.springframework.beans.factory.annotation.Autowired
import java.util.Optional
import kotlin.test.assertEquals
import kotlin.test.assertNotNull
import kotlin.test.assertNull
import kotlin.test.assertTrue

class SupplierServiceIT @Autowired constructor(
    private val underTest: SupplierService,
    private val supplierRepository: SupplierRepository,
) : IntegrationTest(DatabaseCleanup.AFTER_EACH) {

    @BeforeEach
    fun beforeEach() {
        every { applicationEventPublisherMock.publishEvent(any<SupplierCreatedOrUpdatedEvent>()) } just runs
        every { applicationEventPublisherMock.publishEvent(any<AdminSupplierCreatedOrUpdatedEvent>()) } just runs

        confirmVerified(applicationEventPublisherMock)
    }

    @Test
    fun `test syncCreateOrUpdateSupplier - create supplier - should throw if supplier code already exists`() {
        val supplier1 = createSupplier(originalId = 1, code = "SUP1").also { supplierRepository.save(it) }
        val supplier2 = createSupplier(originalId = 2, code = "SUP1")
        val command = mapToCreateOrUpdateSupplierCommand(supplier2).copy(id = null)

        assertThrows<SupplierCodeAlreadyExistsException> { underTest.syncCreateOrUpdateSupplier(command) }
    }

    @Test
    fun `test syncCreateOrUpdateSupplier - update supplier code - should not update`() {
        val supplier1 = createSupplier(originalId = 1, code = "SUP1")
        val supplier2 = createSupplier(originalId = 2, code = "SUP2")
        supplierRepository.saveAll(listOf(supplier1, supplier2))

        val command = mapToCreateOrUpdateSupplierCommand(supplier2).copy(code = "SUP3")

        underTest.syncCreateOrUpdateSupplier(command)

        val notUpdatedSupplier = supplierRepository.findByOriginalId(supplier2.originalId!!)!!
        assertCommandToSupplierMapping(mapToCreateOrUpdateSupplierCommand(supplier2), notUpdatedSupplier)
    }

    @Test
    fun `test syncCreateOrUpdateSupplier - does not exist by originalId - should create supplier`() {
        val supplier = createSupplier(originalId = 2)
        val command = mapToCreateOrUpdateSupplierCommand(supplier)

        assertEquals(0, supplierRepository.findAll().size)

        underTest.syncCreateOrUpdateSupplier(command)

        assertEquals(1, supplierRepository.findAll().size)

        supplierRepository.findAll().first { it.originalId == supplier.originalId }.let {
            assertNotNull(it.id)
            assertCommandToSupplierMapping(expected = command, actual = it)
            assertNotNull(it.createdAt)
            assertNull(it.deletedAt)
        }
    }

    @Test
    fun `test syncCreateOrUpdateSupplier - exists by originalId - should update supplier`() {
        val supplier = createSupplier().also { supplierRepository.save(it) }
        assertEquals(1, supplierRepository.findAll().size)

        val updateCommand = mapToCreateOrUpdateSupplierCommand(supplier).copy(
            title = "Updated Supplier Co.",
            addressStreet = Optional.of("Updated Street"),
            addressCity = Optional.of("Updated City"),
            addressPostCode = Optional.of("10101"),
            contactName = Optional.of("New Contact"),
            contactPhone = Optional.of("*********"),
            contactEmails = Optional.of(setOf("<EMAIL>")),
            bankName = Optional.of("New Bank"),
            bankAccount = Optional.of("999999/1111"),
            idNumber = Optional.of("********"),
            taxIdNumber = Optional.of("CZ2029999999")
        )

        underTest.syncCreateOrUpdateSupplier(updateCommand)
        assertEquals(1, supplierRepository.findAll().size)

        supplierRepository.findAll()[0].let {
            assertEquals(updateCommand.id, it.id)
            assertCommandToSupplierMapping(expected = updateCommand, actual = it, expectedCode = supplier.code)
            assertTrue(it.updatedAt.isAfter(supplier.updatedAt))
            assertNull(it.deletedAt)
        }
    }

    @Test
    fun `test syncCreateOrUpdateSupplier - exists by originalId and code is being updated - should not update`() {
        val supplier = createSupplier().also { supplierRepository.save(it) }
        assertEquals(1, supplierRepository.findAll().size)

        val createCommand = mapToCreateOrUpdateSupplierCommand(supplier)
        val updateCommand = createCommand.copy(
            code = "S06",
            title = "Updated Supplier Co."
        )

        underTest.syncCreateOrUpdateSupplier(updateCommand)
        assertEquals(1, supplierRepository.findAll().size)

        supplierRepository.findAll()[0].let {
            assertEquals(updateCommand.id, it.id)
            assertCommandToSupplierMapping(expected = createCommand, actual = it, expectedCode = supplier.code)
            assertNull(it.deletedAt)
        }
    }

    @Test
    fun `test syncCreateOrUpdateSupplier - exists deleted by originalId - should not update supplier`() {
        val supplier = supplierRepository.save(createSupplier { it.markDeleted() })
        assertEquals(1, supplierRepository.findAll().size)

        val deletedSupplier = supplierRepository.findByOriginalId(supplier.originalId!!)
        assertNotNull(deletedSupplier)
        assertTrue(deletedSupplier.isDeleted())

        val updateCommand = mapToCreateOrUpdateSupplierCommand(supplier).copy(
            title = "Updated Supplier Co."
        )

        underTest.syncCreateOrUpdateSupplier(updateCommand)

        val suppliers = supplierRepository.findAll()
        assertEquals(1, suppliers.size)

        suppliers[0].let {
            assertEquals(supplier.title, it.title)
            assertEquals(supplier.updatedAt.truncatedToSeconds(), it.updatedAt.truncatedToSeconds())
            assertTrue(it.isDeleted())
        }
    }

    @Test
    fun `test adminCreateOrUpdateSupplier - does not exist by supplier id - should create supplier`() {
        val supplier = createSupplier(originalId = null)
        val command = mapToCreateOrUpdateSupplierCommand(supplier).copy(id = null, code = null)

        assertEquals(0, supplierRepository.findAll().size)

        underTest.adminCreateOrUpdateSupplier(command)

        assertEquals(1, supplierRepository.findAll().size)

        supplierRepository.findAll()[0].let {
            assertNotNull(it.id)
            assertCommandToSupplierMapping(expected = command, actual = it, expectedCode = null)
            assertNotNull(it.createdAt)
            assertNull(it.deletedAt)

            verify(exactly = 1) {
                applicationEventPublisherMock.publishEvent(SupplierCreatedOrUpdatedEvent(it.id))
            }
            verify(exactly = 1) {
                applicationEventPublisherMock.publishEvent(it.toMessagingEvent())
            }
        }
    }

    @Test
    fun `test adminCreateOrUpdateSupplier - exists by supplier id - should update supplier`() {
        val supplier = createSupplier(originalId = null).also { supplierRepository.save(it) }
        assertEquals(1, supplierRepository.findAll().size)

        val updateCommand = mapToCreateOrUpdateSupplierCommand(supplier).copy(
            id = supplier.id,
            originalId = null,
            code = null,
            title = "Updated Supplier Co.",
            addressStreet = Optional.of("Updated Street"),
            addressCity = Optional.of("Updated City"),
            addressPostCode = Optional.of("10101"),
            contactName = Optional.of("New Contact"),
            contactPhone = Optional.of("*********"),
            contactEmails = Optional.of(setOf("<EMAIL>")),
            bankName = Optional.of("New Bank"),
            bankAccount = Optional.of("999999/1111"),
            idNumber = Optional.of("********"),
            taxIdNumber = Optional.of("CZ2029999999")
        )

        underTest.adminCreateOrUpdateSupplier(updateCommand)
        assertEquals(1, supplierRepository.findAll().size)

        supplierRepository.findAll()[0].let {
            assertEquals(updateCommand.id, it.id)
            assertCommandToSupplierMapping(expected = updateCommand, actual = it, expectedCode = supplier.code)
            assertTrue(it.updatedAt.isAfter(supplier.updatedAt))
            assertNull(it.deletedAt)

            verify(exactly = 1) {
                applicationEventPublisherMock.publishEvent(SupplierCreatedOrUpdatedEvent(it.id))
            }
            verify(exactly = 1) {
                applicationEventPublisherMock.publishEvent(it.toMessagingEvent())
            }
        }
    }

    @Test
    fun `test adminCreateOrUpdateSupplier - update with no attribute changed  - should not throw`() {
        val supplier = createSupplier(originalId = 1).also { supplierRepository.save(it) }
        assertEquals(1, supplierRepository.findAll().size)

        val updateCommand = mapToCreateOrUpdateSupplierCommand(supplier).copy(
            id = supplier.id,
            originalId = supplier.originalId,
            code = null
        )

        supplierRepository.findAll()[0].also {
            assertEquals(updateCommand.id, it.id)
            assertCommandToSupplierMapping(expected = updateCommand, actual = it, expectedCode = supplier.code)
            assertNull(it.deletedAt)
        }

        assertDoesNotThrow { underTest.adminCreateOrUpdateSupplier(updateCommand) }
        verify(exactly = 1) {
            applicationEventPublisherMock.publishEvent(SupplierCreatedOrUpdatedEvent(supplier.id))
        }
    }

    @Test
    fun `test adminCreateOrUpdateSupplier - should throw if supplier does not exist with given id`() {
        val supplier = createSupplier(originalId = 1, code = "SUP1")
        val command = mapToCreateOrUpdateSupplierCommand(supplier)

        assertThrows<SupplierNotFoundException> { underTest.adminCreateOrUpdateSupplier(command) }
    }

    @Test
    fun `test syncCreateOrUpdateSupplier - command with blank string - should throw exception`() {
        val supplier = createSupplier()
        val command = mapToCreateOrUpdateSupplierCommand(supplier)
        listOf(
            command.copy(addressStreet = Optional.ofNullable(" ")),
            command.copy(addressCity = Optional.ofNullable(" ")),
            command.copy(addressPostCode = Optional.ofNullable(" ")),
            command.copy(contactName = Optional.ofNullable(" ")),
            command.copy(contactPhone = Optional.ofNullable(" ")),
            command.copy(bankName = Optional.ofNullable(" ")),
            command.copy(bankAccount = Optional.ofNullable(" ")),
            command.copy(idNumber = Optional.ofNullable(" ")),
            command.copy(taxIdNumber = Optional.ofNullable(" "))
        )
            .forEach { assertThrows<ConstraintViolationException> { underTest.syncCreateOrUpdateSupplier(it) } }
    }

    @Test
    fun `test messagingCreateOrUpdateSupplier - should create new supplier if not found`() {
        val supplier = createSupplier()
        val command = mapToMessagingCreateOrUpdateSupplierCommand(supplier)

        assertEquals(0, supplierRepository.count())

        underTest.messagingCreateOrUpdateSupplier(command)
        assertEquals(1, supplierRepository.count())

        val createdSupplier = supplierRepository.findAll()[0].also {
            assertNotNull(it.id)
            assertMessagingSupplierCommandToSupplier(command, it)
            assertNotNull(it.createdAt)
            assertEquals(it.createdAt.truncatedToSeconds(), it.updatedAt.truncatedToSeconds())
            assertNull(it.deletedAt)
        }

        verify(exactly = 1) {
            applicationEventPublisherMock.publishEvent(SupplierCreatedOrUpdatedEvent(createdSupplier.id))
        }
    }

    @Test
    fun `test messagingCreateOrUpdateSupplier - should update existing supplier if found`() {
        val existingSupplier = createSupplier(code = "1234").also { supplierRepository.save(it) }
        assertEquals(1, supplierRepository.count())

        val command = mapToMessagingCreateOrUpdateSupplierCommand(existingSupplier).copy(
            title = "Updated Supplier Co.",
            addressStreet = Optional.of("Updated Street"),
            addressCity = Optional.of("Updated City"),
            addressPostCode = Optional.of("10101"),
            contactName = Optional.of("New Contact"),
            contactPhone = Optional.of("*********"),
            contactEmails = Optional.of(setOf("<EMAIL>")),
            bankName = Optional.of("New Bank"),
            bankAccount = Optional.of("999999/1111"),
            idNumber = Optional.of("********"),
            taxIdNumber = Optional.of("CZ2029999999")
        )

        underTest.messagingCreateOrUpdateSupplier(command)
        assertEquals(1, supplierRepository.count())

        val updatedSupplier = supplierRepository.findAll()[0].also {
            assertEquals(existingSupplier.id, it.id)
            assertMessagingSupplierCommandToSupplier(command, it)
            assertTrue(it.updatedAt.isAfter(it.createdAt))
        }

        verify(exactly = 1) {
            applicationEventPublisherMock.publishEvent(SupplierCreatedOrUpdatedEvent(updatedSupplier.id))
        }
    }

    @Test
    fun `test messagingDeleteSupplier - should soft delete supplier`() {
        val supplier = createSupplier(code = "1234").also { supplierRepository.save(it) }
        assertNull(supplier.deletedAt)
        assertEquals(1, supplierRepository.count())

        underTest.messagingDeleteSupplier(MessagingDeleteSupplierCommand("1234"))

        val deletedSupplier = supplierRepository.findAll()[0]
        assertNotNull(deletedSupplier.deletedAt)
    }

    @Test
    fun `test messagingDeleteSupplier - supplier not found by code - should throw`() {
        assertThrows<SupplierNotFoundException> {
            underTest.messagingDeleteSupplier(MessagingDeleteSupplierCommand("1234"))
        }
    }

    @Test
    fun `test updateSupplierOriginalId - should correctly update in db`() {
        val supplier1 = createSupplier(originalId = null).also { supplierRepository.save(it) }
        assertNull(supplierRepository.findAll()[0].originalId)

        underTest.updateSupplierOriginalId(
            UpdateSupplierOriginalIdCommand(
                supplierId = supplier1.id,
                originalId = 5
            )
        )

        assertEquals(5, supplierRepository.findAll()[0].originalId)
    }

    @Test
    fun `test deleteSupplier - should successfully soft delete supplier`() {
        val supplier = createSupplier().also { supplierRepository.save(it) }

        underTest.deleteSupplier(DeleteSupplierCommand(supplier.id))

        supplierRepository.findAll()[0].let {
            assertNotNull(it)
            assertNotNull(it.isDeleted())
        }
    }
}
