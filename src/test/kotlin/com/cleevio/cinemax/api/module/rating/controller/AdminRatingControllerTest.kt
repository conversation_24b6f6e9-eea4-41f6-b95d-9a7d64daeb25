package com.cleevio.cinemax.api.module.rating.controller

import com.cleevio.cinemax.api.ControllerTest
import com.cleevio.cinemax.api.common.constant.ApiVersion
import com.cleevio.cinemax.api.common.descriptor.mssql.DescriptorMssqlResponse
import com.cleevio.cinemax.api.common.util.jsonContent
import com.cleevio.cinemax.api.module.employee.constant.EmployeeRole
import com.cleevio.cinemax.api.module.rating.entity.Rating
import com.cleevio.cinemax.api.util.mockAccessToken
import io.mockk.every
import io.mockk.verify
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest
import org.springframework.http.HttpHeaders
import org.springframework.test.web.servlet.MockMvc
import org.springframework.test.web.servlet.get

@WebMvcTest(AdminRatingController::class)
class AdminRatingControllerTest @Autowired constructor(
    private val mvc: MockMvc,
) : ControllerTest() {

    @Test
    fun `test getRatings, should serialize and deserialize correctly`() {
        every { ratingJooqFinderService.findAll() } returns listOf(RATING_1, RATING_2, RATING_3)
        every { descriptorMssqlResponseMapper.mapList(any()) } returns listOf(
            RATING_1_RESPONSE,
            RATING_2_RESPONSE,
            RATING_3_RESPONSE
        )

        mvc.get(GET_RATINGS_PATH) {
            header(HttpHeaders.ACCEPT, ApiVersion.VERSION_1_JSON)
            header(HttpHeaders.AUTHORIZATION, mockAccessToken(role = EmployeeRole.MANAGER))
        }.andExpect {
            status { isOk() }
            content { contentType(ApiVersion.VERSION_1_JSON) }
            jsonContent(
                """
                [{
                  "id": "${RATING_1.id}",
                  "title": "${RATING_1.title}"
                },
                {
                  "id": "${RATING_2.id}",
                  "title": "${RATING_2.title}"
                },
                {
                  "id": "${RATING_3.id}",
                  "title": "${RATING_3.title}"
                }]
            """
            )
        }

        verify {
            descriptorMssqlResponseMapper.mapList(listOf(RATING_1, RATING_2, RATING_3))
        }
    }
}

private const val GET_RATINGS_PATH = "/manager-app/ratings"
private val RATING_1 = Rating(
    originalId = 5,
    code = "U",
    title = "U"
)
private val RATING_2 = Rating(
    originalId = 6,
    code = "12",
    title = "12"
)
private val RATING_3 = Rating(
    originalId = 7,
    code = "15",
    title = "15"
)
private val RATING_1_RESPONSE = DescriptorMssqlResponse(
    id = RATING_1.id,
    title = RATING_1.title
)
private val RATING_2_RESPONSE = DescriptorMssqlResponse(
    id = RATING_2.id,
    title = RATING_2.title
)
private val RATING_3_RESPONSE = DescriptorMssqlResponse(
    id = RATING_3.id,
    title = RATING_3.title
)
