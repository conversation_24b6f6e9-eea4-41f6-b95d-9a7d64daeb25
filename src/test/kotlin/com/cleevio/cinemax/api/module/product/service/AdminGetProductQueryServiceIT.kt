package com.cleevio.cinemax.api.module.product.service

import com.cleevio.cinemax.api.DatabaseCleanup
import com.cleevio.cinemax.api.IntegrationTest
import com.cleevio.cinemax.api.common.constant.NO_TAX_RATE
import com.cleevio.cinemax.api.common.constant.STANDARD_TAX_RATE
import com.cleevio.cinemax.api.common.util.isEqualTo
import com.cleevio.cinemax.api.common.util.subtractTax
import com.cleevio.cinemax.api.module.file.service.FileRepository
import com.cleevio.cinemax.api.module.product.constant.ProductType
import com.cleevio.cinemax.api.module.product.event.ProductCreatedOrUpdatedEvent
import com.cleevio.cinemax.api.module.product.exception.ProductNotFoundException
import com.cleevio.cinemax.api.module.product.service.query.AdminGetProductQuery
import com.cleevio.cinemax.api.module.productcategory.constant.ProductCategoryType
import com.cleevio.cinemax.api.module.productcategory.service.ProductCategoryService
import com.cleevio.cinemax.api.module.productcomponent.constant.ProductComponentUnit
import com.cleevio.cinemax.api.module.productcomponent.service.ProductComponentService
import com.cleevio.cinemax.api.module.productcomponentcategory.service.ProductComponentCategoryRepository
import com.cleevio.cinemax.api.module.productcomposition.entity.ProductComposition
import com.cleevio.cinemax.api.util.createFile
import com.cleevio.cinemax.api.util.createProduct
import com.cleevio.cinemax.api.util.createProductCategory
import com.cleevio.cinemax.api.util.createProductComponent
import com.cleevio.cinemax.api.util.createProductComponentCategory
import com.cleevio.cinemax.api.util.createProductComposition
import com.cleevio.cinemax.api.util.mapToCreateOrUpdateProductCategoryCommand
import com.cleevio.cinemax.api.util.mapToCreateOrUpdateProductComponentCommand
import com.cleevio.cinemax.api.util.mapToCreateProductCommand
import com.cleevio.cinemax.api.util.toUUID
import io.mockk.Runs
import io.mockk.every
import io.mockk.just
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import org.springframework.beans.factory.annotation.Autowired
import java.math.BigDecimal
import java.util.UUID
import kotlin.test.assertEquals
import kotlin.test.assertNotNull
import kotlin.test.assertNull
import kotlin.test.assertTrue

class AdminGetProductQueryServiceIT @Autowired constructor(
    private val underTest: AdminGetProductQueryService,
    private val productService: ProductService,
    private val productComponentService: ProductComponentService,
    private val productCategoryService: ProductCategoryService,
    private val productComponentCategoryRepository: ProductComponentCategoryRepository,
    private val fileRepository: FileRepository,
) : IntegrationTest(DatabaseCleanup.AFTER_EACH) {

    @BeforeEach
    fun setUp() {
        every { applicationEventPublisherMock.publishEvent(any<ProductCreatedOrUpdatedEvent>()) } just Runs
    }

    @Test
    fun `test AdminGetProductQuery - productId does not exist - should throw`() {
        assertThrows<ProductNotFoundException> {
            underTest(AdminGetProductQuery(1.toUUID()))
        }
    }

    @Test
    fun `test AdminGetProductQuery - PRODUCT type product - should return correct response`() {
        val createdProducts = initData()
        val product3Id = createdProducts[PRODUCT_3.id]!! // Popcorn XXL

        underTest(AdminGetProductQuery(product3Id)).let { response ->
            assertEquals(product3Id, response.id)
            assertEquals(PRODUCT_3.title, response.title)
            assertTrue(response.code.matches(Regex("[0-9]{5}")))
            assertEquals(PRODUCT_3.active, response.active)
            assertEquals(PRODUCT_3.type, response.type)
            assertEquals(PRODUCT_3.price, response.price)
            assertEquals(PRODUCT_3.flagshipPrice, response.flagshipPrice)
            assertTrue(PRODUCT_3.priceNoVat.subtractTax(PRODUCT_3.taxRate!!) isEqualTo response.priceNoVat)
            assertEquals(PRODUCT_3.soldInBuffet, response.soldInBuffet)
            assertEquals(PRODUCT_3.soldInCafe, response.soldInCafe)
            assertEquals(PRODUCT_3.soldInVip, response.soldInVip)
            assertEquals(PRODUCT_3.order, response.order)
            assertEquals(PRODUCT_3.tabletOrder, response.tabletOrder)
            assertEquals(PRODUCT_3.isPackagingDeposit, response.isPackagingDeposit)
            assertEquals(PRODUCT_3.stockQuantityThreshold, response.stockQuantityThreshold)
            assertEquals(PRODUCT_3.discountAmount, response.discountAmount)
            assertEquals(PRODUCT_3.discountPercentage, response.discountPercentage)
            assertEquals(PRODUCT_3.taxRate, response.taxRate)
            assertNotNull(response.createdAt)
            assertNotNull(response.updatedAt)
            assertEquals(PRODUCT_CATEGORY_1.id, response.productCategory.id)
            assertEquals(PRODUCT_CATEGORY_1.type, response.productCategory.type)
            assertEquals(PRODUCT_CATEGORY_1.title, response.productCategory.title)
            assertNotNull(response.imageFile)
            assertEquals(FILE_1.id, response.imageFile!!.id)
            assertEquals(
                "https://example.com/manager-app/files/product_image/${FILE_1.getFilename()}",
                response.imageFile!!.url
            )

            assertTrue(25.toBigDecimal() isEqualTo response.stockQuantity)
            assertEquals(3, response.productComposition.size)
            assertTrue(response.productComposition.all { it.productInProduct == null })

            val productComponents = response.productComposition.map { it.productComponent }

            productComponents.first { it?.title == PRODUCT_COMPONENT_3.title }.let {
                assertEquals(PRODUCT_COMPONENT_3.id, it!!.id)
                assertEquals(PRODUCT_COMPONENT_3.title, it.title)
                assertTrue(1.toBigDecimal() isEqualTo it.quantity)
                assertEquals(PRODUCT_COMPONENT_3.unit, it.unit)
            }

            productComponents.first { it?.title == PRODUCT_COMPONENT_4.title }.let {
                assertEquals(PRODUCT_COMPONENT_4.id, it!!.id)
                assertEquals(PRODUCT_COMPONENT_4.title, it.title)
                assertTrue((3.toBigDecimal() isEqualTo it.quantity))
                assertEquals(PRODUCT_COMPONENT_4.unit, it.unit)
            }

            productComponents.first { it?.title == PRODUCT_COMPONENT_5.title }.let {
                assertEquals(PRODUCT_COMPONENT_5.id, it!!.id)
                assertEquals(PRODUCT_COMPONENT_5.title, it.title)
                assertTrue(2.toBigDecimal() isEqualTo it.quantity)
                assertEquals(PRODUCT_COMPONENT_5.unit, it.unit)
            }
        }
    }

    @Test
    fun `test AdminGetProductQuery - PRODUCT type product with packaging deposit - deposit product should be included in response`() {
        val createdProducts = initData()
        val product1Id = createdProducts[PRODUCT_1.id]!! // Coca-cola
        val product7Id = createdProducts[PRODUCT_7.id]!! // Zaloha na obal

        underTest(AdminGetProductQuery(product1Id)).let { response ->

            assertTrue(90.toBigDecimal() isEqualTo response.stockQuantity)
            assertEquals(2, response.productComposition.size)

            val productComponents = response.productComposition.filter { it.productComponent != null }
            val productInProducts = response.productComposition.filter { it.productInProduct != null }

            assertEquals(1, productComponents.size)
            productComponents[0].productComponent.let {
                assertEquals(PRODUCT_COMPONENT_1.id, it!!.id)
                assertEquals(PRODUCT_COMPONENT_1.title, it.title)
                assertTrue(1.toBigDecimal() isEqualTo it.quantity)
                assertEquals(PRODUCT_COMPONENT_1.unit, it.unit)
            }

            assertEquals(1, productInProducts.size)
            productInProducts[0].productInProduct.let {
                assertEquals(product7Id, it!!.id)
                assertEquals(PRODUCT_7.title, it.title)
                assertTrue(1.toBigDecimal() isEqualTo it.quantity)
                assertEquals(ProductComponentUnit.KS, it.unit)
                assertNull(it.productInProductPrice)
            }
        }
    }

    @Test
    fun `test AdminGetProductQuery - PRODUCT_IN_PRODUCT type product - should return correct response`() {
        val createdProducts = initData()
        val createdProductInProductId = createdProducts[PRODUCT_4.id]!! // Combo Popcorn XXL + 3x Coca Cola 0,33l
        val product1Id = createdProducts[PRODUCT_1.id]!!
        val product3Id = createdProducts[PRODUCT_3.id]!!

        underTest(AdminGetProductQuery(createdProductInProductId)).let { response ->
            assertEquals(createdProductInProductId, response.id)
            assertEquals(PRODUCT_4.title, response.title)
            assertTrue(response.code.matches(Regex("[0-9]{5}")))
            assertEquals(PRODUCT_4.active, response.active)
            assertEquals(PRODUCT_4.type, response.type)
            assertEquals(PRODUCT_4.price, response.price)
            assertEquals(PRODUCT_4.flagshipPrice, response.flagshipPrice)
            // TODO product in product VAT rate
            // assertEquals(PRODUCT_4.price.subtractTax(PRODUCT_4.taxRate!!), response.priceNoVat)
            assertEquals(PRODUCT_4.soldInBuffet, response.soldInBuffet)
            assertEquals(PRODUCT_4.soldInCafe, response.soldInCafe)
            assertEquals(PRODUCT_4.soldInVip, response.soldInVip)
            assertEquals(PRODUCT_4.order, response.order)
            assertEquals(PRODUCT_4.tabletOrder, response.tabletOrder)
            assertEquals(PRODUCT_4.isPackagingDeposit, response.isPackagingDeposit)
            assertEquals(PRODUCT_4.stockQuantityThreshold, response.stockQuantityThreshold)
            assertEquals(PRODUCT_4.discountAmount, response.discountAmount)
            assertEquals(PRODUCT_4.discountPercentage, response.discountPercentage)
            assertEquals(PRODUCT_4.taxRate, response.taxRate)
            assertNotNull(response.createdAt)
            assertNotNull(response.updatedAt)
            assertEquals(PRODUCT_CATEGORY_1.id, response.productCategory.id)
            assertEquals(PRODUCT_CATEGORY_1.type, response.productCategory.type)
            assertEquals(PRODUCT_CATEGORY_1.title, response.productCategory.title)
            assertNull(response.imageFile)
            assertTrue(12.5.toBigDecimal() isEqualTo response.stockQuantity)
            assertEquals(2, response.productComposition.size)
            assertTrue(response.productComposition.all { it.productComponent == null })

            val compositionWithProduct1 = response.productComposition.find { pc -> pc.productInProduct?.id == product1Id }!!
            val compositionWithProduct3 = response.productComposition.find { pc -> pc.productInProduct?.id == product3Id }!!

            // For composition with PRODUCT_1 (Coca Cola 0.33l)
            assertNull(compositionWithProduct1.productComponent)
            val productInProduct1 = compositionWithProduct1.productInProduct!!
            assertEquals(product1Id, productInProduct1.id)
            assertEquals(PRODUCT_1.title, productInProduct1.title)
            assertTrue(3.toBigDecimal() isEqualTo productInProduct1.quantity)
            assertEquals(ProductComponentUnit.KS, productInProduct1.unit)
            assertTrue(BigDecimal.ONE isEqualTo productInProduct1.productInProductPrice)

            // For composition with PRODUCT_3 (Popcorn XXL)
            assertNull(compositionWithProduct3.productComponent)
            val productInProduct3 = compositionWithProduct3.productInProduct!!
            assertEquals(product3Id, productInProduct3.id)
            assertEquals(PRODUCT_3.title, productInProduct3.title)
            assertTrue(2.toBigDecimal() isEqualTo productInProduct3.quantity)
            assertEquals(ProductComponentUnit.KS, productInProduct3.unit)
            assertTrue(3.toBigDecimal() isEqualTo productInProduct3.productInProductPrice)
        }
    }

    @Test
    fun `test AdminGetProductQuery - ADDITIONAL_SALE type product - should return correct response`() {
        val createdProducts = initData()
        val product6Id = createdProducts[PRODUCT_6.id]!! // Additional Sale

        underTest(AdminGetProductQuery(product6Id)).let { response ->
            assertEquals(product6Id, response.id)
            assertEquals(PRODUCT_6.title, response.title)
            assertTrue(response.code.matches(Regex("[0-9]{5}")))
            assertEquals(PRODUCT_6.active, response.active)
            assertEquals(PRODUCT_6.type, response.type)
            assertEquals(PRODUCT_6.price, response.price)
            assertEquals(PRODUCT_6.flagshipPrice, response.flagshipPrice)
            assertEquals(PRODUCT_6.priceNoVat, response.priceNoVat)
            assertEquals(PRODUCT_6.soldInBuffet, response.soldInBuffet)
            assertEquals(PRODUCT_6.soldInCafe, response.soldInCafe)
            assertEquals(PRODUCT_6.soldInVip, response.soldInVip)
            assertEquals(PRODUCT_6.order, response.order)
            assertEquals(PRODUCT_6.tabletOrder, response.tabletOrder)
            assertEquals(PRODUCT_6.isPackagingDeposit, response.isPackagingDeposit)
            assertEquals(PRODUCT_6.stockQuantityThreshold, response.stockQuantityThreshold)
            assertEquals(PRODUCT_6.discountAmount, response.discountAmount)
            assertEquals(PRODUCT_6.discountPercentage, response.discountPercentage)
            assertEquals(PRODUCT_6.taxRate, response.taxRate)
            assertNotNull(response.createdAt)
            assertNotNull(response.updatedAt)
            assertEquals(PRODUCT_CATEGORY_2.id, response.productCategory.id)
            assertEquals(PRODUCT_CATEGORY_2.type, response.productCategory.type)
            assertEquals(PRODUCT_CATEGORY_2.title, response.productCategory.title)
            assertNull(response.imageFile)

            assertEquals(0.toBigDecimal(), response.stockQuantity)
            assertTrue(response.productComposition.isEmpty())
        }
    }

    private fun initData(): Map<UUID, UUID> {
        fileRepository.save(FILE_1)

        setOf(PRODUCT_CATEGORY_1, PRODUCT_CATEGORY_2, PRODUCT_CATEGORY_3).forEach {
            productCategoryService.syncCreateOrUpdateProductCategory(
                mapToCreateOrUpdateProductCategoryCommand(it)
            )
        }

        productComponentCategoryRepository.save(PRODUCT_COMPONENT_CATEGORY_1)
        setOf(
            PRODUCT_COMPONENT_1,
            PRODUCT_COMPONENT_2,
            PRODUCT_COMPONENT_3,
            PRODUCT_COMPONENT_4,
            PRODUCT_COMPONENT_5
        ).forEach {
            productComponentService.syncCreateOrUpdateProductComponent(
                mapToCreateOrUpdateProductComponentCommand(it)
            )
        }

        val product7Id = productService.adminCreateProduct(
            mapToCreateProductCommand(
                product = PRODUCT_7,
                productCategoryId = PRODUCT_CATEGORY_3.id,
                productCompositions = emptyList()
            )
        )

        val product1Id = productService.adminCreateProduct(
            mapToCreateProductCommand(
                product = PRODUCT_1,
                productCategoryId = PRODUCT_CATEGORY_1.id,
                productCompositions = listOf(PRODUCT_COMPOSITION_1, PRODUCT_COMPOSITION_10(product7Id))
            )
        )

        val product2Id = productService.adminCreateProduct(
            mapToCreateProductCommand(
                product = PRODUCT_2,
                productCategoryId = PRODUCT_CATEGORY_1.id,
                productCompositions = listOf(PRODUCT_COMPOSITION_2)
            )
        )

        val product3Id = productService.adminCreateProduct(
            mapToCreateProductCommand(
                product = PRODUCT_3,
                productCategoryId = PRODUCT_CATEGORY_1.id,
                productCompositions = listOf(PRODUCT_COMPOSITION_3, PRODUCT_COMPOSITION_4, PRODUCT_COMPOSITION_5)
            )
        )

        val product4Id = productService.adminCreateProduct(
            mapToCreateProductCommand(
                product = PRODUCT_4,
                productCategoryId = PRODUCT_CATEGORY_1.id,
                productCompositions = listOf(
                    PRODUCT_COMPOSITION_6(product1Id),
                    PRODUCT_COMPOSITION_7(product3Id)
                )
            )
        )

        val product5Id = productService.adminCreateProduct(
            mapToCreateProductCommand(
                product = PRODUCT_5,
                productCategoryId = PRODUCT_CATEGORY_1.id,
                productCompositions = listOf(
                    PRODUCT_COMPOSITION_8(product2Id),
                    PRODUCT_COMPOSITION_9(product3Id)
                )
            )
        )

        val product6Id = productService.adminCreateProduct(
            mapToCreateProductCommand(
                product = PRODUCT_6,
                productCategoryId = PRODUCT_CATEGORY_2.id,
                productCompositions = emptyList()
            )
        )

        return mapOf(
            PRODUCT_1.id to product1Id,
            PRODUCT_2.id to product2Id,
            PRODUCT_3.id to product3Id,
            PRODUCT_4.id to product4Id,
            PRODUCT_5.id to product5Id,
            PRODUCT_6.id to product6Id,
            PRODUCT_7.id to product7Id
        )
    }
}

private val FILE_1 = createFile(id = 1.toUUID(), originalName = "${1.toUUID()}.jpg")
private val PRODUCT_COMPONENT_CATEGORY_1 = createProductComponentCategory()
private val PRODUCT_CATEGORY_1 = createProductCategory(
    originalId = 1,
    code = "1",
    title = "Kategorie produktu",
    type = ProductCategoryType.PRODUCT,
    taxRate = STANDARD_TAX_RATE
)

private val PRODUCT_CATEGORY_2 = createProductCategory(
    originalId = 2,
    code = "2",
    title = "Kategorie slev",
    type = ProductCategoryType.DISCOUNT,
    taxRate = STANDARD_TAX_RATE
)
private val PRODUCT_CATEGORY_3 = createProductCategory(
    originalId = 3,
    code = "3",
    title = "Zaloha",
    type = ProductCategoryType.PRODUCT,
    taxRate = NO_TAX_RATE
)

private val PRODUCT_1 = createProduct(
    originalId = 1,
    code = "1",
    productCategoryId = PRODUCT_CATEGORY_1.id,
    title = "Coca Cola 0.33l",
    type = ProductType.PRODUCT
)
private val PRODUCT_2 = createProduct(
    originalId = 2,
    code = "2",
    productCategoryId = PRODUCT_CATEGORY_1.id,
    title = "Sprite 0.33l",
    type = ProductType.PRODUCT
)
private val PRODUCT_3 = createProduct(
    originalId = 3,
    code = "3",
    productCategoryId = PRODUCT_CATEGORY_1.id,
    title = "Popcorn XXL",
    type = ProductType.PRODUCT,
    imageFileId = FILE_1.id,
    taxRate = STANDARD_TAX_RATE
)
private val PRODUCT_4 = createProduct(
    originalId = 4,
    code = "4",
    productCategoryId = PRODUCT_CATEGORY_1.id,
    title = "Combo Popcorn XXL + 3x Coca Cola 0,33l",
    type = ProductType.PRODUCT_IN_PRODUCT,
    taxRate = null
)
private val PRODUCT_5 = createProduct(
    originalId = 5,
    code = "5",
    productCategoryId = PRODUCT_CATEGORY_1.id,
    title = "Combo Popcorn XXL + Sprite 0,33l",
    type = ProductType.PRODUCT_IN_PRODUCT,
    taxRate = null
)
private val PRODUCT_6 = createProduct(
    originalId = 6,
    code = "6",
    productCategoryId = PRODUCT_CATEGORY_2.id,
    title = "Additional Sale",
    type = ProductType.ADDITIONAL_SALE,
    discountAmount = 5.toBigDecimal()
)
private val PRODUCT_7 = createProduct(
    originalId = 7,
    code = "7",
    productCategoryId = PRODUCT_CATEGORY_2.id,
    title = "Zaloha na obal",
    type = ProductType.ADDITIONAL_SALE,
    price = 0.15.toBigDecimal(),
    isPackagingDeposit = true
)
private val PRODUCT_COMPONENT_1 = createProductComponent(
    originalId = 1,
    code = "1",
    title = "Coca Cola 0,33l",
    unit = ProductComponentUnit.KS,
    stockQuantity = 90.toBigDecimal(),
    productComponentCategoryId = PRODUCT_COMPONENT_CATEGORY_1.id
)
private val PRODUCT_COMPONENT_2 = createProductComponent(
    originalId = 2,
    code = "2",
    title = "Sprite 0,33l",
    unit = ProductComponentUnit.KS,
    stockQuantity = 5.0.toBigDecimal(),
    productComponentCategoryId = PRODUCT_COMPONENT_CATEGORY_1.id
)
private val PRODUCT_COMPONENT_3 = createProductComponent(
    originalId = 3,
    code = "3",
    title = "Kukurica",
    unit = ProductComponentUnit.KG,
    stockQuantity = 1000.toBigDecimal(),
    productComponentCategoryId = PRODUCT_COMPONENT_CATEGORY_1.id
)
private val PRODUCT_COMPONENT_4 = createProductComponent(
    originalId = 4,
    code = "4",
    title = "Soľ",
    unit = ProductComponentUnit.KG,
    stockQuantity = 100.toBigDecimal(),
    productComponentCategoryId = PRODUCT_COMPONENT_CATEGORY_1.id
)
private val PRODUCT_COMPONENT_5 = createProductComponent(
    originalId = 5,
    code = "5",
    title = "Tuk",
    unit = ProductComponentUnit.KG,
    stockQuantity = 50.toBigDecimal(),
    productComponentCategoryId = PRODUCT_COMPONENT_CATEGORY_1.id
)

// Coca Cola 0.33l  - Coca Cola 0.33l (1x)
private val PRODUCT_COMPOSITION_1 = createProductComposition(
    originalId = 1,
    productId = PRODUCT_1.id,
    productComponentId = PRODUCT_COMPONENT_1.id,
    amount = 1.toBigDecimal()
)

// Coca Cola 0.33l  -> Zaloha na obal (1x)
private val PRODUCT_COMPOSITION_10: (UUID) -> ProductComposition = { productInProductId: UUID ->
    createProductComposition(
        originalId = 10,
        productId = PRODUCT_1.id,
        productInProductId = productInProductId,
        productComponentId = null,
        amount = 1.toBigDecimal()
    )
}

// Sprite 0.33l - Sprite 0.33l (1x)
private val PRODUCT_COMPOSITION_2 = createProductComposition(
    originalId = 2,
    productId = PRODUCT_2.id,
    productComponentId = PRODUCT_COMPONENT_2.id,
    amount = 1.toBigDecimal()
)

// Popcorn XXL - Kukurica (1x)
private val PRODUCT_COMPOSITION_3 = createProductComposition(
    originalId = 3,
    productId = PRODUCT_3.id,
    productComponentId = PRODUCT_COMPONENT_3.id,
    amount = 1.toBigDecimal(),
    productInProductId = null
)

// Popcorn XXL - Soľ (3x)
private val PRODUCT_COMPOSITION_4 = createProductComposition(
    originalId = 4,
    productId = PRODUCT_3.id,
    productComponentId = PRODUCT_COMPONENT_4.id,
    amount = 3.toBigDecimal()
)

// Popcorn XXL - Tuk (2x)
private val PRODUCT_COMPOSITION_5 = createProductComposition(
    originalId = 5,
    productId = PRODUCT_3.id,
    productComponentId = PRODUCT_COMPONENT_5.id,
    amount = 2.toBigDecimal()
)

// Combo Popcorn XXL + MEGA Coca Cola 0,33l -> Coca Cola 0,33l (3x)
private val PRODUCT_COMPOSITION_6: (UUID) -> ProductComposition = { productInProductId: UUID ->
    createProductComposition(
        originalId = 6,
        productId = PRODUCT_4.id,
        productInProductId = productInProductId,
        productComponentId = null,
        amount = 3.toBigDecimal(),
        productInProductPrice = BigDecimal.ONE
    )
}

// Combo Popcorn XXL + MEGA Cola 0,33l -> Popcorn XXL (2x)
private val PRODUCT_COMPOSITION_7: (UUID) -> ProductComposition = { productInProductId: UUID ->
    createProductComposition(
        originalId = 7,
        productId = PRODUCT_4.id,
        productInProductId = productInProductId,
        productComponentId = null,
        amount = 2.toBigDecimal(),
        productInProductPrice = 3.toBigDecimal()
    )
}

// Combo Popcorn XXL + Sprite 0,33l -> Sprite 0,33l (1x)
private val PRODUCT_COMPOSITION_8: (UUID) -> ProductComposition = { productInProductId: UUID ->
    createProductComposition(
        originalId = 8,
        productId = PRODUCT_5.id,
        productInProductId = productInProductId,
        productComponentId = null,
        amount = 1.toBigDecimal(),
        productInProductPrice = 1.8.toBigDecimal()
    )
}

// Combo Popcorn XXL + Coca Cola 0,33l -> Popcorn XXL (1x)
private val PRODUCT_COMPOSITION_9: (UUID) -> ProductComposition = { productInProductId: UUID ->
    createProductComposition(
        originalId = 9,
        productId = PRODUCT_5.id,
        productInProductId = productInProductId,
        productComponentId = null,
        amount = 1.toBigDecimal(),
        productInProductPrice = 2.5.toBigDecimal()
    )
}
