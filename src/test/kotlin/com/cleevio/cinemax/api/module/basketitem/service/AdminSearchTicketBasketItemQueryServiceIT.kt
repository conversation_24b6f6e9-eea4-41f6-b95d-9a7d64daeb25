package com.cleevio.cinemax.api.module.basketitem.service

import com.cleevio.cinemax.api.DatabaseCleanup
import com.cleevio.cinemax.api.INTEGRATION_TEST_DATE_TIME
import com.cleevio.cinemax.api.IntegrationTest
import com.cleevio.cinemax.api.common.constant.PaymentType
import com.cleevio.cinemax.api.common.util.associateNotNull
import com.cleevio.cinemax.api.common.util.truncatedToSeconds
import com.cleevio.cinemax.api.module.auditorium.service.AuditoriumService
import com.cleevio.cinemax.api.module.auditoriumlayout.service.AuditoriumLayoutService
import com.cleevio.cinemax.api.module.basket.constant.BasketState
import com.cleevio.cinemax.api.module.basket.entity.Basket
import com.cleevio.cinemax.api.module.basket.service.BasketReceiptNumbersService
import com.cleevio.cinemax.api.module.basket.service.BasketRepository
import com.cleevio.cinemax.api.module.basket.service.BasketService
import com.cleevio.cinemax.api.module.basket.service.command.CompleteBasketPaymentCommand
import com.cleevio.cinemax.api.module.basket.service.command.CreateMssqlTicketBasketItemCommand
import com.cleevio.cinemax.api.module.basket.service.command.GenerateBasketReceiptNumbersCommand
import com.cleevio.cinemax.api.module.basket.service.command.InitiateBasketPaymentCommand
import com.cleevio.cinemax.api.module.basketitem.constant.BasketItemType
import com.cleevio.cinemax.api.module.basketitem.controller.dto.AdminSearchTicketBasketItemsFilter
import com.cleevio.cinemax.api.module.basketitem.controller.dto.AdminSearchTicketBasketItemsResponse
import com.cleevio.cinemax.api.module.basketitem.entity.BasketItem
import com.cleevio.cinemax.api.module.basketitem.service.command.CancelBasketItemsCommand
import com.cleevio.cinemax.api.module.basketitem.service.command.CreateBasketItemCommand
import com.cleevio.cinemax.api.module.basketitem.service.command.DeleteBasketItemCommand
import com.cleevio.cinemax.api.module.basketitem.service.query.AdminSearchTicketBasketItemsQuery
import com.cleevio.cinemax.api.module.branch.service.BranchRepository
import com.cleevio.cinemax.api.module.card.service.CardRepository
import com.cleevio.cinemax.api.module.cardusage.entity.CardUsage
import com.cleevio.cinemax.api.module.cardusage.service.CardUsageRepository
import com.cleevio.cinemax.api.module.discountcard.constant.DiscountCardType
import com.cleevio.cinemax.api.module.discountcard.service.DiscountCardMssqlService
import com.cleevio.cinemax.api.module.discountcardusage.service.DiscountCardUsageService
import com.cleevio.cinemax.api.module.discountcardusage.service.command.CreateDiscountCardUsageCommand
import com.cleevio.cinemax.api.module.discountcardusage.service.command.CreateMssqlDiscountCardUsageCommand
import com.cleevio.cinemax.api.module.discountcardusage.service.command.UpdateDiscountCardUsageCommand
import com.cleevio.cinemax.api.module.distributor.service.DistributorService
import com.cleevio.cinemax.api.module.movie.service.MovieService
import com.cleevio.cinemax.api.module.posconfiguration.service.PosConfigurationService
import com.cleevio.cinemax.api.module.pricecategory.service.PriceCategoryService
import com.cleevio.cinemax.api.module.pricecategoryitem.constant.PriceCategoryItemNumber
import com.cleevio.cinemax.api.module.pricecategoryitem.service.PriceCategoryItemService
import com.cleevio.cinemax.api.module.product.service.ProductService
import com.cleevio.cinemax.api.module.productcategory.service.ProductCategoryService
import com.cleevio.cinemax.api.module.productcomponent.service.ProductComponentService
import com.cleevio.cinemax.api.module.productcomponentcategory.service.ProductComponentCategoryRepository
import com.cleevio.cinemax.api.module.productcomposition.service.ProductCompositionService
import com.cleevio.cinemax.api.module.production.entity.Production
import com.cleevio.cinemax.api.module.production.service.ProductionRepository
import com.cleevio.cinemax.api.module.reservation.constant.ReservationState
import com.cleevio.cinemax.api.module.reservation.event.ScreeningWithNonDefaultLayoutSyncedFromMssqlEvent
import com.cleevio.cinemax.api.module.reservation.service.ReservationRepository
import com.cleevio.cinemax.api.module.screening.service.ScreeningService
import com.cleevio.cinemax.api.module.screeningfee.service.ScreeningFeeService
import com.cleevio.cinemax.api.module.screeningtype.service.ScreeningTypeService
import com.cleevio.cinemax.api.module.screeningtypes.service.ScreeningTypesService
import com.cleevio.cinemax.api.module.screeningtypes.service.command.DeleteAndCreateScreeningTypesCommand
import com.cleevio.cinemax.api.module.seat.constant.SeatType
import com.cleevio.cinemax.api.module.seat.service.SeatService
import com.cleevio.cinemax.api.module.technology.entity.Technology
import com.cleevio.cinemax.api.module.technology.service.TechnologyRepository
import com.cleevio.cinemax.api.module.ticket.service.TicketService
import com.cleevio.cinemax.api.module.ticket.service.command.UpdateTicketOriginalIdCommand
import com.cleevio.cinemax.api.module.ticket.service.command.UpdateTicketUsedCommand
import com.cleevio.cinemax.api.module.ticketdiscount.constant.TicketDiscountType
import com.cleevio.cinemax.api.module.ticketdiscount.constant.TicketDiscountUsageType
import com.cleevio.cinemax.api.module.ticketdiscount.service.TicketDiscountService
import com.cleevio.cinemax.api.module.ticketprice.constant.ServiceFeeType
import com.cleevio.cinemax.api.module.ticketprice.constant.SurchargeType
import com.cleevio.cinemax.api.util.createAuditorium
import com.cleevio.cinemax.api.util.createAuditoriumLayout
import com.cleevio.cinemax.api.util.createBasket
import com.cleevio.cinemax.api.util.createBranch
import com.cleevio.cinemax.api.util.createCard
import com.cleevio.cinemax.api.util.createDiscountCard
import com.cleevio.cinemax.api.util.createDistributor
import com.cleevio.cinemax.api.util.createMovie
import com.cleevio.cinemax.api.util.createPosConfiguration
import com.cleevio.cinemax.api.util.createPriceCategory
import com.cleevio.cinemax.api.util.createPriceCategoryItem
import com.cleevio.cinemax.api.util.createProduct
import com.cleevio.cinemax.api.util.createProductCategory
import com.cleevio.cinemax.api.util.createProductComponent
import com.cleevio.cinemax.api.util.createProductComponentCategory
import com.cleevio.cinemax.api.util.createProductComposition
import com.cleevio.cinemax.api.util.createReservation
import com.cleevio.cinemax.api.util.createScreening
import com.cleevio.cinemax.api.util.createScreeningFee
import com.cleevio.cinemax.api.util.createScreeningType
import com.cleevio.cinemax.api.util.createSeat
import com.cleevio.cinemax.api.util.createTicketBasketItemRequest
import com.cleevio.cinemax.api.util.createTicketDiscount
import com.cleevio.cinemax.api.util.mapToCreateBasketItemCommand
import com.cleevio.cinemax.api.util.mapToCreateOrUpdateAuditoriumCommand
import com.cleevio.cinemax.api.util.mapToCreateOrUpdateAuditoriumLayoutCommand
import com.cleevio.cinemax.api.util.mapToCreateOrUpdateDiscountCardCommand
import com.cleevio.cinemax.api.util.mapToCreateOrUpdateDistributorCommand
import com.cleevio.cinemax.api.util.mapToCreateOrUpdateMovieCommand
import com.cleevio.cinemax.api.util.mapToCreateOrUpdatePosConfigurationCommand
import com.cleevio.cinemax.api.util.mapToCreateOrUpdatePriceCategoryItemCommand
import com.cleevio.cinemax.api.util.mapToCreateOrUpdateProductCategoryCommand
import com.cleevio.cinemax.api.util.mapToCreateOrUpdateProductComponentCommand
import com.cleevio.cinemax.api.util.mapToCreateOrUpdateProductCompositionCommand
import com.cleevio.cinemax.api.util.mapToCreateOrUpdateScreeningCommand
import com.cleevio.cinemax.api.util.mapToCreateOrUpdateScreeningFeeCommand
import com.cleevio.cinemax.api.util.mapToCreateOrUpdateScreeningTypeCommand
import com.cleevio.cinemax.api.util.mapToCreateOrUpdateSeatCommand
import com.cleevio.cinemax.api.util.mapToCreateOrUpdateTicketDiscountCommand
import com.cleevio.cinemax.api.util.mapToSyncCreateOrUpdatePriceCategoryCommand
import com.cleevio.cinemax.api.util.mapToSyncCreateOrUpdateProductCommand
import io.mockk.Runs
import io.mockk.every
import io.mockk.just
import org.junit.jupiter.api.BeforeAll
import org.junit.jupiter.api.Test
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.Arguments
import org.junit.jupiter.params.provider.MethodSource
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.data.domain.PageRequest
import org.springframework.data.domain.Pageable
import org.springframework.data.domain.Sort
import java.time.LocalDateTime
import java.time.LocalTime
import java.util.UUID
import java.util.stream.Stream
import kotlin.test.assertEquals
import kotlin.test.assertNull

class AdminSearchTicketBasketItemQueryServiceIT @Autowired constructor(
    private val underTest: AdminSearchTicketBasketItemQueryService,
    private val basketItemJooqFinderService: BasketItemJooqFinderService,
    private val posConfigurationService: PosConfigurationService,
    private val auditoriumService: AuditoriumService,
    private val auditoriumLayoutService: AuditoriumLayoutService,
    private val seatService: SeatService,
    private val distributorService: DistributorService,
    private val movieService: MovieService,
    private val productionRepository: ProductionRepository,
    private val technologyRepository: TechnologyRepository,
    private val screeningService: ScreeningService,
    private val screeningTypeService: ScreeningTypeService,
    private val screeningTypesService: ScreeningTypesService,
    private val priceCategoryService: PriceCategoryService,
    private val priceCategoryItemService: PriceCategoryItemService,
    private val screeningFeeService: ScreeningFeeService,
    private val ticketDiscountService: TicketDiscountService,
    private val discountCardMssqlService: DiscountCardMssqlService,
    private val discountCardUsageService: DiscountCardUsageService,
    private val basketRepository: BasketRepository,
    private val basketService: BasketService,
    private val basketReceiptNumbersService: BasketReceiptNumbersService,
    private val basketItemService: BasketItemService,
    private val basketItemRepository: BasketItemRepository,
    private val productService: ProductService,
    private val productCategoryService: ProductCategoryService,
    private val productComponentService: ProductComponentService,
    private val productCompositionService: ProductCompositionService,
    private val ticketService: TicketService,
    private val productComponentCategoryRepository: ProductComponentCategoryRepository,
    private val reservationRepository: ReservationRepository,
    private val branchRepository: BranchRepository,
    private val cardRepository: CardRepository,
    private val cardUsageRepository: CardUsageRepository,
) : IntegrationTest(DatabaseCleanup.AFTER_ALL) {

    @BeforeAll
    fun setUp() {
        every {
            reservationMssqlFinderServiceMock.findByOriginalScreeningIdAndOriginalSeatId(
                any(),
                any()
            )
        } returns null
        every { productReceiptNumberGeneratorMock.generateProductReceiptNumber() } returns "0000000001"
        every { applicationEventPublisherMock.publishEvent(any<ScreeningWithNonDefaultLayoutSyncedFromMssqlEvent>()) } just Runs

        branchRepository.save(BRANCH_1)
        setOf(POS_CONFIGURATION_1, POS_CONFIGURATION_2).forEach {
            posConfigurationService.createOrUpdatePosConfiguration(mapToCreateOrUpdatePosConfigurationCommand(it))
        }
        setOf(AUDITORIUM_1, AUDITORIUM_2, AUDITORIUM_3).forEach {
            auditoriumService.syncCreateOrUpdateAuditorium(mapToCreateOrUpdateAuditoriumCommand(it))
        }
        setOf(AUDITORIUM_LAYOUT_1, AUDITORIUM_LAYOUT_2, AUDITORIUM_LAYOUT_3).forEach {
            auditoriumLayoutService.syncCreateOrUpdateAuditoriumLayout(mapToCreateOrUpdateAuditoriumLayoutCommand(it))
        }
        setOf(
            SEAT_1,
            SEAT_2,
            SEAT_3,
            SEAT_4,
            SEAT_5,
            SEAT_6,
            SEAT_7,
            SEAT_8,
            SEAT_9,
            SEAT_10,
            SEAT_11,
            SEAT_12,
            SEAT_13
        ).forEach {
            seatService.createOrUpdateSeat(mapToCreateOrUpdateSeatCommand(it))
        }

        with(technologyRepository) {
            deleteAll()
            saveAll(setOf(TECHNOLOGY_1, TECHNOLOGY_2, TECHNOLOGY_3))
        }
        with(productionRepository) {
            deleteAll()
            saveAll(setOf(PRODUCTION_1, PRODUCTION_2))
        }
        setOf(DISTRIBUTOR_1, DISTRIBUTOR_2).forEach {
            distributorService.syncCreateOrUpdateDistributor(mapToCreateOrUpdateDistributorCommand(it))
        }
        setOf(MOVIE_1, MOVIE_2, MOVIE_3, MOVIE_4).forEach {
            movieService.syncCreateOrUpdateMovie(mapToCreateOrUpdateMovieCommand(it))
        }

        setOf(PRICE_CATEGORY_1, PRICE_CATEGORY_2).forEach {
            priceCategoryService.syncCreateOrUpdatePriceCategory(mapToSyncCreateOrUpdatePriceCategoryCommand(it))
        }
        setOf(
            PRICE_CATEGORY_ITEM_1,
            PRICE_CATEGORY_ITEM_2,
            PRICE_CATEGORY_ITEM_3,
            PRICE_CATEGORY_ITEM_4,
            PRICE_CATEGORY_ITEM_5,
            PRICE_CATEGORY_ITEM_6
        ).forEach {
            priceCategoryItemService.createOrUpdatePriceCategoryItem(
                mapToCreateOrUpdatePriceCategoryItemCommand(it, it.priceCategoryId)
            )
        }
        setOf(SCREENING_1, SCREENING_2, SCREENING_3, SCREENING_4).forEach {
            screeningService.syncCreateOrUpdateScreening(mapToCreateOrUpdateScreeningCommand(it))
        }
        setOf(SCREENING_FEE_1, SCREENING_FEE_2, SCREENING_FEE_3, SCREENING_FEE_4).forEach {
            screeningFeeService.createOrUpdateScreeningFee(mapToCreateOrUpdateScreeningFeeCommand(it))
        }
        setOf(SCREENING_TYPE_1, SCREENING_TYPE_2, SCREENING_TYPE_3).forEach {
            screeningTypeService.syncCreateOrUpdateScreeningType(mapToCreateOrUpdateScreeningTypeCommand(it))
        }
        screeningTypesService.deleteAndCreateScreeningTypes(
            DeleteAndCreateScreeningTypesCommand(SCREENING_1.id, setOf(SCREENING_TYPE_2.id, SCREENING_TYPE_3.id))
        )
        screeningTypesService.deleteAndCreateScreeningTypes(
            DeleteAndCreateScreeningTypesCommand(SCREENING_4.id, setOf(SCREENING_TYPE_1.id))
        )
        reservationRepository.saveAll(setOf(ONLINE_RESERVATION_1, ONLINE_RESERVATION_2))

        setOf(TICKET_DISCOUNT_1, TICKET_DISCOUNT_2, TICKET_DISCOUNT_3, TICKET_DISCOUNT_4).forEach {
            ticketDiscountService.syncCreateOrUpdateTicketDiscount(mapToCreateOrUpdateTicketDiscountCommand(it))
        }
        setOf(DISCOUNT_CARD_1, DISCOUNT_CARD_2).forEach {
            discountCardMssqlService.createOrUpdateDiscountCard(mapToCreateOrUpdateDiscountCardCommand(it))
        }
        cardRepository.save(CARD_1)

        productCategoryService.syncCreateOrUpdateProductCategory(
            mapToCreateOrUpdateProductCategoryCommand(
                PRODUCT_CATEGORY_1
            )
        )
        productComponentCategoryRepository.save(PRODUCT_COMPONENT_CATEGORY_1)
        productComponentService.syncCreateOrUpdateProductComponent(
            mapToCreateOrUpdateProductComponentCommand(
                PRODUCT_COMPONENT_1
            )
        )
        productService.syncCreateOrUpdateProduct(mapToSyncCreateOrUpdateProductCommand(PRODUCT_1, PRODUCT_CATEGORY_1.id))
        productCompositionService.createOrUpdateProductComposition(
            mapToCreateOrUpdateProductCompositionCommand(PRODUCT_COMPOSITION_1)
        )

        // two tickets, no discounts, two base prices
        BASKET_1.createBasket()
        BASKET_1.addToBasket(1, SEAT_1.id, SCREENING_1.id, PriceCategoryItemNumber.PRICE_1)
        BASKET_1.addToBasket(2, SEAT_2.id, SCREENING_1.id, PriceCategoryItemNumber.PRICE_2)
        BASKET_1.payBasket(PaymentType.CASH, POS_CONFIGURATION_1.id)

        // one ticket, no discounts, two base prices, cashless, another POS
        BASKET_2.createBasket()
        BASKET_2.addToBasket(3, SEAT_3.id, SCREENING_1.id, PriceCategoryItemNumber.PRICE_1)
        BASKET_2.payBasket(PaymentType.CASHLESS, POS_CONFIGURATION_2.id)

        // one ticket, but basket contains product item
        BASKET_3.createBasket()
        BASKET_3.addToBasket(4, SEAT_4.id, SCREENING_2.id, PriceCategoryItemNumber.PRICE_2)
        BASKET_3.addProductToBasket(101, PRODUCT_1.id)
        BASKET_3.payBasket(PaymentType.CASH, POS_CONFIGURATION_1.id)

        // two tickets, each with one applied ticket discount
        BASKET_4.createBasket()
        BASKET_4.addToBasket(
            originalId = 5,
            seatId = SEAT_5.id,
            screeningId = SCREENING_2.id,
            priceCategoryItemNumber = PriceCategoryItemNumber.PRICE_1,
            primaryTicketDiscountId = TICKET_DISCOUNT_1.id
        )
        BASKET_4.addToBasket(
            originalId = 6,
            seatId = SEAT_6.id,
            screeningId = SCREENING_2.id,
            priceCategoryItemNumber = PriceCategoryItemNumber.PRICE_2,
            secondaryTicketDiscountId = TICKET_DISCOUNT_2.id
        )
        BASKET_4.payBasket(PaymentType.CASHLESS, POS_CONFIGURATION_1.id)

        // two tickets, one with applied discount card, one with voucher, both tickets used
        BASKET_5.createBasket()
        BASKET_5.addToBasket(
            originalId = 7,
            seatId = SEAT_7.id,
            screeningId = SCREENING_3.id,
            priceCategoryItemNumber = PriceCategoryItemNumber.PRICE_1,
            primaryDiscountCardId = DISCOUNT_CARD_1.id,
            cardId = CARD_1.id,
            primaryTicketDiscountId = TICKET_DISCOUNT_3.id,
            posConfigurationId = POS_CONFIGURATION_2.id,
            isUsed = true,
            ticketOriginalId = 1
        )
        BASKET_5.addToBasket(
            originalId = 8,
            seatId = SEAT_8.id,
            screeningId = SCREENING_3.id,
            priceCategoryItemNumber = PriceCategoryItemNumber.PRICE_1,
            secondaryDiscountCardId = DISCOUNT_CARD_2.id,
            secondaryTicketDiscountId = TICKET_DISCOUNT_4.id,
            posConfigurationId = POS_CONFIGURATION_2.id,
            isUsed = true,
            ticketOriginalId = 2
        )
        BASKET_5.payBasket(PaymentType.CASH, POS_CONFIGURATION_2.id)

        // cancelled and deleted ticket
        BASKET_6.createBasket()
        val basket6Item1 = BASKET_6.addToBasket(9, SEAT_9.id, SCREENING_4.id, PriceCategoryItemNumber.PRICE_2)
        BASKET_6.cancelBasketItem(10, basket6Item1.id)
        val basket6Item2 = BASKET_6.addToBasket(11, SEAT_10.id, SCREENING_4.id, PriceCategoryItemNumber.PRICE_2)
        BASKET_6.deleteBasketItem(basket6Item2.id)
        BASKET_6.payBasket(PaymentType.CASHLESS, POS_CONFIGURATION_2.id)

        // non-paid basket - shouldn't appear anywhere
        BASKET_7.createBasket()
        BASKET_7.addToBasket(12, SEAT_10.id, SCREENING_4.id, PriceCategoryItemNumber.PRICE_1)

        // online basket
        BASKET_8.createBasket()
        BASKET_8.addToOnlineBasket(
            originalId = 13,
            seatId = SEAT_11.id,
            screeningId = SCREENING_1.id,
            reservationId = ONLINE_RESERVATION_1.id,
            priceCategoryItemNumber = PriceCategoryItemNumber.PRICE_20,
            includes3dGlasses = false
        )
        BASKET_8.addToOnlineBasket(
            originalId = 14,
            seatId = SEAT_12.id,
            screeningId = SCREENING_1.id,
            reservationId = ONLINE_RESERVATION_2.id,
            priceCategoryItemNumber = PriceCategoryItemNumber.PRICE_19,
            includes3dGlasses = true
        )

        // historical MSSQL sales basket
        BASKET_9.createBasket()
        BASKET_9.addToMssqlTicketSalesBasket(
            originalId = 15,
            seatId = SEAT_13.id,
            screeningId = SCREENING_1.id,
            priceCategoryItemNumber = PriceCategoryItemNumber.PRICE_1,
            branchId = BRANCH_1.id
        )
    }

    @Test
    fun `test searchTicketBasketItems - item with all attributes - should be mapped correctly`() {
        val basketItem = basketItemRepository.findAllByBasketId(BASKET_5.id).first()
        basketItemRepository.save(
            basketItem.apply { branchId = BRANCH_1.id }
        )

        val result = underTest(
            AdminSearchTicketBasketItemsQuery(
                pageable = Pageable.unpaged(),
                filter = AdminSearchTicketBasketItemsFilter(
                    discountCardCodes = setOf(CARD_1.code)
                )
            )
        ).content.first()

        val expectedTotalPrice = listOf(
            PRICE_CATEGORY_ITEM_3.price,
            SCREENING_FEE_3.getSurchargesSum(),
            SCREENING_FEE_3.getServiceFeesSum(),
            -TICKET_DISCOUNT_3.amount!!
        ).sumOf { it }

        val expectedBasePrice = listOf(
            PRICE_CATEGORY_ITEM_3.price,
            -TICKET_DISCOUNT_3.amount!!
        ).sumOf { it }

        assertEquals(BasketItemType.TICKET, result.type)
        assertEquals(false, result.isCancelled)
        assertEquals("Bratislava Bory", result.branchName)
        assertEquals(BasketState.PAID, result.basket.state)
        assertEquals(BASKET_5.paidAt?.truncatedToSeconds(), result.basket.paidAt.truncatedToSeconds())
        assertEquals(BASKET_5.updatedBy, result.basket.updatedBy)
        assertEquals(PaymentType.CASH, result.basket.paymentType)
        assertEquals(POS_CONFIGURATION_2.id, result.basket.paymentPosConfiguration?.id)
        assertEquals(POS_CONFIGURATION_2.title, result.basket.paymentPosConfiguration?.title)
        assertEquals("1000000007", result.ticket.receiptNumber)
        assertEquals(true, result.ticket.isUsed)
        assertEquals(false, result.ticket.includes3dGlasses)
        assertEquals(expectedTotalPrice, result.ticket.ticketPrice.totalPrice)
        assertEquals(expectedBasePrice, result.ticket.ticketPrice.basePrice)
        assertEquals(SCREENING_FEE_3.getSurchargesSum(), result.ticket.ticketPrice.surchargesSum)
        assertEquals(SCREENING_FEE_3.getServiceFeesSum(), result.ticket.ticketPrice.serviceFeesSum)
        assertEquals(TICKET_DISCOUNT_3.id, result.ticket.primaryTicketDiscount?.id)
        assertNull(result.ticket.secondaryTicketDiscount?.id)
        assertEquals(SCREENING_3.id, result.ticket.screening.id)
        assertEquals(SCREENING_3.date, result.ticket.screening.date)
        assertEquals(SCREENING_3.time, result.ticket.screening.time)
        assertEquals(SCREENING_3.saleTimeLimit, result.ticket.screening.saleTimeLimit)
        assertEquals(AUDITORIUM_3.id, result.ticket.screening.auditorium.id)
        assertEquals(AUDITORIUM_3.code, result.ticket.screening.auditorium.code)
        assertEquals(MOVIE_4.id, result.ticket.screening.movie.id)
        assertEquals(MOVIE_4.rawTitle, result.ticket.screening.movie.rawTitle)
        assertEquals(DISTRIBUTOR_2.id, result.ticket.screening.movie.distributor.id)
        assertEquals(DISTRIBUTOR_2.title, result.ticket.screening.movie.distributor.title)
        assertEquals(SEAT_7.id, result.ticket.seat.id)
        assertEquals(SEAT_7.row, result.ticket.seat.row)
        assertEquals(SEAT_7.number, result.ticket.seat.number)
        assertEquals(CARD_1.id, result.discountCard?.id)
        assertEquals(CARD_1.type, result.discountCard?.type)
        assertEquals(CARD_1.code, result.discountCard?.code)
        assertEquals(CARD_1.title, result.discountCard?.title)
        assertEquals(CARD_1.id, result.card?.id)
        assertEquals(CARD_1.code, result.card?.code)
        assertEquals(CARD_1.title, result.card?.title)

        basketItemRepository.save(basketItem.apply { branchId = null })
    }

    @ParameterizedTest
    @MethodSource("filtersAndExpectedOriginalIdsProvider")
    fun `test searchTicketBasketItems - single filter - should search correct items`(
        filter: AdminSearchTicketBasketItemsFilter,
        expectedResult: Set<Int>,
    ) {
        val response = underTest(
            AdminSearchTicketBasketItemsQuery(
                pageable = Pageable.unpaged(),
                filter = filter
            )
        ).content

        // assert equality of sets here, because ordering is not the point of this test
        assertEquals(expectedResult, determineOriginalIdsFromIds(response).toSet())
    }

    @ParameterizedTest
    @MethodSource("sortsAndExpectedOriginalIdsProvider")
    fun `test searchTicketBasketItems - pagination and sorting - should search correct items`(
        pageable: Pageable,
        expectedResult: List<Int>,
    ) {
        val response = underTest(
            AdminSearchTicketBasketItemsQuery(
                pageable = pageable,
                filter = AdminSearchTicketBasketItemsFilter()
            )
        ).content

        assertEquals(expectedResult, determineOriginalIdsFromIds(response))
    }

    private fun determineOriginalIdsFromIds(response: List<AdminSearchTicketBasketItemsResponse>): List<Int> {
        val idToOriginalIdMap = basketItemJooqFinderService.findAll().associateNotNull { it.id to it.originalId }
        return response.map { it.id }.mapNotNull { idToOriginalIdMap[it] }
    }

    companion object {
        @JvmStatic
        fun filtersAndExpectedOriginalIdsProvider(): Stream<Arguments> {
            return Stream.of(
                Arguments.of(
                    AdminSearchTicketBasketItemsFilter(),
                    setOf(1, 2, 3, 4, 5, 6, 7, 8, 9, 13, 14, 15)
                ),
                Arguments.of(
                    AdminSearchTicketBasketItemsFilter(
                        basketPaidAtFrom = BASKET_3.paidAt
                    ),
                    setOf(4, 5, 6, 7, 8, 9, 13, 14, 15)
                ),
                Arguments.of(
                    AdminSearchTicketBasketItemsFilter(
                        basketPaidAtTo = BASKET_4.paidAt
                    ),
                    setOf(1, 2, 3, 4, 5, 6)
                ),
                Arguments.of(
                    AdminSearchTicketBasketItemsFilter(
                        basketPaidAtFrom = BASKET_3.paidAt,
                        basketPaidAtTo = BASKET_4.paidAt
                    ),
                    setOf(4, 5, 6)
                ),
                Arguments.of(
                    AdminSearchTicketBasketItemsFilter(
                        basketStates = setOf(BasketState.PAID_ONLINE)
                    ),
                    setOf(13, 14)
                ),
                Arguments.of(
                    AdminSearchTicketBasketItemsFilter(
                        basketStates = setOf(BasketState.PAID)
                    ),
                    setOf(1, 2, 3, 4, 5, 6, 7, 8, 9, 15)
                ),
                Arguments.of(
                    AdminSearchTicketBasketItemsFilter(
                        basketVariableSymbol = "4293126007"
                    ),
                    setOf(13, 14)
                ),
                Arguments.of(
                    AdminSearchTicketBasketItemsFilter(
                        screeningDateTimeFrom = SCREENING_4.getScreeningTime()
                    ),
                    setOf(7, 8, 9)
                ),
                Arguments.of(
                    AdminSearchTicketBasketItemsFilter(
                        screeningDateTimeTo = SCREENING_1.getScreeningTime()
                    ),
                    setOf(1, 2, 3, 4, 5, 6, 13, 14, 15)
                ),
                Arguments.of(
                    AdminSearchTicketBasketItemsFilter(
                        screeningDateTimeFrom = SCREENING_1.getScreeningTime(),
                        screeningDateTimeTo = SCREENING_4.getScreeningTime()
                    ),
                    setOf(1, 2, 3, 9, 13, 14, 15)
                ),
                Arguments.of(
                    AdminSearchTicketBasketItemsFilter(
                        auditoriumIds = setOf(AUDITORIUM_1.id, AUDITORIUM_3.id)
                    ),
                    setOf(1, 2, 3, 7, 8, 13, 14, 15)
                ),
                Arguments.of(
                    AdminSearchTicketBasketItemsFilter(
                        screeningIds = setOf(SCREENING_2.id, SCREENING_3.id)
                    ),
                    setOf(4, 5, 6, 7, 8)
                ),
                Arguments.of(
                    AdminSearchTicketBasketItemsFilter(
                        movieIds = setOf(MOVIE_2.id)
                    ),
                    setOf(1, 2, 3, 13, 14, 15)
                ),
                Arguments.of(
                    AdminSearchTicketBasketItemsFilter(
                        distributorIds = setOf(DISTRIBUTOR_2.id)
                    ),
                    setOf(7, 8, 9)
                ),
                Arguments.of(
                    AdminSearchTicketBasketItemsFilter(
                        posConfigurationIds = setOf(POS_CONFIGURATION_1.id)
                    ),
                    setOf(1, 2, 4, 5, 6)
                ),
                Arguments.of(
                    AdminSearchTicketBasketItemsFilter(
                        paymentTypes = setOf(PaymentType.CASHLESS)
                    ),
                    setOf(3, 5, 6, 9, 13, 14, 15)
                ),
                Arguments.of(
                    AdminSearchTicketBasketItemsFilter(
                        ticketUsed = true
                    ),
                    setOf(7, 8, 15)
                ),
                Arguments.of(
                    AdminSearchTicketBasketItemsFilter(
                        ticketIncludes3dGlasses = true
                    ),
                    setOf(14)
                ),
                Arguments.of(
                    AdminSearchTicketBasketItemsFilter(
                        surchargeTypes = setOf(SurchargeType.IMAX, SurchargeType.VIP)
                    ),
                    setOf(1, 2, 3, 9)
                ),
                Arguments.of(
                    AdminSearchTicketBasketItemsFilter(
                        serviceFeeTypes = setOf(ServiceFeeType.IMAX, ServiceFeeType.PREMIUM_PLUS)
                    ),
                    setOf(3, 6, 7, 8)
                ),
                Arguments.of(
                    AdminSearchTicketBasketItemsFilter(
                        serviceFeeTypes = setOf(ServiceFeeType.GENERAL)
                    ),
                    setOf(9)
                ),
                Arguments.of(
                    AdminSearchTicketBasketItemsFilter(
                        screeningTypeIds = setOf(SCREENING_TYPE_2.id)
                    ),
                    setOf(1, 2, 3, 13, 14, 15)
                ),
                Arguments.of(
                    AdminSearchTicketBasketItemsFilter(
                        technologyIds = setOf(TECHNOLOGY_2.id)
                    ),
                    setOf(4, 5, 6, 7, 8)
                ),
                Arguments.of(
                    AdminSearchTicketBasketItemsFilter(
                        productionIds = setOf(PRODUCTION_2.id)
                    ),
                    setOf(7, 8)
                ),
                Arguments.of(
                    AdminSearchTicketBasketItemsFilter(
                        ticketReceiptNumbers = setOf("1000000001", "1000000005", "1000000009")
                    ),
                    setOf(1, 5, 9)
                ),
                Arguments.of(
                    AdminSearchTicketBasketItemsFilter(
                        discountCardCodes = setOf(DISCOUNT_CARD_1.code)
                    ),
                    setOf(7)
                ),
                Arguments.of(
                    AdminSearchTicketBasketItemsFilter(
                        discountCardTitles = setOf(DISCOUNT_CARD_2.title)
                    ),
                    setOf(8)
                ),
                Arguments.of(
                    AdminSearchTicketBasketItemsFilter(
                        discountCardCodes = setOf(CARD_1.code)
                    ),
                    setOf(7)
                ),
                Arguments.of(
                    AdminSearchTicketBasketItemsFilter(
                        discountCardTitles = setOf(CARD_1.title)
                    ),
                    setOf(7)
                ),
                Arguments.of(
                    AdminSearchTicketBasketItemsFilter(
                        isDiscounted = true
                    ),
                    setOf(2, 5, 6, 7, 8, 14)
                ),
                Arguments.of(
                    AdminSearchTicketBasketItemsFilter(
                        isDiscounted = false
                    ),
                    setOf(1, 3, 4, 9, 13, 15)
                ),
                Arguments.of(
                    AdminSearchTicketBasketItemsFilter(
                        primaryTicketDiscountIds = setOf(
                            TICKET_DISCOUNT_1.id,
                            TICKET_DISCOUNT_2.id,
                            TICKET_DISCOUNT_4.id
                        )
                    ),
                    setOf(5)
                ),
                Arguments.of(
                    AdminSearchTicketBasketItemsFilter(
                        secondaryTicketDiscountIds = setOf(
                            TICKET_DISCOUNT_1.id,
                            TICKET_DISCOUNT_2.id,
                            TICKET_DISCOUNT_4.id
                        )
                    ),
                    setOf(6, 8)
                ),
                Arguments.of(
                    AdminSearchTicketBasketItemsFilter(
                        isCancelled = true
                    ),
                    setOf(9)
                ),
                Arguments.of(
                    AdminSearchTicketBasketItemsFilter(
                        isCancelled = false
                    ),
                    setOf(1, 2, 3, 4, 5, 6, 7, 8, 13, 14, 15)
                ),
                Arguments.of(
                    AdminSearchTicketBasketItemsFilter(
                        basketUpdatedBy = setOf("anonymous")
                    ),
                    setOf(1, 2, 3, 4, 5, 6, 7, 8, 9, 13, 14, 15)
                ),
                Arguments.of(
                    AdminSearchTicketBasketItemsFilter(
                        basketUpdatedBy = setOf("monika")
                    ),
                    emptySet<Int>()
                )
            )
        }

        @JvmStatic
        fun sortsAndExpectedOriginalIdsProvider(): Stream<Arguments> {
            // when the items are sorted by common values, e.g. Basket.paidAt or Basket.paymentType, there has to be a default
            // sort to have deterministic results - this one's order is different from the default, so it was easier writing it
            val defaultSort = Sort.Order.asc("basketItem.createdAt")

            return Stream.of(
                Arguments.of(
                    PageRequest.of(0, 5, Sort.by(defaultSort)),
                    listOf(1, 2, 3, 4, 5)
                ),
                Arguments.of(
                    PageRequest.of(1, 5, Sort.by(defaultSort)),
                    listOf(6, 7, 8, 9, 13)
                ),
                Arguments.of(
                    PageRequest.of(0, 15, Sort.by(Sort.Order.asc("basket.paidAt"), defaultSort)),
                    listOf(1, 2, 3, 4, 5, 6, 7, 8, 9, 13, 14, 15)
                ),
                Arguments.of(
                    PageRequest.of(0, 15, Sort.by(Sort.Order.desc("basket.paidAt"), defaultSort)),
                    listOf(15, 13, 14, 9, 7, 8, 5, 6, 4, 3, 1, 2)
                ),
                Arguments.of(
                    PageRequest.of(0, 15, Sort.by(Sort.Order.asc("basket.state"), defaultSort)),
                    listOf(1, 2, 3, 4, 5, 6, 7, 8, 9, 15, 13, 14)
                ),
                Arguments.of(
                    PageRequest.of(0, 15, Sort.by(Sort.Order.desc("basket.state"), defaultSort)),
                    listOf(13, 14, 1, 2, 3, 4, 5, 6, 7, 8, 9, 15)
                ),
                Arguments.of(
                    PageRequest.of(0, 15, Sort.by(Sort.Order.asc("basket.variableSymbol"), defaultSort)),
                    listOf(13, 14, 1, 2, 3, 4, 5, 6, 7, 8, 9, 15)
                ),
                Arguments.of(
                    PageRequest.of(0, 15, Sort.by(Sort.Order.asc("basket.updatedBy"), defaultSort)),
                    listOf(1, 2, 3, 4, 5, 6, 7, 8, 9, 13, 14, 15)
                ),
                Arguments.of(
                    PageRequest.of(0, 15, Sort.by(Sort.Order.asc("branch.name"), defaultSort)),
                    listOf(1, 2, 3, 4, 5, 6, 7, 8, 9, 13, 14, 15)
                ),
                Arguments.of(
                    PageRequest.of(0, 15, Sort.by(Sort.Order.desc("branch.name"), defaultSort)),
                    listOf(1, 2, 3, 4, 5, 6, 7, 8, 9, 13, 14, 15)
                ),
                Arguments.of(
                    PageRequest.of(0, 15, Sort.by(Sort.Order.asc("ticket.receiptNumber"), defaultSort)),
                    listOf(1, 2, 3, 4, 5, 6, 7, 8, 9, 13, 14, 15)
                ),
                Arguments.of(
                    PageRequest.of(0, 15, Sort.by(Sort.Order.desc("ticket.receiptNumber"), defaultSort)),
                    listOf(15, 14, 13, 9, 8, 7, 6, 5, 4, 3, 2, 1)
                ),
                Arguments.of(
                    PageRequest.of(0, 15, Sort.by(Sort.Order.asc("posConfiguration.title"), defaultSort)),
                    listOf(1, 2, 4, 5, 6, 3, 7, 8, 9, 13, 14, 15)
                ),
                Arguments.of(
                    PageRequest.of(0, 15, Sort.by(Sort.Order.desc("posConfiguration.title"), defaultSort)),
                    listOf(3, 7, 8, 9, 1, 2, 4, 5, 6, 13, 14, 15)
                ),
                Arguments.of(
                    PageRequest.of(0, 15, Sort.by(Sort.Order.asc("auditorium.code"), defaultSort)),
                    listOf(4, 5, 6, 9, 7, 8, 1, 2, 3, 13, 14, 15)
                ),
                Arguments.of(
                    PageRequest.of(0, 15, Sort.by(Sort.Order.desc("auditorium.code"), defaultSort)),
                    listOf(1, 2, 3, 13, 14, 15, 7, 8, 4, 5, 6, 9)
                ),
                Arguments.of(
                    PageRequest.of(0, 15, Sort.by(Sort.Order.asc("distributor.title"), defaultSort)),
                    listOf(1, 2, 3, 4, 5, 6, 13, 14, 15, 7, 8, 9)
                ),
                Arguments.of(
                    PageRequest.of(0, 15, Sort.by(Sort.Order.desc("distributor.title"), defaultSort)),
                    listOf(7, 8, 9, 1, 2, 3, 4, 5, 6, 13, 14, 15)
                ),
                Arguments.of(
                    PageRequest.of(
                        0,
                        15,
                        Sort.by(Sort.Order.asc("screening.date"), Sort.Order.asc("screening.time"), defaultSort)
                    ),
                    listOf(4, 5, 6, 1, 2, 3, 13, 14, 15, 9, 7, 8)
                ),
                Arguments.of(
                    PageRequest.of(
                        0,
                        15,
                        Sort.by(Sort.Order.desc("screening.date"), Sort.Order.desc("screening.time"), defaultSort)
                    ),
                    listOf(7, 8, 9, 1, 2, 3, 13, 14, 15, 4, 5, 6)
                ),
                Arguments.of(
                    PageRequest.of(0, 15, Sort.by(Sort.Order.asc("movie.rawTitle"), defaultSort)),
                    listOf(1, 2, 3, 13, 14, 15, 9, 7, 8, 4, 5, 6)
                ),
                Arguments.of(
                    PageRequest.of(0, 15, Sort.by(Sort.Order.desc("movie.rawTitle"), defaultSort)),
                    listOf(4, 5, 6, 7, 8, 9, 1, 2, 3, 13, 14, 15)
                ),
                Arguments.of(
                    PageRequest.of(0, 15, Sort.by(Sort.Order.asc("ticket.isUsed"), defaultSort)),
                    listOf(1, 2, 3, 4, 5, 6, 9, 13, 14, 7, 8, 15)
                ),
                Arguments.of(
                    PageRequest.of(0, 15, Sort.by(Sort.Order.desc("ticket.isUsed"), defaultSort)),
                    listOf(7, 8, 15, 1, 2, 3, 4, 5, 6, 9, 13, 14)
                ),
                Arguments.of(
                    PageRequest.of(0, 15, Sort.by(Sort.Order.asc("ticket.includes3dGlasses"), defaultSort)),
                    listOf(1, 2, 3, 4, 5, 6, 7, 8, 9, 13, 15, 14)
                ),
                Arguments.of(
                    PageRequest.of(0, 15, Sort.by(Sort.Order.desc("ticket.includes3dGlasses"), defaultSort)),
                    listOf(14, 1, 2, 3, 4, 5, 6, 7, 8, 9, 13, 15)
                ),
                Arguments.of(
                    PageRequest.of(0, 15, Sort.by(Sort.Order.asc("basket.paymentType"), defaultSort)),
                    listOf(1, 2, 4, 7, 8, 3, 5, 6, 9, 13, 14, 15)
                ),
                Arguments.of(
                    PageRequest.of(0, 15, Sort.by(Sort.Order.desc("basket.paymentType"), defaultSort)),
                    listOf(3, 5, 6, 9, 13, 14, 15, 1, 2, 4, 7, 8)
                ),
                Arguments.of(
                    PageRequest.of(0, 15, Sort.by(Sort.Order.asc("ticket.basePrice"), defaultSort)),
                    listOf(5, 13, 14, 15, 6, 4, 9, 8, 2, 7, 1, 3)
                ),
                Arguments.of(
                    PageRequest.of(0, 15, Sort.by(Sort.Order.desc("ticket.basePrice"), defaultSort)),
                    listOf(1, 3, 2, 7, 8, 4, 9, 6, 5, 13, 14, 15)
                ),
                Arguments.of(
                    PageRequest.of(0, 15, Sort.by(Sort.Order.asc("ticketPrice.serviceFeesSum"), defaultSort)),
                    listOf(1, 2, 4, 5, 6, 13, 14, 15, 3, 7, 8, 9)
                ),
                Arguments.of(
                    PageRequest.of(0, 15, Sort.by(Sort.Order.desc("ticketPrice.serviceFeesSum"), defaultSort)),
                    listOf(3, 7, 8, 9, 1, 2, 4, 5, 6, 13, 14, 15)
                ),
                Arguments.of(
                    PageRequest.of(0, 15, Sort.by(Sort.Order.asc("ticketPrice.surchargesSum"), defaultSort)),
                    listOf(4, 5, 6, 8, 13, 14, 15, 1, 2, 3, 7, 9)
                ),
                Arguments.of(
                    PageRequest.of(0, 15, Sort.by(Sort.Order.desc("ticketPrice.surchargesSum"), defaultSort)),
                    listOf(1, 2, 3, 7, 9, 4, 5, 6, 8, 13, 14, 15)
                ),
                Arguments.of(
                    PageRequest.of(0, 15, Sort.by(Sort.Order.asc("basketItem.isCancelled"), defaultSort)),
                    listOf(1, 2, 3, 4, 5, 6, 7, 8, 13, 14, 15, 9)
                ),
                Arguments.of(
                    PageRequest.of(0, 15, Sort.by(Sort.Order.desc("basketItem.isCancelled"), defaultSort)),
                    listOf(9, 1, 2, 3, 4, 5, 6, 7, 8, 13, 14, 15)
                ),
                Arguments.of(
                    PageRequest.of(0, 15, Sort.by(Sort.Order.asc("primaryTicketDiscount.title"), defaultSort)),
                    listOf(7, 5, 1, 2, 3, 4, 6, 8, 9, 13, 14, 15)
                ),
                Arguments.of(
                    PageRequest.of(0, 15, Sort.by(Sort.Order.desc("primaryTicketDiscount.title"), defaultSort)),
                    listOf(5, 7, 1, 2, 3, 4, 6, 8, 9, 13, 14, 15)
                ),
                Arguments.of(
                    PageRequest.of(0, 15, Sort.by(Sort.Order.asc("secondaryTicketDiscount.title"), defaultSort)),
                    listOf(8, 6, 1, 2, 3, 4, 5, 7, 9, 13, 14, 15)
                ),
                Arguments.of(
                    PageRequest.of(0, 15, Sort.by(Sort.Order.desc("secondaryTicketDiscount.title"), defaultSort)),
                    listOf(6, 8, 1, 2, 3, 4, 5, 7, 9, 13, 14, 15)
                ),
                Arguments.of(
                    PageRequest.of(0, 15, Sort.by(Sort.Order.asc("discountCard.title"), defaultSort)),
                    listOf(7, 8, 1, 2, 3, 4, 5, 6, 9, 13, 14, 15)
                ),
                Arguments.of(
                    PageRequest.of(0, 15, Sort.by(Sort.Order.desc("discountCard.title"), defaultSort)),
                    listOf(8, 7, 1, 2, 3, 4, 5, 6, 9, 13, 14, 15)
                ),
                Arguments.of(
                    PageRequest.of(0, 15, Sort.by(Sort.Order.asc("card.title"), defaultSort)),
                    listOf(5, 6, 4, 1, 2, 7, 8, 9, 3, 13, 14, 15)
                ),
                Arguments.of(
                    PageRequest.of(0, 15, Sort.by(Sort.Order.desc("card.title"), defaultSort)),
                    listOf(7, 8, 3, 9, 5, 6, 1, 2, 4, 13, 14, 15)
                ),
                Arguments.of(
                    PageRequest.of(0, 15, Sort.by(Sort.Order.asc("seat.row"), defaultSort)),
                    listOf(1, 2, 3, 13, 14, 15, 4, 5, 6, 7, 8, 9)
                ),
                Arguments.of(
                    PageRequest.of(0, 15, Sort.by(Sort.Order.asc("seat.number"), defaultSort)),
                    listOf(1, 4, 7, 2, 5, 8, 3, 6, 13, 9, 14, 15)
                ),
                Arguments.of(
                    PageRequest.of(0, 15, Sort.by(Sort.Order.desc("seat.number"), defaultSort)),
                    listOf(15, 9, 14, 13, 3, 6, 2, 5, 8, 1, 4, 7)
                )
            )
        }
    }

    private fun Basket.addToBasket(
        originalId: Int,
        seatId: UUID,
        screeningId: UUID,
        priceCategoryItemNumber: PriceCategoryItemNumber,
        primaryTicketDiscountId: UUID? = null,
        secondaryTicketDiscountId: UUID? = null,
        primaryDiscountCardId: UUID? = null,
        secondaryDiscountCardId: UUID? = null,
        posConfigurationId: UUID? = null,
        isUsed: Boolean = false,
        ticketOriginalId: Int? = null,
        cardId: UUID? = null,
    ): BasketItem {
        val basketItem = basketItemService.createBasketItem(
            mapToCreateBasketItemCommand(
                request = createTicketBasketItemRequest(
                    seatId = seatId,
                    screeningId = screeningId,
                    priceCategoryItem = priceCategoryItemNumber,
                    primaryTicketDiscountId = primaryTicketDiscountId,
                    secondaryTicketDiscountId = secondaryTicketDiscountId,
                    primaryDiscountCardId = primaryDiscountCardId,
                    secondaryDiscountCardId = secondaryDiscountCardId
                ),
                basketId = this.id,
                originalId = originalId
            )
        )

        (primaryDiscountCardId ?: secondaryDiscountCardId)?.let {
            val discountCardUsage = discountCardUsageService.createDiscountCardUsage(
                CreateDiscountCardUsageCommand(
                    discountCardId = it,
                    screeningId = screeningId,
                    basketId = basketItem.basketId,
                    posConfigurationId = posConfigurationId!!
                )
            )
            discountCardUsageService.updateDiscountCardUsage(
                UpdateDiscountCardUsageCommand(
                    discountCardUsageId = discountCardUsage.id,
                    basketItemId = basketItem.id
                )
            )
        }

        if (isUsed) {
            ticketService.updateTicketOriginalId(
                UpdateTicketOriginalIdCommand(
                    ticketId = basketItem.ticketId!!,
                    originalId = ticketOriginalId!!
                )
            )
            ticketService.updateTicketUsed(
                UpdateTicketUsedCommand(originalId = ticketOriginalId, isUsed = true)
            )
        }

        cardId?.let {
            cardUsageRepository.save(
                CardUsage(
                    cardId = it,
                    basketId = basketItem.basketId,
                    basketItemId = basketItem.id,
                    originalId = 100,
                    discountPercentage = null
                )
            )
        }

        return basketItem
    }

    private fun Basket.addToOnlineBasket(
        originalId: Int,
        seatId: UUID,
        screeningId: UUID,
        reservationId: UUID,
        priceCategoryItemNumber: PriceCategoryItemNumber,
        primaryTicketDiscountId: UUID? = null,
        secondaryTicketDiscountId: UUID? = null,
        discountCardId: UUID? = null,
        isUsed: Boolean = false,
        includes3dGlasses: Boolean = false,
    ): BasketItem {
        val basketItem = basketItemService.createMssqlTicketBasketItem(
            CreateMssqlTicketBasketItemCommand(
                basketId = this.id,
                screeningId = screeningId,
                seatId = seatId,
                reservationId = reservationId,
                originalId = originalId,
                priceCategoryItemNumber = priceCategoryItemNumber,
                receiptNumber = "10000001$originalId",
                ticketDiscountPrimaryId = primaryTicketDiscountId,
                ticketDiscountSecondaryId = secondaryTicketDiscountId,
                discountCardId = discountCardId,
                isUsed = isUsed,
                includes3dGlasses = includes3dGlasses,
                branchId = BRANCH_1.id,
                basePrice = 0.toBigDecimal(),
                freeTicket = false,
                totalPrice = 0.toBigDecimal()
            )
        )
        basketItemRepository.save(basketItem.apply { this.originalId = originalId })

        discountCardId?.let {
            discountCardUsageService.createMssqlDiscountCardUsage(
                CreateMssqlDiscountCardUsageCommand(
                    discountCardId = it,
                    screeningId = screeningId,
                    basketId = basketItem.basketId,
                    ticketBasketItemId = basketItem.id
                )
            )
        }

        return basketItem
    }

    private fun Basket.addToMssqlTicketSalesBasket(
        originalId: Int,
        seatId: UUID,
        screeningId: UUID,
        priceCategoryItemNumber: PriceCategoryItemNumber,
        primaryTicketDiscountId: UUID? = null,
        secondaryTicketDiscountId: UUID? = null,
        primaryDiscountCardId: UUID? = null,
        branchId: UUID,
    ): BasketItem {
        return basketItemService.createMssqlTicketBasketItem(
            CreateMssqlTicketBasketItemCommand(
                basketId = this.id,
                screeningId = screeningId,
                seatId = seatId,
                reservationId = null,
                originalId = originalId,
                priceCategoryItemNumber = priceCategoryItemNumber,
                receiptNumber = "10000001$originalId",
                ticketDiscountPrimaryId = primaryTicketDiscountId,
                ticketDiscountSecondaryId = secondaryTicketDiscountId,
                discountCardId = primaryDiscountCardId,
                isUsed = true,
                includes3dGlasses = false,
                branchId = branchId,
                basePrice = 0.toBigDecimal(),
                freeTicket = false,
                totalPrice = 0.toBigDecimal()
            )
        )
    }

    private fun Basket.addProductToBasket(originalId: Int, productId: UUID) {
        basketItemService.createBasketItem(
            CreateBasketItemCommand(
                basketId = this.id,
                type = BasketItemType.PRODUCT,
                quantity = 1,
                productId = productId,
                originalId = originalId
            )
        )
    }

    private fun Basket.payBasket(
        paymentType: PaymentType,
        posConfigurationId: UUID,
    ) {
        basketService.initiateBasketPayment(
            InitiateBasketPaymentCommand(
                basketId = this.id,
                posConfigurationId = posConfigurationId,
                paymentType = paymentType
            )
        )
        basketReceiptNumbersService.generateBasketReceiptNumbers(
            GenerateBasketReceiptNumbersCommand(
                basketId = this.id
            )
        )
        this.paidAt = basketService.completeBasketPayment(
            CompleteBasketPaymentCommand(
                basketId = this.id
            )
        ).paidAt
    }

    private fun Basket.cancelBasketItem(originalId: Int, basketItemId: UUID) {
        val cancelledBasketItemIds = basketItemService.cancelBasketItems(
            command = CancelBasketItemsCommand(
                basketItemIds = setOf(basketItemId),
                printReceipt = false
            )
        )
        val cancelledBasketItem =
            basketItemRepository.findAllByBasketIdAndDeletedAtIsNullAndCancelledBasketItemIdIsNotNull(
                basketId = cancelledBasketItemIds.first()
            ).first()

        basketItemRepository.save(
            cancelledBasketItem.apply { this.originalId = originalId }
        )
    }

    private fun Basket.deleteBasketItem(basketItemId: UUID) {
        basketItemService.deleteBasketItem(
            command = DeleteBasketItemCommand(
                basketId = this.id,
                basketItemId = basketItemId
            )
        )
    }

    private fun Basket.createBasket() = basketRepository.save(this)
}

// baskets
private val BASKET_1 = createBasket(state = BasketState.OPEN, totalPrice = 0.toBigDecimal())
private val BASKET_2 = createBasket(state = BasketState.OPEN, totalPrice = 0.toBigDecimal())
private val BASKET_3 = createBasket(state = BasketState.OPEN, totalPrice = 0.toBigDecimal())
private val BASKET_4 = createBasket(state = BasketState.OPEN, totalPrice = 0.toBigDecimal())
private val BASKET_5 = createBasket(state = BasketState.OPEN, totalPrice = 0.toBigDecimal())
private val BASKET_6 = createBasket(state = BasketState.OPEN, totalPrice = 0.toBigDecimal())
private val BASKET_7 = createBasket(state = BasketState.OPEN, totalPrice = 0.toBigDecimal())
private val BASKET_8 = createBasket(
    state = BasketState.PAID_ONLINE,
    paymentType = PaymentType.CASHLESS,
    totalPrice = 10.toBigDecimal(),
    paidAt = LocalDateTime.now().plusHours(2),
    variableSymbol = "4293126007"
)
private val BASKET_9 = createBasket(
    state = BasketState.PAID,
    paymentType = PaymentType.CASHLESS,
    totalPrice = 12.toBigDecimal(),
    paidAt = LocalDateTime.now().plusHours(3)
)

// physical stuff - auditoriums, seats, POS's
private val BRANCH_1 = createBranch(name = "Bratislava Bory")
private val POS_CONFIGURATION_1 = createPosConfiguration(title = "pokl1")
private val POS_CONFIGURATION_2 = createPosConfiguration(title = "pokl2", macAddress = "EE:DD:CC:BB:AA")
private val AUDITORIUM_1 = createAuditorium(originalId = 1, title = "Sála IMAX", code = "IMAX")
private val AUDITORIUM_2 = createAuditorium(originalId = 2, title = "Sála A", code = "A")
private val AUDITORIUM_3 = createAuditorium(originalId = 3, title = "Sála B", code = "B")
private val AUDITORIUM_LAYOUT_1 = createAuditoriumLayout(originalId = 1, auditoriumId = AUDITORIUM_1.id)
private val AUDITORIUM_LAYOUT_2 = createAuditoriumLayout(originalId = 2, auditoriumId = AUDITORIUM_1.id)
private val AUDITORIUM_LAYOUT_3 = createAuditoriumLayout(originalId = 3, auditoriumId = AUDITORIUM_1.id)
private val SEAT_1 = createSeat(
    originalId = 1,
    auditoriumId = AUDITORIUM_1.id,
    auditoriumLayoutId = AUDITORIUM_LAYOUT_1.id,
    type = SeatType.REGULAR,
    row = "1",
    number = "1"
)
private val SEAT_2 = createSeat(
    originalId = 2,
    auditoriumId = AUDITORIUM_1.id,
    auditoriumLayoutId = AUDITORIUM_LAYOUT_1.id,
    type = SeatType.REGULAR,
    row = "1",
    number = "2"
)
private val SEAT_3 = createSeat(
    originalId = 3,
    auditoriumId = AUDITORIUM_1.id,
    auditoriumLayoutId = AUDITORIUM_LAYOUT_1.id,
    type = SeatType.PREMIUM_PLUS,
    row = "1",
    number = "3"
)
private val SEAT_4 = createSeat(
    originalId = 4,
    auditoriumId = AUDITORIUM_2.id,
    auditoriumLayoutId = AUDITORIUM_LAYOUT_2.id,
    type = SeatType.REGULAR,
    row = "2",
    number = "1"
)
private val SEAT_5 = createSeat(
    originalId = 5,
    auditoriumId = AUDITORIUM_2.id,
    auditoriumLayoutId = AUDITORIUM_LAYOUT_2.id,
    type = SeatType.REGULAR,
    row = "2",
    number = "2"
)
private val SEAT_6 = createSeat(
    originalId = 6,
    auditoriumId = AUDITORIUM_2.id,
    auditoriumLayoutId = AUDITORIUM_LAYOUT_2.id,
    type = SeatType.PREMIUM_PLUS,
    row = "2",
    number = "3"
)
private val SEAT_7 = createSeat(
    originalId = 7,
    auditoriumId = AUDITORIUM_3.id,
    auditoriumLayoutId = AUDITORIUM_LAYOUT_3.id,
    type = SeatType.PREMIUM_PLUS,
    row = "3",
    number = "1"
)
private val SEAT_8 = createSeat(
    originalId = 8,
    auditoriumId = AUDITORIUM_3.id,
    auditoriumLayoutId = AUDITORIUM_LAYOUT_3.id,
    type = SeatType.REGULAR,
    row = "3",
    number = "2"
)
private val SEAT_9 = createSeat(
    originalId = 9,
    auditoriumId = AUDITORIUM_2.id,
    auditoriumLayoutId = AUDITORIUM_LAYOUT_2.id,
    type = SeatType.VIP,
    row = "3",
    number = "5"
)
private val SEAT_10 = createSeat(
    originalId = 10,
    auditoriumId = AUDITORIUM_2.id,
    auditoriumLayoutId = AUDITORIUM_LAYOUT_2.id,
    type = SeatType.REGULAR,
    row = "3",
    number = "6"
)
private val SEAT_11 = createSeat(
    originalId = 11,
    auditoriumId = AUDITORIUM_1.id,
    auditoriumLayoutId = AUDITORIUM_LAYOUT_1.id,
    type = SeatType.REGULAR,
    row = "1",
    number = "4"
)
private val SEAT_12 = createSeat(
    originalId = 12,
    auditoriumId = AUDITORIUM_1.id,
    auditoriumLayoutId = AUDITORIUM_LAYOUT_1.id,
    type = SeatType.REGULAR,
    row = "1",
    number = "5"
)
private val SEAT_13 = createSeat(
    originalId = 13,
    auditoriumId = AUDITORIUM_1.id,
    auditoriumLayoutId = AUDITORIUM_LAYOUT_1.id,
    type = SeatType.REGULAR,
    row = "1",
    number = "6"
)

// movie stuff - distributor, production, technology, movie...
private val DISTRIBUTOR_1 = createDistributor(originalId = 1, title = "Best movies ever, s.r.o.")
private val DISTRIBUTOR_2 = createDistributor(originalId = 2, title = "Worst movies ever, a.s.")
private val PRODUCTION_1 = Production(originalId = 1, code = "01", title = "USA")
private val PRODUCTION_2 = Production(originalId = 2, code = "02", title = "Česká Republika")
private val TECHNOLOGY_1 = Technology(originalId = 1, code = "01", title = "IMAX")
private val TECHNOLOGY_2 = Technology(originalId = 2, code = "02", title = "DCI2D")
private val TECHNOLOGY_3 = Technology(originalId = 3, code = "03", title = "Analog")
private val MOVIE_1 = createMovie(
    originalId = 1,
    rawTitle = "Star Wars: Episode I – The Phantom Menace 2D (CT)",
    title = "Star Wars: Episode I – The Phantom Menace",
    distributorId = DISTRIBUTOR_1.id,
    productionId = PRODUCTION_1.id,
    technologyId = TECHNOLOGY_2.id
)
private val MOVIE_2 = createMovie(
    originalId = 2,
    rawTitle = "Guardians of the Galaxy IMAX 3D (ST)",
    title = "Guardians of the Galaxy",
    distributorId = DISTRIBUTOR_1.id,
    productionId = PRODUCTION_1.id,
    technologyId = TECHNOLOGY_1.id
)
private val MOVIE_3 = createMovie(
    originalId = 3,
    rawTitle = "Oppenheimer 35 (ST)",
    title = "Oppenheimer",
    distributorId = DISTRIBUTOR_2.id,
    productionId = PRODUCTION_1.id,
    technologyId = TECHNOLOGY_3.id
)
private val MOVIE_4 = createMovie(
    originalId = 4,
    rawTitle = "Pelíšky 2D",
    title = "Pelíšky",
    distributorId = DISTRIBUTOR_2.id,
    productionId = PRODUCTION_2.id,
    technologyId = TECHNOLOGY_2.id
)

// screenings and prices
private val PRICE_CATEGORY_1 = createPriceCategory(originalId = 1, title = "Do 17h")
private val PRICE_CATEGORY_2 = createPriceCategory(originalId = 2, title = "Po 17h")
private val PRICE_CATEGORY_ITEM_1 = createPriceCategoryItem(
    priceCategoryId = PRICE_CATEGORY_1.id,
    number = PriceCategoryItemNumber.PRICE_1,
    price = 5.toBigDecimal()
)
private val PRICE_CATEGORY_ITEM_2 = createPriceCategoryItem(
    priceCategoryId = PRICE_CATEGORY_1.id,
    number = PriceCategoryItemNumber.PRICE_2,
    price = 4.toBigDecimal()
)
private val PRICE_CATEGORY_ITEM_3 = createPriceCategoryItem(
    priceCategoryId = PRICE_CATEGORY_2.id,
    number = PriceCategoryItemNumber.PRICE_1,
    price = 7.toBigDecimal()
)
private val PRICE_CATEGORY_ITEM_4 = createPriceCategoryItem(
    priceCategoryId = PRICE_CATEGORY_2.id,
    number = PriceCategoryItemNumber.PRICE_2,
    price = 6.toBigDecimal(),
    discounted = true
)
private val PRICE_CATEGORY_ITEM_5 = createPriceCategoryItem(
    priceCategoryId = PRICE_CATEGORY_2.id,
    number = PriceCategoryItemNumber.PRICE_20,
    price = 4.5.toBigDecimal()
)
private val PRICE_CATEGORY_ITEM_6 = createPriceCategoryItem(
    priceCategoryId = PRICE_CATEGORY_2.id,
    number = PriceCategoryItemNumber.PRICE_19,
    price = 3.5.toBigDecimal(),
    discounted = true
)
private val SCREENING_1 = createScreening(
    originalId = 1,
    date = INTEGRATION_TEST_DATE_TIME.toLocalDate().plusDays(1),
    time = LocalTime.of(20, 0),
    auditoriumId = AUDITORIUM_1.id,
    auditoriumLayoutId = AUDITORIUM_LAYOUT_1.id,
    movieId = MOVIE_2.id,
    priceCategoryId = PRICE_CATEGORY_2.id
)
private val SCREENING_2 = createScreening(
    originalId = 2,
    date = INTEGRATION_TEST_DATE_TIME.toLocalDate().plusDays(1),
    time = LocalTime.of(18, 0),
    auditoriumId = AUDITORIUM_2.id,
    auditoriumLayoutId = AUDITORIUM_LAYOUT_2.id,
    movieId = MOVIE_1.id,
    priceCategoryId = PRICE_CATEGORY_1.id
)
private val SCREENING_3 = createScreening(
    originalId = 3,
    date = INTEGRATION_TEST_DATE_TIME.toLocalDate().plusDays(2),
    time = LocalTime.of(19, 0),
    auditoriumId = AUDITORIUM_3.id,
    auditoriumLayoutId = AUDITORIUM_LAYOUT_3.id,
    movieId = MOVIE_4.id,
    priceCategoryId = PRICE_CATEGORY_2.id
)
private val SCREENING_4 = createScreening(
    originalId = 4,
    date = INTEGRATION_TEST_DATE_TIME.toLocalDate().plusDays(2),
    time = LocalTime.of(18, 0),
    auditoriumId = AUDITORIUM_2.id,
    auditoriumLayoutId = AUDITORIUM_LAYOUT_2.id,
    movieId = MOVIE_3.id,
    priceCategoryId = PRICE_CATEGORY_1.id
)
private val SCREENING_FEE_1 = createScreeningFee(
    originalScreeningId = 1,
    screeningId = SCREENING_1.id,
    surchargeVip = 0.toBigDecimal(),
    surchargePremium = 0.toBigDecimal(),
    surchargeImax = 1.toBigDecimal(),
    surchargeUltraX = 0.toBigDecimal(),
    surchargeDBox = 0.toBigDecimal(),
    serviceFeeVip = 0.toBigDecimal(),
    serviceFeePremium = 1.toBigDecimal(),
    serviceFeeImax = 0.toBigDecimal(),
    serviceFeeUltraX = 0.toBigDecimal(),
    serviceFeeGeneral = 0.toBigDecimal()
)
private val SCREENING_FEE_2 = createScreeningFee(
    originalScreeningId = 2,
    screeningId = SCREENING_2.id,
    surchargeVip = 0.toBigDecimal(),
    surchargePremium = 0.toBigDecimal(),
    surchargeImax = 0.toBigDecimal(),
    surchargeUltraX = 0.toBigDecimal(),
    surchargeDBox = 0.toBigDecimal(),
    serviceFeeVip = 0.toBigDecimal(),
    serviceFeePremium = 0.toBigDecimal(),
    serviceFeeImax = 0.toBigDecimal(),
    serviceFeeUltraX = 0.toBigDecimal(),
    serviceFeeGeneral = 0.toBigDecimal()
)
private val SCREENING_FEE_3 = createScreeningFee(
    originalScreeningId = 3,
    screeningId = SCREENING_3.id,
    surchargeVip = 0.toBigDecimal(),
    surchargePremium = 1.toBigDecimal(),
    surchargeImax = 0.toBigDecimal(),
    surchargeUltraX = 0.toBigDecimal(),
    surchargeDBox = 0.toBigDecimal(),
    serviceFeeVip = 0.toBigDecimal(),
    serviceFeePremium = 0.toBigDecimal(),
    serviceFeeImax = 1.toBigDecimal(),
    serviceFeeUltraX = 0.toBigDecimal(),
    serviceFeeGeneral = 0.toBigDecimal()
)
private val SCREENING_FEE_4 = createScreeningFee(
    originalScreeningId = 4,
    screeningId = SCREENING_4.id,
    surchargeVip = 1.toBigDecimal(),
    surchargePremium = 0.toBigDecimal(),
    surchargeImax = 0.toBigDecimal(),
    surchargeUltraX = 0.toBigDecimal(),
    surchargeDBox = 0.toBigDecimal(),
    serviceFeeVip = 0.toBigDecimal(),
    serviceFeePremium = 0.toBigDecimal(),
    serviceFeeImax = 0.toBigDecimal(),
    serviceFeeUltraX = 0.toBigDecimal(),
    serviceFeeGeneral = 1.toBigDecimal()
)
private val SCREENING_TYPE_1 = createScreeningType(
    originalId = 1,
    title = "Festival"
)
private val SCREENING_TYPE_2 = createScreeningType(
    originalId = 2,
    title = "IMAX"
)
private val SCREENING_TYPE_3 = createScreeningType(
    originalId = 3,
    title = "Premiera"
)
private val ONLINE_RESERVATION_1 = createReservation(
    originalId = 100,
    screeningId = SCREENING_1.id,
    seatId = SEAT_11.id,
    state = ReservationState.UNAVAILABLE
)
private val ONLINE_RESERVATION_2 = createReservation(
    originalId = 101,
    screeningId = SCREENING_1.id,
    seatId = SEAT_12.id,
    state = ReservationState.UNAVAILABLE
)

// discounts and discount cards
private val TICKET_DISCOUNT_1 = createTicketDiscount(
    originalId = 1,
    code = "01",
    title = "Sleva 15%",
    type = TicketDiscountType.PERCENTAGE,
    percentage = 15,
    usageType = TicketDiscountUsageType.PRIMARY,
    freeCount = 1
)
private val TICKET_DISCOUNT_2 = createTicketDiscount(
    originalId = 2,
    code = "02",
    title = "Sleva 30%",
    type = TicketDiscountType.PERCENTAGE,
    percentage = 30,
    usageType = TicketDiscountUsageType.SECONDARY,
    freeCount = null
)
private val TICKET_DISCOUNT_3 = createTicketDiscount(
    originalId = 3,
    code = "03",
    title = "Sleva 1€",
    type = TicketDiscountType.ABSOLUTE,
    amount = 1.toBigDecimal(),
    usageType = TicketDiscountUsageType.PRIMARY,
    freeCount = null
)
private val TICKET_DISCOUNT_4 = createTicketDiscount(
    originalId = 4,
    code = "04",
    title = "Sleva 2€",
    type = TicketDiscountType.ABSOLUTE,
    amount = 2.toBigDecimal(),
    usageType = TicketDiscountUsageType.SECONDARY,
    freeCount = null
)
private val DISCOUNT_CARD_1 = createDiscountCard(
    originalId = 1,
    ticketDiscountId = TICKET_DISCOUNT_1.id,
    type = DiscountCardType.CARD,
    title = "FILM karta",
    code = "333333333"
)
private val DISCOUNT_CARD_2 = createDiscountCard(
    originalId = 2,
    ticketDiscountId = TICKET_DISCOUNT_3.id,
    title = "Slovak Telekom Voucher",
    code = "555555555"
)
private val CARD_1 = createCard()

// one product item just so we have this covered
private val PRODUCT_CATEGORY_1 = createProductCategory()
private val PRODUCT_COMPONENT_CATEGORY_1 = createProductComponentCategory()
private val PRODUCT_COMPONENT_1 = createProductComponent(
    title = "Kukurica",
    stockQuantity = 10.toBigDecimal(),
    productComponentCategoryId = PRODUCT_COMPONENT_CATEGORY_1.id
)
private val PRODUCT_1 = createProduct(productCategoryId = PRODUCT_CATEGORY_1.id, title = "Popcorn XXL")
private val PRODUCT_COMPOSITION_1 = createProductComposition(
    productId = PRODUCT_1.id,
    productComponentId = PRODUCT_COMPONENT_1.id
)
