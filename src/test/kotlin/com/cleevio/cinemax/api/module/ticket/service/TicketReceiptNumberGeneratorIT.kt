package com.cleevio.cinemax.api.module.ticket.service

import com.cleevio.cinemax.api.DatabaseCleanup
import com.cleevio.cinemax.api.IntegrationTest
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import kotlin.test.assertEquals

class TicketReceiptNumberGeneratorIT @Autowired constructor(
    private val underTest: TicketReceiptNumberGenerator,
) : IntegrationTest(DatabaseCleanup.AFTER_EACH) {

    @Test
    fun `test generateTicketReceiptNumber - should generate correctly`() {
        (1..150).forEach { index ->
            underTest.generateTicketReceiptNumber().also {
                if (index in (1..9)) assertEquals("100000000$index", it)
                if (index in (10..99)) assertEquals("10000000$index", it)
                if (index in (100..999)) assertEquals("1000000$index", it)
            }
        }
    }
}
