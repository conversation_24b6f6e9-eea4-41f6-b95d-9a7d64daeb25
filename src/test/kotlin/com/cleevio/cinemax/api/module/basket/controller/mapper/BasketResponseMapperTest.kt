package com.cleevio.cinemax.api.module.basket.controller.mapper

import com.cleevio.cinemax.api.common.util.isEqualTo
import com.cleevio.cinemax.api.common.util.toCents
import com.cleevio.cinemax.api.module.basket.constant.BasketState
import com.cleevio.cinemax.api.module.basketitem.constant.BasketItemType
import com.cleevio.cinemax.api.module.basketitem.service.BasketItemJooqFinderRepository
import com.cleevio.cinemax.api.module.card.entity.Card
import com.cleevio.cinemax.api.module.card.service.CardJpaFinderService
import com.cleevio.cinemax.api.module.cardusage.entity.CardUsage
import com.cleevio.cinemax.api.module.cardusage.service.CardUsageFinderService
import com.cleevio.cinemax.api.module.discountcard.constant.DiscountCardType
import com.cleevio.cinemax.api.module.pricecategoryitem.constant.PriceCategoryItemNumber
import com.cleevio.cinemax.api.module.product.service.ProductJooqFinderService
import com.cleevio.cinemax.api.module.reservation.service.ReservationJooqFinderService
import com.cleevio.cinemax.api.module.seat.service.SeatJooqFinderService
import com.cleevio.cinemax.api.module.ticket.service.TicketJooqFinderService
import com.cleevio.cinemax.api.module.ticketdiscount.service.TicketDiscountJooqFinderService
import com.cleevio.cinemax.api.module.ticketprice.service.TicketPriceJooqFinderService
import com.cleevio.cinemax.api.util.assertBasketEquals
import com.cleevio.cinemax.api.util.assertBasketItemEquals
import com.cleevio.cinemax.api.util.assertBasketResponseReservationEquals
import com.cleevio.cinemax.api.util.assertBasketResponseTicketPriceEquals
import com.cleevio.cinemax.api.util.createBasket
import com.cleevio.cinemax.api.util.createBasketItem
import com.cleevio.cinemax.api.util.createProduct
import com.cleevio.cinemax.api.util.createReservation
import com.cleevio.cinemax.api.util.createSeat
import com.cleevio.cinemax.api.util.createTicket
import com.cleevio.cinemax.api.util.createTicketPrice
import io.mockk.every
import io.mockk.mockk
import io.mockk.verifySequence
import org.junit.jupiter.api.Test
import java.math.BigDecimal
import java.util.UUID
import kotlin.test.assertEquals
import kotlin.test.assertNotNull
import kotlin.test.assertNull
import kotlin.test.assertTrue

class BasketResponseMapperTest {

    private val basketItemJooqFinderRepository = mockk<BasketItemJooqFinderRepository>()
    private val reservationJooqFinderService = mockk<ReservationJooqFinderService>()
    private val productFinderService = mockk<ProductJooqFinderService>()
    private val ticketPriceJooqFinderService = mockk<TicketPriceJooqFinderService>()
    private val ticketJooqFinderService = mockk<TicketJooqFinderService>()
    private val ticketDiscountJooqFinderService = mockk<TicketDiscountJooqFinderService>()
    private val seatJooqFinderService = mockk<SeatJooqFinderService>()
    private val cardJpaFinderService = mockk<CardJpaFinderService>()
    private val cardUsageFinderService = mockk<CardUsageFinderService>()

    private val basketResponseMapper = BasketResponseMapper(
        basketItemJooqFinderRepository,
        reservationJooqFinderService,
        productFinderService,
        ticketPriceJooqFinderService,
        ticketJooqFinderService,
        ticketDiscountJooqFinderService,
        seatJooqFinderService,
        cardJpaFinderService,
        cardUsageFinderService
    )

    @Test
    fun `test mapSingle, without cards, should map correctly`() {
        every { basketItemJooqFinderRepository.findAllNonDeletedByBasketId(any()) } returns listOf(
            BASKET_ITEM_1,
            BASKET_ITEM_2,
            BASKET_ITEM_3
        )
        every { ticketJooqFinderService.findAllByIdIn(any()) } returns listOf(TICKET_1, TICKET_2)
        every { reservationJooqFinderService.findAllNonDeletedByIdIn(any()) } returns listOf(RESERVATION_1, RESERVATION_2)
        every { seatJooqFinderService.findAllByIdIn(any()) } returns listOf(SEAT_1, SEAT_2)
        every { ticketPriceJooqFinderService.findAllByIdIn(any()) } returns listOf(
            TICKET_PRICE_SEAT_1_SCREENING_1,
            TICKET_PRICE_SEAT_2_SCREENING_1
        )
        every { ticketDiscountJooqFinderService.findAllNonDeletedByIdIn(any()) } returns listOf()
        every { productFinderService.findAllNonDeletedByIdIn(any()) } returns listOf(PRODUCT_1)
        every { cardUsageFinderService.findAllByBasketId(any()) } returns listOf()
        every { cardJpaFinderService.findAllByIdIn(any()) } returns listOf()

        val mappedBasket = basketResponseMapper.mapSingle(BASKET_1)

        assertBasketEquals(BASKET_1, mappedBasket)
        assertBasketItemEquals(BASKET_ITEM_1, mappedBasket.items[0])
        assertNotNull(mappedBasket.items[0].ticket)

        val basketItem1TicketPrice = mappedBasket.items[0].ticket?.ticketPrice
        assertNotNull(basketItem1TicketPrice)
        assertBasketResponseTicketPriceEquals(TICKET_PRICE_SEAT_1_SCREENING_1, basketItem1TicketPrice)
        val basketItem1Reservation = mappedBasket.items[0].ticket?.reservation
        assertNotNull(basketItem1Reservation)
        assertBasketResponseReservationEquals(RESERVATION_1, basketItem1Reservation)

        assertBasketItemEquals(BASKET_ITEM_2, mappedBasket.items[1])
        assertNotNull(mappedBasket.items[1].ticket)

        val basketItem2TicketPrice = mappedBasket.items[1].ticket?.ticketPrice
        assertNotNull(basketItem2TicketPrice)
        assertBasketResponseTicketPriceEquals(TICKET_PRICE_SEAT_2_SCREENING_1, basketItem2TicketPrice)
        val basketItem2Reservation = mappedBasket.items[1].ticket?.reservation
        assertNotNull(basketItem2Reservation)
        assertBasketResponseReservationEquals(RESERVATION_2, basketItem2Reservation)

        assertBasketItemEquals(BASKET_ITEM_3, mappedBasket.items[2])
        assertNull(mappedBasket.items[2].ticket)

        verifySequence {
            basketItemJooqFinderRepository.findAllNonDeletedByBasketId(BASKET_1.id)
            ticketJooqFinderService.findAllByIdIn(setOf(TICKET_1.id, TICKET_2.id))
            reservationJooqFinderService.findAllNonDeletedByIdIn(setOf(RESERVATION_1.id, RESERVATION_2.id))
            seatJooqFinderService.findAllByIdIn(setOf(SEAT_1.id, SEAT_2.id))
            ticketPriceJooqFinderService.findAllByIdIn(
                setOf(TICKET_PRICE_SEAT_1_SCREENING_1.id, TICKET_PRICE_SEAT_2_SCREENING_1.id)
            )
            productFinderService.findAllNonDeletedByIdIn(setOf(PRODUCT_1.id))
            cardUsageFinderService.findAllByBasketId(BASKET_1.id)
            cardJpaFinderService.findAllByIdIn(setOf())
        }
    }

    @Test
    fun `test mapSingle, with cards, should map cards to BasketCardResponse correctly`() {
        every { basketItemJooqFinderRepository.findAllNonDeletedByBasketId(any()) } returns listOf(
            BASKET_ITEM_1,
            BASKET_ITEM_2,
            BASKET_ITEM_3
        )
        every { ticketJooqFinderService.findAllByIdIn(any()) } returns listOf(TICKET_1, TICKET_2)
        every { reservationJooqFinderService.findAllNonDeletedByIdIn(any()) } returns listOf(RESERVATION_1, RESERVATION_2)
        every { seatJooqFinderService.findAllByIdIn(any()) } returns listOf(SEAT_1, SEAT_2)
        every { ticketPriceJooqFinderService.findAllByIdIn(any()) } returns listOf(
            TICKET_PRICE_SEAT_1_SCREENING_1,
            TICKET_PRICE_SEAT_2_SCREENING_1
        )
        every { ticketDiscountJooqFinderService.findAllNonDeletedByIdIn(any()) } returns listOf()
        every { productFinderService.findAllNonDeletedByIdIn(any()) } returns listOf(PRODUCT_1)
        every { cardUsageFinderService.findAllByBasketId(any()) } returns listOf(CARD_USAGE_1, CARD_USAGE_2)
        every { cardJpaFinderService.findAllByIdIn(any()) } returns listOf(CARD_1, CARD_2)

        val mappedBasket = basketResponseMapper.mapSingle(BASKET_WITH_CARDS)

        assertEquals(2, mappedBasket.cards.size)

        val card1Response = mappedBasket.cards.find { it.id == CARD_1.id }
        assertNotNull(card1Response)
        assertEquals(CARD_1.title, card1Response.title)

        val card2Response = mappedBasket.cards.find { it.id == CARD_2.id }
        assertNotNull(card2Response)
        assertEquals(CARD_2.title, card2Response.title)

        verifySequence {
            basketItemJooqFinderRepository.findAllNonDeletedByBasketId(BASKET_WITH_CARDS.id)
            ticketJooqFinderService.findAllByIdIn(setOf(TICKET_1.id, TICKET_2.id))
            reservationJooqFinderService.findAllNonDeletedByIdIn(setOf(RESERVATION_1.id, RESERVATION_2.id))
            seatJooqFinderService.findAllByIdIn(setOf(SEAT_1.id, SEAT_2.id))
            ticketPriceJooqFinderService.findAllByIdIn(
                setOf(TICKET_PRICE_SEAT_1_SCREENING_1.id, TICKET_PRICE_SEAT_2_SCREENING_1.id)
            )
            productFinderService.findAllNonDeletedByIdIn(setOf(PRODUCT_1.id))
            cardUsageFinderService.findAllByBasketId(BASKET_WITH_CARDS.id)
            cardJpaFinderService.findAllByIdIn(setOf(CARD_1.id, CARD_2.id))
        }
    }

    @Test
    fun `test mapSingle, with card usage, should map card usage to BasketItemCardDiscountResponse correctly`() {
        every { basketItemJooqFinderRepository.findAllNonDeletedByBasketId(any()) } returns listOf(
            BASKET_ITEM_WITH_DISCOUNT
        )
        every { ticketJooqFinderService.findAllByIdIn(any()) } returns listOf(TICKET_1)
        every { reservationJooqFinderService.findAllNonDeletedByIdIn(any()) } returns listOf(RESERVATION_1)
        every { seatJooqFinderService.findAllByIdIn(any()) } returns listOf(SEAT_1)
        every { ticketPriceJooqFinderService.findAllByIdIn(any()) } returns listOf(TICKET_PRICE_SEAT_1_SCREENING_1)
        every { ticketDiscountJooqFinderService.findAllNonDeletedByIdIn(any()) } returns listOf()
        every { productFinderService.findAllNonDeletedByIdIn(any()) } returns listOf()
        every { cardUsageFinderService.findAllByBasketId(any()) } returns listOf(CARD_USAGE_3)
        every { cardJpaFinderService.findAllByIdIn(any()) } returns listOf(CARD_1)

        val mappedBasket = basketResponseMapper.mapSingle(BASKET_1)

        assertEquals(1, mappedBasket.items.size)
        val basketItem = mappedBasket.items[0]

        val cardDiscount = basketItem.cardDiscount!!
        assertEquals(CARD_1.title, cardDiscount.cardTitle)
        assertTrue(2.1.toBigDecimal() isEqualTo cardDiscount.discountAmount.toCents())
        assertEquals(25, cardDiscount.discountPercentage)

        verifySequence {
            basketItemJooqFinderRepository.findAllNonDeletedByBasketId(BASKET_1.id)
            ticketJooqFinderService.findAllByIdIn(setOf(TICKET_1.id))
            reservationJooqFinderService.findAllNonDeletedByIdIn(setOf(RESERVATION_1.id))
            seatJooqFinderService.findAllByIdIn(setOf(SEAT_1.id))
            ticketPriceJooqFinderService.findAllByIdIn(setOf(TICKET_PRICE_SEAT_1_SCREENING_1.id))
            productFinderService.findAllNonDeletedByIdIn(setOf())
            cardUsageFinderService.findAllByBasketId(BASKET_1.id)
            cardJpaFinderService.findAllByIdIn(setOf(CARD_1.id))
        }
    }
}

private val AUDITORIUM_LAYOUT_ID = UUID.fromString("db9f7243-68df-479e-a6b7-72a159c46ab3")
private val AUDITORIUM_ID = UUID.fromString("22473e51-4e14-4c6b-ab5d-7fc9fee15b9b")
private val TABLE_1_ID = UUID.randomUUID()
private val SCREENING_1_ID = UUID.randomUUID()
private val SEAT_1 = createSeat(
    originalId = 1,
    auditoriumLayoutId = AUDITORIUM_LAYOUT_ID,
    auditoriumId = AUDITORIUM_ID
)
private val SEAT_2 = createSeat(
    originalId = 1,
    auditoriumLayoutId = AUDITORIUM_LAYOUT_ID,
    auditoriumId = AUDITORIUM_ID
)
private val BASKET_1 = createBasket(
    tableId = TABLE_1_ID,
    totalPrice = 12.0.toBigDecimal(),
    state = BasketState.OPEN
)
private val TICKET_PRICE_SEAT_1_SCREENING_1 = createTicketPrice(
    screeningId = SCREENING_1_ID,
    seatId = SEAT_1.id,
    basePrice = 8.4.toBigDecimal(),
    basePriceItemNumber = PriceCategoryItemNumber.PRICE_1,
    totalPrice = 8.4.toBigDecimal()
)
private val TICKET_PRICE_SEAT_2_SCREENING_1 = createTicketPrice(
    screeningId = SCREENING_1_ID,
    seatId = SEAT_2.id,
    basePrice = 4.2.toBigDecimal(),
    basePriceItemNumber = PriceCategoryItemNumber.PRICE_1,
    totalPrice = 4.2.toBigDecimal()
)
private val RESERVATION_1 = createReservation(
    screeningId = SCREENING_1_ID,
    seatId = SEAT_1.id
)
private val RESERVATION_2 = createReservation(
    screeningId = SCREENING_1_ID,
    seatId = SEAT_2.id
)
private val TICKET_1 = createTicket(
    screeningId = SCREENING_1_ID,
    reservationId = RESERVATION_1.id,
    ticketPriceId = TICKET_PRICE_SEAT_1_SCREENING_1.id
)
private val TICKET_2 = createTicket(
    screeningId = SCREENING_1_ID,
    reservationId = RESERVATION_2.id,
    ticketPriceId = TICKET_PRICE_SEAT_2_SCREENING_1.id
)
private val PRODUCT_1 = createProduct(
    productCategoryId = UUID.randomUUID(),
    title = "Popcorn XXL",
    price = BigDecimal.TEN
)
private val BASKET_ITEM_1 = createBasketItem(
    basketId = BASKET_1.id,
    ticketId = TICKET_1.id,
    type = BasketItemType.TICKET,
    price = 8.4.toBigDecimal(),
    quantity = 1
)
private val BASKET_ITEM_2 = createBasketItem(
    basketId = BASKET_1.id,
    ticketId = TICKET_2.id,
    type = BasketItemType.TICKET,
    price = 4.2.toBigDecimal(),
    quantity = 1
)
private val BASKET_ITEM_3 = createBasketItem(
    basketId = BASKET_1.id,
    productId = PRODUCT_1.id,
    type = BasketItemType.PRODUCT,
    price = 5.6.toBigDecimal(),
    quantity = 1
)
private val BASKET_ITEM_WITH_DISCOUNT = createBasketItem(
    basketId = BASKET_1.id,
    ticketId = TICKET_1.id,
    type = BasketItemType.TICKET,
    price = 6.3.toBigDecimal(), // discounted price
    quantity = 1,
    entityModifier = { it.originalPrice = 8.4.toBigDecimal() } // original price before discount
)
private val CARD_1 = Card(
    id = UUID.randomUUID(),
    type = DiscountCardType.CARD,
    title = "VIP Card",
    code = "VIP123",
    validFrom = null,
    validUntil = null
)
private val CARD_2 = Card(
    id = UUID.randomUUID(),
    type = DiscountCardType.CARD,
    title = "Student Card",
    code = "STU456",
    validFrom = null,
    validUntil = null
)
private val CARD_USAGE_1 = CardUsage(
    id = UUID.randomUUID(),
    cardId = CARD_1.id,
    basketId = BASKET_1.id,
    basketItemId = BASKET_ITEM_1.id,
    originalId = 1L,
    discountPercentage = 100
)
private val CARD_USAGE_2 = CardUsage(
    id = UUID.randomUUID(),
    cardId = CARD_2.id,
    basketId = BASKET_1.id,
    basketItemId = BASKET_ITEM_2.id,
    originalId = 2L,
    discountPercentage = 50
)
private val CARD_USAGE_3 = CardUsage(
    id = UUID.randomUUID(),
    cardId = CARD_1.id,
    basketId = BASKET_1.id,
    basketItemId = BASKET_ITEM_WITH_DISCOUNT.id,
    originalId = 3L,
    discountPercentage = 25
)
private val BASKET_WITH_CARDS = createBasket(
    tableId = TABLE_1_ID,
    totalPrice = 12.0.toBigDecimal(),
    state = BasketState.OPEN,
    entityModifier = { it.appliedCardIds = setOf(CARD_1.id, CARD_2.id) }
)
