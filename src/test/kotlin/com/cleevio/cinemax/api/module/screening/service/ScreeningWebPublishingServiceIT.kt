package com.cleevio.cinemax.api.module.screening.service

import com.cleevio.cinemax.api.DatabaseCleanup
import com.cleevio.cinemax.api.IntegrationTest
import com.cleevio.cinemax.api.common.integration.web.constant.CinemaCode
import com.cleevio.cinemax.api.common.integration.web.dto.PublishScreeningOnlineRequest
import com.cleevio.cinemax.api.common.integration.web.dto.PublishScreeningsOnlineResponse
import com.cleevio.cinemax.api.common.integration.web.dto.UnpublishScreeningOnlineRequest
import com.cleevio.cinemax.api.common.integration.web.dto.UnpublishScreeningsOnlineResponse
import com.cleevio.cinemax.api.module.auditorium.service.AuditoriumRepository
import com.cleevio.cinemax.api.module.auditoriumlayout.service.AuditoriumLayoutRepository
import com.cleevio.cinemax.api.module.distributor.service.DistributorRepository
import com.cleevio.cinemax.api.module.jso.entity.Jso
import com.cleevio.cinemax.api.module.jso.service.JsoRepository
import com.cleevio.cinemax.api.module.movie.service.MovieRepository
import com.cleevio.cinemax.api.module.moviejso.entity.MovieJso
import com.cleevio.cinemax.api.module.moviejso.service.MovieJsoRepository
import com.cleevio.cinemax.api.module.pricecategory.service.PriceCategoryRepository
import com.cleevio.cinemax.api.module.rating.entity.Rating
import com.cleevio.cinemax.api.module.rating.service.RatingRepository
import com.cleevio.cinemax.api.module.screening.constant.ScreeningState
import com.cleevio.cinemax.api.module.screening.exception.InvalidScreeningStateException
import com.cleevio.cinemax.api.module.screening.exception.PublishingScreeningsOnlineFailedException
import com.cleevio.cinemax.api.module.screening.exception.ScreeningNotFoundException
import com.cleevio.cinemax.api.module.screening.exception.UnpublishingScreeningsOnlineFailedException
import com.cleevio.cinemax.api.module.screening.service.command.PublishScreeningsOnlineCommand
import com.cleevio.cinemax.api.module.screening.service.command.UnpublishScreeningsOnlineCommand
import com.cleevio.cinemax.api.module.screeningtype.entity.ScreeningType
import com.cleevio.cinemax.api.module.screeningtype.service.ScreeningTypeRepository
import com.cleevio.cinemax.api.module.screeningtypes.entity.ScreeningTypes
import com.cleevio.cinemax.api.module.screeningtypes.service.ScreeningTypesRepository
import com.cleevio.cinemax.api.module.tmslanguage.entity.TmsLanguage
import com.cleevio.cinemax.api.module.tmslanguage.service.TmsLanguageRepository
import com.cleevio.cinemax.api.util.createAuditorium
import com.cleevio.cinemax.api.util.createAuditoriumLayout
import com.cleevio.cinemax.api.util.createDistributor
import com.cleevio.cinemax.api.util.createMovie
import com.cleevio.cinemax.api.util.createPriceCategory
import com.cleevio.cinemax.api.util.createScreening
import com.cleevio.cinemax.api.util.toUUID
import io.mockk.every
import io.mockk.verify
import org.junit.jupiter.api.BeforeAll
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import org.springframework.beans.factory.annotation.Autowired
import java.time.LocalDate
import java.time.LocalTime
import kotlin.test.assertEquals

class ScreeningWebPublishingServiceIT @Autowired constructor(
    private val underTest: ScreeningWebPublishingService,
    private val screeningRepository: ScreeningRepository,
    private val movieRepository: MovieRepository,
    private val ratingRepository: RatingRepository,
    private val tmsLanguageRepository: TmsLanguageRepository,
    private val movieJsoRepository: MovieJsoRepository,
    private val jsoRepository: JsoRepository,
    private val screeningTypeRepository: ScreeningTypeRepository,
    private val screeningTypesRepository: ScreeningTypesRepository,
    private val auditoriumRepository: AuditoriumRepository,
    private val auditoriumLayoutRepository: AuditoriumLayoutRepository,
    private val distributorRepository: DistributorRepository,
    private val priceCategoryRepository: PriceCategoryRepository,
) : IntegrationTest(DatabaseCleanup.AFTER_ALL) {

    @BeforeAll
    fun setup() {
        initAllData()
    }

    @Test
    fun `test publishScreeningsOnline - screeningId does not exists - should throw`() {
        assertThrows<ScreeningNotFoundException> {
            underTest.publishScreeningsOnline(PublishScreeningsOnlineCommand(setOf(SCREENING_1.id, 11.toUUID())))
        }
    }

    @Test
    fun `test unpublishScreeningsOnline - screeningId does not exists - should throw`() {
        assertThrows<ScreeningNotFoundException> {
            underTest.unpublishScreeningsOnline(UnpublishScreeningsOnlineCommand(setOf(SCREENING_1.id, 11.toUUID())))
        }
    }

    @Test
    fun `test publishScreeningsOnline - screening is not in published state - should throw`() {
        assertThrows<InvalidScreeningStateException> {
            underTest.publishScreeningsOnline(PublishScreeningsOnlineCommand(setOf(SCREENING_4.id)))
        }
    }

    @Test
    fun `test publishScreeningsOnline - screening with denied publishing online - should throw`() {
        assertThrows<InvalidScreeningStateException> {
            underTest.publishScreeningsOnline(PublishScreeningsOnlineCommand(setOf(SCREENING_3.id)))
        }
    }

    @Test
    fun `test publishScreeningsOnline - missing screening originalId - should throw`() {
        assertThrows<IllegalStateException> {
            underTest.publishScreeningsOnline(PublishScreeningsOnlineCommand(setOf(SCREENING_5.id)))
        }
    }

    @Test
    fun `test unpublishScreeningsOnline - missing screening originalId - should throw`() {
        assertThrows<IllegalStateException> {
            underTest.unpublishScreeningsOnline(UnpublishScreeningsOnlineCommand(setOf(SCREENING_5.id)))
        }
    }

    @Test
    fun `test publishScreeningsOnline - web connector fails with exception - should throw api exception`() {
        every { cinemaxWebConnectorMock.publishScreeningsOnline(any()) } throws IllegalStateException()

        val exception = assertThrows<PublishingScreeningsOnlineFailedException> {
            underTest.publishScreeningsOnline(PublishScreeningsOnlineCommand(setOf(SCREENING_1.id)))
        }
        assertEquals("Publishing of 1 screenings online failed.", exception.message)
    }

    @Test
    fun `test publishScreeningsOnline - not all screenings were published by connector - should throw with count failed`() {
        every { cinemaxWebConnectorMock.publishScreeningsOnline(any()) } returns
            PublishScreeningsOnlineResponse(imported = 1, errors = emptyList())

        val exception = assertThrows<PublishingScreeningsOnlineFailedException> {
            underTest.publishScreeningsOnline(PublishScreeningsOnlineCommand(setOf(SCREENING_1.id, SCREENING_2.id)))
        }
        assertEquals("Publishing of 1 screenings online failed.", exception.message)
    }

    @Test
    fun `test unpublishScreeningsOnline - web connector fails with exception - should throw api exception`() {
        every { cinemaxWebConnectorMock.unpublishScreeningsOnline(any()) } throws IllegalStateException()

        val exception = assertThrows<UnpublishingScreeningsOnlineFailedException> {
            underTest.unpublishScreeningsOnline(UnpublishScreeningsOnlineCommand(setOf(SCREENING_1.id)))
        }
        assertEquals("Unpublishing of 1 screenings online failed.", exception.message)
    }

    @Test
    fun `test unpublishScreeningsOnline - not all screenings were published by connector - should log warning`() {
        every { cinemaxWebConnectorMock.unpublishScreeningsOnline(any()) } returns
            UnpublishScreeningsOnlineResponse(deleted = 1, errors = emptyList())

        // Should not throw an exception, just log a warning
        underTest.unpublishScreeningsOnline(UnpublishScreeningsOnlineCommand(setOf(SCREENING_1.id, SCREENING_2.id)))
    }

    @Test
    fun `test publishScreeningsOnline - request with all values - should correctly create request to connector`() {
        every { cinemaxWebConnectorMock.publishScreeningsOnline(any()) } returns
            PublishScreeningsOnlineResponse(imported = 1, errors = emptyList())

        underTest.publishScreeningsOnline(PublishScreeningsOnlineCommand(setOf(SCREENING_1.id)))

        verify { cinemaxWebConnectorMock.publishScreeningsOnline(listOf(PUBLISH_SCREENING_ONLINE_REQUEST_1)) }
    }

    @Test
    fun `test publishScreeningsOnline, request with only required values - should correctly create request to connector`() {
        every { cinemaxWebConnectorMock.publishScreeningsOnline(any()) } returns
            PublishScreeningsOnlineResponse(imported = 1, errors = emptyList())

        underTest.publishScreeningsOnline(PublishScreeningsOnlineCommand(setOf(SCREENING_2.id)))

        verify { cinemaxWebConnectorMock.publishScreeningsOnline(listOf(PUBLISH_SCREENING_ONLINE_REQUEST_2)) }
    }

    @Test
    fun `test unpublishScreeningsOnline - valid command -  - should correctly create request to connector`() {
        every { cinemaxWebConnectorMock.unpublishScreeningsOnline(any()) } returns
            UnpublishScreeningsOnlineResponse(deleted = 1, errors = emptyList())

        underTest.unpublishScreeningsOnline(
            UnpublishScreeningsOnlineCommand(setOf(SCREENING_1.id))
        )

        verify {
            cinemaxWebConnectorMock.unpublishScreeningsOnline(
                listOf(
                    UnpublishScreeningOnlineRequest(
                        screeningOriginalId = SCREENING_1.originalId.toString(),
                        cinemaCode = CinemaCode.KINO_BRATISLAVA_BORY_MALL.webCinemaCode
                    )
                )
            )
        }
    }

    private fun initAllData() {
        distributorRepository.save(DISTRIBUTOR_1)
        ratingRepository.save(RATING)
        tmsLanguageRepository.save(TMS_LANGUAGE)
        movieRepository.saveAll(listOf(MOVIE_1, MOVIE_2))
        jsoRepository.saveAll(JSO_LIST)
        movieJsoRepository.saveAll(MOVIE_JSO_LIST)
        screeningTypeRepository.saveAll(SCREENING_TYPE_LIST)
        auditoriumRepository.saveAll(setOf(AUDITORIUM_1, AUDITORIUM_2))
        auditoriumLayoutRepository.saveAll(setOf(AUDITORIUM_LAYOUT_1, AUDITORIUM_LAYOUT_2))
        priceCategoryRepository.save(PRICE_CATEGORY_1)
        screeningRepository.saveAll(listOf(SCREENING_1, SCREENING_2, SCREENING_3, SCREENING_4, SCREENING_5))
        screeningTypesRepository.saveAll(SCREENING_TYPES_LIST)
    }
}

private val RATING = Rating(
    title = "PG-13",
    originalId = 1,
    code = "Rating-60"
)
private val TMS_LANGUAGE = TmsLanguage(
    title = "English",
    originalId = 1,
    code = "TmsLanguageCode-1"
)
private val JSO_1 = Jso(
    originalId = 101,
    code = "JSO_1",
    title = "jso-1-title"
)
private val JSO_2 = Jso(
    originalId = 102,
    code = "JSO_2",
    title = "jso-2-title"
)
private val JSO_LIST = listOf(JSO_1, JSO_2)
private val DISTRIBUTOR_1 = createDistributor()
private val MOVIE_1 = createMovie(
    originalId = 1,
    title = "Star Wars: Episode I IMAX 3D (ST)",
    distributorId = DISTRIBUTOR_1.id,
    duration = 60,
    ratingId = RATING.id,
    tmsLanguageId = TMS_LANGUAGE.id
)
private val MOVIE_2 = createMovie(
    originalId = 2,
    rawTitle = "Star Wars: Episode II IMAX 2D (ST)",
    distributorId = DISTRIBUTOR_1.id,
    duration = null,
    ratingId = null,
    tmsLanguageId = null
)
private val MOVIE_JSO_LIST = listOf(
    MovieJso(
        jsoId = JSO_1.id,
        movieId = MOVIE_1.id
    ),
    MovieJso(
        jsoId = JSO_2.id,
        movieId = MOVIE_1.id
    )
)
private val AUDITORIUM_1 = createAuditorium(
    originalId = 1,
    code = "A",
    title = "SÁLA A - CINEMAX BRATISLAVA"
)
private val AUDITORIUM_2 = createAuditorium(
    originalId = 2,
    code = "B",
    title = "SÁLA B - CINEMAX BRATISLAVA"
)
private val AUDITORIUM_LAYOUT_1 = createAuditoriumLayout(originalId = 1, auditoriumId = AUDITORIUM_1.id, code = "01")
private val AUDITORIUM_LAYOUT_2 = createAuditoriumLayout(originalId = 2, auditoriumId = AUDITORIUM_2.id, code = "01")
private val PRICE_CATEGORY_1 = createPriceCategory(
    originalId = 1,
    originalCode = null,
    title = "Po 17h"
)
private val SCREENING_1 = createScreening(
    originalId = 1,
    auditoriumId = AUDITORIUM_1.id,
    auditoriumLayoutId = AUDITORIUM_LAYOUT_1.id,
    movieId = MOVIE_1.id,
    priceCategoryId = PRICE_CATEGORY_1.id,
    proCommission = 3,
    filmFondCommission = 6,
    distributorCommission = 70,
    publishOnline = true,
    date = LocalDate.of(2024, 12, 24),
    time = LocalTime.of(20, 0),
    state = ScreeningState.PUBLISHED
)
private val SCREENING_2 = createScreening(
    originalId = 2,
    auditoriumId = AUDITORIUM_2.id,
    auditoriumLayoutId = AUDITORIUM_LAYOUT_2.id,
    movieId = MOVIE_2.id,
    priceCategoryId = PRICE_CATEGORY_1.id,
    publishOnline = true,
    date = LocalDate.of(2024, 12, 25),
    time = LocalTime.of(22, 0),
    state = ScreeningState.PUBLISHED
)
private val SCREENING_3 = createScreening(
    originalId = 3,
    auditoriumId = AUDITORIUM_1.id,
    auditoriumLayoutId = AUDITORIUM_LAYOUT_1.id,
    movieId = MOVIE_1.id,
    priceCategoryId = PRICE_CATEGORY_1.id,
    publishOnline = false,
    date = LocalDate.of(2024, 12, 25),
    time = LocalTime.of(22, 0),
    state = ScreeningState.PUBLISHED
)
private val SCREENING_4 = createScreening(
    originalId = 4,
    auditoriumId = AUDITORIUM_1.id,
    auditoriumLayoutId = AUDITORIUM_LAYOUT_1.id,
    movieId = MOVIE_1.id,
    priceCategoryId = PRICE_CATEGORY_1.id,
    publishOnline = true,
    date = LocalDate.of(2024, 12, 25),
    time = LocalTime.of(22, 0),
    state = ScreeningState.DRAFT
)
private val SCREENING_5 = createScreening(
    originalId = null,
    auditoriumId = AUDITORIUM_1.id,
    auditoriumLayoutId = AUDITORIUM_LAYOUT_1.id,
    movieId = MOVIE_1.id,
    priceCategoryId = PRICE_CATEGORY_1.id,
    publishOnline = true,
    date = LocalDate.of(2024, 12, 25),
    time = LocalTime.of(22, 0),
    state = ScreeningState.PUBLISHED
)
private val SCREENING_TYPE_LIST = listOf(
    ScreeningType(
        id = 1.toUUID(),
        title = "2D",
        originalId = 1,
        code = "ScreeningTypeCode-01"
    ),
    ScreeningType(
        id = 2.toUUID(),
        title = "3D",
        originalId = 2,
        code = "ScreeningTypeCode-02"
    )
)
private val SCREENING_TYPES_LIST = SCREENING_TYPE_LIST.map {
    ScreeningTypes(
        screeningId = SCREENING_1.id,
        screeningTypeId = it.id,
        blacklisted = false
    )
}
private val PUBLISH_SCREENING_ONLINE_REQUEST_1 = PublishScreeningOnlineRequest(
    movieTitle = MOVIE_1.rawTitle,
    auditoriumTitle = "Sála A",
    screeningDate = "24-12-2024",
    screeningTime = "20:00",
    screeningTypeTitle1 = "2D",
    screeningTypeTitle2 = "3D",
    screeningTypeTitle3 = "",
    screeningTypeTitle4 = "",
    ratingTitle = RATING.title,
    tmsLanguageTitle = TMS_LANGUAGE.title,
    duration = MOVIE_1.duration.toString(),
    screeningOriginalId = SCREENING_1.originalId.toString(),
    cinemaCode = CinemaCode.KINO_BRATISLAVA_BORY_MALL.webCinemaCode
    // jsoList = "JSO_1,JSO_2"
)

private val PUBLISH_SCREENING_ONLINE_REQUEST_2 = PublishScreeningOnlineRequest(
    movieTitle = MOVIE_2.rawTitle,
    auditoriumTitle = "Sála B",
    screeningDate = "25-12-2024",
    screeningTime = "22:00",
    screeningTypeTitle1 = "",
    screeningTypeTitle2 = "",
    screeningTypeTitle3 = "",
    screeningTypeTitle4 = "",
    ratingTitle = "",
    tmsLanguageTitle = "",
    duration = MOVIE_2.duration.toString(),
    screeningOriginalId = SCREENING_2.originalId.toString(),
    cinemaCode = CinemaCode.KINO_BRATISLAVA_BORY_MALL.webCinemaCode
    // jsoList = ""
)
