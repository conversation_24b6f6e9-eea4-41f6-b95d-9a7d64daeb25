package com.cleevio.cinemax.api.module.file.event

import com.cleevio.cinemax.api.common.integration.pubsub.constant.MessageType
import com.cleevio.cinemax.api.common.util.MESSAGING_MAPPER
import com.cleevio.cinemax.api.module.file.constant.FileType
import com.cleevio.cinemax.api.util.toUUID
import io.kotest.matchers.shouldBe
import io.kotest.matchers.shouldNotBe
import org.junit.jupiter.api.Test

class FileImageUploadedEventTest {

    @Test
    fun `test toMessagePayload - all event fields with values - should create message payload with correct type and serialization`() {
        val event = FileImageUploadedEvent(
            fileId = 1.toUUID(),
            type = FileType.PRODUCT_CATEGORY_IMAGE,
            originalName = "test-image.jpg",
            extension = "jpg",
            base64Content = "iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mP8/5+hHgAHggJ/PchI7wAAAABJRU5ErkJggg=="
        )

        val expectedJson = """
            {
              "fileId": "00000000-0000-0000-0000-000000000001",
              "type": "PRODUCT_CATEGORY_IMAGE",
              "originalName": "test-image.jpg",
              "extension": "jpg",
              "base64Content": "iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mP8/5+hHgAHggJ/PchI7wAAAABJRU5ErkJggg=="
            }
        """.trimIndent()

        val messagePayload = event.toMessagePayload()

        messagePayload.id shouldNotBe null
        messagePayload.createdAt shouldNotBe null
        messagePayload.type shouldBe MessageType.IMAGE_UPLOADED
        MESSAGING_MAPPER.readTree(messagePayload.data) shouldBe MESSAGING_MAPPER.readTree(expectedJson)
    }

    @Test
    fun `test toMessagePayload - required event fields all other are null - should create message payload with correct type and serialization`() {
        val event = FileImageUploadedEvent(
            fileId = 2.toUUID(),
            type = FileType.PRODUCT_IMAGE,
            originalName = null,
            extension = "png",
            base64Content = "iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mP8/5+hHgAHggJ/PchI7wAAAABJRU5ErkJggg=="
        )

        val expectedJson = """
            {
              "fileId": "00000000-0000-0000-0000-000000000002",
              "type": "PRODUCT_IMAGE",
              "originalName": null,
              "extension": "png",
              "base64Content": "iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mP8/5+hHgAHggJ/PchI7wAAAABJRU5ErkJggg=="
            }
        """.trimIndent()

        val messagePayload = event.toMessagePayload()

        messagePayload.id shouldNotBe null
        messagePayload.createdAt shouldNotBe null
        messagePayload.type shouldBe MessageType.IMAGE_UPLOADED
        MESSAGING_MAPPER.readTree(messagePayload.data) shouldBe MESSAGING_MAPPER.readTree(expectedJson)
    }
}
