package com.cleevio.cinemax.api.module.screening.service

import com.cleevio.cinemax.api.IntegrationTest
import com.cleevio.cinemax.api.common.util.toDateString
import com.cleevio.cinemax.api.module.screening.service.model.DistributorScreeningsXlsxExportMovieRecordModel
import com.cleevio.cinemax.api.module.screening.service.model.DistributorScreeningsXlsxExportRecordModel
import com.cleevio.cinemax.api.module.screening.service.model.DistributorScreeningsXlsxExportScreeningRecordModel
import org.apache.poi.xssf.usermodel.XSSFWorkbook
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import java.io.ByteArrayInputStream
import java.math.BigDecimal
import java.time.LocalDate
import kotlin.test.assertContains

class DistributorScreeningsXlsxExportResultMapperIT @Autowired constructor(
    private val underTest: DistributorScreeningsXlsxExportResultMapper,
) : IntegrationTest() {

    @Test
    fun `test mapToExportResultModel - aggregated all distributors - should create one sheet with all movies`() {
        val exportResult = underTest.mapToExportResultModel(
            dateFrom = FIXED_DATE,
            dateTo = FIXED_DATE.plusDays(7),
            data = createDistributorScreeningsExportRecordModels(listOf(null)),
            username = USERNAME,
            isDBoxExport = false
        )

        val byteArray = exportResult.inputStream.readAllBytes()
        assertTrue(byteArray.isNotEmpty())

        // open the generated Excel file from the byte array
        val workbook = XSSFWorkbook(ByteArrayInputStream(byteArray))
        assertEquals(1, workbook.numberOfSheets)
        val sheet = workbook.getSheet("Výkaz výsledkov")

        // Assert main header
        val mainHeaderRow1 = sheet.getRow(0).getCell(0).stringCellValue
        val mainHeaderRow2 = sheet.getRow(0).getCell(2).stringCellValue
        val mainHeaderRow3 = sheet.getRow(0).getCell(sheet.getRow(0).lastCellNum - 1).stringCellValue

        assertEquals("Kino: Bratislava Bory\nPoužívateľ: $USERNAME", mainHeaderRow1)
        assertEquals(
            "Výkaz výsledkov\nObdobie od: ${FIXED_DATE.toDateString()} do: ${
                FIXED_DATE.plusDays(7).toDateString()
            } ",
            mainHeaderRow2
        )
        assertContains(mainHeaderRow3, "Dátum: ${LocalDate.now().toDateString()}\nČas:")

        // FIRST TABLE ASSERTIONS

        // Assert table first headers
        sheet.getRow(6).let {
            assertEquals("Bratislava Bory", it.getCell(0).stringCellValue)
            assertEquals("št 1.8.2024", it.getCell(1).stringCellValue)
            assertEquals("pi 2.8.2024", it.getCell(5).stringCellValue)
            assertEquals("so 3.8.2024", it.getCell(9).stringCellValue)
            assertEquals("ne 4.8.2024", it.getCell(13).stringCellValue)
            assertEquals("spolu za št - ne", it.getCell(17).stringCellValue)
        }

        // Assert table second headers
        sheet.getRow(7).let {
            assertEquals("Názov filmu", it.getCell(0).stringCellValue)
            (0..4).forEach { columnIndex ->
                assertEquals("pr.", it.getCell(1 + columnIndex * 4).stringCellValue)
                assertEquals("div.", it.getCell(2 + columnIndex * 4).stringCellValue)
                assertEquals("TOTAL GBO", it.getCell(3 + columnIndex * 4).stringCellValue)
                assertEquals("NET BO", it.getCell(4 + columnIndex * 4).stringCellValue)
            }
            assertEquals("%", it.getCell(21).stringCellValue)
            assertEquals("Odvod", it.getCell(22).stringCellValue)
        }

        // Assert movie rows values
        listOf("Movie 1", "Movie 2").forEachIndexed { index, movieTitle ->
            val movieRow = sheet.getRow(8 + index)

            (1..4).forEachIndexed { columnIndex, day ->
                // movie title
                assertEquals(movieTitle, movieRow.getCell(0).stringCellValue)
                // pr.
                assertEquals(1, movieRow.getCell(1 + columnIndex * 4).numericCellValue.toInt())
                // div.
                assertEquals(day * (index + 1), movieRow.getCell(2 + columnIndex * 4).numericCellValue.toInt())
                // total GBO
                assertEquals(10 * day * (index + 1), movieRow.getCell(3 + columnIndex * 4).numericCellValue.toInt())
                // net BO
                assertEquals(8 * day * (index + 1), movieRow.getCell(4 + columnIndex * 4).numericCellValue.toInt())
            }
            // assert sums for four days
            // pr.
            assertEquals(4, movieRow.getCell(17).numericCellValue.toInt())
            // div.
            assertEquals((1 + index) * 10, movieRow.getCell(18).numericCellValue.toInt())
            // total GBO
            assertEquals((1 + index) * 100, movieRow.getCell(19).numericCellValue.toInt())
            // net BO
            assertEquals((1 + index) * 80, movieRow.getCell(20).numericCellValue.toInt())
            // distributor commission
            assertEquals((1 + index) * 20, movieRow.getCell(21).numericCellValue.toInt())
            // odvod
            assertEquals(if (index == 0) 16 else 64, movieRow.getCell(22).numericCellValue.toInt())
        }

        // Assert summary row values
        sheet.getRow(10).let { summaryRow ->
            (1..4).forEachIndexed { columnIndex, day ->
                // movie title
                assertEquals("Total", summaryRow.getCell(0).stringCellValue)
                // pr.
                assertEquals(2, summaryRow.getCell(1 + columnIndex * 4).numericCellValue.toInt())
                // div.
                assertEquals(day * 3, summaryRow.getCell(2 + columnIndex * 4).numericCellValue.toInt())
                // total GBO
                assertEquals(day * 30, summaryRow.getCell(3 + columnIndex * 4).numericCellValue.toInt())
                // net BO
                assertEquals(day * 24, summaryRow.getCell(4 + columnIndex * 4).numericCellValue.toInt())
            }
            // assert total of sums for four days
            // pr.
            assertEquals(8, summaryRow.getCell(17).numericCellValue.toInt())
            // div.
            assertEquals(30, summaryRow.getCell(18).numericCellValue.toInt())
            // total GBO
            assertEquals(300, summaryRow.getCell(19).numericCellValue.toInt())
            // net BO
            assertEquals(240, summaryRow.getCell(20).numericCellValue.toInt())
            // distributor commission
            assertEquals("", summaryRow.getCell(21).stringCellValue)
            // odvod
            assertEquals(80, summaryRow.getCell(22).numericCellValue.toInt())
        }

        // SECOND TABLE ASSERTIONS

        // Assert table first headers
        sheet.getRow(12).let {
            assertEquals("Bratislava Bory", it.getCell(0).stringCellValue)
            assertEquals("po 5.8.2024", it.getCell(1).stringCellValue)
            assertEquals("ut 6.8.2024", it.getCell(5).stringCellValue)
            assertEquals("st 7.8.2024", it.getCell(9).stringCellValue)
            assertEquals("spolu za po - streda", it.getCell(13).stringCellValue)
            assertEquals("spolu za týždeň", it.getCell(17).stringCellValue)
        }

        // Assert table second headers
        sheet.getRow(13).let {
            assertEquals("Názov filmu", it.getCell(0).stringCellValue)
            (0..4).forEach { columnIndex ->
                assertEquals("pr.", it.getCell(1 + columnIndex * 4).stringCellValue)
                assertEquals("div.", it.getCell(2 + columnIndex * 4).stringCellValue)
                assertEquals("TOTAL GBO", it.getCell(3 + columnIndex * 4).stringCellValue)
                assertEquals("NET BO", it.getCell(4 + columnIndex * 4).stringCellValue)
            }
            assertEquals("%", it.getCell(21).stringCellValue)
            assertEquals("Odvod", it.getCell(22).stringCellValue)
        }

        // Assert movie rows values
        listOf("Movie 1", "Movie 2").forEachIndexed { index, movieTitle ->
            val movieRow = sheet.getRow(14 + index)

            (5..7).forEachIndexed { columnIndex, day ->
                // movie title
                assertEquals(movieTitle, movieRow.getCell(0).stringCellValue)
                // pr.
                assertEquals(1, movieRow.getCell(1 + columnIndex * 4).numericCellValue.toInt())
                // div.
                assertEquals(day * (index + 1), movieRow.getCell(2 + columnIndex * 4).numericCellValue.toInt())
                // total GBO
                assertEquals(10 * day * (index + 1), movieRow.getCell(3 + columnIndex * 4).numericCellValue.toInt())
                // net BO
                assertEquals(8 * day * (index + 1), movieRow.getCell(4 + columnIndex * 4).numericCellValue.toInt())
            }
            // assert sums for three days
            // pr.
            assertEquals(3, movieRow.getCell(13).numericCellValue.toInt())
            // div.
            assertEquals((1 + index) * 18, movieRow.getCell(14).numericCellValue.toInt())
            // total GBO
            assertEquals((1 + index) * 180, movieRow.getCell(15).numericCellValue.toInt())
            // net BO
            assertEquals((1 + index) * 144, movieRow.getCell(16).numericCellValue.toInt())

            // assert sums for seven days
            // pr.
            assertEquals(7, movieRow.getCell(17).numericCellValue.toInt())
            // div.
            assertEquals((1 + index) * 28, movieRow.getCell(18).numericCellValue.toInt())
            // total GBO
            assertEquals((1 + index) * 280, movieRow.getCell(19).numericCellValue.toInt())
            // net BO
            assertEquals((1 + index) * 224, movieRow.getCell(20).numericCellValue.toInt())
            // distributor commission
            assertEquals((1 + index) * 20, movieRow.getCell(21).numericCellValue.toInt())
            // odvod
            assertEquals(if (index == 0) 44.8 else 179.2, movieRow.getCell(22).numericCellValue)
        }

        // Assert summary row values
        sheet.getRow(16).let { summaryRow ->
            (5..7).forEachIndexed { columnIndex, day ->
                // movie title
                assertEquals("Total", summaryRow.getCell(0).stringCellValue)
                // pr.
                assertEquals(2, summaryRow.getCell(1 + columnIndex * 4).numericCellValue.toInt())
                // div.
                assertEquals(day * 3, summaryRow.getCell(2 + columnIndex * 4).numericCellValue.toInt())
                // total GBO
                assertEquals(day * 30, summaryRow.getCell(3 + columnIndex * 4).numericCellValue.toInt())
                // net BO
                assertEquals(day * 24, summaryRow.getCell(4 + columnIndex * 4).numericCellValue.toInt())
            }
            // assert total of sums for three days
            // pr.
            assertEquals(6, summaryRow.getCell(13).numericCellValue.toInt())
            // div.
            assertEquals(54, summaryRow.getCell(14).numericCellValue.toInt())
            // total GBO
            assertEquals(540, summaryRow.getCell(15).numericCellValue.toInt())
            // net BO
            assertEquals(432, summaryRow.getCell(16).numericCellValue.toInt())

            // assert total of sums for seven days
            // pr.
            assertEquals(14, summaryRow.getCell(17).numericCellValue.toInt())
            // div.
            assertEquals(84, summaryRow.getCell(18).numericCellValue.toInt())
            // total GBO
            assertEquals(840, summaryRow.getCell(19).numericCellValue.toInt())
            // net BO
            assertEquals(672, summaryRow.getCell(20).numericCellValue.toInt())
            // distributor commission
            assertEquals("", summaryRow.getCell(21).stringCellValue)
            // odvod
            assertEquals(224, summaryRow.getCell(22).numericCellValue.toInt())
        }
    }

    @Test
    fun `test mapToExportResultModel - each data per one distributor - should create export sheet for each of them`() {
        val distributorTitle1 = "Distributor1"
        val distributorTitle2 = "Distributor2"
        val exportResult = underTest.mapToExportResultModel(
            dateFrom = FIXED_DATE,
            dateTo = FIXED_DATE.plusDays(7),
            data = createDistributorScreeningsExportRecordModels(listOf(distributorTitle1, distributorTitle2)),
            username = USERNAME,
            isDBoxExport = false
        )

        val byteArray = exportResult.inputStream.readAllBytes()
        assertTrue(byteArray.isNotEmpty())

        // open the generated Excel file from the byte array
        val workbook = XSSFWorkbook(ByteArrayInputStream(byteArray))
        val sheet1 = workbook.getSheet("Výkaz výsledkov - $distributorTitle1")
        val sheet2 = workbook.getSheet("Výkaz výsledkov - $distributorTitle2")

        assertEquals(2, workbook.numberOfSheets)
        assertEquals(distributorTitle1, sheet1.getRow(5).getCell(0).stringCellValue)
        assertEquals(distributorTitle2, sheet2.getRow(5).getCell(0).stringCellValue)
    }

    @Test
    fun `test mapToExportResultModel - isDBoxExport=true, aggregated all distributors - should create one sheet with all movies`() {
        val exportResult = underTest.mapToExportResultModel(
            dateFrom = FIXED_DATE,
            dateTo = FIXED_DATE.plusDays(7),
            data = createDistributorScreeningsExportRecordModels(listOf(null)),
            username = USERNAME,
            isDBoxExport = true
        )

        val byteArray = exportResult.inputStream.readAllBytes()
        assertTrue(byteArray.isNotEmpty())

        // open the generated Excel file from the byte array
        val workbook = XSSFWorkbook(ByteArrayInputStream(byteArray))
        assertEquals(1, workbook.numberOfSheets)
        val sheet = workbook.getSheet("Výkaz výsledkov DBOX")

        // Assert main header
        val mainHeaderRow1 = sheet.getRow(0).getCell(0).stringCellValue
        val mainHeaderRow2 = sheet.getRow(0).getCell(2).stringCellValue
        val mainHeaderRow3 = sheet.getRow(0).getCell(sheet.getRow(0).lastCellNum - 1).stringCellValue

        assertEquals("Kino: Bratislava Bory\nPoužívateľ: $USERNAME", mainHeaderRow1)
        assertEquals(
            "Výkaz výsledkov DBOX\nObdobie od: ${FIXED_DATE.toDateString()} do: ${
                FIXED_DATE.plusDays(7).toDateString()
            } ",
            mainHeaderRow2
        )
        assertContains(mainHeaderRow3, "Dátum: ${LocalDate.now().toDateString()}\nČas:")

        // FIRST TABLE ASSERTIONS

        // Assert table first headers
        sheet.getRow(6).let {
            assertEquals("Bratislava Bory", it.getCell(0).stringCellValue)
            assertEquals("št 1.8.2024", it.getCell(1).stringCellValue)
            assertEquals("pi 2.8.2024", it.getCell(5).stringCellValue)
            assertEquals("so 3.8.2024", it.getCell(9).stringCellValue)
            assertEquals("ne 4.8.2024", it.getCell(13).stringCellValue)
            assertEquals("spolu za št - ne", it.getCell(17).stringCellValue)
        }

        // Assert table second headers
        sheet.getRow(7).let {
            assertEquals("Názov filmu", it.getCell(0).stringCellValue)
            (0..4).forEach { columnIndex ->
                assertEquals("pr.", it.getCell(1 + columnIndex * 4).stringCellValue)
                assertEquals("div.", it.getCell(2 + columnIndex * 4).stringCellValue)
                assertEquals("TOTAL GBO", it.getCell(3 + columnIndex * 4).stringCellValue)
                assertEquals("NET BO", it.getCell(4 + columnIndex * 4).stringCellValue)
            }
            assertEquals("%", it.getCell(21).stringCellValue)
            assertEquals("Odvod", it.getCell(22).stringCellValue)
        }

        // Assert movie rows values
        listOf("Movie 1", "Movie 2").forEachIndexed { index, movieTitle ->
            val movieRow = sheet.getRow(8 + index)

            (1..4).forEachIndexed { columnIndex, day ->
                // movie title
                assertEquals(movieTitle, movieRow.getCell(0).stringCellValue)
                // pr.
                assertEquals(1, movieRow.getCell(1 + columnIndex * 4).numericCellValue.toInt())
                // div.
                assertEquals(day * (index + 1), movieRow.getCell(2 + columnIndex * 4).numericCellValue.toInt())
                // total GBO
                assertEquals(10 * day * (index + 1), movieRow.getCell(3 + columnIndex * 4).numericCellValue.toInt())
                // net BO
                assertEquals(8 * day * (index + 1), movieRow.getCell(4 + columnIndex * 4).numericCellValue.toInt())
            }
            // assert sums for four days
            // pr.
            assertEquals(4, movieRow.getCell(17).numericCellValue.toInt())
            // div.
            assertEquals((1 + index) * 10, movieRow.getCell(18).numericCellValue.toInt())
            // total GBO
            assertEquals((1 + index) * 100, movieRow.getCell(19).numericCellValue.toInt())
            // net BO
            assertEquals((1 + index) * 80, movieRow.getCell(20).numericCellValue.toInt())
            // distributor commission
            assertEquals((1 + index) * 20, movieRow.getCell(21).numericCellValue.toInt())
            // odvod
            assertEquals(if (index == 0) 16 else 64, movieRow.getCell(22).numericCellValue.toInt())
        }

        // Assert summary row values
        sheet.getRow(10).let { summaryRow ->
            (1..4).forEachIndexed { columnIndex, day ->
                // movie title
                assertEquals("Total", summaryRow.getCell(0).stringCellValue)
                // pr.
                assertEquals(2, summaryRow.getCell(1 + columnIndex * 4).numericCellValue.toInt())
                // div.
                assertEquals(day * 3, summaryRow.getCell(2 + columnIndex * 4).numericCellValue.toInt())
                // total GBO
                assertEquals(day * 30, summaryRow.getCell(3 + columnIndex * 4).numericCellValue.toInt())
                // net BO
                assertEquals(day * 24, summaryRow.getCell(4 + columnIndex * 4).numericCellValue.toInt())
            }
            // assert total of sums for four days
            // pr.
            assertEquals(8, summaryRow.getCell(17).numericCellValue.toInt())
            // div.
            assertEquals(30, summaryRow.getCell(18).numericCellValue.toInt())
            // total GBO
            assertEquals(300, summaryRow.getCell(19).numericCellValue.toInt())
            // net BO
            assertEquals(240, summaryRow.getCell(20).numericCellValue.toInt())
            // distributor commission
            assertEquals("", summaryRow.getCell(21).stringCellValue)
            // odvod
            assertEquals(80, summaryRow.getCell(22).numericCellValue.toInt())
        }

        // SECOND TABLE ASSERTIONS

        // Assert table first headers
        sheet.getRow(12).let {
            assertEquals("Bratislava Bory", it.getCell(0).stringCellValue)
            assertEquals("po 5.8.2024", it.getCell(1).stringCellValue)
            assertEquals("ut 6.8.2024", it.getCell(5).stringCellValue)
            assertEquals("st 7.8.2024", it.getCell(9).stringCellValue)
            assertEquals("spolu za po - streda", it.getCell(13).stringCellValue)
            assertEquals("spolu za týždeň", it.getCell(17).stringCellValue)
        }

        // Assert table second headers
        sheet.getRow(13).let {
            assertEquals("Názov filmu", it.getCell(0).stringCellValue)
            (0..4).forEach { columnIndex ->
                assertEquals("pr.", it.getCell(1 + columnIndex * 4).stringCellValue)
                assertEquals("div.", it.getCell(2 + columnIndex * 4).stringCellValue)
                assertEquals("TOTAL GBO", it.getCell(3 + columnIndex * 4).stringCellValue)
                assertEquals("NET BO", it.getCell(4 + columnIndex * 4).stringCellValue)
            }
            assertEquals("%", it.getCell(21).stringCellValue)
            assertEquals("Odvod", it.getCell(22).stringCellValue)
        }

        // Assert movie rows values
        listOf("Movie 1", "Movie 2").forEachIndexed { index, movieTitle ->
            val movieRow = sheet.getRow(14 + index)

            (5..7).forEachIndexed { columnIndex, day ->
                // movie title
                assertEquals(movieTitle, movieRow.getCell(0).stringCellValue)
                // pr.
                assertEquals(1, movieRow.getCell(1 + columnIndex * 4).numericCellValue.toInt())
                // div.
                assertEquals(day * (index + 1), movieRow.getCell(2 + columnIndex * 4).numericCellValue.toInt())
                // total GBO
                assertEquals(10 * day * (index + 1), movieRow.getCell(3 + columnIndex * 4).numericCellValue.toInt())
                // net BO
                assertEquals(8 * day * (index + 1), movieRow.getCell(4 + columnIndex * 4).numericCellValue.toInt())
            }
            // assert sums for three days
            // pr.
            assertEquals(3, movieRow.getCell(13).numericCellValue.toInt())
            // div.
            assertEquals((1 + index) * 18, movieRow.getCell(14).numericCellValue.toInt())
            // total GBO
            assertEquals((1 + index) * 180, movieRow.getCell(15).numericCellValue.toInt())
            // net BO
            assertEquals((1 + index) * 144, movieRow.getCell(16).numericCellValue.toInt())

            // assert sums for seven days
            // pr.
            assertEquals(7, movieRow.getCell(17).numericCellValue.toInt())
            // div.
            assertEquals((1 + index) * 28, movieRow.getCell(18).numericCellValue.toInt())
            // total GBO
            assertEquals((1 + index) * 280, movieRow.getCell(19).numericCellValue.toInt())
            // net BO
            assertEquals((1 + index) * 224, movieRow.getCell(20).numericCellValue.toInt())
            // distributor commission
            assertEquals((1 + index) * 20, movieRow.getCell(21).numericCellValue.toInt())
            // odvod
            assertEquals(if (index == 0) 44.8 else 179.2, movieRow.getCell(22).numericCellValue)
        }

        // Assert summary row values
        sheet.getRow(16).let { summaryRow ->
            (5..7).forEachIndexed { columnIndex, day ->
                // movie title
                assertEquals("Total", summaryRow.getCell(0).stringCellValue)
                // pr.
                assertEquals(2, summaryRow.getCell(1 + columnIndex * 4).numericCellValue.toInt())
                // div.
                assertEquals(day * 3, summaryRow.getCell(2 + columnIndex * 4).numericCellValue.toInt())
                // total GBO
                assertEquals(day * 30, summaryRow.getCell(3 + columnIndex * 4).numericCellValue.toInt())
                // net BO
                assertEquals(day * 24, summaryRow.getCell(4 + columnIndex * 4).numericCellValue.toInt())
            }
            // assert total of sums for three days
            // pr.
            assertEquals(6, summaryRow.getCell(13).numericCellValue.toInt())
            // div.
            assertEquals(54, summaryRow.getCell(14).numericCellValue.toInt())
            // total GBO
            assertEquals(540, summaryRow.getCell(15).numericCellValue.toInt())
            // net BO
            assertEquals(432, summaryRow.getCell(16).numericCellValue.toInt())

            // assert total of sums for seven days
            // pr.
            assertEquals(14, summaryRow.getCell(17).numericCellValue.toInt())
            // div.
            assertEquals(84, summaryRow.getCell(18).numericCellValue.toInt())
            // total GBO
            assertEquals(840, summaryRow.getCell(19).numericCellValue.toInt())
            // net BO
            assertEquals(672, summaryRow.getCell(20).numericCellValue.toInt())
            // distributor commission
            assertEquals("", summaryRow.getCell(21).stringCellValue)
            // odvod
            assertEquals(224, summaryRow.getCell(22).numericCellValue.toInt())
        }
    }

    private fun createDistributorScreeningsExportRecordModels(
        distributorTitles: List<String?>,
    ): List<DistributorScreeningsXlsxExportRecordModel> {
        return distributorTitles.map {
            DistributorScreeningsXlsxExportRecordModel(
                distributorTitle = it, // No distributor title for this case
                movies = listOf(
                    DistributorScreeningsXlsxExportMovieRecordModel(
                        movieTitle = "Movie 1",
                        screenings = (1..7).map { day ->
                            DistributorScreeningsXlsxExportScreeningRecordModel(
                                screeningDate = FIXED_DATE.plusDays(day.toLong() - 1),
                                ticketsCount = day,
                                groTicketSales = BigDecimal(10).times(day.toBigDecimal()),
                                netTicketsSales = BigDecimal(8).times(day.toBigDecimal()),
                                distributorCommission = 20
                            )
                        }
                    ),
                    DistributorScreeningsXlsxExportMovieRecordModel(
                        movieTitle = "Movie 2",
                        screenings = (1..7).map { day ->
                            DistributorScreeningsXlsxExportScreeningRecordModel(
                                screeningDate = FIXED_DATE.plusDays(day.toLong() - 1),
                                ticketsCount = day * 2,
                                groTicketSales = BigDecimal(20).times(day.toBigDecimal()),
                                netTicketsSales = BigDecimal(16).times(day.toBigDecimal()),
                                distributorCommission = 40
                            )
                        }
                    )
                )
            )
        }
    }
}

private const val USERNAME = "username"
private val FIXED_DATE = LocalDate.of(2024, 8, 1)
