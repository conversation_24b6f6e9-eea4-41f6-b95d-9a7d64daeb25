package com.cleevio.cinemax.api.module.screening.service

import com.cleevio.cinemax.api.DatabaseCleanup
import com.cleevio.cinemax.api.IntegrationDataTestHelper
import com.cleevio.cinemax.api.IntegrationTest
import com.cleevio.cinemax.api.common.constant.PaymentType
import com.cleevio.cinemax.api.module.basket.constant.BasketState
import com.cleevio.cinemax.api.module.basket.service.BasketRepository
import com.cleevio.cinemax.api.module.basketitem.constant.BasketItemType
import com.cleevio.cinemax.api.module.basketitem.service.BasketItemRepository
import com.cleevio.cinemax.api.module.branch.service.BranchRepository
import com.cleevio.cinemax.api.module.groupreservation.service.GroupReservationRepository
import com.cleevio.cinemax.api.module.posconfiguration.constant.PosConfigurationType
import com.cleevio.cinemax.api.module.pricecategoryitem.constant.PriceCategoryItemNumber
import com.cleevio.cinemax.api.module.productcomponent.constant.ProductComponentUnit
import com.cleevio.cinemax.api.module.reservation.event.ReservationCreatedEvent
import com.cleevio.cinemax.api.module.reservation.service.ReservationRepository
import com.cleevio.cinemax.api.module.screening.exception.ScreeningNotFoundException
import com.cleevio.cinemax.api.module.screening.service.command.WebCreateGroupReservationCommand
import com.cleevio.cinemax.api.module.screening.service.command.WebCreateGroupReservationSeatInput
import com.cleevio.cinemax.api.module.seat.exception.SeatNotFoundException
import com.cleevio.cinemax.api.module.ticket.service.TicketRepository
import com.cleevio.cinemax.api.util.createBranch
import com.cleevio.cinemax.api.util.toUUID
import io.kotest.assertions.throwables.shouldThrow
import io.kotest.matchers.date.shouldBeAfter
import io.kotest.matchers.shouldBe
import io.mockk.Runs
import io.mockk.every
import io.mockk.just
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import java.time.LocalDateTime

class WebCreateGroupReservationServiceIT @Autowired constructor(
    private val underTest: WebCreateGroupReservationService,
    private val groupReservationRepository: GroupReservationRepository,
    private val reservationRepository: ReservationRepository,
    private val basketRepository: BasketRepository,
    private val basketItemRepository: BasketItemRepository,
    private val ticketRepository: TicketRepository,
    private val integrationDataTestHelper: IntegrationDataTestHelper,
    private val branchRepository: BranchRepository,
) : IntegrationTest(DatabaseCleanup.AFTER_EACH) {

    @BeforeEach
    fun setUp() {
        branchRepository.save(createBranch())

        every {
            reservationMssqlFinderServiceMock.findByOriginalScreeningIdAndOriginalSeatId(
                any(),
                any()
            )
        } returns null

        every { applicationEventPublisherMock.publishEvent(any<ReservationCreatedEvent>()) } just Runs
        every { applicationEventPublisherMock.publishEvent(any()) } just Runs
    }

    @Test
    fun `test create group reservation - single ticket - should create group reservation, reservation, basket and ticket`() {
        every { groupReservationMssqlRepositoryMock.create(any(), any(), any()) } returns 1000

        val auditoriumLayout = integrationDataTestHelper.getAuditoriumLayout(
            auditoriumId = integrationDataTestHelper.getAuditorium(
                id = 1.toUUID()
            )
        )

        val seat1 = integrationDataTestHelper.getSeat(
            id = 10.toUUID(),
            originalId = 1,
            row = "1",
            number = "1",
            auditoriumId = auditoriumLayout.auditoriumId,
            auditoriumLayoutId = auditoriumLayout.id
        )
        integrationDataTestHelper.getSeat(
            id = 11.toUUID(),
            originalId = 11,
            row = "1",
            number = "2",
            auditoriumId = auditoriumLayout.auditoriumId,
            auditoriumLayoutId = auditoriumLayout.id
        )

        val screening = integrationDataTestHelper.getScreening(
            id = 100.toUUID(),
            originalId = 100,
            auditoriumId = auditoriumLayout.auditoriumId,
            auditoriumLayoutId = auditoriumLayout.id,
            priceCategoryId = integrationDataTestHelper.getPriceCategory(
                id = 1000.toUUID()
            ).id
        )
        integrationDataTestHelper.getScreeningFee(
            screeningId = screening.id,
            originalScreeningId = screening.originalId
        )
        integrationDataTestHelper.getPriceCategoryItem(
            priceCategoryId = screening.priceCategoryId
        )
        integrationDataTestHelper.getPosConfiguration(type = PosConfigurationType.ONLINE)

        val command = WebCreateGroupReservationCommand(
            screeningOriginalId = screening.originalId!!,
            name = "Test Group Reservation",
            expiresInSeconds = 600,
            seats = listOf(
                WebCreateGroupReservationSeatInput(
                    seatOriginalId = seat1.originalId!!,
                    priceCategoryItemNumber = PriceCategoryItemNumber.PRICE_1,
                    discountCardCode = null,
                    includes3dGlasses = false
                )
            )
        )

        underTest(command)

        val groupReservations = groupReservationRepository.findAll()
        groupReservations.size shouldBe 1
        groupReservations[0].name shouldBe "Test Group Reservation"
        groupReservations[0].originalId shouldBe 1000
        groupReservations[0].expiresAt?.shouldBeAfter(LocalDateTime.now().plusSeconds(599))

        val reservations = reservationRepository.findAll()
        reservations.size shouldBe 1
        reservations[0].screeningId shouldBe screening.id
        reservations[0].seatId shouldBe seat1.id
        reservations[0].groupReservationId shouldBe groupReservations[0].id

        val basket = basketRepository.findAll()
        basket.size shouldBe 1
        basket[0].state shouldBe BasketState.OPEN_ONLINE
        basket[0].paymentType shouldBe PaymentType.CASHLESS
        basket[0].totalPrice shouldBe 14.2.toBigDecimal()

        val basketItems = basketItemRepository.findAll()
        basketItems.size shouldBe 1
        basketItems[0].quantity shouldBe 1
        basketItems[0].price shouldBe 14.2.toBigDecimal()

        val tickets = ticketRepository.findAll()
        tickets.size shouldBe 1
        tickets.first().run {
            this.isGroupTicket shouldBe false
            this.screeningId shouldBe screening.id
        }
    }

    // @Disabled("We mock applicationEventPublisher, so the event is not published.")
    @Test
    fun `test create group reservation - ticket with 3D glasses - should create all entities`() {
        every { groupReservationMssqlRepositoryMock.create(any(), any(), any()) } returns 1000

        val auditoriumLayout = integrationDataTestHelper.getAuditoriumLayout(
            auditoriumId = integrationDataTestHelper.getAuditorium(
                id = 1.toUUID()
            )
        )

        val seat1 = integrationDataTestHelper.getSeat(
            id = 10.toUUID(),
            originalId = 1,
            row = "1",
            number = "1",
            auditoriumId = auditoriumLayout.auditoriumId,
            auditoriumLayoutId = auditoriumLayout.id
        )
        integrationDataTestHelper.getSeat(
            id = 11.toUUID(),
            originalId = 11,
            row = "1",
            number = "2",
            auditoriumId = auditoriumLayout.auditoriumId,
            auditoriumLayoutId = auditoriumLayout.id
        )

        val screening = integrationDataTestHelper.getScreening(
            id = 100.toUUID(),
            originalId = 100,
            auditoriumId = auditoriumLayout.auditoriumId,
            auditoriumLayoutId = auditoriumLayout.id,
            priceCategoryId = integrationDataTestHelper.getPriceCategory(
                id = 1000.toUUID()
            ).id
        )
        integrationDataTestHelper.getScreeningFee(
            screeningId = screening.id,
            originalScreeningId = screening.originalId
        )
        integrationDataTestHelper.getPriceCategoryItem(
            priceCategoryId = screening.priceCategoryId
        )
        integrationDataTestHelper.getPosConfiguration(type = PosConfigurationType.ONLINE)

        val productComponentCategory = integrationDataTestHelper.getProductComponentCategory(
            id = 1.toUUID(),
            title = "Okuliare"
        )
        val productComponent = integrationDataTestHelper.getProductComponent(
            originalId = 1,
            code = "05",
            title = "3D okuliare",
            unit = ProductComponentUnit.KS,
            stockQuantity = 100.toBigDecimal(),
            productComponentCategoryId = productComponentCategory.id
        )
        val productCategory = integrationDataTestHelper.getProductCategory(
            id = 1.toUUID(),
            title = "Okuliare",
            code = "36"
        )
        val product = integrationDataTestHelper.getProduct(
            id = 1.toUUID(),
            title = "3D Okuliare",
            price = 2.20.toBigDecimal(),
            productCategoryId = productCategory.id
        )
        integrationDataTestHelper.getProductComposition(
            id = 1.toUUID(),
            productId = product.id,
            productComponentId = productComponent.id,
            amount = 1.toBigDecimal()
        )

        val command = WebCreateGroupReservationCommand(
            screeningOriginalId = screening.originalId!!,
            name = "Test Group Reservation",
            expiresInSeconds = 600,
            seats = listOf(
                WebCreateGroupReservationSeatInput(
                    seatOriginalId = seat1.originalId!!,
                    priceCategoryItemNumber = PriceCategoryItemNumber.PRICE_1,
                    discountCardCode = null,
                    includes3dGlasses = true
                )
            )
        )

        underTest(command)

        val groupReservations = groupReservationRepository.findAll()
        groupReservations.size shouldBe 1
        groupReservations[0].name shouldBe "Test Group Reservation"
        groupReservations[0].originalId shouldBe 1000
        groupReservations[0].expiresAt?.shouldBeAfter(LocalDateTime.now().plusSeconds(599))

        val reservations = reservationRepository.findAll()
        reservations.size shouldBe 1
        reservations[0].screeningId shouldBe screening.id
        reservations[0].seatId shouldBe seat1.id
        reservations[0].groupReservationId shouldBe groupReservations[0].id

        val basket = basketRepository.findAll()
        basket.size shouldBe 1
        basket[0].state shouldBe BasketState.OPEN_ONLINE
        basket[0].paymentType shouldBe PaymentType.CASHLESS
        // basket[0].totalPrice shouldBe 16.4.toBigDecimal()
        basket[0].totalPrice shouldBe 14.2.toBigDecimal()

        val basketItems = basketItemRepository.findAll().sortedBy { it.type }
        // basketItems.size shouldBe 2
        basketItems.size shouldBe 1

        // ticket
        basketItems[0].quantity shouldBe 1
        basketItems[0].price shouldBe 14.2.toBigDecimal()
        basketItems[0].type shouldBe BasketItemType.TICKET
        basketItems[0].productId shouldBe null

        // glasses
        // basketItems[1].quantity shouldBe 1
        // basketItems[1].price shouldBe 2.2.toBigDecimal()
        // basketItems[1].type shouldBe BasketItemType.PRODUCT
        // basketItems[1].productId shouldBe product.id

        val tickets = ticketRepository.findAll()
        tickets.size shouldBe 1
        tickets.first().run {
            this.includes3dGlasses shouldBe true
            this.isGroupTicket shouldBe false
            this.screeningId shouldBe screening.id
        }
    }

    @Test
    fun `test create group reservation with multiple seats - should create group reservation, reservations`() {
        every { groupReservationMssqlRepositoryMock.create(any(), any(), any()) } returns 1000

        val auditoriumLayout = integrationDataTestHelper.getAuditoriumLayout(
            auditoriumId = integrationDataTestHelper.getAuditorium(
                id = 2.toUUID()
            )
        )

        val seat1 = integrationDataTestHelper.getSeat(
            id = 20.toUUID(),
            originalId = 20,
            row = "2",
            number = "1",
            auditoriumId = auditoriumLayout.auditoriumId,
            auditoriumLayoutId = auditoriumLayout.id
        )
        val seat2 = integrationDataTestHelper.getSeat(
            id = 21.toUUID(),
            originalId = 21,
            row = "2",
            number = "2",
            auditoriumId = auditoriumLayout.auditoriumId,
            auditoriumLayoutId = auditoriumLayout.id
        )
        val seat3 = integrationDataTestHelper.getSeat(
            id = 22.toUUID(),
            originalId = 22,
            row = "2",
            number = "3",
            auditoriumId = auditoriumLayout.auditoriumId,
            auditoriumLayoutId = auditoriumLayout.id
        )

        val screening = integrationDataTestHelper.getScreening(
            id = 200.toUUID(),
            originalId = 200,
            auditoriumId = auditoriumLayout.auditoriumId,
            auditoriumLayoutId = auditoriumLayout.id,
            priceCategoryId = integrationDataTestHelper.getPriceCategory(
                id = 2000.toUUID()
            ).id
        )
        integrationDataTestHelper.getScreeningFee(
            screeningId = screening.id,
            originalScreeningId = screening.originalId
        )
        integrationDataTestHelper.getPriceCategoryItem(
            priceCategoryId = screening.priceCategoryId
        )

        integrationDataTestHelper.getPosConfiguration(type = PosConfigurationType.ONLINE)

        val command = WebCreateGroupReservationCommand(
            screeningOriginalId = screening.originalId!!,
            name = "Test Multiple Seats Group Reservation",
            expiresInSeconds = 600,
            seats = listOf(
                WebCreateGroupReservationSeatInput(
                    seatOriginalId = seat1.originalId!!,
                    priceCategoryItemNumber = PriceCategoryItemNumber.PRICE_1,
                    discountCardCode = null,
                    includes3dGlasses = false
                ),
                WebCreateGroupReservationSeatInput(
                    seatOriginalId = seat2.originalId!!,
                    priceCategoryItemNumber = PriceCategoryItemNumber.PRICE_1,
                    discountCardCode = null,
                    includes3dGlasses = false
                ),
                WebCreateGroupReservationSeatInput(
                    seatOriginalId = seat3.originalId!!,
                    priceCategoryItemNumber = PriceCategoryItemNumber.PRICE_1,
                    discountCardCode = null,
                    includes3dGlasses = false
                )
            )
        )

        underTest(command)

        val groupReservations = groupReservationRepository.findAll()
        groupReservations.size shouldBe 1
        groupReservations[0].name shouldBe "Test Multiple Seats Group Reservation"
        groupReservations[0].originalId shouldBe 1000

        val reservations = reservationRepository.findAll()
        reservations.size shouldBe 3

        reservations.forEach { reservation ->
            reservation.screeningId shouldBe screening.id
            reservation.groupReservationId shouldBe groupReservations[0].id
        }

        val seatIds = setOf(seat1.id, seat2.id, seat3.id)
        val reservedSeatIds = reservations.map { it.seatId }.toSet()
        reservedSeatIds shouldBe seatIds

        val basket = basketRepository.findAll()
        basket.size shouldBe 1
        basket[0].state shouldBe BasketState.OPEN_ONLINE
        basket[0].paymentType shouldBe PaymentType.CASHLESS

        val basketItems = basketItemRepository.findAll()
        basketItems.size shouldBe 3
        basketItems.forEach { basketItem ->
            basketItem.quantity shouldBe 1
            basketItem.price shouldBe 14.2.toBigDecimal()
        }

        val tickets = ticketRepository.findAll()
        tickets.size shouldBe 3
        tickets.forEach { ticket ->
            ticket.isGroupTicket shouldBe false
            ticket.screeningId shouldBe screening.id
        }
    }

    @Test
    fun `test create group reservation with invalid screening - should throw exception`() {
        val auditoriumLayout = integrationDataTestHelper.getAuditoriumLayout(
            auditoriumId = integrationDataTestHelper.getAuditorium(
                id = 2.toUUID()
            )
        )

        val seat1 = integrationDataTestHelper.getSeat(
            id = 20.toUUID(),
            originalId = 20,
            row = "2",
            number = "1",
            auditoriumId = auditoriumLayout.auditoriumId,
            auditoriumLayoutId = auditoriumLayout.id
        )

        val command = WebCreateGroupReservationCommand(
            screeningOriginalId = 999, // Non-existent screening
            name = "Test Group Reservation",
            expiresInSeconds = 600,
            seats = listOf(
                WebCreateGroupReservationSeatInput(
                    seatOriginalId = seat1.originalId!!,
                    priceCategoryItemNumber = PriceCategoryItemNumber.PRICE_1,
                    discountCardCode = null,
                    includes3dGlasses = false
                )
            )
        )

        shouldThrow<ScreeningNotFoundException> {
            underTest(command)
        }
    }

    @Test
    fun `test create group reservation with invalid seat - should throw exception`() {
        every { groupReservationMssqlRepositoryMock.create(any(), any(), any()) } returns 1000

        val auditoriumLayout = integrationDataTestHelper.getAuditoriumLayout(
            auditoriumId = integrationDataTestHelper.getAuditorium(
                id = 2.toUUID()
            )
        )

        val screening = integrationDataTestHelper.getScreening(
            id = 200.toUUID(),
            originalId = 200,
            auditoriumId = auditoriumLayout.auditoriumId,
            auditoriumLayoutId = auditoriumLayout.id,
            priceCategoryId = integrationDataTestHelper.getPriceCategory(
                id = 2000.toUUID()
            ).id
        )
        integrationDataTestHelper.getScreeningFee(
            screeningId = screening.id,
            originalScreeningId = screening.originalId
        )
        integrationDataTestHelper.getPriceCategoryItem(
            priceCategoryId = screening.priceCategoryId
        )

        val command = WebCreateGroupReservationCommand(
            screeningOriginalId = screening.originalId!!,
            name = "Test Group Reservation",
            expiresInSeconds = 600,
            seats = listOf(
                WebCreateGroupReservationSeatInput(
                    seatOriginalId = 999, // Non-existent seat
                    priceCategoryItemNumber = PriceCategoryItemNumber.PRICE_1,
                    discountCardCode = null,
                    includes3dGlasses = false
                )
            )
        )

        shouldThrow<SeatNotFoundException> {
            underTest(command)
        }
    }

    @Test
    fun `test create group reservation with seat from other auditorium - should throw exception`() {
        val auditoriumLayout = integrationDataTestHelper.getAuditoriumLayout(
            originalId = 100,
            auditoriumId = integrationDataTestHelper.getAuditorium(
                id = 1.toUUID()
            )
        )

        val auditoriumLayoutOther = integrationDataTestHelper.getAuditoriumLayout(
            originalId = 999,
            auditoriumId = integrationDataTestHelper.getAuditorium(
                id = 999.toUUID()
            )
        )

        val screening = integrationDataTestHelper.getScreening(
            id = 200.toUUID(),
            originalId = 200,
            auditoriumId = auditoriumLayout.auditoriumId,
            auditoriumLayoutId = auditoriumLayout.id,
            priceCategoryId = integrationDataTestHelper.getPriceCategory(
                id = 2000.toUUID()
            ).id
        )
        integrationDataTestHelper.getScreeningFee(
            screeningId = screening.id,
            originalScreeningId = screening.originalId
        )
        integrationDataTestHelper.getPriceCategoryItem(
            priceCategoryId = screening.priceCategoryId
        )

        val seat1 = integrationDataTestHelper.getSeat(
            id = 20.toUUID(),
            originalId = 20,
            row = "2",
            number = "1",
            auditoriumId = auditoriumLayoutOther.auditoriumId,
            auditoriumLayoutId = auditoriumLayoutOther.id
        )

        val command = WebCreateGroupReservationCommand(
            screeningOriginalId = screening.originalId!!,
            name = "Test Group Reservation",
            expiresInSeconds = 600,
            seats = listOf(
                WebCreateGroupReservationSeatInput(
                    seatOriginalId = seat1.originalId!!, // seat from other auditorium
                    priceCategoryItemNumber = PriceCategoryItemNumber.PRICE_1,
                    discountCardCode = null,
                    includes3dGlasses = false
                )
            )
        )

        shouldThrow<SeatNotFoundException> {
            underTest(command)
        }
    }
}
