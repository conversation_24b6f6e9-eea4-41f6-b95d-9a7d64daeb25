package com.cleevio.cinemax.api.module.moviejso.service

import com.cleevio.cinemax.api.DatabaseCleanup
import com.cleevio.cinemax.api.IntegrationTest
import com.cleevio.cinemax.api.module.distributor.service.DistributorService
import com.cleevio.cinemax.api.module.jso.exception.JsoNotFoundException
import com.cleevio.cinemax.api.module.jso.service.JsoJooqFinderService
import com.cleevio.cinemax.api.module.movie.exception.MovieNotFoundException
import com.cleevio.cinemax.api.module.movie.service.MovieService
import com.cleevio.cinemax.api.module.moviejso.service.command.DeleteAndCreateMovieJsosCommand
import com.cleevio.cinemax.api.util.createDistributor
import com.cleevio.cinemax.api.util.createMovie
import com.cleevio.cinemax.api.util.mapToCreateOrUpdateDistributorCommand
import com.cleevio.cinemax.api.util.mapToCreateOrUpdateMovieCommand
import jakarta.validation.ConstraintViolationException
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.test.context.jdbc.Sql
import org.springframework.test.context.jdbc.SqlGroup
import java.util.UUID
import kotlin.test.assertEquals
import kotlin.test.assertTrue

@SqlGroup(
    Sql(
        scripts = [
            "/db/migration/V110__init_jso_table_with_data.sql"
        ]
    )
)
class MovieJsoServiceIT @Autowired constructor(
    private val underTest: MovieJsoService,
    private val movieService: MovieService,
    private val distributorService: DistributorService,
    private val movieJsoRepository: MovieJsoRepository,
    private val jsoJooqFinderService: JsoJooqFinderService,
) : IntegrationTest(DatabaseCleanup.AFTER_EACH) {

    @BeforeEach
    fun setUp() {
        distributorService.syncCreateOrUpdateDistributor(mapToCreateOrUpdateDistributorCommand(DISTRIBUTOR_1))
        movieService.syncCreateOrUpdateMovie(mapToCreateOrUpdateMovieCommand(MOVIE_1))
    }

    @Test
    fun `test deleteAndCreateMovieJsos - movie does not exist - should throw exception`() {
        assertThrows<MovieNotFoundException> {
            underTest.deleteAndCreateMovieJsos(
                DeleteAndCreateMovieJsosCommand(
                    movieId = UUID.randomUUID(),
                    jsoIds = emptySet()
                )
            )
        }
    }

    @Test
    fun `test deleteAndCreateMovieJsos - jsoIds invalid size - should throw exception`() {
        assertThrows<ConstraintViolationException> {
            underTest.deleteAndCreateMovieJsos(
                DeleteAndCreateMovieJsosCommand(
                    movieId = MOVIE_1.id,
                    jsoIds = setOf(UUID.randomUUID(), UUID.randomUUID(), UUID.randomUUID(), UUID.randomUUID())
                )
            )
        }
    }

    @Test
    fun `test deleteAndCreateMovieJsos - jso does not exist - should throw exception`() {
        assertThrows<JsoNotFoundException> {
            underTest.deleteAndCreateMovieJsos(
                DeleteAndCreateMovieJsosCommand(
                    movieId = MOVIE_1.id,
                    jsoIds = setOf(UUID.randomUUID())
                )
            )
        }
    }

    @Test
    fun `test deleteAndCreateMovieJsos - create movieJsos - should create movie jsos`() {
        val jso1 = jsoJooqFinderService.findByOriginalId(1)!!
        val jso2 = jsoJooqFinderService.findByOriginalId(2)!!
        assertTrue(movieJsoRepository.findAllByMovieId(MOVIE_1.id).isEmpty())

        underTest.deleteAndCreateMovieJsos(
            DeleteAndCreateMovieJsosCommand(
                movieId = MOVIE_1.id,
                jsoIds = setOf(jso1.id, jso2.id)
            )
        )

        val movieJsos = movieJsoRepository.findAllByMovieId(MOVIE_1.id)
        assertEquals(2, movieJsos.size)
        assertTrue(movieJsos.map { it.jsoId }.containsAll(setOf(jso1.id, jso2.id)))
    }

    @Test
    fun `test deleteAndCreateMovieJsos - jso list is empty - should delete related jsos`() {
        val jso1 = jsoJooqFinderService.findByOriginalId(1)!!
        val jso2 = jsoJooqFinderService.findByOriginalId(2)!!
        underTest.deleteAndCreateMovieJsos(
            DeleteAndCreateMovieJsosCommand(
                movieId = MOVIE_1.id,
                jsoIds = setOf(jso1.id, jso2.id)
            )
        )
        assertEquals(2, movieJsoRepository.findAllByMovieId(MOVIE_1.id).size)

        underTest.deleteAndCreateMovieJsos(
            DeleteAndCreateMovieJsosCommand(
                movieId = MOVIE_1.id,
                jsoIds = setOf()
            )
        )
        assertTrue(movieJsoRepository.findAllByMovieId(MOVIE_1.id).isEmpty())
    }

    @Test
    fun `test deleteAndCreateMovieJsos - jso list contains jsoIds - should delete related movie jsos and create new ones`() {
        val jso1 = jsoJooqFinderService.findByOriginalId(1)!!
        val jso2 = jsoJooqFinderService.findByOriginalId(2)!!
        val jso3 = jsoJooqFinderService.findByOriginalId(3)!!

        underTest.deleteAndCreateMovieJsos(
            DeleteAndCreateMovieJsosCommand(
                movieId = MOVIE_1.id,
                jsoIds = setOf(jso1.id, jso2.id)
            )
        )
        assertEquals(2, movieJsoRepository.findAllByMovieId(MOVIE_1.id).size)

        underTest.deleteAndCreateMovieJsos(
            DeleteAndCreateMovieJsosCommand(
                movieId = MOVIE_1.id,
                jsoIds = setOf(jso3.id)
            )
        )
        val movieJsos = movieJsoRepository.findAllByMovieId(MOVIE_1.id)
        assertEquals(1, movieJsos.size)
        assertEquals(movieJsos.first().jsoId, jso3.id)
    }
}

private val DISTRIBUTOR_1 = createDistributor()
private val MOVIE_1 = createMovie(
    originalId = 1,
    title = "Star Wars: Episode I – The Phantom Menace",
    releaseYear = 2001,
    distributorId = DISTRIBUTOR_1.id
)
