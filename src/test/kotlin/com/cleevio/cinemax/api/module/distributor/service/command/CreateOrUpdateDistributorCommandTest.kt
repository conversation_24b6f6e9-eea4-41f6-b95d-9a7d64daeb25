package com.cleevio.cinemax.api.module.distributor.service.command

import jakarta.validation.ConstraintViolationException
import jakarta.validation.Validation
import jakarta.validation.Validator
import org.junit.jupiter.api.Assertions.assertThrows
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.Arguments
import org.junit.jupiter.params.provider.MethodSource
import java.util.Optional
import java.util.UUID
import java.util.stream.Stream

class CreateOrUpdateDistributorCommandTest {

    private val validator: Validator = Validation.buildDefaultValidatorFactory().validator

    @ParameterizedTest
    @MethodSource("invalidCommandsProvider")
    fun `createOrUpdateDistributorCommandTest - invalid data - should throw`(command: CreateOrUpdateDistributorCommand) {
        assertThrows(ConstraintViolationException::class.java) {
            val violations = validator.validate(command)
            if (violations.isNotEmpty()) {
                throw ConstraintViolationException(violations)
            }
        }
    }

    companion object {
        @JvmStatic
        fun invalidCommandsProvider(): Stream<Arguments> {
            val validCommand = CreateOrUpdateDistributorCommand(
                id = UUID.randomUUID(),
                originalId = 1,
                code = "OC1",
                disfilmCode = Optional.of("01"),
                title = "Best movies ever, s.r.o.",
                addressStreet = Optional.of("Filmová 58"),
                addressCity = Optional.of("Praha"),
                addressPostCode = Optional.of("150 00"),
                contactName1 = Optional.of("John"),
                contactName2 = Optional.of("Jerry"),
                contactName3 = Optional.of("Tom"),
                contactPhone1 = Optional.of("+************"),
                contactPhone2 = Optional.of("+************"),
                contactPhone3 = Optional.of("*********"),
                contactEmails = Optional.of(setOf("<EMAIL>", "<EMAIL>")),
                bankName = Optional.of("AirBank, a.s."),
                bankAccount = Optional.of("**********/0300"),
                idNumber = Optional.of("********"),
                taxIdNumber = Optional.of("SK2022916731"),
                vatRate = Optional.of(20),
                note = Optional.of("Best note ever")
            )

            return Stream.of(
                Arguments.of(validCommand.copy(code = "")),
                Arguments.of(validCommand.copy(code = "12345")),
                Arguments.of(validCommand.copy(title = "")),
                Arguments.of(validCommand.copy(title = "a".repeat(51))),
                Arguments.of(validCommand.copy(addressStreet = Optional.of("a".repeat(51)))),
                Arguments.of(validCommand.copy(addressCity = Optional.of("a".repeat(51)))),
                Arguments.of(validCommand.copy(addressPostCode = Optional.of("1234567"))),
                Arguments.of(validCommand.copy(contactName1 = Optional.of("a".repeat(21)))),
                Arguments.of(validCommand.copy(contactPhone1 = Optional.of("a".repeat(16)))),
                Arguments.of(validCommand.copy(bankName = Optional.of("a".repeat(51)))),
                Arguments.of(validCommand.copy(bankAccount = Optional.of("a".repeat(16)))),
                Arguments.of(validCommand.copy(idNumber = Optional.of("a".repeat(11)))),
                Arguments.of(validCommand.copy(taxIdNumber = Optional.of("a".repeat(13)))),
                Arguments.of(validCommand.copy(vatRate = Optional.of(-1))),
                Arguments.of(validCommand.copy(note = Optional.of("")))
            )
        }
    }
}
