package com.cleevio.cinemax.api.module.movie.service

import com.cleevio.cinemax.api.DatabaseCleanup
import com.cleevio.cinemax.api.IntegrationTest
import com.cleevio.cinemax.api.module.distributor.service.DistributorService
import com.cleevio.cinemax.api.module.genre.entity.Genre
import com.cleevio.cinemax.api.module.genre.service.GenreJpaFinderService
import com.cleevio.cinemax.api.module.jso.entity.Jso
import com.cleevio.cinemax.api.module.jso.service.JsoJpaFinderService
import com.cleevio.cinemax.api.module.language.entity.Language
import com.cleevio.cinemax.api.module.language.service.LanguageJpaFinderService
import com.cleevio.cinemax.api.module.movie.exception.MovieNotFoundException
import com.cleevio.cinemax.api.module.movie.service.query.AdminGetMovieQuery
import com.cleevio.cinemax.api.module.moviejso.service.MovieJsoService
import com.cleevio.cinemax.api.module.moviejso.service.command.DeleteAndCreateMovieJsosCommand
import com.cleevio.cinemax.api.module.production.entity.Production
import com.cleevio.cinemax.api.module.production.service.ProductionJpaFinderService
import com.cleevio.cinemax.api.module.rating.entity.Rating
import com.cleevio.cinemax.api.module.rating.service.RatingJpaFinderService
import com.cleevio.cinemax.api.module.technology.entity.Technology
import com.cleevio.cinemax.api.module.technology.service.TechnologyJpaFinderService
import com.cleevio.cinemax.api.module.tmslanguage.entity.TmsLanguage
import com.cleevio.cinemax.api.module.tmslanguage.service.TmsLanguageJpaFinderService
import com.cleevio.cinemax.api.util.assertAdminGetMovieResponseEquals
import com.cleevio.cinemax.api.util.createDistributor
import com.cleevio.cinemax.api.util.createMovie
import com.cleevio.cinemax.api.util.mapToCreateOrUpdateDistributorCommand
import com.cleevio.cinemax.api.util.mapToCreateOrUpdateMovieCommand
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.test.context.jdbc.Sql
import org.springframework.test.context.jdbc.SqlGroup
import java.time.LocalDate
import java.util.UUID

@SqlGroup(
    Sql(
        scripts = [
            "/db/migration/V104__init_production_table_with_data.sql",
            "/db/migration/V105__init_genre_table_with_data.sql",
            "/db/migration/V106__init_rating_table_with_data.sql",
            "/db/migration/V107__init_technology_table_with_data.sql",
            "/db/migration/V108__init_language_table_with_data.sql",
            "/db/migration/V109__init_tms_language_table_with_data.sql",
            "/db/migration/V110__init_jso_table_with_data.sql"
        ]
    )
)
class AdminGetMovieQueryServiceIT @Autowired constructor(
    private val underTest: AdminGetMovieQueryService,
    private val distributorService: DistributorService,
    private val movieService: MovieService,
    private val movieRepository: MovieRepository,
    private val movieJsoService: MovieJsoService,
    private val productionJpaFinderService: ProductionJpaFinderService,
    private val genreJpaFinderService: GenreJpaFinderService,
    private val ratingJpaFinderService: RatingJpaFinderService,
    private val technologyJpaFinderService: TechnologyJpaFinderService,
    private val languageJpaFinderService: LanguageJpaFinderService,
    private val tmsLanguageJpaFinderService: TmsLanguageJpaFinderService,
    private val jsoJpaFinderService: JsoJpaFinderService,
) : IntegrationTest(DatabaseCleanup.AFTER_EACH) {

    @BeforeEach
    fun setUp() {
        val production1 = productionJpaFinderService.findByCode(PRODUCTION_1.code)!!
        val genre1 = genreJpaFinderService.findByCode(GENRE_1.code)!!
        val genre2 = genreJpaFinderService.findByCode(GENRE_2.code)!!
        val rating1 = ratingJpaFinderService.findByCode(RATING_1.code)!!
        val technology1 = technologyJpaFinderService.findByCode(TECHNOLOGY_1.code)!!
        val language1 = languageJpaFinderService.findByCode(LANGUAGE_1.code)!!
        val tmsLanguage1 = tmsLanguageJpaFinderService.findByCode(TMS_LANGUAGE_1.code)!!
        val jso1 = jsoJpaFinderService.findByCode(JSO_1.code)!!

        setOf(DISTRIBUTOR_1, DISTRIBUTOR_2).forEach {
            distributorService.syncCreateOrUpdateDistributor(mapToCreateOrUpdateDistributorCommand(it))
        }

        movieService.syncCreateOrUpdateMovie(
            mapToCreateOrUpdateMovieCommand(
                MOVIE_1.apply {
                    productionId = production1.id
                    primaryGenreId = genre1.id
                    secondaryGenreId = genre2.id
                    languageId = language1.id
                    tmsLanguageId = tmsLanguage1.id
                    ratingId = rating1.id
                    technologyId = technology1.id
                }
            )
        )
        movieJsoService.deleteAndCreateMovieJsos(
            DeleteAndCreateMovieJsosCommand(
                movieId = MOVIE_1.id,
                jsoIds = setOf(jso1.id)
            )
        )

        movieService.syncCreateOrUpdateMovie(mapToCreateOrUpdateMovieCommand(MOVIE_2))
        val movie4 = movieRepository.findByOriginalIdAndDeletedAtIsNull(MOVIE_2.originalId!!)!!
        movieRepository.save(
            movie4.apply {
                markDeleted()
            }
        )
    }

    @Test
    fun `test AdminGetMovieQuery - existing movie - should find and map correctly`() {
        val movie = underTest(AdminGetMovieQuery(MOVIE_1.id))

        assertAdminGetMovieResponseEquals(
            expected = MOVIE_1,
            expectedProductionTitle = PRODUCTION_1.title,
            expectedPrimaryGenreTitle = GENRE_1.title,
            expectedSecondaryGenreTitle = GENRE_2.title,
            expectedLanguageTitle = LANGUAGE_1.title,
            expectedTechnologyTitle = TECHNOLOGY_1.title,
            expectedRatingTitle = RATING_1.title,
            expectedTmsLanguageTitle = TMS_LANGUAGE_1.title,
            expectedJsoTitles = setOf(JSO_1.title),
            actual = movie
        )
    }

    @Test
    fun `test AdminGetMovieQuery - deleted movie - should throw exception`() {
        assertThrows<MovieNotFoundException> {
            underTest(AdminGetMovieQuery(MOVIE_2.id))
        }
    }

    @Test
    fun `test AdminGetMovieQuery - non-existing movie - should throw exception`() {
        assertThrows<MovieNotFoundException> {
            underTest(AdminGetMovieQuery(UUID.randomUUID()))
        }
    }
}

private val DISTRIBUTOR_1 = createDistributor()
private val DISTRIBUTOR_2 = createDistributor(
    originalId = 2,
    title = "Vychodoslovenská distribučná spoločnosť"
)
private val MOVIE_1 = createMovie(
    originalId = 1,
    title = "Star Wars: Episode I – The Phantom Menace",
    releaseYear = 2001,
    premiereDate = LocalDate.of(2001, 4, 30),
    distributorId = DISTRIBUTOR_1.id
)
private val MOVIE_2 = createMovie(
    originalId = 4,
    title = "Twelve Angry Men",
    code = "567890",
    premiereDate = LocalDate.of(1952, 11, 30),
    distributorId = DISTRIBUTOR_2.id
)
private val PRODUCTION_1 = Production(
    originalId = 98,
    code = "56",
    title = "USA"
)
private val GENRE_1 = Genre(
    originalId = 2,
    code = "1",
    title = "Dráma"
)
private val GENRE_2 = Genre(
    originalId = 5,
    code = "4",
    title = "Sci-Fi"
)
private val RATING_1 = Rating(
    originalId = 6,
    code = "12",
    title = "12"
)
private val TECHNOLOGY_1 = Technology(
    originalId = 12,
    code = "05",
    title = "IMAX"
)
private val LANGUAGE_1 = Language(
    originalId = 1,
    code = "CZ",
    title = "česká verze"
)
private val TMS_LANGUAGE_1 = TmsLanguage(
    originalId = 15,
    code = "C",
    title = "český dabing"
)
private val JSO_1 = Jso(
    originalId = 3,
    code = "S",
    title = "Strach"
)
