package com.cleevio.cinemax.api.module.product.service.query

import com.cleevio.cinemax.api.DatabaseCleanup
import com.cleevio.cinemax.api.IntegrationTest
import com.cleevio.cinemax.api.common.constant.STANDARD_TAX_RATE
import com.cleevio.cinemax.api.common.util.isEqualTo
import com.cleevio.cinemax.api.module.file.service.FileRepository
import com.cleevio.cinemax.api.module.product.constant.ProductType
import com.cleevio.cinemax.api.module.product.event.ProductCreatedOrUpdatedEvent
import com.cleevio.cinemax.api.module.product.service.ProductService
import com.cleevio.cinemax.api.module.product.service.command.DeleteProductCommand
import com.cleevio.cinemax.api.module.productcategory.constant.ProductCategoryType
import com.cleevio.cinemax.api.module.productcategory.service.ProductCategoryService
import com.cleevio.cinemax.api.module.productcomponent.constant.ProductComponentUnit
import com.cleevio.cinemax.api.module.productcomponent.service.ProductComponentService
import com.cleevio.cinemax.api.module.productcomponentcategory.service.ProductComponentCategoryRepository
import com.cleevio.cinemax.api.module.productcomposition.entity.ProductComposition
import com.cleevio.cinemax.api.util.createFile
import com.cleevio.cinemax.api.util.createProduct
import com.cleevio.cinemax.api.util.createProductCategory
import com.cleevio.cinemax.api.util.createProductComponent
import com.cleevio.cinemax.api.util.createProductComponentCategory
import com.cleevio.cinemax.api.util.createProductComposition
import com.cleevio.cinemax.api.util.mapToCreateOrUpdateProductCategoryCommand
import com.cleevio.cinemax.api.util.mapToCreateOrUpdateProductComponentCommand
import com.cleevio.cinemax.api.util.mapToCreateProductCommand
import com.cleevio.cinemax.api.util.toUUID
import com.cleevio.cinemax.psql.tables.ProductColumnNames
import io.mockk.Runs
import io.mockk.every
import io.mockk.just
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.Arguments
import org.junit.jupiter.params.provider.MethodSource
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.data.domain.PageRequest
import org.springframework.data.domain.Pageable
import org.springframework.data.domain.Sort
import java.math.BigDecimal
import java.util.UUID
import java.util.stream.Stream
import kotlin.test.assertNotNull
import kotlin.test.assertNull
import kotlin.test.assertTrue

class AdminSearchProductsQueryServiceIT @Autowired constructor(
    private val underTest: AdminSearchProductsQueryService,
    private val productService: ProductService,
    private val productCategoryService: ProductCategoryService,
    private val productComponentCategoryRepository: ProductComponentCategoryRepository,
    private val productComponentService: ProductComponentService,
    private val fileRepository: FileRepository,
) : IntegrationTest(DatabaseCleanup.AFTER_EACH) {

    @Test
    fun `test AdminSearchProductsQuery - no filter - should correctly return all products`() {
        val expectedProductIds = initData()

        val result = underTest(
            AdminSearchProductsQuery(
                filter = AdminSearchProductsFilter(),
                pageable = Pageable.unpaged()
            )
        )

        assertEquals(7, result.totalElements)
        assertEquals(1, result.totalPages)
        assertEquals(7, result.content.size)
        assertEquals(expectedProductIds.values.toSet(), result.content.map { it.id }.toSet())

        setOf(PRODUCT_1, PRODUCT_2, PRODUCT_3, PRODUCT_4, PRODUCT_5, PRODUCT_6, PRODUCT_8).forEach { expectedProduct ->
            val response = result.content.first { product -> product.id == expectedProductIds[expectedProduct.id] }

            assertEquals(expectedProductIds[expectedProduct.id], response.id)
            assertTrue(response.code.matches(Regex("[0-9]{5}")))
            assertEquals(expectedProduct.title, response.title)
            assertEquals(expectedProduct.type, response.type)
            assertEquals(expectedProduct.order, response.order)
            assertTrue(expectedProduct.price isEqualTo response.price)
            assertTrue(expectedProduct.flagshipPrice isEqualTo response.flagshipPrice)
            assertEquals(expectedProduct.active, response.active)
            assertTrue(expectedProduct.discountAmount isEqualTo response.discountAmount)
            assertEquals(expectedProduct.discountPercentage, response.discountPercentage)
            assertNotNull(response.createdAt)
            assertNotNull(response.updatedAt)

            val expectedCategory = setOf(PRODUCT_CATEGORY_1, PRODUCT_CATEGORY_2, PRODUCT_CATEGORY_3).associateBy { it.id }

            assertEquals(expectedProduct.productCategoryId, response.productCategory.id)
            assertEquals(expectedCategory[expectedProduct.productCategoryId]!!.type, response.productCategory.type)
            assertEquals(expectedCategory[expectedProduct.productCategoryId]!!.title, response.productCategory.title)

            when (expectedProduct) {
                // Coca Cola 0.33l
                PRODUCT_1 -> {
                    assertTrue(90.toBigDecimal() isEqualTo response.stockQuantity)
                    assertEquals(true, response.isProductComponent)
                    assertEquals(FILE_1.id, response.imageFile!!.id)
                    assertEquals(
                        "https://example.com/manager-app/files/product_image/${FILE_1.getFilename()}",
                        response.imageFile!!.url
                    )
                }
                // Sprite 0.33l
                PRODUCT_2 -> {
                    assertTrue(5.toBigDecimal() isEqualTo response.stockQuantity)
                    assertEquals(true, response.isProductComponent)
                    assertNull(response.imageFile)
                }
                // Popcorn XXL
                PRODUCT_3 -> {
                    assertTrue(25.toBigDecimal() isEqualTo response.stockQuantity)
                    assertEquals(false, response.isProductComponent)
                    assertEquals(FILE_2.id, response.imageFile!!.id)
                    assertEquals(
                        "https://example.com/manager-app/files/product_image/${FILE_2.getFilename()}",
                        response.imageFile!!.url
                    )
                }
                // Combo Popcorn XXL + 3x Coca Cola 0,33l
                PRODUCT_4 -> {
                    assertTrue(18.toBigDecimal() isEqualTo response.stockQuantity)
                    assertEquals(false, response.isProductComponent)
                    assertNull(response.imageFile)
                }
                // Combo Popcorn XXL + Sprite 0,33l
                PRODUCT_5 -> {
                    assertTrue(5.toBigDecimal() isEqualTo response.stockQuantity)
                    assertEquals(false, response.isProductComponent)
                    assertNull(response.imageFile)
                }
                // Additional Sale
                PRODUCT_6 -> {
                    assertTrue(0.toBigDecimal() isEqualTo response.stockQuantity)
                    assertEquals(false, response.isProductComponent)
                    assertEquals(FILE_3.id, response.imageFile!!.id)
                    assertEquals(
                        "https://example.com/manager-app/files/product_image/${FILE_3.getFilename()}",
                        response.imageFile!!.url
                    )
                }
                // Záloha za obal
                PRODUCT_8 -> {
                    assertTrue(0.toBigDecimal() isEqualTo response.stockQuantity)
                    assertEquals(false, response.isProductComponent)
                    assertNull(response.imageFile)
                }
            }
        }
    }

    @ParameterizedTest
    @MethodSource("filteringParametersProvider")
    fun `test AdminSearchProductsQuery - should correctly return filtered products`(
        expectedResult: Set<UUID>,
        searchFilter: AdminSearchProductsFilter,
    ) {
        val createdProductIds = initData()

        val result = underTest(
            AdminSearchProductsQuery(
                filter = searchFilter,
                pageable = Pageable.unpaged()
            )
        )

        assertEquals(
            expectedResult.map { createdProductIds[it] }.toSet(),
            result.content.map { it.id }.toSet()
        )
    }

    @ParameterizedTest
    @MethodSource("sortingParametersProvider")
    fun `test AdminSearchProductsQuery - sorting by entity fields - should return sorted products`(
        expectedResult: List<UUID>,
        sortProperties: List<String>,
        direction: Sort.Direction,
    ) {
        val createdProductIds = initData()

        val result = underTest(
            AdminSearchProductsQuery(
                filter = AdminSearchProductsFilter(),
                pageable = PageRequest.of(0, 10, Sort.by(direction, *sortProperties.toTypedArray()))
            )
        )
        assertEquals(
            expectedResult.map { createdProductIds[it] },
            result.content.map { it.id }
        )
    }

    private fun initData(): Map<UUID, UUID> {
        every { applicationEventPublisherMock.publishEvent(any<ProductCreatedOrUpdatedEvent>()) } just Runs

        fileRepository.saveAll(setOf(FILE_1, FILE_2, FILE_3))

        setOf(PRODUCT_CATEGORY_1, PRODUCT_CATEGORY_2, PRODUCT_CATEGORY_3).forEach {
            productCategoryService.syncCreateOrUpdateProductCategory(
                mapToCreateOrUpdateProductCategoryCommand(it)
            )
        }

        productComponentCategoryRepository.save(PRODUCT_COMPONENT_CATEGORY_1)
        setOf(
            PRODUCT_COMPONENT_1,
            PRODUCT_COMPONENT_2,
            PRODUCT_COMPONENT_3,
            PRODUCT_COMPONENT_4,
            PRODUCT_COMPONENT_5,
            PRODUCT_COMPONENT_6
        ).forEach {
            productComponentService.syncCreateOrUpdateProductComponent(
                mapToCreateOrUpdateProductComponentCommand(it)
            )
        }

        val product1Id = productService.adminCreateProduct(
            mapToCreateProductCommand(
                product = PRODUCT_1,
                productCategoryId = PRODUCT_CATEGORY_1.id,
                productCompositions = listOf(PRODUCT_COMPOSITION_1)
            )
        )

        val product2Id = productService.adminCreateProduct(
            mapToCreateProductCommand(
                product = PRODUCT_2,
                productCategoryId = PRODUCT_CATEGORY_3.id,
                productCompositions = listOf(PRODUCT_COMPOSITION_2)
            )
        )

        val product3Id = productService.adminCreateProduct(
            mapToCreateProductCommand(
                product = PRODUCT_3,
                productCategoryId = PRODUCT_CATEGORY_1.id,
                productCompositions = listOf(PRODUCT_COMPOSITION_3, PRODUCT_COMPOSITION_4, PRODUCT_COMPOSITION_5)
            )
        )

        val product4Id = productService.adminCreateProduct(
            mapToCreateProductCommand(
                product = PRODUCT_4,
                productCategoryId = PRODUCT_CATEGORY_3.id,
                productCompositions = listOf(PRODUCT_COMPOSITION_6(product1Id), PRODUCT_COMPOSITION_7(product3Id))
            )
        )

        val product5Id = productService.adminCreateProduct(
            mapToCreateProductCommand(
                product = PRODUCT_5,
                productCategoryId = PRODUCT_CATEGORY_1.id,
                productCompositions = listOf(PRODUCT_COMPOSITION_8(product2Id), PRODUCT_COMPOSITION_9(product3Id))
            )
        )

        val product6Id = productService.adminCreateProduct(
            mapToCreateProductCommand(
                product = PRODUCT_6,
                productCategoryId = PRODUCT_CATEGORY_2.id,
                productCompositions = emptyList()
            )
        )

        // just to be sure deleted product won't appear anywhere
        productService.adminCreateProduct(
            mapToCreateProductCommand(
                product = PRODUCT_7_DELETED,
                productCategoryId = PRODUCT_CATEGORY_1.id,
                productCompositions = listOf(PRODUCT_COMPOSITION_10)
            )
        ).also {
            productService.deleteProduct(DeleteProductCommand(it))
        }

        val product8Id = productService.adminCreateProduct(
            mapToCreateProductCommand(
                product = PRODUCT_8,
                productCategoryId = PRODUCT_CATEGORY_3.id,
                productCompositions = emptyList()
            )
        )

        return mapOf(
            PRODUCT_1.id to product1Id,
            PRODUCT_2.id to product2Id,
            PRODUCT_3.id to product3Id,
            PRODUCT_4.id to product4Id,
            PRODUCT_5.id to product5Id,
            PRODUCT_6.id to product6Id,
            PRODUCT_8.id to product8Id
        )
    }

    companion object {
        @JvmStatic
        fun filteringParametersProvider(): Stream<Arguments> {
            return Stream.of(
                Arguments.of(
                    setOf(PRODUCT_3.id, PRODUCT_4.id, PRODUCT_5.id),
                    AdminSearchProductsFilter(
                        title = "popcorn"
                    )
                ),
                Arguments.of(
                    setOf(PRODUCT_4.id, PRODUCT_5.id, PRODUCT_6.id, PRODUCT_8.id),
                    AdminSearchProductsFilter(
                        types = setOf(ProductType.PRODUCT_IN_PRODUCT, ProductType.ADDITIONAL_SALE)
                    )
                ),
                Arguments.of(
                    setOf(PRODUCT_6.id),
                    AdminSearchProductsFilter(
                        categoryTypes = setOf(ProductCategoryType.DISCOUNT)
                    )
                ),
                Arguments.of(
                    setOf(PRODUCT_6.id),
                    AdminSearchProductsFilter(
                        active = false
                    )
                ),
                Arguments.of(
                    setOf(PRODUCT_3.id, PRODUCT_4.id, PRODUCT_5.id, PRODUCT_6.id, PRODUCT_8.id),
                    AdminSearchProductsFilter(
                        isProductComponent = false
                    )
                ),
                Arguments.of(
                    setOf(PRODUCT_8.id),
                    AdminSearchProductsFilter(
                        isPackagingDeposit = true
                    )
                ),
                Arguments.of(
                    setOf(PRODUCT_1.id, PRODUCT_3.id, PRODUCT_5.id, PRODUCT_6.id),
                    AdminSearchProductsFilter(
                        categoryIds = setOf(PRODUCT_CATEGORY_1.id, PRODUCT_CATEGORY_2.id)
                    )
                ),
                Arguments.of(
                    setOf(PRODUCT_1.id, PRODUCT_2.id, PRODUCT_3.id, PRODUCT_4.id, PRODUCT_5.id, PRODUCT_6.id, PRODUCT_8.id),
                    AdminSearchProductsFilter(
                        categoryIds = null
                    )
                ),
                Arguments.of(
                    setOf(PRODUCT_1.id, PRODUCT_4.id, PRODUCT_6.id),
                    AdminSearchProductsFilter(
                        soldInBuffet = true
                    )
                ),
                Arguments.of(
                    setOf(PRODUCT_2.id, PRODUCT_5.id, PRODUCT_6.id),
                    AdminSearchProductsFilter(
                        soldInCafe = true
                    )
                ),
                Arguments.of(
                    setOf(PRODUCT_3.id, PRODUCT_4.id, PRODUCT_5.id),
                    AdminSearchProductsFilter(
                        soldInVip = true
                    )
                ),
                Arguments.of(
                    setOf(PRODUCT_5.id),
                    AdminSearchProductsFilter(
                        soldInCafe = true,
                        soldInVip = true
                    )
                )
            )
        }

        @JvmStatic
        fun sortingParametersProvider(): Stream<Arguments> {
            return Stream.of(
                Arguments.of(
                    listOf(PRODUCT_8.id, PRODUCT_2.id, PRODUCT_3.id, PRODUCT_5.id, PRODUCT_4.id, PRODUCT_1.id, PRODUCT_6.id),
                    listOf(ProductColumnNames.TITLE),
                    Sort.Direction.DESC
                ),
                // Arguments.of(
                //     listOf(PRODUCT_6.id, PRODUCT_8.id, PRODUCT_3.id, PRODUCT_1.id, PRODUCT_2.id, PRODUCT_4.id, PRODUCT_5.id),
                //     listOf(ProductColumnNames.TYPE),
                //     Sort.Direction.ASC
                // ),
                Arguments.of(
                    listOf(PRODUCT_1.id, PRODUCT_3.id, PRODUCT_2.id, PRODUCT_6.id, PRODUCT_4.id, PRODUCT_5.id, PRODUCT_8.id),
                    listOf(ProductColumnNames.ORDER, ProductColumnNames.CREATED_AT),
                    Sort.Direction.ASC
                ),
                // Arguments.of(
                //     listOf(PRODUCT_8.id, PRODUCT_1.id, PRODUCT_4.id, PRODUCT_6.id, PRODUCT_2.id, PRODUCT_3.id, PRODUCT_5.id),
                //     listOf(ProductColumnNames.PRICE),
                //     Sort.Direction.ASC
                // ),
                // Arguments.of(
                //     listOf(PRODUCT_6.id, PRODUCT_2.id, PRODUCT_3.id, PRODUCT_1.id, PRODUCT_5.id, PRODUCT_8.id, PRODUCT_4.id),
                //     listOf(ProductColumnNames.ACTIVE),
                //     Sort.Direction.ASC
                // ),
                Arguments.of(
                    listOf(PRODUCT_2.id, PRODUCT_5.id, PRODUCT_4.id, PRODUCT_3.id, PRODUCT_1.id, PRODUCT_6.id, PRODUCT_8.id),
                    listOf("product.stockQuantity", ProductColumnNames.CREATED_AT),
                    Sort.Direction.ASC
                ),
                Arguments.of(
                    listOf(PRODUCT_3.id, PRODUCT_4.id, PRODUCT_5.id, PRODUCT_6.id, PRODUCT_8.id, PRODUCT_1.id, PRODUCT_2.id),
                    listOf("product.isProductComponent", ProductColumnNames.CREATED_AT),
                    Sort.Direction.ASC
                ),
                Arguments.of(
                    listOf(PRODUCT_4.id, PRODUCT_1.id, PRODUCT_2.id, PRODUCT_3.id, PRODUCT_5.id, PRODUCT_6.id, PRODUCT_8.id),
                    listOf(ProductColumnNames.DISCOUNT_AMOUNT, ProductColumnNames.CREATED_AT),
                    Sort.Direction.ASC
                ),
                Arguments.of(
                    listOf(PRODUCT_6.id, PRODUCT_1.id, PRODUCT_2.id, PRODUCT_3.id, PRODUCT_4.id, PRODUCT_5.id, PRODUCT_8.id),
                    listOf(ProductColumnNames.DISCOUNT_PERCENTAGE, ProductColumnNames.CREATED_AT),
                    Sort.Direction.ASC
                ),
                Arguments.of(
                    listOf(PRODUCT_1.id, PRODUCT_2.id, PRODUCT_3.id, PRODUCT_4.id, PRODUCT_5.id, PRODUCT_6.id, PRODUCT_8.id),
                    listOf(ProductColumnNames.CREATED_AT),
                    Sort.Direction.ASC
                ),
                Arguments.of(
                    listOf(PRODUCT_1.id, PRODUCT_2.id, PRODUCT_3.id, PRODUCT_4.id, PRODUCT_5.id, PRODUCT_6.id, PRODUCT_8.id),
                    listOf(ProductColumnNames.UPDATED_AT),
                    Sort.Direction.ASC
                ),
                Arguments.of(
                    listOf(PRODUCT_6.id, PRODUCT_8.id, PRODUCT_1.id, PRODUCT_4.id, PRODUCT_2.id, PRODUCT_3.id, PRODUCT_5.id),
                    listOf("productCategory.type", ProductColumnNames.PRICE),
                    Sort.Direction.ASC
                ),
                Arguments.of(
                    listOf(PRODUCT_1.id, PRODUCT_3.id, PRODUCT_5.id, PRODUCT_6.id, PRODUCT_2.id, PRODUCT_4.id, PRODUCT_8.id),
                    listOf("productCategory.title", ProductColumnNames.CREATED_AT),
                    Sort.Direction.ASC
                )
            )
        }
    }
}

private val PRODUCT_COMPONENT_CATEGORY_1 = createProductComponentCategory()

private val PRODUCT_CATEGORY_1 = createProductCategory(
    originalId = 1,
    code = "1",
    title = "Kategorie A",
    type = ProductCategoryType.PRODUCT,
    taxRate = STANDARD_TAX_RATE
)

private val PRODUCT_CATEGORY_3 = createProductCategory(
    originalId = 3,
    code = "3",
    title = "Kategorie C",
    type = ProductCategoryType.PRODUCT,
    taxRate = STANDARD_TAX_RATE
)

private val PRODUCT_CATEGORY_2 = createProductCategory(
    originalId = 2,
    code = "2",
    title = "Kategorie B",
    type = ProductCategoryType.DISCOUNT,
    taxRate = STANDARD_TAX_RATE
)

private val FILE_1 = createFile(id = 1.toUUID(), originalName = "${1.toUUID()}.jpg", originalId = 1)
private val FILE_2 = createFile(id = 2.toUUID(), originalName = "${2.toUUID()}.jpg", originalId = 2)
private val FILE_3 = createFile(id = 3.toUUID(), originalName = "${3.toUUID()}.jpg", originalId = 3)

private val PRODUCT_1 = createProduct(
    originalId = 1,
    code = "11",
    productCategoryId = PRODUCT_CATEGORY_1.id,
    title = "Coca Cola 0.33l",
    type = ProductType.PRODUCT,
    order = 1,
    price = 10.toBigDecimal(),
    imageFileId = FILE_1.id,
    soldInCafe = false,
    soldInVip = false
)
private val PRODUCT_2 = createProduct(
    originalId = 2,
    code = "21",
    productCategoryId = PRODUCT_CATEGORY_3.id,
    title = "Sprite 0.33l",
    type = ProductType.PRODUCT,
    order = 3,
    price = 30.toBigDecimal(),
    soldInBuffet = false,
    soldInVip = false
)
private val PRODUCT_3 = createProduct(
    originalId = 3,
    code = "3",
    productCategoryId = PRODUCT_CATEGORY_1.id,
    title = "Popcorn XXL",
    type = ProductType.PRODUCT,
    order = 2,
    price = 50.toBigDecimal(),
    imageFileId = FILE_2.id,
    soldInBuffet = false,
    soldInCafe = false
)
private val PRODUCT_4 = createProduct(
    originalId = 4,
    code = "4",
    productCategoryId = PRODUCT_CATEGORY_3.id,
    title = "Combo Popcorn XXL + 3x Coca Cola 0,33l",
    type = ProductType.PRODUCT_IN_PRODUCT,
    order = 5,
    price = 15.toBigDecimal(),
    discountAmount = 100.toBigDecimal(),
    taxRate = null,
    soldInCafe = false
)
private val PRODUCT_5 = createProduct(
    originalId = 5,
    code = "5",
    productCategoryId = PRODUCT_CATEGORY_1.id,
    title = "Combo Popcorn XXL + Sprite 0,33l",
    type = ProductType.PRODUCT_IN_PRODUCT,
    order = 6,
    price = 100.toBigDecimal(),
    taxRate = null,
    soldInBuffet = false
)
private val PRODUCT_6 = createProduct(
    originalId = 6,
    code = "6",
    productCategoryId = PRODUCT_CATEGORY_2.id,
    title = "Additional Sale",
    type = ProductType.ADDITIONAL_SALE,
    active = false,
    order = 4,
    price = 30.toBigDecimal(),
    discountPercentage = 20,
    imageFileId = FILE_3.id,
    soldInVip = false
)
private val PRODUCT_7_DELETED = createProduct(
    originalId = 7,
    code = "7",
    productCategoryId = PRODUCT_CATEGORY_1.id,
    title = "Coca Cola 0.5l",
    type = ProductType.PRODUCT,
    price = 55.toBigDecimal(),
    discountAmount = 40.toBigDecimal()
).also { it.markDeleted() }
private val PRODUCT_8 = createProduct(
    originalId = 8,
    code = "8",
    productCategoryId = PRODUCT_CATEGORY_3.id,
    title = "Záloha za obal",
    type = ProductType.ADDITIONAL_SALE,
    active = true,
    order = 100,
    price = 0.30.toBigDecimal(),
    isPackagingDeposit = true,
    soldInBuffet = false,
    soldInCafe = false,
    soldInVip = false
)

private val PRODUCT_COMPONENT_1 = createProductComponent(
    originalId = 1,
    code = "1",
    title = "Coca Cola 0,33l",
    unit = ProductComponentUnit.KS,
    stockQuantity = BigDecimal.valueOf(90),
    productComponentCategoryId = PRODUCT_COMPONENT_CATEGORY_1.id
)
private val PRODUCT_COMPONENT_2 = createProductComponent(
    originalId = 2,
    code = "2",
    title = "Sprite 0,33l",
    unit = ProductComponentUnit.KS,
    stockQuantity = BigDecimal.valueOf(5.0),
    productComponentCategoryId = PRODUCT_COMPONENT_CATEGORY_1.id
)
private val PRODUCT_COMPONENT_3 = createProductComponent(
    originalId = 3,
    code = "3",
    title = "Kukurica",
    unit = ProductComponentUnit.KG,
    stockQuantity = BigDecimal.valueOf(1000),
    productComponentCategoryId = PRODUCT_COMPONENT_CATEGORY_1.id
)
private val PRODUCT_COMPONENT_4 = createProductComponent(
    originalId = 4,
    code = "4",
    title = "Soľ",
    unit = ProductComponentUnit.KG,
    stockQuantity = BigDecimal.valueOf(100),
    productComponentCategoryId = PRODUCT_COMPONENT_CATEGORY_1.id
)
private val PRODUCT_COMPONENT_5 = createProductComponent(
    originalId = 5,
    code = "5",
    title = "Tuk",
    unit = ProductComponentUnit.KG,
    stockQuantity = BigDecimal.valueOf(50),
    productComponentCategoryId = PRODUCT_COMPONENT_CATEGORY_1.id
)
private val PRODUCT_COMPONENT_6 = createProductComponent(
    originalId = 6,
    code = "6",
    title = "Coca Cola 0,5l",
    unit = ProductComponentUnit.KS,
    stockQuantity = BigDecimal.valueOf(30),
    productComponentCategoryId = PRODUCT_COMPONENT_CATEGORY_1.id
)

// Coca Cola 0.33l  - Coca Cola 0.33l (1x)
private val PRODUCT_COMPOSITION_1 = createProductComposition(
    originalId = 1,
    productId = PRODUCT_1.id,
    productComponentId = PRODUCT_COMPONENT_1.id,
    amount = BigDecimal.valueOf(1)
)

// Sprite 0.33l - Sprite 0.33l (1x)
private val PRODUCT_COMPOSITION_2 = createProductComposition(
    originalId = 2,
    productId = PRODUCT_2.id,
    productComponentId = PRODUCT_COMPONENT_2.id,
    amount = BigDecimal.valueOf(1)
)

// Popcorn XXL - Kukurica (1x)
private val PRODUCT_COMPOSITION_3 = createProductComposition(
    originalId = 3,
    productId = PRODUCT_3.id,
    productComponentId = PRODUCT_COMPONENT_3.id,
    amount = BigDecimal.valueOf(1),
    productInProductId = null
)

// Popcorn XXL - Soľ (3x)
private val PRODUCT_COMPOSITION_4 = createProductComposition(
    originalId = 4,
    productId = PRODUCT_3.id,
    productComponentId = PRODUCT_COMPONENT_4.id,
    amount = BigDecimal.valueOf(3)
)

// Popcorn XXL - Tuk (2x)
private val PRODUCT_COMPOSITION_5 = createProductComposition(
    originalId = 5,
    productId = PRODUCT_3.id,
    productComponentId = PRODUCT_COMPONENT_5.id,
    amount = BigDecimal.valueOf(2)
)

// Combo Popcorn XXL + MEGA Coca Cola 0,33l -> Coca Cola 0,33l (3x)
private val PRODUCT_COMPOSITION_6: (UUID) -> ProductComposition = { productInProductId: UUID ->
    createProductComposition(
        originalId = 6,
        productId = PRODUCT_4.id,
        productInProductId = productInProductId,
        productComponentId = null,
        amount = BigDecimal.valueOf(5),
        productInProductPrice = 2.toBigDecimal()
    )
}

// Combo Popcorn XXL + MEGA Cola 0,33l -> Popcorn XXL (1x)
private val PRODUCT_COMPOSITION_7: (UUID) -> ProductComposition = { productInProductId: UUID ->
    createProductComposition(
        originalId = 7,
        productId = PRODUCT_4.id,
        productInProductId = productInProductId,
        productComponentId = null,
        amount = BigDecimal.valueOf(1),
        productInProductPrice = 2.toBigDecimal()
    )
}

// Combo Popcorn XXL + Sprite 0,33l -> Sprite 0,33l (1x)
private val PRODUCT_COMPOSITION_8: (UUID) -> ProductComposition = { productInProductId: UUID ->
    createProductComposition(
        originalId = 8,
        productId = PRODUCT_5.id,
        productInProductId = productInProductId,
        productComponentId = null,
        amount = BigDecimal.valueOf(1),
        productInProductPrice = 2.toBigDecimal()
    )
}

// Combo Popcorn XXL + Coca Cola 0,33l -> Popcorn XXL (1x)
private val PRODUCT_COMPOSITION_9: (UUID) -> ProductComposition = { productInProductId: UUID ->
    createProductComposition(
        originalId = 9,
        productId = PRODUCT_5.id,
        productInProductId = productInProductId,
        productComponentId = null,
        amount = BigDecimal.valueOf(1),
        productInProductPrice = 2.toBigDecimal()
    )
}

// Coca Cola 0.5l  - Coca Cola 0.5l (1x)
private val PRODUCT_COMPOSITION_10 = createProductComposition(
    originalId = 10,
    productId = PRODUCT_7_DELETED.id,
    productComponentId = PRODUCT_COMPONENT_6.id,
    amount = BigDecimal.valueOf(1)
)
