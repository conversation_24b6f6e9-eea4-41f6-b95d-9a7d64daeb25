package com.cleevio.cinemax.api.module.product.service

import com.cleevio.cinemax.api.DatabaseCleanup
import com.cleevio.cinemax.api.IntegrationTest
import com.cleevio.cinemax.api.common.constant.NO_TAX_RATE
import com.cleevio.cinemax.api.common.constant.REDUCED_TAX_RATE
import com.cleevio.cinemax.api.common.constant.STANDARD_TAX_RATE
import com.cleevio.cinemax.api.common.util.isEqualTo
import com.cleevio.cinemax.api.module.basket.event.ProductBasketItemAddedToBasketEvent
import com.cleevio.cinemax.api.module.basket.service.BasketService
import com.cleevio.cinemax.api.module.basketitem.constant.BasketItemType
import com.cleevio.cinemax.api.module.basketitem.controller.dto.CreateBasketItemRequest
import com.cleevio.cinemax.api.module.basketitem.controller.dto.CreateProductRequest
import com.cleevio.cinemax.api.module.basketitem.service.BasketItemJpaFinderService
import com.cleevio.cinemax.api.module.branch.service.BranchRepository
import com.cleevio.cinemax.api.module.product.constant.ProductType
import com.cleevio.cinemax.api.module.product.service.command.DetermineProductDiscountPriceCommand
import com.cleevio.cinemax.api.module.productcategory.constant.ProductCategoryType
import com.cleevio.cinemax.api.module.productcategory.service.ProductCategoryService
import com.cleevio.cinemax.api.module.productcomponent.constant.ProductComponentUnit
import com.cleevio.cinemax.api.module.productcomponent.service.ProductComponentService
import com.cleevio.cinemax.api.module.productcomponentcategory.service.ProductComponentCategoryRepository
import com.cleevio.cinemax.api.module.productcomposition.service.ProductCompositionService
import com.cleevio.cinemax.api.util.createBranch
import com.cleevio.cinemax.api.util.createProduct
import com.cleevio.cinemax.api.util.createProductCategory
import com.cleevio.cinemax.api.util.createProductComponent
import com.cleevio.cinemax.api.util.createProductComponentCategory
import com.cleevio.cinemax.api.util.createProductComposition
import com.cleevio.cinemax.api.util.mapToCreateOrUpdateProductCategoryCommand
import com.cleevio.cinemax.api.util.mapToCreateOrUpdateProductComponentCommand
import com.cleevio.cinemax.api.util.mapToCreateOrUpdateProductCompositionCommand
import com.cleevio.cinemax.api.util.mapToInitBasketCommand
import com.cleevio.cinemax.api.util.mapToSyncCreateOrUpdateProductCommand
import io.mockk.Runs
import io.mockk.every
import io.mockk.just
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import java.math.BigDecimal
import kotlin.test.assertTrue

class ProductDiscountPriceServiceIT @Autowired constructor(
    private val underTest: ProductDiscountPriceService,
    private val basketService: BasketService,
    private val productService: ProductService,
    private val basketItemJpaFinderService: BasketItemJpaFinderService,
    private val productCategoryService: ProductCategoryService,
    private val productCompositionService: ProductCompositionService,
    private val productComponentService: ProductComponentService,
    private val productComponentCategoryRepository: ProductComponentCategoryRepository,
    private val branchRepository: BranchRepository,
) : IntegrationTest(DatabaseCleanup.AFTER_EACH) {

    @BeforeEach
    fun setUp() {
        branchRepository.save(createBranch())

        setOf(PRODUCT_CATEGORY_1, PRODUCT_CATEGORY_2, PRODUCT_CATEGORY_3, PRODUCT_CATEGORY_4).forEach {
            productCategoryService.syncCreateOrUpdateProductCategory(mapToCreateOrUpdateProductCategoryCommand(it))
        }
        setOf(
            PRODUCT_1_POPCORN to PRODUCT_CATEGORY_1.id,
            PRODUCT_2_MARGOT to PRODUCT_CATEGORY_2.id,
            PRODUCT_3_PACKAGE_DEPOSIT to PRODUCT_CATEGORY_3.id,
            PRODUCT_4_PERCENTAGE_DISCOUNT_FROM_CARD to PRODUCT_CATEGORY_4.id,
            PRODUCT_5_ABSOLUTE_DISCOUNT_2_EURO to PRODUCT_CATEGORY_4.id,
            PRODUCT_6_ABSOLUTE_DISCOUNT_20_EURO to PRODUCT_CATEGORY_4.id,
            PRODUCT_7_PERCENTAGE_DISCOUNT_NON_CARD to PRODUCT_CATEGORY_4.id
        ).forEach {
            productService.syncCreateOrUpdateProduct(mapToSyncCreateOrUpdateProductCommand(it.first, it.second))
        }

        productComponentCategoryRepository.save(PRODUCT_COMPONENT_CATEGORY_1)
        setOf(
            PRODUCT_COMPONENT_1,
            PRODUCT_COMPONENT_2,
            PRODUCT_COMPONENT_3
        ).forEach {
            productComponentService.syncCreateOrUpdateProductComponent(
                mapToCreateOrUpdateProductComponentCommand(it)
            )
        }

        setOf(
            PRODUCT_COMPOSITION_1,
            PRODUCT_COMPOSITION_2,
            PRODUCT_COMPOSITION_3
        ).forEach {
            productCompositionService.createOrUpdateProductComposition(
                mapToCreateOrUpdateProductCompositionCommand(it)
            )
        }

        every { applicationEventPublisherMock.publishEvent(any<ProductBasketItemAddedToBasketEvent>()) } just Runs
    }

    @Test
    fun `test determineProductDiscountPrice - products, percentage discount card discount applied - should determine discount price`() {
        val basket = basketService.initBasket(
            mapToInitBasketCommand(
                items = listOf(
                    PRODUCT_BASKET_ITEM_REQUEST_1,
                    PRODUCT_BASKET_ITEM_REQUEST_2,
                    PRODUCT_BASKET_ITEM_REQUEST_4
                )
            )
        )

        val basketItems = basketItemJpaFinderService.findAllByBasketId(basket.id)
        val discountPrice = underTest.determineProductDiscountPrice(
            DetermineProductDiscountPriceCommand(
                basketItems = basketItems,
                productDiscountPercentage = PRODUCT_4_PERCENTAGE_DISCOUNT_FROM_CARD.discountPercentage,
                productDiscountTitle = PRODUCT_4_PERCENTAGE_DISCOUNT_FROM_CARD.title,
                productDiscountPrice = PRODUCT_4_PERCENTAGE_DISCOUNT_FROM_CARD.price
            )
        )

        assertTrue(BigDecimal.valueOf(1.2).negate() isEqualTo discountPrice)
    }

    @Test
    fun `test determineProductDiscountPrice - products with package deposit, percentage discount card discount applied - should determine discount price`() {
        val basket = basketService.initBasket(
            mapToInitBasketCommand(
                items = listOf(
                    PRODUCT_BASKET_ITEM_REQUEST_1,
                    PRODUCT_BASKET_ITEM_REQUEST_2,
                    PRODUCT_BASKET_ITEM_REQUEST_3,
                    PRODUCT_BASKET_ITEM_REQUEST_4
                )
            )
        )

        val basketItems = basketItemJpaFinderService.findAllByBasketId(basket.id)
        val discountPrice = underTest.determineProductDiscountPrice(
            DetermineProductDiscountPriceCommand(
                basketItems = basketItems,
                productDiscountPercentage = PRODUCT_4_PERCENTAGE_DISCOUNT_FROM_CARD.discountPercentage,
                productDiscountTitle = PRODUCT_4_PERCENTAGE_DISCOUNT_FROM_CARD.title,
                productDiscountPrice = PRODUCT_4_PERCENTAGE_DISCOUNT_FROM_CARD.price
            )
        )

        assertTrue(BigDecimal.valueOf(1.2).negate() isEqualTo discountPrice)
    }

    @Test
    fun `test determineProductDiscountPrice - product and one absolute discount, percentage discount applied - should determine discount price`() {
        val basket = basketService.initBasket(
            mapToInitBasketCommand(
                items = listOf(
                    PRODUCT_BASKET_ITEM_REQUEST_1,
                    PRODUCT_BASKET_ITEM_REQUEST_5,
                    PRODUCT_BASKET_ITEM_REQUEST_7
                )
            )
        )

        val basketItems = basketItemJpaFinderService.findAllByBasketId(basket.id)
        val discountPrice = underTest.determineProductDiscountPrice(
            DetermineProductDiscountPriceCommand(
                basketItems = basketItems,
                productDiscountPercentage = PRODUCT_7_PERCENTAGE_DISCOUNT_NON_CARD.discountPercentage,
                productDiscountTitle = PRODUCT_7_PERCENTAGE_DISCOUNT_NON_CARD.title,
                productDiscountPrice = PRODUCT_7_PERCENTAGE_DISCOUNT_NON_CARD.price
            )
        )

        assertTrue(BigDecimal.valueOf(0.4).negate() isEqualTo discountPrice)
    }

    @Test
    fun `test determineProductDiscountPrice - product and one percentage discount, absolute discount lesser than products total price applied - should determine discount price`() {
        val basket = basketService.initBasket(
            mapToInitBasketCommand(
                items = listOf(
                    PRODUCT_BASKET_ITEM_REQUEST_1,
                    PRODUCT_BASKET_ITEM_REQUEST_4,
                    PRODUCT_BASKET_ITEM_REQUEST_5
                )
            )
        )

        val basketItems = basketItemJpaFinderService.findAllByBasketId(basket.id)
        val discountPrice = underTest.determineProductDiscountPrice(
            DetermineProductDiscountPriceCommand(
                basketItems = basketItems,
                productDiscountPercentage = PRODUCT_5_ABSOLUTE_DISCOUNT_2_EURO.discountPercentage,
                productDiscountTitle = PRODUCT_5_ABSOLUTE_DISCOUNT_2_EURO.title,
                productDiscountPrice = PRODUCT_5_ABSOLUTE_DISCOUNT_2_EURO.price
            )
        )

        assertTrue(BigDecimal.valueOf(2).negate() isEqualTo discountPrice)
    }

    @Test
    fun `test determineProductDiscountPrice - product and one percentage discount, absolute discount greater than products total price applied - should determine discount price`() {
        val basket = basketService.initBasket(
            mapToInitBasketCommand(
                items = listOf(
                    PRODUCT_BASKET_ITEM_REQUEST_1,
                    PRODUCT_BASKET_ITEM_REQUEST_4,
                    PRODUCT_BASKET_ITEM_REQUEST_6
                )
            )
        )

        val basketItems = basketItemJpaFinderService.findAllByBasketId(basket.id)
        val discountPrice = underTest.determineProductDiscountPrice(
            DetermineProductDiscountPriceCommand(
                basketItems = basketItems,
                productDiscountPercentage = PRODUCT_6_ABSOLUTE_DISCOUNT_20_EURO.discountPercentage,
                productDiscountTitle = PRODUCT_6_ABSOLUTE_DISCOUNT_20_EURO.title,
                productDiscountPrice = PRODUCT_6_ABSOLUTE_DISCOUNT_20_EURO.price
            )
        )

        assertTrue(BigDecimal.valueOf(4).negate() isEqualTo discountPrice)
    }
}

private val PRODUCT_CATEGORY_1 = createProductCategory(
    originalId = 1,
    code = "01",
    title = "Popcorn nachos",
    type = ProductCategoryType.PRODUCT,
    order = 10,
    taxRate = STANDARD_TAX_RATE
)
private val PRODUCT_CATEGORY_2 = createProductCategory(
    originalId = 2,
    code = "02",
    title = "Pochúťky",
    type = ProductCategoryType.PRODUCT,
    order = 1,
    taxRate = REDUCED_TAX_RATE
)
private val PRODUCT_CATEGORY_3 = createProductCategory(
    originalId = 3,
    code = "03",
    title = "Zaloha",
    type = ProductCategoryType.PRODUCT,
    order = 13,
    taxRate = NO_TAX_RATE
)
private val PRODUCT_CATEGORY_4 = createProductCategory(
    originalId = 4,
    code = "04",
    title = "zlava",
    type = ProductCategoryType.DISCOUNT,
    order = 15,
    taxRate = STANDARD_TAX_RATE
)
private val PRODUCT_1_POPCORN = createProduct(
    originalId = 1,
    productCategoryId = PRODUCT_CATEGORY_1.id,
    title = "Popcorn XXL",
    order = 23,
    type = ProductType.PRODUCT,
    price = BigDecimal.valueOf(4),
    stockQuantityThreshold = 10
)
private val PRODUCT_2_MARGOT = createProduct(
    originalId = 2,
    code = "02X",
    productCategoryId = PRODUCT_CATEGORY_2.id,
    title = "Margot guličky",
    price = BigDecimal.valueOf(2)
)
private val PRODUCT_3_PACKAGE_DEPOSIT = createProduct(
    originalId = 3,
    code = "03X",
    productCategoryId = PRODUCT_CATEGORY_3.id,
    title = "Záloha za obal",
    type = ProductType.PRODUCT,
    price = BigDecimal.valueOf(0.15),
    isPackagingDeposit = true
)
private val PRODUCT_4_PERCENTAGE_DISCOUNT_FROM_CARD = createProduct(
    originalId = 4,
    code = "04X",
    productCategoryId = PRODUCT_CATEGORY_4.id,
    title = "VIP karta -20%",
    type = ProductType.PRODUCT,
    price = BigDecimal.ZERO,
    discountPercentage = 20
)
private val PRODUCT_5_ABSOLUTE_DISCOUNT_2_EURO = createProduct(
    originalId = 5,
    code = "05X",
    productCategoryId = PRODUCT_CATEGORY_4.id,
    title = "Sleva €2",
    type = ProductType.ADDITIONAL_SALE,
    price = BigDecimal.valueOf(2)
)
private val PRODUCT_6_ABSOLUTE_DISCOUNT_20_EURO = createProduct(
    originalId = 6,
    code = "06X",
    productCategoryId = PRODUCT_CATEGORY_4.id,
    title = "Sleva €20",
    type = ProductType.ADDITIONAL_SALE,
    price = BigDecimal.valueOf(20)
)
private val PRODUCT_7_PERCENTAGE_DISCOUNT_NON_CARD = createProduct(
    originalId = 7,
    code = "07X",
    productCategoryId = PRODUCT_CATEGORY_4.id,
    title = "Sleva 10%",
    type = ProductType.PRODUCT,
    price = BigDecimal.ZERO,
    discountPercentage = 10
)
private val PRODUCT_COMPONENT_CATEGORY_1 = createProductComponentCategory()
private val PRODUCT_COMPONENT_1 = createProductComponent(
    originalId = 1,
    code = "01",
    title = "Popcorn XXL",
    unit = ProductComponentUnit.KS,
    stockQuantity = BigDecimal.valueOf(125),
    productComponentCategoryId = PRODUCT_COMPONENT_CATEGORY_1.id
)
private val PRODUCT_COMPONENT_2 = createProductComponent(
    originalId = 2,
    code = "02",
    title = "Margot gulicky",
    unit = ProductComponentUnit.KG,
    stockQuantity = BigDecimal.valueOf(180.0),
    productComponentCategoryId = PRODUCT_COMPONENT_CATEGORY_1.id
)
private val PRODUCT_COMPONENT_3 = createProductComponent(
    originalId = 3,
    code = "03",
    title = "Zaloha za obal",
    unit = ProductComponentUnit.KS,
    stockQuantity = BigDecimal.valueOf(1),
    productComponentCategoryId = PRODUCT_COMPONENT_CATEGORY_1.id
)
private val PRODUCT_COMPOSITION_1 = createProductComposition(
    originalId = 1,
    productId = PRODUCT_1_POPCORN.id,
    productComponentId = PRODUCT_COMPONENT_1.id,
    amount = BigDecimal.valueOf(1)
)
private val PRODUCT_COMPOSITION_2 = createProductComposition(
    originalId = 2,
    productId = PRODUCT_2_MARGOT.id,
    productComponentId = PRODUCT_COMPONENT_2.id,
    amount = BigDecimal.valueOf(1)
)
private val PRODUCT_COMPOSITION_3 = createProductComposition(
    originalId = 3,
    productId = PRODUCT_3_PACKAGE_DEPOSIT.id,
    productComponentId = PRODUCT_COMPONENT_3.id,
    amount = BigDecimal.valueOf(1)
)
private val PRODUCT_BASKET_ITEM_REQUEST_1 = CreateBasketItemRequest(
    type = BasketItemType.PRODUCT,
    quantity = 1,
    product = CreateProductRequest(
        productId = PRODUCT_1_POPCORN.id
    )
)
private val PRODUCT_BASKET_ITEM_REQUEST_2 = CreateBasketItemRequest(
    type = BasketItemType.PRODUCT,
    quantity = 1,
    product = CreateProductRequest(
        productId = PRODUCT_2_MARGOT.id
    )
)
private val PRODUCT_BASKET_ITEM_REQUEST_3 = CreateBasketItemRequest(
    type = BasketItemType.PRODUCT,
    quantity = 1,
    product = CreateProductRequest(
        productId = PRODUCT_3_PACKAGE_DEPOSIT.id
    )
)
private val PRODUCT_BASKET_ITEM_REQUEST_4 = CreateBasketItemRequest(
    type = BasketItemType.PRODUCT_DISCOUNT,
    quantity = 1,
    product = CreateProductRequest(
        productId = PRODUCT_4_PERCENTAGE_DISCOUNT_FROM_CARD.id
    )
)
private val PRODUCT_BASKET_ITEM_REQUEST_5 = CreateBasketItemRequest(
    type = BasketItemType.PRODUCT_DISCOUNT,
    quantity = 1,
    product = CreateProductRequest(
        productId = PRODUCT_5_ABSOLUTE_DISCOUNT_2_EURO.id
    )
)
private val PRODUCT_BASKET_ITEM_REQUEST_6 = CreateBasketItemRequest(
    type = BasketItemType.PRODUCT_DISCOUNT,
    quantity = 1,
    product = CreateProductRequest(
        productId = PRODUCT_6_ABSOLUTE_DISCOUNT_20_EURO.id
    )
)
private val PRODUCT_BASKET_ITEM_REQUEST_7 = CreateBasketItemRequest(
    type = BasketItemType.PRODUCT_DISCOUNT,
    quantity = 1,
    product = CreateProductRequest(
        productId = PRODUCT_7_PERCENTAGE_DISCOUNT_NON_CARD.id
    )
)
