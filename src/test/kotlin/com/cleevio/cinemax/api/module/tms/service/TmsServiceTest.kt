package com.cleevio.cinemax.api.module.tms.service

import Session
import SessionAttributes
import Sessions
import TmsScheduleDataXmlModel
import com.cleevio.cinemax.api.module.tms.service.query.AdminGetTmsScheduleDataQueryService
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import org.junit.jupiter.api.Test
import kotlin.test.assertEquals

class TmsServiceTest {

    private val adminGetTmsScheduleDataQueryService = mockk<AdminGetTmsScheduleDataQueryService>()
    private val underTest = TmsService(
        adminGetTmsScheduleDataQueryService = adminGetTmsScheduleDataQueryService
    )

    @Test
    fun `test getTmsScheduleData - should call query service and return export result model`() {
        val tmsScheduleData = TmsScheduleDataXmlModel(
            sessions = Sessions(
                sessionList = listOf(
                    Session(
                        id = "142976",
                        screenIdentifier = "X",
                        start = "202502241900",
                        end = "202502242141",
                        featureDuration = 161,
                        title = "Čierny Panther: Navždy Wakanda IMAX 3D (SD)",
                        rating = "12",
                        seatsAvailable = 264,
                        seatsSold = 1,
                        sessionAttributes = SessionAttributes(
                            attributes = listOf(
                                "CinemArt SK s.r.o.",
                                "12",
                                "3D"
                            )
                        ),
                        complexIdentifier = "CINEMAX Bory"
                    )
                )
            )
        )

        every { adminGetTmsScheduleDataQueryService() } returns tmsScheduleData

        val result = underTest.getTmsScheduleData()

        assertEquals(
            EXPECTED_TMS_SCHEDULE_XML,
            String(result.inputStream.readAllBytes()).trimIndent()
        )

        verify {
            adminGetTmsScheduleDataQueryService()
        }
    }
}

private val EXPECTED_TMS_SCHEDULE_XML = """
<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<AAMPos xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="AAMPos.xsd">
  <sessions>
    <session>
      <id>142976</id>
      <screen_identifier>X</screen_identifier>
      <start>202502241900</start>
      <end>202502242141</end>
      <feature_duration>161</feature_duration>
      <title>Cierny Panther: Navzdy Wakanda IMAX 3D (SD)</title>
      <rating>12</rating>
      <seats_available>264</seats_available>
      <seats_sold>1</seats_sold>
      <session_attributes>
        <session_attribute>CinemArt SK s.r.o.</session_attribute>
        <session_attribute>12</session_attribute>
        <session_attribute>3D</session_attribute>
      </session_attributes>
      <complex_identifier>CINEMAX Bory</complex_identifier>
    </session>
  </sessions>
</AAMPos>
""".trimIndent()
