package com.cleevio.cinemax.api.module.basket.service

import com.cleevio.cinemax.api.DatabaseCleanup
import com.cleevio.cinemax.api.IntegrationTest
import com.cleevio.cinemax.api.common.constant.PaymentType
import com.cleevio.cinemax.api.module.auditorium.service.AuditoriumService
import com.cleevio.cinemax.api.module.auditoriumlayout.service.AuditoriumLayoutRepository
import com.cleevio.cinemax.api.module.basket.event.ProductBasketItemAddedToBasketEvent
import com.cleevio.cinemax.api.module.basket.model.productDiscountItems
import com.cleevio.cinemax.api.module.basket.model.productItems
import com.cleevio.cinemax.api.module.basket.service.command.CreateBasketItemInput
import com.cleevio.cinemax.api.module.basket.service.command.CreateProductInput
import com.cleevio.cinemax.api.module.basket.service.command.CreateReservationInput
import com.cleevio.cinemax.api.module.basket.service.command.CreateTicketInput
import com.cleevio.cinemax.api.module.basket.service.command.CreateTicketPriceInput
import com.cleevio.cinemax.api.module.basket.service.command.GenerateBasketReceiptNumbersCommand
import com.cleevio.cinemax.api.module.basket.service.command.InitBasketCommand
import com.cleevio.cinemax.api.module.basket.service.command.InitiateBasketPaymentCommand
import com.cleevio.cinemax.api.module.basketitem.constant.BasketItemType
import com.cleevio.cinemax.api.module.basketitem.service.BasketItemService
import com.cleevio.cinemax.api.module.basketitem.service.command.DetermineProductReceiptNumberCommand
import com.cleevio.cinemax.api.module.branch.service.BranchRepository
import com.cleevio.cinemax.api.module.distributor.service.DistributorService
import com.cleevio.cinemax.api.module.movie.service.MovieService
import com.cleevio.cinemax.api.module.posconfiguration.constant.ProductMode
import com.cleevio.cinemax.api.module.posconfiguration.constant.TablesType
import com.cleevio.cinemax.api.module.posconfiguration.service.PosConfigurationService
import com.cleevio.cinemax.api.module.posconfiguration.service.command.CreateOrUpdatePosConfigurationCommand
import com.cleevio.cinemax.api.module.pricecategory.service.PriceCategoryService
import com.cleevio.cinemax.api.module.pricecategoryitem.service.PriceCategoryItemService
import com.cleevio.cinemax.api.module.product.constant.ProductType
import com.cleevio.cinemax.api.module.product.service.ProductService
import com.cleevio.cinemax.api.module.productcategory.constant.ProductCategoryType
import com.cleevio.cinemax.api.module.productcategory.service.ProductCategoryService
import com.cleevio.cinemax.api.module.productcomponent.constant.ProductComponentUnit
import com.cleevio.cinemax.api.module.productcomponent.service.ProductComponentService
import com.cleevio.cinemax.api.module.productcomponentcategory.service.ProductComponentCategoryRepository
import com.cleevio.cinemax.api.module.productcomposition.service.ProductCompositionService
import com.cleevio.cinemax.api.module.screening.service.ScreeningService
import com.cleevio.cinemax.api.module.screeningfee.service.ScreeningFeeService
import com.cleevio.cinemax.api.module.seat.service.SeatService
import com.cleevio.cinemax.api.module.ticket.service.TicketJooqFinderService
import com.cleevio.cinemax.api.util.createAuditorium
import com.cleevio.cinemax.api.util.createAuditoriumLayout
import com.cleevio.cinemax.api.util.createBranch
import com.cleevio.cinemax.api.util.createDistributor
import com.cleevio.cinemax.api.util.createMovie
import com.cleevio.cinemax.api.util.createPriceCategory
import com.cleevio.cinemax.api.util.createPriceCategoryItem
import com.cleevio.cinemax.api.util.createProduct
import com.cleevio.cinemax.api.util.createProductCategory
import com.cleevio.cinemax.api.util.createProductComponent
import com.cleevio.cinemax.api.util.createProductComponentCategory
import com.cleevio.cinemax.api.util.createProductComposition
import com.cleevio.cinemax.api.util.createScreeningFee
import com.cleevio.cinemax.api.util.createScreeningWithCurrentDateTime
import com.cleevio.cinemax.api.util.createSeat
import com.cleevio.cinemax.api.util.mapToCreateBasketItemCommand
import com.cleevio.cinemax.api.util.mapToCreateOrUpdateAuditoriumCommand
import com.cleevio.cinemax.api.util.mapToCreateOrUpdateDistributorCommand
import com.cleevio.cinemax.api.util.mapToCreateOrUpdateMovieCommand
import com.cleevio.cinemax.api.util.mapToCreateOrUpdatePriceCategoryItemCommand
import com.cleevio.cinemax.api.util.mapToCreateOrUpdateProductCategoryCommand
import com.cleevio.cinemax.api.util.mapToCreateOrUpdateProductComponentCommand
import com.cleevio.cinemax.api.util.mapToCreateOrUpdateProductCompositionCommand
import com.cleevio.cinemax.api.util.mapToCreateOrUpdateScreeningCommand
import com.cleevio.cinemax.api.util.mapToCreateOrUpdateScreeningFeeCommand
import com.cleevio.cinemax.api.util.mapToCreateOrUpdateSeatCommand
import com.cleevio.cinemax.api.util.mapToSyncCreateOrUpdatePriceCategoryCommand
import com.cleevio.cinemax.api.util.mapToSyncCreateOrUpdateProductCommand
import io.mockk.Called
import io.mockk.Runs
import io.mockk.every
import io.mockk.just
import io.mockk.verify
import io.mockk.verifySequence
import jakarta.validation.ConstraintViolationException
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import org.springframework.beans.factory.annotation.Autowired
import java.math.BigDecimal
import java.util.UUID
import kotlin.test.assertEquals
import kotlin.test.assertNull
import kotlin.test.assertTrue

class BasketReceiptNumbersServiceIT @Autowired constructor(
    private val underTest: BasketReceiptNumbersService,
    private val basketService: BasketService,
    private val basketItemService: BasketItemService,
    private val auditoriumService: AuditoriumService,
    private val movieService: MovieService,
    private val screeningService: ScreeningService,
    private val seatService: SeatService,
    private val screeningFeeService: ScreeningFeeService,
    private val priceCategoryService: PriceCategoryService,
    private val priceCategoryItemService: PriceCategoryItemService,
    private val productService: ProductService,
    private val productCategoryService: ProductCategoryService,
    private val productComponentService: ProductComponentService,
    private val productCompositionService: ProductCompositionService,
    private val posConfigurationService: PosConfigurationService,
    private val ticketJooqFinderService: TicketJooqFinderService,
    private val basketJpaFinderService: BasketJpaFinderService,
    private val distributorService: DistributorService,
    private val auditoriumLayoutRepository: AuditoriumLayoutRepository,
    private val productComponentCategoryRepository: ProductComponentCategoryRepository,
    private val branchRepository: BranchRepository,
) : IntegrationTest(DatabaseCleanup.AFTER_EACH) {

    @BeforeEach
    fun setUp() {
        branchRepository.save(createBranch())
        auditoriumService.syncCreateOrUpdateAuditorium(mapToCreateOrUpdateAuditoriumCommand(AUDITORIUM_1))
        auditoriumLayoutRepository.save(AUDITORIUM_LAYOUT)
        distributorService.syncCreateOrUpdateDistributor(mapToCreateOrUpdateDistributorCommand(DISTRIBUTOR_1))
        movieService.syncCreateOrUpdateMovie(mapToCreateOrUpdateMovieCommand(MOVIE_1))
        priceCategoryService.syncCreateOrUpdatePriceCategory(mapToSyncCreateOrUpdatePriceCategoryCommand(PRICE_CATEGORY_1))
        screeningService.syncCreateOrUpdateScreening(mapToCreateOrUpdateScreeningCommand(SCREENING_1))
        setOf(SEAT_1, SEAT_2).forEach {
            seatService.createOrUpdateSeat(mapToCreateOrUpdateSeatCommand(it))
        }
        priceCategoryItemService.createOrUpdatePriceCategoryItem(
            mapToCreateOrUpdatePriceCategoryItemCommand(PRICE_CATEGORY_ITEM_1, PRICE_CATEGORY_1.id)
        )
        screeningFeeService.createOrUpdateScreeningFee(mapToCreateOrUpdateScreeningFeeCommand(SCREENING_FEE_1))
        setOf(PRODUCT_CATEGORY_1, PRODUCT_CATEGORY_2).forEach {
            productCategoryService.syncCreateOrUpdateProductCategory(mapToCreateOrUpdateProductCategoryCommand(it))
        }
        setOf(PRODUCT_1, PRODUCT_2, PRODUCT_3).forEach {
            productService.syncCreateOrUpdateProduct(mapToSyncCreateOrUpdateProductCommand(it, PRODUCT_CATEGORY_1.id))
        }
        productComponentCategoryRepository.save(PRODUCT_COMPONENT_CATEGORY_1)
        productComponentService.syncCreateOrUpdateProductComponent(
            mapToCreateOrUpdateProductComponentCommand(PRODUCT_COMPONENT_1)
        )
        setOf(PRODUCT_COMPOSITION_1, PRODUCT_COMPOSITION_2, PRODUCT_COMPOSITION_3).forEach {
            productCompositionService.createOrUpdateProductComposition(mapToCreateOrUpdateProductCompositionCommand(it))
        }
        posConfigurationService.createOrUpdatePosConfiguration(POS_CONFIGURATION_COMMAND)

        every { reservationMssqlFinderServiceMock.findByOriginalScreeningIdAndOriginalSeatId(any(), any()) } returns null
        every { productReceiptNumberGeneratorMock.generateProductReceiptNumber() } returns
            PRODUCT_RECEIPT_NUMBER_1 andThen
            PRODUCT_RECEIPT_NUMBER_2 andThen
            PRODUCT_RECEIPT_NUMBER_3
        every { applicationEventPublisherMock.publishEvent(any<ProductBasketItemAddedToBasketEvent>()) } just Runs
    }

    @Test
    fun `test generateBasketReceiptNumbers - 2 tickets, 2 products, all without receipt number - should generate`() {
        val basket = basketService.initBasket(
            InitBasketCommand(
                items = listOf(
                    CREATE_BASKET_ITEM_REQUEST_TICKET_1,
                    CREATE_BASKET_ITEM_REQUEST_TICKET_2,
                    CREATE_BASKET_ITEM_REQUEST_PRODUCT_1,
                    CREATE_BASKET_ITEM_REQUEST_PRODUCT_2
                )
            )
        )

        basketService.initiateBasketPayment(
            InitiateBasketPaymentCommand(
                basketId = basket.id,
                posConfigurationId = POS_CONFIGURATION_ID,
                paymentType = PaymentType.CASH
            )
        )

        underTest.generateBasketReceiptNumbers(
            GenerateBasketReceiptNumbersCommand(basketId = basket.id)
        )

        val tickets = ticketJooqFinderService.findAll().sortedBy { it.createdAt }
        assertEquals(2, tickets.size)
        assertEquals(TICKET_RECEIPT_NUMBER_1, tickets[0].receiptNumber)
        assertEquals(TICKET_RECEIPT_NUMBER_2, tickets[1].receiptNumber)

        val productBasketItems = basketJpaFinderService.getWithItemsById(basket.id).productItems().sortedBy { it.createdAt }
        assertEquals(2, productBasketItems.size)
        assertEquals(PRODUCT_RECEIPT_NUMBER_1, productBasketItems[0].productReceiptNumber)
        assertEquals(PRODUCT_RECEIPT_NUMBER_1, productBasketItems[1].productReceiptNumber)

        verify(exactly = 1) { productReceiptNumberGeneratorMock.generateProductReceiptNumber() }
    }

    @Test
    fun `test generateBasketReceiptNumbers - 2 tickets without receipt number - should generate`() {
        val basket = basketService.initBasket(
            InitBasketCommand(
                items = listOf(
                    CREATE_BASKET_ITEM_REQUEST_TICKET_1,
                    CREATE_BASKET_ITEM_REQUEST_TICKET_2
                )
            )
        )

        basketService.initiateBasketPayment(
            InitiateBasketPaymentCommand(
                basketId = basket.id,
                posConfigurationId = POS_CONFIGURATION_ID,
                paymentType = PaymentType.CASH
            )
        )

        underTest.generateBasketReceiptNumbers(
            GenerateBasketReceiptNumbersCommand(basketId = basket.id)
        )

        val tickets = ticketJooqFinderService.findAll().sortedBy { it.createdAt }
        assertEquals(2, tickets.size)
        assertEquals(TICKET_RECEIPT_NUMBER_1, tickets[0].receiptNumber)
        assertEquals(TICKET_RECEIPT_NUMBER_2, tickets[1].receiptNumber)

        val productBasketItems = basketJpaFinderService.getWithItemsById(basket.id).productItems().sortedBy { it.createdAt }
        assertTrue(productBasketItems.isEmpty())

        verify(exactly = 0) { productReceiptNumberGeneratorMock.generateProductReceiptNumber() }
    }

    @Test
    fun `test generateBasketReceiptNumbers - 2 products without receipt number - should generate`() {
        val basket = basketService.initBasket(
            InitBasketCommand(
                items = listOf(
                    CREATE_BASKET_ITEM_REQUEST_PRODUCT_1,
                    CREATE_BASKET_ITEM_REQUEST_PRODUCT_2
                )
            )
        )

        basketService.initiateBasketPayment(
            InitiateBasketPaymentCommand(
                basketId = basket.id,
                posConfigurationId = POS_CONFIGURATION_ID,
                paymentType = PaymentType.CASH
            )
        )

        underTest.generateBasketReceiptNumbers(
            GenerateBasketReceiptNumbersCommand(basketId = basket.id)
        )

        val tickets = ticketJooqFinderService.findAll().sortedBy { it.createdAt }
        assertTrue(tickets.isEmpty())

        val productBasketItems = basketJpaFinderService.getWithItemsById(basket.id).productItems().sortedBy { it.createdAt }
        assertEquals(2, productBasketItems.size)
        assertEquals(PRODUCT_RECEIPT_NUMBER_1, productBasketItems[0].productReceiptNumber)
        assertEquals(PRODUCT_RECEIPT_NUMBER_1, productBasketItems[1].productReceiptNumber)

        verify(exactly = 1) { productReceiptNumberGeneratorMock.generateProductReceiptNumber() }
    }

    @Test
    fun `test generateBasketReceiptNumbers - 2 tickets, 2 products, one of each without receipt number - should generate`() {
        val basket = basketService.initBasket(
            InitBasketCommand(
                items = listOf(
                    CREATE_BASKET_ITEM_REQUEST_TICKET_1,
                    CREATE_BASKET_ITEM_REQUEST_PRODUCT_1
                )
            )
        )

        basketService.initiateBasketPayment(
            InitiateBasketPaymentCommand(
                basketId = basket.id,
                posConfigurationId = POS_CONFIGURATION_ID,
                paymentType = PaymentType.CASH
            )
        )

        underTest.generateBasketReceiptNumbers(
            GenerateBasketReceiptNumbersCommand(basketId = basket.id)
        )

        val tickets = ticketJooqFinderService.findAll().sortedBy { it.createdAt }
        assertEquals(1, tickets.size)
        assertEquals(TICKET_RECEIPT_NUMBER_1, tickets[0].receiptNumber)

        val productBasketItems = basketJpaFinderService.getWithItemsById(basket.id).productItems().sortedBy { it.createdAt }
        assertEquals(1, productBasketItems.size)
        assertEquals(PRODUCT_RECEIPT_NUMBER_1, productBasketItems[0].productReceiptNumber)

        basketItemService.createBasketItem(
            mapToCreateBasketItemCommand(CREATE_BASKET_ITEM_REQUEST_TICKET_2, basket.id)
        )
        basketItemService.createBasketItem(
            mapToCreateBasketItemCommand(CREATE_BASKET_ITEM_REQUEST_PRODUCT_2, basket.id)
        )

        underTest.generateBasketReceiptNumbers(
            GenerateBasketReceiptNumbersCommand(basketId = basket.id)
        )

        val updatedTickets = ticketJooqFinderService.findAll().sortedBy { it.createdAt }
        assertEquals(2, updatedTickets.size)
        assertEquals(TICKET_RECEIPT_NUMBER_1, updatedTickets[0].receiptNumber)
        assertEquals(TICKET_RECEIPT_NUMBER_2, updatedTickets[1].receiptNumber)

        val updatedProductBasketItems = basketJpaFinderService.getWithItemsById(basket.id).productItems().sortedBy { it.createdAt }
        assertEquals(2, updatedProductBasketItems.size)
        assertEquals(PRODUCT_RECEIPT_NUMBER_1, updatedProductBasketItems[0].productReceiptNumber)
        assertEquals(PRODUCT_RECEIPT_NUMBER_1, updatedProductBasketItems[1].productReceiptNumber)

        verify(exactly = 1) { productReceiptNumberGeneratorMock.generateProductReceiptNumber() }
    }

    @Test
    fun `test determineProductReceiptNumber - 2 product items without receipt number - should generate receipt number`() {
        val basket = basketService.initBasket(
            InitBasketCommand(
                items = listOf(
                    CREATE_BASKET_ITEM_REQUEST_PRODUCT_1,
                    CREATE_BASKET_ITEM_REQUEST_PRODUCT_2
                )
            )
        )

        basketService.initiateBasketPayment(
            InitiateBasketPaymentCommand(
                basketId = basket.id,
                posConfigurationId = POS_CONFIGURATION_ID,
                paymentType = PaymentType.CASH
            )
        )

        val productBasketItems = basketJpaFinderService.getWithItemsById(basket.id).productItems()
        assertEquals(2, productBasketItems.size)

        val receiptNumber = underTest.determineProductReceiptNumber(
            DetermineProductReceiptNumberCommand(productItems = productBasketItems)
        )

        assertEquals(PRODUCT_RECEIPT_NUMBER_1, receiptNumber)
        verify(exactly = 1) { productReceiptNumberGeneratorMock.generateProductReceiptNumber() }
    }

    @Test
    fun `test determineProductReceiptNumber - 2 product items, 1 without receipt number - receipt number is generated once`() {
        val basket = basketService.initBasket(
            InitBasketCommand(
                items = listOf(
                    CREATE_BASKET_ITEM_REQUEST_PRODUCT_1
                )
            )
        )

        basketService.initiateBasketPayment(
            InitiateBasketPaymentCommand(
                basketId = basket.id,
                posConfigurationId = POS_CONFIGURATION_ID,
                paymentType = PaymentType.CASH
            )
        )

        underTest.generateBasketReceiptNumbers(
            GenerateBasketReceiptNumbersCommand(basketId = basket.id)
        )

        basketItemService.createBasketItem(
            mapToCreateBasketItemCommand(CREATE_BASKET_ITEM_REQUEST_PRODUCT_2, basket.id)
        )

        val productBasketItems = basketJpaFinderService.getWithItemsById(basket.id).productItems().sortedBy { it.createdAt }
        assertEquals(PRODUCT_RECEIPT_NUMBER_1, productBasketItems[0].productReceiptNumber)
        assertNull(productBasketItems[1].productReceiptNumber)

        val receiptNumber = underTest.determineProductReceiptNumber(
            DetermineProductReceiptNumberCommand(productItems = productBasketItems)
        )

        assertEquals(PRODUCT_RECEIPT_NUMBER_1, receiptNumber)
        verify(exactly = 1) { productReceiptNumberGeneratorMock.generateProductReceiptNumber() }
    }

    @Test
    fun `test determineProductReceiptNumber - product discount only - should skip receipt numbers generation`() {
        val basket = basketService.initBasket(
            InitBasketCommand(
                items = listOf(
                    CREATE_BASKET_ITEM_REQUEST_PRODUCT_DISCOUNT_1
                )
            )
        )

        basketService.initiateBasketPayment(
            InitiateBasketPaymentCommand(
                basketId = basket.id,
                posConfigurationId = POS_CONFIGURATION_ID,
                paymentType = PaymentType.CASH
            )
        )

        underTest.generateBasketReceiptNumbers(
            GenerateBasketReceiptNumbersCommand(basketId = basket.id)
        )

        val productBasketItems = basketJpaFinderService.getWithItemsById(basket.id).productDiscountItems().sortedBy { it.createdAt }
        assertNull(productBasketItems[0].productReceiptNumber)

        verifySequence {
            reservationMssqlFinderServiceMock wasNot Called
            productReceiptNumberGeneratorMock wasNot Called
        }
    }

    @Test
    fun `test determineProductReceiptNumber - empty collection sent in command - should throw exception`() {
        assertThrows<ConstraintViolationException> {
            underTest.determineProductReceiptNumber(
                DetermineProductReceiptNumberCommand(productItems = listOf())
            )
        }
    }
}

private const val PRODUCT_RECEIPT_NUMBER_1 = "1234567890"
private const val PRODUCT_RECEIPT_NUMBER_2 = "1234567891"
private const val PRODUCT_RECEIPT_NUMBER_3 = "1234567892"
private const val TICKET_RECEIPT_NUMBER_1 = "1000000001"
private const val TICKET_RECEIPT_NUMBER_2 = "1000000002"

private val POS_CONFIGURATION_ID = UUID.randomUUID()
private val DISTRIBUTOR_1 = createDistributor()
private val AUDITORIUM_1 = createAuditorium()
private val AUDITORIUM_LAYOUT = createAuditoriumLayout(auditoriumId = AUDITORIUM_1.id)
private val MOVIE_1 = createMovie(distributorId = DISTRIBUTOR_1.id)
private val PRICE_CATEGORY_1 = createPriceCategory()
private val PRICE_CATEGORY_ITEM_1 = createPriceCategoryItem(
    priceCategoryId = PRICE_CATEGORY_1.id
)
private val SCREENING_1 = createScreeningWithCurrentDateTime(
    auditoriumId = AUDITORIUM_1.id,
    auditoriumLayoutId = AUDITORIUM_LAYOUT.id,
    movieId = MOVIE_1.id,
    priceCategoryId = PRICE_CATEGORY_1.id
)
private val SCREENING_FEE_1 = createScreeningFee(
    originalScreeningId = 1,
    screeningId = SCREENING_1.id,
    surchargeVip = BigDecimal.ZERO,
    surchargePremium = BigDecimal.ZERO,
    surchargeImax = BigDecimal.ZERO,
    surchargeUltraX = BigDecimal.ZERO,
    serviceFeeVip = BigDecimal.ZERO,
    serviceFeePremium = BigDecimal.ZERO,
    serviceFeeImax = BigDecimal.ZERO,
    serviceFeeUltraX = BigDecimal.ZERO,
    surchargeDBox = BigDecimal.ZERO,
    serviceFeeGeneral = BigDecimal.ZERO
)
private val SEAT_1 = createSeat(
    originalId = 1,
    auditoriumId = AUDITORIUM_1.id,
    auditoriumLayoutId = AUDITORIUM_LAYOUT.id
)
private val SEAT_2 = createSeat(
    originalId = 2,
    auditoriumId = AUDITORIUM_1.id,
    auditoriumLayoutId = AUDITORIUM_LAYOUT.id
)
private val PRODUCT_CATEGORY_1 = createProductCategory(
    originalId = 1,
    code = "A1",
    title = "Popcorn"
)
private val PRODUCT_CATEGORY_2 = createProductCategory(
    originalId = 2,
    code = "B3",
    title = "Zlavy",
    type = ProductCategoryType.DISCOUNT
)
private val PRODUCT_1 = createProduct(
    originalId = 1,
    code = "01",
    productCategoryId = PRODUCT_CATEGORY_1.id,
    title = "Popcorn XXL",
    price = BigDecimal.TEN
)
private val PRODUCT_2 = createProduct(
    originalId = 2,
    code = "02",
    productCategoryId = PRODUCT_CATEGORY_1.id,
    title = "Popcorn XL",
    price = BigDecimal.TEN
)
private val PRODUCT_3 = createProduct(
    originalId = 3,
    code = "03",
    productCategoryId = PRODUCT_CATEGORY_2.id,
    title = "VIP karta -20%",
    type = ProductType.PRODUCT,
    price = BigDecimal.ZERO,
    discountPercentage = 20
)
private val CREATE_BASKET_ITEM_REQUEST_TICKET_1 = CreateBasketItemInput(
    type = BasketItemType.TICKET,
    quantity = 1,
    ticket = CreateTicketInput(
        ticketPrice = CreateTicketPriceInput(
            priceCategoryItemNumber = PRICE_CATEGORY_ITEM_1.number
        ),
        reservation = CreateReservationInput(
            seatId = SEAT_1.id
        ),
        screeningId = SCREENING_1.id
    )
)
private val CREATE_BASKET_ITEM_REQUEST_TICKET_2 = CREATE_BASKET_ITEM_REQUEST_TICKET_1.copy(
    ticket = CreateTicketInput(
        ticketPrice = CreateTicketPriceInput(
            priceCategoryItemNumber = PRICE_CATEGORY_ITEM_1.number
        ),
        reservation = CreateReservationInput(
            seatId = SEAT_2.id
        ),
        screeningId = SCREENING_1.id
    )
)
private val CREATE_BASKET_ITEM_REQUEST_PRODUCT_1 = CreateBasketItemInput(
    type = BasketItemType.PRODUCT,
    quantity = 2,
    product = CreateProductInput(
        productId = PRODUCT_1.id
    )
)
private val CREATE_BASKET_ITEM_REQUEST_PRODUCT_2 = CREATE_BASKET_ITEM_REQUEST_PRODUCT_1.copy(
    product = CreateProductInput(
        productId = PRODUCT_2.id
    )
)
private val CREATE_BASKET_ITEM_REQUEST_PRODUCT_DISCOUNT_1 = CreateBasketItemInput(
    type = BasketItemType.PRODUCT_DISCOUNT,
    quantity = 1,
    product = CreateProductInput(
        productId = PRODUCT_3.id
    )
)
private val PRODUCT_COMPONENT_CATEGORY_1 = createProductComponentCategory()
private val PRODUCT_COMPONENT_1 = createProductComponent(
    originalId = 1,
    code = "01",
    title = "Kukurica",
    unit = ProductComponentUnit.KG,
    stockQuantity = BigDecimal.valueOf(180.0),
    productComponentCategoryId = PRODUCT_COMPONENT_CATEGORY_1.id
)
private val PRODUCT_COMPOSITION_1 = createProductComposition(
    originalId = 1,
    productId = PRODUCT_1.id,
    productComponentId = PRODUCT_COMPONENT_1.id,
    amount = BigDecimal.valueOf(0.25)
)
private val PRODUCT_COMPOSITION_2 = createProductComposition(
    originalId = 2,
    productId = PRODUCT_2.id,
    productComponentId = PRODUCT_COMPONENT_1.id,
    amount = BigDecimal.valueOf(0.20)
)
private val PRODUCT_COMPOSITION_3 = createProductComposition(
    originalId = 3,
    productId = PRODUCT_3.id,
    productComponentId = PRODUCT_COMPONENT_1.id,
    amount = BigDecimal.valueOf(1.00)
)
private val POS_CONFIGURATION_COMMAND = CreateOrUpdatePosConfigurationCommand(
    id = POS_CONFIGURATION_ID,
    macAddress = "AA-BB-CC-DD-EE",
    title = "dummyPOS",
    receiptsDirectory = "/dummy/directory",
    ticketSalesEnabled = false,
    productModes = setOf(ProductMode.STANDARD),
    tablesType = TablesType.NO_TABLES,
    seatsEnabled = false
)
