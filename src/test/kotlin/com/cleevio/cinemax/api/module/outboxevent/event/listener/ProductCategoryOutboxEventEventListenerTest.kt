package com.cleevio.cinemax.api.module.outboxevent.event.listener

import com.cleevio.cinemax.api.module.outboxevent.constant.OutboxEventType
import com.cleevio.cinemax.api.module.outboxevent.service.OutboxEventService
import com.cleevio.cinemax.api.module.outboxevent.service.command.CreateOutboxEventCommand
import com.cleevio.cinemax.api.module.productcategory.event.ProductCategoryCreatedOrUpdatedEvent
import io.mockk.every
import io.mockk.just
import io.mockk.mockk
import io.mockk.runs
import io.mockk.verify
import org.junit.jupiter.api.Test
import java.util.UUID

class ProductCategoryOutboxEventEventListenerTest {

    private val outboxEventService = mockk<OutboxEventService>()
    private val underTest = ProductCategoryOutboxEventEventListener(outboxEventService)

    @Test
    fun `test listenToProductCategoryCreatedOrUpdatedEvent - should correctly handle event`() {
        every { outboxEventService.createOutboxEvent(any()) } just runs

        val productCategoryId = UUID.fromString("65aa8e3f-4b86-4eb3-b2e9-5b1482021eda")
        underTest.listenToProductCategoryCreatedOrUpdatedEvent(
            ProductCategoryCreatedOrUpdatedEvent(productCategoryId)
        )

        verify {
            outboxEventService.createOutboxEvent(
                CreateOutboxEventCommand(
                    entityId = productCategoryId,
                    type = OutboxEventType.PRODUCT_CATEGORY_CREATED_OR_UPDATED
                )
            )
        }
    }
}
