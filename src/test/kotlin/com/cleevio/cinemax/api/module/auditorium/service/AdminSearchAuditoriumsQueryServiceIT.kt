package com.cleevio.cinemax.api.module.auditorium.service

import com.cleevio.cinemax.api.DatabaseCleanup
import com.cleevio.cinemax.api.IntegrationTest
import com.cleevio.cinemax.api.module.auditorium.service.query.AdminSearchAuditoriumsQuery
import com.cleevio.cinemax.api.module.auditoriumdefault.service.AuditoriumDefaultRepository
import com.cleevio.cinemax.api.module.auditoriumlayout.service.AuditoriumLayoutRepository
import com.cleevio.cinemax.api.truncatedToSeconds
import com.cleevio.cinemax.api.util.createAuditorium
import com.cleevio.cinemax.api.util.createAuditoriumDefault
import com.cleevio.cinemax.api.util.createAuditoriumLayout
import com.cleevio.cinemax.psql.tables.AuditoriumColumnNames
import org.junit.jupiter.api.Test
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.Arguments
import org.junit.jupiter.params.provider.MethodSource
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.data.domain.PageRequest
import org.springframework.data.domain.Sort
import java.util.UUID
import java.util.stream.Stream
import kotlin.test.assertEquals

class AdminSearchAuditoriumsQueryServiceIT @Autowired constructor(
    private val auditoriumRepository: AuditoriumRepository,
    private val auditoriumLayoutRepository: AuditoriumLayoutRepository,
    private val auditoriumDefaultRepository: AuditoriumDefaultRepository,
    private val underTest: AdminSearchAuditoriumsQueryService,
) : IntegrationTest(DatabaseCleanup.AFTER_EACH) {

    @Test
    fun `test AdminSearchAuditoriumsQuery - should correctly return page of search auditorium responses`() {
        auditoriumRepository.saveAll(listOf(AUDITORIUM_1, AUDITORIUM_2))
        auditoriumLayoutRepository.saveAll(
            listOf(
                AUDITORIUM_LAYOUT_1,
                AUDITORIUM_LAYOUT_2,
                AUDITORIUM_LAYOUT_3,
                AUDITORIUM_LAYOUT_4
            )
        )
        auditoriumDefaultRepository.saveAll(listOf(AUDITORIUM_DEFAULT_1, AUDITORIUM_DEFAULT_2))

        val result = underTest(
            AdminSearchAuditoriumsQuery(
                pageable = PageRequest.of(0, 10, Sort.by(AuditoriumColumnNames.CODE))
            )
        )

        assertEquals(1, result.totalPages)
        assertEquals(2, result.totalElements)
        assertEquals(2, result.content.size)

        result.content[0].let {
            assertEquals(AUDITORIUM_2.id, it.id)
            assertEquals(AUDITORIUM_2.title, it.title)
            assertEquals(AUDITORIUM_2.capacity, it.capacity)
            assertEquals(AUDITORIUM_2.createdAt.truncatedToSeconds(), it.createdAt.truncatedToSeconds())
            assertEquals(AUDITORIUM_2.updatedAt.truncatedToSeconds(), it.updatedAt.truncatedToSeconds())
            assertEquals(AUDITORIUM_DEFAULT_2.id, it.default.id)
            assertEquals(AUDITORIUM_DEFAULT_2.surchargeVip, it.default.surchargeVip)
            assertEquals(AUDITORIUM_DEFAULT_2.surchargePremium, it.default.surchargePremium)
            assertEquals(AUDITORIUM_DEFAULT_2.surchargeImax, it.default.surchargeImax)
            assertEquals(AUDITORIUM_DEFAULT_2.surchargeUltraX, it.default.surchargeUltraX)
            assertEquals(AUDITORIUM_DEFAULT_2.serviceFeeVip, it.default.serviceFeeVip)
            assertEquals(AUDITORIUM_DEFAULT_2.serviceFeePremium, it.default.serviceFeePremium)
            assertEquals(AUDITORIUM_DEFAULT_2.serviceFeeImax, it.default.serviceFeeImax)
            assertEquals(AUDITORIUM_DEFAULT_2.serviceFeeUltraX, it.default.serviceFeeUltraX)
            assertEquals(AUDITORIUM_DEFAULT_2.surchargeDBox, it.default.surchargeDBox)
            assertEquals(AUDITORIUM_DEFAULT_2.proCommission, it.default.proCommission)
            assertEquals(AUDITORIUM_DEFAULT_2.filmFondCommission, it.default.filmFondCommission)
            assertEquals(AUDITORIUM_DEFAULT_2.distributorCommission, it.default.distributorCommission)
            assertEquals(AUDITORIUM_DEFAULT_2.saleTimeLimit, it.default.saleTimeLimit)
            assertEquals(AUDITORIUM_DEFAULT_2.publishOnline, it.default.publishOnline)
            assertEquals(1, it.layouts.size)
            it.layouts[0].let { layout ->
                assertEquals(AUDITORIUM_LAYOUT_3.id, layout.id)
                assertEquals(AUDITORIUM_LAYOUT_3.code, layout.code)
                assertEquals(AUDITORIUM_LAYOUT_3.title, layout.title)
            }
        }

        result.content[1].let {
            assertEquals(AUDITORIUM_1.id, it.id)
            assertEquals(AUDITORIUM_1.code, it.title)
            assertEquals(AUDITORIUM_1.capacity, it.capacity)
            assertEquals(AUDITORIUM_1.createdAt.truncatedToSeconds(), it.createdAt.truncatedToSeconds())
            assertEquals(AUDITORIUM_1.updatedAt.truncatedToSeconds(), it.updatedAt.truncatedToSeconds())
            assertEquals(AUDITORIUM_DEFAULT_1.id, it.default.id)
            assertEquals(AUDITORIUM_DEFAULT_1.surchargeVip, it.default.surchargeVip)
            assertEquals(AUDITORIUM_DEFAULT_1.surchargePremium, it.default.surchargePremium)
            assertEquals(AUDITORIUM_DEFAULT_1.surchargeImax, it.default.surchargeImax)
            assertEquals(AUDITORIUM_DEFAULT_1.surchargeUltraX, it.default.surchargeUltraX)
            assertEquals(AUDITORIUM_DEFAULT_1.serviceFeeVip, it.default.serviceFeeVip)
            assertEquals(AUDITORIUM_DEFAULT_1.serviceFeePremium, it.default.serviceFeePremium)
            assertEquals(AUDITORIUM_DEFAULT_1.serviceFeeImax, it.default.serviceFeeImax)
            assertEquals(AUDITORIUM_DEFAULT_1.serviceFeeUltraX, it.default.serviceFeeUltraX)
            assertEquals(AUDITORIUM_DEFAULT_1.surchargeDBox, it.default.surchargeDBox)
            assertEquals(AUDITORIUM_DEFAULT_1.proCommission, it.default.proCommission)
            assertEquals(AUDITORIUM_DEFAULT_1.filmFondCommission, it.default.filmFondCommission)
            assertEquals(AUDITORIUM_DEFAULT_1.distributorCommission, it.default.distributorCommission)
            assertEquals(AUDITORIUM_DEFAULT_1.saleTimeLimit, it.default.saleTimeLimit)
            assertEquals(AUDITORIUM_DEFAULT_1.publishOnline, it.default.publishOnline)
            assertEquals(2, it.layouts.size)
            it.layouts[0].let { layout ->
                assertEquals(AUDITORIUM_LAYOUT_1.id, layout.id)
                assertEquals(AUDITORIUM_LAYOUT_1.code, layout.code)
                assertEquals(AUDITORIUM_LAYOUT_1.title, layout.title)
            }
            it.layouts[1].let { layout ->
                assertEquals(AUDITORIUM_LAYOUT_2.id, layout.id)
                assertEquals(AUDITORIUM_LAYOUT_2.code, layout.code)
                assertEquals(AUDITORIUM_LAYOUT_2.title, layout.title)
            }
        }
    }

    @ParameterizedTest
    @MethodSource("auditoriumSortingParametersProvider")
    fun `test AdminSearchAuditoriumsQuery - should correctly sort by auditorium property`(
        expectedOrder: List<UUID>,
        sortProperty: List<String>,
        direction: Sort.Direction,
    ) {
        auditoriumRepository.saveAll(listOf(AUDITORIUM_1, AUDITORIUM_2))
        auditoriumLayoutRepository.saveAll(
            listOf(
                AUDITORIUM_LAYOUT_1,
                AUDITORIUM_LAYOUT_2,
                AUDITORIUM_LAYOUT_3,
                AUDITORIUM_LAYOUT_4
            )
        )
        auditoriumDefaultRepository.saveAll(listOf(AUDITORIUM_DEFAULT_1, AUDITORIUM_DEFAULT_2))

        val result = underTest(
            query = AdminSearchAuditoriumsQuery(
                pageable = PageRequest.of(0, 10, Sort.by(direction, *sortProperty.toTypedArray()))
            )
        )

        assertEquals(1, result.totalPages)
        assertEquals(2, result.totalElements)
        assertEquals(expectedOrder.size, result.content.size)
        assertEquals(expectedOrder, result.content.map { it.id })
    }

    companion object {
        @JvmStatic
        fun auditoriumSortingParametersProvider(): Stream<Arguments> {
            return Stream.of(
                Arguments.of(
                    listOf(AUDITORIUM_2.id, AUDITORIUM_1.id),
                    listOf(AuditoriumColumnNames.TITLE),
                    Sort.Direction.DESC
                ),
                Arguments.of(
                    listOf(AUDITORIUM_1.id, AUDITORIUM_2.id),
                    listOf(AuditoriumColumnNames.CAPACITY),
                    Sort.Direction.ASC
                ),
                Arguments.of(
                    listOf(AUDITORIUM_2.id, AUDITORIUM_1.id),
                    listOf("auditorium.layouts"),
                    Sort.Direction.ASC
                )
            )
        }
    }
}

private val AUDITORIUM_1 = createAuditorium(originalId = 1, code = "IMAX", title = "IMAX", capacity = 100)
private val AUDITORIUM_2 = createAuditorium(originalId = 2, code = "B", title = "Sal B", capacity = 150)
private val AUDITORIUM_LAYOUT_1 = createAuditoriumLayout(
    originalId = 1,
    code = "01",
    auditoriumId = AUDITORIUM_1.id,
    title = "IMAX 3D"
)
private val AUDITORIUM_LAYOUT_2 = createAuditoriumLayout(
    originalId = 2,
    code = "02",
    auditoriumId = AUDITORIUM_1.id,
    title = "Dolby Atmos"
)
private val AUDITORIUM_LAYOUT_3 = createAuditoriumLayout(
    originalId = 3,
    code = "03",
    auditoriumId = AUDITORIUM_2.id,
    title = "Dolby Atmos"
)
private val AUDITORIUM_LAYOUT_4 = createAuditoriumLayout(
    originalId = 4,
    code = "04",
    auditoriumId = AUDITORIUM_2.id,
    title = "4DX"
).also { it.markDeleted() }

private val AUDITORIUM_DEFAULT_1 = createAuditoriumDefault(
    originalId = 1,
    auditoriumId = AUDITORIUM_1.id,
    surchargeVip = 1.00.toBigDecimal(),
    surchargePremium = 2.00.toBigDecimal(),
    surchargeImax = 3.00.toBigDecimal(),
    surchargeUltraX = 4.00.toBigDecimal(),
    serviceFeeVip = 5.00.toBigDecimal(),
    serviceFeePremium = 6.00.toBigDecimal(),
    serviceFeeImax = 7.00.toBigDecimal(),
    serviceFeeUltraX = 8.00.toBigDecimal(),
    surchargeDBox = 9.00.toBigDecimal(),
    proCommission = 2,
    filmFondCommission = 3,
    distributorCommission = 50,
    saleTimeLimit = 25,
    publishOnline = true
)
private val AUDITORIUM_DEFAULT_2 = createAuditoriumDefault(
    originalId = 2,
    auditoriumId = AUDITORIUM_2.id,
    surchargeVip = 10.00.toBigDecimal(),
    surchargePremium = 20.00.toBigDecimal(),
    surchargeImax = 30.00.toBigDecimal(),
    surchargeUltraX = 40.00.toBigDecimal(),
    serviceFeeVip = 50.00.toBigDecimal(),
    serviceFeePremium = 60.00.toBigDecimal(),
    serviceFeeImax = 70.00.toBigDecimal(),
    serviceFeeUltraX = 80.00.toBigDecimal(),
    surchargeDBox = 90.00.toBigDecimal(),
    proCommission = 5,
    filmFondCommission = 10,
    distributorCommission = 100,
    saleTimeLimit = 50,
    publishOnline = false
)
