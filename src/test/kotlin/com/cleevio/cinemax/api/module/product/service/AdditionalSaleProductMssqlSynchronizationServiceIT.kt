package com.cleevio.cinemax.api.module.product.service

import com.cleevio.cinemax.api.MssqlIntegrationTest
import com.cleevio.cinemax.api.common.constant.STANDARD_TAX_RATE
import com.cleevio.cinemax.api.common.util.isEqualTo
import com.cleevio.cinemax.api.module.product.constant.ProductType
import com.cleevio.cinemax.api.module.product.service.command.SyncCreateOrUpdateProductCommand
import com.cleevio.cinemax.api.module.productcategory.constant.ProductCategoryType
import com.cleevio.cinemax.api.module.synchronizationfrommssql.constant.SynchronizationFromMssqlType
import com.cleevio.cinemax.api.module.synchronizationfrommssql.entity.SynchronizationFromMssql
import com.cleevio.cinemax.api.module.synchronizationfrommssql.service.command.CreateOrUpdateSynchronizationFromMssqlCommand
import com.cleevio.cinemax.api.util.createProduct
import com.cleevio.cinemax.api.util.createProductCategory
import io.mockk.Called
import io.mockk.every
import io.mockk.verify
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.test.context.jdbc.Sql
import org.springframework.test.context.jdbc.SqlConfig
import org.springframework.test.context.jdbc.SqlGroup
import java.math.BigDecimal
import java.time.LocalDateTime
import kotlin.test.assertEquals
import kotlin.test.assertTrue

@SqlGroup(
    Sql(
        scripts = [
            "/db/mssql-buffet/test/init_mssql_additional_sale_product.sql"
        ],
        config = SqlConfig(
            dataSource = "mssqlBuffetDataSource",
            transactionManager = "mssqlBuffetTransactionManager"
        )
    ),
    Sql(
        scripts = [
            "/db/mssql-buffet/test/drop_mssql_additional_sale_product.sql"
        ],
        config = SqlConfig(
            dataSource = "mssqlBuffetDataSource",
            transactionManager = "mssqlBuffetTransactionManager"
        ),
        executionPhase = Sql.ExecutionPhase.AFTER_TEST_METHOD
    )
)
class AdditionalSaleProductMssqlSynchronizationServiceIT @Autowired constructor(
    private val underTest: AdditionalSaleProductMssqlSynchronizationService,
) : MssqlIntegrationTest() {

    @Test
    fun `test synchronize all - no PSQL product, 6 MSSQL products - should create 4 products`() {
        every { productServiceMock.syncCreateOrUpdateProduct(any()) } returns DUMMY_PRODUCT
        every { synchronizationFromMssqlFinderServiceMock.findByType(any()) } returns null
        every {
            productCategoryJooqFinderServiceMock.findNonDeletedByOriginalCode(PRODUCT_CATEGORY_1.code)
        } returns PRODUCT_CATEGORY_1
        every {
            productCategoryJooqFinderServiceMock.findNonDeletedByOriginalCode(PRODUCT_CATEGORY_2.code)
        } returns PRODUCT_CATEGORY_2
        every {
            productCategoryJooqFinderServiceMock.findNonDeletedByOriginalCode(PRODUCT_CATEGORY_3.code)
        } returns PRODUCT_CATEGORY_3
        every { synchronizationFromMssqlServiceMock.createOrUpdate(any()) } returns SYNCHRONIZATION_FROM_MSSQL

        underTest.synchronizeAll()

        val originalProductCategoryCodeCaptor = mutableListOf<String>()
        val commandCaptor = mutableListOf<SyncCreateOrUpdateProductCommand>()

        verify { synchronizationFromMssqlFinderServiceMock.findByType(SynchronizationFromMssqlType.ADDITIONAL_SALE_PRODUCT) }
        verify { productCategoryJooqFinderServiceMock.findNonDeletedByOriginalCode(capture(originalProductCategoryCodeCaptor)) }
        verify { productServiceMock.syncCreateOrUpdateProduct(capture(commandCaptor)) }
        verify {
            synchronizationFromMssqlServiceMock.createOrUpdate(
                CreateOrUpdateSynchronizationFromMssqlCommand(
                    type = SynchronizationFromMssqlType.ADDITIONAL_SALE_PRODUCT,
                    lastSynchronization = PRODUCT_3_UPDATED_AT
                )
            )
        }

        val productCategoryCodes = setOf(
            PRODUCT_CATEGORY_1.code,
            PRODUCT_CATEGORY_2.code,
            PRODUCT_CATEGORY_3.code
        )
        assertTrue(originalProductCategoryCodeCaptor.size == 4)
        assertTrue(originalProductCategoryCodeCaptor.containsAll(productCategoryCodes))
        assertTrue(commandCaptor.size == 4)
        assertCommandEquals(EXPECTED_COMMAND_1, commandCaptor[0])
        assertCommandEquals(EXPECTED_COMMAND_2, commandCaptor[1])
        assertCommandEquals(EXPECTED_COMMAND_3, commandCaptor[2])
        assertCommandEquals(EXPECTED_COMMAND_4, commandCaptor[3])
    }

    @Test
    fun `test synchronize all - 2 PSQL products, 6 MSSQL products - should create 1 product`() {
        every { productServiceMock.syncCreateOrUpdateProduct(any()) } returns DUMMY_PRODUCT
        every { synchronizationFromMssqlFinderServiceMock.findByType(any()) } returns PRODUCT_2_UPDATED_AT
        every { productCategoryJooqFinderServiceMock.findNonDeletedByOriginalCode(any()) } returns PRODUCT_CATEGORY_1
        every { synchronizationFromMssqlServiceMock.createOrUpdate(any()) } returns SYNCHRONIZATION_FROM_MSSQL

        underTest.synchronizeAll()

        val originalProductCategoryCodeCaptor = mutableListOf<String>()
        val commandCaptor = mutableListOf<SyncCreateOrUpdateProductCommand>()

        verify { synchronizationFromMssqlFinderServiceMock.findByType(SynchronizationFromMssqlType.ADDITIONAL_SALE_PRODUCT) }
        verify { productCategoryJooqFinderServiceMock.findNonDeletedByOriginalCode(capture(originalProductCategoryCodeCaptor)) }
        verify { productServiceMock.syncCreateOrUpdateProduct(capture(commandCaptor)) }
        verify {
            synchronizationFromMssqlServiceMock.createOrUpdate(
                CreateOrUpdateSynchronizationFromMssqlCommand(
                    type = SynchronizationFromMssqlType.ADDITIONAL_SALE_PRODUCT,
                    lastSynchronization = PRODUCT_3_UPDATED_AT
                )
            )
        }

        assertTrue(originalProductCategoryCodeCaptor.size == 1)
        assertTrue(originalProductCategoryCodeCaptor.containsAll(setOf(PRODUCT_CATEGORY_1.code)))
        assertTrue(commandCaptor.size == 1)
        assertCommandEquals(EXPECTED_COMMAND_2, commandCaptor[0])
    }

    @Test
    fun `test synchronize all - no PSQL product category - should create no product`() {
        every { productServiceMock.syncCreateOrUpdateProduct(any()) } returns DUMMY_PRODUCT
        every { synchronizationFromMssqlFinderServiceMock.findByType(any()) } returns null
        every { synchronizationFromMssqlServiceMock.createOrUpdate(any()) } returns SYNCHRONIZATION_FROM_MSSQL
        every { productCategoryJooqFinderServiceMock.findNonDeletedByOriginalCode(any()) } returns null

        underTest.synchronizeAll()

        val originalProductCategoryCodeCaptor = mutableListOf<String>()

        verify { synchronizationFromMssqlFinderServiceMock.findByType(SynchronizationFromMssqlType.ADDITIONAL_SALE_PRODUCT) }
        verify { productCategoryJooqFinderServiceMock.findNonDeletedByOriginalCode(capture(originalProductCategoryCodeCaptor)) }
        verify { productServiceMock wasNot Called }
        verify {
            synchronizationFromMssqlServiceMock.createOrUpdate(
                CreateOrUpdateSynchronizationFromMssqlCommand(
                    type = SynchronizationFromMssqlType.ADDITIONAL_SALE_PRODUCT,
                    lastSynchronization = PRODUCT_3_UPDATED_AT
                )
            )
        }

        val productCategoryCodes = setOf(PRODUCT_CATEGORY_1.code, PRODUCT_CATEGORY_2.code)
        assertTrue(originalProductCategoryCodeCaptor.size == 4)
        assertTrue(originalProductCategoryCodeCaptor.containsAll(productCategoryCodes))
    }

    private fun assertCommandEquals(expected: SyncCreateOrUpdateProductCommand, actual: SyncCreateOrUpdateProductCommand) {
        assertEquals(expected.originalId, actual.originalId)
        assertEquals(expected.originalCode, actual.originalCode)
        assertEquals(expected.productCategoryId, actual.productCategoryId)
        assertEquals(expected.title, actual.title)
        assertEquals(expected.title, actual.title)
        assertEquals(expected.type, actual.type)
        assertTrue(expected.price isEqualTo actual.price)
        assertEquals(expected.order, actual.order)
        assertEquals(expected.soldInBuffet, actual.soldInBuffet)
        assertEquals(expected.soldInCafe, actual.soldInCafe)
        assertEquals(expected.soldInVip, actual.soldInVip)
        assertEquals(expected.isPackagingDeposit, actual.isPackagingDeposit)
    }
}

private val PRODUCT_2_UPDATED_AT = LocalDateTime.of(2023, 1, 20, 15, 26, 0)
private val PRODUCT_3_UPDATED_AT = LocalDateTime.of(2023, 1, 20, 15, 35, 0)
private val PRODUCT_CATEGORY_1 = createProductCategory(
    originalId = 1,
    code = "11",
    title = "Káva čaj",
    type = ProductCategoryType.PRODUCT,
    order = 11,
    taxRate = STANDARD_TAX_RATE
)
private val PRODUCT_CATEGORY_2 = createProductCategory(
    originalId = 2,
    code = "13",
    title = "Zlava",
    type = ProductCategoryType.DISCOUNT,
    order = 13,
    taxRate = STANDARD_TAX_RATE
)
private val PRODUCT_CATEGORY_3 = createProductCategory(
    originalId = 3,
    code = "17",
    title = "Záloha",
    type = ProductCategoryType.PRODUCT,
    taxRate = 0
)
private val DUMMY_PRODUCT = createProduct(
    productCategoryId = PRODUCT_CATEGORY_1.id,
    title = "Dummy Product",
    price = BigDecimal.ONE
)
private val EXPECTED_COMMAND_1 = SyncCreateOrUpdateProductCommand(
    originalId = -20,
    originalCode = "00001",
    productCategoryId = PRODUCT_CATEGORY_1.id,
    title = "Mliecko do kávy 2cl",
    type = ProductType.ADDITIONAL_SALE,
    price = BigDecimal.valueOf(0.3),
    active = true,
    order = null,
    soldInBuffet = false,
    soldInCafe = true,
    soldInVip = false,
    priceNoVat = BigDecimal.valueOf(0.25),
    isPackagingDeposit = false
)
private val EXPECTED_COMMAND_2 = SyncCreateOrUpdateProductCommand(
    originalId = -25,
    originalCode = "00006",
    productCategoryId = PRODUCT_CATEGORY_1.id,
    title = "Cerstvé štavy 0,1l",
    type = ProductType.ADDITIONAL_SALE,
    price = BigDecimal.valueOf(1.95),
    active = false,
    order = null,
    soldInBuffet = false,
    soldInCafe = true,
    soldInVip = false,
    priceNoVat = BigDecimal.valueOf(1.63),
    isPackagingDeposit = false
)
private val EXPECTED_COMMAND_3 = SyncCreateOrUpdateProductCommand(
    originalId = -52,
    originalCode = "00026",
    productCategoryId = PRODUCT_CATEGORY_2.id,
    title = "Zlava voucher",
    type = ProductType.ADDITIONAL_SALE,
    price = BigDecimal.valueOf(100),
    active = true,
    order = null,
    soldInBuffet = true,
    soldInCafe = false,
    soldInVip = false,
    priceNoVat = BigDecimal.valueOf(83.33),
    isPackagingDeposit = false
)
private val EXPECTED_COMMAND_4 = SyncCreateOrUpdateProductCommand(
    originalId = -68,
    originalCode = "00035",
    productCategoryId = PRODUCT_CATEGORY_3.id,
    title = "Záloha za obal",
    type = ProductType.ADDITIONAL_SALE,
    price = BigDecimal.valueOf(0.15),
    active = true,
    order = null,
    soldInBuffet = true,
    soldInCafe = true,
    soldInVip = false,
    priceNoVat = BigDecimal.valueOf(0.15),
    isPackagingDeposit = true
)
private val SYNCHRONIZATION_FROM_MSSQL = SynchronizationFromMssql(
    type = SynchronizationFromMssqlType.ADDITIONAL_SALE_PRODUCT,
    lastSynchronization = LocalDateTime.MIN,
    lastHeartbeat = null
)
