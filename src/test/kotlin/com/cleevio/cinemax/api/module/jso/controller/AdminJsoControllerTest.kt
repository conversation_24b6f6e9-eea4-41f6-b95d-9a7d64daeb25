package com.cleevio.cinemax.api.module.jso.controller

import com.cleevio.cinemax.api.ControllerTest
import com.cleevio.cinemax.api.common.constant.ApiVersion
import com.cleevio.cinemax.api.common.descriptor.mssql.DescriptorMssqlResponse
import com.cleevio.cinemax.api.common.util.jsonContent
import com.cleevio.cinemax.api.module.employee.constant.EmployeeRole
import com.cleevio.cinemax.api.module.jso.entity.Jso
import com.cleevio.cinemax.api.util.mockAccessToken
import io.mockk.every
import io.mockk.verify
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest
import org.springframework.http.HttpHeaders
import org.springframework.test.web.servlet.MockMvc
import org.springframework.test.web.servlet.get

@WebMvcTest(AdminJsoController::class)
class AdminJsoControllerTest @Autowired constructor(
    private val mvc: MockMvc,
) : ControllerTest() {

    @Test
    fun `test getJsos, should serialize and deserialize correctly`() {
        every { jsoJooqFinderService.findAll() } returns listOf(JSO_1, JSO_2, JSO_3)
        every { descriptorMssqlResponseMapper.mapList(any()) } returns listOf(
            JSO_1_RESPONSE,
            JSO_2_RESPONSE,
            JSO_3_RESPONSE
        )

        mvc.get(GET_JSOS_PATH) {
            header(HttpHeaders.ACCEPT, ApiVersion.VERSION_1_JSON)
            header(HttpHeaders.AUTHORIZATION, mockAccessToken(role = EmployeeRole.MANAGER))
        }.andExpect {
            status { isOk() }
            content { contentType(ApiVersion.VERSION_1_JSON) }
            jsonContent(
                """
                [{
                  "id": "${JSO_1.id}",
                  "title": "${JSO_1.title}"
                },
                {
                  "id": "${JSO_2.id}",
                  "title": "${JSO_2.title}"
                },
                {
                  "id": "${JSO_3.id}",
                  "title": "${JSO_3.title}"
                }]
            """
            )
        }

        verify {
            descriptorMssqlResponseMapper.mapList(listOf(JSO_1, JSO_2, JSO_3))
        }
    }
}

private const val GET_JSOS_PATH = "/manager-app/jsos"
private val JSO_1 = Jso(
    originalId = 3,
    code = "S",
    title = "Strach"
)
private val JSO_2 = Jso(
    originalId = 4,
    code = "Z",
    title = "Závislosť"
)
private val JSO_3 = Jso(
    originalId = 5,
    code = "H",
    title = "Nahota"
)
private val JSO_1_RESPONSE = DescriptorMssqlResponse(
    id = JSO_1.id,
    title = JSO_1.title
)
private val JSO_2_RESPONSE = DescriptorMssqlResponse(
    id = JSO_2.id,
    title = JSO_2.title
)
private val JSO_3_RESPONSE = DescriptorMssqlResponse(
    id = JSO_3.id,
    title = JSO_3.title
)
