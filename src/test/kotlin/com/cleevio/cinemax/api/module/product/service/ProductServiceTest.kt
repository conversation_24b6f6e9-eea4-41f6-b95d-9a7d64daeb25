package com.cleevio.cinemax.api.module.product.service

import com.cleevio.cinemax.api.common.constant.STANDARD_TAX_RATE
import com.cleevio.cinemax.api.common.service.CodeGenerator
import com.cleevio.cinemax.api.module.branch.service.BranchJpaFinderService
import com.cleevio.cinemax.api.module.file.exception.FileNotFoundException
import com.cleevio.cinemax.api.module.file.service.FileJpaFinderService
import com.cleevio.cinemax.api.module.product.constant.ProductType
import com.cleevio.cinemax.api.module.product.service.command.SyncCreateOrUpdateProductCommand
import com.cleevio.cinemax.api.module.productcategory.constant.ProductCategoryType
import com.cleevio.cinemax.api.module.productcategory.exception.ProductCategoryNotFoundException
import com.cleevio.cinemax.api.module.productcategory.service.ProductCategoryJpaFinderService
import com.cleevio.cinemax.api.module.productcomponent.service.ProductComponentJpaFinderService
import com.cleevio.cinemax.api.module.productcomponent.service.ProductComponentService
import com.cleevio.cinemax.api.module.productcomposition.service.ProductCompositionService
import com.cleevio.cinemax.api.util.createProductCategory
import com.cleevio.cinemax.api.util.toUUID
import io.mockk.every
import io.mockk.mockk
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import org.springframework.context.ApplicationEventPublisher
import java.math.BigDecimal
import java.util.UUID

class ProductServiceTest {

    private val productRepository = mockk<ProductRepository>()
    private val fileJpaFinderService = mockk<FileJpaFinderService>()
    private val productCategoryJpaFinderService = mockk<ProductCategoryJpaFinderService>()
    private val productComponentService = mockk<ProductComponentService>()
    private val productCompositionService = mockk<ProductCompositionService>()
    private val productJpaFinderService = mockk<ProductJpaFinderService>()
    private val productComponentJpaFinderService = mockk<ProductComponentJpaFinderService>()
    private val codeGenerator = mockk<CodeGenerator>()
    private val applicationEventPublisher = mockk<ApplicationEventPublisher>()
    private val branchJpaFinderService = mockk<BranchJpaFinderService>()

    private val underTest = ProductService(
        productRepository = productRepository,
        productJpaFinderService = productJpaFinderService,
        productComponentService = productComponentService,
        productCategoryJpaFinderService = productCategoryJpaFinderService,
        productCompositionService = productCompositionService,
        fileJpaFinderService = fileJpaFinderService,
        codeGenerator = codeGenerator,
        applicationEventPublisher = applicationEventPublisher,
        productComponentJpaFinderService = productComponentJpaFinderService,
        branchJpaFinderService = branchJpaFinderService
    )

    @Test
    fun `test syncCreateOrUpdateProduct - command with not existing category - should throw exception`() {
        every { productCategoryJpaFinderService.getNonDeletedById(any()) } throws ProductCategoryNotFoundException()

        assertThrows<ProductCategoryNotFoundException> {
            underTest.syncCreateOrUpdateProduct(CREATE_PRODUCT_COMMAND_1)
        }
    }

    @Test
    fun `test syncCreateOrUpdateProduct - command with not existing file - should throw exception`() {
        every { productCategoryJpaFinderService.getNonDeletedById(any()) } returns PRODUCT_CATEGORY_1
        every { fileJpaFinderService.existsById(any()) } returns false

        assertThrows<FileNotFoundException> {
            underTest.syncCreateOrUpdateProduct(CREATE_PRODUCT_COMMAND_2)
        }
    }
}

private val PRODUCT_CATEGORY_1 = createProductCategory(
    originalId = 1,
    code = "01",
    title = "Nápoje",
    type = ProductCategoryType.PRODUCT,
    order = 10,
    taxRate = STANDARD_TAX_RATE
)
private val CREATE_PRODUCT_COMMAND_1 = SyncCreateOrUpdateProductCommand(
    originalId = 1,
    originalCode = "00001",
    productCategoryId = UUID.randomUUID(),
    title = "Coca Cola 0.33 l",
    type = ProductType.PRODUCT,
    price = BigDecimal.valueOf(3.5),
    priceNoVat = BigDecimal.valueOf(2.92),
    active = true,
    order = 26,
    soldInBuffet = true,
    soldInCafe = true,
    soldInVip = true,
    imageFileId = null,
    isPackagingDeposit = false
)
private val CREATE_PRODUCT_COMMAND_2 = SyncCreateOrUpdateProductCommand(
    originalId = 2,
    originalCode = "00002",
    productCategoryId = UUID.randomUUID(),
    title = "Coca Cola 0.33 l",
    type = ProductType.PRODUCT,
    price = BigDecimal.valueOf(3.5),
    priceNoVat = BigDecimal.valueOf(2.92),
    active = true,
    order = 27,
    soldInBuffet = true,
    soldInCafe = true,
    soldInVip = true,
    imageFileId = 1.toUUID(),
    isPackagingDeposit = false
)
