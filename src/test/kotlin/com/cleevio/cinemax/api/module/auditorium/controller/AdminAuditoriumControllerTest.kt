package com.cleevio.cinemax.api.module.auditorium.controller

import com.cleevio.cinemax.api.ControllerTest
import com.cleevio.cinemax.api.common.constant.ApiVersion
import com.cleevio.cinemax.api.common.util.jsonContent
import com.cleevio.cinemax.api.module.auditorium.controller.dto.AdminGetAuditoriumResponse
import com.cleevio.cinemax.api.module.auditorium.controller.dto.AdminSearchAuditoriumResponse
import com.cleevio.cinemax.api.module.auditorium.controller.dto.AuditoriumDefaultResponse
import com.cleevio.cinemax.api.module.auditorium.controller.dto.AuditoriumLayoutResponse
import com.cleevio.cinemax.api.module.auditorium.controller.dto.SearchAuditoriumDefaultResponse
import com.cleevio.cinemax.api.module.auditorium.controller.dto.SearchAuditoriumLayoutResponse
import com.cleevio.cinemax.api.module.auditorium.service.command.CreateOrUpdateAuditoriumCommand
import com.cleevio.cinemax.api.module.auditorium.service.query.AdminGetAuditoriumQuery
import com.cleevio.cinemax.api.module.auditorium.service.query.AdminSearchAuditoriumsQuery
import com.cleevio.cinemax.api.module.employee.constant.EmployeeRole
import com.cleevio.cinemax.api.truncatedAndFormatted
import com.cleevio.cinemax.api.util.createAuditorium
import com.cleevio.cinemax.api.util.createAuditoriumDefault
import com.cleevio.cinemax.api.util.createAuditoriumLayout
import com.cleevio.cinemax.api.util.mockAccessToken
import com.cleevio.cinemax.psql.tables.AuditoriumColumnNames
import io.mockk.every
import io.mockk.verify
import io.mockk.verifySequence
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest
import org.springframework.data.domain.PageImpl
import org.springframework.data.domain.PageRequest
import org.springframework.data.domain.Sort
import org.springframework.http.HttpHeaders
import org.springframework.http.MediaType
import org.springframework.test.web.servlet.MockMvc
import org.springframework.test.web.servlet.get
import org.springframework.test.web.servlet.post
import org.springframework.test.web.servlet.put
import java.util.UUID

@WebMvcTest(AdminAuditoriumController::class)
class AdminAuditoriumControllerTest @Autowired constructor(
    private val mvc: MockMvc,
) : ControllerTest() {

    @Test
    fun `test searchAuditoriums - should serialize and deserialize correctly`() {
        val auditorium = createAuditorium()
        val auditoriumDefault = createAuditoriumDefault(auditoriumId = auditorium.id)
        val auditoriumLayout1 = createAuditoriumLayout(originalId = 1, auditoriumId = auditorium.id, title = "IMAX")
        val auditoriumLayout2 = createAuditoriumLayout(originalId = 2, auditoriumId = auditorium.id, title = "3D")

        every { adminSearchAuditoriumsQueryService(any()) } returns PageImpl(
            listOf(
                AdminSearchAuditoriumResponse(
                    id = auditorium.id,
                    title = auditorium.title,
                    capacity = auditorium.capacity,
                    layouts = listOf(
                        SearchAuditoriumLayoutResponse(
                            id = auditoriumLayout1.id,
                            code = auditoriumLayout1.code,
                            title = auditoriumLayout1.title
                        ),
                        SearchAuditoriumLayoutResponse(
                            id = auditoriumLayout2.id,
                            code = auditoriumLayout2.code,
                            title = auditoriumLayout2.title
                        )
                    ),
                    default = SearchAuditoriumDefaultResponse(
                        id = auditoriumDefault.id,
                        surchargeVip = auditoriumDefault.surchargeVip,
                        surchargePremium = auditoriumDefault.surchargePremium,
                        surchargeImax = auditoriumDefault.surchargeImax,
                        surchargeUltraX = auditoriumDefault.surchargeUltraX,
                        serviceFeeVip = auditoriumDefault.serviceFeeVip,
                        serviceFeePremium = auditoriumDefault.serviceFeePremium,
                        serviceFeeImax = auditoriumDefault.serviceFeeImax,
                        serviceFeeUltraX = auditoriumDefault.serviceFeeUltraX,
                        surchargeDBox = auditoriumDefault.surchargeDBox,
                        proCommission = auditoriumDefault.proCommission,
                        filmFondCommission = auditoriumDefault.filmFondCommission,
                        distributorCommission = auditoriumDefault.distributorCommission,
                        saleTimeLimit = auditoriumDefault.saleTimeLimit,
                        publishOnline = auditoriumDefault.publishOnline
                    ),
                    createdAt = auditorium.createdAt,
                    updatedAt = auditorium.updatedAt
                )
            )
        )

        mvc.post(SEARCH_AUDITORIUMS_PATH) {
            contentType = MediaType.APPLICATION_JSON
            header(HttpHeaders.ACCEPT, ApiVersion.VERSION_1_JSON)
            header(HttpHeaders.AUTHORIZATION, mockAccessToken(role = EmployeeRole.MANAGER))
        }.andExpect {
            status { isOk() }
            content { contentType(ApiVersion.VERSION_1_JSON) }
            jsonContent(
                """
                    {
                      "content": [
                        {
                          "id": "${auditorium.id}",
                          "title": "${auditorium.title}",
                          "capacity": ${auditorium.capacity},
                          "createdAt": "${auditorium.createdAt.truncatedAndFormatted()}",
                          "updatedAt": "${auditorium.updatedAt.truncatedAndFormatted()}",
                          "layouts": [
                            {
                              "id": "${auditoriumLayout1.id}",
                              "code": "${auditoriumLayout1.code}",
                              "title": "${auditoriumLayout1.title}"
                            },
                            {
                              "id": "${auditoriumLayout2.id}",
                              "code": "${auditoriumLayout2.code}",
                              "title": "${auditoriumLayout2.title}"
                            }
                          ],
                          "default": {
                            "id": "${auditoriumDefault.id}",
                            "surchargeVip": ${auditoriumDefault.surchargeVip},
                            "surchargePremium": ${auditoriumDefault.surchargePremium},
                            "surchargeImax": ${auditoriumDefault.surchargeImax},
                            "surchargeUltraX": ${auditoriumDefault.surchargeUltraX},
                            "serviceFeeVip": ${auditoriumDefault.serviceFeeVip},
                            "serviceFeePremium": ${auditoriumDefault.serviceFeePremium},
                            "serviceFeeImax": ${auditoriumDefault.serviceFeeImax},
                            "serviceFeeUltraX": ${auditoriumDefault.serviceFeeUltraX},
                            "surchargeDBox": ${auditoriumDefault.surchargeDBox},
                            "proCommission": ${auditoriumDefault.proCommission},
                            "filmFondCommission": ${auditoriumDefault.filmFondCommission},
                            "distributorCommission": ${auditoriumDefault.distributorCommission},
                            "saleTimeLimit": ${auditoriumDefault.saleTimeLimit},
                            "publishOnline": ${auditoriumDefault.publishOnline}
                          }
                        }
                      ],
                      "totalElements": 1,
                      "totalPages": 1
                    }
                """.trimIndent()
            )
        }

        verify {
            adminSearchAuditoriumsQueryService(
                AdminSearchAuditoriumsQuery(
                    pageable = PageRequest.of(0, 10, Sort.by(AuditoriumColumnNames.CODE))
                )
            )
        }
    }

    @Test
    fun `test getAuditorium, should serialize and deserialize correctly`() {
        val auditorium = createAuditorium()
        val auditoriumDefault = createAuditoriumDefault(auditoriumId = auditorium.id)
        val auditoriumLayout1 = createAuditoriumLayout(originalId = 1, auditoriumId = auditorium.id, title = "IMAX 3D")
        val auditoriumLayout2 =
            createAuditoriumLayout(originalId = 2, auditoriumId = auditorium.id, title = "Dolby Atmos")

        every { adminGetAuditoriumQueryService(any()) } returns AdminGetAuditoriumResponse(
            id = auditorium.id,
            title = auditorium.title,
            originalCode = auditorium.originalCode,
            city = auditorium.city,
            capacity = auditorium.capacity,
            createdAt = auditorium.createdAt,
            updatedAt = auditorium.updatedAt,
            layouts = listOf(
                AuditoriumLayoutResponse(
                    id = auditoriumLayout1.id,
                    code = auditoriumLayout1.code,
                    title = auditoriumLayout1.title
                ),
                AuditoriumLayoutResponse(
                    id = auditoriumLayout2.id,
                    code = auditoriumLayout2.code,
                    title = auditoriumLayout2.title
                )
            ),
            default = AuditoriumDefaultResponse(
                id = auditoriumDefault.id,
                surchargeVip = auditoriumDefault.surchargeVip,
                surchargePremium = auditoriumDefault.surchargePremium,
                surchargeImax = auditoriumDefault.surchargeImax,
                surchargeUltraX = auditoriumDefault.surchargeUltraX,
                serviceFeeVip = auditoriumDefault.serviceFeeVip,
                serviceFeePremium = auditoriumDefault.serviceFeePremium,
                serviceFeeImax = auditoriumDefault.serviceFeeImax,
                serviceFeeUltraX = auditoriumDefault.serviceFeeUltraX,
                surchargeDBox = auditoriumDefault.surchargeDBox,
                proCommission = auditoriumDefault.proCommission,
                filmFondCommission = auditoriumDefault.filmFondCommission,
                distributorCommission = auditoriumDefault.distributorCommission,
                saleTimeLimit = auditoriumDefault.saleTimeLimit,
                publishOnline = auditoriumDefault.publishOnline
            )
        )

        mvc.get(GET_AND_UPDATE_AUDITORIUM_PATH(auditorium.id)) {
            contentType = MediaType.APPLICATION_JSON
            header(HttpHeaders.ACCEPT, ApiVersion.VERSION_1_JSON)
            header(HttpHeaders.AUTHORIZATION, mockAccessToken(role = EmployeeRole.MANAGER))
        }.andExpect {
            status { isOk() }
            content { contentType(ApiVersion.VERSION_1_JSON) }
            jsonContent(
                """
                    {
                      "id": "${auditorium.id}",
                      "title": "${auditorium.title}",
                      "originalCode": ${auditorium.originalCode},
                      "city": "${auditorium.city}",
                      "capacity": ${auditorium.capacity},
                      "createdAt": "${auditorium.createdAt.truncatedAndFormatted()}",
                      "updatedAt": "${auditorium.updatedAt.truncatedAndFormatted()}",
                      "layouts": [
                        {
                          "id": "${auditoriumLayout1.id}",
                          "code": "${auditoriumLayout1.code}",
                          "title": "${auditoriumLayout1.title}"
                        },
                        {
                          "id": "${auditoriumLayout2.id}",
                          "code": "${auditoriumLayout2.code}",
                          "title": "${auditoriumLayout2.title}"
                        }
                      ],
                      "default": {
                        "id": "${auditoriumDefault.id}",
                        "surchargeVip": ${auditoriumDefault.surchargeVip},
                        "surchargePremium": ${auditoriumDefault.surchargePremium},
                        "surchargeImax": ${auditoriumDefault.surchargeImax},
                        "surchargeUltraX": ${auditoriumDefault.surchargeUltraX},
                        "serviceFeeVip": ${auditoriumDefault.serviceFeeVip},
                        "serviceFeePremium": ${auditoriumDefault.serviceFeePremium},
                        "serviceFeeImax": ${auditoriumDefault.serviceFeeImax},
                        "serviceFeeUltraX": ${auditoriumDefault.serviceFeeUltraX},
                        "surchargeDBox": ${auditoriumDefault.surchargeDBox},
                        "proCommission": ${auditoriumDefault.proCommission},
                        "filmFondCommission": ${auditoriumDefault.filmFondCommission},
                        "distributorCommission": ${auditoriumDefault.distributorCommission},
                        "saleTimeLimit": ${auditoriumDefault.saleTimeLimit},
                        "publishOnline": ${auditoriumDefault.publishOnline}
                      }
                    }
                """.trimIndent()
            )
        }

        verifySequence {
            adminGetAuditoriumQueryService(
                query = AdminGetAuditoriumQuery(auditorium.id)
            )
        }
    }

    @Test
    fun `test updateAuditorium, should serialize and deserialize correctly`() {
        val auditoriumId = UUID.fromString("9010b470-04b9-4042-b97a-b8e50b486bd4")
        val command = CreateOrUpdateAuditoriumCommand(
            id = auditoriumId,
            originalCode = 510360,
            title = "SÁLA A - CINEMAX BRATISLAVA",
            capacity = 250,
            city = "Bratislava",
            saleTimeLimit = 25,
            surchargeVip = 1.1.toBigDecimal(),
            surchargePremium = 1.2.toBigDecimal(),
            surchargeImax = 1.3.toBigDecimal(),
            surchargeUltraX = 1.4.toBigDecimal(),
            surchargeDBox = 1.5.toBigDecimal(),
            serviceFeeVip = 1.6.toBigDecimal(),
            serviceFeePremium = 1.7.toBigDecimal(),
            serviceFeeImax = 1.8.toBigDecimal(),
            serviceFeeUltraX = 1.9.toBigDecimal(),
            proCommission = 5,
            filmFondCommission = 6,
            distributorCommission = 7,
            publishOnline = false
        )

        every { auditoriumService.adminCreateOrUpdateAuditorium(any()) } returns auditoriumId

        mvc.put(GET_AND_UPDATE_AUDITORIUM_PATH(auditoriumId)) {
            contentType = MediaType.APPLICATION_JSON
            header(HttpHeaders.ACCEPT, ApiVersion.VERSION_1_JSON)
            header(HttpHeaders.AUTHORIZATION, mockAccessToken(role = EmployeeRole.MANAGER))
            content = """
                {
                  "code": "A",
                  "title": "SÁLA A - CINEMAX BRATISLAVA",
                  "city": "Bratislava",
                  "capacity": 250,
                  "originalCode": "510360",
                  "saleTimeLimit": 25,
                  "surchargeVip": 1.1,
                  "surchargePremium": 1.2,
                  "surchargeImax": 1.3,
                  "surchargeUltraX": 1.4,
                  "surchargeDBox": 1.5,
                  "serviceFeeVip": 1.6,
                  "serviceFeePremium": 1.7,
                  "serviceFeeImax": 1.8,
                  "serviceFeeUltraX": 1.9,
                  "proCommission": 5,
                  "filmFondCommission": 6,
                  "distributorCommission": 7,
                  "publishOnline": false
                }
            """.trimIndent()
        }.andExpect {
            status { isOk() }
            content { contentType(ApiVersion.VERSION_1_JSON) }
            jsonContent(
                """
                    {
                      "id": "$auditoriumId"
                    }
                """.trimIndent()
            )
        }

        verify { auditoriumService.adminCreateOrUpdateAuditorium(command) }
    }
}

private const val BASE_AUDITORIUM_PATH = "/manager-app/auditoriums"
private const val SEARCH_AUDITORIUMS_PATH = "$BASE_AUDITORIUM_PATH/search"
private val GET_AND_UPDATE_AUDITORIUM_PATH: (UUID) -> String =
    { auditoriumId: UUID -> "/manager-app/auditoriums/$auditoriumId" }
