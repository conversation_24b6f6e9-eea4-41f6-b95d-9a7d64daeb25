package com.cleevio.cinemax.api.module.screening.controller

import com.cleevio.cinemax.api.ControllerTest
import com.cleevio.cinemax.api.common.constant.ApiVersion
import com.cleevio.cinemax.api.common.constant.ExportFormat
import com.cleevio.cinemax.api.common.service.model.ExportResultModel
import com.cleevio.cinemax.api.common.util.jsonContent
import com.cleevio.cinemax.api.module.employee.constant.EmployeeRole
import com.cleevio.cinemax.api.module.reservation.constant.ReservationState
import com.cleevio.cinemax.api.module.screening.constant.ScreeningState
import com.cleevio.cinemax.api.module.screening.controller.dto.AdminGetScreeningResponse
import com.cleevio.cinemax.api.module.screening.controller.dto.AdminSearchScreeningsResponse
import com.cleevio.cinemax.api.module.screening.controller.dto.AdminSearchScreeningsTimelineResponse
import com.cleevio.cinemax.api.module.screening.controller.dto.AuditoriumGetResponse
import com.cleevio.cinemax.api.module.screening.controller.dto.AuditoriumLayoutResponse
import com.cleevio.cinemax.api.module.screening.controller.dto.AuditoriumResponse
import com.cleevio.cinemax.api.module.screening.controller.dto.AuditoriumTimelineResponse
import com.cleevio.cinemax.api.module.screening.controller.dto.CreateScreeningRequest
import com.cleevio.cinemax.api.module.screening.controller.dto.DistributorResponse
import com.cleevio.cinemax.api.module.screening.controller.dto.FeesResponse
import com.cleevio.cinemax.api.module.screening.controller.dto.MovieGetResponse
import com.cleevio.cinemax.api.module.screening.controller.dto.MovieResponse
import com.cleevio.cinemax.api.module.screening.controller.dto.PriceCategoryItemResponse
import com.cleevio.cinemax.api.module.screening.controller.dto.PriceCategoryResponse
import com.cleevio.cinemax.api.module.screening.controller.dto.RatingResponse
import com.cleevio.cinemax.api.module.screening.controller.dto.ReservationGetResponse
import com.cleevio.cinemax.api.module.screening.controller.dto.ScreeningTypeResponse
import com.cleevio.cinemax.api.module.screening.controller.dto.SeatGetResponse
import com.cleevio.cinemax.api.module.screening.controller.dto.TechnologyResponse
import com.cleevio.cinemax.api.module.screening.controller.dto.TmsLanguageResponse
import com.cleevio.cinemax.api.module.screening.controller.dto.UpdateScreeningStatesRequest
import com.cleevio.cinemax.api.module.screening.service.command.CreateOrUpdateScreeningCommand
import com.cleevio.cinemax.api.module.screening.service.command.DeleteScreeningCommand
import com.cleevio.cinemax.api.module.screening.service.command.DuplicateScreeningCommand
import com.cleevio.cinemax.api.module.screening.service.command.DuplicateScreeningDayCommand
import com.cleevio.cinemax.api.module.screening.service.command.UpdateScreeningStatesCommand
import com.cleevio.cinemax.api.module.screening.service.query.AdminExportDistributorScreeningsFilter
import com.cleevio.cinemax.api.module.screening.service.query.AdminExportDistributorScreeningsQuery
import com.cleevio.cinemax.api.module.screening.service.query.AdminExportScreeningsFilter
import com.cleevio.cinemax.api.module.screening.service.query.AdminExportScreeningsQuery
import com.cleevio.cinemax.api.module.screening.service.query.AdminGetScreeningQuery
import com.cleevio.cinemax.api.module.screening.service.query.AdminSearchScreeningsFilter
import com.cleevio.cinemax.api.module.screening.service.query.AdminSearchScreeningsQuery
import com.cleevio.cinemax.api.module.screening.service.query.AdminSearchScreeningsTimelineFilter
import com.cleevio.cinemax.api.module.screening.service.query.AdminSearchScreeningsTimelineQuery
import com.cleevio.cinemax.api.module.seat.constant.DoubleSeatType
import com.cleevio.cinemax.api.module.seat.constant.SeatType
import com.cleevio.cinemax.api.util.mockAccessToken
import io.mockk.Runs
import io.mockk.every
import io.mockk.just
import io.mockk.runs
import io.mockk.verify
import io.mockk.verifySequence
import org.hamcrest.CoreMatchers.containsString
import org.junit.jupiter.api.Test
import org.junitpioneer.jupiter.RetryingTest
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest
import org.springframework.data.domain.PageImpl
import org.springframework.data.domain.PageRequest
import org.springframework.data.domain.Sort
import org.springframework.http.HttpHeaders
import org.springframework.http.MediaType
import org.springframework.test.web.servlet.MockMvc
import org.springframework.test.web.servlet.delete
import org.springframework.test.web.servlet.get
import org.springframework.test.web.servlet.post
import org.springframework.test.web.servlet.put
import java.io.ByteArrayInputStream
import java.math.BigDecimal
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.LocalTime
import java.util.UUID

@WebMvcTest(AdminScreeningController::class)
class AdminScreeningControllerTest @Autowired constructor(
    private val mvc: MockMvc,
) : ControllerTest() {

    @Test
    fun `test search screenings, should serialize and deserialize correctly`() {
        val response = AdminSearchScreeningsResponse(
            id = UUID.fromString("41fa16ea-c3b9-4a2b-88b0-************"),
            date = LocalDate.of(2024, 1, 26),
            time = LocalTime.of(14, 0),
            endTime = LocalTime.of(16, 30),
            ticketsSold = 100,
            saleTimeLimit = 30,
            adTimeSlot = 10,
            stopped = false,
            cancelled = false,
            state = ScreeningState.PUBLISHED,
            publishOnline = true,
            proCommission = 5,
            filmFondCommission = 10,
            distributorCommission = 15,
            createdAt = LocalDateTime.of(2022, 12, 24, 18, 15, 30),
            updatedAt = LocalDateTime.of(2023, 11, 19, 15, 18, 39),
            fees = FeesResponse(
                surchargeVip = 10.toBigDecimal(),
                surchargePremium = 5.toBigDecimal(),
                surchargeImax = 7.toBigDecimal(),
                surchargeUltraX = 8.toBigDecimal(),
                serviceFeeVip = 2.toBigDecimal(),
                serviceFeePremium = 3.toBigDecimal(),
                serviceFeeImax = 4.toBigDecimal(),
                serviceFeeUltraX = 5.toBigDecimal(),
                surchargeDBox = 6.toBigDecimal(),
                serviceFeeGeneral = 1.toBigDecimal()
            ),
            auditorium = AuditoriumResponse(
                id = UUID.fromString("54d4f4c9-6894-4526-a4e8-57bd64312d3d"),
                code = "AUD1",
                capacity = 200,
                layout = AuditoriumLayoutResponse(
                    id = UUID.fromString("f5961fb5-89bd-4f69-96f8-09f9e20a156c"),
                    title = "Standard Layout"
                )
            ),
            movie = MovieResponse(
                id = UUID.fromString("0296ed26-28b1-43e3-836a-a21016200b6b"),
                title = "Test Movie",
                rawTitle = "Test Movie Raw",
                duration = 120,
                distributor = DistributorResponse(
                    id = UUID.fromString("10ff77a3-e694-4de8-8e99-94644359a665"),
                    title = "Distributor"
                ),
                rating = RatingResponse(
                    id = UUID.fromString("3ad022bb-feff-48be-9158-0d914400d27d"),
                    title = "PG-13"
                ),
                technology = TechnologyResponse(
                    id = UUID.fromString("1eb8d0c2-9eb4-4122-b8cf-fa83d4f7b9c6"),
                    title = "3D"
                ),
                language = "EN",
                tmsLanguage = TmsLanguageResponse(
                    id = UUID.fromString("904d92b4-e003-4e66-8e71-4e12cd50413e"),
                    title = "English"
                )
            ),
            priceCategory = PriceCategoryResponse(
                id = UUID.fromString("8cc7ddc0-b473-4415-937c-27a2588a5dd8"),
                title = "Standard",
                items = listOf(
                    PriceCategoryItemResponse(
                        title = "Adult",
                        price = 10.toBigDecimal()
                    ),
                    PriceCategoryItemResponse(
                        title = "Child",
                        price = 5.toBigDecimal()
                    )
                )
            ),
            screeningTypes = listOf(
                ScreeningTypeResponse(
                    id = UUID.fromString("f9c0d282-5ef6-4d7f-ad4d-************"),
                    title = "Regular"
                )
            )
        )

        every { adminSearchScreeningsQueryService(any()) } returns PageImpl(
            listOf(
                response
            )
        )

        mvc.post("/manager-app/screenings/search") {
            contentType = MediaType.APPLICATION_JSON
            header(HttpHeaders.ACCEPT, ApiVersion.VERSION_1_JSON)
            header(HttpHeaders.AUTHORIZATION, mockAccessToken(role = EmployeeRole.MANAGER))
            content = """
                {
                  "dateFrom": "2024-05-01",
                  "dateTo": "2024-05-31",
                  "states": ["PUBLISHED"],
                  "auditoriumIds": ["41fa16ea-c3b9-4a2b-88b0-************"],
                  "movieIds": ["41fa16ea-c3b9-4a2b-88b0-************"],
                  "movieDistributorIds": ["41fa16ea-c3b9-4a2b-88b0-************"]
                }
            """.trimIndent()
        }.andExpect {
            status { isOk() }
            content { contentType(ApiVersion.VERSION_1_JSON) }
            jsonContent(
                """
                    {
                      "content": [
                        {
                          "id": "41fa16ea-c3b9-4a2b-88b0-************",
                          "date": "2024-01-26",
                          "time": "14:00:00",
                          "endTime": "16:30:00",
                          "ticketsSold": 100,
                          "saleTimeLimit": 30,
                          "adTimeSlot": 10,
                          "stopped": false,
                          "cancelled": false,
                          "state": "PUBLISHED",
                          "publishOnline": true,
                          "proCommission": 5,
                          "filmFondCommission": 10,
                          "distributorCommission": 15,
                          "createdAt": "2022-12-24T17:15:30Z",
                          "updatedAt": "2023-11-19T14:18:39Z",
                          "fees": {
                            "surchargeVip": 10,
                            "surchargePremium": 5,
                            "surchargeImax": 7,
                            "surchargeUltraX": 8,
                            "serviceFeeVip": 2,
                            "serviceFeePremium": 3,
                            "serviceFeeImax": 4,
                            "serviceFeeUltraX": 5,
                            "surchargeDBox": 6,
                            "serviceFeeGeneral": 1
                          },
                          "auditorium": {
                            "id": "54d4f4c9-6894-4526-a4e8-57bd64312d3d",
                            "code": "AUD1",
                            "capacity": 200,
                            "layout": {
                              "id": "f5961fb5-89bd-4f69-96f8-09f9e20a156c",
                              "title": "Standard Layout"
                            }
                          },
                          "movie": {
                            "id": "0296ed26-28b1-43e3-836a-a21016200b6b",
                            "title": "Test Movie",
                            "rawTitle": "Test Movie Raw",
                            "duration": 120,
                            "distributor": {
                              "id": "10ff77a3-e694-4de8-8e99-94644359a665",
                              "title": "Distributor"
                            },
                            "rating": {
                              "id": "3ad022bb-feff-48be-9158-0d914400d27d",
                              "title": "PG-13"
                            },
                            "technology": {
                              "id": "1eb8d0c2-9eb4-4122-b8cf-fa83d4f7b9c6",
                              "title": "3D"
                            },
                            "language": "EN",
                            "tmsLanguage": {
                              "id": "904d92b4-e003-4e66-8e71-4e12cd50413e",
                              "title": "English"
                            }
                          },
                          "priceCategory": {
                            "id": "8cc7ddc0-b473-4415-937c-27a2588a5dd8",
                            "title": "Standard",
                            "items": [
                              {
                                "title": "Adult",
                                "price": 10
                              },
                              {
                                "title": "Child",
                                "price": 5
                              }
                            ]
                          },
                          "screeningTypes": [
                            {
                              "id": "f9c0d282-5ef6-4d7f-ad4d-************",
                              "title": "Regular"
                            }
                          ]
                        }
                      ],
                      "totalElements": 1,
                      "totalPages": 1
                    }
                """.trimIndent()
            )
        }

        verifySequence {
            adminSearchScreeningsQueryService(
                AdminSearchScreeningsQuery(
                    pageable = PageRequest.of(
                        0,
                        10,
                        Sort.by("screening.date", "auditorium.code", "screening.time")
                    ),
                    filter = AdminSearchScreeningsFilter(
                        dateFrom = LocalDate.parse("2024-05-01"),
                        dateTo = LocalDate.parse("2024-05-31"),
                        states = setOf(ScreeningState.PUBLISHED),
                        auditoriumIds = setOf(UUID.fromString("41fa16ea-c3b9-4a2b-88b0-************")),
                        movieIds = setOf(UUID.fromString("41fa16ea-c3b9-4a2b-88b0-************")),
                        movieDistributorIds = setOf(UUID.fromString("41fa16ea-c3b9-4a2b-88b0-************"))
                    )
                )
            )
        }
    }

    @Test
    fun `test search timeline screenings, should serialize and deserialize correctly`() {
        val screeningResponse = AdminSearchScreeningsResponse(
            id = UUID.fromString("41fa16ea-c3b9-4a2b-88b0-************"),
            date = LocalDate.of(2024, 1, 26),
            time = LocalTime.of(14, 0),
            endTime = LocalTime.of(16, 30),
            ticketsSold = 100,
            saleTimeLimit = 30,
            adTimeSlot = 10,
            stopped = false,
            cancelled = false,
            state = ScreeningState.DRAFT,
            publishOnline = true,
            proCommission = 5,
            filmFondCommission = 10,
            distributorCommission = 15,
            createdAt = LocalDateTime.of(2022, 12, 24, 18, 15, 30),
            updatedAt = LocalDateTime.of(2023, 11, 19, 15, 18, 39),
            fees = FeesResponse(
                surchargeVip = 10.toBigDecimal(),
                surchargePremium = 5.toBigDecimal(),
                surchargeImax = 7.toBigDecimal(),
                surchargeUltraX = 8.toBigDecimal(),
                serviceFeeVip = 2.toBigDecimal(),
                serviceFeePremium = 3.toBigDecimal(),
                serviceFeeImax = 4.toBigDecimal(),
                serviceFeeUltraX = 5.toBigDecimal(),
                surchargeDBox = 6.toBigDecimal(),
                serviceFeeGeneral = 1.toBigDecimal()
            ),
            auditorium = AuditoriumResponse(
                id = UUID.fromString("54d4f4c9-6894-4526-a4e8-57bd64312d3d"),
                code = "AUD1",
                capacity = 200,
                layout = AuditoriumLayoutResponse(
                    id = UUID.fromString("f5961fb5-89bd-4f69-96f8-09f9e20a156c"),
                    title = "Standard Layout"
                )
            ),
            movie = MovieResponse(
                id = UUID.fromString("0296ed26-28b1-43e3-836a-a21016200b6b"),
                title = "Test Movie",
                rawTitle = "Test Movie Raw",
                duration = 120,
                distributor = DistributorResponse(
                    id = UUID.fromString("10ff77a3-e694-4de8-8e99-94644359a665"),
                    title = "Distributor"
                ),
                rating = RatingResponse(
                    id = UUID.fromString("3ad022bb-feff-48be-9158-0d914400d27d"),
                    title = "PG-13"
                ),
                technology = TechnologyResponse(
                    id = UUID.fromString("1eb8d0c2-9eb4-4122-b8cf-fa83d4f7b9c6"),
                    title = "3D"
                ),
                language = "EN",
                tmsLanguage = TmsLanguageResponse(
                    id = UUID.fromString("904d92b4-e003-4e66-8e71-4e12cd50413e"),
                    title = "English"
                )
            ),
            priceCategory = PriceCategoryResponse(
                id = UUID.fromString("8cc7ddc0-b473-4415-937c-27a2588a5dd8"),
                title = "Standard",
                items = listOf(
                    PriceCategoryItemResponse(
                        title = "Adult",
                        price = 10.toBigDecimal()
                    ),
                    PriceCategoryItemResponse(
                        title = "Child",
                        price = 5.toBigDecimal()
                    )
                )
            ),
            screeningTypes = listOf(
                ScreeningTypeResponse(
                    id = UUID.fromString("f9c0d282-5ef6-4d7f-ad4d-************"),
                    title = "Regular"
                )
            )
        )

        val response = AdminSearchScreeningsTimelineResponse(
            auditoriums = listOf(
                AuditoriumTimelineResponse(
                    id = UUID.fromString("54d4f4c9-6894-4526-a4e8-57bd64312d3d"),
                    code = "AUD1",
                    capacity = 200,
                    screenings = listOf(screeningResponse),
                    layouts = listOf(
                        AuditoriumLayoutResponse(
                            id = UUID.fromString("f5961fb5-89bd-4f69-96f8-09f9e20a156c"),
                            title = "Standard Layout"
                        ),
                        AuditoriumLayoutResponse(
                            id = UUID.fromString("8c74c0a2-7b5b-4990-b35b-71f440b23880"),
                            title = "Second Layout"
                        )
                    )
                )
            )
        )

        every { adminSearchScreeningsTimelineQueryService(any()) } returns response

        mvc.post("/manager-app/screenings/timeline-search") {
            contentType = MediaType.APPLICATION_JSON
            header(HttpHeaders.ACCEPT, ApiVersion.VERSION_1_JSON)
            header(HttpHeaders.AUTHORIZATION, mockAccessToken(role = EmployeeRole.MANAGER))
            content = """
                {
                  "date": "2024-05-01"
                }
            """.trimIndent()
        }.andExpect {
            status { isOk() }
            content { contentType(ApiVersion.VERSION_1_JSON) }
            jsonContent(
                """
                    {
                      "auditoriums": [
                        {
                          "id": "54d4f4c9-6894-4526-a4e8-57bd64312d3d",
                          "code": "AUD1",
                          "capacity": 200,
                          "screenings": [
                            {
                              "id": "41fa16ea-c3b9-4a2b-88b0-************",
                              "date": "2024-01-26",
                              "time": "14:00:00",
                              "endTime": "16:30:00",
                              "ticketsSold": 100,
                              "saleTimeLimit": 30,
                              "adTimeSlot": 10,
                              "stopped": false,
                              "cancelled": false,
                              "state": "DRAFT",
                              "publishOnline": true,
                              "proCommission": 5,
                              "filmFondCommission": 10,
                              "distributorCommission": 15,
                              "createdAt": "2022-12-24T17:15:30Z",
                              "updatedAt": "2023-11-19T14:18:39Z",
                              "fees": {
                                "surchargeVip": 10,
                                "surchargePremium": 5,
                                "surchargeImax": 7,
                                "surchargeUltraX": 8,
                                "serviceFeeVip": 2,
                                "serviceFeePremium": 3,
                                "serviceFeeImax": 4,
                                "serviceFeeUltraX": 5,
                                "surchargeDBox": 6,
                                "serviceFeeGeneral": 1
                              },
                              "auditorium": {
                                "id": "54d4f4c9-6894-4526-a4e8-57bd64312d3d",
                                "code": "AUD1",
                                "capacity": 200,
                                "layout": {
                                  "id": "f5961fb5-89bd-4f69-96f8-09f9e20a156c",
                                  "title": "Standard Layout"
                                }
                              },
                              "movie": {
                                "id": "0296ed26-28b1-43e3-836a-a21016200b6b",
                                "title": "Test Movie",
                                "rawTitle": "Test Movie Raw",
                                "duration": 120,
                                "distributor": {
                                  "id": "10ff77a3-e694-4de8-8e99-94644359a665",
                                  "title": "Distributor"
                                },
                                "rating": {
                                  "id": "3ad022bb-feff-48be-9158-0d914400d27d",
                                  "title": "PG-13"
                                },
                                "technology": {
                                  "id": "1eb8d0c2-9eb4-4122-b8cf-fa83d4f7b9c6",
                                  "title": "3D"
                                },
                                "language": "EN",
                                "tmsLanguage": {
                                  "id": "904d92b4-e003-4e66-8e71-4e12cd50413e",
                                  "title": "English"
                                }
                              },
                              "priceCategory": {
                                "id": "8cc7ddc0-b473-4415-937c-27a2588a5dd8",
                                "title": "Standard",
                                "items": [
                                  {
                                    "title": "Adult",
                                    "price": 10
                                  },
                                  {
                                    "title": "Child",
                                    "price": 5
                                  }
                                ]
                              },
                              "screeningTypes": [
                                {
                                  "id": "f9c0d282-5ef6-4d7f-ad4d-************",
                                  "title": "Regular"
                                }
                              ]
                            }
                          ],
                          "layouts": [
                            {
                              "id": "f5961fb5-89bd-4f69-96f8-09f9e20a156c",
                              "title": "Standard Layout"
                            },
                            {
                              "id": "8c74c0a2-7b5b-4990-b35b-71f440b23880",
                              "title": "Second Layout"
                            }
                          ]
                        }
                      ]
                    }
                """.trimIndent()
            )
        }

        verifySequence {
            adminSearchScreeningsTimelineQueryService(
                AdminSearchScreeningsTimelineQuery(
                    filter = AdminSearchScreeningsTimelineFilter(
                        date = LocalDate.parse("2024-05-01")
                    )
                )
            )
        }
    }

    @Test
    fun `test create screening, should serialize and deserialize correctly`() {
        val request = CreateScreeningRequest(
            movieId = UUID.fromString("0296ed26-28b1-43e3-836a-a21016200b6b"),
            auditoriumId = UUID.fromString("54d4f4c9-6894-4526-a4e8-57bd64312d3d"),
            priceCategoryId = UUID.fromString("8cc7ddc0-b473-4415-937c-27a2588a5dd8"),
            auditoriumLayoutId = UUID.fromString("f5961fb5-89bd-4f69-96f8-09f9e20a156c"),
            screeningTypeIds = setOf(UUID.fromString("f9c0d282-5ef6-4d7f-ad4d-************")),
            date = LocalDate.of(2024, 1, 26),
            time = LocalTime.of(14, 0),
            state = ScreeningState.PUBLISHED,
            saleTimeLimit = 30,
            adTimeSlot = 10,
            surchargeVip = BigDecimal(10),
            surchargePremium = BigDecimal(5),
            surchargeImax = BigDecimal(7),
            surchargeUltraX = BigDecimal(8),
            serviceFeeVip = BigDecimal(2),
            serviceFeePremium = BigDecimal(3),
            serviceFeeImax = BigDecimal(4),
            serviceFeeUltraX = BigDecimal(5),
            surchargeDBox = BigDecimal(6),
            serviceFeeGeneral = BigDecimal(1),
            proCommission = 5,
            filmFondCommission = 10,
            distributorCommission = 15,
            publishOnline = true
        )

        val command = CreateOrUpdateScreeningCommand(
            originalId = null,
            movieId = request.movieId,
            auditoriumId = request.auditoriumId,
            priceCategoryId = request.priceCategoryId,
            auditoriumLayoutId = request.auditoriumLayoutId,
            screeningTypeIds = request.screeningTypeIds,
            date = request.date,
            time = request.time,
            state = request.state,
            saleTimeLimit = request.saleTimeLimit,
            adTimeSlot = request.adTimeSlot,
            surchargeVip = request.surchargeVip,
            surchargePremium = request.surchargePremium,
            surchargeImax = request.surchargeImax,
            surchargeUltraX = request.surchargeUltraX,
            serviceFeeVip = request.serviceFeeVip,
            serviceFeePremium = request.serviceFeePremium,
            serviceFeeImax = request.serviceFeeImax,
            serviceFeeUltraX = request.serviceFeeUltraX,
            surchargeDBox = request.surchargeDBox,
            serviceFeeGeneral = request.serviceFeeGeneral,
            proCommission = request.proCommission,
            filmFondCommission = request.filmFondCommission,
            distributorCommission = request.distributorCommission,
            publishOnline = request.publishOnline,
            stopped = false,
            cancelled = false
        )

        every { screeningService.adminCreateOrUpdateScreening(any()) } just runs

        mvc.post("/manager-app/screenings") {
            contentType = MediaType.APPLICATION_JSON
            header(HttpHeaders.ACCEPT, ApiVersion.VERSION_1_JSON)
            header(HttpHeaders.AUTHORIZATION, mockAccessToken(role = EmployeeRole.MANAGER))
            content = """
                {
                  "movieId": "0296ed26-28b1-43e3-836a-a21016200b6b",
                  "auditoriumId": "54d4f4c9-6894-4526-a4e8-57bd64312d3d",
                  "priceCategoryId": "8cc7ddc0-b473-4415-937c-27a2588a5dd8",
                  "auditoriumLayoutId": "f5961fb5-89bd-4f69-96f8-09f9e20a156c",
                  "screeningTypeIds": ["f9c0d282-5ef6-4d7f-ad4d-************"],
                  "date": "2024-01-26",
                  "time": "14:00:00",
                  "state": "PUBLISHED",
                  "saleTimeLimit": 30,
                  "adTimeSlot": 10,
                  "surchargeVip": 10,
                  "surchargePremium": 5,
                  "surchargeImax": 7,
                  "surchargeUltraX": 8,
                  "serviceFeeVip": 2,
                  "serviceFeePremium": 3,
                  "serviceFeeImax": 4,
                  "serviceFeeUltraX": 5,
                  "surchargeDBox": 6,
                  "serviceFeeGeneral": 1,
                  "proCommission": 5,
                  "filmFondCommission": 10,
                  "distributorCommission": 15,
                  "publishOnline": true
                }
            """.trimIndent()
        }.andExpect {
            status { isNoContent() }
        }

        verifySequence {
            screeningService.adminCreateOrUpdateScreening(command)
        }
    }

    @Test
    fun `test update screening, should serialize and deserialize correctly`() {
        val screeningId = UUID.fromString("41fa16ea-c3b9-4a2b-88b0-************")
        val command = CreateOrUpdateScreeningCommand(
            id = screeningId,
            originalId = null,
            movieId = UUID.fromString("0296ed26-28b1-43e3-836a-a21016200b6b"),
            auditoriumId = UUID.fromString("54d4f4c9-6894-4526-a4e8-57bd64312d3d"),
            priceCategoryId = UUID.fromString("8cc7ddc0-b473-4415-937c-27a2588a5dd8"),
            auditoriumLayoutId = UUID.fromString("f5961fb5-89bd-4f69-96f8-09f9e20a156c"),
            screeningTypeIds = setOf(UUID.fromString("f9c0d282-5ef6-4d7f-ad4d-************")),
            date = LocalDate.of(2024, 1, 26),
            time = LocalTime.of(14, 0),
            state = null,
            saleTimeLimit = 30,
            adTimeSlot = 10,
            surchargeVip = BigDecimal(10),
            surchargePremium = BigDecimal(5),
            surchargeImax = BigDecimal(7),
            surchargeUltraX = BigDecimal(8),
            serviceFeeVip = BigDecimal(2),
            serviceFeePremium = BigDecimal(3),
            serviceFeeImax = BigDecimal(4),
            serviceFeeUltraX = BigDecimal(5),
            surchargeDBox = BigDecimal(6),
            serviceFeeGeneral = BigDecimal(1),
            proCommission = 5,
            filmFondCommission = 10,
            distributorCommission = 15,
            publishOnline = true,
            stopped = false,
            cancelled = false
        )

        every { screeningService.adminCreateOrUpdateScreening(any()) } just runs

        mvc.put("/manager-app/screenings/$screeningId") {
            contentType = MediaType.APPLICATION_JSON
            header(HttpHeaders.ACCEPT, ApiVersion.VERSION_1_JSON)
            header(HttpHeaders.AUTHORIZATION, mockAccessToken(role = EmployeeRole.MANAGER))
            content = """
                {
                  "movieId": "0296ed26-28b1-43e3-836a-a21016200b6b",
                  "auditoriumId": "54d4f4c9-6894-4526-a4e8-57bd64312d3d",
                  "priceCategoryId": "8cc7ddc0-b473-4415-937c-27a2588a5dd8",
                  "auditoriumLayoutId": "f5961fb5-89bd-4f69-96f8-09f9e20a156c",
                  "screeningTypeIds": ["f9c0d282-5ef6-4d7f-ad4d-************"],
                  "date": "2024-01-26",
                  "time": "14:00:00",
                  "saleTimeLimit": 30,
                  "adTimeSlot": 10,
                  "stopped": false,
                  "cancelled": false,
                  "surchargeVip": 10,
                  "surchargePremium": 5,
                  "surchargeImax": 7,
                  "surchargeUltraX": 8,
                  "serviceFeeVip": 2,
                  "serviceFeePremium": 3,
                  "serviceFeeImax": 4,
                  "serviceFeeUltraX": 5,
                  "surchargeDBox": 6,
                  "serviceFeeGeneral": 1,
                  "proCommission": 5,
                  "filmFondCommission": 10,
                  "distributorCommission": 15,
                  "publishOnline": true
                }
            """.trimIndent()
        }.andExpect {
            status { isNoContent() }
        }

        verifySequence {
            screeningService.adminCreateOrUpdateScreening(command)
        }
    }

    @Test
    fun `test duplicateScreening, should serialize and deserialize correctly`() {
        val screeningId = UUID.randomUUID()
        val auditoriumId = UUID.randomUUID()
        val time = LocalTime.of(14, 0, 0)

        every { screeningService.duplicateScreening(any()) } just Runs

        mvc.post("/manager-app/screenings/$screeningId/duplicate") {
            header(HttpHeaders.ACCEPT, ApiVersion.VERSION_1_JSON)
            header(HttpHeaders.AUTHORIZATION, mockAccessToken(role = EmployeeRole.MANAGER))
            contentType = MediaType.APPLICATION_JSON
            content = """
                {
                    "auditoriumId": "$auditoriumId",
                    "time": "14:00:00"
                }
            """.trimIndent()
        }.andExpect {
            status { isNoContent() }
        }

        verifySequence {
            screeningService.duplicateScreening(
                DuplicateScreeningCommand(
                    screeningId = screeningId,
                    auditoriumId = auditoriumId,
                    time = time
                )
            )
        }
    }

    @Test
    fun `test delete screening, should call service and return no content`() {
        val screeningId = UUID.fromString("41fa16ea-c3b9-4a2b-88b0-************")
        val command = DeleteScreeningCommand(screeningId)

        every { screeningService.deleteScreening(any()) } just runs

        mvc.delete("/manager-app/screenings/$screeningId") {
            contentType = MediaType.APPLICATION_JSON
            header(HttpHeaders.ACCEPT, ApiVersion.VERSION_1_JSON)
            header(HttpHeaders.AUTHORIZATION, mockAccessToken(role = EmployeeRole.MANAGER))
        }.andExpect {
            status { isNoContent() }
        }

        verifySequence {
            screeningService.deleteScreening(command)
        }
    }

    @Test
    fun `test duplicateScreeningDay, should serialize and deserialize correctly`() {
        val screeningDateString = "2024-01-01"
        val screeningDate = LocalDate.parse(screeningDateString)

        every { screeningService.duplicateScreeningDay(any()) } just Runs

        mvc.post("/manager-app/screenings/dates/$screeningDateString/duplicate") {
            header(HttpHeaders.ACCEPT, ApiVersion.VERSION_1_JSON)
            header(HttpHeaders.AUTHORIZATION, mockAccessToken(role = EmployeeRole.MANAGER))
            contentType = MediaType.APPLICATION_JSON
            content = """
                {
                    "destinationDates": ["2024-01-02", "2024-01-03", "2024-01-04"]
                }
            """.trimIndent()
        }.andExpect {
            status { isNoContent() }
        }

        verifySequence {
            screeningService.duplicateScreeningDay(
                DuplicateScreeningDayCommand(
                    sourceDate = screeningDate,
                    destinationDates = setOf(
                        LocalDate.of(2024, 1, 2),
                        LocalDate.of(2024, 1, 3),
                        LocalDate.of(2024, 1, 4)
                    )
                )
            )
        }
    }

    @Test
    fun `test update screening state, should serialize and deserialize correctly`() {
        val screeningState = ScreeningState.PUBLISHED
        val request = UpdateScreeningStatesRequest(
            screeningIds = setOf(
                UUID.fromString("41fa16ea-c3b9-4a2b-88b0-************"),
                UUID.fromString("54d4f4c9-6894-4526-a4e8-57bd64312d3d")
            )
        )

        val command = UpdateScreeningStatesCommand(
            screeningIds = request.screeningIds,
            screeningState = screeningState
        )

        every { screeningService.updateScreeningStates(any()) } just Runs

        mvc.put("/manager-app/screenings/states/$screeningState") {
            contentType = MediaType.APPLICATION_JSON
            header(HttpHeaders.ACCEPT, ApiVersion.VERSION_1_JSON)
            header(HttpHeaders.AUTHORIZATION, mockAccessToken(role = EmployeeRole.MANAGER))
            content = """
                {
                  "screeningIds": [
                    "41fa16ea-c3b9-4a2b-88b0-************",
                    "54d4f4c9-6894-4526-a4e8-57bd64312d3d"
                  ]
                }
            """.trimIndent()
        }.andExpect {
            status { isNoContent() }
        }

        verifySequence {
            screeningService.updateScreeningStates(command)
        }
    }

    @RetryingTest(5)
    fun `test export screenings, should call service and return excel file`() {
        val filter = AdminExportScreeningsFilter(
            dateFrom = LocalDate.parse("2024-05-01"),
            dateTo = LocalDate.parse("2024-05-31"),
            states = setOf(ScreeningState.PUBLISHED),
            auditoriumIds = setOf(UUID.fromString("41fa16ea-c3b9-4a2b-88b0-************")),
            movieIds = setOf(UUID.fromString("41fa16ea-c3b9-4a2b-88b0-************")),
            distributorIds = setOf(UUID.fromString("41fa16ea-c3b9-4a2b-88b0-************"))
        )
        val byteArray = ByteArray(1024)
        val inputStream = ByteArrayInputStream(byteArray)
        val exportResult = ExportResultModel(inputStream, byteArray.size.toLong())

        every { screeningExportService.exportScreenings(any()) } returns exportResult

        mvc.post("/manager-app/screenings/export/${ExportFormat.XLSX}") {
            contentType = MediaType.APPLICATION_JSON
            header(HttpHeaders.ACCEPT, ApiVersion.VERSION_1_XLSX)
            header(HttpHeaders.AUTHORIZATION, mockAccessToken(role = EmployeeRole.MANAGER))
            content = """
                {
                  "dateFrom": "2024-05-01",
                  "dateTo": "2024-05-31",
                  "states": ["PUBLISHED"],
                  "auditoriumIds": ["41fa16ea-c3b9-4a2b-88b0-************"],
                  "movieIds": ["41fa16ea-c3b9-4a2b-88b0-************"],
                  "distributorIds": ["41fa16ea-c3b9-4a2b-88b0-************"]
                }
            """.trimIndent()
        }.andExpect {
            status { isOk() }
            header {
                string(HttpHeaders.CONTENT_TYPE, ApiVersion.VERSION_1_XLSX)
                string(HttpHeaders.CONTENT_DISPOSITION, containsString("attachment"))
                longValue(HttpHeaders.CONTENT_LENGTH, byteArray.size.toLong())
            }
        }

        verify {
            screeningExportService.exportScreenings(
                AdminExportScreeningsQuery(
                    filter = filter,
                    exportFormat = ExportFormat.XLSX,
                    username = "anonymous"
                )
            )
        }
    }

    @RetryingTest(5)
    fun `test export distributor screenings, should call service and return excel file`() {
        val filter = AdminExportDistributorScreeningsFilter(
            dateFrom = LocalDate.parse("2024-05-01"),
            dateTo = LocalDate.parse("2024-05-31"),
            distributorIds = setOf(UUID.fromString("41fa16ea-c3b9-4a2b-88b0-************"))
        )
        val byteArray = ByteArray(1024)
        val inputStream = ByteArrayInputStream(byteArray)
        val exportResult = ExportResultModel(inputStream, byteArray.size.toLong())

        every { distributorScreeningsExportService.exportDistributorScreenings(any()) } returns exportResult

        mvc.post("/manager-app/screenings/distributor-export/${ExportFormat.XLSX}") {
            contentType = MediaType.APPLICATION_JSON
            header(HttpHeaders.ACCEPT, ApiVersion.VERSION_1_XLSX)
            header(HttpHeaders.AUTHORIZATION, mockAccessToken(role = EmployeeRole.MANAGER))
            content = """
                {
                  "dateFrom": "2024-05-01",
                  "dateTo": "2024-05-31",
                  "distributorIds": ["41fa16ea-c3b9-4a2b-88b0-************"]
                }
            """.trimIndent()
        }.andExpect {
            status { isOk() }
            header {
                string(HttpHeaders.CONTENT_TYPE, ApiVersion.VERSION_1_XLSX)
                string(HttpHeaders.CONTENT_DISPOSITION, containsString("attachment"))
                longValue(HttpHeaders.CONTENT_LENGTH, byteArray.size.toLong())
            }
        }

        verify {
            distributorScreeningsExportService.exportDistributorScreenings(
                AdminExportDistributorScreeningsQuery(
                    filter = filter,
                    exportFormat = ExportFormat.XLSX,
                    username = "anonymous"
                )
            )
        }
    }

    @Test
    fun `test get screening, should serialize and deserialize correctly`() {
        val screeningId = UUID.fromString("41fa16ea-c3b9-4a2b-88b0-************")
        val response = AdminGetScreeningResponse(
            id = screeningId,
            date = LocalDate.of(2024, 1, 26),
            time = LocalTime.of(14, 0),
            saleTimeLimit = 30,
            createdAt = LocalDateTime.of(2022, 12, 24, 18, 15, 30),
            updatedAt = LocalDateTime.of(2023, 11, 19, 15, 18, 39),
            movie = MovieGetResponse(
                id = UUID.fromString("0296ed26-28b1-43e3-836a-a21016200b6b"),
                title = "Test Movie",
                rawTitle = "Test Movie Raw"
            ),
            auditorium = AuditoriumGetResponse(
                id = UUID.fromString("54d4f4c9-6894-4526-a4e8-57bd64312d3d"),
                title = "Auditorium 1",
                code = "AUD1"
            ),
            seats = listOf(
                SeatGetResponse(
                    id = UUID.fromString("6b920c63-bf89-4f95-9285-df6be8882ba6"),
                    type = SeatType.REGULAR,
                    doubleSeatType = DoubleSeatType.DOUBLE_SEAT_RIGHT,
                    row = "A",
                    number = "1",
                    positionLeft = 100,
                    positionTop = 200,
                    reservation = ReservationGetResponse(
                        state = ReservationState.RESERVED,
                        groupReservationId = null
                    )
                ),
                SeatGetResponse(
                    id = UUID.fromString("1d92ff2e-2b70-4a0f-b439-a2c6b914985b"),
                    type = SeatType.VIP,
                    doubleSeatType = null,
                    row = "B",
                    number = "2",
                    positionLeft = 200,
                    positionTop = 300,
                    reservation = ReservationGetResponse(
                        state = ReservationState.FREE,
                        groupReservationId = UUID.fromString("2ed0d06c-03e7-4b41-8758-a372b4b221d5")
                    )
                )
            )
        )

        every { adminGetScreeningQueryService(any()) } returns response

        mvc.get(GET_SCREENING_PATH(screeningId)) {
            contentType = MediaType.APPLICATION_JSON
            header(HttpHeaders.ACCEPT, ApiVersion.VERSION_1_JSON)
            header(HttpHeaders.AUTHORIZATION, mockAccessToken(role = EmployeeRole.MANAGER))
        }.andExpect {
            status { isOk() }
            content { contentType(ApiVersion.VERSION_1_JSON) }
            jsonContent(
                """
            {
              "id": "41fa16ea-c3b9-4a2b-88b0-************",
              "date": "2024-01-26",
              "time": "14:00:00",
              "saleTimeLimit": 30,
              "createdAt": "2022-12-24T17:15:30Z",
              "updatedAt": "2023-11-19T14:18:39Z",
              "movie": {
                "id": "0296ed26-28b1-43e3-836a-a21016200b6b",
                "title": "Test Movie",
                "rawTitle": "Test Movie Raw"
              },
              "auditorium": {
                "id": "54d4f4c9-6894-4526-a4e8-57bd64312d3d",
                "title": "Auditorium 1",
                "code": "AUD1"
              },
              "seats": [
                {
                  "id": "6b920c63-bf89-4f95-9285-df6be8882ba6",
                  "type": "REGULAR",
                  "doubleSeatType": DOUBLE_SEAT_RIGHT,
                  "row": "A",
                  "number": "1",
                  "positionLeft": 100,
                  "positionTop": 200,
                  "reservation": {
                    "state": "RESERVED",
                    "groupReservationId": null
                  }
                },
                {
                  "id": "1d92ff2e-2b70-4a0f-b439-a2c6b914985b",
                  "type": "VIP",
                  "doubleSeatType": null,
                  "row": "B",
                  "number": "2",
                  "positionLeft": 200,
                  "positionTop": 300,
                  "reservation": {
                    "state": "FREE",
                    "groupReservationId": "2ed0d06c-03e7-4b41-8758-a372b4b221d5"
                  }
                }
              ]
            }
                """.trimIndent()
            )
        }

        verifySequence {
            adminGetScreeningQueryService(AdminGetScreeningQuery(screeningId))
        }
    }
}

private val GET_SCREENING_PATH: (UUID) -> String = { screeningId: UUID -> "/manager-app/screenings/$screeningId" }
