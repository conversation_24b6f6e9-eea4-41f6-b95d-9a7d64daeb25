package com.cleevio.cinemax.api.module.basket.service

import com.cleevio.cinemax.api.DatabaseCleanup
import com.cleevio.cinemax.api.INTEGRATION_TEST_DATE_TIME
import com.cleevio.cinemax.api.IntegrationDataTestHelper
import com.cleevio.cinemax.api.IntegrationTest
import com.cleevio.cinemax.api.common.constant.NO_TAX_RATE
import com.cleevio.cinemax.api.common.constant.REDUCED_TAX_RATE
import com.cleevio.cinemax.api.common.constant.SUPER_REDUCED_TAX_RATE
import com.cleevio.cinemax.api.common.integration.IntegrationException
import com.cleevio.cinemax.api.common.integration.cards.constant.CardsBasketItemType
import com.cleevio.cinemax.api.common.integration.cards.constant.CardsBasketType
import com.cleevio.cinemax.api.common.integration.cards.constant.CardsBranch
import com.cleevio.cinemax.api.common.integration.cards.constant.CardsCountry
import com.cleevio.cinemax.api.common.integration.cards.constant.CardsCurrency
import com.cleevio.cinemax.api.common.integration.cards.constant.CardsPosType
import com.cleevio.cinemax.api.common.integration.cards.constant.CardsScreeningType
import com.cleevio.cinemax.api.common.integration.cards.constant.CardsUsageState
import com.cleevio.cinemax.api.common.integration.cards.dto.AvailableCardUsageProductResponse
import com.cleevio.cinemax.api.common.integration.cards.dto.AvailableCardUsageTicketResponse
import com.cleevio.cinemax.api.common.integration.cards.dto.CardUsageProductResponse
import com.cleevio.cinemax.api.common.integration.cards.dto.CardUsageTicketResponse
import com.cleevio.cinemax.api.common.integration.cards.dto.CardsDiscountSubjectResponse
import com.cleevio.cinemax.api.common.integration.cards.dto.DiscountSubjectType
import com.cleevio.cinemax.api.common.integration.cards.dto.UseCardsProductBasketItem
import com.cleevio.cinemax.api.common.integration.cards.dto.UseCardsRequest
import com.cleevio.cinemax.api.common.integration.cards.dto.UseCardsTicketBasketItem
import com.cleevio.cinemax.api.module.basket.constant.BasketState
import com.cleevio.cinemax.api.module.basket.service.command.AddAppliedBasketCardIdCommand
import com.cleevio.cinemax.api.module.basket.service.command.ApplyCardsOnBasketCommand
import com.cleevio.cinemax.api.module.basket.service.command.RecalculateBasketTotalPriceCommand
import com.cleevio.cinemax.api.module.basket.service.command.RemoveCardFromBasketCommand
import com.cleevio.cinemax.api.module.basket.service.command.UseCardsOnBasketCommand
import com.cleevio.cinemax.api.module.basketitem.constant.BasketItemType
import com.cleevio.cinemax.api.module.basketitem.service.BasketItemJpaFinderService
import com.cleevio.cinemax.api.module.card.event.CardUsedOnTicketBasketItemEvent
import com.cleevio.cinemax.api.module.card.exception.CardNotApplicableException
import com.cleevio.cinemax.api.module.card.exception.CardNotCombinableException
import com.cleevio.cinemax.api.module.card.service.BasketCinemaxCardsService
import com.cleevio.cinemax.api.module.cardusage.service.CardUsageFinderService
import com.cleevio.cinemax.api.module.product.constant.ProductType
import com.cleevio.cinemax.api.module.productcomponent.constant.ProductComponentUnit
import com.cleevio.cinemax.api.module.screeningtypes.service.ScreeningTypesRepository
import com.cleevio.cinemax.api.module.seat.constant.SeatType
import com.cleevio.cinemax.api.module.ticket.service.TicketService
import com.cleevio.cinemax.api.module.ticket.service.command.UpdateTicketCommand
import com.cleevio.cinemax.api.module.ticketdiscount.constant.TicketDiscountType
import com.cleevio.cinemax.api.util.toUUID
import io.kotest.matchers.comparables.shouldBeEqualComparingTo
import io.kotest.matchers.shouldBe
import io.mockk.Called
import io.mockk.Runs
import io.mockk.every
import io.mockk.just
import io.mockk.verifySequence
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import org.springframework.beans.factory.annotation.Autowired
import java.time.LocalDate
import java.time.LocalDateTime
import java.util.Optional
import java.util.UUID

class BasketCinemaxCardsServiceIT @Autowired constructor(
    private val underTest: BasketCinemaxCardsService,
    private val integrationDataTestHelper: IntegrationDataTestHelper,
    private val cardUsageFinderService: CardUsageFinderService,
    private val basketJpaFinderService: BasketJpaFinderService,
    private val basketItemJpaFinderService: BasketItemJpaFinderService,
    private val basketService: BasketService,
    private val ticketService: TicketService,
    private val screeningTypesRepository: ScreeningTypesRepository,
) : IntegrationTest(DatabaseCleanup.AFTER_EACH) {

    @Test
    fun `test applyCardsOnBasket & useCardsOnBasket - basket with one ticket, discount subject with unlimited 25 percent discount on tickets - should successfully apply discount`() {
        // 1. APPLY CARDS ON BASKET
        val testData = prepareSingleTicketTestData()
        screeningTypesRepository.deleteAll()

        val card = integrationDataTestHelper.getCard()

        every { cinemaxCardsConnectorMock.listCardsUsages(any()) } returns listOf(
            AvailableCardUsageTicketResponse(
                basketItemId = testData.basketItemId,
                discountSubjectId = 100L,
                cardTemplateName = card.title,
                cardCode = card.code,
                discountedPrice = 8.0.toBigDecimal(),
                discountedBasePrice = 8.0.toBigDecimal(),
                originalPrice = 10.0.toBigDecimal(),
                originalBasePrice = 10.0.toBigDecimal(),
                discountValue = 2.toBigDecimal(),
                discountCurrency = CardsCurrency.EUR,
                discountSubject = CardsDiscountSubjectResponse(
                    id = 1L,
                    type = DiscountSubjectType.TICKET,
                    relativeDiscount = 0.2.toBigDecimal(),
                    canBeCombined = false,
                    includeSurcharges = false,
                    distributorFee = mapOf()
                )
            )
        )

        every { applicationEventPublisherMock.publishEvent(any<CardUsedOnTicketBasketItemEvent>()) } just Runs

        basketService.addAppliedBasketCardId(
            AddAppliedBasketCardIdCommand(
                basketId = testData.basketId,
                cardId = card.id
            )
        )

        underTest.applyCardsOnBasket(
            command = ApplyCardsOnBasketCommand(
                basketId = testData.basketId,
                cardIds = setOf(card.id),
                posType = CardsPosType.CINEMA
            )
        )

        val basket = basketJpaFinderService.getById(testData.basketId)
        with(basket) {
            totalPrice shouldBeEqualComparingTo 8.0.toBigDecimal()
        }

        val basketItem = basketItemJpaFinderService.getNonDeletedById(testData.basketItemId)
        with(basketItem) {
            price shouldBeEqualComparingTo 8.0.toBigDecimal()
            originalPrice shouldBeEqualComparingTo 10.0.toBigDecimal()
            fixedPrice shouldBeEqualComparingTo 0.0.toBigDecimal()
            vatAmount shouldBeEqualComparingTo 1.5.toBigDecimal()
        }

        val cardUsage = cardUsageFinderService.findAll().first()
        with(cardUsage) {
            cardId shouldBe card.id
            basketId shouldBe testData.basketId
            basketItemId shouldBe testData.basketItemId
            originalId shouldBe null
            discountPercentage shouldBe 20
        }

        // 2. USE CARDS ON BASKET
        every { cinemaxCardsConnectorMock.useCards(any()) } returns listOf(
            CardUsageTicketResponse(
                id = 1L,
                cardCode = "CU5X2URJ",
                branch = CardsBranch.BA,
                country = CardsCountry.SK,
                discountAmount = 2.toBigDecimal(),
                discountCurrency = CardsCurrency.EUR,
                discountSubject = CardsDiscountSubjectResponse(
                    id = 1L,
                    type = DiscountSubjectType.TICKET,
                    relativeDiscount = 0.2.toBigDecimal()
                ),
                discountedPrice = 8.0.toBigDecimal(),
                discountedBasePrice = 8.0.toBigDecimal(),
                originalPrice = 10.0.toBigDecimal(),
                pos = CardsPosType.CINEMA,
                basketId = testData.basketId,
                basketItemId = testData.basketItemId,
                state = CardsUsageState.CREATED,
                timestamp = LocalDateTime.now(),
                canBeCombined = false,
                screeningDate = testData.screeningDate,
                screeningId = 10.toUUID(),
                movieCode = testData.movieCode,
                overridePosDistributorFee = emptyMap(),
                screeningTypes = setOf()
            )
        )

        underTest.useCardsOnBasket(
            UseCardsOnBasketCommand(
                basketId = testData.basketId,
                posType = CardsPosType.CINEMA
            )
        )

        val cardUsages = cardUsageFinderService.findAll()
        cardUsages.size shouldBe 1
        cardUsages[0].originalId shouldBe 1L

        verifySequence {
            cinemaxCardsConnectorMock.listCardsUsages(
                withArg<UseCardsRequest> { request ->
                    request.cardCodes shouldBe setOf("CU5X2URJ")

                    with(request.payload) {
                        basketId shouldBe testData.basketId
                        type shouldBe CardsBasketType.BASKET
                        pos shouldBe CardsPosType.CINEMA
                        branch shouldBe CardsBranch.BA
                        currency shouldBe CardsCurrency.EUR
                        items.size shouldBe 1

                        with(items[0] as UseCardsTicketBasketItem) {
                            basketItemId shouldBe testData.basketItemId
                            type shouldBe CardsBasketItemType.TICKET
                            ticketBasePrice shouldBeEqualComparingTo 10.toBigDecimal()
                            ticketTotalPrice shouldBeEqualComparingTo 10.toBigDecimal()
                            screeningId shouldBe testData.screeningId
                            movieCode shouldBe testData.movieCode
                            screeningDate shouldBe testData.screeningDate
                            screeningTypes shouldBe setOf()
                        }
                    }
                }
            )

            applicationEventPublisherMock.publishEvent(
                withArg<CardUsedOnTicketBasketItemEvent> { event ->
                    event.ticketId shouldBe testData.ticketId
                    event.price shouldBeEqualComparingTo 8.0.toBigDecimal()
                    // event.basePrice shouldBeEqualComparingTo 10.toBigDecimal()
                    event.zeroFees shouldBe false
                }
            )

            cinemaxCardsConnectorMock.useCards(
                withArg<UseCardsRequest> { request ->
                    request.cardCodes shouldBe setOf("CU5X2URJ")

                    with(request.payload) {
                        basketId shouldBe testData.basketId
                        type shouldBe CardsBasketType.BASKET
                        pos shouldBe CardsPosType.CINEMA
                        branch shouldBe CardsBranch.BA
                        currency shouldBe CardsCurrency.EUR
                        items.size shouldBe 1

                        with(items[0] as UseCardsTicketBasketItem) {
                            basketItemId shouldBe testData.basketItemId
                            type shouldBe CardsBasketItemType.TICKET
                            ticketBasePrice shouldBeEqualComparingTo 10.toBigDecimal()
                            ticketTotalPrice shouldBeEqualComparingTo 10.toBigDecimal()
                            screeningId shouldBe testData.screeningId
                            movieCode shouldBe testData.movieCode
                            screeningDate shouldBe testData.screeningDate
                            screeningTypes shouldBe setOf()
                        }
                    }
                }
            )
        }
    }

    @Test
    fun `test applyCardsOnBasket & useCardsOnBasket - basket with one ticket, discount subject with zero fees, fixed price and screening types - should successfully apply discount`() {
        // 1. APPLY CARDS ON BASKET
        val testData = prepareSingleTicketTestData()
        val card = integrationDataTestHelper.getCard()

        every { cinemaxCardsConnectorMock.listCardsUsages(any()) } returns listOf(
            AvailableCardUsageTicketResponse(
                basketItemId = testData.basketItemId,
                discountSubjectId = 100L,
                cardTemplateName = card.title,
                cardCode = card.code,
                discountedPrice = 8.0.toBigDecimal(),
                discountedBasePrice = 8.0.toBigDecimal(),
                originalPrice = 10.0.toBigDecimal(),
                originalBasePrice = 10.0.toBigDecimal(),
                discountValue = 2.toBigDecimal(),
                discountCurrency = CardsCurrency.EUR,
                discountSubject = CardsDiscountSubjectResponse(
                    id = 1L,
                    type = DiscountSubjectType.TICKET,
                    relativeDiscount = 0.2.toBigDecimal(),
                    canBeCombined = false,
                    includeSurcharges = true,
                    distributorFee = mapOf(
                        CardsCurrency.EUR to 5.0.toBigDecimal()
                    )
                )
            )
        )

        every { applicationEventPublisherMock.publishEvent(any<CardUsedOnTicketBasketItemEvent>()) } just Runs

        underTest.applyCardsOnBasket(
            command = ApplyCardsOnBasketCommand(
                basketId = testData.basketId,
                cardIds = setOf(card.id),
                posType = CardsPosType.CINEMA
            )
        )

        val basket = basketJpaFinderService.getById(testData.basketId)
        with(basket) {
            totalPrice shouldBeEqualComparingTo 8.0.toBigDecimal()
        }

        val basketItem = basketItemJpaFinderService.getNonDeletedById(testData.basketItemId)
        with(basketItem) {
            price shouldBeEqualComparingTo 8.0.toBigDecimal()
            originalPrice shouldBeEqualComparingTo 10.0.toBigDecimal()
            fixedPrice shouldBeEqualComparingTo 5.0.toBigDecimal()
            vatAmount shouldBeEqualComparingTo 1.5.toBigDecimal()
        }

        val cardUsage = cardUsageFinderService.findAll().first()
        with(cardUsage) {
            cardId shouldBe card.id
            basketId shouldBe testData.basketId
            basketItemId shouldBe testData.basketItemId
            originalId shouldBe null
            discountPercentage shouldBe 20
        }

        // 2. USE CARDS ON BASKET
        every { cinemaxCardsConnectorMock.useCards(any()) } returns listOf(
            CardUsageTicketResponse(
                id = 1L,
                cardCode = "CU5X2URJ",
                branch = CardsBranch.BA,
                country = CardsCountry.SK,
                discountAmount = 2.toBigDecimal(),
                discountCurrency = CardsCurrency.EUR,
                discountSubject = CardsDiscountSubjectResponse(
                    id = 1L,
                    type = DiscountSubjectType.TICKET,
                    relativeDiscount = 0.2.toBigDecimal(),
                    includeSurcharges = true,
                    distributorFee = mapOf(
                        CardsCurrency.CZK to 100.0.toBigDecimal(),
                        CardsCurrency.EUR to 5.0.toBigDecimal()
                    )
                ),
                discountedPrice = 8.0.toBigDecimal(),
                discountedBasePrice = 8.0.toBigDecimal(),
                originalPrice = 10.0.toBigDecimal(),
                pos = CardsPosType.CINEMA,
                basketId = testData.basketId,
                basketItemId = testData.basketItemId,
                state = CardsUsageState.CREATED,
                timestamp = LocalDateTime.now(),
                canBeCombined = false,
                screeningDate = testData.screeningDate,
                screeningId = 10.toUUID(),
                movieCode = testData.movieCode,
                overridePosDistributorFee = emptyMap(),
                screeningTypes = setOf(
                    CardsScreeningType.LADIES_RIDE,
                    CardsScreeningType.ARTMAX_MOVIES
                )
            )
        )

        basketService.addAppliedBasketCardId(
            AddAppliedBasketCardIdCommand(
                basketId = testData.basketId,
                cardId = card.id
            )
        )

        underTest.useCardsOnBasket(
            UseCardsOnBasketCommand(
                basketId = testData.basketId,
                posType = CardsPosType.CINEMA
            )
        )

        val cardUsages = cardUsageFinderService.findAll()
        cardUsages.size shouldBe 1
        cardUsages[0].originalId shouldBe 1L

        verifySequence {
            cinemaxCardsConnectorMock.listCardsUsages(
                withArg<UseCardsRequest> { request ->
                    request.cardCodes shouldBe setOf("CU5X2URJ")

                    with(request.payload) {
                        basketId shouldBe testData.basketId
                        type shouldBe CardsBasketType.BASKET
                        pos shouldBe CardsPosType.CINEMA
                        branch shouldBe CardsBranch.BA
                        currency shouldBe CardsCurrency.EUR
                        items.size shouldBe 1

                        with(items[0] as UseCardsTicketBasketItem) {
                            basketItemId shouldBe testData.basketItemId
                            type shouldBe CardsBasketItemType.TICKET
                            ticketBasePrice shouldBeEqualComparingTo 10.toBigDecimal()
                            ticketTotalPrice shouldBeEqualComparingTo 10.toBigDecimal()
                            screeningId shouldBe testData.screeningId
                            movieCode shouldBe testData.movieCode
                            screeningDate shouldBe testData.screeningDate
                            screeningTypes shouldBe setOf(
                                CardsScreeningType.LADIES_RIDE,
                                CardsScreeningType.ARTMAX_MOVIES
                            )
                        }
                    }
                }
            )

            applicationEventPublisherMock.publishEvent(
                withArg<CardUsedOnTicketBasketItemEvent> { event ->
                    event.ticketId shouldBe testData.ticketId
                    event.price shouldBeEqualComparingTo 8.0.toBigDecimal()
                    event.basePrice shouldBeEqualComparingTo 8.0.toBigDecimal()
                    event.zeroFees shouldBe true
                }
            )

            cinemaxCardsConnectorMock.useCards(
                withArg<UseCardsRequest> { request ->
                    request.cardCodes shouldBe setOf("CU5X2URJ")

                    with(request.payload) {
                        basketId shouldBe testData.basketId
                        type shouldBe CardsBasketType.BASKET
                        pos shouldBe CardsPosType.CINEMA
                        branch shouldBe CardsBranch.BA
                        currency shouldBe CardsCurrency.EUR
                        items.size shouldBe 1

                        with(items[0] as UseCardsTicketBasketItem) {
                            basketItemId shouldBe testData.basketItemId
                            type shouldBe CardsBasketItemType.TICKET
                            ticketBasePrice shouldBeEqualComparingTo 10.toBigDecimal()
                            ticketTotalPrice shouldBeEqualComparingTo 10.toBigDecimal()
                            screeningId shouldBe testData.screeningId
                            movieCode shouldBe testData.movieCode
                            screeningDate shouldBe testData.screeningDate
                            screeningTypes shouldBe setOf(
                                CardsScreeningType.LADIES_RIDE,
                                CardsScreeningType.ARTMAX_MOVIES
                            )
                        }
                    }
                }
            )
        }
    }

    @Test
    fun `test applyCardsOnBasket & useCardsOnBasket - basket with one ticket, no applicable discount subject - should throw exception`() {
        val testData = prepareSingleTicketTestData()
        val card = integrationDataTestHelper.getCard()

        every { cinemaxCardsConnectorMock.listCardsUsages(any()) } throws IntegrationException(
            statusCode = 400,
            message = "Card with id COXXEZ13 has not applicable discount subjects for given request.",
            responseBody = ""
        )

        assertThrows<CardNotApplicableException> {
            underTest.applyCardsOnBasket(
                command = ApplyCardsOnBasketCommand(
                    basketId = testData.basketId,
                    cardIds = setOf(card.id),
                    posType = CardsPosType.CINEMA
                )
            )
        }

        val basket = basketJpaFinderService.getById(testData.basketId)
        with(basket) {
            totalPrice shouldBeEqualComparingTo 10.0.toBigDecimal()
        }

        val basketItem = basketItemJpaFinderService.getNonDeletedById(testData.basketItemId)
        with(basketItem) {
            price shouldBeEqualComparingTo 10.0.toBigDecimal()
            originalPrice shouldBeEqualComparingTo 10.0.toBigDecimal()
            fixedPrice shouldBeEqualComparingTo 0.0.toBigDecimal()
            vatAmount shouldBeEqualComparingTo 1.87.toBigDecimal()
        }

        val cardUsages = cardUsageFinderService.findAll()
        cardUsages shouldBe emptyList()

        verifySequence {
            cinemaxCardsConnectorMock.listCardsUsages(
                withArg<UseCardsRequest> { request ->
                    request.cardCodes shouldBe setOf("CU5X2URJ")

                    with(request.payload) {
                        basketId shouldBe testData.basketId
                        type shouldBe CardsBasketType.BASKET
                        pos shouldBe CardsPosType.CINEMA
                        branch shouldBe CardsBranch.BA
                        currency shouldBe CardsCurrency.EUR
                        items.size shouldBe 1

                        with(items[0] as UseCardsTicketBasketItem) {
                            basketItemId shouldBe testData.basketItemId
                            type shouldBe CardsBasketItemType.TICKET
                            ticketBasePrice shouldBeEqualComparingTo 10.toBigDecimal()
                            ticketTotalPrice shouldBeEqualComparingTo 10.toBigDecimal()
                            screeningId shouldBe testData.screeningId
                            movieCode shouldBe testData.movieCode
                            screeningDate shouldBe testData.screeningDate
                        }
                    }
                }
            )

            applicationEventPublisherMock wasNot Called
        }
    }

    @Test
    fun `test applyCardsOnBasket & useCardsOnBasket - basket with no items, no applicable discount subject - should not throw exception`() {
        val emptyBasket = integrationDataTestHelper.getBasket(
            totalPrice = 0.toBigDecimal(),
            state = BasketState.OPEN
        )
        val card = integrationDataTestHelper.getCard()

        every { cinemaxCardsConnectorMock.listCardsUsages(any()) } throws IntegrationException(
            statusCode = 400,
            message = "Card with id COXXEZ13 has not applicable discount subjects for given request.",
            responseBody = ""
        )

        underTest.applyCardsOnBasket(
            command = ApplyCardsOnBasketCommand(
                basketId = emptyBasket.id,
                cardIds = setOf(card.id),
                posType = CardsPosType.CINEMA
            )
        )

        val basket = basketJpaFinderService.getById(emptyBasket.id)
        with(basket) {
            totalPrice shouldBeEqualComparingTo 0.0.toBigDecimal()
        }

        val basketItems = basketItemJpaFinderService.findAllByBasketId(emptyBasket.id)
        basketItems shouldBe emptyList()

        val cardUsages = cardUsageFinderService.findAll()
        cardUsages shouldBe emptyList()

        verifySequence {
            cinemaxCardsConnectorMock.listCardsUsages(
                withArg<UseCardsRequest> { request ->
                    request.cardCodes shouldBe setOf("CU5X2URJ")

                    with(request.payload) {
                        basketId shouldBe emptyBasket.id
                        type shouldBe CardsBasketType.BASKET
                        pos shouldBe CardsPosType.CINEMA
                        branch shouldBe CardsBranch.BA
                        currency shouldBe CardsCurrency.EUR
                        items.size shouldBe 0
                    }
                }
            )

            applicationEventPublisherMock wasNot Called
        }
    }

    @Test
    fun `test applyCardsOnBasket & useCardsOnBasket - basket with three tickets, discount subject applicable to two tickets only - should apply discount to two tickets only`() {
        // 1. APPLY CARDS ON BASKET
        val testData = prepareSingleTicketTestData()

        val seat2 = integrationDataTestHelper.getSeat(
            originalId = 2,
            auditoriumId = testData.auditoriumId,
            auditoriumLayoutId = testData.auditoriumLayoutId
        )
        val ticketPrice2 = integrationDataTestHelper.getTicketPrice(
            seatId = seat2.id,
            screeningId = testData.screeningId,
            basePrice = 10.toBigDecimal(),
            totalPrice = 10.toBigDecimal()
        )
        val reservation2 = integrationDataTestHelper.getReservation(
            seatId = seat2.id,
            screeningId = testData.screeningId
        )
        val ticket2 = integrationDataTestHelper.getTicket(
            screeningId = testData.screeningId,
            reservationId = reservation2.id,
            ticketPriceId = ticketPrice2.id
        )
        val basketItem2 = integrationDataTestHelper.getBasketItem(
            basketId = testData.basketId,
            ticketId = ticket2.id,
            type = BasketItemType.TICKET,
            price = 10.toBigDecimal()
        )

        val seat3 = integrationDataTestHelper.getSeat(
            originalId = 3,
            auditoriumId = testData.auditoriumId,
            auditoriumLayoutId = testData.auditoriumLayoutId
        )
        val ticketPrice3 = integrationDataTestHelper.getTicketPrice(
            seatId = seat3.id,
            screeningId = testData.screeningId,
            basePrice = 10.toBigDecimal(),
            totalPrice = 10.toBigDecimal()
        )
        val reservation3 = integrationDataTestHelper.getReservation(
            seatId = seat3.id,
            screeningId = testData.screeningId
        )
        val ticket3 = integrationDataTestHelper.getTicket(
            screeningId = testData.screeningId,
            reservationId = reservation3.id,
            ticketPriceId = ticketPrice3.id
        )
        val basketItem3 = integrationDataTestHelper.getBasketItem(
            basketId = testData.basketId,
            ticketId = ticket3.id,
            type = BasketItemType.TICKET,
            price = 10.toBigDecimal()
        )

        val card = integrationDataTestHelper.getCard()

        every { cinemaxCardsConnectorMock.listCardsUsages(any()) } returns listOf(
            AvailableCardUsageTicketResponse(
                basketItemId = basketItem2.id,
                discountSubjectId = 100L,
                cardTemplateName = card.title,
                cardCode = card.code,
                discountedPrice = 0.0.toBigDecimal(),
                discountedBasePrice = 0.0.toBigDecimal(),
                originalPrice = 10.0.toBigDecimal(),
                originalBasePrice = 10.0.toBigDecimal(),
                discountValue = 10.0.toBigDecimal(),
                discountCurrency = CardsCurrency.EUR,
                discountSubject = CardsDiscountSubjectResponse(
                    id = 1L,
                    type = DiscountSubjectType.TICKET,
                    relativeDiscount = 1.toBigDecimal(),
                    canBeCombined = false,
                    includeSurcharges = false,
                    distributorFee = mapOf()
                )
            ),
            AvailableCardUsageTicketResponse(
                basketItemId = basketItem3.id,
                discountSubjectId = 100L,
                cardTemplateName = card.title,
                cardCode = card.code,
                discountedPrice = 0.0.toBigDecimal(),
                discountedBasePrice = 0.0.toBigDecimal(),
                originalPrice = 10.0.toBigDecimal(),
                originalBasePrice = 10.0.toBigDecimal(),
                discountValue = 10.0.toBigDecimal(),
                discountCurrency = CardsCurrency.EUR,
                discountSubject = CardsDiscountSubjectResponse(
                    id = 1L,
                    type = DiscountSubjectType.TICKET,
                    relativeDiscount = 1.toBigDecimal(),
                    canBeCombined = false,
                    includeSurcharges = false,
                    distributorFee = mapOf()
                )
            )
        )

        every { applicationEventPublisherMock.publishEvent(any<CardUsedOnTicketBasketItemEvent>()) } just Runs

        underTest.applyCardsOnBasket(
            command = ApplyCardsOnBasketCommand(
                basketId = testData.basketId,
                cardIds = setOf(card.id),
                posType = CardsPosType.CINEMA
            )
        )

        val basket = basketJpaFinderService.getById(testData.basketId)
        with(basket) {
            totalPrice shouldBeEqualComparingTo 10.0.toBigDecimal()
        }

        val basketItems = basketItemJpaFinderService.findAllNonDeletedByBasketId(testData.basketId).sortedBy { it.createdAt }
        basketItems.size shouldBe 3

        with(basketItems[0]) {
            price shouldBeEqualComparingTo 10.0.toBigDecimal()
            originalPrice shouldBeEqualComparingTo 10.0.toBigDecimal()
            fixedPrice shouldBeEqualComparingTo 0.0.toBigDecimal()
        }
        with(basketItems[1]) {
            price shouldBeEqualComparingTo 0.0.toBigDecimal()
            originalPrice shouldBeEqualComparingTo 10.0.toBigDecimal()
            fixedPrice shouldBeEqualComparingTo 0.0.toBigDecimal()
        }
        with(basketItems[2]) {
            price shouldBeEqualComparingTo 0.0.toBigDecimal()
            originalPrice shouldBeEqualComparingTo 10.0.toBigDecimal()
            fixedPrice shouldBeEqualComparingTo 0.0.toBigDecimal()
        }

        val cardUsages = cardUsageFinderService.findAll().sortedBy { it.createdAt }
        cardUsages.size shouldBe 2

        with(cardUsages[0]) {
            cardId shouldBe card.id
            basketId shouldBe testData.basketId
            basketItemId shouldBe basketItem2.id
            originalId shouldBe null
            discountPercentage shouldBe 100
        }
        with(cardUsages[1]) {
            cardId shouldBe card.id
            basketId shouldBe testData.basketId
            basketItemId shouldBe basketItem3.id
            originalId shouldBe null
            discountPercentage shouldBe 100
        }

        // 2. USE CARDS ON BASKET
        every { cinemaxCardsConnectorMock.useCards(any()) } returns listOf(
            CardUsageTicketResponse(
                id = 1L,
                cardCode = "CU5X2URJ",
                branch = CardsBranch.BA,
                country = CardsCountry.SK,
                discountAmount = 2.toBigDecimal(),
                discountCurrency = CardsCurrency.EUR,
                discountSubject = CardsDiscountSubjectResponse(
                    id = 1L,
                    type = DiscountSubjectType.TICKET,
                    relativeDiscount = 1.toBigDecimal()
                ),
                discountedPrice = 0.0.toBigDecimal(),
                discountedBasePrice = 0.0.toBigDecimal(),
                originalPrice = 10.0.toBigDecimal(),
                pos = CardsPosType.CINEMA,
                basketId = testData.basketId,
                basketItemId = basketItem2.id,
                state = CardsUsageState.CREATED,
                timestamp = LocalDateTime.now(),
                canBeCombined = false,
                screeningDate = testData.screeningDate,
                screeningId = 10.toUUID(),
                movieCode = testData.movieCode,
                overridePosDistributorFee = emptyMap()
            ),
            CardUsageTicketResponse(
                id = 2L,
                cardCode = "CU5X2URJ",
                branch = CardsBranch.BA,
                country = CardsCountry.SK,
                discountAmount = 2.toBigDecimal(),
                discountCurrency = CardsCurrency.EUR,
                discountSubject = CardsDiscountSubjectResponse(
                    id = 1L,
                    type = DiscountSubjectType.TICKET,
                    relativeDiscount = 1.toBigDecimal()
                ),
                discountedPrice = 0.0.toBigDecimal(),
                discountedBasePrice = 0.0.toBigDecimal(),
                originalPrice = 10.0.toBigDecimal(),
                pos = CardsPosType.CINEMA,
                basketId = testData.basketId,
                basketItemId = basketItem3.id,
                state = CardsUsageState.CREATED,
                timestamp = LocalDateTime.now(),
                canBeCombined = false,
                screeningDate = testData.screeningDate,
                screeningId = 10.toUUID(),
                movieCode = testData.movieCode,
                overridePosDistributorFee = emptyMap()
            )
        )

        basketService.addAppliedBasketCardId(
            AddAppliedBasketCardIdCommand(
                basketId = testData.basketId,
                cardId = card.id
            )
        )

        underTest.useCardsOnBasket(
            UseCardsOnBasketCommand(
                basketId = testData.basketId,
                posType = CardsPosType.CINEMA
            )
        )

        val updatedCardUsages = cardUsageFinderService.findAll().sortedBy { it.createdAt }
        updatedCardUsages.size shouldBe 2
        updatedCardUsages[0].originalId shouldBe 1L
        updatedCardUsages[1].originalId shouldBe 2L

        verifySequence {
            cinemaxCardsConnectorMock.listCardsUsages(
                withArg<UseCardsRequest> { request ->
                    request.cardCodes shouldBe setOf("CU5X2URJ")

                    with(request.payload) {
                        basketId shouldBe testData.basketId
                        type shouldBe CardsBasketType.BASKET
                        pos shouldBe CardsPosType.CINEMA
                        branch shouldBe CardsBranch.BA
                        currency shouldBe CardsCurrency.EUR
                        items.size shouldBe 3

                        with(items[0] as UseCardsTicketBasketItem) {
                            basketItemId shouldBe testData.basketItemId
                            type shouldBe CardsBasketItemType.TICKET
                            ticketBasePrice shouldBeEqualComparingTo 10.toBigDecimal()
                            ticketTotalPrice shouldBeEqualComparingTo 10.toBigDecimal()
                            screeningId shouldBe testData.screeningId
                            movieCode shouldBe testData.movieCode
                            screeningDate shouldBe testData.screeningDate
                        }

                        with(items[1] as UseCardsTicketBasketItem) {
                            basketItemId shouldBe basketItem2.id
                            type shouldBe CardsBasketItemType.TICKET
                            ticketBasePrice shouldBeEqualComparingTo 10.toBigDecimal()
                            ticketTotalPrice shouldBeEqualComparingTo 10.toBigDecimal()
                            screeningId shouldBe testData.screeningId
                            movieCode shouldBe testData.movieCode
                            screeningDate shouldBe testData.screeningDate
                        }

                        with(items[2] as UseCardsTicketBasketItem) {
                            basketItemId shouldBe basketItem3.id
                            type shouldBe CardsBasketItemType.TICKET
                            ticketBasePrice shouldBeEqualComparingTo 10.toBigDecimal()
                            ticketTotalPrice shouldBeEqualComparingTo 10.toBigDecimal()
                            screeningId shouldBe testData.screeningId
                            movieCode shouldBe testData.movieCode
                            screeningDate shouldBe testData.screeningDate
                        }
                    }
                }
            )

            applicationEventPublisherMock.publishEvent(
                withArg<CardUsedOnTicketBasketItemEvent> { event ->
                    event.ticketId shouldBe ticket2.id
                    event.price shouldBeEqualComparingTo 0.0.toBigDecimal()
                    event.basePrice shouldBeEqualComparingTo 0.0.toBigDecimal()
                    event.zeroFees shouldBe false
                }
            )
            applicationEventPublisherMock.publishEvent(
                withArg<CardUsedOnTicketBasketItemEvent> { event ->
                    event.ticketId shouldBe ticket3.id
                    event.price shouldBeEqualComparingTo 0.0.toBigDecimal()
                    event.basePrice shouldBeEqualComparingTo 0.0.toBigDecimal()
                    event.zeroFees shouldBe false
                }
            )

            cinemaxCardsConnectorMock.useCards(
                withArg<UseCardsRequest> { request ->
                    request.cardCodes shouldBe setOf("CU5X2URJ")

                    with(request.payload) {
                        basketId shouldBe testData.basketId
                        type shouldBe CardsBasketType.BASKET
                        pos shouldBe CardsPosType.CINEMA
                        branch shouldBe CardsBranch.BA
                        currency shouldBe CardsCurrency.EUR
                        items.size shouldBe 2

                        with(items[0] as UseCardsTicketBasketItem) {
                            basketItemId shouldBe basketItem2.id
                            type shouldBe CardsBasketItemType.TICKET
                            ticketBasePrice shouldBeEqualComparingTo 10.toBigDecimal()
                            ticketTotalPrice shouldBeEqualComparingTo 10.toBigDecimal()
                            screeningId shouldBe testData.screeningId
                            movieCode shouldBe testData.movieCode
                            screeningDate shouldBe testData.screeningDate
                        }

                        with(items[1] as UseCardsTicketBasketItem) {
                            basketItemId shouldBe basketItem3.id
                            type shouldBe CardsBasketItemType.TICKET
                            ticketBasePrice shouldBeEqualComparingTo 10.toBigDecimal()
                            ticketTotalPrice shouldBeEqualComparingTo 10.toBigDecimal()
                            screeningId shouldBe testData.screeningId
                            movieCode shouldBe testData.movieCode
                            screeningDate shouldBe testData.screeningDate
                        }
                    }
                }
            )
        }
    }

    @Test
    fun `test applyCardsOnBasket & useCardsOnBasket - basket with two tickets added one by one, discount subject applicable to one ticket only - should switch the discount to the more expensive ticket`() {
        // 1. APPLY CARDS ON BASKET
        val testData = prepareSingleTicketTestData()
        val card = integrationDataTestHelper.getCard()

        every { cinemaxCardsConnectorMock.listCardsUsages(any()) } returns listOf(
            AvailableCardUsageTicketResponse(
                basketItemId = testData.basketItemId,
                discountSubjectId = 100L,
                cardTemplateName = card.title,
                cardCode = card.code,
                discountedPrice = 0.0.toBigDecimal(),
                discountedBasePrice = 0.0.toBigDecimal(),
                originalPrice = 10.0.toBigDecimal(),
                originalBasePrice = 10.0.toBigDecimal(),
                discountValue = 10.0.toBigDecimal(),
                discountCurrency = CardsCurrency.EUR,
                discountSubject = CardsDiscountSubjectResponse(
                    id = 1L,
                    type = DiscountSubjectType.TICKET,
                    relativeDiscount = 1.toBigDecimal(),
                    canBeCombined = false,
                    includeSurcharges = false,
                    distributorFee = mapOf()
                )
            )
        ) andThen listOf(
            AvailableCardUsageTicketResponse(
                basketItemId = 2.toUUID(),
                discountSubjectId = 100L,
                cardTemplateName = card.title,
                cardCode = card.code,
                discountedPrice = 0.0.toBigDecimal(),
                discountedBasePrice = 0.0.toBigDecimal(),
                originalPrice = 12.0.toBigDecimal(),
                originalBasePrice = 12.0.toBigDecimal(),
                discountValue = 12.0.toBigDecimal(),
                discountCurrency = CardsCurrency.EUR,
                discountSubject = CardsDiscountSubjectResponse(
                    id = 1L,
                    type = DiscountSubjectType.TICKET,
                    relativeDiscount = 1.toBigDecimal(),
                    canBeCombined = false,
                    includeSurcharges = false,
                    distributorFee = mapOf()
                )
            )
        )

        every { applicationEventPublisherMock.publishEvent(any<CardUsedOnTicketBasketItemEvent>()) } just Runs

        underTest.applyCardsOnBasket(
            command = ApplyCardsOnBasketCommand(
                basketId = testData.basketId,
                cardIds = setOf(card.id),
                posType = CardsPosType.CINEMA
            )
        )

        val basket = basketJpaFinderService.getById(testData.basketId)
        with(basket) {
            totalPrice shouldBeEqualComparingTo 0.0.toBigDecimal()
        }

        val basketItems = basketItemJpaFinderService.findAllNonDeletedByBasketId(testData.basketId).sortedBy { it.createdAt }
        basketItems.size shouldBe 1

        with(basketItems[0]) {
            price shouldBeEqualComparingTo 0.0.toBigDecimal()
            originalPrice shouldBeEqualComparingTo 10.0.toBigDecimal()
            fixedPrice shouldBeEqualComparingTo 0.0.toBigDecimal()
        }

        val cardUsages = cardUsageFinderService.findAll().sortedBy { it.createdAt }
        cardUsages.size shouldBe 1

        with(cardUsages[0]) {
            cardId shouldBe card.id
            basketId shouldBe testData.basketId
            basketItemId shouldBe testData.basketItemId
            originalId shouldBe null
            discountPercentage shouldBe 100
        }

        // add another, more expensive ticket
        val seat2 = integrationDataTestHelper.getSeat(
            originalId = 2,
            auditoriumId = testData.auditoriumId,
            auditoriumLayoutId = testData.auditoriumLayoutId
        )
        val ticketPrice2 = integrationDataTestHelper.getTicketPrice(
            seatId = seat2.id,
            screeningId = testData.screeningId,
            basePrice = 10.toBigDecimal(),
            totalPrice = 12.toBigDecimal()
        )
        val reservation2 = integrationDataTestHelper.getReservation(
            seatId = seat2.id,
            screeningId = testData.screeningId
        )
        val ticket2 = integrationDataTestHelper.getTicket(
            screeningId = testData.screeningId,
            reservationId = reservation2.id,
            ticketPriceId = ticketPrice2.id
        )
        val basketItem2 = integrationDataTestHelper.getBasketItem(
            id = 2.toUUID(),
            basketId = testData.basketId,
            ticketId = ticket2.id,
            type = BasketItemType.TICKET,
            price = 12.toBigDecimal()
        )

        underTest.applyCardsOnBasket(
            command = ApplyCardsOnBasketCommand(
                basketId = testData.basketId,
                cardIds = setOf(card.id),
                posType = CardsPosType.CINEMA
            )
        )

        val updatedBasket = basketJpaFinderService.getById(testData.basketId)
        with(updatedBasket) {
            totalPrice shouldBeEqualComparingTo 10.0.toBigDecimal()
        }

        val updatedBasketItems = basketItemJpaFinderService.findAllNonDeletedByBasketId(testData.basketId).sortedBy {
            it.createdAt
        }
        updatedBasketItems.size shouldBe 2

        with(updatedBasketItems[0]) {
            price shouldBeEqualComparingTo 10.0.toBigDecimal()
            originalPrice shouldBeEqualComparingTo 10.0.toBigDecimal()
            fixedPrice shouldBeEqualComparingTo 0.0.toBigDecimal()
        }
        with(updatedBasketItems[1]) {
            price shouldBeEqualComparingTo 0.0.toBigDecimal()
            originalPrice shouldBeEqualComparingTo 12.0.toBigDecimal()
            fixedPrice shouldBeEqualComparingTo 0.0.toBigDecimal()
        }

        val cardUsages2 = cardUsageFinderService.findAll().sortedBy { it.createdAt }
        cardUsages2.size shouldBe 1

        with(cardUsages2[0]) {
            cardId shouldBe card.id
            basketId shouldBe testData.basketId
            basketItemId shouldBe basketItem2.id
            originalId shouldBe null
        }

        // 2. USE CARDS ON BASKET
        every { cinemaxCardsConnectorMock.useCards(any()) } returns listOf(
            CardUsageTicketResponse(
                id = 1L,
                cardCode = "CU5X2URJ",
                branch = CardsBranch.BA,
                country = CardsCountry.SK,
                discountAmount = 12.toBigDecimal(),
                discountCurrency = CardsCurrency.EUR,
                discountSubject = CardsDiscountSubjectResponse(
                    id = 1L,
                    type = DiscountSubjectType.TICKET,
                    relativeDiscount = 1.toBigDecimal()
                ),
                discountedPrice = 0.0.toBigDecimal(),
                discountedBasePrice = 0.0.toBigDecimal(),
                originalPrice = 12.0.toBigDecimal(),
                pos = CardsPosType.CINEMA,
                basketId = testData.basketId,
                basketItemId = basketItem2.id,
                state = CardsUsageState.CREATED,
                timestamp = LocalDateTime.now(),
                canBeCombined = false,
                screeningDate = testData.screeningDate,
                screeningId = 10.toUUID(),
                movieCode = testData.movieCode,
                overridePosDistributorFee = emptyMap()
            )
        )

        basketService.addAppliedBasketCardId(
            AddAppliedBasketCardIdCommand(
                basketId = testData.basketId,
                cardId = card.id
            )
        )

        underTest.useCardsOnBasket(
            UseCardsOnBasketCommand(
                basketId = testData.basketId,
                posType = CardsPosType.CINEMA
            )
        )

        val updatedCardUsages = cardUsageFinderService.findAll().sortedBy { it.createdAt }
        updatedCardUsages.size shouldBe 1
        updatedCardUsages[0].originalId shouldBe 1L

        verifySequence {
            cinemaxCardsConnectorMock.listCardsUsages(
                withArg<UseCardsRequest> { request ->
                    request.cardCodes shouldBe setOf("CU5X2URJ")

                    with(request.payload) {
                        basketId shouldBe testData.basketId
                        type shouldBe CardsBasketType.BASKET
                        pos shouldBe CardsPosType.CINEMA
                        branch shouldBe CardsBranch.BA
                        currency shouldBe CardsCurrency.EUR
                        items.size shouldBe 1

                        with(items[0] as UseCardsTicketBasketItem) {
                            basketItemId shouldBe testData.basketItemId
                            type shouldBe CardsBasketItemType.TICKET
                            ticketBasePrice shouldBeEqualComparingTo 10.toBigDecimal()
                            ticketTotalPrice shouldBeEqualComparingTo 10.toBigDecimal()
                            screeningId shouldBe testData.screeningId
                            movieCode shouldBe testData.movieCode
                            screeningDate shouldBe testData.screeningDate
                        }
                    }
                }
            )

            applicationEventPublisherMock.publishEvent(
                withArg<CardUsedOnTicketBasketItemEvent> { event ->
                    event.ticketId shouldBe testData.ticketId
                    event.price shouldBeEqualComparingTo 0.0.toBigDecimal()
                    event.basePrice shouldBeEqualComparingTo 0.0.toBigDecimal()
                    event.zeroFees shouldBe false
                }
            )

            cinemaxCardsConnectorMock.listCardsUsages(
                withArg<UseCardsRequest> { request ->
                    request.cardCodes shouldBe setOf("CU5X2URJ")

                    with(request.payload) {
                        basketId shouldBe testData.basketId
                        type shouldBe CardsBasketType.BASKET
                        pos shouldBe CardsPosType.CINEMA
                        branch shouldBe CardsBranch.BA
                        currency shouldBe CardsCurrency.EUR
                        items.size shouldBe 2

                        with(items[0] as UseCardsTicketBasketItem) {
                            basketItemId shouldBe testData.basketItemId
                            type shouldBe CardsBasketItemType.TICKET
                            ticketBasePrice shouldBeEqualComparingTo 10.toBigDecimal()
                            ticketTotalPrice shouldBeEqualComparingTo 10.toBigDecimal()
                            screeningId shouldBe testData.screeningId
                            movieCode shouldBe testData.movieCode
                            screeningDate shouldBe testData.screeningDate
                        }

                        with(items[1] as UseCardsTicketBasketItem) {
                            basketItemId shouldBe basketItem2.id
                            type shouldBe CardsBasketItemType.TICKET
                            ticketBasePrice shouldBeEqualComparingTo 10.toBigDecimal()
                            ticketTotalPrice shouldBeEqualComparingTo 12.toBigDecimal()
                            screeningId shouldBe testData.screeningId
                            movieCode shouldBe testData.movieCode
                            screeningDate shouldBe testData.screeningDate
                        }
                    }
                }
            )

            applicationEventPublisherMock.publishEvent(
                withArg<CardUsedOnTicketBasketItemEvent> { event ->
                    event.ticketId shouldBe ticket2.id
                    event.price shouldBeEqualComparingTo 0.0.toBigDecimal()
                    event.basePrice shouldBeEqualComparingTo 0.0.toBigDecimal()
                    event.zeroFees shouldBe false
                }
            )

            cinemaxCardsConnectorMock.useCards(
                withArg<UseCardsRequest> { request ->
                    request.cardCodes shouldBe setOf("CU5X2URJ")

                    with(request.payload) {
                        basketId shouldBe testData.basketId
                        type shouldBe CardsBasketType.BASKET
                        pos shouldBe CardsPosType.CINEMA
                        branch shouldBe CardsBranch.BA
                        currency shouldBe CardsCurrency.EUR
                        items.size shouldBe 1

                        with(items[0] as UseCardsTicketBasketItem) {
                            basketItemId shouldBe basketItem2.id
                            type shouldBe CardsBasketItemType.TICKET
                            ticketBasePrice shouldBeEqualComparingTo 10.toBigDecimal()
                            ticketTotalPrice shouldBeEqualComparingTo 12.toBigDecimal()
                            screeningId shouldBe testData.screeningId
                            movieCode shouldBe testData.movieCode
                            screeningDate shouldBe testData.screeningDate
                        }
                    }
                }
            )
        }
    }

    @Test
    fun `test applyCardsOnBasket & useCardsOnBasket - basket with one product, card with unlimited 100 percent discount on products - should successfully use card`() {
        // 1. APPLY CARDS ON BASKET
        val testData = prepareSingleProductTestData()
        val card = integrationDataTestHelper.getCard()

        every { cinemaxCardsConnectorMock.listCardsUsages(any()) } returns listOf(
            AvailableCardUsageProductResponse(
                basketItemId = testData.basketItemId,
                discountSubjectId = 100L,
                cardTemplateName = card.title,
                cardCode = card.code,
                discountedPrice = 0.0.toBigDecimal(),
                originalPrice = 2.0.toBigDecimal(),
                originalItemPrice = 2.0.toBigDecimal(),
                availableQuantity = 1,
                discountValue = 2.toBigDecimal(),
                discountCurrency = CardsCurrency.EUR,
                discountSubject = CardsDiscountSubjectResponse(
                    id = 1L,
                    type = DiscountSubjectType.PRODUCT,
                    relativeDiscount = 1.toBigDecimal(),
                    canBeCombined = false,
                    includeSurcharges = false,
                    distributorFee = mapOf()
                )
            )
        )

        underTest.applyCardsOnBasket(
            command = ApplyCardsOnBasketCommand(
                basketId = testData.basketId,
                cardIds = setOf(card.id),
                posType = CardsPosType.CINEMA
            )
        )

        val basket = basketJpaFinderService.getById(testData.basketId)
        with(basket) {
            totalPrice shouldBeEqualComparingTo 0.0.toBigDecimal()
        }

        val basketItem = basketItemJpaFinderService.getNonDeletedById(testData.basketItemId)
        with(basketItem) {
            price shouldBeEqualComparingTo 0.0.toBigDecimal()
            originalPrice shouldBeEqualComparingTo 2.0.toBigDecimal()
            fixedPrice shouldBeEqualComparingTo 0.0.toBigDecimal()
            vatAmount shouldBeEqualComparingTo 0.0.toBigDecimal()
        }

        val cardUsage = cardUsageFinderService.findAll().first()
        with(cardUsage) {
            cardId shouldBe card.id
            basketId shouldBe testData.basketId
            basketItemId shouldBe testData.basketItemId
            originalId shouldBe null
            discountPercentage shouldBe 100
        }

        // 2. USE CARDS ON BASKET
        every { cinemaxCardsConnectorMock.useCards(any()) } returns listOf(
            CardUsageProductResponse(
                id = 1L,
                cardCode = "CU5X2URJ",
                branch = CardsBranch.BA,
                country = CardsCountry.SK,
                discountAmount = 2.toBigDecimal(),
                discountCurrency = CardsCurrency.EUR,
                discountSubject = CardsDiscountSubjectResponse(
                    id = 1L,
                    type = DiscountSubjectType.PRODUCT,
                    relativeDiscount = 1.0.toBigDecimal()
                ),
                discountedPrice = 0.0.toBigDecimal(),
                originalPrice = 2.0.toBigDecimal(),
                quantity = 1,
                pos = CardsPosType.CINEMA,
                basketId = testData.basketId,
                basketItemId = testData.basketItemId,
                state = CardsUsageState.CREATED,
                timestamp = LocalDateTime.now(),
                canBeCombined = false,
                productCode = testData.productCode
            )
        )

        basketService.addAppliedBasketCardId(
            AddAppliedBasketCardIdCommand(
                basketId = testData.basketId,
                cardId = card.id
            )
        )

        underTest.useCardsOnBasket(
            UseCardsOnBasketCommand(
                basketId = testData.basketId,
                posType = CardsPosType.CINEMA
            )
        )

        val cardUsages = cardUsageFinderService.findAll()
        cardUsages.size shouldBe 1
        cardUsages[0].originalId shouldBe 1L

        verifySequence {
            cinemaxCardsConnectorMock.listCardsUsages(
                withArg<UseCardsRequest> { request ->
                    request.cardCodes shouldBe setOf("CU5X2URJ")

                    with(request.payload) {
                        basketId shouldBe testData.basketId
                        type shouldBe CardsBasketType.BASKET
                        pos shouldBe CardsPosType.CINEMA
                        branch shouldBe CardsBranch.BA
                        currency shouldBe CardsCurrency.EUR
                        items.size shouldBe 1

                        with(items[0] as UseCardsProductBasketItem) {
                            basketItemId shouldBe testData.basketItemId
                            type shouldBe CardsBasketItemType.PRODUCT
                            itemPrice shouldBeEqualComparingTo 2.0.toBigDecimal()
                            quantity shouldBe 1
                            productCode shouldBe testData.productCode
                        }
                    }
                }
            )

            cinemaxCardsConnectorMock.useCards(
                withArg<UseCardsRequest> { request ->
                    request.cardCodes shouldBe setOf("CU5X2URJ")

                    with(request.payload) {
                        basketId shouldBe testData.basketId
                        type shouldBe CardsBasketType.BASKET
                        pos shouldBe CardsPosType.CINEMA
                        branch shouldBe CardsBranch.BA
                        currency shouldBe CardsCurrency.EUR
                        items.size shouldBe 1

                        with(items[0] as UseCardsProductBasketItem) {
                            basketItemId shouldBe testData.basketItemId
                            type shouldBe CardsBasketItemType.PRODUCT
                            itemPrice shouldBeEqualComparingTo 2.0.toBigDecimal()
                            quantity shouldBe 1
                            productCode shouldBe testData.productCode
                        }
                    }
                }
            )

            applicationEventPublisherMock wasNot Called
        }
    }

    @Test
    fun `test applyCardsOnBasket & useCardsOnBasket - basket with multiple products and quantities, discount subject 25 percent on all products + packaging discount - should successfully use card`() {
        // 1. APPLY CARDS ON BASKET
        val testData = prepareSingleProductTestData()

        // another regular product - popcorn
        val productComponentCategory2 = integrationDataTestHelper.getProductComponentCategory(
            originalId = 2,
            code = "02",
            title = "Popcorn Bufet"
        )
        val productComponent2 = integrationDataTestHelper.getProductComponent(
            originalId = 2,
            code = "02",
            title = "Kukurica",
            unit = ProductComponentUnit.KS,
            stockQuantity = 1000.toBigDecimal(),
            productComponentCategoryId = productComponentCategory2.id
        )
        val productCategory2 = integrationDataTestHelper.getProductCategory(
            originalId = 2,
            code = "02",
            title = "Popcorn Bufet",
            taxRate = SUPER_REDUCED_TAX_RATE
        )
        val product2 = integrationDataTestHelper.getProduct(
            originalId = 2,
            title = "Popcorn XXL 6l",
            price = 5.5.toBigDecimal(),
            productCategoryId = productCategory2.id
        )
        integrationDataTestHelper.getProductComposition(
            originalId = 2,
            productId = product2.id,
            productComponentId = productComponent2.id,
            amount = 1.toBigDecimal()
        )
        val basketItem2 = integrationDataTestHelper.getBasketItem(
            basketId = testData.basketId,
            productId = product2.id,
            type = BasketItemType.PRODUCT,
            price = 11.0.toBigDecimal(),
            quantity = 2,
            vatAmount = 0.52.toBigDecimal()
        )

        // packaging deposit product - must never be discounted
        val productCategory3 = integrationDataTestHelper.getProductCategory(
            originalId = 3,
            code = "03",
            title = "Záloha",
            taxRate = NO_TAX_RATE
        )
        val product3 = integrationDataTestHelper.getProduct(
            originalId = 3,
            title = "Záloha za obal",
            price = 0.15.toBigDecimal(),
            productCategoryId = productCategory3.id,
            isPackagingDeposit = true,
            type = ProductType.ADDITIONAL_SALE,
            taxRate = 0
        )
        val basketItem3 = integrationDataTestHelper.getBasketItem(
            basketId = testData.basketId,
            productId = product3.id,
            type = BasketItemType.PRODUCT,
            price = 0.15.toBigDecimal(),
            quantity = 1,
            vatAmount = 0.0.toBigDecimal()
        )

        val card = integrationDataTestHelper.getCard()

        every { cinemaxCardsConnectorMock.listCardsUsages(any()) } returns listOf(
            AvailableCardUsageProductResponse(
                basketItemId = testData.basketItemId,
                discountSubjectId = 100L,
                cardTemplateName = card.title,
                cardCode = card.code,
                discountedPrice = 1.5.toBigDecimal(),
                originalPrice = 2.0.toBigDecimal(),
                originalItemPrice = 2.0.toBigDecimal(),
                availableQuantity = 1,
                discountValue = 0.5.toBigDecimal(),
                discountCurrency = CardsCurrency.EUR,
                discountSubject = CardsDiscountSubjectResponse(
                    id = 1L,
                    type = DiscountSubjectType.PRODUCT,
                    relativeDiscount = 0.25.toBigDecimal(),
                    canBeCombined = false,
                    includeSurcharges = false,
                    distributorFee = mapOf()
                )
            ),
            AvailableCardUsageProductResponse(
                basketItemId = basketItem2.id,
                discountSubjectId = 100L,
                cardTemplateName = card.title,
                cardCode = card.code,
                discountedPrice = 8.25.toBigDecimal(),
                originalPrice = 11.0.toBigDecimal(),
                originalItemPrice = 5.5.toBigDecimal(),
                availableQuantity = 2,
                discountValue = 2.75.toBigDecimal(),
                discountCurrency = CardsCurrency.EUR,
                discountSubject = CardsDiscountSubjectResponse(
                    id = 1L,
                    type = DiscountSubjectType.PRODUCT,
                    relativeDiscount = 0.25.toBigDecimal(),
                    canBeCombined = false,
                    includeSurcharges = false,
                    distributorFee = mapOf()
                )
            )
        )

        underTest.applyCardsOnBasket(
            command = ApplyCardsOnBasketCommand(
                basketId = testData.basketId,
                cardIds = setOf(card.id),
                posType = CardsPosType.CINEMA
            )
        )

        val basket = basketJpaFinderService.getById(testData.basketId)
        with(basket) {
            totalPrice shouldBeEqualComparingTo 9.9.toBigDecimal()
        }

        val updatedBasketItem1 = basketItemJpaFinderService.getNonDeletedById(testData.basketItemId)
        with(updatedBasketItem1) {
            price shouldBeEqualComparingTo 1.5.toBigDecimal()
            originalPrice shouldBeEqualComparingTo 2.0.toBigDecimal()
            vatAmount shouldBeEqualComparingTo 0.24.toBigDecimal()
        }

        val updatedBasketItem2 = basketItemJpaFinderService.getNonDeletedById(basketItem2.id)
        with(updatedBasketItem2) {
            price shouldBeEqualComparingTo 8.25.toBigDecimal()
            originalPrice shouldBeEqualComparingTo 11.0.toBigDecimal()
            vatAmount shouldBeEqualComparingTo 0.39.toBigDecimal()
        }

        val notUpdatedBasketItem3 = basketItemJpaFinderService.getNonDeletedById(basketItem3.id)
        with(notUpdatedBasketItem3) {
            price shouldBeEqualComparingTo 0.15.toBigDecimal()
            originalPrice shouldBeEqualComparingTo 0.15.toBigDecimal()
            vatAmount shouldBeEqualComparingTo 0.0.toBigDecimal()
        }

        val cardUsages = cardUsageFinderService.findAll().sortedBy { it.createdAt }
        cardUsages.size shouldBe 2

        with(cardUsages[0]) {
            cardId shouldBe card.id
            basketId shouldBe testData.basketId
            basketItemId shouldBe testData.basketItemId
            originalId shouldBe null
            discountPercentage shouldBe 25
        }
        with(cardUsages[1]) {
            cardId shouldBe card.id
            basketId shouldBe testData.basketId
            basketItemId shouldBe basketItem2.id
            originalId shouldBe null
            discountPercentage shouldBe 25
        }

        // 2. USE CARDS ON BASKET
        every { cinemaxCardsConnectorMock.useCards(any()) } returns listOf(
            CardUsageProductResponse(
                id = 1L,
                cardCode = "CU5X2URJ",
                branch = CardsBranch.BA,
                country = CardsCountry.SK,
                discountAmount = 0.5.toBigDecimal(),
                discountCurrency = CardsCurrency.EUR,
                discountSubject = CardsDiscountSubjectResponse(
                    id = 1L,
                    type = DiscountSubjectType.PRODUCT,
                    relativeDiscount = 0.25.toBigDecimal()
                ),
                discountedPrice = 1.5.toBigDecimal(),
                originalPrice = 2.0.toBigDecimal(),
                quantity = 1,
                pos = CardsPosType.CINEMA,
                basketId = testData.basketId,
                basketItemId = testData.basketItemId,
                state = CardsUsageState.CREATED,
                timestamp = LocalDateTime.now(),
                canBeCombined = false,
                productCode = testData.productCode
            ),
            CardUsageProductResponse(
                id = 2L,
                cardCode = "CU5X2URJ",
                branch = CardsBranch.BA,
                country = CardsCountry.SK,
                discountAmount = 2.75.toBigDecimal(),
                discountCurrency = CardsCurrency.EUR,
                discountSubject = CardsDiscountSubjectResponse(
                    id = 1L,
                    type = DiscountSubjectType.PRODUCT,
                    relativeDiscount = 0.25.toBigDecimal()
                ),
                discountedPrice = 8.25.toBigDecimal(),
                originalPrice = 11.0.toBigDecimal(),
                quantity = 2,
                pos = CardsPosType.CINEMA,
                basketId = testData.basketId,
                basketItemId = basketItem2.id,
                state = CardsUsageState.CREATED,
                timestamp = LocalDateTime.now(),
                canBeCombined = false,
                productCode = product2.code
            )
        )

        basketService.addAppliedBasketCardId(
            AddAppliedBasketCardIdCommand(
                basketId = testData.basketId,
                cardId = card.id
            )
        )

        underTest.useCardsOnBasket(
            UseCardsOnBasketCommand(
                basketId = testData.basketId,
                posType = CardsPosType.CINEMA
            )
        )

        val updatedCardUsages = cardUsageFinderService.findAll()
        updatedCardUsages.size shouldBe 2
        updatedCardUsages[0].originalId shouldBe 1L
        updatedCardUsages[1].originalId shouldBe 2L

        verifySequence {
            cinemaxCardsConnectorMock.listCardsUsages(
                withArg<UseCardsRequest> { request ->
                    request.cardCodes shouldBe setOf("CU5X2URJ")

                    with(request.payload) {
                        basketId shouldBe testData.basketId
                        type shouldBe CardsBasketType.BASKET
                        pos shouldBe CardsPosType.CINEMA
                        branch shouldBe CardsBranch.BA
                        currency shouldBe CardsCurrency.EUR
                        items.size shouldBe 2

                        with(items[0] as UseCardsProductBasketItem) {
                            basketItemId shouldBe testData.basketItemId
                            type shouldBe CardsBasketItemType.PRODUCT
                            itemPrice shouldBeEqualComparingTo 2.0.toBigDecimal()
                            quantity shouldBe 1
                            productCode shouldBe testData.productCode
                        }

                        with(items[1] as UseCardsProductBasketItem) {
                            basketItemId shouldBe basketItem2.id
                            type shouldBe CardsBasketItemType.PRODUCT
                            itemPrice shouldBeEqualComparingTo 5.5.toBigDecimal()
                            quantity shouldBe 2
                            productCode shouldBe product2.code
                        }
                    }
                }
            )

            cinemaxCardsConnectorMock.useCards(
                withArg<UseCardsRequest> { request ->
                    request.cardCodes shouldBe setOf("CU5X2URJ")

                    with(request.payload) {
                        basketId shouldBe testData.basketId
                        type shouldBe CardsBasketType.BASKET
                        pos shouldBe CardsPosType.CINEMA
                        branch shouldBe CardsBranch.BA
                        currency shouldBe CardsCurrency.EUR
                        items.size shouldBe 2

                        with(items[0] as UseCardsProductBasketItem) {
                            basketItemId shouldBe testData.basketItemId
                            type shouldBe CardsBasketItemType.PRODUCT
                            itemPrice shouldBeEqualComparingTo 2.0.toBigDecimal()
                            quantity shouldBe 1
                            productCode shouldBe testData.productCode
                        }

                        with(items[1] as UseCardsProductBasketItem) {
                            basketItemId shouldBe basketItem2.id
                            type shouldBe CardsBasketItemType.PRODUCT
                            itemPrice shouldBeEqualComparingTo 5.5.toBigDecimal()
                            quantity shouldBe 2
                            productCode shouldBe product2.code
                        }
                    }
                }
            )

            applicationEventPublisherMock wasNot Called
        }
    }

    @Test
    fun `test applyCardsOnBasket & useCardsOnBasket - basket with one ticket and one product, card with two discount subjects - should successfully use card`() {
        // 1. APPLY CARDS ON BASKET
        val ticketTestData = prepareSingleTicketTestData()
        val productTestData = prepareSingleProductTestData()

        val basket = integrationDataTestHelper.getBasket(
            totalPrice = 12.0.toBigDecimal(),
            state = BasketState.OPEN
        )

        val ticketBasketItem = integrationDataTestHelper.getBasketItem(
            basketId = basket.id,
            ticketId = ticketTestData.ticketId,
            type = BasketItemType.TICKET,
            price = 10.0.toBigDecimal()
        )

        val productBasketItem = integrationDataTestHelper.getBasketItem(
            basketId = basket.id,
            productId = productTestData.productId,
            type = BasketItemType.PRODUCT,
            price = 2.0.toBigDecimal(),
            vatAmount = 0.32.toBigDecimal()
        )

        val card = integrationDataTestHelper.getCard()

        every { cinemaxCardsConnectorMock.listCardsUsages(any()) } returns listOf(
            AvailableCardUsageTicketResponse(
                basketItemId = ticketBasketItem.id,
                discountSubjectId = 100L,
                cardTemplateName = card.title,
                cardCode = card.code,
                discountedPrice = 8.0.toBigDecimal(),
                discountedBasePrice = 8.0.toBigDecimal(),
                originalPrice = 10.0.toBigDecimal(),
                originalBasePrice = 10.0.toBigDecimal(),
                discountValue = 2.toBigDecimal(),
                discountCurrency = CardsCurrency.EUR,
                discountSubject = CardsDiscountSubjectResponse(
                    id = 1L,
                    type = DiscountSubjectType.TICKET,
                    relativeDiscount = 0.2.toBigDecimal(),
                    canBeCombined = false,
                    includeSurcharges = false,
                    distributorFee = mapOf()
                )
            ),
            AvailableCardUsageProductResponse(
                basketItemId = productBasketItem.id,
                discountSubjectId = 101L,
                cardTemplateName = card.title,
                cardCode = card.code,
                discountedPrice = 0.0.toBigDecimal(),
                originalPrice = 2.0.toBigDecimal(),
                originalItemPrice = 2.0.toBigDecimal(),
                availableQuantity = 1,
                discountValue = 2.toBigDecimal(),
                discountCurrency = CardsCurrency.EUR,
                discountSubject = CardsDiscountSubjectResponse(
                    id = 1L,
                    type = DiscountSubjectType.PRODUCT,
                    relativeDiscount = 1.toBigDecimal(),
                    canBeCombined = false,
                    includeSurcharges = false,
                    distributorFee = mapOf()
                )
            )
        )

        every { applicationEventPublisherMock.publishEvent(any<CardUsedOnTicketBasketItemEvent>()) } just Runs

        underTest.applyCardsOnBasket(
            command = ApplyCardsOnBasketCommand(
                basketId = basket.id,
                cardIds = setOf(card.id),
                posType = CardsPosType.CINEMA
            )
        )

        // perform basket price recalculation, normally executed using event listener
        basketService.recalculateBasketTotalPrice(
            RecalculateBasketTotalPriceCommand(
                basketId = basket.id
            )
        )

        val discountedBasket = basketJpaFinderService.getById(basket.id)
        with(discountedBasket) {
            totalPrice shouldBeEqualComparingTo 8.0.toBigDecimal()
        }

        val discountedTicketBasketItem = basketItemJpaFinderService.getNonDeletedById(ticketBasketItem.id)
        with(discountedTicketBasketItem) {
            price shouldBeEqualComparingTo 8.0.toBigDecimal()
            originalPrice shouldBeEqualComparingTo 10.0.toBigDecimal()
            fixedPrice shouldBeEqualComparingTo 0.0.toBigDecimal()
            vatAmount shouldBeEqualComparingTo 1.5.toBigDecimal()
        }

        val discountedProductBasketItem = basketItemJpaFinderService.getNonDeletedById(productBasketItem.id)
        with(discountedProductBasketItem) {
            price shouldBeEqualComparingTo 0.0.toBigDecimal()
            originalPrice shouldBeEqualComparingTo 2.0.toBigDecimal()
            fixedPrice shouldBeEqualComparingTo 0.0.toBigDecimal()
            vatAmount shouldBeEqualComparingTo 0.0.toBigDecimal()
        }

        val cardUsages = cardUsageFinderService.findAll().sortedBy { it.createdAt }
        cardUsages.size shouldBe 2

        with(cardUsages[0]) {
            cardId shouldBe card.id
            basketId shouldBe basket.id
            basketItemId shouldBe ticketBasketItem.id
            originalId shouldBe null
            discountPercentage shouldBe 20
        }
        with(cardUsages[1]) {
            cardId shouldBe card.id
            basketId shouldBe basket.id
            basketItemId shouldBe productBasketItem.id
            originalId shouldBe null
            discountPercentage shouldBe 100
        }

        // 2. USE CARDS ON BASKET
        every { cinemaxCardsConnectorMock.useCards(any()) } returns listOf(
            CardUsageTicketResponse(
                id = 1L,
                cardCode = "CU5X2URJ",
                branch = CardsBranch.BA,
                country = CardsCountry.SK,
                discountAmount = 2.toBigDecimal(),
                discountCurrency = CardsCurrency.EUR,
                discountSubject = CardsDiscountSubjectResponse(
                    id = 1L,
                    type = DiscountSubjectType.TICKET,
                    relativeDiscount = 0.2.toBigDecimal()
                ),
                discountedPrice = 8.0.toBigDecimal(),
                discountedBasePrice = 8.0.toBigDecimal(),
                originalPrice = 10.0.toBigDecimal(),
                pos = CardsPosType.CINEMA,
                basketId = ticketBasketItem.basketId,
                basketItemId = ticketBasketItem.id,
                state = CardsUsageState.CREATED,
                timestamp = LocalDateTime.now(),
                canBeCombined = false,
                screeningDate = ticketTestData.screeningDate,
                screeningId = 10.toUUID(),
                movieCode = ticketTestData.movieCode,
                overridePosDistributorFee = emptyMap()
            ),
            CardUsageProductResponse(
                id = 2L,
                cardCode = "CU5X2URJ",
                branch = CardsBranch.BA,
                country = CardsCountry.SK,
                discountAmount = 2.toBigDecimal(),
                discountCurrency = CardsCurrency.EUR,
                discountSubject = CardsDiscountSubjectResponse(
                    id = 2L,
                    type = DiscountSubjectType.PRODUCT,
                    relativeDiscount = 1.0.toBigDecimal()
                ),
                discountedPrice = 0.0.toBigDecimal(),
                originalPrice = 2.0.toBigDecimal(),
                quantity = 1,
                pos = CardsPosType.CINEMA,
                basketId = productBasketItem.basketId,
                basketItemId = productBasketItem.id,
                state = CardsUsageState.CREATED,
                timestamp = LocalDateTime.now(),
                canBeCombined = false,
                productCode = productTestData.productCode
            )
        )

        basketService.addAppliedBasketCardId(
            AddAppliedBasketCardIdCommand(
                basketId = basket.id,
                cardId = card.id
            )
        )

        underTest.useCardsOnBasket(
            UseCardsOnBasketCommand(
                basketId = basket.id,
                posType = CardsPosType.CINEMA
            )
        )

        val updatedCardUsages = cardUsageFinderService.findAll().sortedBy { it.createdAt }
        updatedCardUsages.size shouldBe 2
        updatedCardUsages[0].originalId shouldBe 1L
        updatedCardUsages[1].originalId shouldBe 2L

        verifySequence {
            cinemaxCardsConnectorMock.listCardsUsages(
                withArg<UseCardsRequest> { request ->
                    request.cardCodes shouldBe setOf("CU5X2URJ")

                    with(request.payload) {
                        basketId shouldBe ticketBasketItem.basketId
                        type shouldBe CardsBasketType.BASKET
                        pos shouldBe CardsPosType.CINEMA
                        branch shouldBe CardsBranch.BA
                        currency shouldBe CardsCurrency.EUR
                        items.size shouldBe 2

                        with(items[0] as UseCardsTicketBasketItem) {
                            basketItemId shouldBe ticketBasketItem.id
                            type shouldBe CardsBasketItemType.TICKET
                            ticketBasePrice shouldBeEqualComparingTo 10.toBigDecimal()
                            ticketTotalPrice shouldBeEqualComparingTo 10.toBigDecimal()
                            screeningId shouldBe ticketTestData.screeningId
                            movieCode shouldBe ticketTestData.movieCode
                            screeningDate shouldBe ticketTestData.screeningDate
                        }

                        with(items[1] as UseCardsProductBasketItem) {
                            basketItemId shouldBe productBasketItem.id
                            type shouldBe CardsBasketItemType.PRODUCT
                            itemPrice shouldBeEqualComparingTo 2.0.toBigDecimal()
                            quantity shouldBe 1
                            productCode shouldBe productTestData.productCode
                        }
                    }
                }
            )

            applicationEventPublisherMock.publishEvent(
                withArg<CardUsedOnTicketBasketItemEvent> { event ->
                    event.ticketId shouldBe ticketBasketItem.ticketId
                    event.price shouldBeEqualComparingTo 8.0.toBigDecimal()
                    event.basePrice shouldBeEqualComparingTo 8.0.toBigDecimal()
                    event.zeroFees shouldBe false
                }
            )

            cinemaxCardsConnectorMock.useCards(
                withArg<UseCardsRequest> { request ->
                    request.cardCodes shouldBe setOf("CU5X2URJ")

                    with(request.payload) {
                        basketId shouldBe ticketBasketItem.basketId
                        type shouldBe CardsBasketType.BASKET
                        pos shouldBe CardsPosType.CINEMA
                        branch shouldBe CardsBranch.BA
                        currency shouldBe CardsCurrency.EUR
                        items.size shouldBe 2

                        with(items[0] as UseCardsTicketBasketItem) {
                            basketItemId shouldBe ticketBasketItem.id
                            type shouldBe CardsBasketItemType.TICKET
                            ticketBasePrice shouldBeEqualComparingTo 10.toBigDecimal()
                            ticketTotalPrice shouldBeEqualComparingTo 10.toBigDecimal()
                            screeningId shouldBe ticketTestData.screeningId
                            movieCode shouldBe ticketTestData.movieCode
                            screeningDate shouldBe ticketTestData.screeningDate
                        }

                        with(items[1] as UseCardsProductBasketItem) {
                            basketItemId shouldBe productBasketItem.id
                            type shouldBe CardsBasketItemType.PRODUCT
                            itemPrice shouldBeEqualComparingTo 2.0.toBigDecimal()
                            quantity shouldBe 1
                            productCode shouldBe productTestData.productCode
                        }
                    }
                }
            )
        }
    }

    @Test
    fun `test applyCardsOnBasket & useCardsOnBasket - combination of card discount and ticket discount, ticket discount first - should successfully apply discount`() {
        // 1. APPLY CARDS ON BASKET
        val testData = prepareSingleTicketTestData()

        val ticketDiscount = integrationDataTestHelper.getTicketDiscount(
            type = TicketDiscountType.PERCENTAGE,
            percentage = 10
        )

        ticketService.updateTicket(
            UpdateTicketCommand(
                ticketId = testData.ticketId,
                primaryTicketDiscountId = Optional.of(ticketDiscount.id),
                freeTicket = false
            )
        )

        val card = integrationDataTestHelper.getCard()

        every { cinemaxCardsConnectorMock.listCardsUsages(any()) } returns listOf(
            AvailableCardUsageTicketResponse(
                basketItemId = testData.basketItemId,
                discountSubjectId = 100L,
                cardTemplateName = card.title,
                cardCode = card.code,
                discountedPrice = 7.2.toBigDecimal(),
                discountedBasePrice = 7.2.toBigDecimal(),
                originalPrice = 9.0.toBigDecimal(),
                originalBasePrice = 9.0.toBigDecimal(),
                discountValue = 1.8.toBigDecimal(),
                discountCurrency = CardsCurrency.EUR,
                discountSubject = CardsDiscountSubjectResponse(
                    id = 1L,
                    type = DiscountSubjectType.TICKET,
                    relativeDiscount = 0.2.toBigDecimal(),
                    canBeCombined = false,
                    includeSurcharges = false,
                    distributorFee = mapOf()
                )
            )
        ) andThen listOf(
            AvailableCardUsageTicketResponse(
                basketItemId = testData.basketItemId,
                discountSubjectId = 100L,
                cardTemplateName = card.title,
                cardCode = card.code,
                discountedPrice = 7.2.toBigDecimal(),
                discountedBasePrice = 7.2.toBigDecimal(),
                originalPrice = 9.0.toBigDecimal(),
                originalBasePrice = 9.0.toBigDecimal(),
                discountValue = 1.8.toBigDecimal(),
                discountCurrency = CardsCurrency.EUR,
                discountSubject = CardsDiscountSubjectResponse(
                    id = 1L,
                    type = DiscountSubjectType.TICKET,
                    relativeDiscount = 0.2.toBigDecimal(),
                    canBeCombined = true,
                    includeSurcharges = false,
                    distributorFee = mapOf()
                )
            )
        )

        every { applicationEventPublisherMock.publishEvent(any<CardUsedOnTicketBasketItemEvent>()) } just Runs

        basketService.addAppliedBasketCardId(
            AddAppliedBasketCardIdCommand(
                basketId = testData.basketId,
                cardId = card.id
            )
        )

        // first request with canBeCombined: false should throw exception
        assertThrows<CardNotCombinableException> {
            underTest.applyCardsOnBasket(
                command = ApplyCardsOnBasketCommand(
                    basketId = testData.basketId,
                    cardIds = setOf(card.id),
                    posType = CardsPosType.CINEMA
                )
            )
        }

        // second request with canBeCombined: true shouldn't throw exception and apply the discount
        underTest.applyCardsOnBasket(
            command = ApplyCardsOnBasketCommand(
                basketId = testData.basketId,
                cardIds = setOf(card.id),
                posType = CardsPosType.CINEMA
            )
        )

        val basket = basketJpaFinderService.getById(testData.basketId)
        with(basket) {
            totalPrice shouldBeEqualComparingTo 7.2.toBigDecimal()
        }

        val basketItem = basketItemJpaFinderService.getNonDeletedById(testData.basketItemId)
        with(basketItem) {
            price shouldBeEqualComparingTo 7.2.toBigDecimal()
            originalPrice shouldBeEqualComparingTo 10.0.toBigDecimal()
            fixedPrice shouldBeEqualComparingTo 0.0.toBigDecimal()
            vatAmount shouldBeEqualComparingTo 1.35.toBigDecimal()
        }

        val cardUsage = cardUsageFinderService.findAll().first()
        with(cardUsage) {
            cardId shouldBe card.id
            basketId shouldBe testData.basketId
            basketItemId shouldBe testData.basketItemId
            originalId shouldBe null
            discountPercentage shouldBe 20
        }

        // 2. USE CARDS ON BASKET
        every { cinemaxCardsConnectorMock.useCards(any()) } returns listOf(
            CardUsageTicketResponse(
                id = 1L,
                cardCode = "CU5X2URJ",
                branch = CardsBranch.BA,
                country = CardsCountry.SK,
                discountAmount = 1.8.toBigDecimal(),
                discountCurrency = CardsCurrency.EUR,
                discountSubject = CardsDiscountSubjectResponse(
                    id = 1L,
                    type = DiscountSubjectType.TICKET,
                    relativeDiscount = 0.2.toBigDecimal()
                ),
                discountedPrice = 7.2.toBigDecimal(),
                discountedBasePrice = 7.2.toBigDecimal(),
                originalPrice = 9.0.toBigDecimal(),
                pos = CardsPosType.CINEMA,
                basketId = testData.basketId,
                basketItemId = testData.basketItemId,
                state = CardsUsageState.CREATED,
                timestamp = LocalDateTime.now(),
                canBeCombined = true,
                screeningDate = testData.screeningDate,
                screeningId = 10.toUUID(),
                movieCode = testData.movieCode,
                overridePosDistributorFee = emptyMap()
            )
        )

        underTest.useCardsOnBasket(
            UseCardsOnBasketCommand(
                basketId = testData.basketId,
                posType = CardsPosType.CINEMA
            )
        )

        val cardUsages = cardUsageFinderService.findAll()
        cardUsages.size shouldBe 1
        cardUsages[0].originalId shouldBe 1L

        verifySequence {
            cinemaxCardsConnectorMock.listCardsUsages(
                withArg<UseCardsRequest> { request ->
                    request.cardCodes shouldBe setOf("CU5X2URJ")

                    with(request.payload) {
                        basketId shouldBe testData.basketId
                        type shouldBe CardsBasketType.BASKET
                        pos shouldBe CardsPosType.CINEMA
                        branch shouldBe CardsBranch.BA
                        currency shouldBe CardsCurrency.EUR
                        items.size shouldBe 1

                        with(items[0] as UseCardsTicketBasketItem) {
                            basketItemId shouldBe testData.basketItemId
                            type shouldBe CardsBasketItemType.TICKET
                            ticketBasePrice shouldBeEqualComparingTo 9.0.toBigDecimal()
                            ticketTotalPrice shouldBeEqualComparingTo 9.0.toBigDecimal()
                            screeningId shouldBe testData.screeningId
                            movieCode shouldBe testData.movieCode
                            screeningDate shouldBe testData.screeningDate
                        }
                    }
                }
            )

            cinemaxCardsConnectorMock.listCardsUsages(
                withArg<UseCardsRequest> { request ->
                    request.cardCodes shouldBe setOf("CU5X2URJ")

                    with(request.payload) {
                        basketId shouldBe testData.basketId
                        type shouldBe CardsBasketType.BASKET
                        pos shouldBe CardsPosType.CINEMA
                        branch shouldBe CardsBranch.BA
                        currency shouldBe CardsCurrency.EUR
                        items.size shouldBe 1

                        with(items[0] as UseCardsTicketBasketItem) {
                            basketItemId shouldBe testData.basketItemId
                            type shouldBe CardsBasketItemType.TICKET
                            ticketBasePrice shouldBeEqualComparingTo 9.0.toBigDecimal()
                            ticketTotalPrice shouldBeEqualComparingTo 9.0.toBigDecimal()
                            screeningId shouldBe testData.screeningId
                            movieCode shouldBe testData.movieCode
                            screeningDate shouldBe testData.screeningDate
                        }
                    }
                }
            )

            applicationEventPublisherMock.publishEvent(
                withArg<CardUsedOnTicketBasketItemEvent> { event ->
                    event.ticketId shouldBe testData.ticketId
                    event.price shouldBeEqualComparingTo 7.2.toBigDecimal()
                    event.basePrice shouldBeEqualComparingTo 7.2.toBigDecimal()
                    event.zeroFees shouldBe false
                }
            )

            cinemaxCardsConnectorMock.useCards(
                withArg<UseCardsRequest> { request ->
                    request.cardCodes shouldBe setOf("CU5X2URJ")

                    with(request.payload) {
                        basketId shouldBe testData.basketId
                        type shouldBe CardsBasketType.BASKET
                        pos shouldBe CardsPosType.CINEMA
                        branch shouldBe CardsBranch.BA
                        currency shouldBe CardsCurrency.EUR
                        items.size shouldBe 1

                        with(items[0] as UseCardsTicketBasketItem) {
                            basketItemId shouldBe testData.basketItemId
                            type shouldBe CardsBasketItemType.TICKET
                            ticketBasePrice shouldBeEqualComparingTo 9.0.toBigDecimal()
                            ticketTotalPrice shouldBeEqualComparingTo 9.0.toBigDecimal()
                            screeningId shouldBe testData.screeningId
                            movieCode shouldBe testData.movieCode
                            screeningDate shouldBe testData.screeningDate
                        }
                    }
                }
            )
        }
    }

    @Test
    fun `test applyCardsOnBasket & useCardsOnBasket - basket with three tickets and multiple cards - should apply discount to two tickets, one for each card`() {
        // 1. APPLY CARDS ON BASKET
        val testData = prepareSingleTicketTestData()

        val seat2 = integrationDataTestHelper.getSeat(
            originalId = 2,
            auditoriumId = testData.auditoriumId,
            auditoriumLayoutId = testData.auditoriumLayoutId
        )
        val ticketPrice2 = integrationDataTestHelper.getTicketPrice(
            seatId = seat2.id,
            screeningId = testData.screeningId,
            basePrice = 10.toBigDecimal(),
            totalPrice = 10.toBigDecimal()
        )
        val reservation2 = integrationDataTestHelper.getReservation(
            seatId = seat2.id,
            screeningId = testData.screeningId
        )
        val ticket2 = integrationDataTestHelper.getTicket(
            screeningId = testData.screeningId,
            reservationId = reservation2.id,
            ticketPriceId = ticketPrice2.id
        )
        val basketItem2 = integrationDataTestHelper.getBasketItem(
            basketId = testData.basketId,
            ticketId = ticket2.id,
            type = BasketItemType.TICKET,
            price = 10.toBigDecimal()
        )

        val seat3 = integrationDataTestHelper.getSeat(
            originalId = 3,
            auditoriumId = testData.auditoriumId,
            auditoriumLayoutId = testData.auditoriumLayoutId
        )
        val ticketPrice3 = integrationDataTestHelper.getTicketPrice(
            seatId = seat3.id,
            screeningId = testData.screeningId,
            basePrice = 10.toBigDecimal(),
            totalPrice = 10.toBigDecimal()
        )
        val reservation3 = integrationDataTestHelper.getReservation(
            seatId = seat3.id,
            screeningId = testData.screeningId
        )
        val ticket3 = integrationDataTestHelper.getTicket(
            screeningId = testData.screeningId,
            reservationId = reservation3.id,
            ticketPriceId = ticketPrice3.id
        )
        val basketItem3 = integrationDataTestHelper.getBasketItem(
            basketId = testData.basketId,
            ticketId = ticket3.id,
            type = BasketItemType.TICKET,
            price = 10.toBigDecimal()
        )

        val card1 = integrationDataTestHelper.getCard()
        val card2 = integrationDataTestHelper.getCard(
            title = "Test Card 2",
            code = "C6JDY3GJ"
        )

        every { cinemaxCardsConnectorMock.listCardsUsages(any()) } returns listOf(
            AvailableCardUsageTicketResponse(
                basketItemId = testData.basketItemId,
                discountSubjectId = 100L,
                cardTemplateName = card1.title,
                cardCode = card1.code,
                discountedPrice = 5.0.toBigDecimal(),
                discountedBasePrice = 5.0.toBigDecimal(),
                originalPrice = 10.0.toBigDecimal(),
                originalBasePrice = 10.0.toBigDecimal(),
                discountValue = 5.0.toBigDecimal(),
                discountCurrency = CardsCurrency.EUR,
                discountSubject = CardsDiscountSubjectResponse(
                    id = 1L,
                    type = DiscountSubjectType.TICKET,
                    relativeDiscount = 0.5.toBigDecimal(),
                    canBeCombined = false,
                    includeSurcharges = false,
                    distributorFee = mapOf()
                )
            )
        ) andThen listOf(
            AvailableCardUsageTicketResponse(
                basketItemId = testData.basketItemId,
                discountSubjectId = 100L,
                cardTemplateName = card1.title,
                cardCode = card1.code,
                discountedPrice = 5.0.toBigDecimal(),
                discountedBasePrice = 5.0.toBigDecimal(),
                originalPrice = 10.0.toBigDecimal(),
                originalBasePrice = 10.0.toBigDecimal(),
                discountValue = 5.0.toBigDecimal(),
                discountCurrency = CardsCurrency.EUR,
                discountSubject = CardsDiscountSubjectResponse(
                    id = 1L,
                    type = DiscountSubjectType.TICKET,
                    relativeDiscount = 0.5.toBigDecimal(),
                    canBeCombined = false,
                    includeSurcharges = false,
                    distributorFee = mapOf()
                )
            ),
            AvailableCardUsageTicketResponse(
                basketItemId = basketItem2.id,
                discountSubjectId = 100L,
                cardTemplateName = card2.title,
                cardCode = card2.code,
                discountedPrice = 5.0.toBigDecimal(),
                discountedBasePrice = 5.0.toBigDecimal(),
                originalPrice = 10.0.toBigDecimal(),
                originalBasePrice = 10.0.toBigDecimal(),
                discountValue = 5.0.toBigDecimal(),
                discountCurrency = CardsCurrency.EUR,
                discountSubject = CardsDiscountSubjectResponse(
                    id = 1L,
                    type = DiscountSubjectType.TICKET,
                    relativeDiscount = 0.5.toBigDecimal(),
                    canBeCombined = false,
                    includeSurcharges = false,
                    distributorFee = mapOf()
                )
            )
        )

        every { applicationEventPublisherMock.publishEvent(any<CardUsedOnTicketBasketItemEvent>()) } just Runs

        // apply first card
        underTest.applyCardsOnBasket(
            command = ApplyCardsOnBasketCommand(
                basketId = testData.basketId,
                cardIds = setOf(card1.id),
                posType = CardsPosType.CINEMA
            )
        )

        val basket = basketJpaFinderService.getById(testData.basketId)
        with(basket) {
            totalPrice shouldBeEqualComparingTo 25.0.toBigDecimal()
        }

        val basketItems = basketItemJpaFinderService.findAllNonDeletedByBasketId(testData.basketId).sortedBy { it.createdAt }
        basketItems.size shouldBe 3

        with(basketItems[0]) {
            price shouldBeEqualComparingTo 5.0.toBigDecimal()
            originalPrice shouldBeEqualComparingTo 10.0.toBigDecimal()
            fixedPrice shouldBeEqualComparingTo 0.0.toBigDecimal()
        }
        with(basketItems[1]) {
            price shouldBeEqualComparingTo 10.0.toBigDecimal()
            originalPrice shouldBeEqualComparingTo 10.0.toBigDecimal()
            fixedPrice shouldBeEqualComparingTo 0.0.toBigDecimal()
        }
        with(basketItems[2]) {
            price shouldBeEqualComparingTo 10.0.toBigDecimal()
            originalPrice shouldBeEqualComparingTo 10.0.toBigDecimal()
            fixedPrice shouldBeEqualComparingTo 0.0.toBigDecimal()
        }

        val cardUsages = cardUsageFinderService.findAll().sortedBy { it.createdAt }
        cardUsages.size shouldBe 1

        with(cardUsages[0]) {
            cardId shouldBe card1.id
            basketId shouldBe testData.basketId
            basketItemId shouldBe testData.basketItemId
            originalId shouldBe null
            discountPercentage shouldBe 50
        }

        // apply second card
        underTest.applyCardsOnBasket(
            command = ApplyCardsOnBasketCommand(
                basketId = testData.basketId,
                cardIds = setOf(card1.id, card2.id),
                posType = CardsPosType.CINEMA
            )
        )

        val updatedBasket = basketJpaFinderService.getById(testData.basketId)
        with(updatedBasket) {
            totalPrice shouldBeEqualComparingTo 20.0.toBigDecimal()
        }

        val updatedBasketItems = basketItemJpaFinderService.findAllNonDeletedByBasketId(testData.basketId)
            .sortedBy { it.createdAt }
        updatedBasketItems.size shouldBe 3

        with(updatedBasketItems[0]) {
            price shouldBeEqualComparingTo 5.0.toBigDecimal()
            originalPrice shouldBeEqualComparingTo 10.0.toBigDecimal()
            fixedPrice shouldBeEqualComparingTo 0.0.toBigDecimal()
        }
        with(updatedBasketItems[1]) {
            price shouldBeEqualComparingTo 5.0.toBigDecimal()
            originalPrice shouldBeEqualComparingTo 10.0.toBigDecimal()
            fixedPrice shouldBeEqualComparingTo 0.0.toBigDecimal()
        }
        with(updatedBasketItems[2]) {
            price shouldBeEqualComparingTo 10.0.toBigDecimal()
            originalPrice shouldBeEqualComparingTo 10.0.toBigDecimal()
            fixedPrice shouldBeEqualComparingTo 0.0.toBigDecimal()
        }

        val cardUsages2 = cardUsageFinderService.findAll().sortedBy { it.createdAt }
        cardUsages2.size shouldBe 2

        with(cardUsages2[0]) {
            cardId shouldBe card1.id
            basketId shouldBe testData.basketId
            basketItemId shouldBe testData.basketItemId
            originalId shouldBe null
            discountPercentage shouldBe 50
        }
        with(cardUsages2[1]) {
            cardId shouldBe card2.id
            basketId shouldBe testData.basketId
            basketItemId shouldBe basketItem2.id
            originalId shouldBe null
            discountPercentage shouldBe 50
        }

        // 2. USE CARDS ON BASKET
        every { cinemaxCardsConnectorMock.useCards(any()) } returns listOf(
            CardUsageTicketResponse(
                id = 1L,
                cardCode = "CU5X2URJ",
                branch = CardsBranch.BA,
                country = CardsCountry.SK,
                discountAmount = 5.toBigDecimal(),
                discountCurrency = CardsCurrency.EUR,
                discountSubject = CardsDiscountSubjectResponse(
                    id = 1L,
                    type = DiscountSubjectType.TICKET,
                    relativeDiscount = 0.5.toBigDecimal()
                ),
                discountedPrice = 5.0.toBigDecimal(),
                discountedBasePrice = 5.0.toBigDecimal(),
                originalPrice = 10.0.toBigDecimal(),
                pos = CardsPosType.CINEMA,
                basketId = testData.basketId,
                basketItemId = testData.basketItemId,
                state = CardsUsageState.CREATED,
                timestamp = LocalDateTime.now(),
                canBeCombined = false,
                screeningDate = testData.screeningDate,
                screeningId = 10.toUUID(),
                movieCode = testData.movieCode,
                overridePosDistributorFee = emptyMap()
            ),
            CardUsageTicketResponse(
                id = 2L,
                cardCode = "C6JDY3GJ",
                branch = CardsBranch.BA,
                country = CardsCountry.SK,
                discountAmount = 5.toBigDecimal(),
                discountCurrency = CardsCurrency.EUR,
                discountSubject = CardsDiscountSubjectResponse(
                    id = 1L,
                    type = DiscountSubjectType.TICKET,
                    relativeDiscount = 0.5.toBigDecimal()
                ),
                discountedPrice = 5.0.toBigDecimal(),
                discountedBasePrice = 5.0.toBigDecimal(),
                originalPrice = 10.0.toBigDecimal(),
                pos = CardsPosType.CINEMA,
                basketId = testData.basketId,
                basketItemId = basketItem2.id,
                state = CardsUsageState.CREATED,
                timestamp = LocalDateTime.now(),
                canBeCombined = false,
                screeningDate = testData.screeningDate,
                screeningId = 10.toUUID(),
                movieCode = testData.movieCode,
                overridePosDistributorFee = emptyMap()
            )
        )

        basketService.addAppliedBasketCardId(
            AddAppliedBasketCardIdCommand(
                basketId = testData.basketId,
                cardId = card1.id
            )
        )
        basketService.addAppliedBasketCardId(
            AddAppliedBasketCardIdCommand(
                basketId = testData.basketId,
                cardId = card2.id
            )
        )

        underTest.useCardsOnBasket(
            UseCardsOnBasketCommand(
                basketId = testData.basketId,
                posType = CardsPosType.CINEMA
            )
        )

        val updatedCardUsages = cardUsageFinderService.findAll().sortedBy { it.createdAt }
        updatedCardUsages.size shouldBe 2
        updatedCardUsages[0].originalId shouldBe 1L
        updatedCardUsages[1].originalId shouldBe 2L

        verifySequence {
            cinemaxCardsConnectorMock.listCardsUsages(
                withArg<UseCardsRequest> { request ->
                    request.cardCodes shouldBe setOf("CU5X2URJ")

                    with(request.payload) {
                        basketId shouldBe testData.basketId
                        type shouldBe CardsBasketType.BASKET
                        pos shouldBe CardsPosType.CINEMA
                        branch shouldBe CardsBranch.BA
                        currency shouldBe CardsCurrency.EUR
                        items.size shouldBe 3

                        with(items[0] as UseCardsTicketBasketItem) {
                            basketItemId shouldBe testData.basketItemId
                            type shouldBe CardsBasketItemType.TICKET
                            ticketBasePrice shouldBeEqualComparingTo 10.toBigDecimal()
                            ticketTotalPrice shouldBeEqualComparingTo 10.toBigDecimal()
                            screeningId shouldBe testData.screeningId
                            movieCode shouldBe testData.movieCode
                            screeningDate shouldBe testData.screeningDate
                        }

                        with(items[1] as UseCardsTicketBasketItem) {
                            basketItemId shouldBe basketItem2.id
                            type shouldBe CardsBasketItemType.TICKET
                            ticketBasePrice shouldBeEqualComparingTo 10.toBigDecimal()
                            ticketTotalPrice shouldBeEqualComparingTo 10.toBigDecimal()
                            screeningId shouldBe testData.screeningId
                            movieCode shouldBe testData.movieCode
                            screeningDate shouldBe testData.screeningDate
                        }

                        with(items[2] as UseCardsTicketBasketItem) {
                            basketItemId shouldBe basketItem3.id
                            type shouldBe CardsBasketItemType.TICKET
                            ticketBasePrice shouldBeEqualComparingTo 10.toBigDecimal()
                            ticketTotalPrice shouldBeEqualComparingTo 10.toBigDecimal()
                            screeningId shouldBe testData.screeningId
                            movieCode shouldBe testData.movieCode
                            screeningDate shouldBe testData.screeningDate
                        }
                    }
                }
            )

            applicationEventPublisherMock.publishEvent(
                withArg<CardUsedOnTicketBasketItemEvent> { event ->
                    event.ticketId shouldBe testData.ticketId
                    event.price shouldBeEqualComparingTo 5.0.toBigDecimal()
                    event.basePrice shouldBeEqualComparingTo 5.0.toBigDecimal()
                    event.zeroFees shouldBe false
                }
            )

            cinemaxCardsConnectorMock.listCardsUsages(
                withArg<UseCardsRequest> { request ->
                    request.cardCodes shouldBe setOf("CU5X2URJ", "C6JDY3GJ")

                    with(request.payload) {
                        basketId shouldBe testData.basketId
                        type shouldBe CardsBasketType.BASKET
                        pos shouldBe CardsPosType.CINEMA
                        branch shouldBe CardsBranch.BA
                        currency shouldBe CardsCurrency.EUR
                        items.size shouldBe 3

                        with(items[0] as UseCardsTicketBasketItem) {
                            basketItemId shouldBe testData.basketItemId
                            type shouldBe CardsBasketItemType.TICKET
                            ticketBasePrice shouldBeEqualComparingTo 10.toBigDecimal()
                            ticketTotalPrice shouldBeEqualComparingTo 10.toBigDecimal()
                            screeningId shouldBe testData.screeningId
                            movieCode shouldBe testData.movieCode
                            screeningDate shouldBe testData.screeningDate
                        }

                        with(items[1] as UseCardsTicketBasketItem) {
                            basketItemId shouldBe basketItem2.id
                            type shouldBe CardsBasketItemType.TICKET
                            ticketBasePrice shouldBeEqualComparingTo 10.toBigDecimal()
                            ticketTotalPrice shouldBeEqualComparingTo 10.toBigDecimal()
                            screeningId shouldBe testData.screeningId
                            movieCode shouldBe testData.movieCode
                            screeningDate shouldBe testData.screeningDate
                        }

                        with(items[2] as UseCardsTicketBasketItem) {
                            basketItemId shouldBe basketItem3.id
                            type shouldBe CardsBasketItemType.TICKET
                            ticketBasePrice shouldBeEqualComparingTo 10.toBigDecimal()
                            ticketTotalPrice shouldBeEqualComparingTo 10.toBigDecimal()
                            screeningId shouldBe testData.screeningId
                            movieCode shouldBe testData.movieCode
                            screeningDate shouldBe testData.screeningDate
                        }
                    }
                }
            )

            applicationEventPublisherMock.publishEvent(
                withArg<CardUsedOnTicketBasketItemEvent> { event ->
                    event.ticketId shouldBe testData.ticketId
                    event.price shouldBeEqualComparingTo 5.0.toBigDecimal()
                    event.basePrice shouldBeEqualComparingTo 5.0.toBigDecimal()
                    event.zeroFees shouldBe false
                }
            )

            applicationEventPublisherMock.publishEvent(
                withArg<CardUsedOnTicketBasketItemEvent> { event ->
                    event.ticketId shouldBe ticket2.id
                    event.price shouldBeEqualComparingTo 5.0.toBigDecimal()
                    event.basePrice shouldBeEqualComparingTo 5.0.toBigDecimal()
                    event.zeroFees shouldBe false
                }
            )

            cinemaxCardsConnectorMock.useCards(
                withArg<UseCardsRequest> { request ->
                    request.cardCodes shouldBe setOf("CU5X2URJ", "C6JDY3GJ")

                    with(request.payload) {
                        basketId shouldBe testData.basketId
                        type shouldBe CardsBasketType.BASKET
                        pos shouldBe CardsPosType.CINEMA
                        branch shouldBe CardsBranch.BA
                        currency shouldBe CardsCurrency.EUR
                        items.size shouldBe 2

                        with(items[0] as UseCardsTicketBasketItem) {
                            basketItemId shouldBe testData.basketItemId
                            type shouldBe CardsBasketItemType.TICKET
                            ticketBasePrice shouldBeEqualComparingTo 10.toBigDecimal()
                            ticketTotalPrice shouldBeEqualComparingTo 10.toBigDecimal()
                            screeningId shouldBe testData.screeningId
                            movieCode shouldBe testData.movieCode
                            screeningDate shouldBe testData.screeningDate
                        }

                        with(items[1] as UseCardsTicketBasketItem) {
                            basketItemId shouldBe basketItem2.id
                            type shouldBe CardsBasketItemType.TICKET
                            ticketBasePrice shouldBeEqualComparingTo 10.toBigDecimal()
                            ticketTotalPrice shouldBeEqualComparingTo 10.toBigDecimal()
                            screeningId shouldBe testData.screeningId
                            movieCode shouldBe testData.movieCode
                            screeningDate shouldBe testData.screeningDate
                        }
                    }
                }
            )
        }
    }

    @Test
    fun `test removeCardFromBasketCommand - basket with applied card - should successfully remove card and restore prices`() {
        val testData = prepareSingleTicketTestData()
        val card = integrationDataTestHelper.getCard()

        every { cinemaxCardsConnectorMock.listCardsUsages(any()) } returns listOf(
            AvailableCardUsageTicketResponse(
                basketItemId = testData.basketItemId,
                discountSubjectId = 100L,
                cardTemplateName = card.title,
                cardCode = card.code,
                discountedPrice = 8.0.toBigDecimal(),
                discountedBasePrice = 8.0.toBigDecimal(),
                originalPrice = 10.0.toBigDecimal(),
                originalBasePrice = 10.0.toBigDecimal(),
                discountValue = 2.toBigDecimal(),
                discountCurrency = CardsCurrency.EUR,
                discountSubject = CardsDiscountSubjectResponse(
                    id = 1L,
                    type = DiscountSubjectType.TICKET,
                    relativeDiscount = 0.2.toBigDecimal(),
                    canBeCombined = false,
                    includeSurcharges = false,
                    distributorFee = mapOf()
                )
            )
        )

        every { applicationEventPublisherMock.publishEvent(any<CardUsedOnTicketBasketItemEvent>()) } just Runs

        basketService.addAppliedBasketCardId(
            AddAppliedBasketCardIdCommand(
                basketId = testData.basketId,
                cardId = card.id
            )
        )

        underTest.applyCardsOnBasket(
            command = ApplyCardsOnBasketCommand(
                basketId = testData.basketId,
                cardIds = setOf(card.id),
                posType = CardsPosType.CINEMA
            )
        )

        val basket = basketJpaFinderService.getById(testData.basketId)
        basket.totalPrice shouldBeEqualComparingTo 8.0.toBigDecimal()

        val basketItem = basketItemJpaFinderService.getNonDeletedById(testData.basketItemId)
        basketItem.price shouldBeEqualComparingTo 8.0.toBigDecimal()
        basketItem.originalPrice shouldBeEqualComparingTo 10.0.toBigDecimal()
        basketItem.vatAmount shouldBeEqualComparingTo 1.5.toBigDecimal()

        val cardUsages = cardUsageFinderService.findAll()
        cardUsages.size shouldBe 1

        underTest.removeCardFromBasketCommand(
            command = RemoveCardFromBasketCommand(
                basketId = testData.basketId,
                cardId = card.id
            )
        )

        val updatedBasket = basketJpaFinderService.getById(testData.basketId)
        updatedBasket.totalPrice shouldBeEqualComparingTo 10.0.toBigDecimal()

        val updatedBasketItem = basketItemJpaFinderService.getNonDeletedById(testData.basketItemId)
        updatedBasketItem.price shouldBeEqualComparingTo 10.0.toBigDecimal()
        updatedBasketItem.originalPrice shouldBeEqualComparingTo 10.0.toBigDecimal()
        updatedBasketItem.vatAmount shouldBeEqualComparingTo 1.87.toBigDecimal()

        val updateCardUsages = cardUsageFinderService.findAll()
        updateCardUsages.size shouldBe 0
    }

    private fun prepareSingleTicketTestData(): TicketTestData {
        val auditoriumLayout = integrationDataTestHelper.getAuditoriumLayout(
            auditoriumId = integrationDataTestHelper.getAuditorium()
        )

        val seat = integrationDataTestHelper.getSeat(
            originalId = 1,
            row = "1",
            number = "1",
            auditoriumId = auditoriumLayout.auditoriumId,
            auditoriumLayoutId = auditoriumLayout.id,
            type = SeatType.REGULAR
        )

        val priceCategory = integrationDataTestHelper.getPriceCategory()
        integrationDataTestHelper.getPriceCategoryItem(priceCategory.id)

        val distributor = integrationDataTestHelper.getDistributor()
        val movie = integrationDataTestHelper.getMovie(
            distributorId = distributor.id
        )
        val screening = integrationDataTestHelper.getScreening(
            auditoriumId = auditoriumLayout.auditoriumId,
            auditoriumLayoutId = auditoriumLayout.id,
            date = INTEGRATION_TEST_DATE_TIME.toLocalDate(),
            priceCategoryId = priceCategory.id,
            movieId = movie.id
        )
        val screeningType1 = integrationDataTestHelper.getScreeningType(
            originalId = 1,
            title = "Babska Jazda",
            code = "03"
        )
        val screeningType2 = integrationDataTestHelper.getScreeningType(
            originalId = 2,
            title = "Artmax",
            code = "01"
        )
        val screeningTypes1 = integrationDataTestHelper.getScreeningTypes(
            screeningId = screening.id,
            screeningTypeId = screeningType1.id
        )
        val screeningTypes2 = integrationDataTestHelper.getScreeningTypes(
            screeningId = screening.id,
            screeningTypeId = screeningType2.id
        )

        val reservation = integrationDataTestHelper.getReservation(
            seatId = seat.id,
            screeningId = screening.id
        )

        val ticketPrice = integrationDataTestHelper.getTicketPrice(
            seatId = seat.id,
            screeningId = screening.id,
            basePrice = 10.toBigDecimal(),
            totalPrice = 10.toBigDecimal()
        )

        val ticket = integrationDataTestHelper.getTicket(
            screeningId = screening.id,
            reservationId = reservation.id,
            ticketPriceId = ticketPrice.id
        )

        val basket = integrationDataTestHelper.getBasket(
            totalPrice = 10.toBigDecimal(),
            state = BasketState.OPEN
        )

        val basketItem = integrationDataTestHelper.getBasketItem(
            basketId = basket.id,
            ticketId = ticket.id,
            type = BasketItemType.TICKET,
            price = 10.toBigDecimal()
        )

        return TicketTestData(
            basketId = basket.id,
            basketItemId = basketItem.id,
            screeningId = screening.id,
            screeningDate = screening.date,
            movieCode = movie.messagingCode,
            ticketId = ticket.id,
            ticketPriceId = ticketPrice.id,
            auditoriumId = auditoriumLayout.auditoriumId,
            auditoriumLayoutId = auditoriumLayout.id
        )
    }

    private fun prepareSingleProductTestData(): ProductTestData {
        val productComponentCategory = integrationDataTestHelper.getProductComponentCategory(
            code = "01",
            title = "Nealko Bufet"
        )
        val productComponent = integrationDataTestHelper.getProductComponent(
            code = "01",
            title = "Coca Cola 0,33l PET",
            unit = ProductComponentUnit.KS,
            stockQuantity = 100.toBigDecimal(),
            productComponentCategoryId = productComponentCategory.id
        )
        val productCategory = integrationDataTestHelper.getProductCategory(
            code = "01",
            title = "Nealko Bufet",
            taxRate = REDUCED_TAX_RATE
        )
        val product = integrationDataTestHelper.getProduct(
            title = "Coca Cola 0,33l",
            price = 2.0.toBigDecimal(),
            productCategoryId = productCategory.id
        )
        integrationDataTestHelper.getProductComposition(
            productId = product.id,
            productComponentId = productComponent.id,
            amount = 1.toBigDecimal()
        )

        val basket = integrationDataTestHelper.getBasket(
            totalPrice = 2.0.toBigDecimal(),
            state = BasketState.OPEN
        )

        val basketItem = integrationDataTestHelper.getBasketItem(
            basketId = basket.id,
            productId = product.id,
            type = BasketItemType.PRODUCT,
            price = 2.0.toBigDecimal(),
            vatAmount = 0.32.toBigDecimal()
        )

        return ProductTestData(
            basketId = basket.id,
            basketItemId = basketItem.id,
            productId = product.id,
            productCode = product.code
        )
    }
}

data class TicketTestData(
    val basketId: UUID,
    val basketItemId: UUID,
    val screeningId: UUID,
    val screeningDate: LocalDate,
    val movieCode: String,
    val ticketId: UUID,
    val ticketPriceId: UUID,
    val auditoriumId: UUID,
    val auditoriumLayoutId: UUID,
)

data class ProductTestData(
    val basketId: UUID,
    val basketItemId: UUID,
    val productId: UUID,
    val productCode: String,
)
