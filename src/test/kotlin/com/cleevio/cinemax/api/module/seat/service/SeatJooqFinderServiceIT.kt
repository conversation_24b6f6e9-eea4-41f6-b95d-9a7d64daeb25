package com.cleevio.cinemax.api.module.seat.service

import com.cleevio.cinemax.api.DatabaseCleanup
import com.cleevio.cinemax.api.IntegrationTest
import com.cleevio.cinemax.api.module.auditorium.service.AuditoriumService
import com.cleevio.cinemax.api.module.auditoriumlayout.service.AuditoriumLayoutRepository
import com.cleevio.cinemax.api.module.seat.constant.SeatObjectType
import com.cleevio.cinemax.api.module.seat.entity.Seat
import com.cleevio.cinemax.api.util.createAuditorium
import com.cleevio.cinemax.api.util.createAuditoriumLayout
import com.cleevio.cinemax.api.util.createSeat
import com.cleevio.cinemax.api.util.mapToCreateOrUpdateAuditoriumCommand
import com.cleevio.cinemax.api.util.mapToCreateOrUpdateSeatCommand
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.Arguments
import org.junit.jupiter.params.provider.MethodSource
import org.springframework.beans.factory.annotation.Autowired
import java.util.UUID
import java.util.stream.Stream
import kotlin.test.assertEquals

class SeatJooqFinderServiceIT @Autowired constructor(
    private val underTest: SeatJooqFinderService,
    private val auditoriumService: AuditoriumService,
    private val seatService: SeatService,
    private val auditoriumLayoutRepository: AuditoriumLayoutRepository,
) : IntegrationTest(DatabaseCleanup.AFTER_EACH) {

    @BeforeEach
    fun setUp() {
        setOf(AUDITORIUM_1, AUDITORIUM_2).forEach {
            auditoriumService.syncCreateOrUpdateAuditorium(mapToCreateOrUpdateAuditoriumCommand(it))
        }
        auditoriumLayoutRepository.saveAll(
            listOf(
                AUDITORIUM_LAYOUT_1,
                AUDITORIUM_LAYOUT_2,
                AUDITORIUM_LAYOUT_3,
                AUDITORIUM_LAYOUT_3,
                AUDITORIUM_LAYOUT_4,
                AUDITORIUM_LAYOUT_5,
                AUDITORIUM_LAYOUT_6
            )
        )
        setOf(SEAT_1, SEAT_2, SEAT_3, SEAT_4, SEAT_5, SEAT_6, SEAT_7, SEAT_8, SEAT_9, SEAT_10, SEAT_11).forEach {
            seatService.createOrUpdateSeat(mapToCreateOrUpdateSeatCommand(it))
        }
    }

    @ParameterizedTest
    @MethodSource("auditoriumIdAndAuditoriumLayoutIdInProvider")
    fun `test findAllSeatsByAuditoriumIdAndAuditoriumLayoutIdIn - should find correct seats`(
        auditoriumIdToAuditoriumLayoutIdPairs: Set<Pair<UUID, UUID>>,
        expectedSeats: List<Seat>,
    ) {
        assertEquals(
            expectedSeats,
            underTest.findAllSeatsByAuditoriumIdAndAuditoriumLayoutIdIn(auditoriumIdToAuditoriumLayoutIdPairs)
        )
    }

    companion object {
        @JvmStatic
        fun auditoriumIdAndAuditoriumLayoutIdInProvider(): Stream<Arguments> {
            return Stream.of(
                Arguments.of(setOf(Pair(AUDITORIUM_1.id, AUDITORIUM_LAYOUT_1.id)), listOf(SEAT_1, SEAT_2)),
                Arguments.of(setOf(Pair(AUDITORIUM_1.id, AUDITORIUM_LAYOUT_2.id)), listOf(SEAT_3)),
                Arguments.of(setOf(Pair(AUDITORIUM_1.id, AUDITORIUM_LAYOUT_3.id)), listOf(SEAT_4)),
                Arguments.of(setOf(Pair(AUDITORIUM_1.id, AUDITORIUM_LAYOUT_4.id)), listOf<Seat>()),
                Arguments.of(
                    setOf(Pair(AUDITORIUM_1.id, AUDITORIUM_LAYOUT_1.id), Pair(AUDITORIUM_1.id, AUDITORIUM_LAYOUT_2.id)),
                    listOf(SEAT_1, SEAT_2, SEAT_3)
                ),
                Arguments.of(setOf(Pair(AUDITORIUM_2.id, AUDITORIUM_LAYOUT_4.id)), listOf(SEAT_5, SEAT_6, SEAT_7)),
                Arguments.of(setOf(Pair(AUDITORIUM_2.id, AUDITORIUM_LAYOUT_5.id)), listOf(SEAT_8)),
                Arguments.of(
                    setOf(Pair(AUDITORIUM_2.id, AUDITORIUM_LAYOUT_4.id), Pair(AUDITORIUM_2.id, AUDITORIUM_LAYOUT_5.id)),
                    listOf(SEAT_5, SEAT_6, SEAT_7, SEAT_8)
                ),
                Arguments.of(
                    setOf(Pair(AUDITORIUM_1.id, AUDITORIUM_LAYOUT_1.id), Pair(AUDITORIUM_2.id, AUDITORIUM_LAYOUT_4.id)),
                    listOf(SEAT_1, SEAT_2, SEAT_5, SEAT_6, SEAT_7)
                )
            )
        }
    }
}

private val AUDITORIUM_1 = createAuditorium(
    originalId = 1,
    code = "A",
    title = "Sála A"
)
private val AUDITORIUM_2 = createAuditorium(
    originalId = 2,
    code = "B",
    title = "Sála B"
)
private val AUDITORIUM_LAYOUT_1 = createAuditoriumLayout(originalId = 1, auditoriumId = AUDITORIUM_1.id, code = "01")
private val AUDITORIUM_LAYOUT_2 = createAuditoriumLayout(originalId = 2, auditoriumId = AUDITORIUM_1.id, code = "02")
private val AUDITORIUM_LAYOUT_3 = createAuditoriumLayout(originalId = 3, auditoriumId = AUDITORIUM_1.id, code = "03")
private val AUDITORIUM_LAYOUT_4 = createAuditoriumLayout(originalId = 4, auditoriumId = AUDITORIUM_2.id, code = "04")
private val AUDITORIUM_LAYOUT_5 = createAuditoriumLayout(originalId = 5, auditoriumId = AUDITORIUM_2.id, code = "05")
private val AUDITORIUM_LAYOUT_6 = createAuditoriumLayout(originalId = 100, auditoriumId = AUDITORIUM_2.id, code = "100")

private val SEAT_1 = createSeat(
    originalId = 1,
    auditoriumLayoutId = AUDITORIUM_LAYOUT_1.id,
    auditoriumId = AUDITORIUM_1.id
)
private val SEAT_2 = createSeat(
    originalId = 2,
    auditoriumLayoutId = AUDITORIUM_LAYOUT_1.id,
    auditoriumId = AUDITORIUM_1.id
)
private val SEAT_3 = createSeat(
    originalId = 3,
    auditoriumLayoutId = AUDITORIUM_LAYOUT_2.id,
    auditoriumId = AUDITORIUM_1.id
)
private val SEAT_4 = createSeat(
    originalId = 4,
    auditoriumLayoutId = AUDITORIUM_LAYOUT_3.id,
    auditoriumId = AUDITORIUM_1.id
)
private val SEAT_5 = createSeat(
    originalId = 5,
    auditoriumLayoutId = AUDITORIUM_LAYOUT_4.id,
    auditoriumId = AUDITORIUM_2.id
)
private val SEAT_6 = createSeat(
    originalId = 6,
    auditoriumLayoutId = AUDITORIUM_LAYOUT_4.id,
    auditoriumId = AUDITORIUM_2.id
)
private val SEAT_7 = createSeat(
    originalId = 7,
    auditoriumLayoutId = AUDITORIUM_LAYOUT_4.id,
    auditoriumId = AUDITORIUM_2.id
)
private val SEAT_8 = createSeat(
    originalId = 8,
    auditoriumLayoutId = AUDITORIUM_LAYOUT_5.id,
    auditoriumId = AUDITORIUM_2.id
)
private val SEAT_9 = createSeat(
    originalId = 9,
    auditoriumLayoutId = AUDITORIUM_LAYOUT_6.id,
    auditoriumId = AUDITORIUM_2.id
)

// row - shouldn't appear anywhere
private val SEAT_10 = createSeat(
    originalId = 10,
    auditoriumLayoutId = AUDITORIUM_LAYOUT_1.id,
    auditoriumId = AUDITORIUM_1.id,
    objectType = SeatObjectType.ROW
)

// image - shouldn't appear anywhere
private val SEAT_11 = createSeat(
    originalId = 11,
    auditoriumLayoutId = AUDITORIUM_LAYOUT_1.id,
    auditoriumId = AUDITORIUM_1.id,
    objectType = SeatObjectType.IMAGE
)
