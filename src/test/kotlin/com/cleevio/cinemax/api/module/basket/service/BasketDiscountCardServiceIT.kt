package com.cleevio.cinemax.api.module.basket.service

import com.cleevio.cinemax.api.DatabaseCleanup
import com.cleevio.cinemax.api.IntegrationTest
import com.cleevio.cinemax.api.common.util.isLessThan
import com.cleevio.cinemax.api.module.auditorium.service.AuditoriumService
import com.cleevio.cinemax.api.module.auditoriumlayout.service.AuditoriumLayoutRepository
import com.cleevio.cinemax.api.module.basket.entity.Basket
import com.cleevio.cinemax.api.module.basket.service.command.DeleteDiscountCardDiscountsAndUsageCommand
import com.cleevio.cinemax.api.module.basket.service.command.DeleteDiscountCardUsagesWhenDeletingBasketItemCommand
import com.cleevio.cinemax.api.module.basket.service.command.DeleteDiscountCardUsagesWhenPatchingBasketItemCommand
import com.cleevio.cinemax.api.module.basketitem.constant.BasketItemType
import com.cleevio.cinemax.api.module.basketitem.controller.dto.CreateBasketItemRequest
import com.cleevio.cinemax.api.module.basketitem.controller.dto.CreateProductRequest
import com.cleevio.cinemax.api.module.basketitem.controller.dto.CreateReservationRequest
import com.cleevio.cinemax.api.module.basketitem.controller.dto.CreateTicketPriceRequest
import com.cleevio.cinemax.api.module.basketitem.controller.dto.CreateTicketRequest
import com.cleevio.cinemax.api.module.basketitem.entity.BasketItem
import com.cleevio.cinemax.api.module.basketitem.event.TicketBasketItemDiscountRemovalInitiatedEvent
import com.cleevio.cinemax.api.module.basketitem.service.BasketItemJpaFinderService
import com.cleevio.cinemax.api.module.basketitem.service.BasketItemService
import com.cleevio.cinemax.api.module.basketitem.service.command.PatchBasketItemCommand
import com.cleevio.cinemax.api.module.branch.service.BranchRepository
import com.cleevio.cinemax.api.module.discountcard.service.DiscountCardMssqlService
import com.cleevio.cinemax.api.module.discountcardusage.entity.DiscountCardUsage
import com.cleevio.cinemax.api.module.discountcardusage.service.DiscountCardUsageFinderService
import com.cleevio.cinemax.api.module.discountcardusage.service.DiscountCardUsageService
import com.cleevio.cinemax.api.module.discountcardusage.service.command.CreateDiscountCardUsageCommand
import com.cleevio.cinemax.api.module.discountcardusage.service.command.UpdateDiscountCardUsageCommand
import com.cleevio.cinemax.api.module.distributor.service.DistributorService
import com.cleevio.cinemax.api.module.movie.service.MovieService
import com.cleevio.cinemax.api.module.posconfiguration.constant.ProductMode
import com.cleevio.cinemax.api.module.posconfiguration.constant.TablesType
import com.cleevio.cinemax.api.module.posconfiguration.service.PosConfigurationService
import com.cleevio.cinemax.api.module.posconfiguration.service.command.CreateOrUpdatePosConfigurationCommand
import com.cleevio.cinemax.api.module.pricecategory.service.PriceCategoryService
import com.cleevio.cinemax.api.module.pricecategoryitem.constant.PriceCategoryItemNumber
import com.cleevio.cinemax.api.module.pricecategoryitem.service.PriceCategoryItemService
import com.cleevio.cinemax.api.module.product.constant.ProductType
import com.cleevio.cinemax.api.module.product.service.ProductService
import com.cleevio.cinemax.api.module.productcategory.constant.ProductCategoryType
import com.cleevio.cinemax.api.module.productcategory.service.ProductCategoryService
import com.cleevio.cinemax.api.module.productcomponent.constant.ProductComponentUnit
import com.cleevio.cinemax.api.module.productcomponent.service.ProductComponentService
import com.cleevio.cinemax.api.module.productcomponentcategory.service.ProductComponentCategoryRepository
import com.cleevio.cinemax.api.module.productcomposition.service.ProductCompositionService
import com.cleevio.cinemax.api.module.reservation.event.ReservationCreatedEvent
import com.cleevio.cinemax.api.module.screening.service.ScreeningService
import com.cleevio.cinemax.api.module.screeningfee.service.ScreeningFeeService
import com.cleevio.cinemax.api.module.seat.service.SeatService
import com.cleevio.cinemax.api.module.ticketdiscount.constant.TicketDiscountType
import com.cleevio.cinemax.api.module.ticketdiscount.constant.TicketDiscountUsageType
import com.cleevio.cinemax.api.module.ticketdiscount.service.TicketDiscountService
import com.cleevio.cinemax.api.util.createAuditorium
import com.cleevio.cinemax.api.util.createAuditoriumLayout
import com.cleevio.cinemax.api.util.createBranch
import com.cleevio.cinemax.api.util.createDiscountCard
import com.cleevio.cinemax.api.util.createDistributor
import com.cleevio.cinemax.api.util.createMovie
import com.cleevio.cinemax.api.util.createPriceCategory
import com.cleevio.cinemax.api.util.createPriceCategoryItem
import com.cleevio.cinemax.api.util.createProduct
import com.cleevio.cinemax.api.util.createProductCategory
import com.cleevio.cinemax.api.util.createProductComponent
import com.cleevio.cinemax.api.util.createProductComponentCategory
import com.cleevio.cinemax.api.util.createProductComposition
import com.cleevio.cinemax.api.util.createScreeningFee
import com.cleevio.cinemax.api.util.createScreeningWithCurrentDateTime
import com.cleevio.cinemax.api.util.createSeat
import com.cleevio.cinemax.api.util.createTicketDiscount
import com.cleevio.cinemax.api.util.mapToCreateBasketItemCommand
import com.cleevio.cinemax.api.util.mapToCreateOrUpdateAuditoriumCommand
import com.cleevio.cinemax.api.util.mapToCreateOrUpdateDiscountCardCommand
import com.cleevio.cinemax.api.util.mapToCreateOrUpdateDistributorCommand
import com.cleevio.cinemax.api.util.mapToCreateOrUpdateMovieCommand
import com.cleevio.cinemax.api.util.mapToCreateOrUpdatePriceCategoryItemCommand
import com.cleevio.cinemax.api.util.mapToCreateOrUpdateProductCategoryCommand
import com.cleevio.cinemax.api.util.mapToCreateOrUpdateProductComponentCommand
import com.cleevio.cinemax.api.util.mapToCreateOrUpdateProductCompositionCommand
import com.cleevio.cinemax.api.util.mapToCreateOrUpdateScreeningCommand
import com.cleevio.cinemax.api.util.mapToCreateOrUpdateScreeningFeeCommand
import com.cleevio.cinemax.api.util.mapToCreateOrUpdateSeatCommand
import com.cleevio.cinemax.api.util.mapToCreateOrUpdateTicketDiscountCommand
import com.cleevio.cinemax.api.util.mapToInitBasketCommand
import com.cleevio.cinemax.api.util.mapToSyncCreateOrUpdatePriceCategoryCommand
import com.cleevio.cinemax.api.util.mapToSyncCreateOrUpdateProductCommand
import io.mockk.Runs
import io.mockk.every
import io.mockk.just
import io.mockk.verify
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import java.math.BigDecimal
import java.util.Optional
import java.util.UUID
import kotlin.test.assertEquals
import kotlin.test.assertNotNull
import kotlin.test.assertNull
import kotlin.test.assertTrue

class BasketDiscountCardServiceIT @Autowired constructor(
    private val underTest: BasketDiscountCardService,
    private val basketService: BasketService,
    private val basketItemService: BasketItemService,
    private val basketItemJpaFinderService: BasketItemJpaFinderService,
    private val auditoriumService: AuditoriumService,
    private val movieService: MovieService,
    private val screeningService: ScreeningService,
    private val seatService: SeatService,
    private val screeningFeeService: ScreeningFeeService,
    private val priceCategoryService: PriceCategoryService,
    private val priceCategoryItemService: PriceCategoryItemService,
    private val productService: ProductService,
    private val productCategoryService: ProductCategoryService,
    private val productComponentService: ProductComponentService,
    private val productCompositionService: ProductCompositionService,
    private val posConfigurationService: PosConfigurationService,
    private val ticketDiscountService: TicketDiscountService,
    private val discountCardMssqlService: DiscountCardMssqlService,
    private val discountCardUsageService: DiscountCardUsageService,
    private val discountCardUsageFinderService: DiscountCardUsageFinderService,
    private val distributorService: DistributorService,
    private val auditoriumLayoutRepository: AuditoriumLayoutRepository,
    private val productComponentCategoryRepository: ProductComponentCategoryRepository,
    private val branchRepository: BranchRepository,
) : IntegrationTest(DatabaseCleanup.AFTER_EACH) {

    @BeforeEach
    fun setUp() {
        branchRepository.save(createBranch())
        auditoriumService.syncCreateOrUpdateAuditorium(mapToCreateOrUpdateAuditoriumCommand(AUDITORIUM_1))
        distributorService.syncCreateOrUpdateDistributor(mapToCreateOrUpdateDistributorCommand(DISTRIBUTOR_1))
        movieService.syncCreateOrUpdateMovie(mapToCreateOrUpdateMovieCommand(MOVIE_1))
        auditoriumLayoutRepository.save(AUDITORIUM_LAYOUT_1)
        priceCategoryService.syncCreateOrUpdatePriceCategory(mapToSyncCreateOrUpdatePriceCategoryCommand(PRICE_CATEGORY_1))
        screeningService.syncCreateOrUpdateScreening(mapToCreateOrUpdateScreeningCommand(SCREENING_1))
        screeningFeeService.createOrUpdateScreeningFee(mapToCreateOrUpdateScreeningFeeCommand(SCREENING_FEE_1))
        setOf(SEAT_1, SEAT_2).forEach {
            seatService.createOrUpdateSeat(mapToCreateOrUpdateSeatCommand(it))
        }
        priceCategoryItemService.createOrUpdatePriceCategoryItem(
            mapToCreateOrUpdatePriceCategoryItemCommand(PRICE_CATEGORY_ITEM_1, PRICE_CATEGORY_1.id)
        )
        setOf(PRODUCT_CATEGORY_1, PRODUCT_CATEGORY_2).forEach {
            productCategoryService.syncCreateOrUpdateProductCategory(mapToCreateOrUpdateProductCategoryCommand(it))
        }
        productService.syncCreateOrUpdateProduct(mapToSyncCreateOrUpdateProductCommand(PRODUCT_1, PRODUCT_CATEGORY_1.id))
        productService.syncCreateOrUpdateProduct(mapToSyncCreateOrUpdateProductCommand(PRODUCT_DISCOUNT_1, PRODUCT_CATEGORY_2.id))
        productComponentCategoryRepository.save(PRODUCT_COMPONENT_CATEGORY_1)
        productComponentService.syncCreateOrUpdateProductComponent(
            mapToCreateOrUpdateProductComponentCommand(
                PRODUCT_COMPONENT_1
            )
        )
        productCompositionService.createOrUpdateProductComposition(
            mapToCreateOrUpdateProductCompositionCommand(PRODUCT_COMPOSITION_1)
        )
        posConfigurationService.createOrUpdatePosConfiguration(POS_CONFIGURATION_COMMAND)
        setOf(TICKET_DISCOUNT_1, TICKET_DISCOUNT_2).forEach {
            ticketDiscountService.syncCreateOrUpdateTicketDiscount(mapToCreateOrUpdateTicketDiscountCommand(it))
        }
        setOf(DISCOUNT_CARD_1, DISCOUNT_CARD_2_ISOLATED_GROUP).forEach {
            discountCardMssqlService.createOrUpdateDiscountCard(mapToCreateOrUpdateDiscountCardCommand(it))
        }

        every { reservationMssqlFinderServiceMock.findByOriginalScreeningIdAndOriginalSeatId(any(), any()) } returns null
        every { applicationEventPublisherMock.publishEvent(any<ReservationCreatedEvent>()) } just Runs
    }

    @Test
    fun `test determineAppliedDiscountCards - discount card applied on ticket - should clear applications of the card`() {
        val (
            basket,
            ticketBasketItem,
            productBasketItem2,
            updatedDiscountCard1Usage,
            updatedDiscountCard2Usage,
        ) = initDetermineDiscountCardsTestData()

        underTest.deleteDiscountCardUsagesWhenDeletingBasketItem(
            DeleteDiscountCardUsagesWhenDeletingBasketItemCommand(
                basketId = basket.id,
                basketItemId = ticketBasketItem.id,
                basketItemType = ticketBasketItem.type
            )
        )
        verify(exactly = 0) { applicationEventPublisherMock.publishEvent(any()) }

        val basketItems = basketItemJpaFinderService.findAllByBasketId(basket.id).sortedBy { it.createdAt }
        assertNull(basketItems[0].deletedAt)
        assertNotNull(basketItems[1].deletedAt)
        assertNull(basketItems[2].deletedAt)
        assertNull(basketItems[3].deletedAt)

        val deletedDiscountCard1Usage = discountCardUsageFinderService.findById(updatedDiscountCard1Usage.id)
        val updatedDiscountCard2Usage2 = discountCardUsageFinderService.findById(updatedDiscountCard2Usage.id)
        assertNotNull(deletedDiscountCard1Usage!!.deletedAt)
        assertNull(updatedDiscountCard2Usage2!!.deletedAt)
    }

    @Test
    fun `test determineAppliedDiscountCards - product from discount card - should clear applications of this discount card`() {
        val (
            basket,
            ticketBasketItem,
            productBasketItem2,
            updatedDiscountCard1Usage,
            updatedDiscountCard2Usage,
        ) = initDetermineDiscountCardsTestData()

        underTest.deleteDiscountCardUsagesWhenDeletingBasketItem(
            DeleteDiscountCardUsagesWhenDeletingBasketItemCommand(
                basketId = basket.id,
                basketItemId = productBasketItem2!!.id,
                basketItemType = productBasketItem2.type
            )
        )

        verify(exactly = 0) { applicationEventPublisherMock.publishEvent(any()) }

        val basketItems = basketItemJpaFinderService.findAllByBasketId(basket.id).sortedBy { it.createdAt }
        assertNull(basketItems[0].deletedAt)
        assertNull(basketItems[1].deletedAt)
        assertNull(basketItems[2].deletedAt)
        assertNotNull(basketItems[3].deletedAt)

        val updatedDiscountCard1Usage2 = discountCardUsageFinderService.findById(updatedDiscountCard1Usage.id)
        val deletedDiscountCard2Usage = discountCardUsageFinderService.findById(updatedDiscountCard2Usage.id)
        assertNull(updatedDiscountCard1Usage2!!.deletedAt)
        assertNotNull(deletedDiscountCard2Usage!!.deletedAt)
    }

    @Test
    fun `test determineAppliedDiscountCard - discount removed from ticket - should clear applications of this card`() {
        val (
            basket,
            ticketBasketItem2,
            productBasketItem,
            updatedDiscountCard1Usage,
            updatedDiscountCard2Usage,
        ) = initDetermineDiscountCardTestData()

        underTest.deleteDiscountCardUsagesWhenPatchingBasketItem(
            DeleteDiscountCardUsagesWhenPatchingBasketItemCommand(
                basketId = basket.id,
                basketItemId = ticketBasketItem2.id,
                ticketId = ticketBasketItem2.ticketId!!,
                primaryTicketDiscountId = null,
                secondaryTicketDiscountId = Optional.empty()
            )
        )
        verify(exactly = 0) { applicationEventPublisherMock.publishEvent(any()) }

        val basketItems = basketItemJpaFinderService.findAllByBasketId(basket.id).sortedBy { it.createdAt }
        assertNull(basketItems[0].deletedAt)
        assertNull(basketItems[1].deletedAt)
        assertNotNull(basketItems[2].deletedAt)
        assertNotNull(basketItems[3].deletedAt)

        val updatedDiscountCard1Usage2 = discountCardUsageFinderService.findById(updatedDiscountCard1Usage.id)
        val deletedDiscountCard2Usage = discountCardUsageFinderService.findById(updatedDiscountCard2Usage.id)
        assertNull(updatedDiscountCard1Usage2!!.deletedAt)
        assertNotNull(deletedDiscountCard2Usage!!.deletedAt)
    }

    @Test
    fun `test deleteAllDiscountCardDiscounts - two discount cards in basket - should clear applications of this discount card`() {
        val (
            basket,
            ticketBasketItem2,
            productBasketItem,
            updatedDiscountCard1Usage,
            updatedDiscountCard2Usage,
        ) = initDetermineDiscountCardTestData()

        underTest.deleteAllDiscountCardDiscountsAppliedInBasketAndDiscountCardUsage(
            DeleteDiscountCardDiscountsAndUsageCommand(
                basketId = basket.id,
                discountCardId = DISCOUNT_CARD_2_ISOLATED_GROUP.id,
                discountCardCode = DISCOUNT_CARD_2_ISOLATED_GROUP.code,
                discountCardTicketDiscountId = DISCOUNT_CARD_2_ISOLATED_GROUP.ticketDiscountId
            )
        )
        verify {
            applicationEventPublisherMock.publishEvent(
                TicketBasketItemDiscountRemovalInitiatedEvent(
                    basketId = basket.id,
                    basketItemId = ticketBasketItem2.id,
                    secondaryTicketDiscountId = Optional.empty()
                )
            )
        }

        val basketItems = basketItemJpaFinderService.findAllByBasketId(basket.id).sortedBy { it.createdAt }
        assertNull(basketItems[0].deletedAt)
        assertNull(basketItems[1].deletedAt)
        assertNotNull(basketItems[2].deletedAt)
        assertNotNull(basketItems[3].deletedAt)

        val updatedDiscountCard1Usage2 = discountCardUsageFinderService.findById(updatedDiscountCard1Usage.id)
        val deletedDiscountCard2Usage = discountCardUsageFinderService.findById(updatedDiscountCard2Usage.id)
        assertNull(updatedDiscountCard1Usage2!!.deletedAt)
        assertNotNull(deletedDiscountCard2Usage!!.deletedAt)
    }

    private fun initDetermineDiscountCardsTestData(): BasketDiscountCardData {
        val basket = basketService.initBasket(
            mapToInitBasketCommand(items = listOf(TICKET_BASKET_ITEM_REQUEST_1))
        )
        val ticketBasketItem = basketItemJpaFinderService.findAllNonDeletedByBasketId(basket.id).first()

        // scan discount card 1
        val discountCard1Usage = discountCardUsageService.createDiscountCardUsage(
            CreateDiscountCardUsageCommand(
                discountCardId = DISCOUNT_CARD_1.id,
                basketId = basket.id,
                posConfigurationId = POS_CONFIGURATION_ID
            )
        )
        assertNull(discountCard1Usage.deletedAt)

        // apply ticket discount from discount card 1 on a ticket
        basketItemService.patchBasketItem(
            PatchBasketItemCommand(
                basketId = basket.id,
                basketItemId = ticketBasketItem.id,
                primaryTicketDiscountId = Optional.of(TICKET_DISCOUNT_1.id),
                primaryTicketDiscountCardId = DISCOUNT_CARD_1.id
            )
        )
        // add product from discount card 1
        val productBasketItem1 = basketItemService.createBasketItem(
            mapToCreateBasketItemCommand(
                request = PRODUCT_1_BASKET_ITEM_REQUEST,
                basketId = basket.id
            )
        )
        // update all discount card 1 usages explicitly (simulates behaviour of BasketItemDiscountCardUsageEventListener)
        discountCardUsageService.updateDiscountCardUsage(
            UpdateDiscountCardUsageCommand(
                discountCardUsageId = discountCard1Usage.id,
                basketItemId = ticketBasketItem.id
            )
        )
        discountCardUsageService.updateDiscountCardUsage(
            UpdateDiscountCardUsageCommand(
                discountCardUsageId = discountCard1Usage.id,
                basketItemId = productBasketItem1.id
            )
        )
        val updatedDiscountCard1Usage = discountCardUsageFinderService.findNonDeletedById(discountCard1Usage.id)
        assertEquals(ticketBasketItem.id, updatedDiscountCard1Usage!!.ticketBasketItemId)
        assertEquals(productBasketItem1.id, updatedDiscountCard1Usage.productBasketItemId)
        assertNull(updatedDiscountCard1Usage.productDiscountBasketItemId)

        // scan discount card 2
        val discountCard2Usage = discountCardUsageService.createDiscountCardUsage(
            CreateDiscountCardUsageCommand(
                discountCardId = DISCOUNT_CARD_2_ISOLATED_GROUP.id,
                basketId = basket.id,
                posConfigurationId = POS_CONFIGURATION_ID
            )
        )
        assertNull(discountCard2Usage.deletedAt)

        // add product from discount card 2
        val productBasketItem2 = basketItemService.createBasketItem(
            mapToCreateBasketItemCommand(
                request = PRODUCT_BASKET_ITEM_ISOLATED_PRODUCT_1_REQUEST,
                basketId = basket.id
            )
        )
        // add product discount from discount card 2
        val productDiscountBasketItem = basketItemService.createBasketItem(
            mapToCreateBasketItemCommand(
                request = PRODUCT_BASKET_ITEM_ISOLATED_PRODUCT_DISCOUNT_1_REQUEST,
                basketId = basket.id
            )
        )
        assertTrue(productDiscountBasketItem.price.isLessThan(0L.toBigDecimal()))

        // update all discount card 2 usages explicitly (simulates behaviour of BasketItemDiscountCardUsageEventListener)
        discountCardUsageService.updateDiscountCardUsage(
            UpdateDiscountCardUsageCommand(
                discountCardUsageId = discountCard2Usage.id,
                basketItemId = productBasketItem2.id
            )
        )
        discountCardUsageService.updateDiscountCardUsage(
            UpdateDiscountCardUsageCommand(
                discountCardUsageId = discountCard2Usage.id,
                basketItemId = productDiscountBasketItem.id
            )
        )
        val updatedDiscountCard2Usage = discountCardUsageFinderService.findNonDeletedById(discountCard2Usage.id)
        assertNull(updatedDiscountCard2Usage!!.ticketBasketItemId)
        assertEquals(productBasketItem2.id, updatedDiscountCard2Usage.productBasketItemId)
        assertEquals(productDiscountBasketItem.id, updatedDiscountCard2Usage.productDiscountBasketItemId)

        return BasketDiscountCardData(
            basket = basket,
            ticketBasketItem = ticketBasketItem,
            productBasketItem = productBasketItem2,
            updatedDiscountCard1Usage = updatedDiscountCard1Usage,
            updatedDiscountCard2Usage = updatedDiscountCard2Usage
        )
    }

    private fun initDetermineDiscountCardTestData(): BasketDiscountCardData {
        val basket = basketService.initBasket(
            mapToInitBasketCommand(items = listOf(TICKET_BASKET_ITEM_REQUEST_1, TICKET_BASKET_ITEM_REQUEST_2))
        )
        val ticketBasketItem1 = basketItemJpaFinderService.findAllNonDeletedByBasketId(basket.id)[0]
        val ticketBasketItem2 = basketItemJpaFinderService.findAllNonDeletedByBasketId(basket.id)[1]

        // scan discount card 1
        val discountCard1Usage = discountCardUsageService.createDiscountCardUsage(
            CreateDiscountCardUsageCommand(
                discountCardId = DISCOUNT_CARD_1.id,
                basketId = basket.id,
                posConfigurationId = POS_CONFIGURATION_ID
            )
        )
        assertNull(discountCard1Usage.deletedAt)

        // apply ticket discount from discount card 1 on a ticket
        basketItemService.patchBasketItem(
            PatchBasketItemCommand(
                basketId = basket.id,
                basketItemId = ticketBasketItem1.id,
                primaryTicketDiscountId = Optional.of(TICKET_DISCOUNT_1.id),
                primaryTicketDiscountCardId = DISCOUNT_CARD_1.id
            )
        )

        // scan discount card 2
        val discountCard2Usage = discountCardUsageService.createDiscountCardUsage(
            CreateDiscountCardUsageCommand(
                discountCardId = DISCOUNT_CARD_2_ISOLATED_GROUP.id,
                basketId = basket.id,
                posConfigurationId = POS_CONFIGURATION_ID
            )
        )
        assertNull(discountCard2Usage.deletedAt)

        // apply ticket discount from discount card 2 on a ticket
        basketItemService.patchBasketItem(
            PatchBasketItemCommand(
                basketId = basket.id,
                basketItemId = ticketBasketItem2.id,
                secondaryTicketDiscountId = Optional.of(TICKET_DISCOUNT_2.id),
                secondaryTicketDiscountCardId = DISCOUNT_CARD_2_ISOLATED_GROUP.id
            )
        )
        // add product from discount card 2
        val productBasketItem = basketItemService.createBasketItem(
            mapToCreateBasketItemCommand(
                request = PRODUCT_BASKET_ITEM_ISOLATED_PRODUCT_1_REQUEST,
                basketId = basket.id
            )
        )
        // add product discount from discount card 2
        val productDiscountBasketItem = basketItemService.createBasketItem(
            mapToCreateBasketItemCommand(
                request = PRODUCT_BASKET_ITEM_ISOLATED_PRODUCT_DISCOUNT_1_REQUEST,
                basketId = basket.id
            )
        )
        assertTrue(productDiscountBasketItem.price.isLessThan(0L.toBigDecimal()))

        // update all discount card usages explicitly (simulates behaviour of BasketItemDiscountCardUsageEventListener)
        discountCardUsageService.updateDiscountCardUsage(
            UpdateDiscountCardUsageCommand(
                discountCardUsageId = discountCard1Usage.id,
                basketItemId = ticketBasketItem1.id
            )
        )
        val updatedDiscountCard1Usage = discountCardUsageFinderService.findNonDeletedById(discountCard1Usage.id)
        assertEquals(ticketBasketItem1.id, updatedDiscountCard1Usage!!.ticketBasketItemId)
        assertNull(updatedDiscountCard1Usage.productBasketItemId)
        assertNull(updatedDiscountCard1Usage.productDiscountBasketItemId)

        discountCardUsageService.updateDiscountCardUsage(
            UpdateDiscountCardUsageCommand(
                discountCardUsageId = discountCard2Usage.id,
                basketItemId = ticketBasketItem2.id
            )
        )
        discountCardUsageService.updateDiscountCardUsage(
            UpdateDiscountCardUsageCommand(
                discountCardUsageId = discountCard2Usage.id,
                basketItemId = productBasketItem.id
            )
        )
        discountCardUsageService.updateDiscountCardUsage(
            UpdateDiscountCardUsageCommand(
                discountCardUsageId = discountCard2Usage.id,
                basketItemId = productDiscountBasketItem.id
            )
        )
        val updatedDiscountCard2Usage = discountCardUsageFinderService.findNonDeletedById(discountCard2Usage.id)
        assertEquals(ticketBasketItem2.id, updatedDiscountCard2Usage!!.ticketBasketItemId)
        assertEquals(productBasketItem.id, updatedDiscountCard2Usage.productBasketItemId)
        assertEquals(productDiscountBasketItem.id, updatedDiscountCard2Usage.productDiscountBasketItemId)

        return BasketDiscountCardData(
            basket = basket,
            ticketBasketItem = ticketBasketItem2,
            productBasketItem = null,
            updatedDiscountCard1Usage = updatedDiscountCard1Usage,
            updatedDiscountCard2Usage = updatedDiscountCard2Usage
        )
    }

    data class BasketDiscountCardData(
        val basket: Basket,
        val ticketBasketItem: BasketItem,
        val productBasketItem: BasketItem? = null,
        val updatedDiscountCard1Usage: DiscountCardUsage,
        val updatedDiscountCard2Usage: DiscountCardUsage,
    )
}

private val POS_CONFIGURATION_ID = UUID.fromString("895c03dc-1ef6-4008-9dab-3c9d6b82b92d")
private val DISTRIBUTOR_1 = createDistributor()
private val AUDITORIUM_1 = createAuditorium(
    originalId = 1,
    code = "A",
    title = "SÁLA A - CINEMAX BRATISLAVA"
)
private val AUDITORIUM_LAYOUT_1 = createAuditoriumLayout(auditoriumId = AUDITORIUM_1.id)
private val MOVIE_1 = createMovie(
    originalId = 1,
    title = "Star Wars: Episode I – The Phantom Menace",
    releaseYear = 2001,
    distributorId = DISTRIBUTOR_1.id
)
private val PRICE_CATEGORY_1 = createPriceCategory(
    originalId = 1,
    title = "ARTMAX PO 17",
    active = true
)
private val PRICE_CATEGORY_ITEM_1 = createPriceCategoryItem(
    priceCategoryId = PRICE_CATEGORY_1.id,
    number = PriceCategoryItemNumber.PRICE_1,
    title = "Dospely",
    price = BigDecimal.valueOf(9.5)
)
private var SCREENING_1 = createScreeningWithCurrentDateTime(
    originalId = 1,
    auditoriumId = AUDITORIUM_1.id,
    auditoriumLayoutId = AUDITORIUM_LAYOUT_1.id,
    movieId = MOVIE_1.id,
    priceCategoryId = PRICE_CATEGORY_1.id
)
private val SCREENING_FEE_1 = createScreeningFee(
    originalScreeningId = 1,
    screeningId = SCREENING_1.id,
    surchargeVip = BigDecimal.ZERO,
    surchargePremium = BigDecimal.ZERO,
    surchargeImax = BigDecimal.ZERO,
    surchargeUltraX = BigDecimal.ZERO,
    serviceFeeVip = BigDecimal.ZERO,
    serviceFeePremium = BigDecimal.ZERO,
    serviceFeeImax = BigDecimal.ZERO,
    serviceFeeUltraX = BigDecimal.ZERO,
    surchargeDBox = BigDecimal.ZERO,
    serviceFeeGeneral = BigDecimal.ZERO
)
private val SEAT_1 = createSeat(
    originalId = 1,
    auditoriumId = AUDITORIUM_1.id,
    auditoriumLayoutId = AUDITORIUM_LAYOUT_1.id,
    row = "A",
    number = "6",
    positionTop = 62
)
private val SEAT_2 = createSeat(
    originalId = 2,
    auditoriumId = AUDITORIUM_1.id,
    auditoriumLayoutId = AUDITORIUM_LAYOUT_1.id,
    row = "5",
    number = "7",
    positionLeft = 25,
    positionTop = 40
)
private val PRODUCT_CATEGORY_1 = createProductCategory(
    originalId = 1,
    code = "A1",
    title = "Snacks - doplnky"
)
private val PRODUCT_CATEGORY_2 = createProductCategory(
    originalId = 2,
    code = "A2",
    title = "Slevy",
    type = ProductCategoryType.DISCOUNT
)
private val PRODUCT_1 = createProduct(
    productCategoryId = PRODUCT_CATEGORY_1.id,
    title = "Popcorn XXL",
    price = BigDecimal.TEN
)
private val PRODUCT_COMPONENT_CATEGORY_1 = createProductComponentCategory()
private val PRODUCT_COMPONENT_1 = createProductComponent(
    originalId = 1,
    code = "01",
    title = "Kukurica",
    unit = ProductComponentUnit.KG,
    stockQuantity = BigDecimal.valueOf(180.0),
    productComponentCategoryId = PRODUCT_COMPONENT_CATEGORY_1.id
)
private val PRODUCT_COMPOSITION_1 = createProductComposition(
    originalId = 1,
    productId = PRODUCT_1.id,
    productComponentId = PRODUCT_COMPONENT_1.id,
    amount = BigDecimal.valueOf(0.25)
)
private val POS_CONFIGURATION_COMMAND = CreateOrUpdatePosConfigurationCommand(
    id = POS_CONFIGURATION_ID,
    macAddress = "AA-BB-CC-DD-EE",
    title = "dummyPOS",
    receiptsDirectory = "/dummy/directory",
    ticketSalesEnabled = false,
    productModes = setOf(ProductMode.STANDARD),
    tablesType = TablesType.NO_TABLES,
    seatsEnabled = false
)
private val TICKET_DISCOUNT_1 = createTicketDiscount(
    originalId = 1,
    code = "01X",
    title = "Artmax",
    type = TicketDiscountType.ABSOLUTE,
    usageType = TicketDiscountUsageType.PRIMARY,
    amount = BigDecimal.ONE,
    order = 1
)
private val TICKET_DISCOUNT_2 = createTicketDiscount(
    originalId = 2,
    code = "02X",
    title = "Billa 2",
    type = TicketDiscountType.ABSOLUTE,
    usageType = TicketDiscountUsageType.SECONDARY,
    amount = BigDecimal.valueOf(2),
    order = 2
)
private val PRODUCT_DISCOUNT_1 = createProduct(
    originalId = 10,
    code = "03X",
    productCategoryId = PRODUCT_CATEGORY_2.id,
    title = "Slevova karta -10%",
    type = ProductType.PRODUCT,
    price = 0L.toBigDecimal(),
    discountPercentage = 10
)
private val DISCOUNT_CARD_1 = createDiscountCard(
    originalId = 123435,
    ticketDiscountId = TICKET_DISCOUNT_1.id,
    productId = PRODUCT_1.id,
    title = "VIP karta",
    code = "67900000"
)
private val DISCOUNT_CARD_2_ISOLATED_GROUP = createDiscountCard(
    originalId = 67890,
    ticketDiscountId = TICKET_DISCOUNT_2.id,
    productDiscountId = PRODUCT_DISCOUNT_1.id,
    productId = PRODUCT_1.id,
    title = "Slevova karta",
    code = "679567"
)
private val PRODUCT_1_BASKET_ITEM_REQUEST = CreateBasketItemRequest(
    type = BasketItemType.PRODUCT,
    quantity = 1,
    product = CreateProductRequest(
        productId = PRODUCT_1.id,
        discountCardId = DISCOUNT_CARD_1.id
    )
)
private val PRODUCT_BASKET_ITEM_ISOLATED_PRODUCT_1_REQUEST = CreateBasketItemRequest(
    type = BasketItemType.PRODUCT,
    quantity = 1,
    product = CreateProductRequest(
        productId = PRODUCT_1.id,
        productIsolatedWithId = PRODUCT_DISCOUNT_1.id,
        discountCardId = DISCOUNT_CARD_2_ISOLATED_GROUP.id
    )
)
private val PRODUCT_BASKET_ITEM_ISOLATED_PRODUCT_DISCOUNT_1_REQUEST = CreateBasketItemRequest(
    type = BasketItemType.PRODUCT_DISCOUNT,
    quantity = 1,
    product = CreateProductRequest(
        productId = PRODUCT_DISCOUNT_1.id,
        productIsolatedWithId = PRODUCT_1.id,
        discountCardId = DISCOUNT_CARD_2_ISOLATED_GROUP.id
    )
)
private val TICKET_BASKET_ITEM_REQUEST_1 = CreateBasketItemRequest(
    type = BasketItemType.TICKET,
    quantity = 1,
    ticket = CreateTicketRequest(
        ticketPrice = CreateTicketPriceRequest(
            // proper one is calculated via TicketPriceService and set in relevant test cases
            priceCategoryItemNumber = PriceCategoryItemNumber.PRICE_1
        ),
        reservation = CreateReservationRequest(
            seatId = SEAT_1.id
        ),
        screeningId = SCREENING_1.id
    )
)
private val TICKET_BASKET_ITEM_REQUEST_2 = CreateBasketItemRequest(
    type = BasketItemType.TICKET,
    quantity = 1,
    ticket = CreateTicketRequest(
        ticketPrice = CreateTicketPriceRequest(
            // proper one is calculated via TicketPriceService and set in relevant test cases
            priceCategoryItemNumber = PriceCategoryItemNumber.PRICE_1
        ),
        reservation = CreateReservationRequest(
            seatId = SEAT_2.id
        ),
        screeningId = SCREENING_1.id
    )
)
