package com.cleevio.cinemax.api.common.service

import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.CsvSource
import kotlin.test.assertEquals

class CodeGeneratorTest {

    @ParameterizedTest
    @CsvSource(
        "2024, false",
        "202, false",
        "2, false",
        "ABCD, false",
        "ABC, false",
        "A, false",
        "ak, false",
        "a0, false",
        "9k, false",
        "$^*}, false",
        "$^*, false",
        "$^, false",
        "$, false",
        "A&, false",
        "8&, false",
        "08, true",
        "16, true",
        "9K, true",
        "AA, true",
        "ZZ, true",
        "T4, true"
    )
    fun `test Distributor and TicketDiscount regex - should evaluate according to the regex rule`(
        code: String,
        expectedResult: Boolean,
    ) {
        assertEquals(expectedResult, code.matches(Regex("[0-9A-Z]{2}")))
    }

    @ParameterizedTest
    @CsvSource(
        "202444, false",
        "2024, false",
        "202, false",
        "20, false",
        "2, false",
        "ABCDEF, false",
        "ABCDE, false",
        "ABCD, false",
        "ABC, false",
        "AB, false",
        "A, false",
        "abcdef, false",
        "abcde, false",
        "abcd, false",
        "abc, false",
        "ab, false",
        "a, false",
        "AAA77, false",
        "7U9K0, false",
        "$^#@)(, false",
        "$^#@), false",
        "$^#@, false",
        "$^#, false",
        "$^, false",
        "$, false",
        "00987, true",
        "11894, true",
        "44356, true"
    )
    fun `test ProductComponent and Product regex - should evaluate according to the regex rule`(
        code: String,
        expectedResult: Boolean,
    ) {
        assertEquals(expectedResult, code.matches(Regex("[0-9]{5}")))
    }

    @ParameterizedTest
    @CsvSource(
        "202, false",
        "2, false",
        "ABC, false",
        "AB, false",
        "A, false",
        "abc, false",
        "ab, false",
        "a, false",
        "A7, false",
        "k0, false",
        "$^#, false",
        "$^, false",
        "$, false",
        "00, true",
        "11, true",
        "56, true"
    )
    fun `test ProductComponentCategory, ProductCategory and Supplier regex - should evaluate according to the regex rule`(
        code: String,
        expectedResult: Boolean,
    ) {
        assertEquals(expectedResult, code.matches(Regex("[0-9]{2}")))
    }

    @ParameterizedTest
    @CsvSource(
        "2024444, false",
        "20244, false",
        "2024, false",
        "202, false",
        "20, false",
        "2, false",
        "ABCDEFG, false",
        "ABCDEF, false",
        "ABCDE, false",
        "ABCD, false",
        "ABC, false",
        "AB, false",
        "A, false",
        "abcdefg, false",
        "abcdef, false",
        "abcde, false",
        "abcd, false",
        "abc, false",
        "ab, false",
        "a, false",
        "AAA777, false",
        "7U9K00, false",
        "8f9000, false",
        "8DA787, false",
        "$^#@)(), false",
        "$^#@)(, false",
        "$^#@), false",
        "$^#@, false",
        "$^#, false",
        "$^, false",
        "$, false",
        "809879, true",
        "8Z0000, true",
        "8D8787, true"
    )
    fun `test Movie regex - should evaluate according to the regex rule`(
        code: String,
        expectedResult: Boolean,
    ) {
        assertEquals(expectedResult, code.matches(Regex("8[0-9A-Z][0-9]{4}")))
    }
}
