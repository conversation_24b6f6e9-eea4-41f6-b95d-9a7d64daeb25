package com.cleevio.cinemax.api.common.holiday

import com.cleevio.cinemax.api.IntegrationTest
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.CsvSource
import org.springframework.beans.factory.annotation.Autowired
import java.time.LocalDate
import kotlin.test.assertEquals

class HolidayResolverIT @Autowired constructor(
    private val underTest: HolidayResolver,
) : IntegrationTest() {

    @ParameterizedTest
    @CsvSource(
        // Easter in 2024
        "2024-03-29, true",
        "2024-03-31, true",
        "2024-04-01, true",
        // Fixed-date holidays
        "2018-11-17, true",
        "2032-08-29, true",
        "2024-12-24, true",
        "2027-12-25, true",
        "2054-12-26, true",
        // Easter is celebrated on different days in 2025 (comparing to 2024)
        "2025-03-29, false",
        "2025-03-31, false",
        "2025-04-01, false",
        // Random non-holiday dates
        "2026-01-17, false",
        "2030-08-02, false",
        "2045-03-05, false"
    )
    fun `test isNationalHoliday, should resolve correctly`(
        date: LocalDate,
        expectedResult: Boolean,
    ) {
        assertEquals(expectedResult, underTest.isNationalHoliday(date))
    }
}
