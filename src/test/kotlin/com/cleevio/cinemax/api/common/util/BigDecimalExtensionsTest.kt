package com.cleevio.cinemax.api.common.util

import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.CsvSource
import java.math.BigDecimal
import kotlin.test.assertEquals
import kotlin.test.assertTrue

class BigDecimalExtensionsTest {

    @ParameterizedTest
    @CsvSource(
        "100, 15, 115",
        "0, 15, 0",
        "100, 0, 100",
        "100.897, 10, 110.99",
        "100.897, 15, 116.03",
        "100.836, 20, 121"
    )
    fun `test addTax`(amount: BigDecimal, taxRate: Int, expected: BigDecimal) =
        assertTrue(expected.isEqualTo(amount.addTax(taxRate)))

    @ParameterizedTest
    @CsvSource(
        "100, 15, 86.96",
        "0, 15, 0",
        "100, 0, 100",
        "100.897, 10, 91.72",
        "100.897, 15, 87.74",
        "145.204, 20, 121"
    )
    fun `test subtractTax`(amount: BigDecimal, taxRate: Int, expected: BigDecimal) =
        assertTrue(expected.isEqualTo(amount.subtractTax(taxRate)))

    @ParameterizedTest
    @CsvSource(
        "1.00, true",
        "1.01, false",
        "0.4321, false",
        "205.000, true",
        "5500.897, false"
    )
    fun `test isWholeNumber`(amount: BigDecimal, expected: Boolean) =
        assertEquals(expected, amount.isWholeNumber())
}
