package com.cleevio.cinemax.api.common.service

import com.cleevio.cinemax.api.common.constant.WebSocketEndpoint
import com.cleevio.cinemax.api.common.constant.WebSocketMessageDataKey
import com.cleevio.cinemax.api.common.constant.WebSocketMessageType
import com.cleevio.cinemax.api.common.message.WebSocketMessage
import com.cleevio.cinemax.api.module.product.service.model.ProductStockQuantityModel
import com.cleevio.cinemax.api.module.reservation.constant.ReservationState
import com.cleevio.cinemax.api.module.reservation.service.model.ReservationWebSocketModel
import com.fasterxml.jackson.databind.ObjectMapper
import io.mockk.Runs
import io.mockk.confirmVerified
import io.mockk.every
import io.mockk.just
import io.mockk.mockk
import io.mockk.verify
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.Arguments
import org.junit.jupiter.params.provider.MethodSource
import org.springframework.messaging.simp.SimpMessagingTemplate
import java.util.UUID
import java.util.stream.Stream

class WebSocketMessagingServiceTest {

    private val simpMessagingTemplate = mockk<SimpMessagingTemplate>()

    private val underTest = WebSocketMessagingService(simpMessagingTemplate)

    @AfterEach
    fun afterEach() = confirmVerified(
        simpMessagingTemplate
    )

    @ParameterizedTest
    @MethodSource("webSocketMessageArgumentProvider")
    fun `test sendMessage, should serialize correct message`(
        message: WebSocketMessage,
        expectedMessage: String,
        endpoint: String,
    ) {
        every { simpMessagingTemplate.convertAndSend(any(), any<String>()) } just Runs

        underTest.sendMessage(
            message = message,
            destination = endpoint
        )

        verify {
            simpMessagingTemplate.convertAndSend(
                endpoint,
                OBJECT_MAPPER.readTree(expectedMessage).toString()
            )
        }
    }

    companion object {
        @JvmStatic
        fun webSocketMessageArgumentProvider(): Stream<Arguments> {
            return Stream.of(
                Arguments.of(
                    WS_MESSAGE_RESERVATION_CREATED,
                    EXPECTED_WS_MESSAGE_RESERVATION_CREATED,
                    WebSocketEndpoint.RESERVATIONS_TOPIC_ENDPOINT
                ),
                Arguments.of(
                    WS_MESSAGE_RESERVATION_CONFIRMED,
                    EXPECTED_WS_MESSAGE_RESERVATION_CONFIRMED,
                    WebSocketEndpoint.RESERVATIONS_TOPIC_ENDPOINT
                ),
                Arguments.of(
                    WS_MESSAGE_RESERVATION_DELETED,
                    EXPECTED_WS_MESSAGE_RESERVATION_DELETED,
                    WebSocketEndpoint.RESERVATIONS_TOPIC_ENDPOINT
                ),
                Arguments.of(
                    WS_MESSAGE_PRODUCT_STOCK_QUANTITY_CHANGED,
                    EXPECTED_WS_MESSAGE_PRODUCT_STOCK_QUANTITY_CHANGED,
                    WebSocketEndpoint.PRODUCTS_TOPIC_ENDPOINT
                )
            )
        }
    }
}

private val OBJECT_MAPPER = ObjectMapper()

private val SCREENING_ID = UUID.fromString("85c8c6be-2dc7-45cf-a54d-b8a57093b180")
private val SEAT_ID = UUID.fromString("24bcf57b-6d82-4df6-a0c4-b3e88713219f")
private val RESERVATION_CREATED_MODEL_1 = ReservationWebSocketModel(
    state = ReservationState.RESERVED,
    screeningId = SCREENING_ID,
    seatId = SEAT_ID
)
private val RESERVATION_CONFIRMED_MODEL_1 = RESERVATION_CREATED_MODEL_1.copy(state = ReservationState.UNAVAILABLE)
private val RESERVATION_DELETED_MODEL_1 = RESERVATION_CREATED_MODEL_1.copy(state = ReservationState.DELETED)
private val RELATED_PRODUCT_IDS = listOf(UUID.randomUUID(), UUID.randomUUID(), UUID.randomUUID())
private val PRODUCTS_STOCK_QUANTITY_MODEL = listOf(
    ProductStockQuantityModel(RELATED_PRODUCT_IDS[0], 750),
    ProductStockQuantityModel(RELATED_PRODUCT_IDS[1], 1500),
    ProductStockQuantityModel(RELATED_PRODUCT_IDS[2], 255)
)
private val WS_MESSAGE_RESERVATION_CREATED = WebSocketMessage(
    type = WebSocketMessageType.RESERVATION_CREATED,
    data = mapOf(WebSocketMessageDataKey.RESERVATION to RESERVATION_CREATED_MODEL_1)
)
private val WS_MESSAGE_RESERVATION_CONFIRMED = WebSocketMessage(
    type = WebSocketMessageType.RESERVATION_CONFIRMED,
    data = mapOf(WebSocketMessageDataKey.RESERVATION to RESERVATION_CONFIRMED_MODEL_1)
)
private val WS_MESSAGE_RESERVATION_DELETED = WebSocketMessage(
    type = WebSocketMessageType.RESERVATION_DELETED,
    data = mapOf(WebSocketMessageDataKey.RESERVATION to RESERVATION_DELETED_MODEL_1)
)
private val WS_MESSAGE_PRODUCT_STOCK_QUANTITY_CHANGED = WebSocketMessage(
    type = WebSocketMessageType.PRODUCT_STOCK_QUANTITY_CHANGED,
    data = mapOf(WebSocketMessageDataKey.PRODUCTS to PRODUCTS_STOCK_QUANTITY_MODEL)
)
private val EXPECTED_WS_MESSAGE_RESERVATION_CREATED = """
        {
          "type": "${WebSocketMessageType.RESERVATION_CREATED}",
          "data": {
            "reservation": {
              "state": "${ReservationState.RESERVED}",
              "screeningId": "$SCREENING_ID",
              "seatId": "$SEAT_ID"
            }
          }
        }
""".trimIndent()
private val EXPECTED_WS_MESSAGE_RESERVATION_CONFIRMED = """
        {
          "type": "${WebSocketMessageType.RESERVATION_CONFIRMED}",
          "data": {
            "reservation": {
              "state": "${ReservationState.UNAVAILABLE}",
              "screeningId": "$SCREENING_ID",
              "seatId": "$SEAT_ID"
            }
          }
        }
""".trimIndent()
private val EXPECTED_WS_MESSAGE_RESERVATION_DELETED = """
        {
          "type": "${WebSocketMessageType.RESERVATION_DELETED}",
          "data": {
            "reservation": {
              "state": "${ReservationState.DELETED}",
              "screeningId": "$SCREENING_ID",
              "seatId": "$SEAT_ID"
            }
          }
        }
""".trimIndent()
private val EXPECTED_WS_MESSAGE_PRODUCT_STOCK_QUANTITY_CHANGED = """
        {
          "type": "${WebSocketMessageType.PRODUCT_STOCK_QUANTITY_CHANGED}",
          "data": {
            "products": [
              {
                "id": "${PRODUCTS_STOCK_QUANTITY_MODEL[0].id}",
                "stockQuantity": ${PRODUCTS_STOCK_QUANTITY_MODEL[0].stockQuantity}
              },
              {
                "id": "${PRODUCTS_STOCK_QUANTITY_MODEL[1].id}",
                "stockQuantity": ${PRODUCTS_STOCK_QUANTITY_MODEL[1].stockQuantity}
              },
              {
                "id": "${PRODUCTS_STOCK_QUANTITY_MODEL[2].id}",
                "stockQuantity": ${PRODUCTS_STOCK_QUANTITY_MODEL[2].stockQuantity}
              }
            ]
          }
        }
""".trimIndent()
