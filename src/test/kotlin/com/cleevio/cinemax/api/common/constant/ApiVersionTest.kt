package com.cleevio.cinemax.api.common.constant

import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.api.Test
import java.util.Locale
import java.util.regex.Pattern

class ApiVersionTest {

    @Test
    fun `test all string constants are correctly named`() {
        for (field in ApiVersion::class.java.declaredFields) {
            if (String::class.java.isAssignableFrom(field.type)) {
                val fieldName = field.name
                assertTrue(
                    SUPPORTED_STRING_CONSTANT_NAME_PATTERN.matcher(fieldName).matches(),
                    "The String field $fieldName does not match the expected name format."
                )
            }
        }
    }

    @Test
    fun `test supported api versions contains all publicly declared version constants`() {
        val declaredFields = ApiVersion::class.java.declaredFields
        val supposedApiVersions: MutableList<String> = mutableListOf()
        for (field in declaredFields) {
            if (APP_NAMESPACE_FIELD_NAME == field.name) {
                continue
            }
            if (String::class.java.isAssignableFrom(field.type)) {
                supposedApiVersions.add(field[null] as String)
            }
        }
        assertTrue(
            ApiVersion.SUPPORTED_API_VERSIONS.containsAll(supposedApiVersions),
            "The collection of supported API versions did not contain all supposedly " +
                "declared API versions. Make sure to register all of them."
        )
        assertTrue(
            supposedApiVersions.containsAll(ApiVersion.SUPPORTED_API_VERSIONS),
            "The supported API versions contains some versions which are not publicly declared. " +
                "Make sure all supported versions are declared as public static constants."
        )
    }

    @Test
    fun `test app namespace holds correctly formatted value`() {
        assertTrue(
            APP_NAMESPACE_VALID_VALUE_PATTERN.matcher(ApiVersion.APP_NAMESPACE).matches(),
            "Unsupported format of the APP_NAMESPACE attribute."
        )
    }

    @Test
    fun `test version constants hold property formatted values`() {
        for (field in ApiVersion::class.java.declaredFields) {
            val fieldName = field.name
            val matcher = VERSION_FIELD_NAME_PATTERN.matcher(fieldName)
            if (!matcher.matches()) {
                continue
            }
            val version = matcher.group("version").toInt()
            val dataFormat = matcher.group("dataFormat").lowercase(Locale.getDefault())
            val expectedValue = if (dataFormat == "xlsx") {
                "application/" + ApiVersion.APP_NAMESPACE + "-v" + version +
                    "+vnd.openxmlformats-officedocument.spreadsheetml.sheet"
            } else {
                "application/" + ApiVersion.APP_NAMESPACE + "-v" + version + "+" + dataFormat
            }
            assertEquals(
                expectedValue,
                field[null],
                "Unsupported correlation between the name of the field $fieldName and its value."
            )
        }
    }
}

private const val APP_NAMESPACE_FIELD_NAME = "APP_NAMESPACE"
private const val VERSION_FIELD_NAME = "VERSION_(?<version>\\d{1,2})_(?<dataFormat>JSON|XLSX|XML)"
private val VERSION_FIELD_NAME_PATTERN = Pattern.compile("^$VERSION_FIELD_NAME$")
private val SUPPORTED_STRING_CONSTANT_NAME_PATTERN =
    Pattern.compile("^$APP_NAMESPACE_FIELD_NAME|$VERSION_FIELD_NAME$")

private const val APP_NAMESPACE_VALID_VALUE = "^([a-zA-Z])+(\\.[a-zA-Z]+)*$"
private val APP_NAMESPACE_VALID_VALUE_PATTERN = Pattern.compile(APP_NAMESPACE_VALID_VALUE)
