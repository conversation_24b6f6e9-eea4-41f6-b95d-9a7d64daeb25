package com.cleevio.cinemax.api.common.integration.disfilm

import com.cleevio.cinemax.api.ConnectorTest
import com.cleevio.cinemax.api.common.integration.disfilm.xml.DISFilmMovie
import okhttp3.mockwebserver.MockResponse
import okhttp3.mockwebserver.MockWebServer
import org.junit.jupiter.api.AfterAll
import org.junit.jupiter.api.BeforeAll
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.http.HttpHeaders
import org.springframework.http.HttpMethod
import org.springframework.http.MediaType
import org.springframework.test.context.TestPropertySource
import kotlin.test.assertEquals

@TestPropertySource(properties = ["integration.disfilm.base-url=http://localhost:8080"])
class DISFilmConnectorIT @Autowired constructor(
    private val underTest: DISFilmConnector,
) : ConnectorTest() {

    private lateinit var mockWebServer: MockWebServer

    @BeforeAll
    fun setUp() {
        mockWebServer = MockWebServer()
        mockWebServer.start(8080)
    }

    @AfterAll
    fun tearDown() {
        mockWebServer.shutdown()
    }

    @Test
    fun `test getMovies, should deserialize correctly`() {
        mockWebServer.enqueue(
            MockResponse()
                .setBody(EXPECTED_MOVIES_RESPONSE_XML)
                .addHeader(HttpHeaders.CONTENT_TYPE, MediaType.TEXT_XML)
        )

        val disFilmMovies = underTest.getMovies()
        assertEquals(DISFILM_MOVIES.size, disFilmMovies.size)

        for (i in disFilmMovies.indices) {
            assertDISFilmMovieEquals(DISFILM_MOVIES[i], disFilmMovies[i])
        }

        val recordedRequest = mockWebServer.takeRequest()
        assertEquals(HttpMethod.GET.name(), recordedRequest.method)
        assertEquals("/movies?by=format", recordedRequest.path)
    }

    private fun assertDISFilmMovieEquals(expected: DISFilmMovie, actual: DISFilmMovie) {
        assertEquals(expected.disfilmCode, actual.disfilmCode)
        assertEquals(expected.rawTitle, actual.rawTitle)
        assertEquals(expected.originalTitle, actual.originalTitle)
        assertEquals(expected.distributorCode, actual.distributorCode)
        assertEquals(expected.premiereDate, actual.premiereDate)
        assertEquals(expected.genreTitle, actual.genreTitle)
        assertEquals(expected.ratingCode, actual.ratingCode)
        assertEquals(expected.duration, actual.duration)
        assertEquals(expected.technologyTitle, actual.technologyTitle)
        assertEquals(expected.languageCode, actual.languageCode)
        assertEquals(expected.productionTitle, actual.productionTitle)
    }
}

private val DISFILM_MOVIES = listOf(
    DISFilmMovie(
        disfilmCode = "XXX19003F3",
        rawTitle = "Trhlina (DVD) DVD (OV)",
        originalTitle = "Trhlina DVD (OV)",
        distributorCode = "17",
        premiereDate = "2019-01-01",
        ratingCode = "15",
        duration = 111,
        technologyTitle = "DVD",
        languageCode = "ORIG"
    ),
    DISFilmMovie(
        disfilmCode = "BON19001A5",
        rawTitle = "Hovory s TGM 2D (ČD)",
        originalTitle = "Hovory s TGM 2D (ČD)",
        distributorCode = "12",
        premiereDate = "2019-01-03",
        genreTitle = "Dráma",
        ratingCode = "12",
        duration = 80,
        technologyTitle = "DCI2D",
        languageCode = "CZ",
        productionTitle = "Česká Republika"
    ),
    DISFilmMovie(
        disfilmCode = "SAT19001S4",
        rawTitle = "Ralph búra internet 2DS (SD)",
        originalTitle = "RALPF BREAKS THE INTERNET 2DS (SD)",
        distributorCode = "14"
    )
)
private val EXPECTED_MOVIES_RESPONSE_XML = """
<data>
    <movie>
        <code>XXX19003F3</code>
        <title>Trhlina (DVD) DVD (OV)</title>
        <titleOrig>Trhlina DVD (OV)</titleOrig>
        <distribCode>17</distribCode>
        <distribName>CONTINENTAL FILM, s.r.o.</distribName>
        <releaseDate>2019-01-01</releaseDate>
        <rating>15</rating>
        <length>111</length>
        <format>DVD</format>
        <language>ORIG</language>
    </movie>
    <movie>
        <code>BON19001A5</code>
        <title>Hovory s TGM 2D (ČD)</title>
        <titleOrig>Hovory s TGM 2D (ČD)</titleOrig>
        <distribCode>12</distribCode>
        <distribName>BONTONFILM a.s.</distribName>
        <releaseDate>2019-01-03</releaseDate>
        <genre>Dráma</genre>
        <rating>12</rating>
        <length>80</length>
        <format>DCI2D</format>
        <language>CZ</language>
        <country>Česká Republika</country>
    </movie>
    <movie>
        <code>SAT19001S4</code>
        <title>Ralph búra internet 2DS (SD)</title>
        <titleOrig>RALPF BREAKS THE INTERNET 2DS (SD)</titleOrig>
        <distribCode>14</distribCode>
        <distribName>SATURN ENTERTAINMENT, spol. s r. o.</distribName>
        <rights>Autorska agentura Ceske republiky</rights>
        <cast>Ralph Doe</cast>
    </movie>
</data>
""".trimIndent()
