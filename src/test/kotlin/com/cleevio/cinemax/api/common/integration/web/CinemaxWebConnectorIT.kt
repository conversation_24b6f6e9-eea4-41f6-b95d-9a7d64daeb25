package com.cleevio.cinemax.api.common.integration.web

import com.cleevio.cinemax.api.ConnectorTest
import com.cleevio.cinemax.api.common.integration.IntegrationException
import com.cleevio.cinemax.api.common.integration.web.dto.PublishScreeningOnlineRequest
import com.cleevio.cinemax.api.common.integration.web.dto.PublishScreeningsOnlineResponse
import com.cleevio.cinemax.api.common.integration.web.dto.UnpublishScreeningOnlineRequest
import com.cleevio.cinemax.api.common.integration.web.dto.UnpublishScreeningsOnlineResponse
import com.cleevio.cinemax.api.common.util.MAPPER
import com.cleevio.cinemax.api.common.util.fromJsonString
import okhttp3.mockwebserver.MockResponse
import okhttp3.mockwebserver.MockWebServer
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.http.HttpHeaders
import org.springframework.http.MediaType
import org.springframework.test.context.TestPropertySource
import kotlin.test.assertEquals
import kotlin.test.assertNotNull
import kotlin.test.assertTrue

@TestPropertySource(properties = ["integration.cinemax-web.base-url=http://localhost:8080"])
class CinemaxWebConnectorIT @Autowired constructor(
    private val underTest: CinemaxWebConnector,
) : ConnectorTest() {

    private lateinit var mockWebServer: MockWebServer

    @BeforeEach
    fun setUp() {
        mockWebServer = MockWebServer()
        mockWebServer.start(8080)
    }

    @AfterEach
    fun tearDown() {
        mockWebServer.shutdown()
    }

    @Test
    fun `test publishScreeningOnline - should map request and deserialize response correctly`() {
        val expectedResponseJson = MAPPER.writeValueAsString(PUBLISH_SCREENING_ONLINE_RESPONSE)

        mockWebServer.enqueue(
            MockResponse()
                .setBody(expectedResponseJson)
                .addHeader(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE)
        )

        val response = underTest.publishScreeningsOnline(listOf(PUBLISH_SCREENING_ONLINE_REQUEST))

        assertNotNull(response)
        assertEquals(PUBLISH_SCREENING_ONLINE_RESPONSE.imported, response.imported)
        assertTrue(response.errors.isEmpty())

        val recordedRequest = mockWebServer.takeRequest()
        assertEquals("POST", recordedRequest.method)
        assertEquals("/api/programApi", recordedRequest.path)
        assertEquals(MediaType.APPLICATION_JSON_VALUE, recordedRequest.getHeader(HttpHeaders.CONTENT_TYPE))
        assertEquals("application/json", recordedRequest.getHeader(HttpHeaders.ACCEPT))

        val actualRequestBody = recordedRequest.body.readUtf8()
        val actualRequest: List<PublishScreeningOnlineRequest> = actualRequestBody.fromJsonString()

        assertEquals(PUBLISH_SCREENING_ONLINE_REQUEST, actualRequest[0])
    }

    @Test
    fun `test unpublishScreeningOnline - should map request and deserialize response correctly`() {
        val expectedResponseJson = MAPPER.writeValueAsString(UNPUBLISH_SCREENING_ONLINE_RESPONSE)

        mockWebServer.enqueue(
            MockResponse()
                .setBody(expectedResponseJson)
                .addHeader(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE)
        )

        val response = underTest.unpublishScreeningsOnline(listOf(UNPUBLISH_SCREENING_ONLINE_REQUEST))

        assertNotNull(response)
        assertEquals(UNPUBLISH_SCREENING_ONLINE_RESPONSE.deleted, response.deleted)
        assertTrue(response.errors.isEmpty())

        val recordedRequest = mockWebServer.takeRequest()
        assertEquals("POST", recordedRequest.method)
        assertEquals("/api/programApi/delete", recordedRequest.path)
        assertEquals(MediaType.APPLICATION_JSON_VALUE, recordedRequest.getHeader(HttpHeaders.CONTENT_TYPE))
        assertEquals("application/json", recordedRequest.getHeader(HttpHeaders.ACCEPT))

        val actualRequestBody = recordedRequest.body.readUtf8()
        val actualRequest: List<UnpublishScreeningOnlineRequest> = actualRequestBody.fromJsonString()

        assertEquals(UNPUBLISH_SCREENING_ONLINE_REQUEST, actualRequest[0])
    }

    @Test
    fun `test publishScreeningOnline - error response - should throw`() {
        val expectedResponseJson = MAPPER.writeValueAsString(PUBLISH_SCREENING_ONLINE_FAIL_RESPONSE)

        mockWebServer.enqueue(
            MockResponse()
                .setResponseCode(500)
                .setBody(expectedResponseJson)
                .addHeader(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE)
        )

        assertThrows<IntegrationException> {
            underTest.publishScreeningsOnline(listOf(PUBLISH_SCREENING_ONLINE_REQUEST))
        }
    }

    @Test
    fun `test unpublishScreeningOnline - error response - should throw`() {
        val expectedResponseJson = MAPPER.writeValueAsString(UNPUBLISH_SCREENING_ONLINE_FAIL_RESPONSE)

        mockWebServer.enqueue(
            MockResponse()
                .setResponseCode(400)
                .setBody(expectedResponseJson)
                .addHeader(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE)
        )

        assertThrows<IntegrationException> {
            underTest.unpublishScreeningsOnline(listOf(UNPUBLISH_SCREENING_ONLINE_REQUEST))
        }
    }
}

private val PUBLISH_SCREENING_ONLINE_REQUEST = PublishScreeningOnlineRequest(
    movieTitle = "Dummy Movie 3D",
    auditoriumTitle = "Sála A",
    screeningDate = "01-12-2024",
    screeningTime = "20:30",
    screeningTypeTitle1 = "2D",
    screeningTypeTitle2 = "3D",
    screeningTypeTitle3 = "",
    screeningTypeTitle4 = "",
    ratingTitle = "PG-13",
    tmsLanguageTitle = "English",
    duration = "160",
    screeningOriginalId = "1",
    cinemaCode = "ba"
)

private val UNPUBLISH_SCREENING_ONLINE_REQUEST = UnpublishScreeningOnlineRequest(
    screeningOriginalId = "1",
    cinemaCode = "ba"
)

private val PUBLISH_SCREENING_ONLINE_RESPONSE = PublishScreeningsOnlineResponse(
    imported = 1,
    errors = emptyList()
)

private val PUBLISH_SCREENING_ONLINE_FAIL_RESPONSE = PublishScreeningsOnlineResponse(
    imported = 0,
    errors = emptyList()
)

private val UNPUBLISH_SCREENING_ONLINE_RESPONSE = UnpublishScreeningsOnlineResponse(
    deleted = 1,
    errors = emptyList()
)
private val UNPUBLISH_SCREENING_ONLINE_FAIL_RESPONSE = UnpublishScreeningsOnlineResponse(
    deleted = 0,
    errors = emptyList()
)
