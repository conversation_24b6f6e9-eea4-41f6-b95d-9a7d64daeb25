package com.cleevio.cinemax.api.common.util

import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.Arguments
import org.junit.jupiter.params.provider.MethodSource
import java.time.LocalTime
import java.util.stream.Stream
import kotlin.test.assertEquals

class LocalTimeExtensionsTest {

    @ParameterizedTest
    @MethodSource("timeAndTimeIntervalProvider")
    fun `test isInInterval, should determine correctly`(
        time: LocalTime,
        startTime: LocalTime,
        endTime: LocalTime,
        expectedResult: Boolean,
    ) {
        assertEquals(expectedResult, time.isInInterval(startTime, endTime))
    }

    companion object {
        @JvmStatic
        fun timeAndTimeIntervalProvider(): Stream<Arguments> {
            return Stream.of(
                Arguments.of(LocalTime.of(12, 0), LocalTime.of(11, 0), LocalTime.of(13, 0), true),
                Arguments.of(LocalTime.of(12, 0), LocalTime.of(13, 0), LocalTime.of(11, 0), true),
                Arguments.of(LocalTime.of(12, 0), LocalTime.of(10, 0), LocalTime.of(11, 0), false),
                Arguments.of(LocalTime.of(12, 0), LocalTime.of(20, 0), LocalTime.of(21, 0), false),
                Arguments.of(LocalTime.of(12, 0), LocalTime.of(23, 0), LocalTime.of(10, 0), true),
                Arguments.of(LocalTime.of(12, 0), LocalTime.of(13, 0), LocalTime.of(23, 0), false)
            )
        }
    }
}
