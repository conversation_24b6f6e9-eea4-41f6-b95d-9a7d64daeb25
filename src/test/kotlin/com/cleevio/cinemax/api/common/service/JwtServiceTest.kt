package com.cleevio.cinemax.api.common.service

import com.cleevio.cinemax.api.common.constant.TokenType
import com.cleevio.cinemax.api.module.employee.constant.EmployeeRole
import io.jsonwebtoken.ExpiredJwtException
import io.jsonwebtoken.security.SignatureException
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.EnumSource
import kotlin.test.assertEquals
import kotlin.test.assertFalse
import kotlin.test.assertNotEquals
import kotlin.test.assertNotNull
import kotlin.test.assertTrue

class JwtServiceTest {

    private val underTest = JwtService(
        signingKey = TEST_SIGNING_KEY,
        accessExpiration = ACCESS_EXPIRATION,
        refreshExpiration = REFRESH_EXPIRATION
    )
    private val jwtServiceExpired = JwtService(
        signingKey = TEST_SIGNING_KEY,
        accessExpiration = 0,
        refreshExpiration = 0
    )
    private val jwtServiceInvalidSigningKey = JwtService(
        signingKey = INVALID_SIGNING_KEY,
        accessExpiration = ACCESS_EXPIRATION,
        refreshExpiration = REFRESH_EXPIRATION
    )

    @ParameterizedTest
    @EnumSource(TokenType::class)
    fun `test generateToken, should generate`(
        tokenType: TokenType,
    ) {
        val token = underTest.generateToken(PRINCIPAL, tokenType)
        assertNotNull(token)
    }

    @ParameterizedTest
    @EnumSource(TokenType::class)
    fun `test isTokenValid, should be valid`(
        tokenType: TokenType,
    ) {
        val token = underTest.generateToken(PRINCIPAL, tokenType)
        val isTokenValid = underTest.isTokenValid(token, PRINCIPAL, tokenType)
        assertTrue(isTokenValid)
    }

    @ParameterizedTest
    @EnumSource(TokenType::class)
    fun `test isTokenValid, invalid principal, shouldn't be valid`(
        tokenType: TokenType,
    ) {
        val token = underTest.generateToken(INVALID_PRINCIPAL, tokenType)
        val isTokenValid = underTest.isTokenValid(token, PRINCIPAL, tokenType)
        assertFalse(isTokenValid)
    }

    @ParameterizedTest
    @EnumSource(TokenType::class)
    fun `test isTokenValid, invalid signing key, should throw exception`(
        tokenType: TokenType,
    ) {
        val token = jwtServiceInvalidSigningKey.generateToken(PRINCIPAL, tokenType)
        assertThrows<SignatureException> { underTest.isTokenValid(token, PRINCIPAL, tokenType) }
    }

    @ParameterizedTest
    @EnumSource(TokenType::class)
    fun `test extractUserName, should be extracted correctly`(
        tokenType: TokenType,
    ) {
        val token = underTest.generateToken(PRINCIPAL, tokenType)
        val username = underTest.extractUsername(token)
        assertEquals(username, PRINCIPAL)
    }

    @ParameterizedTest
    @EnumSource(TokenType::class)
    fun `test extractUserName, invalid principal, extracted name shouldn't be valid`(
        tokenType: TokenType,
    ) {
        val token = underTest.generateToken(INVALID_PRINCIPAL, tokenType)
        val username = underTest.extractUsername(token)
        assertNotEquals(username, PRINCIPAL)
    }

    @ParameterizedTest
    @EnumSource(TokenType::class)
    fun `test extractRoles, should be extracted correctly`(
        tokenType: TokenType,
    ) {
        val token = underTest.generateToken(PRINCIPAL, tokenType, setOf(EmployeeRole.CASHIER, EmployeeRole.MANAGER))
        val roles = underTest.extractRoles(token)
        assertEquals(2, roles.size)
        assertTrue(roles.map { it.authority }.containsAll(setOf(EmployeeRole.CASHIER.name, EmployeeRole.MANAGER.name)))
    }

    @Test
    fun `test isTokenValid, refresh token instead of access, shouldn't be valid`() {
        val token = underTest.generateToken(PRINCIPAL, TokenType.ACCESS)
        val isTokenValid = underTest.isTokenValid(token, PRINCIPAL, TokenType.REFRESH)
        assertFalse(isTokenValid)
    }

    @Test
    fun `test isTokenValid, access token instead of refresh, shouldn't be valid`() {
        val token = underTest.generateToken(PRINCIPAL, TokenType.REFRESH)
        val isTokenValid = underTest.isTokenValid(token, PRINCIPAL, TokenType.ACCESS)
        assertFalse(isTokenValid)
    }

    @ParameterizedTest
    @EnumSource(TokenType::class)
    fun `test isTokenValid, token is expired, should throw exception`(
        tokenType: TokenType,
    ) {
        val token = jwtServiceExpired.generateToken(PRINCIPAL, tokenType)
        assertThrows<ExpiredJwtException> { jwtServiceExpired.isTokenValid(token, PRINCIPAL, tokenType) }
    }
}

private const val TEST_SIGNING_KEY = "cnFXWkNYYkdrek5pNUFwSHV5SzNXWFVpa2RVZUVwc0dERER3TVRaY1NRVTZabkh6eFpNb0xDVGt5Q2Y5UkpjeQ=="
private const val INVALID_SIGNING_KEY = "cnAyQXdDODZlOUdmNUtTckFuaWYyZXN1M2NCbkhSeXpYTThOTTlaeVZyUnRweHFUeWtobU00Slg1QUxqeGVxNQ=="
private const val ACCESS_EXPIRATION = 900000
private const val REFRESH_EXPIRATION = 172800000
private const val PRINCIPAL = "dummyUsername"
private const val INVALID_PRINCIPAL = "invalidDummyUsername"
