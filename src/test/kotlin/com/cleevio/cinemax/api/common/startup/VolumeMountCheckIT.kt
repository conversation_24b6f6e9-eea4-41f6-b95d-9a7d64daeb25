package com.cleevio.cinemax.api.common.startup

import com.cleevio.cinemax.api.IntegrationTest
import nl.altindag.log.LogCaptor
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import java.io.File
import kotlin.test.assertEquals
import kotlin.test.assertTrue

class VolumeMountCheckIT @Autowired constructor(
    private val underTest: PersistentVolumeMountCheck,
) : IntegrationTest() {

    private lateinit var logCaptor: LogCaptor

    @BeforeEach
    fun setUp() {
        logCaptor = LogCaptor.forClass(PersistentVolumeMountCheck::class.java)

        if (TEST_DIR.exists()) {
            TEST_DIR.deleteRecursively()
        }
        val created = TEST_DIR.mkdirs()
        assertTrue(created, "Could not create /tmp/pos-test directory at setUp")
    }

    @AfterEach
    fun tearDown() {
        logCaptor.close()
    }

    @Test
    fun `test checkMountedDirectory - directory does not exist - should log error`() {
        TEST_DIR.deleteRecursively()

        underTest.checkMountedDirectory()

        assertEquals(1, logCaptor.logs.size)
        assertTrue(logCaptor.errorLogs[0].contains("does not exist or is not accessible"))
    }

    @Test
    fun `test checkMountedDirectory - directory exists but no out xml files exist - should log error`() {
        underTest.checkMountedDirectory()

        assertEquals(1, logCaptor.logs.size)
        assertTrue(logCaptor.errorLogs[0].contains("No $XML_FILENAME files found"))
    }

    @Test
    fun `test checkMountedDirectory - directory exists but no out xml files exist - should log info if out xml found`() {
        setOf("receipts/pokl1", "pokl2").forEach {
            File(TEST_DIR, it)
                .apply { mkdirs() }
                .also { subDir -> File(subDir, XML_FILENAME).createNewFile() }
        }

        underTest.checkMountedDirectory()

        assertEquals(1, logCaptor.logs.size)
        assertTrue(logCaptor.infoLogs[0].contains("Found 2 $XML_FILENAME file(s)"))
    }
}

private val TEST_DIR = File("/tmp/pos-test")
private const val XML_FILENAME = "out.xml"
