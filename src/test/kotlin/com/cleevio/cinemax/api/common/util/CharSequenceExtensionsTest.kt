package com.cleevio.cinemax.api.common.util

import org.junit.jupiter.api.Test
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.CsvSource
import kotlin.test.assertEquals

class CharSequenceExtensionsTest {

    @ParameterizedTest
    @CsvSource(
        "Test 2, Test 2",
        "<PERSON>, <PERSON>",
        "long       whitespace, long whitespace"
    )
    fun `test replaceWhiteCharsWithSingleSpace`(input: String, expected: String) =
        assertEquals(expected, input.replaceWhiteCharsWithSingleSpace())

    @Test
    fun `test replaceWhiteCharsWithSingleSpace - special cases`() {
        val input1 = "tabs\tspace\teverywhere"
        val input2 = "my  \tname\t is"
        val input3 = "my\nsurname\nis"

        assertEquals("tabs space everywhere", input1.replaceWhiteCharsWithSingleSpace())
        assertEquals("my name is", input2.replaceWhiteCharsWithSingleSpace())
        assertEquals("my surname is", input3.replaceWhiteCharsWithSingleSpace())
    }
}
