package com.cleevio.cinemax.api.common.service

import com.cleevio.cinemax.api.DatabaseCleanup
import com.cleevio.cinemax.api.IntegrationTest
import com.cleevio.cinemax.api.module.distributor.entity.Distributor
import com.cleevio.cinemax.api.module.distributor.service.DistributorRepository
import com.cleevio.cinemax.api.module.movie.entity.Movie
import com.cleevio.cinemax.api.module.movie.service.MovieRepository
import com.cleevio.cinemax.api.module.product.entity.Product
import com.cleevio.cinemax.api.module.product.service.ProductRepository
import com.cleevio.cinemax.api.module.productcategory.entity.ProductCategory
import com.cleevio.cinemax.api.module.productcategory.service.ProductCategoryRepository
import com.cleevio.cinemax.api.module.productcomponent.entity.ProductComponent
import com.cleevio.cinemax.api.module.productcomponent.service.ProductComponentRepository
import com.cleevio.cinemax.api.module.productcomponentcategory.entity.ProductComponentCategory
import com.cleevio.cinemax.api.module.productcomponentcategory.service.ProductComponentCategoryRepository
import com.cleevio.cinemax.api.module.supplier.entity.Supplier
import com.cleevio.cinemax.api.module.supplier.service.SupplierRepository
import com.cleevio.cinemax.api.module.ticketdiscount.entity.TicketDiscount
import com.cleevio.cinemax.api.module.ticketdiscount.service.TicketDiscountRepository
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import kotlin.test.assertTrue

class CodeGeneratorIT @Autowired constructor(
    private val distributorRepository: DistributorRepository,
    private val productComponentRepository: ProductComponentRepository,
    private val productComponentCategoryRepository: ProductComponentCategoryRepository,
    private val productCategoryRepository: ProductCategoryRepository,
    private val supplierRepository: SupplierRepository,
    private val productRepository: ProductRepository,
    private val ticketDiscountRepository: TicketDiscountRepository,
    private val movieRepository: MovieRepository,
) : IntegrationTest(DatabaseCleanup.AFTER_EACH) {

    private val underTest = CodeGenerator()

    @Test
    fun `test generateCode - should generate correctly`() {
        assertTrue(
            underTest.generateCode(
                clazz = Distributor::class.java,
                checkIfExistsFunction = distributorRepository::existsByCodeAndDeletedAtIsNull
            ).matches(Regex("[0-9A-Z]{2}"))
        )
        assertTrue(
            underTest.generateCode(
                clazz = ProductComponent::class.java,
                checkIfExistsFunction = productComponentRepository::existsByCodeAndDeletedAtIsNull
            ).matches(Regex("[0-9]{5}"))
        )
        assertTrue(
            underTest.generateCode(
                clazz = ProductComponentCategory::class.java,
                checkIfExistsFunction = productComponentCategoryRepository::existsByCodeAndDeletedAtIsNull
            ).matches(Regex("[0-9]{2}"))
        )
        assertTrue(
            underTest.generateCode(
                clazz = ProductCategory::class.java,
                checkIfExistsFunction = productCategoryRepository::existsByCode
            ).matches(Regex("[0-9]{2}"))
        )
        assertTrue(
            underTest.generateCode(
                clazz = Supplier::class.java,
                checkIfExistsFunction = supplierRepository::existsByCodeAndDeletedAtIsNull
            ).matches(Regex("[0-9]{2}"))
        )
        assertTrue(
            underTest.generateCode(
                clazz = Product::class.java,
                checkIfExistsFunction = productRepository::existsByCodeAndDeletedAtIsNull
            ).matches(Regex("[0-9]{5}"))
        )
        assertTrue(
            underTest.generateCode(
                clazz = TicketDiscount::class.java,
                checkIfExistsFunction = ticketDiscountRepository::existsByCodeAndDeletedAtIsNull
            ).matches(Regex("[0-9A-Z]{2}"))
        )
        assertTrue(
            underTest.generateCode(
                clazz = Movie::class.java,
                checkIfExistsFunction = movieRepository::existsByCodeAndDeletedAtIsNull
            ).matches(Regex("8[0-9A-Z][0-9]{4}"))
        )
    }
}
