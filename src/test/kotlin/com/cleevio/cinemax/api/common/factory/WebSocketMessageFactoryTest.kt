package com.cleevio.cinemax.api.common.factory

import com.cleevio.cinemax.api.common.constant.WebSocketMessageDataKey
import com.cleevio.cinemax.api.common.constant.WebSocketMessageType
import com.cleevio.cinemax.api.module.product.service.ProductJooqFinderService
import com.cleevio.cinemax.api.module.product.service.model.ProductStockQuantityModel
import com.cleevio.cinemax.api.module.productcomposition.service.ProductCompositionJooqFinderService
import com.cleevio.cinemax.api.module.reservation.constant.ReservationState
import com.cleevio.cinemax.api.module.reservation.service.ReservationJooqFinderService
import com.cleevio.cinemax.api.module.reservation.service.model.ReservationWebSocketModel
import io.mockk.every
import io.mockk.mockk
import io.mockk.verifySequence
import org.junit.jupiter.api.Test
import java.math.BigDecimal
import java.util.UUID
import kotlin.test.assertEquals
import kotlin.test.assertTrue

class WebSocketMessageFactoryTest {

    private val reservationJooqFinderService = mockk<ReservationJooqFinderService>()
    private val productFinderService = mockk<ProductJooqFinderService>()
    private val productCompositionJooqFinderService = mockk<ProductCompositionJooqFinderService>()

    private val webSocketMessageFactory = WebSocketMessageFactory(
        reservationJooqFinderService,
        productFinderService,
        productCompositionJooqFinderService
    )

    @Test
    fun `test mapToReservationWebSocketMessage - reservation created - should return reservation created message`() {
        every { reservationJooqFinderService.getWebSocketModelById(any()) } returns RESERVATION_CREATED_MODEL_1

        val message = webSocketMessageFactory.mapToReservationWebSocketMessage(
            reservationId = RESERVATION_ID,
            messageType = WebSocketMessageType.RESERVATION_CREATED
        )

        verifySequence {
            reservationJooqFinderService.getWebSocketModelById(RESERVATION_ID)
        }

        assertEquals(WebSocketMessageType.RESERVATION_CREATED, message.type)
        assertEquals(1, message.data.entries.size)
        assertTrue(message.data.keys.contains(WebSocketMessageDataKey.RESERVATION))
        assertTrue(message.data.values.contains(RESERVATION_CREATED_MODEL_1))
    }

    @Test
    fun `test mapToReservationWebSocketMessage - reservation confirmed - should return reservation confirmed message`() {
        every { reservationJooqFinderService.getWebSocketModelById(any()) } returns RESERVATION_CONFIRMED_MODEL_1

        val message = webSocketMessageFactory.mapToReservationWebSocketMessage(
            reservationId = RESERVATION_ID,
            messageType = WebSocketMessageType.RESERVATION_CONFIRMED
        )

        verifySequence {
            reservationJooqFinderService.getWebSocketModelById(RESERVATION_ID)
        }

        assertEquals(WebSocketMessageType.RESERVATION_CONFIRMED, message.type)
        assertEquals(1, message.data.entries.size)
        assertTrue(message.data.keys.contains(WebSocketMessageDataKey.RESERVATION))
        assertTrue(message.data.values.contains(RESERVATION_CONFIRMED_MODEL_1))
    }

    @Test
    fun `test mapToReservationWebSocketMessage - reservation deleted - should return reservation deleted message`() {
        every { reservationJooqFinderService.getWebSocketModelById(any()) } returns RESERVATION_DELETED_MODEL_1

        val message = webSocketMessageFactory.mapToReservationWebSocketMessage(
            reservationId = RESERVATION_ID,
            messageType = WebSocketMessageType.RESERVATION_DELETED
        )

        verifySequence {
            reservationJooqFinderService.getWebSocketModelById(RESERVATION_ID)
        }

        assertEquals(WebSocketMessageType.RESERVATION_DELETED, message.type)
        assertEquals(1, message.data.entries.size)
        assertTrue(message.data.keys.contains(WebSocketMessageDataKey.RESERVATION))
        assertTrue(message.data.values.contains(RESERVATION_DELETED_MODEL_1))
    }

    @Test
    fun `test mapToProductWebSocketMessage - two products - should return reservation deleted message`() {
        every {
            productCompositionJooqFinderService.findAllProductIdsSharingSameProductComponents(any())
        } returns RELATED_PRODUCT_IDS.toSet()
        every { productFinderService.findAllNonDeletedActiveProductStockQuantitiesByIdIn(any()) } returns mapOf(
            RELATED_PRODUCT_IDS[0] to BigDecimal.valueOf(750),
            RELATED_PRODUCT_IDS[1] to BigDecimal.valueOf(1500),
            RELATED_PRODUCT_IDS[2] to BigDecimal.valueOf(255)
        )

        val message = webSocketMessageFactory.mapToProductWebSocketMessage(
            productId = PRODUCT_ID
        )

        verifySequence {
            productCompositionJooqFinderService.findAllProductIdsSharingSameProductComponents(PRODUCT_ID)
            productFinderService.findAllNonDeletedActiveProductStockQuantitiesByIdIn(RELATED_PRODUCT_IDS.toSet())
        }

        assertEquals(WebSocketMessageType.PRODUCT_STOCK_QUANTITY_CHANGED, message.type)
        assertEquals(1, message.data.entries.size)
        assertTrue(message.data.keys.contains(WebSocketMessageDataKey.PRODUCTS))
        assertTrue(message.data.values.contains(PRODUCTS_STOCK_QUANTITY_MODEL))
    }
}

private val SCREENING_ID = UUID.randomUUID()
private val SEAT_ID = UUID.randomUUID()
private val RESERVATION_ID = UUID.randomUUID()
private val PRODUCT_ID = UUID.randomUUID()
private val RESERVATION_CREATED_MODEL_1 = ReservationWebSocketModel(
    state = ReservationState.RESERVED,
    screeningId = SCREENING_ID,
    seatId = SEAT_ID
)
private val RESERVATION_CONFIRMED_MODEL_1 = RESERVATION_CREATED_MODEL_1.copy(state = ReservationState.UNAVAILABLE)
private val RESERVATION_DELETED_MODEL_1 = RESERVATION_CREATED_MODEL_1.copy(state = ReservationState.DELETED)
private val RELATED_PRODUCT_IDS = listOf(UUID.randomUUID(), UUID.randomUUID(), UUID.randomUUID())
private val PRODUCTS_STOCK_QUANTITY_MODEL = listOf(
    ProductStockQuantityModel(RELATED_PRODUCT_IDS[0], 750),
    ProductStockQuantityModel(RELATED_PRODUCT_IDS[1], 1500),
    ProductStockQuantityModel(RELATED_PRODUCT_IDS[2], 255)
)
