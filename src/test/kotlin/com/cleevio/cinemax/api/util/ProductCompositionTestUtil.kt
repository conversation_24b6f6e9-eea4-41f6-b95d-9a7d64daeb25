package com.cleevio.cinemax.api.util

import com.cleevio.cinemax.api.common.entity.DeletableEntity
import com.cleevio.cinemax.api.common.util.isEqualTo
import com.cleevio.cinemax.api.module.product.entity.Product
import com.cleevio.cinemax.api.module.productcomponent.entity.ProductComponent
import com.cleevio.cinemax.api.module.productcomposition.entity.ProductComposition
import com.cleevio.cinemax.api.module.productcomposition.service.command.CreateOrUpdateProductCompositionCommand
import com.cleevio.cinemax.api.module.productcomposition.service.command.DeleteAndCreateProductCompositionsCommand
import com.cleevio.cinemax.mssql.rp_bufete.dbo.tables.pojos.Rpolm
import org.junit.jupiter.api.Assertions.assertTrue
import java.math.BigDecimal
import java.util.UUID
import kotlin.math.abs
import kotlin.test.assertEquals
import kotlin.test.assertNotNull
import kotlin.test.assertNull

fun mapToCreateOrUpdateProductCompositionCommand(composition: ProductComposition) =
    CreateOrUpdateProductCompositionCommand(
        id = composition.id,
        originalId = composition.originalId!!,
        productId = composition.productId,
        productInProductId = composition.productInProductId,
        productComponentId = composition.productComponentId,
        amount = composition.amount,
        productInProductPrice = composition.productInProductPrice,
        productInProductFlagshipPrice = composition.productInProductFlagshipPrice
    )

fun createProductComposition(
    originalId: Int? = 1,
    productId: UUID,
    productComponentId: UUID? = null,
    amount: BigDecimal = 10.toBigDecimal(),
    productInProductId: UUID? = null,
    productInProductPrice: BigDecimal? = null,
    productInProductFlagshipPrice: BigDecimal? = null,
) = ProductComposition(
    id = UUID.randomUUID(),
    originalId = originalId,
    productId = productId,
    productComponentId = productComponentId,
    amount = amount,
    productInProductId = productInProductId,
    productInProductPrice = productInProductPrice,
    productInProductFlagshipPrice = productInProductFlagshipPrice
)

fun assertProductCompositionEquals(expected: ProductComposition, actual: ProductComposition) {
    assertEquals(expected.originalId, actual.originalId)
    assertEquals(expected.productId, actual.productId)
    assertEquals(expected.productInProductId, actual.productInProductId)
    assertEquals(expected.productComponentId, actual.productComponentId)
    assertTrue(expected.amount isEqualTo actual.amount)
    assertEquals(expected.productInProductPrice, actual.productInProductPrice)
    assertEquals(expected.productInProductFlagshipPrice, actual.productInProductFlagshipPrice)
    assertNotNull(actual.createdAt)
    assertNotNull(actual.updatedAt)
}

fun assertProductCompositionAndDeleteAndCreateProductCompositionsCommand(
    expected: DeleteAndCreateProductCompositionsCommand,
    actualProductCompositions: List<ProductComposition>,
) {
    assertEquals(expected.productComposition.size, actualProductCompositions.size)
    expected.productComposition.forEach { expectedComposition ->
        actualProductCompositions.first {
            it.productComponentId == expectedComposition.productComponentId &&
                it.productInProductId == expectedComposition.productInProductId &&
                it.productId == expected.productId
        }.let {
            assertNotNull(it.id)
            assertNull(it.originalId)
            assertEquals(expectedComposition.quantity, it.amount)
            assertEquals(expectedComposition.productInProductPrice, it.productInProductPrice)
            assertEquals(expectedComposition.productInProductFlagshipPrice, it.productInProductFlagshipPrice)
        }
    }
}

fun assertProductCompositionToMssqlProductComposition(
    expectedProductCompositionPair: Pair<ProductComposition, DeletableEntity>,
    actualProductComposition: Rpolm,
    expectedProduct: Product,
    expectedProductOriginalId: Int,
) {
    assertEquals(expectedProductOriginalId, actualProductComposition.rmenuid)
    assertEquals(expectedProduct.code, actualProductComposition.menu.trim())

    when (expectedProductCompositionPair.second) {
        is ProductComponent -> {
            assertEquals((expectedProductCompositionPair.second as ProductComponent).originalId, actualProductComposition.rzboziid)
            assertEquals((expectedProductCompositionPair.second as ProductComponent).code, actualProductComposition.zbozi.trim())
            assertEquals(0, actualProductComposition.rmenu2id)
        }
        is Product -> {
            val pip = expectedProductCompositionPair.second as Product
            if (pip.isPackagingDeposit) {
                assertEquals(0, actualProductComposition.rzboziid)
                assertEquals("", actualProductComposition.zbozi.trim())
                assertEquals(0, actualProductComposition.rmenu2id)
                assertEquals(pip.originalId?.let { abs(it) }, actualProductComposition.rzbozirid)
            } else {
                assertEquals(0, actualProductComposition.rzboziid)
                assertEquals("", actualProductComposition.zbozi.trim())
                assertEquals(pip.originalId, actualProductComposition.rmenu2id)
                assertEquals(0, actualProductComposition.rzbozirid)
            }
        }
        else -> throw IllegalArgumentException()
    }

    assertTrue(expectedProductCompositionPair.first.amount isEqualTo actualProductComposition.mnozs.toBigDecimal())
    assertEquals(expectedProduct.updatedBy, actualProductComposition.zuziv.trim())
    assertNotNull(actualProductComposition.zcas)
}
