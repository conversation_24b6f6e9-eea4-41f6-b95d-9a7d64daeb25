package com.cleevio.cinemax.api.util

import com.cleevio.cinemax.api.module.reservation.constant.ReservationState
import com.cleevio.cinemax.api.module.reservation.controller.dto.ReservationSearchResponse
import com.cleevio.cinemax.api.module.seat.constant.DoubleSeatType
import com.cleevio.cinemax.api.module.seat.constant.SeatObjectType
import com.cleevio.cinemax.api.module.seat.constant.SeatType
import com.cleevio.cinemax.api.module.seat.controller.dto.SeatSearchResponse
import com.cleevio.cinemax.api.module.seat.entity.Seat
import com.cleevio.cinemax.api.module.seat.service.command.CreateOrUpdateSeatCommand
import java.util.UUID
import kotlin.test.assertEquals

fun mapToCreateOrUpdateSeatCommand(seat: Seat) =
    CreateOrUpdateSeatCommand(
        id = seat.id,
        originalId = seat.originalId,
        auditoriumLayoutId = seat.auditoriumLayoutId,
        auditoriumId = seat.auditoriumId,
        type = seat.type,
        objectType = seat.objectType,
        row = seat.row,
        number = seat.number,
        positionLeft = seat.positionLeft,
        positionTop = seat.positionTop,
        webPositionTop = seat.webPositionTop,
        webPositionLeft = seat.webPositionLeft
    )

fun createSeat(
    id: UUID = UUID.randomUUID(),
    originalId: Int? = 1,
    auditoriumLayoutId: UUID,
    auditoriumId: UUID,
    type: SeatType = SeatType.VIP,
    objectType: SeatObjectType = SeatObjectType.SEAT,
    doubleSeatType: DoubleSeatType? = null,
    row: String = "1",
    number: String = "1",
    positionLeft: Int = (0..100).random(),
    positionTop: Int = (0..100).random(),
    defaultReservationState: ReservationState? = null,
) = Seat(
    id = id,
    originalId = originalId,
    auditoriumLayoutId = auditoriumLayoutId,
    auditoriumId = auditoriumId,
    type = type,
    objectType = objectType,
    doubleSeatType = doubleSeatType,
    row = row,
    number = number,
    positionLeft = positionLeft,
    positionTop = positionTop,
    webPositionLeft = positionLeft,
    webPositionTop = positionTop,
    defaultReservationState = defaultReservationState
)

fun mapToSeatSearchResponse(
    seat: Seat,
    reservationResponse: ReservationSearchResponse,
) = SeatSearchResponse(
    id = seat.id,
    type = seat.type,
    doubleSeatType = seat.doubleSeatType,
    row = seat.row,
    number = seat.number,
    positionLeft = seat.positionLeft,
    positionTop = seat.positionTop,
    reservation = reservationResponse
)

fun assertSeatEquals(
    expected: Seat,
    actual: SeatSearchResponse,
) {
    assertEquals(expected.id, actual.id)
    assertEquals(expected.type, actual.type)
    assertEquals(expected.doubleSeatType, actual.doubleSeatType)
    assertEquals(expected.row, actual.row)
    assertEquals(expected.number, actual.number)
    assertEquals(expected.positionLeft, actual.positionLeft)
    assertEquals(expected.positionTop, actual.positionTop)
}
