package com.cleevio.cinemax.api.util

import com.cleevio.cinemax.api.module.pricecategoryitem.constant.PriceCategoryItemNumber
import com.cleevio.cinemax.api.module.ticket.entity.Ticket
import com.cleevio.cinemax.api.module.ticket.service.command.CreateTicketCommand
import java.util.UUID

fun mapToCreateTicketCommand(
    ticket: Ticket,
    seatId: UUID,
    priceCategoryItemNumber: PriceCategoryItemNumber,
    freeTicket: Boolean = false,
    includes3dGlasses: Boolean = false,
    isGroupTicket: Boolean = false,
) = CreateTicketCommand(
    screeningId = ticket.screeningId,
    seatId = seatId,
    priceCategoryItemNumber = priceCategoryItemNumber,
    ticketDiscountPrimaryId = ticket.ticketDiscountPrimaryId,
    ticketDiscountSecondaryId = ticket.ticketDiscountSecondaryId,
    freeTicket = freeTicket,
    includes3dGlasses = includes3dGlasses,
    isGroupTicket = isGroupTicket
)

fun createTicket(
    id: UUID = UUID.randomUUID(),
    originalId: Int? = null,
    screeningId: UUID,
    reservationId: UUID,
    ticketPriceId: UUID,
    ticketDiscountPrimaryId: UUID? = null,
    ticketDiscountSecondaryId: UUID? = null,
    receiptNumber: String? = null,
    isGroupTicket: Boolean = false,
    isUsed: Boolean = false,
    includes3dGlasses: Boolean = false,
): Ticket = Ticket(
    id = id,
    originalId = originalId,
    screeningId = screeningId,
    reservationId = reservationId,
    ticketPriceId = ticketPriceId,
    ticketDiscountPrimaryId = ticketDiscountPrimaryId,
    ticketDiscountSecondaryId = ticketDiscountSecondaryId,
    receiptNumber = receiptNumber,
    isGroupTicket = isGroupTicket,
    isUsed = isUsed,
    includes3dGlasses = includes3dGlasses
)
