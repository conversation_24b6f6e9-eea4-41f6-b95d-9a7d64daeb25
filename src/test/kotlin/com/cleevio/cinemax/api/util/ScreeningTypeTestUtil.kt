package com.cleevio.cinemax.api.util

import com.cleevio.cinemax.api.module.screeningtype.entity.ScreeningType
import com.cleevio.cinemax.api.module.screeningtype.service.command.CreateOrUpdateScreeningTypeCommand
import java.util.UUID
import kotlin.test.assertEquals

fun mapToCreateOrUpdateScreeningTypeCommand(screeningType: ScreeningType) = CreateOrUpdateScreeningTypeCommand(
    id = screeningType.id,
    originalId = screeningType.originalId,
    code = screeningType.code,
    title = screeningType.title
)

fun createScreeningType(
    id: UUID = UUID.randomUUID(),
    originalId: Int? = 1,
    code: String = "0${originalId ?: 0}",
    title: String = "Festival",
    entityModifier: (ScreeningType) -> Unit = {},
) = ScreeningType(
    id = id,
    originalId = originalId,
    code = code,
    title = title
).apply(entityModifier)

fun assertCommandToScreeningTypeMapping(expected: CreateOrUpdateScreeningTypeCommand, actual: ScreeningType) {
    assertEquals(expected.originalId, actual.originalId)
    assertEquals(expected.code, actual.code)
    assertEquals(expected.title, actual.title)
}
