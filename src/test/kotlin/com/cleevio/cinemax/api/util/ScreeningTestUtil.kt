package com.cleevio.cinemax.api.util

import com.cleevio.cinemax.api.INTEGRATION_TEST_DATE_TIME
import com.cleevio.cinemax.api.common.constant.ANONYMOUS_USERNAME
import com.cleevio.cinemax.api.common.util.isEqualTo
import com.cleevio.cinemax.api.common.util.isGreaterThan
import com.cleevio.cinemax.api.common.util.trimIfNotBlank
import com.cleevio.cinemax.api.module.auditorium.controller.dto.AuditoriumSearchResponse
import com.cleevio.cinemax.api.module.auditorium.entity.Auditorium
import com.cleevio.cinemax.api.module.groupreservation.controller.dto.AuditoriumResponse
import com.cleevio.cinemax.api.module.groupreservation.controller.dto.MovieResponse
import com.cleevio.cinemax.api.module.groupreservation.controller.dto.ReservationResponse
import com.cleevio.cinemax.api.module.groupreservation.controller.dto.ScreeningResponse
import com.cleevio.cinemax.api.module.movie.controller.dto.MovieSearchResponse
import com.cleevio.cinemax.api.module.pricecategory.controller.dto.PriceCategorySearchResponse
import com.cleevio.cinemax.api.module.pricecategory.entity.PriceCategory
import com.cleevio.cinemax.api.module.screening.constant.ScreeningState
import com.cleevio.cinemax.api.module.screening.controller.dto.ScreeningSearchResponse
import com.cleevio.cinemax.api.module.screening.entity.Screening
import com.cleevio.cinemax.api.module.screening.service.command.CreateOrUpdateScreeningCommand
import com.cleevio.cinemax.api.module.screeningfee.entity.ScreeningFee
import com.cleevio.cinemax.api.module.screeningtype.entity.ScreeningType
import com.cleevio.cinemax.api.module.ticketprice.entity.TicketPrice
import com.cleevio.cinemax.mssql.rps_cinemaxe.dbo.tables.pojos.Rprog
import java.math.BigDecimal
import java.time.DayOfWeek
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.LocalTime
import java.time.temporal.ChronoUnit
import java.util.UUID
import kotlin.test.assertEquals
import kotlin.test.assertFalse
import kotlin.test.assertNotNull
import kotlin.test.assertTrue

fun mapToCreateOrUpdateScreeningCommand(
    screening: Screening,
    screeningFee: ScreeningFee? = null,
    screeningTypeIds: Set<UUID>? = null,
    movieId: UUID? = null,
) = CreateOrUpdateScreeningCommand(
    id = screening.id,
    originalId = screening.originalId,
    auditoriumId = screening.auditoriumId,
    auditoriumLayoutId = screening.auditoriumLayoutId,
    movieId = movieId ?: screening.movieId,
    priceCategoryId = screening.priceCategoryId,
    date = screening.date,
    time = screening.time,
    saleTimeLimit = screening.saleTimeLimit,
    stopped = screening.stopped,
    cancelled = screening.cancelled,
    proCommission = screening.proCommission,
    filmFondCommission = screening.filmFondCommission,
    distributorCommission = screening.distributorCommission,
    publishOnline = screening.publishOnline,
    state = screening.state,
    screeningTypeIds = screeningTypeIds,
    adTimeSlot = screening.adTimeSlot,
    surchargeVip = screeningFee?.surchargeVip ?: BigDecimal.ZERO,
    surchargePremium = screeningFee?.surchargePremium ?: BigDecimal.ZERO,
    surchargeImax = screeningFee?.surchargeImax ?: BigDecimal.ZERO,
    surchargeUltraX = screeningFee?.surchargeUltraX ?: BigDecimal.ZERO,
    serviceFeeVip = screeningFee?.serviceFeeVip ?: BigDecimal.ZERO,
    serviceFeePremium = screeningFee?.serviceFeePremium ?: BigDecimal.ZERO,
    serviceFeeImax = screeningFee?.serviceFeeImax ?: BigDecimal.ZERO,
    serviceFeeUltraX = screeningFee?.serviceFeeUltraX ?: BigDecimal.ZERO,
    surchargeDBox = screeningFee?.surchargeDBox ?: BigDecimal.ZERO,
    serviceFeeGeneral = screeningFee?.serviceFeeGeneral ?: BigDecimal.ZERO
)

fun createScreening(
    id: UUID = UUID.randomUUID(),
    originalId: Int? = 1,
    auditoriumId: UUID,
    auditoriumLayoutId: UUID = UUID.randomUUID(),
    movieId: UUID,
    priceCategoryId: UUID = UUID.randomUUID(),
    date: LocalDate = INTEGRATION_TEST_DATE_TIME.toLocalDate(),
    time: LocalTime = INTEGRATION_TEST_DATE_TIME.toLocalTime(),
    saleTimeLimit: Int = 30,
    stopped: Boolean = false,
    cancelled: Boolean = false,
    proCommission: Int = 3,
    filmFondCommission: Int = 6,
    distributorCommission: Int = 70,
    publishOnline: Boolean = true,
    state: ScreeningState = ScreeningState.PUBLISHED,
    adTimeSlot: Int = 15,
    entityModifier: (Screening) -> Unit = {},
) = Screening(
    id = id,
    originalId = originalId,
    auditoriumId = auditoriumId,
    auditoriumLayoutId = auditoriumLayoutId,
    movieId = movieId,
    priceCategoryId = priceCategoryId,
    date = date,
    time = time,
    saleTimeLimit = saleTimeLimit,
    stopped = stopped,
    cancelled = cancelled,
    proCommission = proCommission,
    filmFondCommission = filmFondCommission,
    distributorCommission = distributorCommission,
    publishOnline = publishOnline,
    state = state,
    adTimeSlot = adTimeSlot
).apply(entityModifier)

fun mapToScreeningResponse(
    screening: Screening,
    movieResponse: MovieSearchResponse,
    priceCategoryResponse: PriceCategorySearchResponse,
    auditoriumResponse: AuditoriumSearchResponse,
) = ScreeningSearchResponse(
    id = screening.id,
    date = screening.date,
    time = screening.time,
    saleTimeLimit = minutesToLocalTime(screening.saleTimeLimit),
    saleTimeLimitWithDate = screening.getScreeningTime().plus(screening.saleTimeLimit.toLong(), ChronoUnit.MINUTES),
    movie = movieResponse,
    priceCategory = priceCategoryResponse,
    auditorium = auditoriumResponse
)

fun assertScreeningEquals(expected: Screening, actual: ScreeningSearchResponse) {
    assertEquals(expected.id, actual.id)
    assertEquals(expected.date, actual.date)
    assertEquals(expected.time, actual.time)
    assertEquals(minutesToLocalTime(expected.saleTimeLimit), actual.saleTimeLimit)
    assertEquals(
        expected.getScreeningTime().plus(expected.saleTimeLimit.toLong(), ChronoUnit.MINUTES),
        actual.saleTimeLimitWithDate
    )
}

fun assertScreeningEquals(expected: Screening, actual: Screening) {
    assertEquals(expected.auditoriumId, actual.auditoriumId)
    assertEquals(expected.auditoriumLayoutId, actual.auditoriumLayoutId)
    assertEquals(expected.priceCategoryId, actual.priceCategoryId)
    assertEquals(expected.movieId, actual.movieId)
    assertEquals(expected.date, actual.date)
    assertEquals(expected.time.withNano(0), actual.time.withNano(0))
    assertEquals(expected.saleTimeLimit, actual.saleTimeLimit)
    assertEquals(expected.stopped, actual.stopped)
    assertEquals(expected.cancelled, actual.cancelled)
    assertNotNull(actual.createdAt)
    assertNotNull(actual.updatedAt)
    assertEquals(expected.proCommission, actual.proCommission)
    assertEquals(expected.filmFondCommission, actual.filmFondCommission)
    assertEquals(expected.distributorCommission, actual.distributorCommission)
    assertEquals(expected.publishOnline, actual.publishOnline)
}

fun assertScreeningToMssqlScreeningMapping(
    expected: Screening,
    expectedAuditoriumLayoutOriginalId: Int,
    expectedMovieOriginalId: Int,
    expectedDistributorOriginalCode: String? = null,
    expectedPriceCategory: PriceCategory,
    expectedAuditorium: Auditorium,
    expectedScreeningFee: ScreeningFee,
    expectedScreeningTypes: List<ScreeningType>? = null,
    expectedTicketPrices: List<TicketPrice>,
    expectedTechnologyCode: String? = null,
    expectedLanguageCode: String? = null,
    actual: Rprog,
) {
    assertEquals(expectedAuditoriumLayoutOriginalId, actual.rupravaid)
    assertEquals(expectedMovieOriginalId, actual.rfilmid)
    assertEquals(expectedPriceCategory.originalId, actual.rvstid)
    assertEquals(expectedAuditorium.originalCode.toString(), actual.csalu.trim())
    assertEquals(LocalDateTime.of(expected.date, expected.time), actual.porad)
    assertEquals(expectedDistributorOriginalCode, actual.distr.trim())
    assertEquals(expectedAuditorium.capacity.toShort(), actual.limit)
    assertTrue(expected.distributorCommission.toBigDecimal() isEqualTo actual.pujcovne)
    assertEquals(expectedPriceCategory.originalCode, actual.cenkat.trim())
    assertTrue(expected.proCommission.toBigDecimal() isEqualTo actual.osa)
    assertEquals(expected.proCommission > 0, actual.osaproc)
    assertTrue(expected.filmFondCommission.toBigDecimal() isEqualTo actual.fk)
    assertEquals(expected.filmFondCommission > 0, actual.fkproc)

    assertTrue(expectedScreeningFee.serviceFeeGeneral isEqualTo actual.priplsluz)
    if (expectedScreeningFee.serviceFeeGeneral isGreaterThan 0.toBigDecimal()) {
        assertTrue(actual.ps)
    } else {
        assertFalse(actual.ps)
    }

    assertEquals(expectedScreeningFee.serviceFeeGeneral isGreaterThan 0.toBigDecimal(), actual.ps)
    assertEquals(expected.publishOnline, actual.naweb)
    assertEquals(expected.stopped, actual.stop)
    assertEquals(expected.cancelled, actual.odpadlo)
    // assertEquals(expectedTicketPrices.size.toShort(), actual.divaku)
    // assertTrue(expectedTicketPrices.sumOf { it.totalPrice } isEqualTo actual.trzba)
    assertNotNull(actual.zcas)
    assertEquals(expected.saleTimeLimit.toShort(), actual.casprod)
    if (actual.zuziv.isBlank()) {
        assertEquals(expected.updatedBy, ANONYMOUS_USERNAME)
    } else {
        assertEquals(expected.updatedBy, actual.zuziv)
    }

    assertEquals(mapExpectedScreeningTypeCode(0, expectedScreeningTypes, "00"), actual.cistyp.trim())
    assertEquals(mapExpectedScreeningTypeCode(1, expectedScreeningTypes), actual.cistyp2.trim())
    assertEquals(mapExpectedScreeningTypeCode(2, expectedScreeningTypes), actual.cistyp3.trim())
    assertEquals(mapExpectedScreeningTypeCode(3, expectedScreeningTypes), actual.cistyp4.trim())

    assertTrue(expectedScreeningFee.surchargeVip isEqualTo actual.priplatekvip)
    assertTrue(expectedScreeningFee.surchargePremium isEqualTo actual.priplatekpremium)
    assertTrue(expectedScreeningFee.surchargeImax isEqualTo actual.priplatekimax)
    assertTrue(expectedScreeningFee.surchargeUltraX isEqualTo actual.priplatekultrax)
    assertTrue(expectedScreeningFee.serviceFeeVip isEqualTo actual.sluzbyvip)
    assertTrue(expectedScreeningFee.serviceFeeImax isEqualTo actual.sluzbyimax)
    assertTrue(expectedScreeningFee.serviceFeeUltraX isEqualTo actual.sluzbyultrax)
    assertTrue(expectedScreeningFee.surchargeDBox isEqualTo actual.sluzbydbox)

    assertEquals(expectedLanguageCode, actual.jazyk.trimIfNotBlank())
    assertEquals(expectedTechnologyCode, actual.cfotrm.trimIfNotBlank())
}

fun mapToAdminSearchGroupReservationScreeningResponse(
    screening: Screening,
    auditoriumResponse: AuditoriumResponse,
    movieResponse: MovieResponse,
    reservationResponses: List<ReservationResponse>,
) = ScreeningResponse(
    id = screening.id,
    date = screening.date,
    time = screening.time,
    auditorium = auditoriumResponse,
    movie = movieResponse,
    reservations = reservationResponses
)

private fun mapExpectedScreeningTypeCode(
    index: Int,
    expectedScreeningTypes: List<ScreeningType>?,
    default: String = "",
): String {
    return expectedScreeningTypes?.let {
        if (expectedScreeningTypes.size > index) expectedScreeningTypes[index].code else default
    } ?: default
}

val minutesToLocalTime: (minutes: Int) -> LocalTime = { minutes: Int -> LocalTime.of((minutes / 60) % 24, minutes % 60) }

fun getNextDateForDay(dayOfWeek: DayOfWeek = DayOfWeek.SUNDAY): LocalDate {
    return generateSequence(LocalDate.now().plusDays(1)) { it.plusDays(1) }.first { it.dayOfWeek == dayOfWeek }
}
