package com.cleevio.cinemax.api.util

import com.cleevio.cinemax.api.common.util.isEqualTo
import com.cleevio.cinemax.api.module.basketitem.controller.dto.BasketItemTicketPriceResponse
import com.cleevio.cinemax.api.module.pricecategoryitem.constant.PriceCategoryItemNumber
import com.cleevio.cinemax.api.module.ticketprice.constant.AuditoriumServiceFeeType
import com.cleevio.cinemax.api.module.ticketprice.constant.AuditoriumSurchargeType
import com.cleevio.cinemax.api.module.ticketprice.constant.SeatServiceFeeType
import com.cleevio.cinemax.api.module.ticketprice.constant.SeatSurchargeType
import com.cleevio.cinemax.api.module.ticketprice.entity.TicketPrice
import java.math.BigDecimal
import java.util.UUID
import kotlin.test.assertEquals
import kotlin.test.assertNull
import kotlin.test.assertTrue

fun assertTicketPriceEquals(expected: TicketPrice, actual: TicketPrice) {
    assertEquals(expected.screeningId, actual.screeningId)
    assertEquals(expected.seatId, actual.seatId)
    assertTrue(expected.basePrice isEqualTo actual.basePrice)
    assertEquals(expected.basePriceItemNumber, actual.basePriceItemNumber)
    assertTrue(expected.basePriceBeforeDiscount isEqualTo actual.basePriceBeforeDiscount)

    if (expected.seatSurcharge == null) {
        assertNull(actual.seatSurcharge)
    } else {
        assertTrue(actual.seatSurcharge isEqualTo expected.seatSurcharge!!)
    }
    assertEquals(expected.seatSurchargeType, actual.seatSurchargeType)

    if (expected.auditoriumSurcharge == null) {
        assertNull(actual.auditoriumSurcharge)
    } else {
        assertTrue(actual.auditoriumSurcharge isEqualTo expected.auditoriumSurcharge!!)
    }

    if (expected.seatServiceFee == null) {
        assertNull(actual.seatServiceFee)
    } else {
        assertTrue(actual.seatServiceFee isEqualTo expected.seatServiceFee!!)
    }
    assertEquals(expected.seatServiceFeeType, actual.seatServiceFeeType)

    if (expected.auditoriumServiceFee == null) {
        assertNull(actual.auditoriumServiceFee)
    } else {
        assertTrue(actual.auditoriumServiceFee isEqualTo expected.auditoriumServiceFee!!)
    }

    if (expected.serviceFeeGeneral == null) {
        assertNull(actual.serviceFeeGeneral)
    } else {
        assertTrue(actual.serviceFeeGeneral isEqualTo expected.serviceFeeGeneral!!)
    }
    assertTrue(expected.totalPrice isEqualTo actual.totalPrice)
}

fun assertBasketResponseTicketPriceEquals(expected: TicketPrice, actual: BasketItemTicketPriceResponse) {
    assertEquals(expected.id, actual.id)
    assertTrue(expected.basePrice isEqualTo actual.basePrice)
    assertEquals(expected.basePriceItemNumber, actual.basePriceItemNumber)
    assertTrue(expected.totalPrice isEqualTo actual.totalPrice)
}

fun createTicketPrice(
    id: UUID = UUID.randomUUID(),
    screeningId: UUID,
    seatId: UUID,
    basePrice: BigDecimal = BigDecimal.TEN,
    basePriceBeforeDiscount: BigDecimal? = null,
    basePriceItemNumber: PriceCategoryItemNumber = PriceCategoryItemNumber.PRICE_1,
    seatSurcharge: BigDecimal? = null,
    seatSurchargeType: SeatSurchargeType? = null,
    auditoriumSurcharge: BigDecimal? = null,
    auditoriumSurchargeType: AuditoriumSurchargeType? = null,
    seatServiceFee: BigDecimal? = null,
    seatServiceFeeType: SeatServiceFeeType? = null,
    auditoriumServiceFee: BigDecimal? = null,
    auditoriumServiceFeeType: AuditoriumServiceFeeType? = null,
    serviceFeeGeneral: BigDecimal? = null,
    freeTicket: Boolean? = null,
    zeroFees: Boolean? = null,
    totalPrice: BigDecimal,
    primaryDiscountAmount: BigDecimal? = null,
    primaryDiscountPercentage: Int? = null,
) = TicketPrice(
    id = id,
    screeningId = screeningId,
    seatId = seatId,
    basePrice = basePrice,
    basePriceItemNumber = basePriceItemNumber,
    basePriceBeforeDiscount = basePriceBeforeDiscount ?: basePrice,
    seatSurcharge = seatSurcharge,
    seatSurchargeType = seatSurchargeType,
    auditoriumSurcharge = auditoriumSurcharge,
    auditoriumSurchargeType = auditoriumSurchargeType,
    seatServiceFee = seatServiceFee,
    seatServiceFeeType = seatServiceFeeType,
    auditoriumServiceFee = auditoriumServiceFee,
    auditoriumServiceFeeType = auditoriumServiceFeeType,
    serviceFeeGeneral = serviceFeeGeneral,
    freeTicket = freeTicket,
    zeroFees = zeroFees,
    totalPrice = totalPrice,
    primaryDiscountAmount = primaryDiscountAmount,
    primaryDiscountPercentage = primaryDiscountPercentage
)

fun mapToBasketItemTicketPriceResponse(ticketPrice: TicketPrice) = BasketItemTicketPriceResponse(
    id = ticketPrice.id,
    basePrice = ticketPrice.basePrice,
    basePriceItemNumber = ticketPrice.basePriceItemNumber,
    basePriceBeforeDiscount = ticketPrice.basePriceBeforeDiscount,
    surchargesAndFeesSum = ticketPrice.getSurchargeAndFeeSum(),
    totalPrice = ticketPrice.totalPrice
)
