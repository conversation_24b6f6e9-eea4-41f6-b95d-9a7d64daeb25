package com.cleevio.cinemax.api.util

import com.cleevio.cinemax.api.common.util.trimIfNotBlank
import com.cleevio.cinemax.api.common.util.truncatedToSeconds
import com.cleevio.cinemax.api.module.auditorium.entity.Auditorium
import com.cleevio.cinemax.api.module.groupreservation.constant.GroupReservationType
import com.cleevio.cinemax.api.module.groupreservation.controller.dto.AdminSearchGroupReservationsResponse
import com.cleevio.cinemax.api.module.groupreservation.entity.GroupReservation
import com.cleevio.cinemax.api.module.groupreservation.service.command.SyncCreateOrUpdateGroupReservationCommand
import com.cleevio.cinemax.api.module.movie.entity.Movie
import com.cleevio.cinemax.api.module.reservation.entity.Reservation
import com.cleevio.cinemax.api.module.screening.entity.Screening
import com.cleevio.cinemax.api.module.seat.entity.Seat
import com.cleevio.cinemax.mssql.rps_cinemaxe.dbo.tables.pojos.Rezervace
import java.time.LocalDateTime
import java.util.UUID
import kotlin.test.assertEquals

fun mapToCreateOrUpdateGroupReservationCommand(
    groupReservation: GroupReservation,
) = SyncCreateOrUpdateGroupReservationCommand(
    originalId = groupReservation.originalId ?: 1,
    name = groupReservation.name
)

fun createGroupReservation(
    id: UUID = UUID.randomUUID(),
    originalId: Int? = 1,
    name: String = "Cleevio rezervace",
    type: GroupReservationType = GroupReservationType.BRANCH,
    expiresAt: LocalDateTime? = null,
) = GroupReservation(
    id = id,
    originalId = originalId,
    name = name,
    type = type,
    expiresAt = expiresAt
)

fun assertGroupReservationToMssqlGroupReservationMapping(
    expected: GroupReservation,
    actual: Rezervace,
    screening: Screening,
    reservationCount: Int,
) {
    assertEquals(expected.originalId, actual.rezervaceid)
    assertEquals(screening.originalId, actual.rprogid)
    assertEquals(screening.getScreeningTime(), actual.porad)
    assertEquals(expected.name, actual.jmeno.trimIfNotBlank())
    assertEquals(reservationCount.toShort(), actual.pocet)
    assertEquals(expected.createdBy, actual.zuziv.trimIfNotBlank())
}

fun assertAdminSearchGroupReservationsResponseEquals(
    expected: GroupReservation,
    expectedScreening: Screening,
    expectedAuditorium: Auditorium,
    expectedMovie: Movie,
    expectedReservations: List<Reservation>,
    expectedSeats: List<Seat>,
    actual: AdminSearchGroupReservationsResponse,
) {
    assertEquals(expected.id, actual.id)
    assertEquals(expected.name, actual.name)
    assertEquals(expected.createdAt.truncatedToSeconds(), actual.createdAt.truncatedToSeconds())
    assertEquals(expected.updatedAt.truncatedToSeconds(), actual.updatedAt.truncatedToSeconds())
    assertEquals(expectedScreening.id, actual.screening.id)
    assertEquals(expectedScreening.date, actual.screening.date)
    assertEquals(expectedAuditorium.id, actual.screening.auditorium.id)
    assertEquals(expectedAuditorium.code, actual.screening.auditorium.code)
    assertEquals(expectedMovie.id, actual.screening.movie.id)
    assertEquals(expectedMovie.title, actual.screening.movie.title)

    assertEquals(expectedReservations.size, actual.screening.reservations.size)
    actual.screening.reservations.forEachIndexed { index, reservationResponse ->
        assertEquals(expectedReservations[index].id, reservationResponse.id)
        assertEquals(expectedReservations[index].state, reservationResponse.state)
        assertEquals(expectedSeats[index].id, reservationResponse.seat.id)
        assertEquals(expectedSeats[index].row, reservationResponse.seat.row)
        assertEquals(expectedSeats[index].number, reservationResponse.seat.number)
    }
}
