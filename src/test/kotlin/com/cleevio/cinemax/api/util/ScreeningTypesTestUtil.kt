package com.cleevio.cinemax.api.util

import com.cleevio.cinemax.api.module.screeningtypes.entity.ScreeningTypes
import java.util.UUID

fun createScreeningTypes(screeningId: UUID, screeningTypeId: UUID, blacklisted: Boolean = false): ScreeningTypes = ScreeningTypes(
    screeningId = screeningId,
    screeningTypeId = screeningTypeId,
    blacklisted = blacklisted
)

fun createListOfScreeningTypes(screeningId: UUID, screeningTypeIds: Set<UUID>): List<ScreeningTypes> = screeningTypeIds.map {
    createScreeningTypes(
        screeningId = screeningId,
        screeningTypeId = it
    )
}
