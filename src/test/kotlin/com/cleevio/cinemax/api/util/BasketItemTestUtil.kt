package com.cleevio.cinemax.api.util

import com.cleevio.cinemax.api.common.util.isEqualTo
import com.cleevio.cinemax.api.common.util.toCents
import com.cleevio.cinemax.api.common.util.toOnePlusConstant
import com.cleevio.cinemax.api.module.basket.service.command.CreateBasketItemInput
import com.cleevio.cinemax.api.module.basket.service.command.CreateProductSalesBasketCommand
import com.cleevio.cinemax.api.module.basketitem.constant.BasketItemType
import com.cleevio.cinemax.api.module.basketitem.controller.dto.AdminSearchTicketBasketItemsSummaryResponse
import com.cleevio.cinemax.api.module.basketitem.controller.dto.BasketItemProductResponse
import com.cleevio.cinemax.api.module.basketitem.controller.dto.BasketItemResponse
import com.cleevio.cinemax.api.module.basketitem.controller.dto.BasketItemTicketResponse
import com.cleevio.cinemax.api.module.basketitem.controller.dto.CreateBasketItemRequest
import com.cleevio.cinemax.api.module.basketitem.controller.dto.CreateProductRequest
import com.cleevio.cinemax.api.module.basketitem.controller.dto.CreateReservationRequest
import com.cleevio.cinemax.api.module.basketitem.controller.dto.CreateTicketDiscountRequest
import com.cleevio.cinemax.api.module.basketitem.controller.dto.CreateTicketPriceRequest
import com.cleevio.cinemax.api.module.basketitem.controller.dto.CreateTicketRequest
import com.cleevio.cinemax.api.module.basketitem.entity.BasketItem
import com.cleevio.cinemax.api.module.basketitem.model.TicketBasketItemsSummaryExportRecordModel
import com.cleevio.cinemax.api.module.basketitem.service.command.CreateBasketItemCommand
import com.cleevio.cinemax.api.module.basketitem.service.command.DeleteBasketItemCommand
import com.cleevio.cinemax.api.module.pricecategoryitem.constant.PriceCategoryItemNumber
import com.cleevio.cinemax.api.module.ticketprice.entity.TicketPrice
import java.math.BigDecimal
import java.math.RoundingMode
import java.util.UUID
import kotlin.test.assertEquals
import kotlin.test.assertNull
import kotlin.test.assertTrue

fun mapToCreateBasketItemCommand(
    request: CreateBasketItemRequest,
    basketId: UUID,
    originalId: Int? = null,
) = CreateBasketItemCommand(
    basketId = basketId,
    type = request.type,
    quantity = request.quantity,
    screeningId = request.ticket?.screeningId,
    reservationSeatId = request.ticket?.reservation?.seatId,
    productId = request.product?.productId,
    productIsolatedWithId = request.product?.productIsolatedWithId,
    priceCategoryItemNumber = request.ticket?.ticketPrice?.priceCategoryItemNumber,
    primaryTicketDiscountId = request.ticket?.primaryDiscount?.ticketDiscountId,
    secondaryTicketDiscountId = request.ticket?.secondaryDiscount?.ticketDiscountId,
    primaryTicketDiscountCardId = request.ticket?.primaryDiscount?.discountCardId,
    secondaryTicketDiscountCardId = request.ticket?.secondaryDiscount?.discountCardId,
    productDiscountCardId = request.product?.discountCardId,
    isGroupTicket = request.ticket?.isGroupTicket,
    originalId = originalId
)

fun mapToCreateBasketItemCommand(
    input: CreateBasketItemInput,
    basketId: UUID,
    originalId: Int? = null,
) = CreateBasketItemCommand(
    basketId = basketId,
    type = input.type,
    quantity = input.quantity,
    screeningId = input.ticket?.screeningId,
    reservationSeatId = input.ticket?.reservation?.seatId,
    productId = input.product?.productId,
    productIsolatedWithId = input.product?.productIsolatedWithId,
    priceCategoryItemNumber = input.ticket?.ticketPrice?.priceCategoryItemNumber,
    primaryTicketDiscountId = input.ticket?.primaryDiscount?.ticketDiscountId,
    secondaryTicketDiscountId = input.ticket?.secondaryDiscount?.ticketDiscountId,
    primaryTicketDiscountCardId = input.ticket?.primaryDiscount?.discountCardId,
    secondaryTicketDiscountCardId = input.ticket?.secondaryDiscount?.discountCardId,
    productDiscountCardId = input.product?.discountCardId,
    isGroupTicket = input.ticket?.isGroupTicket,
    originalId = originalId
)

fun mapToDeleteBasketItemCommand(
    basketId: UUID,
    basketItemId: UUID,
) = DeleteBasketItemCommand(
    basketId = basketId,
    basketItemId = basketItemId
)

fun createBasketItem(
    id: UUID? = null,
    basketId: UUID,
    ticketId: UUID? = null,
    productId: UUID? = null,
    type: BasketItemType,
    price: BigDecimal,
    quantity: Int = 0,
    productReceiptNumber: String? = null,
    productIsolatedWith: UUID? = null,
    cancelledBasketItemId: UUID? = null,
    isCancelled: Boolean = false,
    branchId: UUID? = null,
    testVatRate: Int = 23,
    vatAmountOverride: BigDecimal? = null,
    entityModifier: (BasketItem) -> Unit = {},
) = BasketItem(
    id = id ?: UUID.randomUUID(),
    basketId = basketId,
    ticketId = ticketId,
    productId = productId,
    type = type,
    price = price,
    // some tests rely on BasketItem.vatAmount being correctly pre-computed, so we have to set vatAmount when it's not possible
    vatAmount = vatAmountOverride ?: (price - (price.setScale(6, RoundingMode.HALF_UP) / testVatRate.toOnePlusConstant())),
    quantity = quantity,
    productReceiptNumber = productReceiptNumber,
    productIsolatedWithId = productIsolatedWith,
    cancelledBasketItemId = cancelledBasketItemId,
    isCancelled = isCancelled,
    branchId = branchId
).also { entityModifier(it) }

fun copyBasketItem(
    basketItem: BasketItem,
    entityModifier: (BasketItem) -> Unit = {},
) = createBasketItem(
    id = UUID.randomUUID(),
    basketId = basketItem.basketId,
    ticketId = basketItem.ticketId,
    productId = basketItem.productId,
    type = basketItem.type,
    price = basketItem.price,
    quantity = basketItem.quantity,
    productReceiptNumber = basketItem.productReceiptNumber,
    cancelledBasketItemId = basketItem.cancelledBasketItemId
).also { entityModifier(it) }

fun assertBasketItemEquals(
    expected: BasketItem,
    actual: BasketItemResponse,
) {
    assertEquals(expected.id, actual.id)
    assertEquals(expected.type, actual.type)
    assertTrue(expected.price isEqualTo actual.price)
    assertEquals(expected.quantity, actual.quantity)
    assertEquals(expected.productId, actual.product?.id)
}

fun mapToBasketItemResponse(
    basketItem: BasketItem,
    productResponse: BasketItemProductResponse? = null,
    ticketResponse: BasketItemTicketResponse? = null,
) = BasketItemResponse(
    id = basketItem.id,
    type = basketItem.type,
    price = basketItem.price,
    originalPrice = basketItem.originalPrice,
    quantity = basketItem.quantity,
    product = productResponse,
    ticket = ticketResponse
)

fun mapToBasketItemRequest(
    basketItem: BasketItem,
    productId: UUID? = null,
    ticketPrice: TicketPrice? = null,
    screeningId: UUID,
    isGroupTicket: Boolean? = null,
) = CreateBasketItemRequest(
    type = basketItem.type,
    quantity = basketItem.quantity,
    product = productId?.let {
        CreateProductRequest(
            productId = productId
        )
    },
    ticket = ticketPrice?.let {
        CreateTicketRequest(
            ticketPrice = CreateTicketPriceRequest(
                priceCategoryItemNumber = it.basePriceItemNumber
            ),
            reservation = CreateReservationRequest(
                seatId = it.seatId
            ),
            screeningId = screeningId,
            isGroupTicket = isGroupTicket ?: false
        )
    }
)

fun createTicketBasketItemRequest(
    seatId: UUID,
    screeningId: UUID,
    priceCategoryItem: PriceCategoryItemNumber,
    primaryTicketDiscountId: UUID? = null,
    secondaryTicketDiscountId: UUID? = null,
    primaryDiscountCardId: UUID? = null,
    secondaryDiscountCardId: UUID? = null,
) = CreateBasketItemRequest(
    type = BasketItemType.TICKET,
    quantity = 1,
    ticket = CreateTicketRequest(
        screeningId = screeningId,
        ticketPrice = CreateTicketPriceRequest(
            priceCategoryItemNumber = priceCategoryItem
        ),
        reservation = CreateReservationRequest(
            seatId = seatId
        ),
        primaryDiscount = primaryTicketDiscountId?.let {
            CreateTicketDiscountRequest(
                ticketDiscountId = it,
                discountCardId = primaryDiscountCardId
            )
        },
        secondaryDiscount = secondaryTicketDiscountId?.let {
            CreateTicketDiscountRequest(
                ticketDiscountId = it,
                discountCardId = secondaryDiscountCardId
            )
        }
    )
)

fun assertTicketBasketItemSummaryEquals(
    expected: AdminSearchTicketBasketItemsSummaryResponse,
    actual: AdminSearchTicketBasketItemsSummaryResponse,
) {
    assertEquals(expected.sales.screeningsCount, actual.sales.screeningsCount)
    assertEquals(expected.sales.ticketsCount, actual.sales.ticketsCount)
    assertEquals(expected.sales.ticketsUsedCount, actual.sales.ticketsUsedCount)
    assert(expected.sales.salesCash.toCents() isEqualTo actual.sales.salesCash.toCents())
    assert(expected.sales.salesCashless.toCents() isEqualTo actual.sales.salesCashless.toCents())
    assert(expected.sales.grossSales.toCents() isEqualTo actual.sales.grossSales.toCents())
    assert(expected.sales.netSales.toCents() isEqualTo actual.sales.netSales.toCents())
    assert(expected.sales.filmFondDeduction.toCents() isEqualTo actual.sales.filmFondDeduction.toCents())
    assert(expected.sales.distributorDeduction.toCents() isEqualTo actual.sales.distributorDeduction.toCents())
    assert(expected.sales.taxAmount.toCents() isEqualTo actual.sales.taxAmount.toCents())
    assertEquals(expected.sales.cancelledTicketsCount, actual.sales.cancelledTicketsCount)
    assert(expected.sales.cancelledTicketsExpense.toCents() isEqualTo actual.sales.cancelledTicketsExpense.toCents())

    assert(expected.technologies.serviceFees.general.toCents() isEqualTo actual.technologies.serviceFees.general.toCents())
    assertEquals(expected.technologies.serviceFees.generalCount, actual.technologies.serviceFees.generalCount)
    assert(expected.technologies.serviceFees.vip.toCents() isEqualTo actual.technologies.serviceFees.vip.toCents())
    assertEquals(expected.technologies.serviceFees.vipCount, actual.technologies.serviceFees.vipCount)
    assert(
        expected.technologies.serviceFees.premiumPlus.toCents() isEqualTo actual.technologies.serviceFees.premiumPlus.toCents()
    )
    assertEquals(expected.technologies.serviceFees.premiumPlusCount, actual.technologies.serviceFees.premiumPlusCount)
    assert(expected.technologies.serviceFees.imax.toCents() isEqualTo actual.technologies.serviceFees.imax.toCents())
    assertEquals(expected.technologies.serviceFees.imaxCount, actual.technologies.serviceFees.imaxCount)
    assert(expected.technologies.serviceFees.ultraX.toCents() isEqualTo actual.technologies.serviceFees.ultraX.toCents())
    assertEquals(expected.technologies.serviceFees.ultraXCount, actual.technologies.serviceFees.ultraXCount)

    assert(expected.technologies.surcharges.vip.toCents() isEqualTo actual.technologies.surcharges.vip.toCents())
    assertEquals(expected.technologies.surcharges.vipCount, actual.technologies.surcharges.vipCount)
    assert(expected.technologies.surcharges.dBox.toCents() isEqualTo actual.technologies.surcharges.dBox.toCents())
    assertEquals(expected.technologies.surcharges.dBoxCount, actual.technologies.surcharges.dBoxCount)
    assert(expected.technologies.surcharges.premiumPlus.toCents() isEqualTo actual.technologies.surcharges.premiumPlus.toCents())
    assertEquals(expected.technologies.surcharges.premiumPlusCount, actual.technologies.surcharges.premiumPlusCount)
    assert(expected.technologies.surcharges.imax.toCents() isEqualTo actual.technologies.surcharges.imax.toCents())
    assertEquals(expected.technologies.surcharges.imaxCount, actual.technologies.surcharges.imaxCount)
    assert(expected.technologies.surcharges.ultraX.toCents() isEqualTo actual.technologies.surcharges.ultraX.toCents())
    assertEquals(expected.technologies.surcharges.ultraXCount, actual.technologies.surcharges.ultraXCount)
    assertEquals(expected.discounts.size, actual.discounts.size)
    expected.discounts.forEachIndexed { index, expectedDiscount ->
        assertEquals(expectedDiscount.title, actual.discounts[index].title)
        assertEquals(expectedDiscount.count, actual.discounts[index].count)
    }
}

fun assertTicketBasketItemsSummaryExportEquals(
    expected: TicketBasketItemsSummaryExportRecordModel,
    actual: TicketBasketItemsSummaryExportRecordModel,
) {
    // Sales assertions
    assertEquals(expected.sales.screeningsCount, actual.sales.screeningsCount)
    assertEquals(expected.sales.ticketsCount, actual.sales.ticketsCount)
    assertEquals(expected.sales.ticketsUsedCount, actual.sales.ticketsUsedCount)
    assert(expected.sales.salesCash.toCents() isEqualTo actual.sales.salesCash.toCents())
    assert(expected.sales.salesCashless.toCents() isEqualTo actual.sales.salesCashless.toCents())
    assert(expected.sales.grossSales.toCents() isEqualTo actual.sales.grossSales.toCents())
    assert(expected.sales.netSales.toCents() isEqualTo actual.sales.netSales.toCents())
    assert(expected.sales.proDeduction.toCents() isEqualTo actual.sales.proDeduction.toCents())
    assert(expected.sales.filmFondDeduction.toCents() isEqualTo actual.sales.filmFondDeduction.toCents())
    assert(expected.sales.distributorDeduction.toCents() isEqualTo actual.sales.distributorDeduction.toCents())
    assert(expected.sales.taxAmount.toCents() isEqualTo actual.sales.taxAmount.toCents())
    assertEquals(expected.sales.cancelledTicketsCount, actual.sales.cancelledTicketsCount)
    assert(expected.sales.cancelledTicketsExpense.toCents() isEqualTo actual.sales.cancelledTicketsExpense.toCents())

// Service fees assertions
    assert(expected.serviceFees.general.toCents() isEqualTo actual.serviceFees.general.toCents())
    assertEquals(expected.serviceFees.generalCount, actual.serviceFees.generalCount)
    assert(expected.serviceFees.vip.toCents() isEqualTo actual.serviceFees.vip.toCents())
    assertEquals(expected.serviceFees.vipCount, actual.serviceFees.vipCount)
    assert(expected.serviceFees.premiumPlus.toCents() isEqualTo actual.serviceFees.premiumPlus.toCents())
    assertEquals(expected.serviceFees.premiumPlusCount, actual.serviceFees.premiumPlusCount)
    assert(expected.serviceFees.imax.toCents() isEqualTo actual.serviceFees.imax.toCents())
    assertEquals(expected.serviceFees.imaxCount, actual.serviceFees.imaxCount)
    assert(expected.serviceFees.ultraX.toCents() isEqualTo actual.serviceFees.ultraX.toCents())
    assertEquals(expected.serviceFees.ultraXCount, actual.serviceFees.ultraXCount)

// Surcharges assertions
    assert(expected.surcharges.vip.toCents() isEqualTo actual.surcharges.vip.toCents())
    assertEquals(expected.surcharges.vipCount, actual.surcharges.vipCount)
    assert(expected.surcharges.dBox.toCents() isEqualTo actual.surcharges.dBox.toCents())
    assertEquals(expected.surcharges.dBoxCount, actual.surcharges.dBoxCount)
    assert(expected.surcharges.premiumPlus.toCents() isEqualTo actual.surcharges.premiumPlus.toCents())
    assertEquals(expected.surcharges.premiumPlusCount, actual.surcharges.premiumPlusCount)
    assert(expected.surcharges.imax.toCents() isEqualTo actual.surcharges.imax.toCents())
    assertEquals(expected.surcharges.imaxCount, actual.surcharges.imaxCount)
    assert(expected.surcharges.ultraX.toCents() isEqualTo actual.surcharges.ultraX.toCents())
    assertEquals(expected.surcharges.ultraXCount, actual.surcharges.ultraXCount)

    // Discounts assertions
    assertEquals(expected.discounts.size, actual.discounts.size)
    expected.discounts.forEachIndexed { index, expectedDiscount ->
        assertEquals(expectedDiscount.title, actual.discounts[index].title)
        assertEquals(expectedDiscount.count, actual.discounts[index].count)
    }
}

fun assertEqualsInitOrUpdateProductSalesBasketCommandToBasketItem(
    command: CreateProductSalesBasketCommand,
    basketId: UUID,
    basketItem: BasketItem,
) {
    assertNull(basketItem.originalId)
    assertEquals(basketId, basketItem.basketId)
    assertNull(basketItem.ticketId)
    assertEquals(command.productId, basketItem.productId)
    assertEquals(BasketItemType.PRODUCT, basketItem.type)
    assertTrue(basketItem.price isEqualTo command.price)
    assertEquals(command.quantity, basketItem.quantity)
    assertEquals(command.receiptNumber, basketItem.productReceiptNumber)
    assertNull(basketItem.productIsolatedWithId)
    assertNull(basketItem.cancelledBasketItemId)
    assertEquals(command.isCancelled, basketItem.isCancelled)
    assertEquals(command.branchId, basketItem.branchId)
}
