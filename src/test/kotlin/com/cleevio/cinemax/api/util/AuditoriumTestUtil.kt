package com.cleevio.cinemax.api.util

import com.cleevio.cinemax.api.module.auditorium.controller.dto.AuditoriumSearchResponse
import com.cleevio.cinemax.api.module.auditorium.entity.Auditorium
import com.cleevio.cinemax.api.module.auditorium.service.command.CreateOrUpdateAuditoriumCommand
import com.cleevio.cinemax.api.module.auditoriumdefault.entity.AuditoriumDefault
import com.cleevio.cinemax.api.module.groupreservation.controller.dto.AuditoriumResponse
import com.cleevio.cinemax.api.module.seat.controller.dto.SeatSearchResponse
import java.math.BigDecimal
import java.util.UUID
import kotlin.test.assertEquals

fun mapToCreateOrUpdateAuditoriumCommand(
    auditorium: Auditorium,
    auditoriumDefault: AuditoriumDefault? = null,
) = CreateOrUpdateAuditoriumCommand(
    id = auditorium.id,
    originalId = auditorium.originalId,
    originalCode = auditorium.originalCode,
    title = auditorium.title,
    capacity = auditorium.capacity,
    city = auditorium.city,
    saleTimeLimit = auditoriumDefault?.saleTimeLimit ?: 0,
    surchargeVip = auditoriumDefault?.surchargeVip ?: BigDecimal.ZERO,
    surchargePremium = auditoriumDefault?.surchargePremium ?: BigDecimal.ZERO,
    surchargeImax = auditoriumDefault?.surchargeImax ?: BigDecimal.ZERO,
    surchargeUltraX = auditoriumDefault?.surchargeUltraX ?: BigDecimal.ZERO,
    surchargeDBox = auditoriumDefault?.surchargeDBox ?: BigDecimal.ZERO,
    serviceFeeVip = auditoriumDefault?.serviceFeeVip ?: BigDecimal.ZERO,
    serviceFeePremium = auditoriumDefault?.serviceFeePremium ?: BigDecimal.ZERO,
    serviceFeeImax = auditoriumDefault?.serviceFeeImax ?: BigDecimal.ZERO,
    serviceFeeUltraX = auditoriumDefault?.serviceFeeUltraX ?: BigDecimal.ZERO,
    proCommission = auditoriumDefault?.proCommission ?: 0,
    filmFondCommission = auditoriumDefault?.filmFondCommission ?: 0,
    distributorCommission = auditoriumDefault?.distributorCommission ?: 0,
    publishOnline = auditoriumDefault?.publishOnline ?: false
)

fun createAuditorium(
    id: UUID = UUID.randomUUID(),
    originalId: Int = 1,
    code: String = "A",
    branchId: UUID? = null,
    title: String = "Sála A",
    originalCode: Int = originalId + 100,
    capacity: Int = 100,
    city: String = "Bratislava",
) = Auditorium(
    id = id,
    originalId = originalId,
    originalCode = originalCode,
    branchId = branchId,
    code = code,
    title = title,
    capacity = capacity,
    city = city
)

fun mapToAuditoriumSearchResponse(auditorium: Auditorium, seatResponses: List<SeatSearchResponse>) = AuditoriumSearchResponse(
    id = auditorium.id,
    code = auditorium.code,
    title = auditorium.title,
    seats = seatResponses
)

fun assertAuditoriumEquals(expected: Auditorium, actual: AuditoriumSearchResponse) {
    assertEquals(expected.id, actual.id)
    assertEquals(expected.title, actual.title)
    assertEquals(expected.code, actual.code)
}

fun mapToCreateOrUpdateAuditoriumCommand(
    originalId: Int,
    originalCode: Int,
    branchId: UUID? = null,
    title: String,
    capacity: Int,
    city: String,
) = CreateOrUpdateAuditoriumCommand(
    originalId = originalId,
    originalCode = originalCode,
    branchId = branchId,
    title = title,
    capacity = capacity,
    city = city,
    saleTimeLimit = 0,
    surchargeVip = BigDecimal.ZERO,
    surchargePremium = BigDecimal.ZERO,
    surchargeImax = BigDecimal.ZERO,
    surchargeUltraX = BigDecimal.ZERO,
    surchargeDBox = BigDecimal.ZERO,
    serviceFeeVip = BigDecimal.ZERO,
    serviceFeePremium = BigDecimal.ZERO,
    serviceFeeImax = BigDecimal.ZERO,
    serviceFeeUltraX = BigDecimal.ZERO,
    proCommission = 0,
    filmFondCommission = 0,
    distributorCommission = 0,
    publishOnline = false
)

fun mapToAdminSearchGroupReservationAuditoriumResponse(auditorium: Auditorium) = AuditoriumResponse(
    id = auditorium.id,
    code = auditorium.code
)
