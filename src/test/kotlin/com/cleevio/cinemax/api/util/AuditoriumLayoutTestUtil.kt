package com.cleevio.cinemax.api.util

import com.cleevio.cinemax.api.module.auditoriumlayout.entity.AuditoriumLayout
import com.cleevio.cinemax.api.module.auditoriumlayout.service.command.CreateOrUpdateAuditoriumLayoutCommand
import java.util.UUID
import kotlin.test.assertEquals

fun mapToCreateOrUpdateAuditoriumLayoutCommand(
    auditoriumLayout: AuditoriumLayout,
) = CreateOrUpdateAuditoriumLayoutCommand(
    id = auditoriumLayout.id,
    originalId = auditoriumLayout.originalId,
    auditoriumId = auditoriumLayout.auditoriumId,
    code = auditoriumLayout.code,
    title = auditoriumLayout.title
)

fun createAuditoriumLayout(
    originalId: Int? = 1,
    code: String = "0$originalId",
    auditoriumId: UUID,
    title: String = "Základná",
) = AuditoriumLayout(
    originalId = originalId,
    auditoriumId = auditoriumId,
    code = code,
    title = title
)

fun assertCommandToAuditoriumLayoutMapping(
    expected: CreateOrUpdateAuditoriumLayoutCommand,
    actual: AuditoriumLayout,
) {
    assertEquals(expected.originalId, actual.originalId)
    assertEquals(expected.auditoriumId, actual.auditoriumId)
    assertEquals(expected.code, actual.code)
    assertEquals(expected.title, actual.title)
}
