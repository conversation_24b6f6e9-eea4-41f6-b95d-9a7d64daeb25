package com.cleevio.cinemax.api.util

import com.cleevio.cinemax.api.common.util.forceNegate
import com.cleevio.cinemax.api.common.util.isEqualTo
import com.cleevio.cinemax.api.common.util.truncatedToSeconds
import com.cleevio.cinemax.api.module.productcomponent.entity.ProductComponent
import com.cleevio.cinemax.api.module.productcomponentcategory.entity.ProductComponentCategory
import com.cleevio.cinemax.api.module.stockmovement.constant.StockMovementType
import com.cleevio.cinemax.api.module.stockmovement.controller.dto.AdminSearchStockMovementsResponse
import com.cleevio.cinemax.api.module.stockmovement.entity.StockMovement
import com.cleevio.cinemax.api.module.stockmovement.service.command.CreateInputStockMovementsCommand
import com.cleevio.cinemax.api.module.stockmovement.service.command.CreateOrUpdateStockMovementCommand
import com.cleevio.cinemax.api.module.stockmovement.service.model.StockMovementExportRecordModel
import com.cleevio.cinemax.api.module.supplier.entity.Supplier
import java.math.BigDecimal
import java.time.LocalDateTime
import java.util.UUID
import kotlin.test.assertEquals
import kotlin.test.assertNotNull
import kotlin.test.assertNull
import kotlin.test.assertTrue

fun mapToCreateOrUpdateStockMovementCommand(stockMovement: StockMovement) = CreateOrUpdateStockMovementCommand(
    id = stockMovement.id,
    originalId = stockMovement.originalId,
    supplierId = stockMovement.supplierId,
    productComponentId = stockMovement.productComponentId,
    basketItemId = stockMovement.basketItemId,
    type = stockMovement.type,
    quantity = stockMovement.quantity,
    price = stockMovement.price,
    receiptNumber = stockMovement.receiptNumber,
    note = stockMovement.note,
    recordedAt = stockMovement.recordedAt
)

fun createOutputStockMovement(
    id: UUID = UUID.randomUUID(),
    originalId: Int? = 1,
    productComponentId: UUID,
    basketItemId: UUID? = null,
    type: StockMovementType = StockMovementType.CORRECTION,
    quantity: BigDecimal = 10.toBigDecimal(),
    price: BigDecimal = BigDecimal.TEN,
    note: String? = "stock movement note",
    recordedAt: LocalDateTime = LocalDateTime.now(),
) = createStockMovement(
    id = id,
    originalId = originalId?.forceNegate(),
    productComponentId = productComponentId,
    basketItemId = basketItemId,
    type = type,
    quantity = quantity,
    price = price,
    note = note,
    recordedAt = recordedAt
)

fun createInputStockMovement(
    id: UUID = UUID.randomUUID(),
    originalId: Int? = 1,
    supplierId: UUID,
    productComponentId: UUID,
    quantity: BigDecimal = 10.toBigDecimal(),
    price: BigDecimal = BigDecimal.TEN,
    receiptNumber: String? = "0123456789",
    note: String? = "stock movement note",
    recordedAt: LocalDateTime = LocalDateTime.now(),
) = createStockMovement(
    id = id,
    originalId = originalId,
    supplierId = supplierId,
    productComponentId = productComponentId,
    type = StockMovementType.GOODS_RECEIPT,
    quantity = quantity,
    price = price,
    receiptNumber = receiptNumber,
    note = note,
    recordedAt = recordedAt
)

fun assertCreateOrUpdateCommandToStockMovementMapping(
    expected: CreateOrUpdateStockMovementCommand,
    actual: StockMovement,
) {
    assertEquals(expected.originalId, actual.originalId)
    assertEquals(expected.supplierId, actual.supplierId)
    assertEquals(expected.productComponentId, actual.productComponentId)
    assertEquals(expected.basketItemId, actual.basketItemId)
    assertEquals(expected.type, actual.type)
    assertTrue(expected.quantity.isEqualTo(actual.quantity))
    assertTrue(expected.price.isEqualTo(actual.price))
    assertEquals(expected.receiptNumber, actual.receiptNumber)
    assertEquals(expected.note, actual.note)
    assertEquals(expected.recordedAt.truncatedToSeconds(), actual.recordedAt.truncatedToSeconds())
}

fun assertCreateInputCommandToStockMovementMapping(
    expectedStockMovement: CreateInputStockMovementsCommand,
    expectedProductComponent: CreateInputStockMovementsCommand.CreateInputStockMovementProductComponentCommand,
    expectedPrice: BigDecimal,
    actual: StockMovement,
) {
    assertEquals(expectedStockMovement.supplierId, actual.supplierId)
    assertEquals(expectedProductComponent.productComponentId, actual.productComponentId)
    assertEquals(StockMovementType.GOODS_RECEIPT, actual.type)
    assertTrue(expectedProductComponent.quantity.isEqualTo(actual.quantity))
    assertEquals(expectedStockMovement.receiptNumber, actual.receiptNumber)
    assertTrue(expectedPrice.isEqualTo(actual.price))
    assertEquals(expectedProductComponent.note, actual.note)
    assertEquals(expectedStockMovement.recordedAt.truncatedToSeconds(), actual.recordedAt.truncatedToSeconds())
}

fun assertStockMovementToSearchResponseEquals(
    expectedMovement: StockMovement,
    expectedSupplier: Supplier? = null,
    expectedProductComponent: ProductComponent,
    expectedProductCategory: ProductComponentCategory,
    actual: AdminSearchStockMovementsResponse,
) {
    assertEquals(expectedMovement.id, actual.id)
    assertEquals(expectedMovement.type, actual.type)
    assertEquals(expectedMovement.quantity, actual.quantity)
    assertEquals(expectedMovement.price, actual.price)
    assertEquals(expectedMovement.receiptNumber, actual.receiptNumber)
    assertEquals(expectedMovement.note, actual.note)
    assertNotNull(actual.recordedAt)
    assertNotNull(actual.createdAt)
    assertNotNull(actual.updatedAt)

    expectedSupplier?.let {
        assertEquals(it.id, actual.supplier?.id)
        assertEquals(it.title, actual.supplier?.title)
    } ?: assertNull(actual.supplier)

    assertEquals(expectedProductComponent.id, actual.productComponent.id)
    assertEquals(expectedProductComponent.title, actual.productComponent.title)
    assertEquals(expectedProductComponent.code, actual.productComponent.originalCode)
    assertEquals(expectedProductComponent.unit, actual.productComponent.unit)
    assertEquals(expectedProductCategory.id, actual.productComponent.category.id)
    assertEquals(expectedProductCategory.title, actual.productComponent.category.title)
    assertEquals(expectedProductCategory.taxRate, actual.productComponent.category.taxRate)
}

fun assertStockMovementToExportModelEquals(
    expectedStockMovement: StockMovement,
    expectedProductComponent: ProductComponent,
    expectedSupplier: Supplier?,
    actual: StockMovementExportRecordModel,
) {
    assertEquals(expectedStockMovement.recordedAt, actual.recordedAt)
    assertEquals(expectedProductComponent.code, actual.productComponentCode)
    assertEquals(expectedProductComponent.title, actual.productComponentTitle)
    assertTrue(expectedProductComponent.purchasePrice isEqualTo actual.productComponentPurchasePrice)
    assertTrue(expectedStockMovement.quantity isEqualTo actual.quantity)
    assertEquals(expectedStockMovement.receiptNumber, actual.receiptNumber)
    assertTrue(expectedStockMovement.price isEqualTo actual.price)
    assertEquals(expectedSupplier?.title, actual.supplierTitle)
}

private fun createStockMovement(
    id: UUID = UUID.randomUUID(),
    originalId: Int? = 1,
    supplierId: UUID? = null,
    productComponentId: UUID,
    basketItemId: UUID? = null,
    type: StockMovementType = StockMovementType.GOODS_RECEIPT,
    quantity: BigDecimal = 10.toBigDecimal(),
    price: BigDecimal = BigDecimal.TEN,
    receiptNumber: String? = null,
    note: String? = null,
    recordedAt: LocalDateTime = LocalDateTime.now(),
) = StockMovement(
    id = id,
    originalId = originalId,
    supplierId = supplierId,
    productComponentId = productComponentId,
    basketItemId = basketItemId,
    type = type,
    quantity = quantity,
    price = price,
    receiptNumber = receiptNumber,
    note = note,
    recordedAt = recordedAt
)
