package com.cleevio.cinemax.api.util

import com.cleevio.cinemax.api.module.screeningfee.entity.ScreeningFee
import com.cleevio.cinemax.api.module.screeningfee.service.command.CreateOrUpdateScreeningFeeCommand
import java.math.BigDecimal
import java.util.UUID
import kotlin.test.assertEquals

fun mapToCreateOrUpdateScreeningFeeCommand(screeningFee: ScreeningFee) =
    CreateOrUpdateScreeningFeeCommand(
        id = screeningFee.id,
        originalScreeningId = screeningFee.originalScreeningId,
        screeningId = screeningFee.screeningId,
        surchargeVip = screeningFee.surchargeVip,
        surchargePremium = screeningFee.surchargePremium,
        surchargeImax = screeningFee.surchargeImax,
        surchargeDBox = screeningFee.surchargeDBox,
        surchargeUltraX = screeningFee.surchargeUltraX,
        serviceFeeVip = screeningFee.serviceFeeVip,
        serviceFeePremium = screeningFee.serviceFeePremium,
        serviceFeeImax = screeningFee.serviceFeeImax,
        serviceFeeUltraX = screeningFee.serviceFeeUltraX,
        serviceFeeGeneral = screeningFee.serviceFeeGeneral
    )

fun createScreeningFee(
    originalScreeningId: Int?,
    screeningId: UUID,
    surchargeVip: BigDecimal = 1.toBigDecimal(),
    surchargePremium: BigDecimal = 0.5.toBigDecimal(),
    surchargeImax: BigDecimal = 2.toBigDecimal(),
    surchargeUltraX: BigDecimal = 0.5.toBigDecimal(),
    surchargeDBox: BigDecimal = 5.toBigDecimal(),
    serviceFeeVip: BigDecimal = 1.toBigDecimal(),
    serviceFeePremium: BigDecimal = 0.5.toBigDecimal(),
    serviceFeeImax: BigDecimal = 2.toBigDecimal(),
    serviceFeeUltraX: BigDecimal = 0.5.toBigDecimal(),
    serviceFeeGeneral: BigDecimal = 0.2.toBigDecimal(),
) = ScreeningFee(
    id = UUID.randomUUID(),
    originalScreeningId = originalScreeningId,
    screeningId = screeningId,
    surchargeVip = surchargeVip,
    surchargePremium = surchargePremium,
    surchargeImax = surchargeImax,
    surchargeUltraX = surchargeUltraX,
    surchargeDBox = surchargeDBox,
    serviceFeeVip = serviceFeeVip,
    serviceFeePremium = serviceFeePremium,
    serviceFeeImax = serviceFeeImax,
    serviceFeeUltraX = serviceFeeUltraX,
    serviceFeeGeneral = serviceFeeGeneral
)

fun assertScreeningFeeEquals(expected: ScreeningFee, actual: ScreeningFee) {
    assertEquals(expected.originalScreeningId, actual.originalScreeningId)
    assertEquals(expected.surchargeVip, actual.surchargeVip)
    assertEquals(expected.surchargePremium, actual.surchargePremium)
    assertEquals(expected.surchargeImax, actual.surchargeImax)
    assertEquals(expected.surchargeUltraX, actual.surchargeUltraX)
    assertEquals(expected.surchargeDBox, actual.surchargeDBox)
    assertEquals(expected.serviceFeeVip, actual.serviceFeeVip)
    assertEquals(expected.serviceFeePremium, actual.serviceFeePremium)
    assertEquals(expected.serviceFeeImax, actual.serviceFeeImax)
    assertEquals(expected.serviceFeeUltraX, actual.serviceFeeUltraX)
    assertEquals(expected.serviceFeeGeneral, actual.serviceFeeGeneral)
}
