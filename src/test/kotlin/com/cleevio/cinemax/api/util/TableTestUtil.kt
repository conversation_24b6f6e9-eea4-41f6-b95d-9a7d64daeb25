package com.cleevio.cinemax.api.util

import com.cleevio.cinemax.api.common.constant.PaymentType
import com.cleevio.cinemax.api.module.posconfiguration.constant.ProductMode
import com.cleevio.cinemax.api.module.table.constant.TableType
import com.cleevio.cinemax.api.module.table.entity.Table
import com.cleevio.cinemax.api.module.table.service.command.CreateOrUpdateTableCommand
import java.util.UUID

fun createTable(
    id: UUID = UUID.randomUUID(),
    originalId: Int = 1,
    title: String = "Stul 1",
    label: String = "1",
    order: Int? = 1,
    type: TableType = TableType.TABLE,
    productMode: ProductMode = ProductMode.STANDARD,
    paymentType: PaymentType = PaymentType.CASH,
    discountCardCode: String? = null,
    ipAddress: String? = null,
) = Table(
    id = id,
    originalId = originalId,
    title = title,
    label = label,
    order = order,
    type = type,
    productMode = productMode,
    paymentType = paymentType,
    discountCardCode = discountCardCode,
    ipAddress = ipAddress
)

fun mapToCreateOrUpdateTableCommand(table: Table) =
    CreateOrUpdateTableCommand(
        id = table.id,
        originalId = table.originalId,
        label = table.label,
        title = table.title,
        order = table.order,
        type = table.type,
        productMode = table.productMode,
        paymentType = table.paymentType,
        discountCardCode = table.discountCardCode,
        ipAddress = table.ipAddress
    )
