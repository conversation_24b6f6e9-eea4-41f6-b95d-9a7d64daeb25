package com.cleevio.cinemax.api.util

import com.cleevio.cinemax.api.module.outboxevent.constant.OutboxEventState
import com.cleevio.cinemax.api.module.outboxevent.constant.OutboxEventType
import com.cleevio.cinemax.api.module.outboxevent.entity.OutboxEvent
import com.cleevio.cinemax.api.module.outboxevent.service.command.CreateOutboxEventCommand
import java.util.UUID
import kotlin.test.assertEquals

fun mapToCreateOutboxEventCommand(event: OutboxEvent) = CreateOutboxEventCommand(
    entityId = event.entityId,
    type = event.type,
    data = event.data
)
fun createOutboxEvent(
    entityId: UUID,
    type: OutboxEventType,
    state: OutboxEventState = OutboxEventState.PENDING,
    data: String = "{}",
) = OutboxEvent(
    id = UUID.randomUUID(),
    entityId = entityId,
    type = type,
    state = state,
    data = data
)

fun assertOutboxEventEquals(expected: OutboxEvent, actual: OutboxEvent) {
    assertEquals(expected.entityId, actual.entityId)
    assertEquals(expected.type, actual.type)
    assertEquals(expected.state, actual.state)
    assertEquals(expected.data, actual.data)
}
