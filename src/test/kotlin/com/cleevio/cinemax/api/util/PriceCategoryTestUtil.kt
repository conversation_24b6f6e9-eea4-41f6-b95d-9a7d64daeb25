package com.cleevio.cinemax.api.util

import com.cleevio.cinemax.api.common.util.isEqualTo
import com.cleevio.cinemax.api.common.util.trimIfNotBlank
import com.cleevio.cinemax.api.module.pricecategory.controller.dto.PriceCategorySearchResponse
import com.cleevio.cinemax.api.module.pricecategory.entity.PriceCategory
import com.cleevio.cinemax.api.module.pricecategory.service.command.SyncCreateOrUpdatePriceCategoryCommand
import com.cleevio.cinemax.api.module.pricecategory.service.model.PriceCategoryItemModel
import com.cleevio.cinemax.api.module.pricecategory.service.model.PriceCategoryModel
import com.cleevio.cinemax.api.module.pricecategoryitem.constant.PriceCategoryItemNumber
import com.cleevio.cinemax.api.module.pricecategoryitem.constant.PriceCategoryItemNumber.PRICE_1
import com.cleevio.cinemax.api.module.pricecategoryitem.constant.PriceCategoryItemNumber.PRICE_10
import com.cleevio.cinemax.api.module.pricecategoryitem.constant.PriceCategoryItemNumber.PRICE_11
import com.cleevio.cinemax.api.module.pricecategoryitem.constant.PriceCategoryItemNumber.PRICE_12
import com.cleevio.cinemax.api.module.pricecategoryitem.constant.PriceCategoryItemNumber.PRICE_13
import com.cleevio.cinemax.api.module.pricecategoryitem.constant.PriceCategoryItemNumber.PRICE_14
import com.cleevio.cinemax.api.module.pricecategoryitem.constant.PriceCategoryItemNumber.PRICE_15
import com.cleevio.cinemax.api.module.pricecategoryitem.constant.PriceCategoryItemNumber.PRICE_16
import com.cleevio.cinemax.api.module.pricecategoryitem.constant.PriceCategoryItemNumber.PRICE_17
import com.cleevio.cinemax.api.module.pricecategoryitem.constant.PriceCategoryItemNumber.PRICE_18
import com.cleevio.cinemax.api.module.pricecategoryitem.constant.PriceCategoryItemNumber.PRICE_19
import com.cleevio.cinemax.api.module.pricecategoryitem.constant.PriceCategoryItemNumber.PRICE_2
import com.cleevio.cinemax.api.module.pricecategoryitem.constant.PriceCategoryItemNumber.PRICE_20
import com.cleevio.cinemax.api.module.pricecategoryitem.constant.PriceCategoryItemNumber.PRICE_3
import com.cleevio.cinemax.api.module.pricecategoryitem.constant.PriceCategoryItemNumber.PRICE_4
import com.cleevio.cinemax.api.module.pricecategoryitem.constant.PriceCategoryItemNumber.PRICE_5
import com.cleevio.cinemax.api.module.pricecategoryitem.constant.PriceCategoryItemNumber.PRICE_6
import com.cleevio.cinemax.api.module.pricecategoryitem.constant.PriceCategoryItemNumber.PRICE_7
import com.cleevio.cinemax.api.module.pricecategoryitem.constant.PriceCategoryItemNumber.PRICE_8
import com.cleevio.cinemax.api.module.pricecategoryitem.constant.PriceCategoryItemNumber.PRICE_9
import com.cleevio.cinemax.api.module.pricecategoryitem.controller.dto.PriceCategoryItemSearchResponse
import com.cleevio.cinemax.api.module.pricecategoryitem.entity.PriceCategoryItem
import com.cleevio.cinemax.mssql.rps_cinemaxe.dbo.tables.pojos.Rvst
import java.math.BigDecimal
import java.util.UUID
import kotlin.test.assertEquals
import kotlin.test.assertTrue

fun mapToSyncCreateOrUpdatePriceCategoryCommand(category: PriceCategory) =
    SyncCreateOrUpdatePriceCategoryCommand(
        id = category.id,
        originalId = category.originalId!!,
        originalCode = category.originalCode!!,
        title = category.title,
        active = category.active
    )

fun createPriceCategory(
    id: UUID = UUID.randomUUID(),
    originalId: Int? = 1,
    originalCode: String? = "A",
    title: String? = "Do 17h",
    active: Boolean = true,
) = PriceCategory(
    id = id,
    originalId = originalId,
    originalCode = originalCode,
    title = title,
    active = active
)

fun mapToPriceCategorySearchResponse(
    priceCategory: PriceCategory,
    items: List<PriceCategoryItemSearchResponse>,
) = PriceCategorySearchResponse(
    id = priceCategory.id,
    title = priceCategory.title,
    items = items
)

fun mapToPriceCategoryModel(
    priceCategory: PriceCategory,
    items: List<PriceCategoryItem>,
) = PriceCategoryModel(
    id = priceCategory.id,
    title = priceCategory.title,
    items = items
        .sortedBy { it.number }
        .map {
            PriceCategoryItemModel(
                id = it.id,
                number = it.number,
                title = it.title,
                price = it.price
            )
        }
)

fun assertPriceCategoryEquals(
    expected: PriceCategory,
    actual: PriceCategorySearchResponse,
) {
    assertEquals(expected.id, actual.id)
    assertEquals(expected.title, actual.title)
}

fun assertPriceCategoryEqualsMssqlPriceCategoryMapping(
    expected: PriceCategory,
    actual: Rvst,
    items: Map<PriceCategoryItemNumber, PriceCategoryItem>,
) {
    assertEquals(expected.originalId, actual.rvstid.toInt())
    assertEquals(expected.originalCode, actual.kat.trimIfNotBlank())
    assertEquals(expected.title, actual.nazev.trimIfNotBlank())
    assertEquals(expected.active, actual.aktivni)

    assertTrue(items[PRICE_1]?.price.zeroIfNull().isEqualTo(actual.vst1))
    assertEquals(items[PRICE_1]?.title, actual.nazev1.trimIfNotBlank())
    assertTrue(items[PRICE_2]?.price.zeroIfNull() isEqualTo (actual.vst2))
    assertEquals(items[PRICE_2]?.title, actual.nazev2.trimIfNotBlank())
    assertEquals(if (items[PRICE_2]?.discounted == true) "W7" else "", actual.sleva2.trim())
    assertTrue(items[PRICE_3]?.price.zeroIfNull() isEqualTo (actual.vst3))
    assertEquals(items[PRICE_3]?.title, actual.nazev3.trimIfNotBlank())
    assertEquals(if (items[PRICE_3]?.discounted == true) "W7" else "", actual.sleva3.trim())
    assertTrue(items[PRICE_4]?.price.zeroIfNull() isEqualTo (actual.vst4))
    assertEquals(items[PRICE_4]?.title, actual.nazev4.trimIfNotBlank())
    assertEquals(if (items[PRICE_4]?.discounted == true) "W7" else "", actual.sleva4.trim())
    assertTrue(items[PRICE_5]?.price.zeroIfNull() isEqualTo (actual.vst5))
    assertEquals(items[PRICE_5]?.title, actual.nazev5.trimIfNotBlank())
    assertTrue(items[PRICE_6]?.price.zeroIfNull() isEqualTo (actual.vst6))
    assertEquals(items[PRICE_6]?.title, actual.nazev6.trimIfNotBlank())
    assertTrue(items[PRICE_7]?.price.zeroIfNull() isEqualTo (actual.vst7))
    assertEquals(items[PRICE_7]?.title, actual.nazev7.trimIfNotBlank())
    assertTrue(items[PRICE_8]?.price.zeroIfNull() isEqualTo (actual.vst8))
    assertEquals(items[PRICE_8]?.title, actual.nazev8.trimIfNotBlank())
    assertTrue(items[PRICE_9]?.price.zeroIfNull() isEqualTo (actual.vst9))
    assertEquals(items[PRICE_9]?.title, actual.nazev9.trimIfNotBlank())
    assertTrue(items[PRICE_10]?.price.zeroIfNull() isEqualTo (actual.vst10))
    assertEquals(items[PRICE_10]?.title, actual.nazev10.trimIfNotBlank())
    assertTrue(items[PRICE_11]?.price.zeroIfNull() isEqualTo (actual.vst11))
    assertEquals(items[PRICE_11]?.title, actual.nazev11.trimIfNotBlank())
    assertTrue(items[PRICE_12]?.price.zeroIfNull() isEqualTo (actual.vst12))
    assertEquals(items[PRICE_12]?.title, actual.nazev12.trimIfNotBlank())
    assertTrue(items[PRICE_13]?.price.zeroIfNull() isEqualTo (actual.vst13))
    assertEquals(items[PRICE_13]?.title, actual.nazev13.trimIfNotBlank())
    assertTrue(items[PRICE_14]?.price.zeroIfNull() isEqualTo (actual.vst14))
    assertEquals(items[PRICE_14]?.title, actual.nazev14.trimIfNotBlank())
    assertTrue(items[PRICE_15]?.price.zeroIfNull() isEqualTo (actual.vst15))
    assertEquals(items[PRICE_15]?.title, actual.nazev15.trimIfNotBlank())
    assertTrue(items[PRICE_16]?.price.zeroIfNull() isEqualTo (actual.vst16))
    assertEquals(items[PRICE_16]?.title, actual.nazev16.trimIfNotBlank())
    assertTrue(items[PRICE_17]?.price.zeroIfNull() isEqualTo (actual.vst17))
    assertEquals(items[PRICE_17]?.title, actual.nazev17.trimIfNotBlank())
    assertEquals(if (items[PRICE_17]?.discounted == true) "W7" else "", actual.sleva17.trim())
    assertTrue(items[PRICE_18]?.price.zeroIfNull() isEqualTo (actual.vst18))
    assertEquals(items[PRICE_18]?.title, actual.nazev18.trimIfNotBlank())
    assertEquals(if (items[PRICE_18]?.discounted == true) "W7" else "", actual.sleva18.trim())
    assertTrue(items[PRICE_19]?.price.zeroIfNull() isEqualTo (actual.vst19))
    assertEquals(items[PRICE_19]?.title, actual.nazev19.trimIfNotBlank())
    assertEquals(if (items[PRICE_19]?.discounted == true) "W7" else "", actual.sleva19.trim())
    assertTrue(items[PRICE_20]?.price.zeroIfNull() isEqualTo (actual.vst20))
    assertEquals(items[PRICE_20]?.title, actual.nazev20.trimIfNotBlank())
}

private fun BigDecimal?.zeroIfNull() = this ?: BigDecimal.ZERO
