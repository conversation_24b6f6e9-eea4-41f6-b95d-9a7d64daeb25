package com.cleevio.cinemax.api.util

import com.cleevio.cinemax.api.common.constant.STANDARD_TAX_RATE
import com.cleevio.cinemax.api.module.file.entity.File
import com.cleevio.cinemax.api.module.product.controller.dto.ProductSearchResponse
import com.cleevio.cinemax.api.module.productcategory.constant.ProductCategoryType
import com.cleevio.cinemax.api.module.productcategory.controller.dto.AdminSearchProductCategoriesResponse
import com.cleevio.cinemax.api.module.productcategory.controller.dto.ProductCategorySearchResponse
import com.cleevio.cinemax.api.module.productcategory.entity.ProductCategory
import com.cleevio.cinemax.api.module.productcategory.service.command.CreateOrUpdateProductCategoryCommand
import com.cleevio.cinemax.api.module.productcategory.service.command.MessagingCreateOrUpdateProductCategoryCommand
import com.cleevio.cinemax.api.truncatedToSeconds
import com.cleevio.cinemax.mssql.rp_bufete.dbo.tables.pojos.Rcdme
import java.util.Optional
import java.util.UUID
import kotlin.jvm.optionals.getOrNull
import kotlin.test.assertEquals
import kotlin.test.assertFalse
import kotlin.test.assertNotNull
import kotlin.test.assertTrue

fun mapToCreateOrUpdateProductCategoryCommand(category: ProductCategory) =
    CreateOrUpdateProductCategoryCommand(
        id = category.id,
        originalId = category.originalId,
        code = category.code,
        title = category.title,
        type = category.type,
        order = category.order?.let { Optional.of(it) },
        taxRate = category.taxRate,
        hexColorCode = category.hexColorCode,
        imageFileId = category.imageFileId?.let { Optional.of(it) }
    )

fun createProductCategory(
    originalId: Int? = 1,
    code: String = "0$originalId",
    title: String = "Popcorn",
    type: ProductCategoryType = ProductCategoryType.PRODUCT,
    order: Int? = null,
    taxRate: Int = STANDARD_TAX_RATE,
    hexColorCode: String = "#000000",
    imageFileId: UUID? = null,
) = ProductCategory(
    id = UUID.randomUUID(),
    originalId = originalId,
    code = code,
    title = title,
    type = type,
    order = order,
    taxRate = taxRate,
    hexColorCode = hexColorCode,
    imageFileId = imageFileId
)

fun mapToProductCategorySearchResponse(
    productCategory: ProductCategory,
    products: List<ProductSearchResponse>,
) = ProductCategorySearchResponse(
    id = productCategory.id,
    title = productCategory.title,
    type = productCategory.type,
    order = productCategory.order,
    hexColorCode = productCategory.hexColorCode,
    products = products
)

fun assertProductCategoryEquals(
    expected: ProductCategory,
    actual: ProductCategorySearchResponse,
) {
    assertEquals(expected.id, actual.id)
    assertEquals(expected.title, actual.title)
    assertEquals(expected.type, actual.type)
    assertEquals(expected.order, actual.order)
    assertEquals(expected.hexColorCode, actual.hexColorCode)
}

fun assertAdminSearchProductCategoriesResponseEquals(
    expected: ProductCategory,
    actual: AdminSearchProductCategoriesResponse,
    expectedFile: File?,
) {
    assertEquals(expected.id, actual.id)
    assertEquals(expected.code, actual.code)
    assertEquals(expected.title, actual.title)
    assertEquals(expected.order, actual.order)
    assertEquals(expected.taxRate, actual.taxRate)
    assertEquals(expected.hexColorCode, actual.hexColorCode)
    expectedFile?.let {
        assertEquals(it.id, actual.imageFile?.id)
        assertEquals(
            "https://example.com/manager-app/files/product_category_image/${expectedFile.getFilename()}",
            actual.imageFile?.url
        )
    }
    assertEquals(expected.createdAt.truncatedToSeconds(), actual.createdAt.truncatedToSeconds())
    assertEquals(expected.updatedAt.truncatedToSeconds(), actual.updatedAt.truncatedToSeconds())
}

fun assertProductCategoryToMssqlProductCategoryMapping(
    expected: ProductCategory,
    expectedOriginalId: Int,
    expectedColor: Int,
    expectedFile: File? = null,
    actual: Rcdme,
) {
    assertEquals(expectedOriginalId, actual.rcdmeid)
    assertEquals(expected.code, actual.druh.trim())
    assertEquals(expected.title, actual.nazev.trim())
    if (expected.type == ProductCategoryType.DISCOUNT) {
        assertTrue(actual.lsleva)
    } else {
        assertFalse(actual.lsleva)
    }
    if (expected.order == null) {
        assertEquals(0, actual.pc)
    } else {
        assertEquals(expected.order, actual.pc)
    }
    assertEquals(expected.taxRate.toShort(), actual.dan)
    assertEquals(expectedColor, actual.barva)
    if (expectedFile == null) {
        assertEquals("", actual.obrazek.trim())
    } else {
        assertEquals(expectedFile.getFilename(), actual.obrazek.trim())
    }
}

fun mapToMessagingCreateOrUpdateProductCategoryCommand(
    productCategory: ProductCategory,
    imageFileId: UUID?,
) = MessagingCreateOrUpdateProductCategoryCommand(
    code = productCategory.code,
    title = productCategory.title,
    type = productCategory.type,
    order = Optional.ofNullable(productCategory.order),
    taxRate = productCategory.taxRate,
    hexColorCode = productCategory.hexColorCode,
    imageFileId = imageFileId
)

fun assertCommandToProductCategoryMapping(
    expected: CreateOrUpdateProductCategoryCommand,
    actual: ProductCategory,
    expectedCode: String? = null,
) {
    assertEquals(expected.originalId, actual.originalId)
    if (expected.code != null) {
        assertEquals(expected.code, actual.code)
    } else {
        expectedCode?.let { assertEquals(expectedCode, actual.code) } ?: run { assertNotNull(actual.code) }
    }
    assertEquals(expected.title, actual.title)
    assertEquals(expected.order?.getOrNull(), actual.order)
    assertEquals(expected.taxRate, actual.taxRate)
    assertEquals(expected.hexColorCode, actual.hexColorCode)
    assertNotNull(actual.createdAt)
    assertNotNull(actual.updatedAt)
}
