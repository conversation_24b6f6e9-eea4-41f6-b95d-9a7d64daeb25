package com.cleevio.cinemax.api.util

import com.cleevio.cinemax.api.module.productcomponent.entity.ProductComponent
import com.cleevio.cinemax.api.module.stocktaking.controller.dto.AdminSearchStockTakingsResponse
import com.cleevio.cinemax.api.module.stocktaking.entity.StockTaking
import com.cleevio.cinemax.api.module.stocktaking.service.command.CreateOrUpdateStockTakingCommand
import com.cleevio.cinemax.api.module.stocktaking.service.model.StockTakingExportRecordModel
import java.math.BigDecimal
import java.util.UUID
import kotlin.test.assertEquals
import kotlin.test.assertNotNull

fun mapToCreateOrUpdateStockTakingCommand(stockTaking: StockTaking) =
    CreateOrUpdateStockTakingCommand(
        id = stockTaking.id,
        originalId = stockTaking.originalId!!,
        productComponentId = stockTaking.productComponentId,
        stockQuantity = stockTaking.stockQuantity,
        stockQuantityActual = stockTaking.stockQuantityActual,
        stockQuantityDifference = stockTaking.stockQuantityDifference,
        purchasePrice = stockTaking.purchasePrice,
        purchasePriceDifference = stockTaking.purchasePriceDifference
    )

fun createStockTaking(
    id: UUID? = null,
    originalId: Int? = 1,
    productComponentId: UUID,
    stockQuantity: BigDecimal = 100.toBigDecimal(),
    stockQuantityActual: BigDecimal = stockQuantity.minus(BigDecimal.TEN),
    stockQuantityDifference: BigDecimal = (-10).toBigDecimal(),
    purchasePrice: BigDecimal,
    purchasePriceDifference: BigDecimal = purchasePrice.multiply(stockQuantityDifference),
) = StockTaking(
    id = id ?: UUID.randomUUID(),
    originalId = originalId,
    productComponentId = productComponentId,
    stockQuantity = stockQuantity,
    stockQuantityActual = stockQuantityActual,
    stockQuantityDifference = stockQuantityDifference,
    purchasePrice = purchasePrice,
    purchasePriceDifference = purchasePriceDifference
)

fun assertStockTakingToSearchResponseEquals(
    expectedStockTaking: StockTaking,
    expectedProductComponent: ProductComponent,
    actual: AdminSearchStockTakingsResponse,
) {
    assertEquals(expectedStockTaking.id, actual.id)
    assertEquals(expectedStockTaking.stockQuantity, actual.stockQuantity)
    assertEquals(expectedStockTaking.stockQuantityActual, actual.stockQuantityActual)
    assertEquals(expectedStockTaking.stockQuantityDifference, actual.stockQuantityDifference)
    assertEquals(expectedStockTaking.purchasePrice, actual.purchasePrice)
    assertEquals(expectedStockTaking.purchasePriceDifference, actual.purchasePriceDifference)
    assertEquals(expectedProductComponent.id, actual.productComponent.id)
    assertEquals(expectedProductComponent.title, actual.productComponent.title)
    assertEquals(expectedProductComponent.code, actual.productComponent.originalCode)
    assertEquals(expectedProductComponent.unit, actual.productComponent.unit)
    assertNotNull(actual.createdAt)
    assertNotNull(actual.updatedAt)
}

fun assertStockTakingToExportModelEquals(
    expectedStockTaking: StockTaking,
    expectedProductComponent: ProductComponent,
    actual: StockTakingExportRecordModel,
) {
    assertEquals(expectedProductComponent.code, actual.productComponentCode)
    assertEquals(expectedProductComponent.title, actual.productComponentTitle)
    assertEquals(expectedProductComponent.unit, actual.unit)
    assertEquals(expectedStockTaking.stockQuantity, actual.stockQuantity)
    assertEquals(expectedStockTaking.stockQuantityActual, actual.stockQuantityActual)
    assertEquals(expectedStockTaking.stockQuantityDifference, actual.stockQuantityDifference)
    assertEquals(expectedStockTaking.purchasePrice, actual.purchasePrice)
    assertEquals(expectedStockTaking.purchasePriceDifference, actual.purchasePriceDifference)
}
