package com.cleevio.cinemax.api.util

import com.cleevio.cinemax.api.common.util.trimIfNotBlank
import com.cleevio.cinemax.api.module.supplier.controller.dto.AdminSearchSuppliersResponse
import com.cleevio.cinemax.api.module.supplier.entity.Supplier
import com.cleevio.cinemax.api.module.supplier.service.command.CreateOrUpdateSupplierCommand
import com.cleevio.cinemax.api.module.supplier.service.command.MessagingCreateOrUpdateSupplierCommand
import com.cleevio.cinemax.api.truncatedToSeconds
import com.cleevio.cinemax.mssql.rp_bufete.dbo.tables.pojos.Rdod
import java.util.Optional
import java.util.UUID
import kotlin.jvm.optionals.getOrNull
import kotlin.test.assertEquals
import kotlin.test.assertNotNull

fun mapToCreateOrUpdateSupplierCommand(supplier: Supplier) = CreateOrUpdateSupplierCommand(
    id = supplier.id,
    originalId = supplier.originalId,
    code = supplier.code,
    title = supplier.title,
    addressStreet = supplier.addressStreet?.let { Optional.of(it) },
    addressCity = supplier.addressCity?.let { Optional.of(it) },
    addressPostCode = supplier.addressPostCode?.let { Optional.of(it) },
    contactName = supplier.contactName?.let { Optional.of(it) },
    contactPhone = supplier.contactPhone?.let { Optional.of(it) },
    contactEmails = supplier.contactEmails.let { Optional.of(it) },
    bankName = supplier.bankName?.let { Optional.of(it) },
    bankAccount = supplier.bankAccount?.let { Optional.of(it) },
    idNumber = supplier.idNumber?.let { Optional.of(it) },
    taxIdNumber = supplier.taxIdNumber?.let { Optional.of(it) }
)

fun createSupplier(
    id: UUID = UUID.randomUUID(),
    originalId: Int? = 1,
    code: String = "SUP${originalId ?: 0}",
    title: String = "Best supplies ever, s.r.o.",
    addressStreet: String? = "Supply Street 58",
    addressCity: String? = "Prague",
    addressPostCode: String? = "150 00",
    contactName: String? = "John Doe",
    contactPhone: String? = "+************",
    contactEmails: Set<String> = setOf("<EMAIL>", "<EMAIL>"),
    bankName: String? = "AirBank, a.s.",
    bankAccount: String? = "*********/0300",
    idNumber: String? = "********",
    taxIdNumber: String? = "CZ2022916731",
    entityModifier: (Supplier) -> Unit = {},
) = Supplier(
    id = id,
    originalId = originalId,
    code = code,
    title = title,
    addressStreet = addressStreet,
    addressCity = addressCity,
    addressPostCode = addressPostCode,
    contactName = contactName,
    contactPhone = contactPhone,
    contactEmails = contactEmails,
    bankName = bankName,
    bankAccount = bankAccount,
    idNumber = idNumber,
    taxIdNumber = taxIdNumber
).apply(entityModifier)

fun assertCommandToSupplierMapping(expected: CreateOrUpdateSupplierCommand, actual: Supplier, expectedCode: String? = null) {
    assertEquals(expected.originalId, actual.originalId)
    if (expected.code != null) {
        assertEquals(expected.code, actual.code)
    } else {
        expectedCode?.let { assertEquals(expectedCode, actual.code) } ?: run { assertNotNull(actual.code) }
    }
    assertEquals(expected.title, actual.title)
    assertEquals(expected.addressStreet?.getOrNull(), actual.addressStreet)
    assertEquals(expected.addressCity?.getOrNull(), actual.addressCity)
    assertEquals(expected.addressPostCode?.getOrNull(), actual.addressPostCode)
    assertEquals(expected.contactName?.getOrNull(), actual.contactName)
    assertEquals(expected.contactPhone?.getOrNull(), actual.contactPhone)
    assertEquals(expected.contactEmails?.getOrNull(), actual.contactEmails)
    assertEquals(expected.bankName?.getOrNull(), actual.bankName)
    assertEquals(expected.bankAccount?.getOrNull(), actual.bankAccount)
    assertEquals(expected.idNumber?.getOrNull(), actual.idNumber)
    assertEquals(expected.taxIdNumber?.getOrNull(), actual.taxIdNumber)
}

fun assertSupplierToMssqlSupplierEquals(expected: Supplier, actual: Rdod) {
    assertEquals(expected.originalId, actual.rdodid)
    assertEquals(expected.code, actual.cisdod.trimEnd())
    assertEquals(expected.title, actual.nazev.trimIfNotBlank())
    assertEquals(expected.addressStreet, actual.ulice.trimIfNotBlank())
    assertEquals(expected.addressCity, actual.misto.trimIfNotBlank())
    assertEquals(expected.addressPostCode, actual.psc.trimIfNotBlank())
    assertEquals(expected.contactName, actual.jmeno1.trimIfNotBlank())
    assertEquals(expected.contactPhone, actual.telefon1.trimIfNotBlank())
    assertEquals(expected.contactEmails, parseEmails(actual.email))
    assertEquals(expected.bankName, actual.banka.trimIfNotBlank())
    assertEquals(expected.bankAccount, actual.ucet.trimIfNotBlank())
    assertEquals(expected.idNumber, actual.ico.trimIfNotBlank())
    assertEquals(expected.taxIdNumber, actual.dic.trimIfNotBlank())
}

fun assertSuppliertoSearchSupplierResponseEquals(expected: Supplier, actual: AdminSearchSuppliersResponse) {
    assertEquals(expected.id, actual.id)
    assertEquals(expected.code, actual.code)
    assertEquals(expected.title, actual.title)
    assertEquals(expected.createdAt.truncatedToSeconds(), actual.createdAt.truncatedToSeconds())
    assertEquals(expected.updatedAt.truncatedToSeconds(), actual.updatedAt.truncatedToSeconds())
}

fun mapToMessagingCreateOrUpdateSupplierCommand(supplier: Supplier) = MessagingCreateOrUpdateSupplierCommand(
    code = supplier.code,
    title = supplier.title,
    addressStreet = supplier.addressStreet?.let { Optional.of(it) },
    addressCity = supplier.addressCity?.let { Optional.of(it) },
    addressPostCode = supplier.addressPostCode?.let { Optional.of(it) },
    contactName = supplier.contactName?.let { Optional.of(it) },
    contactPhone = supplier.contactPhone?.let { Optional.of(it) },
    contactEmails = supplier.contactEmails.let { Optional.of(it) },
    bankName = supplier.bankName?.let { Optional.of(it) },
    bankAccount = supplier.bankAccount?.let { Optional.of(it) },
    idNumber = supplier.idNumber?.let { Optional.of(it) },
    taxIdNumber = supplier.taxIdNumber?.let { Optional.of(it) }
)

fun assertMessagingSupplierCommandToSupplier(expected: MessagingCreateOrUpdateSupplierCommand, actual: Supplier) {
    check(actual.code == expected.code)
    check(actual.title == expected.title)
    check(actual.addressStreet == expected.addressStreet?.getOrNull())
    check(actual.addressCity == expected.addressCity?.getOrNull())
    check(actual.addressPostCode == expected.addressPostCode?.getOrNull())
    check(actual.contactName == expected.contactName?.getOrNull())
    check(actual.contactPhone == expected.contactPhone?.getOrNull())
    check(actual.contactEmails == expected.contactEmails?.orElse(emptySet()))
    check(actual.bankName == expected.bankName?.getOrNull())
    check(actual.bankAccount == expected.bankAccount?.getOrNull())
    check(actual.idNumber == expected.idNumber?.getOrNull())
    check(actual.taxIdNumber == expected.taxIdNumber?.getOrNull())
}
