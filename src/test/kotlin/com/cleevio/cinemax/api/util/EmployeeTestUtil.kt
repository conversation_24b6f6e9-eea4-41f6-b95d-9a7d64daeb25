package com.cleevio.cinemax.api.util

import com.cleevio.cinemax.api.module.employee.constant.EmployeeRole
import com.cleevio.cinemax.api.module.employee.controller.dto.EmployeeSearchResponse
import com.cleevio.cinemax.api.module.employee.entity.Employee
import com.cleevio.cinemax.api.module.employee.service.command.CreateOrUpdateEmployeeCommand
import java.time.LocalDateTime
import java.util.UUID
import kotlin.test.assertEquals
import kotlin.test.assertNotNull

fun mapToCreateOrUpdateEmployeeCommand(employee: Employee) =
    CreateOrUpdateEmployeeCommand(
        id = employee.id,
        originalId = employee.originalId,
        originalBuffetId = employee.originalId,
        username = employee.username,
        fullName = employee.fullName,
        posName = employee.posName,
        password = DUMMY_PASSWORD,
        passwordReset = employee.passwordReset,
        role = employee.role,
        accessibleAt = employee.accessibleAt
    )

fun createEmployee(
    originalId: Int? = 1,
    username: String,
    fullName: String = "Full Name",
    posName: String? = "pokl1",
    passwordHash: String = "xxxxyyyyzzzz",
    passwordReset: Boolean = false,
    role: EmployeeRole = EmployeeRole.CASHIER,
    accessibleAt: LocalDateTime? = null,
) = Employee(
    id = UUID.randomUUID(),
    originalId = originalId,
    originalBuffetId = originalId,
    username = username,
    fullName = fullName,
    posName = posName,
    passwordHash = passwordHash,
    passwordReset = passwordReset,
    role = role,
    accessibleAt = accessibleAt
)

fun assertEmployeeEquals(expected: Employee, actual: Employee) {
    assertEquals(expected.originalId, actual.originalId)
    assertEquals(expected.originalBuffetId, actual.originalBuffetId)
    assertEquals(expected.username, actual.username)
    assertEquals(expected.fullName, actual.fullName)
    assertEquals(expected.posName, actual.posName)
    // dummy password hash is set to Employee objects, these objects are mapped to commands (with dummy plaintext password)
    // -> correct password hash cannot be obtained, passwordHash assertion is skipped
    assertEquals(expected.passwordReset, actual.passwordReset)
    assertEquals(expected.role, actual.role)
    assertEquals(expected.lastLoginAt, actual.lastLoginAt)
    assertEquals(expected.accessibleAt, actual.accessibleAt)
    assertEquals(expected.deletedAt, actual.deletedAt)
    assertNotNull(actual.createdAt)
    assertNotNull(actual.updatedAt)
}

const val DUMMY_PASSWORD = "dummyPass"

fun mapToEmployeeResponse(employee: Employee) = EmployeeSearchResponse(
    id = employee.id,
    username = employee.username,
    fullName = employee.fullName,
    posName = employee.posName,
    role = employee.role,
    lastLoginAt = employee.lastLoginAt,
    accessibleAt = employee.accessibleAt,
    createdAt = employee.createdAt,
    updatedAt = employee.updatedAt
)
