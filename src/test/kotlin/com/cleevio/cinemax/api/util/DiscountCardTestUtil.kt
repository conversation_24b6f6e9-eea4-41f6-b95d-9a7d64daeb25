package com.cleevio.cinemax.api.util

import com.cleevio.cinemax.api.module.discountcard.constant.DiscountCardType
import com.cleevio.cinemax.api.module.discountcard.controller.dto.DiscountCardResponse
import com.cleevio.cinemax.api.module.discountcard.entity.DiscountCard
import com.cleevio.cinemax.api.module.discountcard.service.command.CreateOrUpdateDiscountCardCommand
import com.cleevio.cinemax.api.module.product.constant.ProductType
import com.cleevio.cinemax.api.module.productdiscount.constant.ProductDiscountType
import com.cleevio.cinemax.api.module.productdiscount.controller.dto.ProductDiscountResponse
import com.cleevio.cinemax.api.module.productdiscount.controller.dto.ProductResponse
import com.cleevio.cinemax.api.module.ticketdiscount.constant.TicketDiscountType
import com.cleevio.cinemax.api.module.ticketdiscount.constant.TicketDiscountUsageType
import com.cleevio.cinemax.api.module.ticketdiscount.controller.dto.TicketDiscountResponse
import java.time.LocalDate
import java.util.UUID
import kotlin.test.assertEquals

fun mapToCreateOrUpdateDiscountCardCommand(discountCard: DiscountCard) = CreateOrUpdateDiscountCardCommand(
    id = discountCard.id,
    originalId = discountCard.originalId,
    ticketDiscountId = discountCard.ticketDiscountId,
    productDiscountId = discountCard.productDiscountId,
    productId = discountCard.productId,
    type = discountCard.type,
    title = discountCard.title,
    code = discountCard.code,
    validFrom = discountCard.validFrom,
    validUntil = discountCard.validUntil,
    applicableToBasket = discountCard.applicableToBasket,
    applicableToScreening = discountCard.applicableToScreening,
    applicableToScreeningsPerDay = discountCard.applicableToScreeningsPerDay
)

fun createDiscountCard(
    id: UUID = UUID.randomUUID(),
    originalId: Int = 1,
    ticketDiscountId: UUID? = null,
    productDiscountId: UUID? = null,
    productId: UUID? = null,
    type: DiscountCardType = DiscountCardType.CARD,
    title: String = "dummyTitle",
    code: String = "123456789",
    validFrom: LocalDate = LocalDate.now(),
    validUntil: LocalDate = LocalDate.now().plusYears(1),
    applicableToBasket: Int? = 1,
    applicableToScreening: Int? = 1,
    applicableToScreeningsPerDay: Int? = 5,
    productsCount: Int? = 2,
) = DiscountCard(
    id = id,
    originalId = originalId,
    ticketDiscountId = ticketDiscountId,
    productDiscountId = productDiscountId,
    productId = productId,
    type = type,
    title = title,
    code = code,
    validFrom = validFrom,
    validUntil = validUntil,
    applicableToBasket = applicableToBasket,
    applicableToScreening = applicableToScreening,
    applicableToScreeningsPerDay = applicableToScreeningsPerDay,
    productsCount = productsCount
)

fun mapToDiscountCardResponse(discountCard: DiscountCard) = DiscountCardResponse(
    id = discountCard.id,
    type = discountCard.type,
    title = discountCard.title,
    code = discountCard.code,
    validFrom = discountCard.validFrom,
    validUntil = discountCard.validUntil,
    ticketDiscount = TicketDiscountResponse(
        id = UUID.randomUUID(),
        type = TicketDiscountType.ABSOLUTE,
        usageType = TicketDiscountUsageType.PRIMARY,
        title = "1€",
        code = "01"
    ),
    productDiscount = ProductDiscountResponse(
        id = UUID.randomUUID(),
        title = "Sleva 10%",
        type = ProductDiscountType.PERCENTAGE
    ),
    product = ProductResponse(
        id = UUID.randomUUID(),
        title = "Popcorn XXL",
        type = ProductType.PRODUCT,
        price = 0.toBigDecimal()
    )
)

fun assertCommandEquals(command: CreateOrUpdateDiscountCardCommand, expected: CreateOrUpdateDiscountCardCommand) {
    assertEquals(command.originalId, expected.originalId)
    assertEquals(command.ticketDiscountId, expected.ticketDiscountId)
    assertEquals(command.productDiscountId, expected.productDiscountId)
    assertEquals(command.productId, expected.productId)
    assertEquals(command.type, expected.type)
    assertEquals(command.title, expected.title)
    assertEquals(command.code, expected.code)
    assertEquals(command.validFrom, expected.validFrom)
    assertEquals(command.validUntil, expected.validUntil)
    assertEquals(command.applicableToBasket, expected.applicableToBasket)
    assertEquals(command.applicableToScreening, expected.applicableToScreening)
    assertEquals(command.applicableToScreeningsPerDay, expected.applicableToScreeningsPerDay)
    assertEquals(command.productsCount, expected.productsCount)
}
