package com.cleevio.cinemax.api.util

import com.cleevio.cinemax.api.module.file.constant.FileType
import com.cleevio.cinemax.api.module.file.entity.File
import com.cleevio.cinemax.api.module.file.service.command.CreateOrUpdateFileCommand
import java.io.InputStream
import java.util.UUID

fun mapToCreateOrUpdateFileCommand(
    file: File,
    inputStream: InputStream,
) = CreateOrUpdateFileCommand(
    id = file.id,
    originalId = file.originalId!!,
    type = file.type,
    originalName = file.originalName!!,
    extension = file.extension,
    inputStream = inputStream
)

fun createFile(
    id: UUID = UUID.randomUUID(),
    originalId: Int? = 1,
    type: FileType = FileType.PRODUCT_IMAGE,
    originalName: String?,
    extension: String = "jpg",
) = File(
    id = id,
    originalId = originalId,
    type = type,
    originalName = originalName,
    extension = extension
)
