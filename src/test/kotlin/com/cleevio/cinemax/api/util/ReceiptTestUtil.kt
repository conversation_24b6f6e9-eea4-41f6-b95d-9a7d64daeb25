package com.cleevio.cinemax.api.util

import com.cleevio.cinemax.api.module.receipt.constant.ReceiptPrintState
import com.cleevio.cinemax.api.module.receipt.constant.ReceiptType
import com.cleevio.cinemax.api.module.receipt.entity.Receipt
import java.util.UUID

fun createReceipt(
    basketId: UUID = UUID.randomUUID(),
    directory: String = "/pos/receipts",
    content: String = "<xml><content>Test content</content></xml>",
    lastPrintState: ReceiptPrintState? = null,
    type: ReceiptType = ReceiptType.RECEIPT,
) = Receipt(
    basketId = basketId,
    directory = directory,
    content = content,
    lastPrintState = lastPrintState,
    type = type
)
