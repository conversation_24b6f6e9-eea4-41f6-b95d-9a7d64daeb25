package com.cleevio.cinemax.api.util

import com.cleevio.cinemax.api.common.constant.JwtClaim
import com.cleevio.cinemax.api.common.constant.TokenType
import com.cleevio.cinemax.api.module.employee.constant.EmployeeRole
import io.jsonwebtoken.Jwts
import io.jsonwebtoken.SignatureAlgorithm
import io.jsonwebtoken.security.Keys
import java.util.Date

fun mockAccessToken(role: EmployeeRole = EmployeeRole.CASHIER): String = mockJwtToken(
    type = TokenType.ACCESS,
    role = role
)
fun mockRefreshToken(role: EmployeeRole = EmployeeRole.CASHIER): String = mockJwtToken(
    type = TokenType.REFRESH,
    role = role
)

private fun mockJwtToken(
    type: TokenType = TokenType.ACCESS,
    principal: String = TEST_PRINCIPAL_USERNAME,
    role: EmployeeRole,
    expirationMs: Int = 900000,
): String {
    val jwt = Jwts.builder()
        .setClaims(
            mapOf(
                JwtClaim.TYPE_CLAIM to type,
                JwtClaim.ROLES_CLAIM to listOf(role)
            )
        )
        .setSubject(principal)
        .setIssuedAt(Date(System.currentTimeMillis()))
        .setExpiration(Date(System.currentTimeMillis() + expirationMs))
        .signWith(Keys.hmacShaKeyFor(TEST_DECODED_SIGNING_KEY.toByteArray()), SignatureAlgorithm.HS256)
        .compact()
    return "Bearer $jwt"
}

private const val TEST_DECODED_SIGNING_KEY = "rqWZCXbGkzNi5ApHuyK3WXUikdUeEpsGDDDwMTZcSQU6ZnHzxZMoLCTkyCf9RJcy"
const val TEST_PRINCIPAL_USERNAME = "anonymous"
