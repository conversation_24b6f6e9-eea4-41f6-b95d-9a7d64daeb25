package com.cleevio.cinemax.api.util

import com.cleevio.cinemax.api.common.util.setNullIfZero
import com.cleevio.cinemax.api.common.util.trimIfNotBlank
import com.cleevio.cinemax.api.module.distributor.entity.Distributor
import com.cleevio.cinemax.api.module.distributor.service.command.CreateOrUpdateDistributorCommand
import com.cleevio.cinemax.api.module.distributor.service.command.MessagingCreateOrUpdateDistributorCommand
import com.cleevio.cinemax.mssql.rps_cinemaxe.dbo.tables.pojos.Rdistr
import java.time.LocalDateTime
import java.time.temporal.ChronoUnit
import java.util.Optional
import java.util.UUID
import kotlin.jvm.optionals.getOrNull
import kotlin.test.assertEquals
import kotlin.test.assertNotNull

fun mapToCreateOrUpdateDistributorCommand(distributor: Distributor) = CreateOrUpdateDistributorCommand(
    id = distributor.id,
    originalId = distributor.originalId,
    code = distributor.code,
    disfilmCode = distributor.disfilmCode?.let { Optional.of(it) },
    title = distributor.title,
    addressStreet = distributor.addressStreet?.let { Optional.of(it) },
    addressCity = distributor.addressCity?.let { Optional.of(it) },
    addressPostCode = distributor.addressPostCode?.let { Optional.of(it) },
    contactName1 = distributor.contactName1?.let { Optional.of(it) },
    contactName2 = distributor.contactName2?.let { Optional.of(it) },
    contactName3 = distributor.contactName3?.let { Optional.of(it) },
    contactPhone1 = distributor.contactPhone1?.let { Optional.of(it) },
    contactPhone2 = distributor.contactPhone2?.let { Optional.of(it) },
    contactPhone3 = distributor.contactPhone3?.let { Optional.of(it) },
    contactEmails = distributor.contactEmails.let { Optional.of(it) },
    bankName = distributor.bankName?.let { Optional.of(it) },
    bankAccount = distributor.bankAccount?.let { Optional.of(it) },
    idNumber = distributor.idNumber?.let { Optional.of(it) },
    taxIdNumber = distributor.taxIdNumber?.let { Optional.of(it) },
    vatRate = distributor.vatRate?.let { Optional.of(it) },
    note = distributor.note?.let { Optional.of(it) }
)

fun mapToMessagingCreateOrUpdateDistributorCommand(distributor: Distributor) = MessagingCreateOrUpdateDistributorCommand(
    code = distributor.code,
    disfilmCode = distributor.disfilmCode?.let { Optional.of(it) },
    title = distributor.title,
    addressStreet = distributor.addressStreet?.let { Optional.of(it) },
    addressCity = distributor.addressCity?.let { Optional.of(it) },
    addressPostCode = distributor.addressPostCode?.let { Optional.of(it) },
    contactName1 = distributor.contactName1?.let { Optional.of(it) },
    contactName2 = distributor.contactName2?.let { Optional.of(it) },
    contactName3 = distributor.contactName3?.let { Optional.of(it) },
    contactPhone1 = distributor.contactPhone1?.let { Optional.of(it) },
    contactPhone2 = distributor.contactPhone2?.let { Optional.of(it) },
    contactPhone3 = distributor.contactPhone3?.let { Optional.of(it) },
    contactEmails = distributor.contactEmails.let { Optional.of(it) },
    bankName = distributor.bankName?.let { Optional.of(it) },
    bankAccount = distributor.bankAccount?.let { Optional.of(it) },
    idNumber = distributor.idNumber?.let { Optional.of(it) },
    taxIdNumber = distributor.taxIdNumber?.let { Optional.of(it) },
    vatRate = distributor.vatRate?.let { Optional.of(it) },
    note = distributor.note?.let { Optional.of(it) }
)

fun createDistributor(
    id: UUID = UUID.randomUUID(),
    originalId: Int? = 1,
    code: String = "OC${originalId ?: 0}",
    disfilmCode: String = "DF${originalId ?: 0}",
    title: String = "Best movies ever, s.r.o.",
    addressStreet: String? = "Filmová 58",
    addressCity: String? = "Praha",
    addressPostCode: String? = "150 00",
    contactName1: String? = "John",
    contactName2: String? = "Jerry",
    contactName3: String? = "Tom",
    contactPhone1: String? = "+************",
    contactPhone2: String? = "+************",
    contactPhone3: String? = "*********",
    contactEmails: Set<String> = setOf("<EMAIL>", "<EMAIL>"),
    bankName: String? = "AirBank, a.s.",
    bankAccount: String? = "**********/0300",
    idNumber: String? = "********",
    taxIdNumber: String? = "SK2022916731",
    vatRate: Int? = 20,
    note: String? = "Best note ever",
    entityModifier: (Distributor) -> Unit = {},
) = Distributor(
    id = id,
    originalId = originalId,
    code = code,
    disfilmCode = disfilmCode,
    title = title,
    addressStreet = addressStreet,
    addressCity = addressCity,
    addressPostCode = addressPostCode,
    contactName1 = contactName1,
    contactName2 = contactName2,
    contactName3 = contactName3,
    contactPhone1 = contactPhone1,
    contactPhone2 = contactPhone2,
    contactPhone3 = contactPhone3,
    contactEmails = contactEmails,
    bankName = bankName,
    bankAccount = bankAccount,
    idNumber = idNumber,
    taxIdNumber = taxIdNumber,
    vatRate = vatRate,
    note = note
).apply(entityModifier)

fun assertCommandToDistributorMapping(expected: CreateOrUpdateDistributorCommand, actual: Distributor, expectedCode: String? = null) {
    assertEquals(expected.originalId, actual.originalId)
    if (expected.code != null) {
        assertEquals(expected.code, actual.code)
    } else {
        expectedCode?.let { assertEquals(expectedCode, actual.code) } ?: run { assertNotNull(actual.code) }
    }
    assertEquals(expected.disfilmCode?.getOrNull(), actual.disfilmCode)
    assertEquals(expected.title, actual.title)
    assertEquals(expected.addressStreet?.getOrNull(), actual.addressStreet)
    assertEquals(expected.addressCity?.getOrNull(), actual.addressCity)
    assertEquals(expected.addressPostCode?.getOrNull(), actual.addressPostCode)
    assertEquals(expected.contactName1?.getOrNull(), actual.contactName1)
    assertEquals(expected.contactName2?.getOrNull(), actual.contactName2)
    assertEquals(expected.contactName3?.getOrNull(), actual.contactName3)
    assertEquals(expected.contactPhone1?.getOrNull(), actual.contactPhone1)
    assertEquals(expected.contactPhone2?.getOrNull(), actual.contactPhone2)
    assertEquals(expected.contactPhone3?.getOrNull(), actual.contactPhone3)
    assertEquals(expected.contactEmails?.getOrNull(), actual.contactEmails)
    assertEquals(expected.bankName?.getOrNull(), actual.bankName)
    assertEquals(expected.bankAccount?.getOrNull(), actual.bankAccount)
    assertEquals(expected.idNumber?.getOrNull(), actual.idNumber)
    assertEquals(expected.taxIdNumber?.getOrNull(), actual.taxIdNumber)
    assertEquals(expected.vatRate?.getOrNull(), actual.vatRate)
    assertEquals(expected.note?.getOrNull(), actual.note)
}

fun assertMessagingCommandToDistributorMapping(expected: MessagingCreateOrUpdateDistributorCommand, actual: Distributor) {
    assertEquals(expected.code, actual.code)
    assertEquals(expected.disfilmCode?.getOrNull(), actual.disfilmCode)
    assertEquals(expected.title, actual.title)
    assertEquals(expected.addressStreet?.getOrNull(), actual.addressStreet)
    assertEquals(expected.addressCity?.getOrNull(), actual.addressCity)
    assertEquals(expected.addressPostCode?.getOrNull(), actual.addressPostCode)
    assertEquals(expected.contactName1?.getOrNull(), actual.contactName1)
    assertEquals(expected.contactName2?.getOrNull(), actual.contactName2)
    assertEquals(expected.contactName3?.getOrNull(), actual.contactName3)
    assertEquals(expected.contactPhone1?.getOrNull(), actual.contactPhone1)
    assertEquals(expected.contactPhone2?.getOrNull(), actual.contactPhone2)
    assertEquals(expected.contactPhone3?.getOrNull(), actual.contactPhone3)
    assertEquals(expected.contactEmails?.getOrNull(), actual.contactEmails)
    assertEquals(expected.bankName?.getOrNull(), actual.bankName)
    assertEquals(expected.bankAccount?.getOrNull(), actual.bankAccount)
    assertEquals(expected.idNumber?.getOrNull(), actual.idNumber)
    assertEquals(expected.taxIdNumber?.getOrNull(), actual.taxIdNumber)
    assertEquals(expected.vatRate?.getOrNull(), actual.vatRate)
    assertEquals(expected.note?.getOrNull(), actual.note)
}

fun assertDistributorToMssqlDistributorEquals(expected: Distributor, actual: Rdistr) {
    assertEquals(expected.originalId, actual.rdistrid)
    assertEquals(expected.code, actual.distr.trimEnd())
    assertEquals(expected.title, actual.nazevd.trimIfNotBlank())
    assertEquals(expected.addressStreet, actual.ulice.trimIfNotBlank())
    assertEquals(expected.addressCity, actual.misto.trimIfNotBlank())
    assertEquals(expected.addressPostCode, actual.psc.trimIfNotBlank())
    assertEquals(expected.contactName1, actual.jmeno1.trimIfNotBlank())
    assertEquals(expected.contactName2, actual.jmeno2.trimIfNotBlank())
    assertEquals(expected.contactName3, actual.jmeno3.trimIfNotBlank())
    assertEquals(expected.contactPhone1, actual.telefon1.trimIfNotBlank())
    assertEquals(expected.contactPhone2, actual.telefon2.trimIfNotBlank())
    assertEquals(expected.contactPhone3, actual.telefon3.trimIfNotBlank())
    assertEquals(expected.contactEmails, parseEmails(actual.mail))
    assertEquals(expected.bankName, actual.banka.trimIfNotBlank())
    assertEquals(expected.bankAccount, actual.ucet.trimIfNotBlank())
    assertEquals(expected.idNumber, actual.ico.trimIfNotBlank())
    assertEquals(expected.taxIdNumber, actual.dic.trimIfNotBlank())
    assertEquals(expected.vatRate, actual.dph.toInt().setNullIfZero())
    assertEquals(expected.note, actual.pozn.trimIfNotBlank())
}

fun assertEqualsTruncated(expected: LocalDateTime?, actual: LocalDateTime?) =
    assertEquals(expected?.roundToMicros(), actual?.roundToMicros())

fun LocalDateTime.roundToMicros(): LocalDateTime = this.plusNanos(500).truncatedTo(ChronoUnit.MICROS)

fun parseEmails(emails: String?): Set<String> {
    if (emails.isNullOrBlank()) {
        return emptySet()
    }

    return emails.split(",", ";")
        .map { it.trim() }
        .filter { it.isNotBlank() && it.contains("@") }.toSet()
}
