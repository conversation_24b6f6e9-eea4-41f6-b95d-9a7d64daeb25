package com.cleevio.cinemax.api.util

import com.cleevio.cinemax.api.common.entity.DescriptorMssqlEntity
import com.cleevio.cinemax.api.common.integration.disfilm.xml.DISFilmMovie
import com.cleevio.cinemax.api.common.util.trimIfNotBlank
import com.cleevio.cinemax.api.module.groupreservation.controller.dto.MovieResponse
import com.cleevio.cinemax.api.module.language.entity.Language
import com.cleevio.cinemax.api.module.movie.constant.MovieRating
import com.cleevio.cinemax.api.module.movie.controller.dto.AdminGetMovieResponse
import com.cleevio.cinemax.api.module.movie.controller.dto.AdminSearchMoviesResponse
import com.cleevio.cinemax.api.module.movie.controller.dto.MovieSearchResponse
import com.cleevio.cinemax.api.module.movie.entity.Movie
import com.cleevio.cinemax.api.module.movie.service.command.CreateOrUpdateMovieCommand
import com.cleevio.cinemax.api.module.screening.constant.MovieFormat
import com.cleevio.cinemax.api.module.screening.constant.MovieLanguage
import com.cleevio.cinemax.api.module.screening.constant.MovieTechnology
import com.cleevio.cinemax.mssql.rps_cinemaxe.dbo.tables.pojos.Rfilm
import java.time.LocalDate
import java.util.Optional
import java.util.UUID
import kotlin.jvm.optionals.getOrNull
import kotlin.test.assertEquals
import kotlin.test.assertNotNull
import kotlin.test.assertNull
import kotlin.test.assertTrue

fun mapToCreateOrUpdateMovieCommand(movie: Movie) =
    CreateOrUpdateMovieCommand(
        id = movie.id,
        originalId = movie.originalId,
        title = movie.title,
        rawTitle = movie.rawTitle,
        originalTitle = movie.originalTitle?.let { Optional.of(it) },
        code = movie.code,
        disfilmCode = movie.disfilmCode,
        premiereDate = movie.premiereDate?.let { Optional.of(it) },
        duration = movie.duration,
        parsedRating = movie.parsedRating,
        parsedFormat = movie.parsedFormat,
        parsedTechnology = movie.parsedTechnology,
        parsedLanguage = movie.parsedLanguage,
        distributorId = movie.distributorId,
        productionId = movie.productionId?.let { Optional.of(it) },
        primaryGenreId = movie.primaryGenreId?.let { Optional.of(it) },
        secondaryGenreId = movie.secondaryGenreId?.let { Optional.of(it) },
        ratingId = movie.ratingId?.let { Optional.of(it) },
        technologyId = movie.technologyId,
        languageId = movie.languageId,
        tmsLanguageId = movie.tmsLanguageId
    )

fun createMovie(
    originalId: Int? = 1,
    title: String = "Oppenheimer",
    rawTitle: String = "Oppenheimer IMAX 2D (ST)",
    originalTitle: String? = title,
    code: String = if (originalId == null) "100000" else "10000$originalId",
    disfilmCode: String? = null,
    releaseYear: Int? = 2023,
    premiereDate: LocalDate = LocalDate.of(2023, 1, 1),
    parsedFormat: MovieFormat? = MovieFormat.FORMAT_2D,
    parsedTechnology: MovieTechnology? = null,
    parsedLanguage: MovieLanguage? = MovieLanguage.ENG,
    duration: Int? = 160,
    distributorId: UUID,
    productionId: UUID? = null,
    primaryGenreId: UUID? = null,
    secondaryGenreId: UUID? = null,
    ratingId: UUID? = null,
    technologyId: UUID? = null,
    languageId: UUID? = null,
    tmsLanguageId: UUID? = null,
) = Movie(
    id = UUID.randomUUID(),
    originalId = originalId,
    title = title,
    rawTitle = rawTitle,
    originalTitle = originalTitle,
    code = code,
    disfilmCode = disfilmCode,
    releaseYear = releaseYear,
    premiereDate = premiereDate,
    duration = duration,
    parsedRating = MovieRating.PLUS_12,
    parsedFormat = parsedFormat,
    parsedTechnology = parsedTechnology,
    parsedLanguage = parsedLanguage,
    distributorId = distributorId,
    productionId = productionId,
    primaryGenreId = primaryGenreId,
    secondaryGenreId = secondaryGenreId,
    ratingId = ratingId,
    technologyId = technologyId,
    languageId = languageId,
    tmsLanguageId = tmsLanguageId
)

fun mapToMovieSearchResponse(movie: Movie) =
    MovieSearchResponse(
        id = movie.id,
        title = movie.title,
        releaseYear = movie.releaseYear,
        duration = movie.duration,
        rating = movie.parsedRating,
        format = movie.parsedFormat,
        technology = movie.parsedTechnology,
        language = movie.parsedLanguage
    )

fun assertMovieSearchResponseEquals(
    expected: Movie,
    actual: MovieSearchResponse,
) {
    assertEquals(expected.id, actual.id)
    assertEquals(expected.title, actual.title)
    assertEquals(expected.releaseYear, actual.releaseYear)
    assertEquals(expected.duration, actual.duration)
    assertEquals(expected.parsedRating, actual.rating)
    assertEquals(expected.parsedFormat, actual.format)
    assertEquals(expected.parsedTechnology, actual.technology)
    assertEquals(expected.parsedLanguage, actual.language)
}

fun mapToAdminSearchMoviesResponse(
    movie: Movie,
    distributor: AdminSearchMoviesResponse.MovieDistributorSearchResponse,
    production: AdminSearchMoviesResponse.MovieDescriptorMssqlResponse? = null,
    primaryGenre: AdminSearchMoviesResponse.MovieDescriptorMssqlResponse? = null,
    secondaryGenre: AdminSearchMoviesResponse.MovieDescriptorMssqlResponse? = null,
    rating: AdminSearchMoviesResponse.MovieDescriptorMssqlResponse? = null,
    technology: AdminSearchMoviesResponse.MovieDescriptorMssqlResponse? = null,
    language: AdminSearchMoviesResponse.MovieDescriptorMssqlResponse? = null,
    jsos: List<AdminSearchMoviesResponse.MovieDescriptorMssqlResponse>,
) = AdminSearchMoviesResponse(
    id = movie.id,
    rawTitle = movie.rawTitle,
    originalTitle = movie.originalTitle,
    title = movie.title,
    code = movie.code,
    disfilmCode = movie.disfilmCode,
    releaseYear = movie.releaseYear,
    premiereDate = movie.premiereDate,
    duration = movie.duration,
    createdAt = movie.createdAt,
    updatedAt = movie.updatedAt,
    distributor = distributor,
    production = production,
    primaryGenre = primaryGenre,
    secondaryGenre = secondaryGenre,
    rating = rating,
    technology = technology,
    language = language,
    jsos = jsos
)

fun mapToAdminGetMovieResponse(
    movie: Movie,
    distributor: AdminGetMovieResponse.GetMovieDistributorResponse,
    production: AdminGetMovieResponse.GetMovieDescriptorMssqlResponse? = null,
    primaryGenre: AdminGetMovieResponse.GetMovieDescriptorMssqlResponse? = null,
    secondaryGenre: AdminGetMovieResponse.GetMovieDescriptorMssqlResponse? = null,
    rating: AdminGetMovieResponse.GetMovieDescriptorMssqlResponse? = null,
    technology: AdminGetMovieResponse.GetMovieDescriptorMssqlResponse? = null,
    language: AdminGetMovieResponse.GetMovieDescriptorMssqlResponse? = null,
    jsos: List<AdminGetMovieResponse.GetMovieDescriptorMssqlResponse>,
) = AdminGetMovieResponse(
    id = movie.id,
    rawTitle = movie.rawTitle,
    originalTitle = movie.originalTitle,
    title = movie.title,
    code = movie.code,
    disfilmCode = movie.disfilmCode,
    releaseYear = movie.releaseYear,
    premiereDate = movie.premiereDate,
    duration = movie.duration,
    createdAt = movie.createdAt,
    updatedAt = movie.updatedAt,
    distributor = distributor,
    production = production,
    primaryGenre = primaryGenre,
    secondaryGenre = secondaryGenre,
    rating = rating,
    technology = technology,
    language = language,
    jsos = jsos
)

fun assertAdminSearchMoviesResponseEquals(
    expected: Movie,
    expectedProductionTitle: String? = null,
    expectedPrimaryGenreTitle: String? = null,
    expectedSecondaryGenreTitle: String? = null,
    expectedRatingTitle: String? = null,
    expectedTechnologyTitle: String? = null,
    expectedLanguageTitle: String? = null,
    expectedTmsLanguageTitle: String? = null,
    expectedJsoTitles: Set<String>,
    actual: AdminSearchMoviesResponse,
) {
    assertEquals(expected.id, actual.id)
    assertEquals(expected.originalTitle, actual.originalTitle)
    assertEquals(expected.rawTitle, actual.rawTitle)
    assertEquals(expected.title, actual.title)
    assertEquals(expected.code, actual.code)
    assertEquals(expected.disfilmCode, actual.disfilmCode)
    assertEquals(expected.releaseYear, actual.releaseYear)
    assertEquals(expected.premiereDate, actual.premiereDate)
    assertEquals(expected.duration, actual.duration)
    assertEquals(expected.distributorId, actual.distributor.id)

    if (expectedProductionTitle == null) {
        assertNull(actual.production)
    } else {
        assertEquals(expectedProductionTitle, actual.production?.title)
    }
    if (expectedPrimaryGenreTitle == null) {
        assertNull(actual.primaryGenre)
    } else {
        assertEquals(expectedPrimaryGenreTitle, actual.primaryGenre?.title)
    }
    if (expectedSecondaryGenreTitle == null) {
        assertNull(actual.secondaryGenre)
    } else {
        assertEquals(expectedSecondaryGenreTitle, actual.secondaryGenre?.title)
    }
    if (expectedRatingTitle == null) {
        assertNull(actual.rating)
    } else {
        assertEquals(expectedRatingTitle, actual.rating?.title)
    }
    if (expectedTechnologyTitle == null) {
        assertNull(actual.technology)
    } else {
        assertEquals(expectedTechnologyTitle, actual.technology?.title)
    }
    if (expectedLanguageTitle == null) {
        assertNull(actual.language)
    } else {
        assertEquals(expectedLanguageTitle, actual.language?.title)
    }
    if (expectedTmsLanguageTitle == null) {
        assertNull(actual.tmsLanguage)
    } else {
        assertEquals(expectedTmsLanguageTitle, actual.tmsLanguage?.title)
    }
    if (expectedJsoTitles.isEmpty()) {
        assertTrue(actual.jsos.isEmpty())
    } else {
        assertEquals(expectedJsoTitles.size, actual.jsos.size)
        assertTrue(expectedJsoTitles.containsAll(actual.jsos.map { it.title }))
    }
}

fun assertAdminGetMovieResponseEquals(
    expected: Movie,
    expectedProductionTitle: String? = null,
    expectedPrimaryGenreTitle: String? = null,
    expectedSecondaryGenreTitle: String? = null,
    expectedRatingTitle: String? = null,
    expectedTechnologyTitle: String? = null,
    expectedLanguageTitle: String? = null,
    expectedTmsLanguageTitle: String? = null,
    expectedJsoTitles: Set<String>,
    actual: AdminGetMovieResponse,
) {
    assertEquals(expected.id, actual.id)
    assertEquals(expected.originalTitle, actual.originalTitle)
    assertEquals(expected.rawTitle, actual.rawTitle)
    assertEquals(expected.title, actual.title)
    assertEquals(expected.code, actual.code)
    assertEquals(expected.disfilmCode, actual.disfilmCode)
    assertEquals(expected.releaseYear, actual.releaseYear)
    assertEquals(expected.premiereDate, actual.premiereDate)
    assertEquals(expected.duration, actual.duration)
    assertEquals(expected.distributorId, actual.distributor.id)

    if (expectedProductionTitle == null) {
        assertNull(actual.production)
    } else {
        assertEquals(expectedProductionTitle, actual.production?.title)
    }
    if (expectedPrimaryGenreTitle == null) {
        assertNull(actual.primaryGenre)
    } else {
        assertEquals(expectedPrimaryGenreTitle, actual.primaryGenre?.title)
    }
    if (expectedSecondaryGenreTitle == null) {
        assertNull(actual.secondaryGenre)
    } else {
        assertEquals(expectedSecondaryGenreTitle, actual.secondaryGenre?.title)
    }
    if (expectedRatingTitle == null) {
        assertNull(actual.rating)
    } else {
        assertEquals(expectedRatingTitle, actual.rating?.title)
    }
    if (expectedTechnologyTitle == null) {
        assertNull(actual.technology)
    } else {
        assertEquals(expectedTechnologyTitle, actual.technology?.title)
    }
    if (expectedLanguageTitle == null) {
        assertNull(actual.language)
    } else {
        assertEquals(expectedLanguageTitle, actual.language?.title)
    }
    if (expectedTmsLanguageTitle == null) {
        assertNull(actual.tmsLanguage)
    } else {
        assertEquals(expectedTmsLanguageTitle, actual.tmsLanguage?.title)
    }
    if (expectedJsoTitles.isEmpty()) {
        assertTrue(actual.jsos.isEmpty())
    } else {
        assertEquals(expectedJsoTitles.size, actual.jsos.size)
        assertTrue(expectedJsoTitles.containsAll(actual.jsos.map { it.title }))
    }
}

fun mapToSearchMovieDescriptorMssqlResponse(entity: DescriptorMssqlEntity) =
    AdminSearchMoviesResponse.MovieDescriptorMssqlResponse(
        id = entity.id,
        title = entity.title
    )

fun mapToGetMovieDescriptorMssqlResponse(entity: DescriptorMssqlEntity) =
    AdminGetMovieResponse.GetMovieDescriptorMssqlResponse(
        id = entity.id,
        title = entity.title
    )

fun assertMovieToMssqlMovieMapping(
    expected: Movie,
    expectedOriginalId: Int,
    expectedDistributorOriginalCode: String? = null,
    expectedProductionCode: String? = null,
    expectedPrimaryGenreCode: String? = null,
    expectedSecondaryGenreCode: String? = null,
    expectedRatingCode: String? = null,
    expectedTmsLanguageCode: String? = null,
    actual: Rfilm,
) {
    assertEquals(expectedDistributorOriginalCode, actual.distr.trim())
    assertEquals(expectedOriginalId, actual.rfilmid)
    assertEquals(expected.code, actual.cisfilm.trim())
    assertEquals(expected.rawTitle, actual.nazevf.trim())
    assertEquals(expected.originalTitle, actual.ornazevf.trimIfNotBlank())
    assertEquals(expectedProductionCode, actual.prod.trimIfNotBlank())
    if (expected.premiereDate == null) {
        assertNull(actual.datprem)
    } else {
        assertEquals(expected.premiereDate!!.atStartOfDay(), actual.datprem)
    }
    if (expected.duration == null) {
        assertEquals("0", actual.minf.trimIfNotBlank())
    } else {
        assertEquals(expected.duration.toString(), actual.minf.trimIfNotBlank())
    }
    assertEquals(setOfNotNull(expectedPrimaryGenreCode, expectedSecondaryGenreCode).joinToString(""), actual.zanrf.trim())
    assertEquals(expectedRatingCode, actual.pristup.trimIfNotBlank())
    assertEquals(expected.disfilmCode?.substring(0, 10), actual.cisfilmufd?.substring(0, 10).trimIfNotBlank())
    assertNull(actual.cfotrm.trimIfNotBlank())
    assertNull(actual.jazyk.trimIfNotBlank())
    assertEquals(expectedTmsLanguageCode, actual.kodupravy.trimIfNotBlank())
}

fun assertDISFilmMovieToMovieMapping(
    expected: DISFilmMovie,
    actual: Movie,
    actualDistributorCode: String,
    actualGenreTitle: String?,
    actualRatingCode: String?,
    actualTechnologyTitle: String?,
    actualLanguage: Language?,
    actualProductionTitle: String?,
) {
    assertEquals(expected.disfilmCode, actual.disfilmCode)
    assertEquals(expected.rawTitle, actual.rawTitle)
    assertEquals(expected.originalTitle, actual.originalTitle)
    assertEquals(expected.distributorCode, actualDistributorCode)
    if (expected.premiereDate == null) {
        assertNull(actual.premiereDate)
    } else {
        assertEquals(LocalDate.parse(expected.premiereDate), actual.premiereDate)
    }
    assertEquals(expected.genreTitle, actualGenreTitle)
    assertEquals(expected.ratingCode, actualRatingCode)
    assertEquals(expected.duration, actual.duration)
    assertEquals(expected.technologyTitle, actualTechnologyTitle)
    assertEquals(expected.languageCode, actualLanguage?.code)
    assertEquals(expected.productionTitle, actualProductionTitle)
    assertEquals(actualLanguage?.tmsLanguageId, actual.tmsLanguageId)
}

fun mapToAdminSearchGroupReservationMovieResponse(movie: Movie) = MovieResponse(
    id = movie.id,
    title = movie.title,
    rawTitle = movie.rawTitle
)

fun assertCommandToMovieMapping(
    expected: CreateOrUpdateMovieCommand,
    actual: Movie,
    expectedCode: String? = null,
) {
    assertEquals(expected.originalId, actual.originalId)
    if (expected.code != null) {
        assertEquals(expected.code, actual.code)
    } else {
        expectedCode?.let { assertEquals(expectedCode, actual.code) } ?: run { assertNotNull(actual.code) }
    }
    assertEquals(expected.rawTitle, actual.rawTitle)
    assertEquals(expected.originalTitle?.getOrNull(), actual.originalTitle)
    assertEquals(expected.title, actual.title)
    assertEquals(expected.disfilmCode, actual.disfilmCode)
    assertEquals(expected.premiereDate?.getOrNull(), actual.premiereDate)
    assertEquals(expected.duration, actual.duration)
    assertEquals(expected.parsedRating, actual.parsedRating)
    assertEquals(expected.parsedFormat, actual.parsedFormat)
    assertEquals(expected.parsedTechnology, actual.parsedTechnology)
    assertEquals(expected.parsedLanguage, actual.parsedLanguage)
    assertEquals(expected.distributorId, actual.distributorId)
    assertNotNull(actual.createdAt)
    assertNotNull(actual.updatedAt)
}
