package com.cleevio.cinemax.api.util

import com.cleevio.cinemax.api.module.posconfiguration.constant.PosConfigurationType
import com.cleevio.cinemax.api.module.posconfiguration.constant.ProductMode
import com.cleevio.cinemax.api.module.posconfiguration.constant.TablesType
import com.cleevio.cinemax.api.module.posconfiguration.entity.PosConfiguration
import com.cleevio.cinemax.api.module.posconfiguration.service.command.CreateOrUpdatePosConfigurationCommand
import java.util.Optional

fun createPosConfiguration(
    macAddress: String = "AA:BB:CC:DD:EE",
    title: String = "POS 1 config",
    receiptsDirectory: String = "/pos/receipts",
    terminalDirectory: String? = null,
    terminalIpAddress: String? = "***********",
    terminalPort: Int? = 53535,
    ticketSalesEnabled: Boolean = true,
    productModes: Set<ProductMode> = setOf(ProductMode.STANDARD, ProductMode.CAFE),
    tablesType: TablesType = TablesType.CAFE_TABLES,
    seatsEnabled: Boolean = true,
    type: PosConfigurationType = PosConfigurationType.PHYSICAL,
) = PosConfiguration(
    macAddress = macAddress,
    title = title,
    receiptsDirectory = receiptsDirectory,
    terminalDirectory = terminalDirectory,
    terminalIpAddress = terminalIpAddress,
    terminalPort = terminalPort,
    ticketSalesEnabled = ticketSalesEnabled,
    productModes = productModes,
    tablesType = tablesType,
    seatsEnabled = seatsEnabled,
    type = type
)

fun mapToCreateOrUpdatePosConfigurationCommand(posConfiguration: PosConfiguration) =
    CreateOrUpdatePosConfigurationCommand(
        id = posConfiguration.id,
        macAddress = posConfiguration.macAddress,
        title = posConfiguration.title,
        receiptsDirectory = posConfiguration.receiptsDirectory,
        terminalDirectory = posConfiguration.terminalDirectory?.let { Optional.ofNullable(it) },
        terminalIpAddress = posConfiguration.terminalIpAddress?.let { Optional.ofNullable(it) },
        terminalPort = Optional.ofNullable(posConfiguration.terminalPort),
        ticketSalesEnabled = posConfiguration.ticketSalesEnabled,
        productModes = posConfiguration.productModes,
        tablesType = posConfiguration.tablesType,
        seatsEnabled = posConfiguration.seatsEnabled,
        type = posConfiguration.type
    )
