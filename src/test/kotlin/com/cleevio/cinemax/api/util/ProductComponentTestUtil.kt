package com.cleevio.cinemax.api.util

import com.cleevio.cinemax.api.common.util.isEqualTo
import com.cleevio.cinemax.api.common.util.trimIfNotBlank
import com.cleevio.cinemax.api.module.productcomponent.constant.ProductComponentUnit
import com.cleevio.cinemax.api.module.productcomponent.controller.dto.AdminSearchProductComponentsResponse
import com.cleevio.cinemax.api.module.productcomponent.entity.ProductComponent
import com.cleevio.cinemax.api.module.productcomponent.service.command.CreateOrUpdateProductComponentCommand
import com.cleevio.cinemax.api.module.productcomponent.service.command.MessagingCreateOrUpdateProductComponentCommand
import com.cleevio.cinemax.api.module.productcomponent.service.model.ProductComponentExportRecordModel
import com.cleevio.cinemax.api.module.productcomponentcategory.entity.ProductComponentCategory
import com.cleevio.cinemax.mssql.rp_bufete.dbo.tables.pojos.Rzbozi
import org.junit.jupiter.api.Assertions.assertTrue
import java.math.BigDecimal
import java.util.UUID
import kotlin.test.assertEquals
import kotlin.test.assertNotNull

fun mapToCreateOrUpdateProductComponentCommand(component: ProductComponent) =
    CreateOrUpdateProductComponentCommand(
        id = component.id,
        originalId = component.originalId,
        code = component.code,
        title = component.title,
        unit = component.unit,
        purchasePrice = component.purchasePrice,
        stockQuantity = component.stockQuantity,
        productComponentCategoryId = requireNotNull(component.productComponentCategoryId),
        active = component.active,
        taxRateOverride = component.taxRateOverride
    )

fun createProductComponent(
    id: UUID = UUID.randomUUID(),
    originalId: Int? = 1,
    code: String = "$originalId",
    title: String = "dummyTitle",
    unit: ProductComponentUnit = ProductComponentUnit.KG,
    purchasePrice: BigDecimal = BigDecimal.TEN,
    stockQuantity: BigDecimal = BigDecimal.ZERO,
    productComponentCategoryId: UUID,
    active: Boolean = true,
    taxRateOverride: Int? = null,
) = ProductComponent(
    id = id,
    originalId = originalId,
    code = code,
    title = title,
    unit = unit,
    purchasePrice = purchasePrice,
    stockQuantity = stockQuantity,
    productComponentCategoryId = productComponentCategoryId,
    active = active,
    taxRateOverride = taxRateOverride
)

fun assertProductComponentEquals(expected: ProductComponent, actual: ProductComponent) {
    assertEquals(expected.originalId, actual.originalId)
    assertEquals(expected.code, actual.code)
    assertEquals(expected.title, actual.title)
    assertEquals(expected.unit, actual.unit)
    assertEquals(expected.productComponentCategoryId, actual.productComponentCategoryId)
    assertTrue(expected.purchasePrice isEqualTo actual.purchasePrice)
    assertTrue(expected.stockQuantity isEqualTo actual.stockQuantity)
    assertEquals(expected.taxRateOverride, actual.taxRateOverride)
    assertNotNull(actual.createdAt)
    assertNotNull(actual.updatedAt)
}

fun assertProductComponentCommandToProductComponentEquals(
    expected: CreateOrUpdateProductComponentCommand,
    actual: ProductComponent,
    expectedCode: String? = null,
) {
    assertEquals(expected.originalId, actual.originalId)
    if (expected.code != null) {
        assertEquals(expected.code, actual.code)
    } else {
        expectedCode?.let { assertEquals(expectedCode, actual.code) } ?: run { assertNotNull(actual.code) }
    }
    assertEquals(expected.title, actual.title)
    assertEquals(expected.unit, actual.unit)
    assertEquals(expected.productComponentCategoryId, actual.productComponentCategoryId)
    assertTrue(expected.purchasePrice isEqualTo actual.purchasePrice)
    assertTrue(expected.stockQuantity isEqualTo actual.stockQuantity)
    assertNotNull(actual.createdAt)
    assertNotNull(actual.updatedAt)
}

fun assertProductComponentToSearchResponseEquals(
    expectedComponent: ProductComponent,
    expectedCategory: ProductComponentCategory,
    actual: AdminSearchProductComponentsResponse,
) {
    assertEquals(expectedComponent.id, actual.id)
    assertEquals(expectedComponent.code, actual.code)
    assertEquals(expectedComponent.title, actual.title)
    assertEquals(expectedComponent.stockQuantity, actual.stockQuantity)
    assertEquals(expectedComponent.unit, actual.unit)
    assertEquals(expectedComponent.purchasePrice, actual.purchasePrice)
    assertEquals(expectedComponent.active, actual.active)
    assertEquals(expectedComponent.taxRateOverride, actual.taxRateOverride)
    assertEquals(expectedComponent.purchasePriceOverride, actual.purchasePriceOverride)
    assertNotNull(actual.createdAt)
    assertNotNull(actual.updatedAt)
    assertEquals(expectedCategory.id, actual.productComponentCategory.id)
    assertEquals(expectedCategory.title, actual.productComponentCategory.title)
    assertEquals(expectedCategory.taxRate, actual.productComponentCategory.taxRate)
}

fun assertProductComponentToExportModelEquals(
    expected: ProductComponent,
    actual: ProductComponentExportRecordModel,
) {
    assertEquals(expected.code, actual.code)
    assertEquals(expected.title, actual.title)
    assertEquals(expected.stockQuantity, actual.stockQuantity)
    assertEquals(expected.unit, actual.unit)
    assertEquals(expected.purchasePrice, actual.purchasePrice)
    assertEquals(expected.stockQuantity.multiply(expected.purchasePrice), actual.totalPrice)
}

fun assertProductComponentToMssqlProductComponentEquals(
    expected: ProductComponent,
    expectedOriginalId: Int,
    expectedComponentCategoryOriginalCode: String,
    actual: Rzbozi,
) {
    assertEquals(expectedOriginalId, actual.rzboziid)
    assertEquals(expected.code, actual.zbozi.trimIfNotBlank())
    assertEquals(expected.title, actual.nazev.trimIfNotBlank())
    assertEquals(expected.unit.mssqlValue, actual.mj.trimIfNotBlank())
    assertTrue(expected.purchasePrice isEqualTo actual.cenap)
    assertEquals(expectedComponentCategoryOriginalCode, actual.skupina.trimIfNotBlank())
    assertEquals(expected.active, actual.aktivni)
    assertNotNull(actual.zcas)
}

fun mapToMessagingCreateOrUpdateProductComponentCommand(component: ProductComponent) =
    MessagingCreateOrUpdateProductComponentCommand(
        code = component.code,
        title = component.title,
        unit = component.unit,
        purchasePrice = component.purchasePrice,
        productComponentCategoryId = requireNotNull(component.productComponentCategoryId),
        active = component.active,
        taxRateOverride = component.taxRateOverride
    )
