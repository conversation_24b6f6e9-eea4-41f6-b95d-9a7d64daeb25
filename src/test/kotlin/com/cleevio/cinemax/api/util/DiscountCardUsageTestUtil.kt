package com.cleevio.cinemax.api.util

import com.cleevio.cinemax.api.module.discountcardusage.entity.DiscountCardUsage
import java.util.UUID

fun createDiscountCardUsage(
    id: UUID = UUID.randomUUID(),
    discountCardId: UUID,
    screeningId: UUID? = null,
    basketId: UUID,
    posConfigurationId: UUID,
    ticketBasketItemId: UUID? = null,
    productBasketItemId: UUID? = null,
    productDiscountBasketItemId: UUID? = null,
) = DiscountCardUsage(
    id = id,
    discountCardId = discountCardId,
    screeningId = screeningId,
    basketId = basketId,
    posConfigurationId = posConfigurationId,
    ticketBasketItemId = ticketBasketItemId,
    productBasketItemId = productBasketItemId,
    productDiscountBasketItemId = productDiscountBasketItemId
)
