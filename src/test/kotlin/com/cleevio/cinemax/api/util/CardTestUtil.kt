package com.cleevio.cinemax.api.util

import com.cleevio.cinemax.api.module.card.entity.Card
import com.cleevio.cinemax.api.module.discountcard.constant.DiscountCardType
import java.time.LocalDate

fun createCard(
    type: DiscountCardType = DiscountCardType.CARD,
    title: String = "Test Card 1",
    code: String = "CU5X2URJ",
    validFrom: LocalDate = LocalDate.now(),
    validUntil: LocalDate = LocalDate.now().plusYears(1),
) = Card(
    type = type,
    title = title,
    code = code,
    validFrom = validFrom,
    validUntil = validUntil
)
