package com.cleevio.cinemax.api.util

import com.cleevio.cinemax.api.common.util.isEqualTo
import com.cleevio.cinemax.api.common.util.subtractTax
import com.cleevio.cinemax.api.common.util.trimIfNotBlank
import com.cleevio.cinemax.api.module.file.entity.File
import com.cleevio.cinemax.api.module.product.constant.ProductType
import com.cleevio.cinemax.api.module.product.controller.dto.ProductSearchResponse
import com.cleevio.cinemax.api.module.product.entity.Product
import com.cleevio.cinemax.api.module.product.service.command.AdminCreateProductCommand
import com.cleevio.cinemax.api.module.product.service.command.AdminUpdateProductCommand
import com.cleevio.cinemax.api.module.product.service.command.MessagingCreateOrUpdateProductCommand
import com.cleevio.cinemax.api.module.product.service.command.SyncCreateOrUpdateProductCommand
import com.cleevio.cinemax.api.module.productcomposition.entity.ProductComposition
import com.cleevio.cinemax.mssql.rp_bufete.dbo.tables.pojos.Rmenu
import java.math.BigDecimal
import java.util.UUID
import kotlin.test.assertEquals
import kotlin.test.assertNotNull
import kotlin.test.assertNull
import kotlin.test.assertTrue

fun mapToSyncCreateOrUpdateProductCommand(
    product: Product,
    productCategoryId: UUID,
) = SyncCreateOrUpdateProductCommand(
    id = product.id,
    originalId = product.originalId!!,
    originalCode = product.code,
    productCategoryId = productCategoryId,
    title = product.title,
    type = product.type,
    price = product.price,
    active = product.active,
    order = product.order,
    soldInBuffet = product.soldInBuffet,
    soldInCafe = product.soldInCafe,
    soldInVip = product.soldInVip,
    priceNoVat = product.priceNoVat,
    discountPercentage = product.discountPercentage,
    imageFileId = product.imageFileId,
    stockQuantityThreshold = product.stockQuantityThreshold,
    isPackagingDeposit = product.isPackagingDeposit,
    tabletOrder = product.tabletOrder
)

fun mapToCreateProductCommand(
    product: Product,
    productCategoryId: UUID,
    productCompositions: List<ProductComposition>,
) = AdminCreateProductCommand(
    productCategoryId = productCategoryId,
    title = product.title,
    type = product.type,
    price = product.price,
    flagshipPrice = product.flagshipPrice,
    active = product.active,
    order = product.order,
    soldInBuffet = product.soldInBuffet,
    soldInCafe = product.soldInCafe,
    soldInVip = product.soldInVip,
    discountPercentage = product.discountPercentage,
    imageFileId = product.imageFileId,
    stockQuantityThreshold = product.stockQuantityThreshold,
    isPackagingDeposit = product.isPackagingDeposit,
    tabletOrder = product.tabletOrder,
    taxRate = product.taxRate,
    discountAmount = product.discountAmount,
    productComposition = productCompositions.map {
        AdminCreateProductCommand.AdminCreateProductProductCompositionCommand(
            productComponentId = it.productComponentId,
            productInProductId = it.productInProductId,
            quantity = it.amount,
            productInProductPrice = it.productInProductPrice
        )
    }
)

fun createProduct(
    id: UUID = UUID.randomUUID(),
    originalId: Int? = 1,
    code: String = "0000$originalId",
    productCategoryId: UUID,
    title: String,
    type: ProductType = ProductType.PRODUCT,
    price: BigDecimal = 10.toBigDecimal(),
    flagshipPrice: BigDecimal = 11.toBigDecimal(),
    active: Boolean = true,
    order: Int? = null,
    soldInBuffet: Boolean = true,
    soldInCafe: Boolean = true,
    soldInVip: Boolean = true,
    discountPercentage: Int? = null,
    discountAmount: BigDecimal? = null,
    stockQuantityThreshold: Int? = null,
    imageFileId: UUID? = null,
    isPackagingDeposit: Boolean = false,
    tabletOrder: Int? = null,
    taxRate: Int? = null,
) = Product(
    id = id,
    originalId = originalId,
    code = code,
    originalCode = null,
    productCategoryId = productCategoryId,
    title = title,
    type = type,
    price = price,
    flagshipPrice = flagshipPrice,
    priceNoVat = price,
    active = active,
    order = order,
    soldInBuffet = soldInBuffet,
    soldInCafe = soldInCafe,
    soldInVip = soldInVip,
    discountPercentage = discountPercentage,
    stockQuantityThreshold = stockQuantityThreshold,
    imageFileId = imageFileId,
    isPackagingDeposit = isPackagingDeposit,
    tabletOrder = tabletOrder,
    taxRate = taxRate,
    discountAmount = discountAmount
)

fun mapToProductSearchResponse(product: Product, stockQuantity: Int = 0) = ProductSearchResponse(
    id = product.id,
    title = product.title,
    type = product.type,
    price = product.price,
    order = product.order,
    stockQuantity = stockQuantity,
    stockQuantityThreshold = product.stockQuantityThreshold
)

fun assertProductEquals(
    expected: Product,
    actual: ProductSearchResponse,
    expectedStockQuantity: Int,
) {
    assertEquals(expected.id, actual.id)
    assertEquals(expected.title, actual.title)
    assertEquals(expected.type, actual.type)
    assertTrue(expected.price isEqualTo actual.price)
    assertEquals(expected.order, actual.order)
    assertEquals(expectedStockQuantity, actual.stockQuantity)
    assertEquals(expected.stockQuantityThreshold, actual.stockQuantityThreshold)
}

fun assertProductAndProductCompositionsToAdminCreateProductCommand(
    expected: AdminCreateProductCommand,
    actualProduct: Product,
    actualProductCompositions: List<ProductComposition>,
) {
    assertNotNull(actualProduct.id)
    assertNull(actualProduct.originalId)
    assertNotNull(actualProduct.code)
    assertEquals(expected.productCategoryId, actualProduct.productCategoryId)
    assertEquals(expected.title, actualProduct.title)
    assertEquals(expected.type, actualProduct.type)
    assertTrue(expected.price isEqualTo actualProduct.price)
    assertTrue(expected.flagshipPrice isEqualTo actualProduct.flagshipPrice)
    assertTrue(
        (expected.taxRate?.let { expected.price.subtractTax(it) } ?: actualProduct.price) isEqualTo actualProduct.priceNoVat
    )
    assertEquals(expected.active, actualProduct.active)
    assertEquals(expected.order, actualProduct.order)
    assertEquals(expected.soldInBuffet, actualProduct.soldInBuffet)
    assertEquals(expected.soldInCafe, actualProduct.soldInCafe)
    assertEquals(expected.soldInVip, actualProduct.soldInVip)
    assertTrue(expected.discountAmount isEqualTo actualProduct.discountAmount)
    assertEquals(expected.discountPercentage, actualProduct.discountPercentage)
    assertEquals(expected.stockQuantityThreshold, actualProduct.stockQuantityThreshold)
    assertEquals(expected.isPackagingDeposit, actualProduct.isPackagingDeposit)
    assertEquals(expected.tabletOrder, actualProduct.tabletOrder)
    assertEquals(expected.taxRate, actualProduct.taxRate)
    assertEquals(expected.productComposition.size, actualProductCompositions.size)
    expected.productComposition.forEach { expectedComposition ->
        actualProductCompositions.first {
            it.productComponentId == expectedComposition.productComponentId &&
                it.productInProductId == expectedComposition.productInProductId &&
                it.productId == actualProduct.id
        }.let {
            assertNotNull(it.id)
            assertNull(it.originalId)
            assertEquals(expectedComposition.quantity, it.amount)
            assertEquals(expectedComposition.productInProductPrice, it.productInProductPrice)
        }
    }
}

fun assertProductAndProductCompositionsToMessagingCreateOrUpdateProductCommand(
    expected: MessagingCreateOrUpdateProductCommand,
    expectedProductCategoryId: UUID,
    expectedImageFileId: UUID?,
    actualProduct: Product,
    actualProductCompositions: List<ProductComposition>,
    productComponentIdToCode: Map<UUID, String>,
    productInProductIdToCode: Map<UUID, String>,
) {
    assertNull(actualProduct.originalId)
    assertEquals(expected.code, actualProduct.code)
    assertEquals(expectedProductCategoryId, actualProduct.productCategoryId)
    assertEquals(expectedImageFileId, actualProduct.imageFileId)
    assertEquals(expected.title, actualProduct.title)
    assertEquals(expected.type, actualProduct.type)
    assertTrue(expected.price isEqualTo actualProduct.price)
    assertTrue(expected.flagshipPrice isEqualTo actualProduct.flagshipPrice)
    assertTrue(
        (expected.taxRate?.let { expected.price.subtractTax(it) } ?: actualProduct.price) isEqualTo actualProduct.priceNoVat
    )
    assertEquals(expected.active, actualProduct.active)
    assertEquals(expected.order, actualProduct.order)
    assertEquals(expected.soldInBuffet, actualProduct.soldInBuffet)
    assertEquals(expected.soldInCafe, actualProduct.soldInCafe)
    assertEquals(expected.soldInVip, actualProduct.soldInVip)
    assertTrue(expected.discountAmount isEqualTo actualProduct.discountAmount)
    assertEquals(expected.discountPercentage, actualProduct.discountPercentage)
    assertEquals(expected.stockQuantityThreshold, actualProduct.stockQuantityThreshold)
    assertEquals(expected.isPackagingDeposit, actualProduct.isPackagingDeposit)
    assertEquals(expected.tabletOrder, actualProduct.tabletOrder)
    assertEquals(expected.taxRate, actualProduct.taxRate)

    assertEquals(expected.productComposition.size, actualProductCompositions.size)
    expected.productComposition.forEachIndexed { index, expectedComposition ->
        assertNull(actualProductCompositions[index].originalId)
        expectedComposition.productInProductCode?.let { expectedProductComponentCode ->
            assertEquals(productComponentIdToCode[actualProductCompositions[index].id], expectedProductComponentCode)
        }
        expectedComposition.productInProductCode?.let { expectedProductInProductCode ->
            assertEquals(productInProductIdToCode[actualProductCompositions[index].id], expectedProductInProductCode)
        }
        assertEquals(expectedComposition.quantity, actualProductCompositions[index].amount)
        assertEquals(expectedComposition.productInProductPrice, actualProductCompositions[index].productInProductPrice)
        assertEquals(
            expectedComposition.productInProductFlagshipPrice,
            actualProductCompositions[index].productInProductFlagshipPrice
        )
    }
}

fun assertProductAndProductCompositionsToAdminUpdateProductCommand(
    expected: AdminUpdateProductCommand,
    originalProductType: ProductType,
    actualProduct: Product,
    actualProductCompositions: List<ProductComposition>,
) {
    assertEquals(expected.id, actualProduct.id) // Since it's an update, we expect IDs to match
    assertNotNull(actualProduct.code)
    assertEquals(expected.productCategoryId, actualProduct.productCategoryId)
    assertEquals(expected.title, actualProduct.title)
    assertEquals(expected.active, actualProduct.active)
    assertEquals(originalProductType, actualProduct.type)
    assertTrue(expected.price isEqualTo actualProduct.price)
    assertTrue(expected.flagshipPrice isEqualTo actualProduct.flagshipPrice)
    assertTrue(
        (expected.taxRate?.let { expected.price.subtractTax(it) } ?: actualProduct.price) isEqualTo actualProduct.priceNoVat
    )
    assertEquals(expected.order, actualProduct.order)
    assertEquals(expected.soldInBuffet, actualProduct.soldInBuffet)
    assertEquals(expected.soldInCafe, actualProduct.soldInCafe)
    assertEquals(expected.soldInVip, actualProduct.soldInVip)
    assertTrue(expected.discountAmount isEqualTo actualProduct.discountAmount)
    assertEquals(expected.discountPercentage, actualProduct.discountPercentage)
    assertEquals(expected.stockQuantityThreshold, actualProduct.stockQuantityThreshold)
    assertEquals(expected.isPackagingDeposit, actualProduct.isPackagingDeposit)
    assertEquals(expected.tabletOrder, actualProduct.tabletOrder)
    assertEquals(expected.taxRate, actualProduct.taxRate)
    assertEquals(expected.imageFileId, actualProduct.imageFileId)

    assertEquals(expected.productComposition.size, actualProductCompositions.size)
    expected.productComposition.forEach { expectedComposition ->
        actualProductCompositions.firstOrNull {
            it.productComponentId == expectedComposition.productComponentId &&
                it.productInProductId == expectedComposition.productInProductId &&
                it.productId == actualProduct.id
        }?.let {
            assertNotNull(it.id)
            assertNull(it.originalId)
            assertEquals(expectedComposition.quantity, it.amount)
        }
    }
}

fun createProductFromAdminCreateCommand(command: AdminCreateProductCommand, code: String = "09090") = Product(
    id = UUID.randomUUID(),
    code = code,
    originalCode = null,
    productCategoryId = command.productCategoryId,
    title = command.title,
    type = command.type,
    price = command.discountAmount ?: command.price,
    flagshipPrice = command.flagshipPrice,
    priceNoVat = command.taxRate?.let { command.price.subtractTax(it) } ?: command.price,
    active = command.active,
    order = command.order,
    soldInBuffet = command.soldInBuffet,
    soldInCafe = command.soldInCafe,
    soldInVip = command.soldInVip,
    discountPercentage = command.discountPercentage,
    stockQuantityThreshold = command.stockQuantityThreshold,
    isPackagingDeposit = command.isPackagingDeposit ?: false,
    tabletOrder = command.tabletOrder,
    taxRate = command.taxRate,
    imageFileId = command.imageFileId
)

fun assertProductToMssqlProductEquals(
    expected: Product,
    expectedOriginalId: Int,
    expectedProductCategoryOriginalCode: String,
    expectedFile: File?,
    hasSoleProductComposition: Boolean,
    actual: Rmenu,
) {
    assertEquals(expectedOriginalId, actual.rmenuid)
    assertEquals(expected.code, actual.cislo.trimIfNotBlank())
    assertEquals(expected.title, actual.nazev.trimIfNotBlank())
    assertEquals(expectedProductCategoryOriginalCode, actual.druh.trimIfNotBlank())
    assertTrue(expected.priceNoVat isEqualTo actual.cena)
    assertEquals(expected.active, actual.aktivni)
    assertTrue(expected.price isEqualTo actual.cenacelk)
    assertEquals(expected.type == ProductType.PRODUCT, actual.lcelk)
    if (expected.tabletOrder == null) {
        assertEquals(0, actual.poraditablet)
    } else {
        assertEquals(expected.tabletOrder, actual.poraditablet)
    }
    if (expected.order == null) {
        assertEquals(0, actual.poradiprodej)
    } else {
        assertEquals(expected.order, actual.poradiprodej)
    }
    assertEquals(expected.soldInBuffet, actual.lbufet)
    assertEquals(expected.soldInCafe, actual.lbar)
    assertEquals(expected.soldInVip, actual.ltablet)
    assertEquals(expectedFile?.let { it.originalName ?: it.getFilename() }, actual.obrazek.trimIfNotBlank())
    assertEquals(hasSoleProductComposition, actual.lzbozi)
    if (expected.stockQuantityThreshold == null) {
        assertEquals(0, actual.minim)
    } else {
        assertEquals(expected.stockQuantityThreshold!!.toShort(), actual.minim)
    }
    assertEquals(!expected.soldInVip, actual.tabletne)
    assertEquals(expected.updatedBy, actual.zuziv.trim())
    assertNotNull(actual.zcas)
}
