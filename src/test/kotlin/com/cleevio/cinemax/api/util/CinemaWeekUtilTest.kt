package com.cleevio.cinemax.api.util

import com.cleevio.cinemax.api.common.constant.BusinessCountry
import com.cleevio.cinemax.api.common.util.getCurrentCinemaWeekInterval
import com.cleevio.cinemax.api.common.util.isValidCinemaWeekInterval
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertFalse
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.api.Test
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.Arguments
import org.junit.jupiter.params.provider.MethodSource
import java.time.DayOfWeek
import java.time.LocalDate
import java.util.stream.Stream

class CinemaWeekUtilTest {

    @ParameterizedTest
    @MethodSource("validCinemaWeekIntervalsProvider")
    fun `test isValidCinemaWeekInterval, should return true for valid intervals`(
        dateFrom: LocalDate,
        dateTo: LocalDate,
        country: BusinessCountry,
    ) {
        assertTrue(isValidCinemaWeekInterval(dateFrom, dateTo, country))
    }

    @ParameterizedTest
    @MethodSource("invalidCinemaWeekIntervalsProvider")
    fun `test isValidCinemaWeekInterval, should return false for invalid intervals`(
        dateFrom: LocalDate,
        dateTo: LocalDate,
        country: BusinessCountry,
    ) {
        assertFalse(isValidCinemaWeekInterval(dateFrom, dateTo, country))
    }

    @ParameterizedTest
    @MethodSource("cinemaWeekReferenceDateProvider")
    fun `test getCurrentCinemaWeekInterval, should return correct interval for reference date`(
        referenceDate: LocalDate,
        country: BusinessCountry,
        expectedStart: LocalDate,
        expectedEnd: LocalDate,
    ) {
        val (startDate, endDate) = getCurrentCinemaWeekInterval(country, referenceDate)
        assertEquals(expectedStart, startDate)
        assertEquals(expectedEnd, endDate)
    }

    @Test
    fun `test getCurrentCinemaWeekInterval, should return interval starting on Thursday for CZ`() {
        val (startDate, endDate) = getCurrentCinemaWeekInterval(BusinessCountry.CZ, LocalDate.of(2023, 8, 15)) // Tuesday
        assertEquals(DayOfWeek.THURSDAY, startDate.dayOfWeek)
        assertEquals(startDate.plusDays(6), endDate)
    }

    @Test
    fun `test getCurrentCinemaWeekInterval, should return interval starting on Thursday for SK`() {
        val (startDate, endDate) = getCurrentCinemaWeekInterval(BusinessCountry.SK, LocalDate.of(2023, 8, 15)) // Tuesday
        assertEquals(DayOfWeek.THURSDAY, startDate.dayOfWeek)
        assertEquals(startDate.plusDays(6), endDate)
    }

    @Test
    fun `test getCurrentCinemaWeekInterval, should return interval starting on Friday for RO`() {
        val (startDate, endDate) = getCurrentCinemaWeekInterval(BusinessCountry.RO, LocalDate.of(2023, 8, 15)) // Tuesday
        assertEquals(DayOfWeek.FRIDAY, startDate.dayOfWeek)
        assertEquals(startDate.plusDays(6), endDate)
    }

    companion object {
        @JvmStatic
        fun validCinemaWeekIntervalsProvider(): Stream<Arguments> {
            return Stream.of(
                // Thursday to Wednesday for CZ
                Arguments.of(
                    LocalDate.of(2023, 8, 10), // Thursday
                    LocalDate.of(2023, 8, 16), // Wednesday
                    BusinessCountry.CZ
                ),
                // Thursday to Wednesday for SK
                Arguments.of(
                    LocalDate.of(2023, 8, 10), // Thursday
                    LocalDate.of(2023, 8, 16), // Wednesday
                    BusinessCountry.SK
                ),
                // Friday to Thursday for RO
                Arguments.of(
                    LocalDate.of(2023, 8, 11), // Friday
                    LocalDate.of(2023, 8, 17), // Thursday
                    BusinessCountry.RO
                ),
                // Another valid interval
                Arguments.of(
                    LocalDate.of(2023, 8, 17), // Thursday
                    LocalDate.of(2023, 8, 23), // Wednesday
                    BusinessCountry.CZ
                )
            )
        }

        @JvmStatic
        fun invalidCinemaWeekIntervalsProvider(): Stream<Arguments> {
            return Stream.of(
                // wrong start day (Wednesday instead of Thursday)
                Arguments.of(
                    LocalDate.of(2023, 8, 9), // Wednesday
                    LocalDate.of(2023, 8, 15), // Tuesday
                    BusinessCountry.CZ
                ),
                // wrong duration (5 days)
                Arguments.of(
                    LocalDate.of(2023, 8, 10), // Thursday
                    LocalDate.of(2023, 8, 15), // Tuesday
                    BusinessCountry.CZ
                ),
                // wrong start day for RO (Thursday instead of Friday)
                Arguments.of(
                    LocalDate.of(2023, 8, 10), // Thursday
                    LocalDate.of(2023, 8, 16), // Wednesday
                    BusinessCountry.RO
                ),
                // end date before start date
                Arguments.of(
                    LocalDate.of(2023, 8, 17), // Thursday
                    LocalDate.of(2023, 8, 16), // Wednesday
                    BusinessCountry.CZ
                )
            )
        }

        @JvmStatic
        fun cinemaWeekReferenceDateProvider(): Stream<Arguments> {
            return Stream.of(
                // CZ - reference on Thursday (first day of week)
                Arguments.of(
                    LocalDate.of(2023, 8, 10), // Thursday
                    BusinessCountry.CZ,
                    LocalDate.of(2023, 8, 10), // Thursday
                    LocalDate.of(2023, 8, 16) // Wednesday
                ),
                // CZ - reference on Saturday (middle of week)
                Arguments.of(
                    LocalDate.of(2023, 8, 12), // Saturday
                    BusinessCountry.CZ,
                    LocalDate.of(2023, 8, 10), // Thursday
                    LocalDate.of(2023, 8, 16) // Wednesday
                ),
                // CZ - reference on Wednesday (last day of week)
                Arguments.of(
                    LocalDate.of(2023, 8, 16), // Wednesday
                    BusinessCountry.CZ,
                    LocalDate.of(2023, 8, 10), // Thursday
                    LocalDate.of(2023, 8, 16) // Wednesday
                ),
                // RO - reference on Friday (first day of week)
                Arguments.of(
                    LocalDate.of(2023, 8, 11), // Friday
                    BusinessCountry.RO,
                    LocalDate.of(2023, 8, 11), // Friday
                    LocalDate.of(2023, 8, 17) // Thursday
                ),
                // RO - reference on Monday (middle of week)
                Arguments.of(
                    LocalDate.of(2023, 8, 14), // Monday
                    BusinessCountry.RO,
                    LocalDate.of(2023, 8, 11), // Friday
                    LocalDate.of(2023, 8, 17) // Thursday
                )
            )
        }
    }
}
