package com.cleevio.cinemax.api.util

import com.cleevio.cinemax.api.module.auditoriumdefault.entity.AuditoriumDefault
import com.cleevio.cinemax.api.module.auditoriumdefault.service.command.CreateOrUpdateAuditoriumDefaultCommand
import java.math.BigDecimal
import java.util.UUID
import kotlin.test.assertEquals

fun mapToCreateOrUpdateAuditoriumDefault(auditoriumDefault: AuditoriumDefault) = CreateOrUpdateAuditoriumDefaultCommand(
    id = auditoriumDefault.id,
    originalId = auditoriumDefault.originalId,
    auditoriumId = auditoriumDefault.auditoriumId,
    surchargeVip = auditoriumDefault.surchargeVip,
    surchargePremium = auditoriumDefault.surchargePremium,
    surchargeImax = auditoriumDefault.surchargeImax,
    surchargeUltraX = auditoriumDefault.surchargeUltraX,
    serviceFeeVip = auditoriumDefault.serviceFeeVip,
    serviceFeePremium = auditoriumDefault.serviceFeePremium,
    serviceFeeImax = auditoriumDefault.serviceFeeImax,
    serviceFeeUltraX = auditoriumDefault.serviceFeeUltraX,
    surchargeDBox = auditoriumDefault.surchargeDBox,
    proCommission = auditoriumDefault.proCommission,
    filmFondCommission = auditoriumDefault.filmFondCommission,
    distributorCommission = auditoriumDefault.distributorCommission,
    saleTimeLimit = auditoriumDefault.saleTimeLimit,
    publishOnline = auditoriumDefault.publishOnline
)

fun createEmptyAuditoriumDefault(
    originalId: Int? = 1,
    auditoriumId: UUID,
    surchargeVip: BigDecimal = 0.00.toBigDecimal(),
    surchargePremium: BigDecimal = 0.00.toBigDecimal(),
    surchargeImax: BigDecimal = 0.00.toBigDecimal(),
    surchargeUltraX: BigDecimal = 0.00.toBigDecimal(),
    serviceFeeVip: BigDecimal = 0.00.toBigDecimal(),
    serviceFeePremium: BigDecimal = 0.00.toBigDecimal(),
    serviceFeeImax: BigDecimal = 0.00.toBigDecimal(),
    serviceFeeUltraX: BigDecimal = 0.00.toBigDecimal(),
    surchargeDBox: BigDecimal = 0.00.toBigDecimal(),
    proCommission: Int = 0,
    filmFondCommission: Int = 0,
    distributorCommission: Int = 0,
    saleTimeLimit: Int = 0,
    publishOnline: Boolean = true,
) = AuditoriumDefault(
    id = UUID.randomUUID(),
    originalId = originalId,
    auditoriumId = auditoriumId,
    surchargeVip = surchargeVip,
    surchargePremium = surchargePremium,
    surchargeImax = surchargeImax,
    surchargeUltraX = surchargeUltraX,
    serviceFeeVip = serviceFeeVip,
    serviceFeePremium = serviceFeePremium,
    serviceFeeImax = serviceFeeImax,
    serviceFeeUltraX = serviceFeeUltraX,
    surchargeDBox = surchargeDBox,
    proCommission = proCommission,
    filmFondCommission = filmFondCommission,
    distributorCommission = distributorCommission,
    saleTimeLimit = saleTimeLimit,
    publishOnline = publishOnline
)

fun createAuditoriumDefault(
    originalId: Int? = 1,
    auditoriumId: UUID,
    surchargeVip: BigDecimal = 1.00.toBigDecimal(),
    surchargePremium: BigDecimal = 2.00.toBigDecimal(),
    surchargeImax: BigDecimal = 3.00.toBigDecimal(),
    surchargeUltraX: BigDecimal = 4.00.toBigDecimal(),
    serviceFeeVip: BigDecimal = 5.00.toBigDecimal(),
    serviceFeePremium: BigDecimal = 6.00.toBigDecimal(),
    serviceFeeImax: BigDecimal = 7.00.toBigDecimal(),
    serviceFeeUltraX: BigDecimal = 8.00.toBigDecimal(),
    surchargeDBox: BigDecimal = 9.00.toBigDecimal(),
    proCommission: Int = 2,
    filmFondCommission: Int = 3,
    distributorCommission: Int = 50,
    saleTimeLimit: Int = 25,
    publishOnline: Boolean = true,
) = AuditoriumDefault(
    id = UUID.randomUUID(),
    originalId = originalId,
    auditoriumId = auditoriumId,
    surchargeVip = surchargeVip,
    surchargePremium = surchargePremium,
    surchargeImax = surchargeImax,
    surchargeUltraX = surchargeUltraX,
    serviceFeeVip = serviceFeeVip,
    serviceFeePremium = serviceFeePremium,
    serviceFeeImax = serviceFeeImax,
    serviceFeeUltraX = serviceFeeUltraX,
    surchargeDBox = surchargeDBox,
    proCommission = proCommission,
    filmFondCommission = filmFondCommission,
    distributorCommission = distributorCommission,
    saleTimeLimit = saleTimeLimit,
    publishOnline = publishOnline
)

fun assertCommandToAuditoriumDefaultMapping(expected: CreateOrUpdateAuditoriumDefaultCommand, actual: AuditoriumDefault) {
    assertEquals(expected.auditoriumId, actual.auditoriumId)
    assertEquals(expected.surchargeVip, actual.surchargeVip)
    assertEquals(expected.surchargePremium, actual.surchargePremium)
    assertEquals(expected.surchargeImax, actual.surchargeImax)
    assertEquals(expected.surchargeUltraX, actual.surchargeUltraX)
    assertEquals(expected.serviceFeeVip, actual.serviceFeeVip)
    assertEquals(expected.serviceFeePremium, actual.serviceFeePremium)
    assertEquals(expected.serviceFeeImax, actual.serviceFeeImax)
    assertEquals(expected.serviceFeeUltraX, actual.serviceFeeUltraX)
    assertEquals(expected.surchargeDBox, actual.surchargeDBox)
    assertEquals(expected.proCommission, actual.proCommission)
    assertEquals(expected.filmFondCommission, actual.filmFondCommission)
    assertEquals(expected.distributorCommission, actual.distributorCommission)
    assertEquals(expected.saleTimeLimit, actual.saleTimeLimit)
    assertEquals(expected.publishOnline, actual.publishOnline)
}
