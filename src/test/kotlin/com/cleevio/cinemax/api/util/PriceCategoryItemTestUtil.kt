package com.cleevio.cinemax.api.util

import com.cleevio.cinemax.api.common.util.isEqualTo
import com.cleevio.cinemax.api.module.pricecategoryitem.constant.PriceCategoryItemNumber
import com.cleevio.cinemax.api.module.pricecategoryitem.controller.dto.PriceCategoryItemSearchResponse
import com.cleevio.cinemax.api.module.pricecategoryitem.entity.PriceCategoryItem
import com.cleevio.cinemax.api.module.pricecategoryitem.service.command.SyncCreateOrUpdatePriceCategoryItemCommand
import java.math.BigDecimal
import java.util.UUID
import kotlin.test.assertEquals
import kotlin.test.assertTrue

fun mapToCreateOrUpdatePriceCategoryItemCommand(
    item: PriceCategoryItem,
    priceCategoryId: UUID,
) = SyncCreateOrUpdatePriceCategoryItemCommand(
    id = item.id,
    priceCategoryId = priceCategoryId,
    number = item.number,
    title = item.title,
    price = item.price,
    discounted = item.discounted
)

fun createPriceCategoryItem(
    priceCategoryId: UUID,
    number: PriceCategoryItemNumber = PriceCategoryItemNumber.PRICE_1,
    title: String? = "Dospely",
    price: BigDecimal = BigDecimal.TEN,
    discounted: Boolean = false,
) = PriceCategoryItem(
    id = UUID.randomUUID(),
    priceCategoryId = priceCategoryId,
    number = number,
    title = title,
    price = price,
    discounted = discounted
)

fun createPriceCategoryItemCommand(
    number: PriceCategoryItemNumber,
    title: String? = null,
    price: BigDecimal,
    discounted: Boolean = false,
) = SyncCreateOrUpdatePriceCategoryItemCommand(
    priceCategoryId = UUID.randomUUID(),
    number = number,
    title = title,
    price = price,
    discounted = discounted
)

fun mapToPriceCategoryItemResponse(item: PriceCategoryItem) = PriceCategoryItemSearchResponse(
    id = item.id,
    number = item.number,
    title = item.title,
    price = item.price
)

fun assertPriceCategoryItemEquals(
    expected: PriceCategoryItem,
    actual: PriceCategoryItemSearchResponse,
) {
    assertEquals(expected.id, actual.id)
    assertEquals(expected.number, actual.number)
    assertEquals(expected.title, actual.title)
    assertTrue(expected.price isEqualTo actual.price)
}
