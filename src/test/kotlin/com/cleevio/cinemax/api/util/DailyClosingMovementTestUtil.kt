package com.cleevio.cinemax.api.util

import com.cleevio.cinemax.api.common.constant.PaymentType
import com.cleevio.cinemax.api.module.basketitem.constant.BasketItemType
import com.cleevio.cinemax.api.module.dailyclosingmovement.constant.DailyClosingMovementBaseGroup
import com.cleevio.cinemax.api.module.dailyclosingmovement.constant.DailyClosingMovementItemSubtype
import com.cleevio.cinemax.api.module.dailyclosingmovement.constant.DailyClosingMovementItemType
import com.cleevio.cinemax.api.module.dailyclosingmovement.constant.DailyClosingMovementType
import com.cleevio.cinemax.api.module.dailyclosingmovement.entity.DailyClosingMovement
import com.cleevio.cinemax.api.module.dailyclosingmovement.model.DailyClosingMovementDataModel
import com.cleevio.cinemax.api.module.dailyclosingmovement.service.command.CreateOtherDailyClosingMovementCommand
import com.cleevio.cinemax.api.module.dailyclosingmovement.service.command.UpdateOtherDailyClosingMovementCommand
import java.math.BigDecimal
import java.util.UUID

fun mapDailyClosingMovementToCreateOtherDailyClosingMovementCommand(dailyClosingMovement: DailyClosingMovement) =
    CreateOtherDailyClosingMovementCommand(
        dailyClosingId = dailyClosingMovement.dailyClosingId,
        title = dailyClosingMovement.title!!,
        type = dailyClosingMovement.type,
        itemType = dailyClosingMovement.itemType,
        paymentType = dailyClosingMovement.paymentType,
        variableSymbol = dailyClosingMovement.variableSymbol,
        otherReceiptNumber = dailyClosingMovement.otherReceiptNumber,
        amount = dailyClosingMovement.amount
    )

fun mapDailyClosingMovementToUpdateOtherDailyClosingMovementCommand(dailyClosingMovement: DailyClosingMovement) =
    UpdateOtherDailyClosingMovementCommand(
        id = dailyClosingMovement.id,
        dailyClosingId = dailyClosingMovement.dailyClosingId,
        title = dailyClosingMovement.title!!,
        type = dailyClosingMovement.type,
        itemType = dailyClosingMovement.itemType,
        paymentType = dailyClosingMovement.paymentType,
        variableSymbol = dailyClosingMovement.variableSymbol,
        otherReceiptNumber = dailyClosingMovement.otherReceiptNumber,
        amount = dailyClosingMovement.amount
    )

fun createDailyClosingMovement(
    id: UUID = UUID.randomUUID(),
    dailyClosingId: UUID,
    title: String? = null,
    type: DailyClosingMovementType = DailyClosingMovementType.REVENUE,
    itemType: DailyClosingMovementItemType = DailyClosingMovementItemType.TICKETS,
    itemSubtype: DailyClosingMovementItemSubtype = DailyClosingMovementItemSubtype.TICKET_SALES,
    paymentType: PaymentType = PaymentType.CASH,
    variableSymbol: String? = null,
    receiptNumber: String = "P000000005",
    otherReceiptNumber: String? = null,
    amount: BigDecimal = BigDecimal.ZERO,
    entityModifier: (DailyClosingMovement) -> Unit = {},
): DailyClosingMovement = DailyClosingMovement(
    id = id,
    dailyClosingId = dailyClosingId,
    title = title,
    type = type,
    itemType = itemType,
    itemSubtype = itemSubtype,
    paymentType = paymentType,
    variableSymbol = variableSymbol,
    receiptNumber = receiptNumber,
    otherReceiptNumber = otherReceiptNumber,
    amount = amount
).also(entityModifier)

fun createDailyClosingMovementBaseGroupSet(dailyClosingId: UUID): List<DailyClosingMovement> =
    DailyClosingMovementBaseGroup.entries.mapIndexed { index, it ->
        val receiptPrefix = if (it.movementType == DailyClosingMovementType.REVENUE) "P" else "V"

        createDailyClosingMovement(
            dailyClosingId = dailyClosingId,
            type = it.movementType,
            itemType = it.itemType,
            itemSubtype = it.itemSubtype,
            paymentType = it.paymentType,
            receiptNumber = "$receiptPrefix${(index + 1).toString().padStart(9, '0')}"
        )
    }

fun createDailyClosingMovementDataModel(
    basketItemType: BasketItemType = BasketItemType.TICKET,
    basketItemPrice: BigDecimal = BigDecimal.ZERO,
    basketPaymentType: PaymentType = PaymentType.CASH,
    ticketTotalPrice: BigDecimal = BigDecimal.ZERO,
    ticketSeatServiceFee: BigDecimal = BigDecimal.ZERO,
    ticketAuditoriumServiceFee: BigDecimal = BigDecimal.ZERO,
    ticketServiceFeeGeneral: BigDecimal = BigDecimal.ZERO,
    isCancelled: Boolean = false,
): DailyClosingMovementDataModel = DailyClosingMovementDataModel(
    basketItemType = basketItemType,
    basketItemPrice = basketItemPrice,
    basketPaymentType = basketPaymentType,
    ticketTotalPrice = ticketTotalPrice,
    ticketSeatServiceFee = ticketSeatServiceFee,
    ticketAuditoriumServiceFee = ticketAuditoriumServiceFee,
    ticketServiceFeeGeneral = ticketServiceFeeGeneral,
    isCancelled = isCancelled
)
