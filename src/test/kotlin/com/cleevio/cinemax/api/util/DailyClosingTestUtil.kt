package com.cleevio.cinemax.api.util

import com.cleevio.cinemax.api.module.dailyclosing.constant.DailyClosingState
import com.cleevio.cinemax.api.module.dailyclosing.entity.DailyClosing
import java.math.BigDecimal
import java.time.LocalDateTime
import java.util.UUID

fun createDailyClosing(
    id: UUID = UUID.randomUUID(),
    posConfigurationId: UUID,
    state: DailyClosingState = DailyClosingState.OPEN,
    receiptNumber: String = "R12345",
    ticketsCount: Int = 0,
    cancelledTicketsCount: Int = 0,
    fixedPriceTicketsCount: Int = 0,
    fixedPriceTicketsAmount: BigDecimal = 0.toBigDecimal(),
    productsCount: Int = 0,
    cancelledProductsCount: Int = 0,
    closedAt: LocalDateTime? = null,
    previousClosedAt: LocalDateTime? = null,
): DailyClosing = DailyClosing(
    id = id,
    posConfigurationId = posConfigurationId,
    state = state,
    receiptNumber = receiptNumber,
    ticketsCount = ticketsCount,
    cancelledTicketsCount = cancelledTicketsCount,
    fixedPriceTicketsCount = fixedPriceTicketsCount,
    fixedPriceTicketsAmount = fixedPriceTicketsAmount,
    productsCount = productsCount,
    cancelledProductsCount = cancelledProductsCount,
    closedAt = closedAt,
    previousClosedAt = previousClosedAt
)
