package com.cleevio.cinemax.api.util

import com.cleevio.cinemax.api.module.basketitem.controller.dto.BasketItemReservationResponse
import com.cleevio.cinemax.api.module.groupreservation.controller.dto.ReservationResponse
import com.cleevio.cinemax.api.module.groupreservation.controller.dto.SeatResponse
import com.cleevio.cinemax.api.module.reservation.constant.ReservationState
import com.cleevio.cinemax.api.module.reservation.controller.dto.ReservationSearchResponse
import com.cleevio.cinemax.api.module.reservation.entity.Reservation
import com.cleevio.cinemax.api.module.reservation.service.command.CreateOrUpdateReservationCommand
import com.cleevio.cinemax.api.module.reservation.service.command.CreateReservationCommand
import com.cleevio.cinemax.api.module.reservation.service.command.DeleteReservationCommand
import com.cleevio.cinemax.api.module.reservation.service.command.UpdateReservationStateCommand
import com.cleevio.cinemax.api.module.seat.entity.Seat
import java.util.UUID
import kotlin.test.assertEquals
import kotlin.test.assertNotNull

fun mapToCreateReservationCommand(reservation: Reservation) =
    CreateReservationCommand(
        screeningId = reservation.screeningId,
        seatId = reservation.seatId,
        state = reservation.state
    )

fun mapToUpdateReservationCommand(reservation: Reservation) =
    UpdateReservationStateCommand(
        reservationId = reservation.id,
        state = reservation.state
    )

fun mapToDeleteReservationCommand(reservation: Reservation) =
    DeleteReservationCommand(
        reservationId = reservation.id
    )

fun createReservation(
    id: UUID = UUID.randomUUID(),
    originalId: Int? = null,
    screeningId: UUID,
    seatId: UUID,
    state: ReservationState = ReservationState.RESERVED,
    groupReservationId: UUID? = null,
) = Reservation(
    id = id,
    originalId = originalId,
    screeningId = screeningId,
    seatId = seatId,
    state = state,
    groupReservationId = groupReservationId
)

fun createCreateOrUpdateReservationCommand(
    originalId: Int = 1,
    screeningId: UUID,
    seatId: UUID,
    state: ReservationState = ReservationState.RESERVED,
    groupReservationId: UUID? = null,
) = CreateOrUpdateReservationCommand(
    id = UUID.randomUUID(),
    originalId = originalId,
    screeningId = screeningId,
    seatId = seatId,
    state = state,
    groupReservationId = groupReservationId
)

fun mapToReservationSearchResponse(reservation: Reservation) =
    ReservationSearchResponse(
        state = reservation.state,
        groupReservationId = reservation.groupReservationId
    )

fun assertBasketResponseReservationEquals(
    expected: Reservation,
    actual: BasketItemReservationResponse,
) {
    assertEquals(expected.seatId, actual.seat.id)
    assertEquals(expected.state, actual.state)
}

fun assertReservationEquals(expected: Reservation, actual: Reservation) {
    assertEquals(expected.screeningId, actual.screeningId)
    assertEquals(expected.seatId, actual.seatId)
    assertEquals(expected.state, actual.state)
    assertEquals(expected.groupReservationId, actual.groupReservationId)
    assertNotNull(actual.createdAt)
    assertNotNull(actual.updatedAt)
    assertEquals(expected.createdBy, actual.createdBy)
    assertEquals(expected.updatedBy, actual.updatedBy)
}

fun mapToAdminSearchGroupReservationReservationResponse(
    reservation: Reservation,
    seat: Seat,
) = ReservationResponse(
    id = reservation.id,
    state = reservation.state,
    seat = SeatResponse(
        id = seat.id,
        row = seat.row,
        number = seat.number
    )
)
