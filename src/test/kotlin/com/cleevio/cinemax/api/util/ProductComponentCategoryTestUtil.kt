package com.cleevio.cinemax.api.util

import com.cleevio.cinemax.api.common.constant.STANDARD_TAX_RATE
import com.cleevio.cinemax.api.common.util.truncatedToSeconds
import com.cleevio.cinemax.api.module.productcomponentcategory.controller.dto.AdminSearchProductComponentCategoriesResponse
import com.cleevio.cinemax.api.module.productcomponentcategory.entity.ProductComponentCategory
import com.cleevio.cinemax.api.module.productcomponentcategory.service.command.CreateOrUpdateProductComponentCategoryCommand
import com.cleevio.cinemax.api.module.productcomponentcategory.service.command.MessagingCreateOrUpdateProductComponentCategoryCommand
import com.cleevio.cinemax.mssql.rp_bufete.dbo.tables.pojos.Rcdsk
import java.util.UUID
import kotlin.test.assertEquals
import kotlin.test.assertNotNull

fun mapToCreateOrUpdateProductComponentCategoryCommand(productComponentCategory: ProductComponentCategory) =
    CreateOrUpdateProductComponentCategoryCommand(
        id = productComponentCategory.id,
        originalId = productComponentCategory.originalId,
        code = productComponentCategory.code,
        title = productComponentCategory.title,
        taxRate = productComponentCategory.taxRate
    )

fun createProductComponentCategory(
    id: UUID = UUID.randomUUID(),
    originalId: Int? = 1,
    code: String = "01",
    title: String = "Nachos",
    taxRate: Int = STANDARD_TAX_RATE,
    entityModifier: (ProductComponentCategory) -> Unit = {},
) = ProductComponentCategory(
    id = id,
    originalId = originalId,
    code = code,
    title = title,
    taxRate = taxRate
).apply(entityModifier)

fun assertCommandToProductComponentCategoryMapping(
    expected: CreateOrUpdateProductComponentCategoryCommand,
    actual: ProductComponentCategory,
    expectedCode: String? = null,
) {
    assertEquals(expected.originalId, actual.originalId)
    if (expected.code != null) {
        assertEquals(expected.code, actual.code)
    } else {
        expectedCode?.let { assertEquals(expectedCode, actual.code) } ?: run { assertNotNull(actual.code) }
    }
    assertEquals(expected.title, actual.title)
    assertEquals(expected.taxRate, actual.taxRate)
}

fun assertProductComponentCategoryToSearchResponseEquals(
    expected: ProductComponentCategory,
    actual: AdminSearchProductComponentCategoriesResponse,
) {
    assertEquals(expected.id, actual.id)
    assertEquals(expected.title, actual.title)
    assertEquals(expected.code, actual.code)
    assertEquals(expected.taxRate, actual.taxRate)
    assertEquals(expected.createdAt.truncatedToSeconds(), actual.createdAt.truncatedToSeconds())
    assertEquals(expected.updatedAt.truncatedToSeconds(), actual.updatedAt.truncatedToSeconds())
}

fun assertProductComponentCategoryToMssqlProductComponentCategoryMapping(
    expected: ProductComponentCategory,
    expectedOriginalId: Int,
    actual: Rcdsk,
) {
    assertEquals(expectedOriginalId, actual.rcdskid)
    assertEquals(expected.code, actual.druh.trim())
    assertEquals(expected.title, actual.nazev.trim())
    assertEquals(expected.taxRate.toShort(), actual.dan)
}

fun mapToMessagingCreateOrUpdateProductComponentCategoryCommand(productComponentCategory: ProductComponentCategory) =
    MessagingCreateOrUpdateProductComponentCategoryCommand(
        code = productComponentCategory.code,
        title = productComponentCategory.title,
        taxRate = productComponentCategory.taxRate
    )

fun assertMessagingCommandToProductComponentCategoryMapping(
    expected: MessagingCreateOrUpdateProductComponentCategoryCommand,
    actual: ProductComponentCategory,
) {
    assertEquals(expected.code, actual.code)
    assertEquals(expected.title, actual.title)
    assertEquals(expected.taxRate, actual.taxRate)
}
