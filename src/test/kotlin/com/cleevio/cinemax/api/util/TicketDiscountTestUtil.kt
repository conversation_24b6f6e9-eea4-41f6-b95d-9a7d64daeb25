package com.cleevio.cinemax.api.util

import com.cleevio.cinemax.api.common.util.isEqualTo
import com.cleevio.cinemax.api.common.util.trimIfNotBlank
import com.cleevio.cinemax.api.module.ticketdiscount.constant.TicketDiscountType
import com.cleevio.cinemax.api.module.ticketdiscount.constant.TicketDiscountUsageType
import com.cleevio.cinemax.api.module.ticketdiscount.controller.dto.AdminSearchTicketDiscountsResponse
import com.cleevio.cinemax.api.module.ticketdiscount.controller.dto.TicketDiscountSearchResponse
import com.cleevio.cinemax.api.module.ticketdiscount.entity.TicketDiscount
import com.cleevio.cinemax.api.module.ticketdiscount.service.command.CreateOrUpdateTicketDiscountCommand
import com.cleevio.cinemax.api.module.ticketdiscount.service.command.MessagingCreateOrUpdateTicketDiscountCommand
import com.cleevio.cinemax.api.truncatedToSeconds
import com.cleevio.cinemax.mssql.rps_cinemaxe.dbo.tables.pojos.Rslev
import java.math.BigDecimal
import java.util.UUID
import kotlin.test.assertEquals
import kotlin.test.assertFalse
import kotlin.test.assertNull
import kotlin.test.assertTrue

fun mapToCreateOrUpdateTicketDiscountCommand(ticketDiscount: TicketDiscount) =
    CreateOrUpdateTicketDiscountCommand(
        id = ticketDiscount.id,
        originalId = ticketDiscount.originalId,
        code = ticketDiscount.code,
        title = ticketDiscount.title,
        type = ticketDiscount.type,
        usageType = ticketDiscount.usageType,
        amount = ticketDiscount.amount,
        percentage = ticketDiscount.percentage,
        applicableToCount = ticketDiscount.applicableToCount,
        freeCount = ticketDiscount.freeCount,
        zeroFees = ticketDiscount.zeroFees,
        voucherOnly = ticketDiscount.voucherOnly,
        active = ticketDiscount.active,
        order = ticketDiscount.order
    )

fun createTicketDiscount(
    originalId: Int? = null,
    code: String = "VK",
    title: String? = "dummyOriginalTitle",
    type: TicketDiscountType = TicketDiscountType.ABSOLUTE,
    usageType: TicketDiscountUsageType = TicketDiscountUsageType.PRIMARY,
    amount: BigDecimal? = BigDecimal.ONE,
    percentage: Int? = null,
    applicableToCount: Int? = 1,
    freeCount: Int? = 1,
    zeroFees: Boolean = false,
    voucherOnly: Boolean = false,
    active: Boolean = true,
    order: Int? = 1,
) = TicketDiscount(
    id = UUID.randomUUID(),
    originalId = originalId,
    code = code,
    title = title,
    type = type,
    usageType = usageType,
    amount = amount,
    percentage = percentage,
    applicableToCount = applicableToCount,
    freeCount = freeCount,
    zeroFees = zeroFees,
    voucherOnly = voucherOnly,
    active = active,
    order = order
)

fun assertTicketDiscountEquals(
    expected: TicketDiscount,
    expectedAvailableForSelection: Boolean,
    expectedAvailableForBasket: Boolean,
    actual: TicketDiscountSearchResponse,
) {
    assertEquals(expected.id, actual.id)
    assertEquals(expected.title, actual.title)
    assertEquals(expected.type, actual.type)
    assertEquals(expected.usageType, actual.usageType)
    expected.amount?.let {
        assertTrue(it isEqualTo actual.amount!!)
    } ?: assertNull(actual.amount)
    assertEquals(expected.percentage, actual.percentage)
    assertEquals(expectedAvailableForSelection, actual.availableForSelection)
    assertEquals(expectedAvailableForBasket, actual.availableForBasket)
}

fun mapToTicketDiscountSearchResponse(ticketDiscount: TicketDiscount): TicketDiscountSearchResponse {
    return TicketDiscountSearchResponse(
        id = ticketDiscount.id,
        title = ticketDiscount.title,
        type = ticketDiscount.type,
        usageType = ticketDiscount.usageType,
        amount = ticketDiscount.amount,
        percentage = ticketDiscount.percentage
    )
}

fun mapToAdminSearchTicketDiscountsResponse(ticketDiscount: TicketDiscount) = AdminSearchTicketDiscountsResponse(
    id = ticketDiscount.id,
    code = ticketDiscount.code,
    title = ticketDiscount.title,
    type = ticketDiscount.type,
    usageType = ticketDiscount.usageType,
    voucherOnly = ticketDiscount.voucherOnly,
    active = ticketDiscount.active,
    createdAt = ticketDiscount.createdAt,
    updatedAt = ticketDiscount.updatedAt
)

fun assertAdminSearchTicketDiscountsResponseEquals(
    expected: AdminSearchTicketDiscountsResponse,
    actual: AdminSearchTicketDiscountsResponse,
) {
    assertEquals(expected.title, actual.title)
    assertEquals(expected.code, actual.code)
    assertEquals(expected.type, actual.type)
    assertEquals(expected.usageType, actual.usageType)
    assertEquals(expected.voucherOnly, actual.voucherOnly)
    assertEquals(expected.active, actual.active)
    assertEquals(expected.createdAt.truncatedToSeconds(), actual.createdAt.truncatedToSeconds())
    assertEquals(expected.updatedAt.truncatedToSeconds(), actual.updatedAt.truncatedToSeconds())
}

fun assertTicketDiscountToMssqlTicketDiscountMapping(
    expected: TicketDiscount,
    actual: Rslev,
) {
    assertEquals(expected.originalId, actual.rslevid)
    assertEquals(expected.code, actual.cislo.trim())
    if (expected.isFixedPrice()) {
        assertTrue(actual.lpcena)
        assertFalse(actual.lproc)
    } else {
        assertFalse(actual.lpcena)
        if (expected.type == TicketDiscountType.ABSOLUTE) {
            assertFalse(actual.lproc)
        } else {
            assertTrue(actual.lproc)
        }
    }
    assertEquals(expected.title, actual.prijmeni.trimIfNotBlank())
    assertEquals(expected.updatedBy, actual.zuziv.trimIfNotBlank())
    if (expected.usageType == TicketDiscountUsageType.PRIMARY) {
        assertEquals(expected.applicableToCount, actual.pocslev)
        assertEquals(0, actual.pocpo)
    } else {
        assertEquals(expected.applicableToCount, actual.pocpo)
        assertEquals(0, actual.pocslev)
    }
    if (expected.usageType == TicketDiscountUsageType.PRIMARY) {
        assertFalse(actual.lnest)
    } else {
        assertTrue(actual.lnest)
    }
    assertEquals(expected.active, actual.aktivni)
    if (expected.order == null) {
        assertEquals(0, actual.pc)
    } else {
        assertEquals(expected.order, actual.pc)
    }
    assertEquals(expected.zeroFees, actual.nulsluzby)
    assertEquals(expected.voucherOnly, actual.jenvoucher)
    if (expected.freeCount == null) {
        assertEquals(0, actual.poczanula)
    } else {
        assertEquals(expected.freeCount, actual.poczanula)
    }
}

fun mapToMessagingCreateOrUpdateTicketDiscountCommand(discount: TicketDiscount) = MessagingCreateOrUpdateTicketDiscountCommand(
    code = discount.code,
    title = discount.title,
    type = discount.type,
    usageType = discount.usageType,
    amount = discount.amount,
    percentage = discount.percentage,
    applicableToCount = discount.applicableToCount,
    freeCount = discount.freeCount,
    zeroFees = discount.zeroFees,
    voucherOnly = discount.voucherOnly,
    active = discount.active,
    order = discount.order
)

fun assertMessagingCreateOrUpdateTicketDiscountCommandToEntity(
    expected: MessagingCreateOrUpdateTicketDiscountCommand,
    actual: TicketDiscount,
) {
    assertEquals(actual.code, expected.code)
    assertEquals(actual.title, expected.title)
    assertEquals(actual.type, expected.type)
    assertEquals(actual.usageType, expected.usageType)
    assertEquals(actual.amount, expected.amount)
    assertEquals(actual.percentage, expected.percentage)
    assertEquals(actual.applicableToCount, expected.applicableToCount)
    assertEquals(actual.freeCount, expected.freeCount)
    assertEquals(actual.zeroFees, expected.zeroFees)
    assertEquals(actual.voucherOnly, expected.voucherOnly)
    assertEquals(actual.active, expected.active)
    assertEquals(actual.order, expected.order)
}
