package com.cleevio.cinemax.api

import com.cleevio.cinemax.api.common.config.CinemaxConfigProperties
import com.cleevio.cinemax.api.common.config.GrantedAuthorityConfig
import com.cleevio.cinemax.api.common.config.JwtAuthenticationFilter
import com.cleevio.cinemax.api.common.config.SecurityConfig
import com.cleevio.cinemax.api.common.descriptor.mssql.DescriptorMssqlResponseMapper
import com.cleevio.cinemax.api.common.service.JwtService
import com.cleevio.cinemax.api.module.auditorium.service.AdminGetAuditoriumQueryService
import com.cleevio.cinemax.api.module.auditorium.service.AdminSearchAuditoriumsQueryService
import com.cleevio.cinemax.api.module.auditorium.service.AuditoriumService
import com.cleevio.cinemax.api.module.auditoriumlayout.service.AdminGetAuditoriumLayoutQueryService
import com.cleevio.cinemax.api.module.auditoriumlayout.service.AuditoriumLayoutService
import com.cleevio.cinemax.api.module.basket.controller.mapper.BasketResponseMapper
import com.cleevio.cinemax.api.module.basket.service.BasketJpaFinderService
import com.cleevio.cinemax.api.module.basket.service.BasketMessagingService
import com.cleevio.cinemax.api.module.basket.service.BasketService
import com.cleevio.cinemax.api.module.basketitem.service.AdminBranchSalesOverviewQueryService
import com.cleevio.cinemax.api.module.basketitem.service.AdminSearchProductBasketItemQueryService
import com.cleevio.cinemax.api.module.basketitem.service.AdminSearchProductBasketItemSummaryQueryService
import com.cleevio.cinemax.api.module.basketitem.service.AdminSearchTicketBasketItemQueryService
import com.cleevio.cinemax.api.module.basketitem.service.AdminSearchTicketBasketItemSummaryQueryService
import com.cleevio.cinemax.api.module.basketitem.service.BasketItemService
import com.cleevio.cinemax.api.module.basketitem.service.ProductBasketItemExportService
import com.cleevio.cinemax.api.module.basketitem.service.TicketBasketItemExportService
import com.cleevio.cinemax.api.module.branch.service.AdminGetBranchesQueryService
import com.cleevio.cinemax.api.module.dailyclosing.service.AdminGetDailyClosingsQueryService
import com.cleevio.cinemax.api.module.dailyclosing.service.AdminSearchDailyClosingsQueryService
import com.cleevio.cinemax.api.module.dailyclosing.service.DailyClosingExportService
import com.cleevio.cinemax.api.module.dailyclosing.service.DailyClosingService
import com.cleevio.cinemax.api.module.dailyclosing.service.DailyClosingsExportService
import com.cleevio.cinemax.api.module.dailyclosingmovement.service.DailyClosingMovementService
import com.cleevio.cinemax.api.module.dbox.service.DBoxService
import com.cleevio.cinemax.api.module.discountcard.controller.mapper.DiscountCardResponseMapper
import com.cleevio.cinemax.api.module.discountcard.service.AdminGetDiscountCardTitlesQueryService
import com.cleevio.cinemax.api.module.discountcard.service.CinemaxCardsService
import com.cleevio.cinemax.api.module.discountcard.service.DiscountCardImplementationFinderService
import com.cleevio.cinemax.api.module.discountcard.service.DiscountCardLegacyService
import com.cleevio.cinemax.api.module.discountcard.service.DiscountCardMssqlService
import com.cleevio.cinemax.api.module.distributor.service.AdminGetDistributorQueryService
import com.cleevio.cinemax.api.module.distributor.service.AdminSearchDistributorsQueryService
import com.cleevio.cinemax.api.module.distributor.service.DistributorReportProcessingService
import com.cleevio.cinemax.api.module.distributor.service.DistributorService
import com.cleevio.cinemax.api.module.employee.controller.mapper.EmployeeSearchResponseMapper
import com.cleevio.cinemax.api.module.employee.controller.mapper.TokensResponseMapper
import com.cleevio.cinemax.api.module.employee.service.EmployeeFinderService
import com.cleevio.cinemax.api.module.employee.service.EmployeeService
import com.cleevio.cinemax.api.module.file.service.FileService
import com.cleevio.cinemax.api.module.genre.service.GenreJooqFinderService
import com.cleevio.cinemax.api.module.groupreservation.service.AdminSearchGroupReservationsQueryService
import com.cleevio.cinemax.api.module.groupreservation.service.GroupReservationService
import com.cleevio.cinemax.api.module.jso.service.JsoJooqFinderService
import com.cleevio.cinemax.api.module.language.service.LanguageJooqFinderService
import com.cleevio.cinemax.api.module.movie.service.AdminGetMovieQueryService
import com.cleevio.cinemax.api.module.movie.service.AdminSearchMoviesQueryService
import com.cleevio.cinemax.api.module.movie.service.MovieDISFilmSynchronizationService
import com.cleevio.cinemax.api.module.movie.service.MovieMessagingService
import com.cleevio.cinemax.api.module.movie.service.MovieService
import com.cleevio.cinemax.api.module.movie.service.MovieUnificationService
import com.cleevio.cinemax.api.module.posconfiguration.service.AdminGetPosConfigurationsQueryService
import com.cleevio.cinemax.api.module.pricecategory.service.AdminGetPriceCategoryQueryService
import com.cleevio.cinemax.api.module.pricecategory.service.AdminSearchPriceCategoriesQueryService
import com.cleevio.cinemax.api.module.pricecategory.service.PriceCategoryMessagingService
import com.cleevio.cinemax.api.module.pricecategory.service.PriceCategoryService
import com.cleevio.cinemax.api.module.product.service.AdminGetProductQueryService
import com.cleevio.cinemax.api.module.product.service.AdminProductMarginRatesExportService
import com.cleevio.cinemax.api.module.product.service.ProductMessagingService
import com.cleevio.cinemax.api.module.product.service.ProductSaleMssqlSynchronizationService
import com.cleevio.cinemax.api.module.product.service.ProductService
import com.cleevio.cinemax.api.module.product.service.query.AdminSearchProductsQueryService
import com.cleevio.cinemax.api.module.productcategory.controller.mapper.ProductCategoryResponseMapper
import com.cleevio.cinemax.api.module.productcategory.service.AdminSearchProductCategoriesQueryService
import com.cleevio.cinemax.api.module.productcategory.service.ProductCategoryJooqFinderService
import com.cleevio.cinemax.api.module.productcategory.service.ProductCategoryService
import com.cleevio.cinemax.api.module.productcomponent.service.AdminSearchProductComponentsQueryService
import com.cleevio.cinemax.api.module.productcomponent.service.ProductComponentExportService
import com.cleevio.cinemax.api.module.productcomponent.service.ProductComponentService
import com.cleevio.cinemax.api.module.productcomponentcategory.service.AdminSearchProductComponentCategoriesQueryService
import com.cleevio.cinemax.api.module.productcomponentcategory.service.ProductComponentCategoryService
import com.cleevio.cinemax.api.module.production.service.ProductionJooqFinderService
import com.cleevio.cinemax.api.module.rating.service.RatingJooqFinderService
import com.cleevio.cinemax.api.module.receipt.service.SvkXmlReceiptService
import com.cleevio.cinemax.api.module.screening.controller.mapper.ScreeningSearchResponseMapper
import com.cleevio.cinemax.api.module.screening.service.AdminExportMovieScreeningsQueryService
import com.cleevio.cinemax.api.module.screening.service.AdminGetScreeningQueryService
import com.cleevio.cinemax.api.module.screening.service.AdminSearchScreeningsQueryService
import com.cleevio.cinemax.api.module.screening.service.AdminSearchScreeningsTimelineQueryService
import com.cleevio.cinemax.api.module.screening.service.DistributorScreeningsExportService
import com.cleevio.cinemax.api.module.screening.service.MovieScreeningsExportService
import com.cleevio.cinemax.api.module.screening.service.ScreeningExportService
import com.cleevio.cinemax.api.module.screening.service.ScreeningJooqFinderService
import com.cleevio.cinemax.api.module.screening.service.ScreeningMessagingService
import com.cleevio.cinemax.api.module.screening.service.ScreeningService
import com.cleevio.cinemax.api.module.screeningtype.service.AdminSearchScreeningTypesQueryService
import com.cleevio.cinemax.api.module.screeningtype.service.ScreeningTypeService
import com.cleevio.cinemax.api.module.screeningtypes.service.ScreeningTypesService
import com.cleevio.cinemax.api.module.seat.service.SeatService
import com.cleevio.cinemax.api.module.stockmovement.service.AdminSearchStockMovementsQueryService
import com.cleevio.cinemax.api.module.stockmovement.service.StockMovementExportService
import com.cleevio.cinemax.api.module.stockmovement.service.StockMovementService
import com.cleevio.cinemax.api.module.stocktaking.service.AdminSearchStockTakingsQueryService
import com.cleevio.cinemax.api.module.stocktaking.service.StockTakingExportService
import com.cleevio.cinemax.api.module.stocktaking.service.StockTakingService
import com.cleevio.cinemax.api.module.supplier.service.AdminGetSupplierQueryService
import com.cleevio.cinemax.api.module.supplier.service.AdminSearchSuppliersQueryService
import com.cleevio.cinemax.api.module.supplier.service.SupplierService
import com.cleevio.cinemax.api.module.technology.service.TechnologyJooqFinderService
import com.cleevio.cinemax.api.module.terminalpayment.service.SvkTerminalService
import com.cleevio.cinemax.api.module.ticket.service.CheckpointGetBasketTicketsQueryService
import com.cleevio.cinemax.api.module.ticket.service.TicketMssqlSynchronizationService
import com.cleevio.cinemax.api.module.ticket.service.TicketSaleMssqlSynchronizationService
import com.cleevio.cinemax.api.module.ticket.service.TicketService
import com.cleevio.cinemax.api.module.ticket.service.TicketUsedSynchronizationService
import com.cleevio.cinemax.api.module.ticketdiscount.controller.mapper.TicketDiscountResponseMapper
import com.cleevio.cinemax.api.module.ticketdiscount.service.AdminGetTicketDiscountQueryService
import com.cleevio.cinemax.api.module.ticketdiscount.service.AdminSearchTicketDiscountsQueryService
import com.cleevio.cinemax.api.module.ticketdiscount.service.TicketDiscountJooqFinderService
import com.cleevio.cinemax.api.module.ticketdiscount.service.TicketDiscountService
import com.cleevio.cinemax.api.module.tms.service.TmsService
import com.cleevio.cinemax.api.module.tmslanguage.service.TmsLanguageJooqFinderService
import com.ninjasquad.springmockk.MockkBean
import io.mockk.junit5.MockKExtension
import org.junit.jupiter.api.TestInstance
import org.junit.jupiter.api.extension.ExtendWith
import org.springframework.context.annotation.Import
import org.springframework.test.context.ActiveProfiles

@ActiveProfiles("test")
@TestInstance(value = TestInstance.Lifecycle.PER_CLASS)
@ExtendWith(MockKExtension::class)
@Import(
    value = [
        SecurityConfig::class,
        JwtService::class,
        JwtAuthenticationFilter::class,
        GrantedAuthorityConfig::class,
        JacksonTestConfig::class
    ]
)
abstract class ControllerTest {

    @MockkBean
    lateinit var adminSearchAuditoriumsQueryService: AdminSearchAuditoriumsQueryService

    @MockkBean
    lateinit var adminGetAuditoriumQueryService: AdminGetAuditoriumQueryService

    @MockkBean
    lateinit var auditoriumService: AuditoriumService

    @MockkBean
    lateinit var adminGetAuditoriumLayoutQueryService: AdminGetAuditoriumLayoutQueryService

    @MockkBean
    lateinit var auditoriumLayoutService: AuditoriumLayoutService

    @MockkBean
    lateinit var seatService: SeatService

    @MockkBean
    lateinit var basketService: BasketService

    @MockkBean
    lateinit var basketJpaFinderService: BasketJpaFinderService

    @MockkBean
    lateinit var basketResponseMapper: BasketResponseMapper

    @MockkBean
    lateinit var genreFinderService: GenreJooqFinderService

    @MockkBean
    lateinit var descriptorMssqlResponseMapper: DescriptorMssqlResponseMapper

    @MockkBean
    lateinit var basketItemService: BasketItemService

    @MockkBean
    lateinit var searchTicketBasketItemQueryService: AdminSearchTicketBasketItemQueryService

    @MockkBean
    lateinit var searchProductBasketItemQueryService: AdminSearchProductBasketItemQueryService

    @MockkBean
    lateinit var productCategoryJooqFinderService: ProductCategoryJooqFinderService

    @MockkBean
    lateinit var productCategoryResponseMapper: ProductCategoryResponseMapper

    @MockkBean
    lateinit var adminSearchPriceCategoriesQueryService: AdminSearchPriceCategoriesQueryService

    @MockkBean
    lateinit var adminGetPriceCategoryQueryService: AdminGetPriceCategoryQueryService

    @MockkBean
    lateinit var priceCategoryService: PriceCategoryService

    @MockkBean
    lateinit var svkTerminalService: SvkTerminalService

    @MockkBean
    lateinit var dailyClosingService: DailyClosingService

    @MockkBean
    lateinit var adminSearchDailyClosingsQueryService: AdminSearchDailyClosingsQueryService

    @MockkBean
    lateinit var adminGetDailyClosingsQueryService: AdminGetDailyClosingsQueryService

    @MockkBean
    lateinit var dailyClosingsExportService: DailyClosingsExportService

    @MockkBean
    lateinit var dailyClosingExportService: DailyClosingExportService

    @MockkBean
    lateinit var dailyClosingMovementService: DailyClosingMovementService

    @MockkBean
    lateinit var adminGetDiscountCardTitlesQueryService: AdminGetDiscountCardTitlesQueryService

    @MockkBean
    lateinit var discountCardMssqlService: DiscountCardMssqlService

    @MockkBean
    lateinit var discountCardLegacyService: DiscountCardLegacyService

    @MockkBean
    lateinit var discountCardResponseMapper: DiscountCardResponseMapper

    @MockkBean
    lateinit var cinemaxCardsService: CinemaxCardsService

    @MockkBean
    lateinit var discountCardImplementationFinderService: DiscountCardImplementationFinderService

    @MockkBean
    lateinit var distributorService: DistributorService

    @MockkBean
    lateinit var adminSearchDistributorQueryService: AdminSearchDistributorsQueryService

    @MockkBean
    lateinit var adminGetDistributorQueryService: AdminGetDistributorQueryService

    @MockkBean
    lateinit var jsoJooqFinderService: JsoJooqFinderService

    @MockkBean
    lateinit var employeeService: EmployeeService

    @MockkBean
    lateinit var employeeFinderService: EmployeeFinderService

    @MockkBean
    lateinit var tokensResponseMapper: TokensResponseMapper

    @MockkBean
    lateinit var employeeSearchResponseMapper: EmployeeSearchResponseMapper

    @MockkBean
    lateinit var productionFinderService: ProductionJooqFinderService

    @MockkBean
    lateinit var groupReservationService: GroupReservationService

    @MockkBean
    lateinit var adminSearchGroupReservationsQueryService: AdminSearchGroupReservationsQueryService

    @MockkBean
    lateinit var ratingJooqFinderService: RatingJooqFinderService

    @MockkBean
    lateinit var languageJooqFinderService: LanguageJooqFinderService

    @MockkBean
    lateinit var adminSearchMoviesQueryService: AdminSearchMoviesQueryService

    @MockkBean
    lateinit var adminGetMovieQueryService: AdminGetMovieQueryService

    @MockkBean
    lateinit var movieService: MovieService

    @MockkBean
    lateinit var movieDISFilmSynchronizationService: MovieDISFilmSynchronizationService

    @MockkBean
    lateinit var adminGetPosConfigurationsQueryService: AdminGetPosConfigurationsQueryService

    @MockkBean
    lateinit var svkXmlReceiptService: SvkXmlReceiptService

    @MockkBean
    lateinit var screeningService: ScreeningService

    @MockkBean
    lateinit var adminSearchScreeningsQueryService: AdminSearchScreeningsQueryService

    @MockkBean
    lateinit var adminSearchScreeningsTimelineQueryService: AdminSearchScreeningsTimelineQueryService

    @MockkBean
    lateinit var screeningExportService: ScreeningExportService

    @MockkBean
    lateinit var adminGetScreeningQueryService: AdminGetScreeningQueryService

    @MockkBean
    lateinit var distributorScreeningsExportService: DistributorScreeningsExportService

    @MockkBean
    lateinit var screeningJooqFinderService: ScreeningJooqFinderService

    @MockkBean
    lateinit var screeningSearchResponseMapper: ScreeningSearchResponseMapper

    @MockkBean
    lateinit var tmsLanguageJooqFinderService: TmsLanguageJooqFinderService

    @MockkBean
    lateinit var ticketDiscountJooqFinderService: TicketDiscountJooqFinderService

    @MockkBean
    lateinit var ticketDiscountResponseMapper: TicketDiscountResponseMapper

    @MockkBean
    lateinit var adminSearchTicketDiscountsQueryService: AdminSearchTicketDiscountsQueryService

    @MockkBean
    lateinit var adminGetTicketDiscountQueryService: AdminGetTicketDiscountQueryService

    @MockkBean
    lateinit var ticketDiscountService: TicketDiscountService

    @MockkBean
    lateinit var adminSearchScreeningTypesQueryService: AdminSearchScreeningTypesQueryService

    @MockkBean
    lateinit var screeningTypeService: ScreeningTypeService

    @MockkBean
    lateinit var supplierService: SupplierService

    @MockkBean
    lateinit var adminSearchSuppliersQueryService: AdminSearchSuppliersQueryService

    @MockkBean
    lateinit var adminGetSupplierQueryService: AdminGetSupplierQueryService

    @MockkBean
    lateinit var ticketService: TicketService

    @MockkBean
    lateinit var technologyJooqFinderService: TechnologyJooqFinderService

    @MockkBean
    lateinit var searchTicketBasketItemSummaryQueryService: AdminSearchTicketBasketItemSummaryQueryService

    @MockkBean
    lateinit var searchProductBasketItemSummaryQueryService: AdminSearchProductBasketItemSummaryQueryService

    @MockkBean
    lateinit var productComponentCategoryService: ProductComponentCategoryService

    @MockkBean
    lateinit var adminSearchProductComponentCategoriesQueryService: AdminSearchProductComponentCategoriesQueryService

    @MockkBean
    lateinit var productComponentService: ProductComponentService

    @MockkBean
    lateinit var adminSearchProductComponentsQueryService: AdminSearchProductComponentsQueryService

    @MockkBean
    lateinit var ticketBasketItemExportService: TicketBasketItemExportService

    @MockkBean
    lateinit var productComponentExportService: ProductComponentExportService

    @MockkBean
    lateinit var stockTakingService: StockTakingService

    @MockkBean
    lateinit var adminSearchStockTakingsQueryService: AdminSearchStockTakingsQueryService

    @MockkBean
    lateinit var stockTakingExportService: StockTakingExportService

    @MockkBean
    lateinit var stockMovementService: StockMovementService

    @MockkBean
    lateinit var adminSearchStockMovementsQueryService: AdminSearchStockMovementsQueryService

    @MockkBean
    lateinit var fileService: FileService

    @MockkBean
    lateinit var adminSearchProductCategoriesQueryService: AdminSearchProductCategoriesQueryService

    @MockkBean
    lateinit var productCategoryService: ProductCategoryService

    @MockkBean
    lateinit var productService: ProductService

    @MockkBean
    lateinit var adminGetProductQueryService: AdminGetProductQueryService

    @MockkBean
    lateinit var adminSearchProductsQueryService: AdminSearchProductsQueryService

    @MockkBean
    lateinit var screeningTypesService: ScreeningTypesService

    @MockkBean
    lateinit var adminProductMarginRatesExportService: AdminProductMarginRatesExportService

    @MockkBean
    lateinit var productBasketItemExportService: ProductBasketItemExportService

    @MockkBean
    lateinit var adminGetBranchesQueryServiceMock: AdminGetBranchesQueryService

    @MockkBean
    lateinit var cinemaxConfigProperties: CinemaxConfigProperties

    @MockkBean
    lateinit var productSaleMssqlSynchronizationServiceMock: ProductSaleMssqlSynchronizationService

    @MockkBean
    lateinit var ticketSaleMssqlSynchronizationServiceMock: TicketSaleMssqlSynchronizationService

    @MockkBean
    lateinit var adminBranchSalesOverviewQueryService: AdminBranchSalesOverviewQueryService

    @MockkBean
    lateinit var stockMovementExportService: StockMovementExportService

    @MockkBean
    lateinit var movieUnificationService: MovieUnificationService

    @MockkBean
    lateinit var movieMessagingService: MovieMessagingService

    @MockkBean
    lateinit var screeningMessagingService: ScreeningMessagingService

    @MockkBean
    lateinit var basketMessagingService: BasketMessagingService

    @MockkBean
    lateinit var productMessagingService: ProductMessagingService

    @MockkBean
    lateinit var tmsService: TmsService

    @MockkBean
    lateinit var dBoxService: DBoxService

    @MockkBean
    lateinit var movieScreeningsExportService: MovieScreeningsExportService

    @MockkBean
    lateinit var adminExportMovieScreeningsQueryService: AdminExportMovieScreeningsQueryService

    @MockkBean
    lateinit var ticketMssqlSynchronizationService: TicketMssqlSynchronizationService

    @MockkBean
    lateinit var ticketUsedSynchronizationService: TicketUsedSynchronizationService

    @MockkBean
    lateinit var priceCategoryMessagingService: PriceCategoryMessagingService

    @MockkBean
    lateinit var distributorReportProcessingService: DistributorReportProcessingService

    @MockkBean
    lateinit var checkpointGetBasketTicketsQueryService: CheckpointGetBasketTicketsQueryService
}
