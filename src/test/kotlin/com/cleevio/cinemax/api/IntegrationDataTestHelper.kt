package com.cleevio.cinemax.api

import com.cleevio.cinemax.api.common.constant.PaymentType
import com.cleevio.cinemax.api.common.constant.STANDARD_TAX_RATE
import com.cleevio.cinemax.api.module.auditorium.entity.Auditorium
import com.cleevio.cinemax.api.module.auditorium.service.AuditoriumService
import com.cleevio.cinemax.api.module.auditoriumlayout.entity.AuditoriumLayout
import com.cleevio.cinemax.api.module.auditoriumlayout.service.AuditoriumLayoutRepository
import com.cleevio.cinemax.api.module.basket.constant.BasketState
import com.cleevio.cinemax.api.module.basket.entity.Basket
import com.cleevio.cinemax.api.module.basket.service.BasketRepository
import com.cleevio.cinemax.api.module.basketitem.constant.BasketItemType
import com.cleevio.cinemax.api.module.basketitem.entity.BasketItem
import com.cleevio.cinemax.api.module.basketitem.service.BasketItemRepository
import com.cleevio.cinemax.api.module.card.entity.Card
import com.cleevio.cinemax.api.module.card.service.CardRepository
import com.cleevio.cinemax.api.module.cardusage.entity.CardUsage
import com.cleevio.cinemax.api.module.cardusage.service.CardUsageRepository
import com.cleevio.cinemax.api.module.discountcard.constant.DiscountCardType
import com.cleevio.cinemax.api.module.discountcard.entity.DiscountCard
import com.cleevio.cinemax.api.module.discountcard.service.DiscountCardRepository
import com.cleevio.cinemax.api.module.discountcardusage.entity.DiscountCardUsage
import com.cleevio.cinemax.api.module.discountcardusage.service.DiscountCardUsageRepository
import com.cleevio.cinemax.api.module.distributor.entity.Distributor
import com.cleevio.cinemax.api.module.distributor.service.DistributorRepository
import com.cleevio.cinemax.api.module.groupreservation.constant.GroupReservationType
import com.cleevio.cinemax.api.module.groupreservation.entity.GroupReservation
import com.cleevio.cinemax.api.module.groupreservation.service.GroupReservationRepository
import com.cleevio.cinemax.api.module.movie.constant.MovieRating
import com.cleevio.cinemax.api.module.movie.entity.Movie
import com.cleevio.cinemax.api.module.movie.service.MovieRepository
import com.cleevio.cinemax.api.module.posconfiguration.constant.PosConfigurationType
import com.cleevio.cinemax.api.module.posconfiguration.constant.ProductMode
import com.cleevio.cinemax.api.module.posconfiguration.constant.TablesType
import com.cleevio.cinemax.api.module.posconfiguration.entity.PosConfiguration
import com.cleevio.cinemax.api.module.posconfiguration.service.PosConfigurationRepository
import com.cleevio.cinemax.api.module.pricecategory.entity.PriceCategory
import com.cleevio.cinemax.api.module.pricecategory.service.PriceCategoryRepository
import com.cleevio.cinemax.api.module.pricecategoryitem.constant.PriceCategoryItemNumber
import com.cleevio.cinemax.api.module.pricecategoryitem.entity.PriceCategoryItem
import com.cleevio.cinemax.api.module.pricecategoryitem.service.PriceCategoryItemRepository
import com.cleevio.cinemax.api.module.product.constant.ProductType
import com.cleevio.cinemax.api.module.product.entity.Product
import com.cleevio.cinemax.api.module.product.service.ProductRepository
import com.cleevio.cinemax.api.module.productcategory.constant.ProductCategoryType
import com.cleevio.cinemax.api.module.productcategory.entity.ProductCategory
import com.cleevio.cinemax.api.module.productcategory.service.ProductCategoryRepository
import com.cleevio.cinemax.api.module.productcomponent.constant.ProductComponentUnit
import com.cleevio.cinemax.api.module.productcomponent.entity.ProductComponent
import com.cleevio.cinemax.api.module.productcomponent.service.ProductComponentRepository
import com.cleevio.cinemax.api.module.productcomponentcategory.entity.ProductComponentCategory
import com.cleevio.cinemax.api.module.productcomponentcategory.service.ProductComponentCategoryRepository
import com.cleevio.cinemax.api.module.productcomposition.entity.ProductComposition
import com.cleevio.cinemax.api.module.productcomposition.service.ProductCompositionRepository
import com.cleevio.cinemax.api.module.reservation.constant.ReservationState
import com.cleevio.cinemax.api.module.reservation.entity.Reservation
import com.cleevio.cinemax.api.module.reservation.service.ReservationRepository
import com.cleevio.cinemax.api.module.screening.constant.MovieFormat
import com.cleevio.cinemax.api.module.screening.constant.MovieLanguage
import com.cleevio.cinemax.api.module.screening.constant.MovieTechnology
import com.cleevio.cinemax.api.module.screening.constant.ScreeningState
import com.cleevio.cinemax.api.module.screening.entity.Screening
import com.cleevio.cinemax.api.module.screening.service.ScreeningRepository
import com.cleevio.cinemax.api.module.screeningfee.entity.ScreeningFee
import com.cleevio.cinemax.api.module.screeningfee.service.ScreeningFeeRepository
import com.cleevio.cinemax.api.module.screeningtype.entity.ScreeningType
import com.cleevio.cinemax.api.module.screeningtype.service.ScreeningTypeRepository
import com.cleevio.cinemax.api.module.screeningtypes.entity.ScreeningTypes
import com.cleevio.cinemax.api.module.screeningtypes.service.ScreeningTypesRepository
import com.cleevio.cinemax.api.module.seat.constant.DoubleSeatType
import com.cleevio.cinemax.api.module.seat.constant.SeatObjectType
import com.cleevio.cinemax.api.module.seat.constant.SeatType
import com.cleevio.cinemax.api.module.seat.entity.Seat
import com.cleevio.cinemax.api.module.seat.service.SeatRepository
import com.cleevio.cinemax.api.module.table.constant.TableType
import com.cleevio.cinemax.api.module.table.entity.Table
import com.cleevio.cinemax.api.module.table.service.TableRepository
import com.cleevio.cinemax.api.module.ticket.entity.Ticket
import com.cleevio.cinemax.api.module.ticket.service.TicketRepository
import com.cleevio.cinemax.api.module.ticketdiscount.constant.TicketDiscountType
import com.cleevio.cinemax.api.module.ticketdiscount.constant.TicketDiscountUsageType
import com.cleevio.cinemax.api.module.ticketdiscount.entity.TicketDiscount
import com.cleevio.cinemax.api.module.ticketdiscount.service.TicketDiscountRepository
import com.cleevio.cinemax.api.module.ticketprice.constant.AuditoriumServiceFeeType
import com.cleevio.cinemax.api.module.ticketprice.constant.AuditoriumSurchargeType
import com.cleevio.cinemax.api.module.ticketprice.constant.SeatServiceFeeType
import com.cleevio.cinemax.api.module.ticketprice.constant.SeatSurchargeType
import com.cleevio.cinemax.api.module.ticketprice.entity.TicketPrice
import com.cleevio.cinemax.api.module.ticketprice.service.TicketPriceRepository
import com.cleevio.cinemax.api.util.mapToCreateOrUpdateAuditoriumCommand
import org.springframework.stereotype.Service
import java.math.BigDecimal
import java.math.RoundingMode
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.LocalTime
import java.util.UUID

@Service
class IntegrationDataTestHelper(
    private val distributorRepository: DistributorRepository,
    private val screeningRepository: ScreeningRepository,
    private val auditoriumService: AuditoriumService,
    private val movieRepository: MovieRepository,
    private val auditoriumLayoutRepository: AuditoriumLayoutRepository,
    private val discountCardRepository: DiscountCardRepository,
    private val discountCardUsageRepository: DiscountCardUsageRepository,
    private val priceCategoryRepository: PriceCategoryRepository,
    private val priceCategoryItemRepository: PriceCategoryItemRepository,
    private val baskedRepository: BasketRepository,
    private val basketItemRepository: BasketItemRepository,
    private val ticketRepository: TicketRepository,
    private val ticketPriceRepository: TicketPriceRepository,
    private val ticketDiscountRepository: TicketDiscountRepository,
    private val reservationRepository: ReservationRepository,
    private val groupReservationRepository: GroupReservationRepository,
    private val posConfigurationRepository: PosConfigurationRepository,
    private val seatRepository: SeatRepository,
    private val screeningFeeRepository: ScreeningFeeRepository,
    private val tableRepository: TableRepository,
    private val productCategoryRepository: ProductCategoryRepository,
    private val productRepository: ProductRepository,
    private val productCompositionRepository: ProductCompositionRepository,
    private val productComponentCategoryRepository: ProductComponentCategoryRepository,
    private val productComponentRepository: ProductComponentRepository,
    private val cardRepository: CardRepository,
    private val cardUsageRepository: CardUsageRepository,
    private val screeningTypeRepository: ScreeningTypeRepository,
    private val screeningTypesRepository: ScreeningTypesRepository,
) {

    fun getDistributor(
        id: UUID = UUID.randomUUID(),
        originalId: Int? = 1,
        code: String = "OC${originalId ?: 0}",
        disfilmCode: String = "DF${originalId ?: 0}",
        title: String = "Best movies ever, s.r.o.",
        addressStreet: String? = "Filmová 58",
        addressCity: String? = "Praha",
        addressPostCode: String? = "150 00",
        contactName1: String? = "John",
        contactName2: String? = "Jerry",
        contactName3: String? = "Tom",
        contactPhone1: String? = "+************",
        contactPhone2: String? = "+************",
        contactPhone3: String? = "*********",
        contactEmails: Set<String> = setOf("<EMAIL>", "<EMAIL>"),
        bankName: String? = "AirBank, a.s.",
        bankAccount: String? = "**********/0300",
        idNumber: String? = "********",
        taxIdNumber: String? = "SK2022916731",
        vatRate: Int? = 20,
        note: String? = "Best note ever",
        entityModifier: (Distributor) -> Unit = {},
    ) = distributorRepository.save(
        Distributor(
            id = id,
            originalId = originalId,
            code = code,
            disfilmCode = disfilmCode,
            title = title,
            addressStreet = addressStreet,
            addressCity = addressCity,
            addressPostCode = addressPostCode,
            contactName1 = contactName1,
            contactName2 = contactName2,
            contactName3 = contactName3,
            contactPhone1 = contactPhone1,
            contactPhone2 = contactPhone2,
            contactPhone3 = contactPhone3,
            contactEmails = contactEmails,
            bankName = bankName,
            bankAccount = bankAccount,
            idNumber = idNumber,
            taxIdNumber = taxIdNumber,
            vatRate = vatRate,
            note = note
        ).apply(entityModifier)
    )

    fun getAuditorium(
        id: UUID = UUID.randomUUID(),
        originalId: Int = 1,
        code: String = "A",
        branchId: UUID? = null,
        title: String = "Sála A",
        originalCode: Int = originalId + 100,
        capacity: Int = 100,
        city: String = "Bratislava",
    ) = auditoriumService.syncCreateOrUpdateAuditorium(
        mapToCreateOrUpdateAuditoriumCommand(
            Auditorium(
                id = id,
                originalId = originalId,
                originalCode = originalCode,
                branchId = branchId,
                code = code,
                title = title,
                capacity = capacity,
                city = city
            )
        )
    )

    fun getAuditoriumLayout(
        originalId: Int? = 1,
        code: String = "0$originalId",
        auditoriumId: UUID = getAuditorium(),
        title: String = "Základná",
    ) = auditoriumLayoutRepository.save(
        AuditoriumLayout(
            originalId = originalId,
            auditoriumId = auditoriumId,
            code = code,
            title = title
        )
    )

    fun getSeat(
        id: UUID = UUID.randomUUID(),
        originalId: Int? = 1,
        auditoriumId: UUID = getAuditorium(),
        auditoriumLayoutId: UUID = getAuditoriumLayout(auditoriumId = auditoriumId).id,
        type: SeatType = SeatType.REGULAR,
        doubleSeatType: DoubleSeatType? = null,
        row: String = "1",
        number: String = "1",
        positionLeft: Int = row.toInt(),
        positionTop: Int = number.toInt(),
        defaultReservationState: ReservationState? = null,
    ) = seatRepository.save(
        Seat(
            id = id,
            originalId = originalId,
            auditoriumLayoutId = auditoriumLayoutId,
            auditoriumId = auditoriumId,
            type = type,
            objectType = SeatObjectType.SEAT,
            doubleSeatType = doubleSeatType,
            row = row,
            number = number,
            positionLeft = positionLeft,
            positionTop = positionTop,
            webPositionLeft = positionLeft,
            webPositionTop = positionTop,
            defaultReservationState = defaultReservationState
        )
    )

    fun getMovie(
        originalId: Int? = 1,
        title: String = "Oppenheimer",
        rawTitle: String = "Oppenheimer IMAX 2D (ST)",
        originalTitle: String? = title,
        code: String = if (originalId == null) "100000" else "10000$originalId",
        disfilmCode: String? = null,
        releaseYear: Int? = 2023,
        premiereDate: LocalDate = LocalDate.of(2023, 1, 1),
        parsedFormat: MovieFormat? = MovieFormat.FORMAT_2D,
        parsedTechnology: MovieTechnology? = null,
        parsedLanguage: MovieLanguage? = MovieLanguage.ENG,
        duration: Int? = 160,
        distributorId: UUID,
        productionId: UUID? = null,
        primaryGenreId: UUID? = null,
        secondaryGenreId: UUID? = null,
        ratingId: UUID? = null,
        technologyId: UUID? = null,
        languageId: UUID? = null,
        tmsLanguageId: UUID? = null,
    ) = movieRepository.save(
        Movie(
            id = UUID.randomUUID(),
            originalId = originalId,
            title = title,
            rawTitle = rawTitle,
            originalTitle = originalTitle,
            code = code,
            disfilmCode = disfilmCode,
            releaseYear = releaseYear,
            premiereDate = premiereDate,
            duration = duration,
            parsedRating = MovieRating.PLUS_12,
            parsedFormat = parsedFormat,
            parsedTechnology = parsedTechnology,
            parsedLanguage = parsedLanguage,
            distributorId = distributorId,
            productionId = productionId,
            primaryGenreId = primaryGenreId,
            secondaryGenreId = secondaryGenreId,
            ratingId = ratingId,
            technologyId = technologyId,
            languageId = languageId,
            tmsLanguageId = tmsLanguageId
        )
    )

    fun getPriceCategory(
        id: UUID = UUID.randomUUID(),
        originalId: Int? = 1,
        originalCode: String? = "A",
        title: String? = "Do 17h",
        active: Boolean = true,
    ) = priceCategoryRepository.save(
        PriceCategory(
            id = id,
            originalId = originalId,
            originalCode = originalCode,
            title = title,
            active = active
        )
    )

    fun getPriceCategoryItem(
        priceCategoryId: UUID,
        number: PriceCategoryItemNumber = PriceCategoryItemNumber.PRICE_1,
        title: String? = "Dospely",
        price: BigDecimal = BigDecimal.TEN,
        discounted: Boolean = false,
    ) = priceCategoryItemRepository.save(
        PriceCategoryItem(
            id = UUID.randomUUID(),
            priceCategoryId = priceCategoryId,
            number = number,
            title = title,
            price = price,
            discounted = discounted
        )
    )

    fun getScreeningType(
        id: UUID = UUID.randomUUID(),
        originalId: Int? = 1,
        code: String = "BJ",
        title: String = "Babska Jazda",
    ) = screeningTypeRepository.save(
        ScreeningType(
            id = id,
            originalId = originalId,
            code = code,
            title = title
        )
    )

    fun getScreeningTypes(
        id: UUID = UUID.randomUUID(),
        screeningId: UUID,
        screeningTypeId: UUID,
        blacklisted: Boolean = false,
    ) = screeningTypesRepository.save(
        ScreeningTypes(
            id = id,
            screeningId = screeningId,
            screeningTypeId = screeningTypeId,
            blacklisted = blacklisted
        )
    )

    fun getScreening(
        id: UUID = UUID.randomUUID(),
        originalId: Int? = 1,
        auditoriumId: UUID = getAuditorium(),
        auditoriumLayoutId: UUID = getAuditoriumLayout(auditoriumId = auditoriumId).id,
        movieId: UUID = getMovie(distributorId = getDistributor().id).id,
        priceCategoryId: UUID = getPriceCategory().id,
        date: LocalDate = INTEGRATION_TEST_DATE_TIME.toLocalDate(),
        time: LocalTime = INTEGRATION_TEST_DATE_TIME.toLocalTime(),
        saleTimeLimit: Int = 30,
        stopped: Boolean = false,
        cancelled: Boolean = false,
        proCommission: Int = 3,
        filmFondCommission: Int = 6,
        distributorCommission: Int = 70,
        publishOnline: Boolean = true,
        state: ScreeningState = ScreeningState.PUBLISHED,
        adTimeSlot: Int = 15,
        entityModifier: (Screening) -> Unit = {},
    ) = screeningRepository.save(
        Screening(
            id = id,
            originalId = originalId,
            auditoriumId = auditoriumId,
            auditoriumLayoutId = auditoriumLayoutId,
            movieId = movieId,
            priceCategoryId = priceCategoryId,
            date = date,
            time = time,
            saleTimeLimit = saleTimeLimit,
            stopped = stopped,
            cancelled = cancelled,
            proCommission = proCommission,
            filmFondCommission = filmFondCommission,
            distributorCommission = distributorCommission,
            publishOnline = publishOnline,
            state = state,
            adTimeSlot = adTimeSlot
        ).apply(entityModifier)
    )

    fun getScreeningFee(
        id: UUID = UUID.randomUUID(),
        originalScreeningId: Int?,
        screeningId: UUID,
        surchargeVip: BigDecimal = 1.toBigDecimal(),
        surchargePremium: BigDecimal = 0.5.toBigDecimal(),
        surchargeImax: BigDecimal = 2.toBigDecimal(),
        surchargeUltraX: BigDecimal = 0.5.toBigDecimal(),
        surchargeDBox: BigDecimal = 5.toBigDecimal(),
        serviceFeeVip: BigDecimal = 1.toBigDecimal(),
        serviceFeePremium: BigDecimal = 0.5.toBigDecimal(),
        serviceFeeImax: BigDecimal = 2.toBigDecimal(),
        serviceFeeUltraX: BigDecimal = 0.5.toBigDecimal(),
        serviceFeeGeneral: BigDecimal = 0.2.toBigDecimal(),
    ) = screeningFeeRepository.save(
        ScreeningFee(
            id = id,
            originalScreeningId = originalScreeningId,
            screeningId = screeningId,
            surchargeVip = surchargeVip,
            surchargePremium = surchargePremium,
            surchargeImax = surchargeImax,
            surchargeUltraX = surchargeUltraX,
            surchargeDBox = surchargeDBox,
            serviceFeeVip = serviceFeeVip,
            serviceFeePremium = serviceFeePremium,
            serviceFeeImax = serviceFeeImax,
            serviceFeeUltraX = serviceFeeUltraX,
            serviceFeeGeneral = serviceFeeGeneral
        )
    )

    fun getDiscountCard(
        id: UUID = UUID.randomUUID(),
        originalId: Int = 1,
        ticketDiscountId: UUID? = null,
        productDiscountId: UUID? = null,
        productId: UUID? = null,
        type: DiscountCardType = DiscountCardType.CARD,
        title: String = "dummyTitle",
        code: String = "123456789",
        validFrom: LocalDate = LocalDate.now(),
        validUntil: LocalDate = LocalDate.now().plusYears(1),
        applicableToBasket: Int? = 1,
        applicableToScreening: Int? = 1,
        applicableToScreeningsPerDay: Int? = 5,
        productsCount: Int? = 2,
    ) = discountCardRepository.save(
        DiscountCard(
            id = id,
            originalId = originalId,
            ticketDiscountId = ticketDiscountId,
            productDiscountId = productDiscountId,
            productId = productId,
            type = type,
            title = title,
            code = code,
            validFrom = validFrom,
            validUntil = validUntil,
            applicableToBasket = applicableToBasket,
            applicableToScreening = applicableToScreening,
            applicableToScreeningsPerDay = applicableToScreeningsPerDay,
            productsCount = productsCount
        )
    )

    fun getDiscountCardUsage(
        id: UUID = UUID.randomUUID(),
        discountCardId: UUID = getDiscountCard().id,
        screeningId: UUID? = null,
        basketId: UUID = getBasket().id,
        posConfigurationId: UUID = getPosConfiguration().id,
        ticketBasketItemId: UUID? = null,
        productBasketItemId: UUID? = null,
        productDiscountBasketItemId: UUID? = null,
    ) = discountCardUsageRepository.save(
        DiscountCardUsage(
            id = id,
            discountCardId = discountCardId,
            screeningId = screeningId,
            basketId = basketId,
            posConfigurationId = posConfigurationId,
            ticketBasketItemId = ticketBasketItemId,
            productBasketItemId = productBasketItemId,
            productDiscountBasketItemId = productDiscountBasketItemId
        )
    )

    fun getTable(
        id: UUID = UUID.randomUUID(),
        originalId: Int = 1,
        title: String = "Stul 1",
        label: String = "1",
        order: Int? = 1,
        type: TableType = TableType.TABLE,
        productMode: ProductMode = ProductMode.STANDARD,
        paymentType: PaymentType = PaymentType.CASH,
        discountCardCode: String? = null,
        ipAddress: String? = null,
    ) = tableRepository.save(
        Table(
            id = id,
            originalId = originalId,
            title = title,
            label = label,
            order = order,
            type = type,
            productMode = productMode,
            paymentType = paymentType,
            discountCardCode = discountCardCode,
            ipAddress = ipAddress
        )
    )

    fun getBasket(
        id: UUID = UUID.randomUUID(),
        tableId: UUID? = null,
        totalPrice: BigDecimal = BigDecimal.TEN,
        state: BasketState = BasketState.OPEN,
        paymentType: PaymentType? = null,
        paymentPosConfigurationId: UUID? = null,
        paidAt: LocalDateTime? = null,
        variableSymbol: String? = null,
        entityModifier: (Basket) -> Unit = {},
    ) = baskedRepository.save(
        Basket(
            id = id,
            tableId = tableId,
            totalPrice = totalPrice,
            state = state,
            paymentType = paymentType,
            paymentPosConfigurationId = paymentPosConfigurationId,
            paidAt = paidAt,
            variableSymbol = variableSymbol
        ).also(entityModifier)
    )

    fun getBasketItem(
        id: UUID? = null,
        basketId: UUID,
        ticketId: UUID? = null,
        productId: UUID? = null,
        type: BasketItemType,
        price: BigDecimal,
        vatAmount: BigDecimal? = null,
        quantity: Int = 1,
        productReceiptNumber: String? = null,
        productIsolatedWith: UUID? = null,
        cancelledBasketItemId: UUID? = null,
        isCancelled: Boolean = false,
        branchId: UUID? = null,
        entityModifier: (BasketItem) -> Unit = {},
    ) = basketItemRepository.save(
        BasketItem(
            id = id ?: UUID.randomUUID(),
            basketId = basketId,
            ticketId = ticketId,
            productId = productId,
            type = type,
            price = price,
            vatAmount = vatAmount ?: (price - (price.divide(1.23.toBigDecimal(), 2, RoundingMode.HALF_UP))),
            quantity = quantity,
            productReceiptNumber = productReceiptNumber,
            productIsolatedWithId = productIsolatedWith,
            cancelledBasketItemId = cancelledBasketItemId,
            isCancelled = isCancelled,
            branchId = branchId
        ).also { entityModifier(it) }
    )

    fun getTicket(
        id: UUID = UUID.randomUUID(),
        originalId: Int? = null,
        screeningId: UUID,
        reservationId: UUID,
        ticketPriceId: UUID,
        ticketDiscountPrimaryId: UUID? = null,
        ticketDiscountSecondaryId: UUID? = null,
        receiptNumber: String? = "000000001",
        isGroupTicket: Boolean = false,
        isUsed: Boolean = false,
        includes3dGlasses: Boolean = false,
    ): Ticket = ticketRepository.save(
        Ticket(
            id = id,
            originalId = originalId,
            screeningId = screeningId,
            reservationId = reservationId,
            ticketPriceId = ticketPriceId,
            ticketDiscountPrimaryId = ticketDiscountPrimaryId,
            ticketDiscountSecondaryId = ticketDiscountSecondaryId,
            receiptNumber = receiptNumber,
            isGroupTicket = isGroupTicket,
            isUsed = isUsed,
            includes3dGlasses = includes3dGlasses
        )
    )

    fun getTicketPrice(
        id: UUID = UUID.randomUUID(),
        screeningId: UUID,
        seatId: UUID,
        basePrice: BigDecimal = BigDecimal.TEN,
        basePriceBeforeDiscount: BigDecimal? = null,
        basePriceItemNumber: PriceCategoryItemNumber = PriceCategoryItemNumber.PRICE_1,
        seatSurcharge: BigDecimal? = null,
        seatSurchargeType: SeatSurchargeType? = null,
        auditoriumSurcharge: BigDecimal? = null,
        auditoriumSurchargeType: AuditoriumSurchargeType? = null,
        seatServiceFee: BigDecimal? = null,
        seatServiceFeeType: SeatServiceFeeType? = null,
        auditoriumServiceFee: BigDecimal? = null,
        auditoriumServiceFeeType: AuditoriumServiceFeeType? = null,
        serviceFeeGeneral: BigDecimal? = null,
        freeTicket: Boolean? = null,
        zeroFees: Boolean? = null,
        totalPrice: BigDecimal,
        primaryDiscountAmount: BigDecimal? = null,
        primaryDiscountPercentage: Int? = null,
    ) = ticketPriceRepository.save(
        TicketPrice(
            id = id,
            screeningId = screeningId,
            seatId = seatId,
            basePrice = basePrice,
            basePriceItemNumber = basePriceItemNumber,
            basePriceBeforeDiscount = basePriceBeforeDiscount ?: basePrice,
            seatSurcharge = seatSurcharge,
            seatSurchargeType = seatSurchargeType,
            auditoriumSurcharge = auditoriumSurcharge,
            auditoriumSurchargeType = auditoriumSurchargeType,
            seatServiceFee = seatServiceFee,
            seatServiceFeeType = seatServiceFeeType,
            auditoriumServiceFee = auditoriumServiceFee,
            auditoriumServiceFeeType = auditoriumServiceFeeType,
            serviceFeeGeneral = serviceFeeGeneral,
            freeTicket = freeTicket,
            zeroFees = zeroFees,
            totalPrice = totalPrice,
            primaryDiscountAmount = primaryDiscountAmount,
            primaryDiscountPercentage = primaryDiscountPercentage
        )
    )

    fun getTicketDiscount(
        id: UUID = UUID.randomUUID(),
        originalId: Int? = null,
        code: String = "IS",
        title: String? = "ISIC 10%",
        type: TicketDiscountType = TicketDiscountType.PERCENTAGE,
        usageType: TicketDiscountUsageType = TicketDiscountUsageType.PRIMARY,
        amount: BigDecimal? = null,
        percentage: Int? = 10,
        applicableToCount: Int? = 1,
        freeCount: Int? = 0,
        zeroFees: Boolean = false,
        voucherOnly: Boolean = false,
        active: Boolean = true,
        order: Int? = null,
    ) = ticketDiscountRepository.save(
        TicketDiscount(
            id = id,
            originalId = originalId,
            code = code,
            title = title,
            type = type,
            usageType = usageType,
            amount = amount,
            percentage = percentage,
            applicableToCount = applicableToCount,
            freeCount = freeCount,
            zeroFees = zeroFees,
            voucherOnly = voucherOnly,
            active = active,
            order = order
        )
    )

    fun getReservation(
        id: UUID = UUID.randomUUID(),
        originalId: Int? = null,
        screeningId: UUID,
        seatId: UUID,
        state: ReservationState = ReservationState.RESERVED,
        groupReservationId: UUID? = null,
    ) = reservationRepository.save(
        Reservation(
            id = id,
            originalId = originalId,
            screeningId = screeningId,
            seatId = seatId,
            state = state,
            groupReservationId = groupReservationId
        )
    )

    fun getGroupReservation(
        id: UUID = UUID.randomUUID(),
        originalId: Int? = 1,
        name: String = "Cleevio rezervace",
        type: GroupReservationType = GroupReservationType.BRANCH,
        expiresAt: LocalDateTime? = null,
    ) = groupReservationRepository.save(
        GroupReservation(
            id = id,
            originalId = originalId,
            name = name,
            type = type,
            expiresAt = expiresAt
        )
    )

    fun getPosConfiguration(
        id: UUID = UUID.randomUUID(),
        macAddress: String = "AA:BB:CC:DD:EE",
        title: String = "POS 1 config",
        receiptsDirectory: String = "/pos/receipts",
        terminalDirectory: String? = null,
        terminalIpAddress: String? = "***********",
        terminalPort: Int? = 53535,
        ticketSalesEnabled: Boolean = true,
        productModes: Set<ProductMode> = setOf(ProductMode.STANDARD, ProductMode.CAFE),
        tablesType: TablesType = TablesType.CAFE_TABLES,
        seatsEnabled: Boolean = true,
        type: PosConfigurationType = PosConfigurationType.PHYSICAL,
    ) = posConfigurationRepository.save(
        PosConfiguration(
            id = id,
            macAddress = macAddress,
            title = title,
            receiptsDirectory = receiptsDirectory,
            terminalDirectory = terminalDirectory,
            terminalIpAddress = terminalIpAddress,
            terminalPort = terminalPort,
            ticketSalesEnabled = ticketSalesEnabled,
            productModes = productModes,
            tablesType = tablesType,
            seatsEnabled = seatsEnabled,
            type = type
        )
    )

    fun getProductCategory(
        id: UUID = UUID.randomUUID(),
        originalId: Int? = 1,
        code: String = "0$originalId",
        title: String = "Popcorn",
        type: ProductCategoryType = ProductCategoryType.PRODUCT,
        order: Int? = null,
        taxRate: Int = STANDARD_TAX_RATE,
        hexColorCode: String = "#000000",
        imageFileId: UUID? = null,
    ) = productCategoryRepository.save(
        ProductCategory(
            id = id,
            originalId = originalId,
            code = code,
            title = title,
            type = type,
            order = order,
            taxRate = taxRate,
            hexColorCode = hexColorCode,
            imageFileId = imageFileId
        )
    )

    fun getProduct(
        id: UUID = UUID.randomUUID(),
        originalId: Int? = 1,
        code: String = "0000$originalId",
        productCategoryId: UUID,
        title: String,
        type: ProductType = ProductType.PRODUCT,
        price: BigDecimal = 10.toBigDecimal(),
        flagshipPrice: BigDecimal = 11.toBigDecimal(),
        active: Boolean = true,
        order: Int? = null,
        soldInBuffet: Boolean = true,
        soldInCafe: Boolean = true,
        soldInVip: Boolean = true,
        discountPercentage: Int? = null,
        discountAmount: BigDecimal? = null,
        stockQuantityThreshold: Int? = null,
        imageFileId: UUID? = null,
        isPackagingDeposit: Boolean = false,
        tabletOrder: Int? = null,
        taxRate: Int? = null,
    ) = productRepository.save(
        Product(
            id = id,
            originalId = originalId,
            code = code,
            originalCode = null,
            productCategoryId = productCategoryId,
            title = title,
            type = type,
            price = price,
            flagshipPrice = flagshipPrice,
            priceNoVat = price,
            active = active,
            order = order,
            soldInBuffet = soldInBuffet,
            soldInCafe = soldInCafe,
            soldInVip = soldInVip,
            discountPercentage = discountPercentage,
            stockQuantityThreshold = stockQuantityThreshold,
            imageFileId = imageFileId,
            isPackagingDeposit = isPackagingDeposit,
            tabletOrder = tabletOrder,
            taxRate = taxRate,
            discountAmount = discountAmount
        )
    )

    fun getProductComponentCategory(
        id: UUID = UUID.randomUUID(),
        originalId: Int? = 1,
        code: String = "01",
        title: String = "Popcorn",
        taxRate: Int = STANDARD_TAX_RATE,
    ) = productComponentCategoryRepository.save(
        ProductComponentCategory(
            id = id,
            originalId = originalId,
            code = code,
            title = title,
            taxRate = taxRate
        )
    )

    fun getProductComponent(
        id: UUID = UUID.randomUUID(),
        originalId: Int? = 1,
        code: String = "$originalId",
        title: String = "Tuk",
        unit: ProductComponentUnit = ProductComponentUnit.KG,
        purchasePrice: BigDecimal = 10.toBigDecimal(),
        stockQuantity: BigDecimal = 100.toBigDecimal(),
        productComponentCategoryId: UUID,
        active: Boolean = true,
        taxRateOverride: Int? = null,
    ) = productComponentRepository.save(
        ProductComponent(
            id = id,
            originalId = originalId,
            code = code,
            title = title,
            unit = unit,
            purchasePrice = purchasePrice,
            stockQuantity = stockQuantity,
            productComponentCategoryId = productComponentCategoryId,
            active = active,
            taxRateOverride = taxRateOverride
        )
    )

    fun getProductComposition(
        id: UUID = UUID.randomUUID(),
        originalId: Int? = 1,
        productId: UUID,
        productComponentId: UUID? = null,
        amount: BigDecimal = 10.toBigDecimal(),
        productInProductId: UUID? = null,
        productInProductPrice: BigDecimal? = null,
        productInProductFlagshipPrice: BigDecimal? = null,
    ) = productCompositionRepository.save(
        ProductComposition(
            id = id,
            originalId = originalId,
            productId = productId,
            productComponentId = productComponentId,
            amount = amount,
            productInProductId = productInProductId,
            productInProductPrice = productInProductPrice,
            productInProductFlagshipPrice = productInProductFlagshipPrice
        )
    )

    fun getCard(
        id: UUID = UUID.randomUUID(),
        type: DiscountCardType = DiscountCardType.CARD,
        title: String = "Test Card 1",
        code: String = "CU5X2URJ",
        validFrom: LocalDate? = null,
        validUntil: LocalDate? = null,
    ) = cardRepository.save(
        Card(
            id = id,
            type = type,
            title = title,
            code = code,
            validFrom = validFrom,
            validUntil = validUntil
        )
    )

    fun getCardUsage(
        id: UUID = UUID.randomUUID(),
        cardId: UUID,
        basketId: UUID,
        basketItemId: UUID,
        originalId: Long? = null,
        discountPercentage: Int? = null,
    ) = cardUsageRepository.save(
        CardUsage(
            id = id,
            cardId = cardId,
            basketId = basketId,
            basketItemId = basketItemId,
            originalId = originalId,
            discountPercentage = discountPercentage
        )
    )
}
